// For license information, see `https://assets.adobedtm.com/4a848ae9611a/032db4f73473/93571d51095c/RCa16d232f95a944c0aabdea6621a2ef94-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/4a848ae9611a/032db4f73473/93571d51095c/RCa16d232f95a944c0aabdea6621a2ef94-source.min.js', "_satellite.logger.log(\"eventDispatcher: clearing tracking state\");try{s.events=\"\",s.linkTrackVars=\"\",s.linkTrackEvents=\"\"}catch(e){_satellite.logger.log(\"eventDispatcher: s object - could not reset state.\")}try{dispatcherData=JSON.parse(event.detail),window.ddqueue=window.ddqueue||[],window.ddqueue.push(dispatcherData),window.eventData=dispatcherData.eventData,window.pageData=dispatcherData.pageData,_satellite.track(dispatcherData.eventName)}catch(e){_satellite.logger.log(\"eventDispatcher: exception\"),_satellite.logger.log(e)}");