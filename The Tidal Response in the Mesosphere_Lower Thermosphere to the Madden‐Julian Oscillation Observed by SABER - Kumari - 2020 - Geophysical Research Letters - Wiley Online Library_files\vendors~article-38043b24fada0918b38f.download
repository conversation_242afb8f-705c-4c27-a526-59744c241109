(window.webpackJsonp=window.webpackJsonp||[]).push([[53],{196:function(e,t){var i;i={sections:null,holder:$(".coolBar--sections .coolBar__drop"),vPort:"screen-sm",isMobile:!1,coolbarElement:null,headerHeight:0,isStickyBar:!1,lock:!0,init:function init(){i.coolbarElement=$(".coolBar"),i.isStickyBar=i.coolbarElement.hasClass("stickybar"),i.get.sections(),i.fill.sections(),i.control(),i.addtionalControls()},control:function control(){$(".journal-home").length&&(i.vPort="screen-md"),$(document).on(i.vPort+"-on",(function(){$(".coolBar").addClass("coolBar--res"),i.isMobile=!0,$(".stickybar.coolBar--res,.coolBar.coolBar--res").length>0&&$(".content>article.container").addClass("article--res")})),$(document).on(i.vPort+"-off",(function(){$(".coolBar").removeClass("coolBar--res"),$(".content article.container").removeClass("article--res"),$(document).trigger("eventSetContentPadding"),i.isMobile=!1,void 0!==UX.controller&&UX.controller.check()}))},addtionalControls:function addtionalControls(){},get:{sections:function sections(){i.sections=$(".article__content .section__title")}},fill:{sections:function sections(){if($(".coolBar--sections").length&&i.sections.length>1){$(".coolBar--sections").removeClass("hidden");var e=$('<ul class="rlist w-slide--list"/>').appendTo(i.holder);i.sections.each((function(){var t=$(this),i=$("<li/>").attr("role","menuitem").appendTo(e),n=$('<a class="w-slide__hide"/>').attr("href","#"+t.attr("id")).appendTo(i);$("<span/>").append(t.html()).appendTo(n)}))}else $(".coolBar--sections").remove();$(".authorArticleInfoCon").length&&$(".coolBar--sections .coolBar__drop").append('<li role="menuitem"><a class="w-slide__hide" href="#_ac_authorArticleInfoCon" target="_self"><span>Author and Article Information</span></a></li>')}}},UX.coolbar=i},223:function(e,t){var i,n;i=UX.tab,n={$activator:"",doi:"",figure:"",tab:"",references:"",related:"",details:"",$tabs:null,$spinner:null,figuresTab:".figures-tab",tabFigure:"#pane-pcw-Figures , #pane-pcw-figures",referencesTab:".references-tab",tabReferences:"#pane-pcw-References, #pane-pcw-references",relatedTab:".related-tab",tabRelated:"#pane-pcw-Related, #pane-pcw-related",detailsTab:".details-tab",accessTab:".access-tab",tabDetails:"#pane-pcw-Details, #pane-pcw-details",vPort:"screen-xs",isMobile:!1,cloudFlareScript:"",openSeaDragonCloudFlareScript:"",init:function init(){n.$tabs=$(".article__tab"),n.$spinner=$(".tab__spinner"),n.cloudFlareScript=$(".cloudFlareScript").length&&$(".cloudFlareScript").val(),n.openSeaDragonCloudFlareScript=$(".openSeaDragonCloudFlareScript").length&&$(".openSeaDragonCloudFlareScript").val(),n.get.figures(),n.get.references(),n.get.related(),n.get.details(),n.truncation.init(),$("article")&&n.load.articleTabs(),n.tableToggle(),n.controller(),n.additionalController()},controller:function controller(){$("body").on("click",".figures-tab",(function(e){n.load.figures(!0)})),$("body").on("click",".boxed-text",(function(e){e.preventDefault();var t=$(this).attr("href"),i=$(t).html(),n=$("#myModal");t&&i&&(0===n.length&&$("body").append('<div class="ux-modal-container"><div id="myModal" class="modal"><div class="modal__dialog  modal--large"><div class="modal-content"><div class="modal__header clearfix"><button type="button" data-dismiss="modal" class="close">X</button></div><div class="modal__body"></div></div></div></div></div>'),n.find(".modal__body").html(i),n.modal("show"))})),$(document).on(n.vPort+"-on",(function(){n.isMobile=!0,n.rebuild.responsive(),n.rebuild.extra(),n.figures.accordion()})),$(document).on(n.vPort+"-off",(function(){n.isMobile=!1,n.rebuild.original(),n.figures.back()})),n.articleTabsTrigger()},additionalController:function additionalController(){},articleTabsTrigger:function articleTabsTrigger(){$(".access-tab").length&&$(".access-tab").trigger("click")},rebuild:{original:function original(){var e=$(".loa-wrapper");e.find(".accordion-tabbed__tab").removeClass("accordion-tabbed__tab").addClass("accordion-tabbed__tab-mobile"),e.find(".delimiter").removeClass("hidden"),e.find(".accordion-tabbed__control").each((function(){var e=$(this).attr("data-id");$(this).attr("data-db-target-for",e),$(this).next().attr("data-db-target-of",e).removeAttr("style")}))},responsive:function responsive(){var e=$(".loa-wrapper");e.find(".accordion-tabbed__tab-mobile").removeClass("accordion-tabbed__tab-mobile").addClass("accordion-tabbed__tab ").find(".accordion-tabbed__control").removeAttr("data-db-target-for"),e.find(".loa-accordion").contents().map((function(){3===this.nodeType&&$(this).wrap('<span class="delimiter hidden"></span>'),$(this).hasClass("delimiter")&&$(this).addClass("hidden")})),e.find(".accordion-tabbed__content").removeAttr("data-db-target-of").hide()},extra:function extra(){}},get:{figures:function figures(){n.figure=$("article").attr("data-figures")},references:function references(){n.references=$("article").attr("data-references")},related:function related(){n.related=$("article").attr("data-related")},details:function details(){n.details=$("article").attr("data-details")}},load:{articleTabs:function articleTabs(){$(n.figuresTab).parent("li.active").length&&n.load.figures(!1,!0),n.load.references(),n.load.related(),n.load.details()},figures:function figures(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];n.load.articleTab(n.figure,n.figuresTab,n.tabFigure,"figure",e,t)},references:function references(){var e=$(".article__body .article__references");e.length?($(".article__body.show-references").length?n.load.copyPastReferences(e):(e.appendTo(n.tabReferences),e.show()),$(n.tabReferences).removeClass("empty"),n.$spinner.hide()):n.load.articleTab(n.references,n.referencesTab,n.tabReferences,"references")},related:function related(){n.load.articleTab(n.related,n.relatedTab,n.tabRelated,"related"),n.truncation.creativeWorkMeta()},details:function details(){n.load.articleTab(n.details,n.detailsTab,n.tabDetails,"details")},copyPastReferences:function copyPastReferences(e){e.clone().appendTo(n.tabReferences),e.find("[id]").each((function(){this.id=this.id+"_copied"}))},articleTab:function articleTab(e,t,o,r){var a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],s=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];if(void 0===e)return UX.loader.selectActiveTab(t,o,a),!1;i.check.empty($(t))?n.getContent(e,t,o,r,a,s):UX.loader.selectActiveTab(t,o,a)}},getContent:function getContent(e,t,i,o){var r=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5?arguments[5]:void 0;n.$spinner.show();var s=$(i);s.load(e,{onLoad:a},(function(e,a,c){"error"===a&&console.error("Sorry but there was an error: "+c.status+" "+c.statusText),s.trigger("content-loaded"),UX.loader.selectActiveTab(t,i,r),n.attach();var l=$(this).attr("id");-1!==n.tabFigure.indexOf(l)&&n.isMobile&&n.figures.accordion(),n.getContentSuccess(o)}))},getContentSuccess:function getContentSuccess(e){},selectActiveTab:function selectActiveTab(e,t,i){UX.loader.$spinner.hide(),!i&&UX.loader.isArticleTabEmpty($(e))&&$(t).hasClass("active")&&$(".details-tab").trigger("click")},isArticleTabEmpty:function isArticleTabEmpty(e){return i.check.empty(e)||e.hasClass("figures-tab")&&0===$(e.data("slide-target")).find("figure").length||e.hasClass("references-tab")&&$(e.data("slide-target")).find(".NoContentMessage").length},attach:function attach(){void 0!==UX.figureViewer&&UX.figureViewer.expand()},figures:{figcaption:"figcaption",figureTitle:".figure__title",figureCaption:".figure__caption",accordionClass:"accordion",accordionContent:"accordion__content",accordionTrigger:$('<button title="Toggle figure caption" class="accordion__control"/>'),accordionTriggerIcon:$('<i aria-hidden="true" class="icon-section_arrow_d"/>'),accordionTriggerCaption:$("<span>Caption</span>"),accordion:function accordion(){$(n.figures.figcaption).hasClass(n.figures.accordionClass)||(n.figures.accordionTriggerCaption.appendTo(n.figures.accordionTrigger),n.figures.accordionTriggerIcon.appendTo(n.figures.accordionTrigger),$(n.figures.figcaption).find(n.figures.figureCaption).before(n.figures.accordionTrigger),$(n.figures.figcaption).addClass(n.figures.accordionClass),$(n.figures.figcaption).find(n.figures.figureCaption).addClass(n.figures.accordionContent).hide())},back:function back(){$(n.figures.figcaption).hasClass(n.figures.accordionClass)&&($(".accordion__control").remove(),$(n.figures.figcaption).removeClass(n.figures.accordionClass),$(n.figures.figcaption).find(n.figures.figureCaption).removeClass(n.figures.accordionContent).show())}},truncation:{toTrunk8:".loa-accordion",toTrunk8Mobile:".loa.mobile-authors",linesToShow:3,MobileLinesToShow:3,showMoreText:"See all authors",showLessText:"See fewer authors",seeMoreIcon:"icon-section_arrow_d",seeLessIcon:"icon-section_arrow_u",seeMoreIconMobile:"icon-arrow_r",init:function init(){var e=(n.isMobile?$(n.truncation.toTrunk8Mobile):$(n.truncation.toTrunk8)).not("[data-truncate='none']");(e.length||e.truncate)&&(n.isMobile?e.truncate({lines:n.truncation.MobileLinesToShow,type:"list",seeMoreLink:!0,seeMoreText:n.truncation.showMoreText,seeLessText:n.truncation.showLessText,seeMoreIcon:n.truncation.seeMoreIcon,seeLessIcon:n.truncation.seeLessIcon,seeMoreIconMobile:n.truncation.seeMoreIconMobile,isMobile:!0,mobileTarget:"#sb-1"}):e.truncate({lines:n.truncation.linesToShow,type:"list",seeMoreLink:!0,seeMoreText:n.truncation.showMoreText,seeLessText:n.truncation.showLessText,seeMoreIcon:n.truncation.seeMoreIcon,seeLessIcon:n.truncation.seeLessIcon,seeMoreIconMobile:n.truncation.seeMoreIconMobile}))},creativeWorkMeta:function creativeWorkMeta(){var e=$(".creative-work .meta").not("[data-truncate='none']");e.length&&e.truncate({lines:1})}},tableToggle:function tableToggle(){var e=$(".tableToggle__trigger");e.length&&e.on("click",(function(e){var t=$(e.currentTarget),i=t.closest(".tableToggle");i.find(".tableToggle__table").toggle(),i.find(".tableToggle__image").toggle(),t.find("span").toggle()}))}},UX.loader=n},371:function(e,t,i){"use strict";i.r(t);i(372),i(373),i(375),i(378),i(379),i(223);t.default=function main(e){var t=Array.prototype.slice.call(document.querySelectorAll(".article-page figure:not(.holder), figure,.article-page .figure:not(.holder),.article-page .colored-block,.article-page .component-container")),i=Array.prototype.slice.call(document.querySelectorAll(".article-page > article"));try{t.length&&e.figureViewer.init(t)}catch(e){console.error(e)}try{e.loader.init()}catch(e){console.error(e)}try{i.length&&e.loader.init(i)}catch(e){console.error(e)}}},372:function(e,t,i){var n,o=i(1);
/**
 * @license jquery.panzoom.js v3.2.2
 * Updated: Sat Aug 27 2016
 * Add pan and zoom functionality to any element
 * Copyright (c) timmy willison
 * Released under the MIT license
 * https://github.com/timmywil/jquery.panzoom/blob/master/MIT-License.txt
 */(function(e,t){"use strict";var i=e.document,n=Array.prototype.slice,r=/([A-Z])/g,a=/^http:[\w\.\/]+svg$/,s="(\\-?\\d[\\d\\.e-]*)",c=new RegExp("^matrix\\("+s+"\\,?\\s*"+s+"\\,?\\s*"+s+"\\,?\\s*"+s+"\\,?\\s*"+s+"\\,?\\s*"+s+"\\)$");function createResetOptions(e){var i={range:!0,animate:!0};return"boolean"==typeof e?i.animate=e:t.extend(i,e),i}function Matrix(e,i,n,o,r,a,s,c,l){"array"===t.type(e)?this.elements=[+e[0],+e[2],+e[4],+e[1],+e[3],+e[5],0,0,1]:this.elements=[e,i,n,o,r,a,s||0,c||0,l||1]}function Vector(e,t,i){this.elements=[e,t,i]}function Panzoom(e,n){if(!(this instanceof Panzoom))return new Panzoom(e,n);1!==e.nodeType&&t.error("Panzoom called on non-Element node"),t.contains(i,e)||t.error("Panzoom element must be attached to the document");var o=t.data(e,"__pz__");if(o)return o;this.options=n=t.extend({},Panzoom.defaults,n),this.elem=e;var s=this.$elem=t(e);this.$set=n.$set&&n.$set.length?n.$set:s,this.$doc=t(e.ownerDocument||i),this.$parent=s.parent(),this.parent=this.$parent[0],this.isSVG=a.test(e.namespaceURI)&&"svg"!==e.nodeName.toLowerCase(),this.panning=!1,this._buildTransform(),this._transform=t.cssProps.transform?t.cssProps.transform.replace(r,"-$1").toLowerCase():"transform",this._buildTransition(),this.resetDimensions();var c=t(),l=this;t.each(["$zoomIn","$zoomOut","$zoomRange","$reset"],(function(e,t){l[t]=n[t]||c})),this.enable(),this.scale=this.getMatrix()[0],this._checkPanWhenZoomed(),t.data(e,"__pz__",this)}Matrix.prototype={x:function x(e){var t=e instanceof Vector,i=this.elements,n=e.elements;return t&&3===n.length?new Vector(i[0]*n[0]+i[1]*n[1]+i[2]*n[2],i[3]*n[0]+i[4]*n[1]+i[5]*n[2],i[6]*n[0]+i[7]*n[1]+i[8]*n[2]):n.length===i.length&&new Matrix(i[0]*n[0]+i[1]*n[3]+i[2]*n[6],i[0]*n[1]+i[1]*n[4]+i[2]*n[7],i[0]*n[2]+i[1]*n[5]+i[2]*n[8],i[3]*n[0]+i[4]*n[3]+i[5]*n[6],i[3]*n[1]+i[4]*n[4]+i[5]*n[7],i[3]*n[2]+i[4]*n[5]+i[5]*n[8],i[6]*n[0]+i[7]*n[3]+i[8]*n[6],i[6]*n[1]+i[7]*n[4]+i[8]*n[7],i[6]*n[2]+i[7]*n[5]+i[8]*n[8])},inverse:function inverse(){var e=1/this.determinant(),t=this.elements;return new Matrix(e*(t[8]*t[4]-t[7]*t[5]),e*-(t[8]*t[1]-t[7]*t[2]),e*(t[5]*t[1]-t[4]*t[2]),e*-(t[8]*t[3]-t[6]*t[5]),e*(t[8]*t[0]-t[6]*t[2]),e*-(t[5]*t[0]-t[3]*t[2]),e*(t[7]*t[3]-t[6]*t[4]),e*-(t[7]*t[0]-t[6]*t[1]),e*(t[4]*t[0]-t[3]*t[1]))},determinant:function determinant(){var e=this.elements;return e[0]*(e[8]*e[4]-e[7]*e[5])-e[3]*(e[8]*e[1]-e[7]*e[2])+e[6]*(e[5]*e[1]-e[4]*e[2])}},Vector.prototype.e=Matrix.prototype.e=function(e){return this.elements[e]},Panzoom.rmatrix=c,Panzoom.defaults={eventNamespace:".panzoom",transition:!0,cursor:"move",disablePan:!1,disableZoom:!1,disableXAxis:!1,disableYAxis:!1,which:1,increment:.3,exponential:!0,panOnlyWhenZoomed:!1,minScale:.3,maxScale:6,rangeStep:.05,duration:200,easing:"ease-in-out",contain:!1},Panzoom.prototype={constructor:Panzoom,instance:function instance(){return this},enable:function enable(){this._initStyle(),this._bind(),this.disabled=!1},disable:function disable(){this.disabled=!0,this._resetStyle(),this._unbind()},isDisabled:function isDisabled(){return this.disabled},destroy:function destroy(){this.disable(),t.removeData(this.elem,"__pz__")},resetDimensions:function resetDimensions(){this.container=this.parent.getBoundingClientRect();var e=this.elem,i=e.getBoundingClientRect(),n=Math.abs(this.scale);this.dimensions={width:i.width,height:i.height,left:t.css(e,"left",!0)||0,top:t.css(e,"top",!0)||0,border:{top:t.css(e,"borderTopWidth",!0)*n||0,bottom:t.css(e,"borderBottomWidth",!0)*n||0,left:t.css(e,"borderLeftWidth",!0)*n||0,right:t.css(e,"borderRightWidth",!0)*n||0},margin:{top:t.css(e,"marginTop",!0)*n||0,left:t.css(e,"marginLeft",!0)*n||0}}},reset:function reset(e){e=createResetOptions(e);var t=this.setMatrix(this._origTransform,e);e.silent||this._trigger("reset",t)},resetZoom:function resetZoom(e){e=createResetOptions(e);var t=this.getMatrix(this._origTransform);e.dValue=t[3],this.zoom(t[0],e)},resetPan:function resetPan(e){var t=this.getMatrix(this._origTransform);this.pan(t[4],t[5],createResetOptions(e))},setTransform:function setTransform(e){for(var i=this.$set,n=i.length;n--;)t.style(i[n],"transform",e),this.isSVG&&i[n].setAttribute("transform",e)},getTransform:function getTransform(e){var i=this.$set[0];return e?this.setTransform(e):(e=t.style(i,"transform"),!this.isSVG||e&&"none"!==e||(e=t.attr(i,"transform")||"none")),"none"===e||c.test(e)||this.setTransform(e=t.css(i,"transform")),e||"none"},getMatrix:function getMatrix(e){var t=c.exec(e||this.getTransform());return t&&t.shift(),t||[1,0,0,1,0,0]},setMatrix:function setMatrix(e,i){if(!this.disabled){i||(i={}),"string"==typeof e&&(e=this.getMatrix(e));var n=+e[0],o=void 0!==i.contain?i.contain:this.options.contain;if(o){var r,a,s,c=i.dims;c||(this.resetDimensions(),c=this.dimensions);var l=this.container,d=c.width,u=c.height,f=l.width,h=l.height,g=f/d,p=h/u;"center"!==this.$parent.css("textAlign")||"inline"!==t.css(this.elem,"display")?(r=(s=(d-this.elem.offsetWidth)/2)-c.border.left,a=d-f-s+c.border.right):r=a=(d-f)/2;var m=(u-h)/2+c.border.top,_=(u-h)/2-c.border.top-c.border.bottom;e[4]="invert"===o||"automatic"===o&&g<1.01?Math.max(Math.min(e[4],r-c.border.left),-a):Math.min(Math.max(e[4],r),-a),e[5]="invert"===o||"automatic"===o&&p<1.01?Math.max(Math.min(e[5],m-c.border.top),-_):Math.min(Math.max(e[5],m),-_)}if("skip"!==i.animate&&this.transition(!i.animate),i.range&&this.$zoomRange.val(n),this.options.disableXAxis||this.options.disableYAxis){var v=this.getMatrix();this.options.disableXAxis&&(e[4]=v[4]),this.options.disableYAxis&&(e[5]=v[5])}return this.setTransform("matrix("+e.join(",")+")"),this.scale=n,this._checkPanWhenZoomed(n),i.silent||this._trigger("change",e),e}},isPanning:function isPanning(){return this.panning},transition:function transition(e){if(this._transition)for(var transition=e||!this.options.transition?"none":this._transition,i=this.$set,n=i.length;n--;)t.style(i[n],"transition")!==transition&&t.style(i[n],"transition",transition)},pan:function pan(e,t,i){if(!this.options.disablePan){i||(i={});var n=i.matrix;n||(n=this.getMatrix()),i.relative&&(e+=+n[4],t+=+n[5]),n[4]=e,n[5]=t,this.setMatrix(n,i),i.silent||this._trigger("pan",n[4],n[5])}},zoom:function zoom(e,i){"object"===o(e)?(i=e,e=null):i||(i={});var n=t.extend({},this.options,i);if(!n.disableZoom){var r=!1,a=n.matrix||this.getMatrix(),s=+a[0];"number"!=typeof e&&(e=n.exponential&&s-n.increment>=1?Math[e?"sqrt":"pow"](s,2):s+n.increment*(e?-1:1),r=!0),e>n.maxScale?e=n.maxScale:e<n.minScale&&(e=n.minScale);var c=n.focal;if(c&&!n.disablePan){this.resetDimensions();var l=n.dims=this.dimensions,d=c.clientX,u=c.clientY;this.isSVG||(d-=l.width/s/2,u-=l.height/s/2);var f=new Vector(d,u,1),h=new Matrix(a),g=this.parentOffset||this.$parent.offset(),p=new Matrix(1,0,g.left-this.$doc.scrollLeft(),0,1,g.top-this.$doc.scrollTop()),m=h.inverse().x(p.inverse().x(f)),_=e/a[0];h=h.x(new Matrix([_,0,0,_,0,0])),f=p.x(h.x(m)),a[4]=+a[4]+(d-f.e(0)),a[5]=+a[5]+(u-f.e(1))}a[0]=e,a[3]="number"==typeof n.dValue?n.dValue:e,this.setMatrix(a,{animate:void 0!==n.animate?n.animate:r,range:!n.noSetRange}),n.silent||this._trigger("zoom",a[0],n)}},option:function option(e,i){var n;if(!e)return t.extend({},this.options);if("string"==typeof e){if(1===arguments.length)return void 0!==this.options[e]?this.options[e]:null;(n={})[e]=i}else n=e;this._setOptions(n)},_setOptions:function _setOptions(e){t.each(e,t.proxy((function(e,i){switch(e){case"disablePan":this._resetStyle();case"$zoomIn":case"$zoomOut":case"$zoomRange":case"$reset":case"disableZoom":case"onStart":case"onChange":case"onZoom":case"onPan":case"onEnd":case"onReset":case"eventNamespace":this._unbind()}switch(this.options[e]=i,e){case"disablePan":this._initStyle();case"$zoomIn":case"$zoomOut":case"$zoomRange":case"$reset":this[e]=i;case"disableZoom":case"onStart":case"onChange":case"onZoom":case"onPan":case"onEnd":case"onReset":case"eventNamespace":this._bind();break;case"cursor":t.style(this.elem,"cursor",i);break;case"minScale":this.$zoomRange.attr("min",i);break;case"maxScale":this.$zoomRange.attr("max",i);break;case"rangeStep":this.$zoomRange.attr("step",i);break;case"startTransform":this._buildTransform();break;case"duration":case"easing":this._buildTransition();case"transition":this.transition();break;case"panOnlyWhenZoomed":this._checkPanWhenZoomed();break;case"$set":i instanceof t&&i.length&&(this.$set=i,this._initStyle(),this._buildTransform())}}),this))},_checkPanWhenZoomed:function _checkPanWhenZoomed(e){var t=this.options;if(t.panOnlyWhenZoomed){e||(e=this.getMatrix()[0]);var i=e<=t.minScale;t.disablePan!==i&&this.option("disablePan",i)}},_initStyle:function _initStyle(){var e={"transform-origin":this.isSVG?"0 0":"50% 50%"};this.options.disablePan||(e.cursor=this.options.cursor),this.$set.css(e);var i=this.$parent;i.length&&!t.nodeName(this.parent,"body")&&(e={overflow:"hidden"},"static"===i.css("position")&&(e.position="relative"),i.css(e))},_resetStyle:function _resetStyle(){this.$elem.css({cursor:"",transition:""}),this.$parent.css({overflow:"",position:""})},_bind:function _bind(){var e=this,i=this.options,n=i.eventNamespace,o="mousedown"+n+" pointerdown"+n+" MSPointerDown"+n,r="touchstart"+n+" "+o,a="touchend"+n+" click"+n+" pointerup"+n+" MSPointerUp"+n,s={},c=this.$reset,l=this.$zoomRange;if(t.each(["Start","Change","Zoom","Pan","End","Reset"],(function(){var e=i["on"+this];t.isFunction(e)&&(s["panzoom"+this.toLowerCase()+n]=e)})),i.disablePan&&i.disableZoom||(s[r]=function(t){var n;("touchstart"===t.type?!(n=t.touches||t.originalEvent.touches)||(1!==n.length||i.disablePan)&&2!==n.length:i.disablePan||(t.which||t.originalEvent.which)!==i.which)||(t.preventDefault(),t.stopPropagation(),e._startMove(t,n))},3===i.which&&(s.contextmenu=!1)),this.$elem.on(s),c.length&&c.on(a,(function(t){t.preventDefault(),e.reset()})),l.length&&l.attr({step:i.rangeStep===Panzoom.defaults.rangeStep&&l.attr("step")||i.rangeStep,min:i.minScale,max:i.maxScale}).prop({value:this.getMatrix()[0]}),!i.disableZoom){var d=this.$zoomIn,u=this.$zoomOut;d.length&&u.length&&(d.on(a,(function(t){t.preventDefault(),e.zoom()})),u.on(a,(function(t){t.preventDefault(),e.zoom(!0)}))),l.length&&((s={})[o]=function(){e.transition(!0)},s["input"+n]=function(){e.zoom(+this.value,{noSetRange:!0})},l.on(s))}},_unbind:function _unbind(){this.$elem.add(this.$zoomIn).add(this.$zoomOut).add(this.$reset).off(this.options.eventNamespace)},_buildTransform:function _buildTransform(){return this._origTransform=this.getTransform(this.options.startTransform)},_buildTransition:function _buildTransition(){if(this._transform){var e=this.options;this._transition=this._transform+" "+e.duration+"ms "+e.easing}},_getDistance:function _getDistance(e){var t=e[0],i=e[1];return Math.sqrt(Math.pow(Math.abs(i.clientX-t.clientX),2)+Math.pow(Math.abs(i.clientY-t.clientY),2))},_getMiddle:function _getMiddle(e){var t=e[0],i=e[1];return{clientX:(i.clientX-t.clientX)/2+t.clientX,clientY:(i.clientY-t.clientY)/2+t.clientY}},_trigger:function _trigger(e){"string"==typeof e&&(e="panzoom"+e),this.$elem.triggerHandler(e,[this].concat(n.call(arguments,1)))},_startMove:function _startMove(e,n){if(!this.panning){var o,r,a,s,c,l,d,u,f=this,h=this.options,g=h.eventNamespace,p=this.getMatrix(),m=p.slice(0),_=+m[4],v=+m[5],$={matrix:p,animate:"skip"},b=e.type;"pointerdown"===b?(o="pointermove",r="pointerup"):"touchstart"===b?(o="touchmove",r="touchend"):"MSPointerDown"===b?(o="MSPointerMove",r="MSPointerUp"):(o="mousemove",r="mouseup"),o+=g,r+=g,this.transition(!0),this.panning=!0,this._trigger("start",e,n);var w=function setStart(e,t){if(t){if(2===t.length){if(null!=a)return;return a=f._getDistance(t),s=f.getScale(p),void(c=f._getMiddle(t))}if(null!=l)return;(u=t[0])&&(l=u.pageX,d=u.pageY)}null==l&&(l=e.pageX,d=e.pageY)};w(e,n),t(i).off(g).on(o,(function move(e){var t;if(e.preventDefault(),n=e.touches||e.originalEvent.touches,w(e,n),n){if(2===n.length){var i=f._getMiddle(n),o=f._getDistance(n)-a;return f.zoom(o*(h.increment/100)+s,{focal:i,matrix:p,animate:"skip"}),f.pan(+p[4]+i.clientX-c.clientX,+p[5]+i.clientY-c.clientY,$),void(c=i)}t=n[0]||{pageX:0,pageY:0}}t||(t=e),f.pan(_+t.pageX-l,v+t.pageY-d,$)})).on(r,(function(e){e.preventDefault(),t(this).off(g),f.panning=!1,e.type="panzoomend",f._trigger(e,p,!function matrixEquals(e,t){for(var i=e.length;--i;)if(Math.round(+e[i])!==Math.round(+t[i]))return!1;return!0}(p,m))}))}}},t.Panzoom=Panzoom,t.fn.panzoom=function(e){var i,o,r,a;return"string"==typeof e?(a=[],o=n.call(arguments,1),this.each((function(){(i=t.data(this,"__pz__"))?"_"!==e.charAt(0)&&"function"==typeof(r=i[e])&&void 0!==(r=r.apply(i,o))&&a.push(r):a.push(void 0)})),a.length?1===a.length?a[0]:a:this):this.each((function(){new Panzoom(this,e)}))}})(n="undefined"!=typeof window?window:this,n.jQuery)},374:function(e,t,i){"use strict";var n,o,r,a,s,c,l,d,u=i(26);n=$(".figure-viewer"),o=$(window),r=$("html"),a=$(document),s=$("body"),c=!1,l=!1,d={options:{filter:!1,filterClass:"article__tabFigure"},$topRegHeight:$(".figure-viewer__reg__top").innerHeight(),vPort:"screen-md",$figures:null,$figuresToShow:null,$figureWidth:null,$currentIndex:null,$focusedElementBeforeOpened:null,$section:n,$holder:n.find(".figure-viewer__hold__fig"),$holderElement:n.find(".figure-viewer__hold__fig").find("figure"),$captionHolder:n.find(".figure-viewer__hold__figcap"),$lister:n.find(".figure-viewer__hold__list"),$figureRegionContainer:n.find(".figure-viewer__reg__center"),$captionRegion:n.find(".figure-viewer__cent__right"),$contentRegion:n.find(".figure-viewer__cent__left"),$figureNav:n.find(".figure-viewer__ctrl__next, .figure-viewer__ctrl__prev"),$zoominbtn:n.find(".zoom-in"),$zoomoutbtn:n.find(".zoom-out"),$zoomrange:n.find(".zoom-range"),$zoomreset:n.find(".reset"),$image:n.find("figure"),$browsebtn:$(".figure-viewer__ctrl__browse"),$returnbtn:$(".figure-viewer__ctrl__return"),isMobile:!1,islocked:!1,offsetY:window.pageYOffset,$hideList:$(".zoomSlider, .figure-viewer__label__zoom"),$browseContainer:$(".figure-viewer__hold__list"),$captionTitle:$(".figure-viewer__title__text"),onHighRes:function onHighRes(){i.e(65).then(i.t.bind(null,387,7)).then((function(){d.onHighResCallback()}))},expand:function expand(){d.options.filter?d.$figures=a.find(":not(.figure-viewer__hold__list) > figure:not(.holder), :not(.figure-viewer__hold__list) > .figure:not(.holder)"):d.$figures=a.find("figure:not(.holder), .figure:not(.holder)"),d.$figures.each((function(e){var t=$(this);if(t.data("index",e),d.options.filter&&(t.addClass("figure"),"dataRel"===d.options.filter)){var i=t.find("[data-rel]");i.length>0&&i.data().rel&&t.attr("data-rel",i.data().rel)}})),d.$figuresToShow=d.$figures},filter:function filter(e){if("classCombination"===d.options.filter){var t=e.attr("class");d.$figuresToShow=d.$figures.filter('[class="'+t+'"]')}else if("className"===d.options.filter)d.$figuresToShow=d.$figures.filter("."+d.options.filterClass);else if("dataRel"===d.options.filter){var i=e.data("rel");d.$figuresToShow=i?d.$figures.filter('[data-rel="'+i+'"]'):d.$figures}else d.$figuresToShow=d.$figures;d.$currentIndex=d.$figuresToShow.index(e)},control:function control(){$(document).on(d.vPort+"-on",(function(){d.isMobile=!0})),$(document).on(d.vPort+"-off",(function(){d.isMobile=!1})),d.clickHandler(),s.find(".figure-viewer").find("a, button").each((function(){var e=$(this);e.on({click:function click(t){t.preventDefault(),t.stopPropagation(),e.hasClass("figure-viewer__ctrl__close")?d.on.hide():e.hasClass("figure-viewer__ctrl__next")?d.next():e.hasClass("figure-viewer__ctrl__prev")?d.prev():e.hasClass("figure-viewer__ctrl__caption")?d.captionToggle(e):e.hasClass("figure-viewer__ctrl__browse")?(l=!1,d.browse($(this))):e.hasClass("figure-viewer__ctrl__return")&&d.return($(this))}})})),d.options.filter?($(".figure a:not(.open-figure-link)").not(":has(img)").click((function(e){var t=$(this).find("img");$(this).hasClass("linkBehavior")&&0===t.length||(e.preventDefault(),t.trigger("click"))})),$(".figure a:not(.open-figure-link) img").click((function(e){e.stopPropagation()}))):$(".figure a:not('.ppt-figure-link')").not(":has(img)").click((function(e){var t=$(this).find("img");$(this).hasClass("linkBehavior")&&0===t.length||t.trigger("click")})),$("body").keydown((function(e){"figureViewer"===s.attr("data-active")&&(37===e.keyCode?d.prev():39===e.keyCode&&d.next())})),d.supplementary.control(),d.hideOnClickOutside(),d.resize(),$(window).on("resized",(function(){d.resize()})),1===d.$figures.length&&d.$figureNav.hide(),Object(u.b)(d.$section[0])},hideOnClickOutside:function hideOnClickOutside(){d.$image.on("click",(function(e){!($(e.target).closest(".figure__image").length||$(".figure__image").is(e.target)||$(".shortcutsViewHelpTxt").is(e.target)||$(e.target).closest(".shortcutsViewHelpTxt").length||$(".figure__highResImgReplacer").is(e.target)||$(e.target).closest(".figure__highResImgReplacer").length)&&d.on.hide()}))},clickHandler:function clickHandler(){$(document).on("click",".figure__image, .open-figure-link",(function(e){e.preventDefault();var t=$(this);if(!(t.parent().hasClass("figure-viewer__thumbnail-item")||t.is(".figure__audio")||t.is(".figure__video"))){var i=t.closest("figure, .figure, .imageTable");i.hasClass("ui-disabled")||i.closest(".figure-viewer__hold__fig").length||d.on.show(i,i.data("index")),!c&&$(".shortcutsViewHelpTxt").length>=0?$(document).find(".figure-viewer__cent__left .shortcutsViewHelpTxt").remove():UX.highResImg.shortcutsView(i)}}))},captionToggle:function captionToggle(e){d.toggle(e.closest(".figure-viewer__cent__right")),d.toggle($(".figure-viewer__cent__left")),e.toggleClass("js-open"),1===d.$holder.find(".figure__highResImgReplacer").length&&e.hasClass("js-open")?d.$holder.find(".figure__highResImgReplacer").css({width:"97%"}):d.$holder.find(".figure__highResImgReplacer").css({width:"100%"});var t=document.querySelector(".figure-viewer__ctrl__caption"),i=document.querySelector(".figure-viewer__hold__figcap"),o=document.querySelector(".figure-viewer__title__text");l=!l,t.setAttribute("aria-expanded",l),i.classList.toggle("hidden",!l),o.classList.toggle("hidden",!l),UX.utils.focusCycleInitiator(n[0]),d.updateAriaLabelForCaptionHolder()},next:function next(){if(d.options.filter||(d.$figuresToShow=d.$figures),d.$currentIndex<d.$figuresToShow.length-1){if(!d.$figuresToShow.eq(d.$currentIndex+1).closest(".figure-viewer").length){d.$currentIndex++;var e=d.$figuresToShow.eq(d.$currentIndex);d.replace(e),!1===c&&$(".shortcutsViewHelpTxt").length>0&&$(document).find(".figure-viewer__cent__left .shortcutsViewHelpTxt").remove(),c&&UX.highResImg.shortcutsView(e),d.zoom.reset()}}else{d.$currentIndex=0;var t=d.$figuresToShow.eq(d.$currentIndex);d.replace(t),!1===c&&$(".shortcutsViewHelpTxt").length>0&&$(document).find(".figure-viewer__cent__left .shortcutsViewHelpTxt").remove(),c&&UX.highResImg.shortcutsView(t),d.zoom.reset()}d.updateAriaLabelForCaptionHolder()},prev:function prev(){if(d.options.filter||(d.$figuresToShow=d.$figures),d.$currentIndex>=1){if(!d.$figuresToShow.eq(d.$currentIndex-1).closest(".figure-viewer").length){d.$currentIndex--;var e=d.$figuresToShow.eq(d.$currentIndex);d.replace(e),!1===c&&$(".shortcutsViewHelpTxt").length>0&&$(document).find(".figure-viewer__cent__left .shortcutsViewHelpTxt").remove(),c&&UX.highResImg.shortcutsView(e),d.zoom.reset()}}else{d.$currentIndex=d.$figuresToShow.length-1;var t=d.$figuresToShow.eq(d.$currentIndex);d.replace(t),!1===c&&$(".shortcutsViewHelpTxt").length>0&&$(document).find(".figure-viewer__cent__left .shortcutsViewHelpTxt").remove(),c&&UX.highResImg.shortcutsView(t),d.zoom.reset()}d.updateAriaLabelForCaptionHolder()},replace:function replace(e){var t;d.pauseOriginalStream(e),(t=(c=e.find(".figure__image").length&&e.find(".figure__highResImgReplacer").length)?e.find(".figure__highResImgReplacer").clone():e.find(".figure__image").clone()).each((function(){var t=$(this);if(t.attr("data-lg-src"))t.attr("src",t.attr("data-lg-src")),t.attr("data-lg-width")&&t.attr("data-lg-height")&&(t.attr("width",t.attr("data-lg-width")),t.attr("height",t.attr("data-lg-height")));else if(t.attr("data-src"))t.attr("src",t.attr("data-src"));else{var i=e.children("a");i.attr("href",i.find(".figure__image").attr("src"))}})),t.length>1?t=d.supplementary.show(e,t):d.supplementary.hide(),d.streamReset(d.$holder,e),c&&(UX.highResImg.show(d.$holder,e),d.$figureWidth=d.$holder.find(".figure__highResImgReplacer").width()),d.$holder.find(".figure__highResImgReplacer").remove(),d.$holder.find(".figure__image").remove(),d.$holder.find("figure").append(t.get(0).outerHTML),d.$captionHolder.empty(),d.showCaptionTitle(e);var i=d.additionalBehaviorOnShow(e);d.showCaption(i),d.removeFigureTitle(),d.$figureWidth=d.$holder.find(".figure__image").width(),d.zoom.check(),d.headerOptions(e),d.additionalReplace()},additionalReplace:function additionalReplace(){},supplementary:{nav:null,show:function show(e,t){var i=this.check(e),n=t.clone(),o=this;return this.nav||(this.nav=$('<ul class="figure-viewer__hold__supplementary-nav"></ul>'),this.nav.appendTo(d.$section)),this.nav.empty(),n.each((function(t){var i=$('<li><a href="#" title="Go to figure image '+(t+1)+'. of current figure." class="figure-viewer__thumbnail-item"></a></li>');i.find("a").append(this),i.find("a").on("click",(function(i){i.preventDefault(),o.toIndex(e,t)})),o.nav.append(i)})),this.nav.find("li").eq(i).addClass("active"),t.eq(i)},hide:function hide(){this.nav&&this.nav.empty()},save:function save(e,t){e.data("figure-viewer-display",t)},check:function check(e){return e.data("figure-viewer-display")||0},toIndex:function toIndex(e,t){var i=e.find(".figure__image");if(1!==i.length){t>=i.length&&(t=0),t<0&&(t=i.length-1),d.$holder.find(".figure__image").remove(),d.$holder.find(".figure__highResImgReplacer").remove();var n=i.eq(t).clone();n.data("lg-src")&&n.attr("src",n.data("lg-src")),d.$holderElement.append(n),this.nav.find("li").removeClass("active"),this.nav.find("li").eq(t).addClass("active"),this.save(e,t)}},next:function next(e){var t=this.check(e)+1;this.toIndex(e,t)},prev:function prev(e){var t=this.check(e)-1;this.toIndex(e,t)},control:function control(){$("body").on("keydown keyup",(function(e){if("figureViewer"===s.attr("data-active")&&(d.options.filter||(d.$figuresToShow=d.$figures),!(d.$figuresToShow&&d.$figuresToShow.length<1))){var t=d.$figuresToShow.eq(d.$currentIndex);38===e.keyCode?d.supplementary.prev(t):40===e.keyCode&&d.supplementary.next(t)}}))}},browse:function browse(e){d.$holder.addClass("is-hidden"),d.$figureNav.addClass("is-hidden"),d.$browsebtn.addClass("is-hidden"),d.$returnbtn.removeClass("is-hidden"),d.$captionRegion.removeClass("js-open"),d.$contentRegion.removeClass("js-open"),d.$figureRegionContainer.addClass("scrollable"),d.$captionRegion.addClass("is-hidden"),d.$hideList.each((function(){$(this).addClass("is-hidden")})),d.$lister.removeClass("is-hidden"),d.supplementary.hide(),d.$browseContainer.empty(),d.options.filter?d.$figuresToShow.each((function(){var e=$(this).clone(!0),t=e.find(".figure-extra");t.length&&t.remove(),d.$browseContainer.append(e)})):d.$figures.each((function(){if($(this).hasClass("article__inlineFigure")){var e=$(this).clone(!0),t=e.children("img");t.attr("src",t.data("src"));var i=e.find(".figure-links");i.length&&i.remove(),d.$browseContainer.append(e)}})),d.additionalBrowse()},additionalBrowse:function additionalBrowse(){},return:function _return(){d.$holder.removeClass("is-hidden"),d.$figureNav.removeClass("is-hidden"),d.$browsebtn.removeClass("is-hidden"),d.$returnbtn.addClass("is-hidden"),d.$captionRegion.removeClass("is-hidden"),d.$hideList.each((function(){$(this).removeClass("is-hidden")})),d.$lister.addClass("is-hidden");var e=$(".figure-viewer__title__text");e.removeClass("figure-viewer__title__text__allfigures"),e.parent(".figure-viewer__ctrl").removeClass("allfigures__is__displayed"),$(".figure-viewer__ctrl__caption").parent(".figure-viewer__ctrl").show(),d.$browseContainer.empty(),d.additionalReturn()},additionalReturn:function additionalReturn(){},height:function height(e){var t=$(window).innerHeight();d.$topRegHeight=$(".figure-viewer__reg__top").innerHeight();var i=t-d.$topRegHeight-$(".figure-viewer__caption__label").innerHeight();$(".figure-viewer__cent__left").find("figure").height(i)},toggle:function toggle(e){e.toggleClass("js-open"),d.figcaptionSize()},displayViewer:function displayViewer(){$(".figure-viewer").show()},additionalShow:function additionalShow(){},on:{show:function show(e,t){d.isMobile&&r.addClass("lock-screen"),d.headerOptions(e),d.offsetY=s.data("yoffset")||window.pageYOffset,s.data("yoffset",d.offsetY),void 0!==UX.controller&&"slide"!==s.attr("data-active")&&UX.controller.check(),s.attr("data-active","figureViewer"),d.islocked=!0,d.$currentIndex=t,d.replace(e),d.$focusedElementBeforeOpened=document.activeElement,d.displayViewer(),s.css("overflow","hidden"),d.height(),d.zoom.check(),d.resize(),d.$holder.removeClass("is-hidden"),d.$figuresToShow.length>1&&d.$browsebtn.removeClass("is-hidden"),d.$returnbtn.addClass("is-hidden"),d.$figureNav.removeClass("is-hidden"),d.$hideList.each((function(){$(this).removeClass("is-hidden")})),d.$lister.addClass("is-hidden"),d.$figureRegionContainer.removeClass("scrollable"),$(".figure-viewer__ctrl__close").focus(),d.options.filter?d.filter(e):d.setFiguersToShow(e),d.additionalShow(),d.streamReset(d.$holder,e.closest("figure")),c&&UX.highResImg.show(d.$holder,e.closest("figure")),d.on.additionalOnShow()},hide:function hide(){d.isMobile&&r.hasClass("lock-screen")&&r.removeClass("lock-screen"),d.islocked?s.removeAttr("data-active"):s.removeClass("lock-screen").removeAttr("data-active"),d.zoom.reset(),$(".figure-viewer").hide(),d.pauseClonedStream(d.$holder),d.$focusedElementBeforeOpened.focus(),s.css("overflow",""),$(window).scrollTop(d.offsetY),s.removeData("yoffset"),d.on.additionalOnHide()},additionalOnShow:function additionalOnShow(){},additionalOnHide:function additionalOnHide(){}},setFiguersToShow:function setFiguersToShow(e){e.hasClass("article__tabFigure")?d.$figuresToShow=d.$figures.filter(".article__tabFigure"):e.hasClass("article__inlineFigure")?d.$figuresToShow=d.$figures.filter(".article__inlineFigure"):d.$figuresToShow=d.$figures},additionalBehaviorOnShow:function additionalBehaviorOnShow(e){return e.find("figcaption, .figcaption").clone()},zoom:{reset:function reset(){$(".figure-viewer").find("figure").panzoom("reset")},check:function check(){d.zoom.panzoom.init(),o.width()<992&&d.zoom.swap()},panzoom:{init:function init(){void 0!==$.fn.panzoom&&(d.$image.panzoom({$zoomIn:d.$zoominbtn,$zoomOut:d.$zoomoutbtn,$zoomRange:d.$zoomrange,$reset:d.$zoomreset,panOnlyWhenZoomed:!0,minScale:1}),d.$image.on("panzoomchange",(function(e){"1"===d.$zoomrange.val()?(d.$image.css("transform","matrix(1, 0, 0, 1, 0, 0)"),$(this).removeClass("zoomed")):$(this).addClass("zoomed")})))}},swap:function swap(){d.$section.find("img").swipe({swipe:function swipe(e,t,i,n,o,r){"left"!==t||d.$section.find("figure").hasClass("zoomed")?"right"!==t||d.$section.find("figure").hasClass("zoomed")||d.prev():d.next()}})}},slide:function slide(){var e=s.find("div.ux3-in-slide");s.removeAttr("data-ux3-show"),setTimeout((function(){e.append($slide.children("div")),e.removeClass("ux3-in-slide")}),250)},resize:function resize(){var e=o.innerHeight(),t=d.$topRegHeight||56;d.isMobile?d.$figureRegionContainer.css("height",e-t):d.$figureRegionContainer.css("height","calc(100vh - ".concat(t,"px)"))},figcaptionSize:function figcaptionSize(){var e=$(".figure-viewer__hold__figcap").find("figcaption"),t=d.$figureRegionContainer.height(),i=$(".figure-viewer__title").height()+30;e.css("max-height",t-60-i)},doubleTap:function doubleTap(){!function(e){e.fn.nodoubletapzoom=function(){e(this).bind("touchstart",(function preventZoom(t){var i=t.timeStamp,n=i-(e(this).data("lastTouch")||i),o=t.originalEvent.touches.length;e(this).data("lastTouch",i),!n||n>500||o>1||(t.preventDefault(),e(t.target).trigger("click"))}))}}(jQuery)},init:function init(){d.expand(),d.control(),d.doubleTap(),$("body").nodoubletapzoom(),$(".figure__highResImgReplacer").length&&d.onHighRes()},showCaption:function showCaption(e){e.length?(d.$captionHolder.append(e),d.$captionHolder.find("figcaption").removeClass("hidden"),d.$captionRegion.removeClass("is-hidden"),d.$captionRegion.hasClass("js-open")&&d.$contentRegion.addClass("js-open")):d.$captionHolder.hasClass("is-hidden")||(d.$captionRegion.addClass("is-hidden"),d.$contentRegion.removeClass("js-open"))},showCaptionTitle:function showCaptionTitle(e){var t=e.find(".figure__title, .captionLabel").clone();$(".figure-viewer__title__text").text(t.text())},headerOptions:function headerOptions(e){var t=e.find(".figure-extra").clone();s.find(".figureExtra").length&&s.find(".figureExtra").remove(),e.find(".figure__image").is(".figure__audio")||e.find(".figure__image").is(".figure__video")?(s.find(".figure-viewer__top__left").addClass("hidden").after('<div class="figure-viewer__top__left figureExtra">'),s.find(".figureExtra").append(t),t.appendTo(s.find(".figureExtra")),s.find(".figureExtra").find(".open-figure-link").remove()):s.find(".figure-viewer__top__left").hasClass("hidden")&&s.find(".figure-viewer__top__left").removeClass("hidden")},streamReset:function streamReset(e,t){setTimeout((function(){e.find("stream").length?(e.find("stream").html(""),$.getScript(UX.loader.cloudFlareScript),e.find("stream").get(0).currentTime=t.find("stream").get(0).currentTime):e.find("audio").length&&(e.find("audio").get(0).currentTime=t.find("audio").get(0).currentTime)}),500)},pauseOriginalStream:function pauseOriginalStream(e){e.find("stream, audio").length&&e.find("stream, audio").get(0).pause()},pauseClonedStream:function pauseClonedStream(e){e.find("stream, audio").length&&e.find("stream, audio").get(0).pause()},removeFigureTitle:function removeFigureTitle(){d.$captionHolder.find(".figure__title").remove()},onHighResCallback:function onHighResCallback(){},updateAriaLabelForCaptionHolder:function updateAriaLabelForCaptionHolder(){d.$captionHolder.attr("aria-label","Caption for ".concat(d.$captionTitle.text()))}},UX.figureViewer=d},376:function(e,t,i){"use strict";var n=i(32),o=i(105).map;n({target:"Array",proto:!0,forced:!i(377)("map")},{map:function map(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},377:function(e,t,i){var n=i(15),o=i(10),r=i(89),a=o("species");e.exports=function(e){return r>=51||!n((function(){var t=[];return(t.constructor={})[a]=function(){return{foo:1}},1!==t[e](Boolean).foo}))}},378:function(e,t){var i;i={init:function init(){i.controller()},controller:function controller(){var e,t="https://crossmark.crossref.org/dialog",i="v2.0",n="https://crossmark-cdn.crossref.org/widget/v2.0/style.css",o=!1,r=function tapEvent(t,i){t.addEventListener("click",(function(e){if(!(e.ctrlKey||e.shiftKey||e.metaKey||1!==e.which))return i(e)}),!1),t.addEventListener("touchstart",(function(i){if(i.touches.length>1)return o=!1;e={x:i.touches[0].screenX,y:i.touches[0].screenY},o=t,i.stopPropagation()}),!1),window.addEventListener("touchstart",(function(e){o=!1})),t.addEventListener("touchmove",(function(t){if(t.touches.length>1)return o=!1;var i=t.touches[0].screenX,n=t.touches[0].screenY;Math.pow(e.x-i,2)+Math.pow(e.y-n,2)>500&&(o=!1)}),!1),t.addEventListener("touchend",(function(e){if(o)return o=!1,i(e);e.preventDefault()}),!1)},a=function buildQueryString(e){var t=[];for(var i in e)t.push(encodeURIComponent(i)+"="+encodeURIComponent(e[i]));return"?"+t.join("&")},s=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,c=function getDoiMeta(){var e=document.querySelectorAll("meta");for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)){var i=e[t];if("dc.identifier"===(i.name||"").toLowerCase()&&"doi"===i.scheme)return i}}(),l=c?c.getAttribute("content").replace(/^(info:doi\/|doi:)/,""):null;null==l&&(l=$("#crossMark").data("doi"));var d={doi:l,domain:window.location.hostname,uri_scheme:window.location.protocol,cm_version:i},u=document.createElement("link");u.setAttribute("href",n),u.setAttribute("type","text/css"),u.setAttribute("rel","stylesheet"),document.querySelector("head").appendChild(u);var f=document.createElement("div");f.setAttribute("id","crossmark-widget"),f.style.display="none",f.innerHTML='<div class="crossmark-reset crossmark-overlay"></div><div class="crossmark-reset crossmark-popup"><div class="crossmark-reset crossmark-popup__offset"><div class="crossmark-reset crossmark-popup__inner"><div class="crossmark-reset crossmark-popup__header"><button class="crossmark-reset crossmark-popup__btn-close"><span class="sr-only">Close crossmark popup</span></button></div><div class="crossmark-reset crossmark-popup__content-wrapper"><iframe title="Crossmark service - Intentionally blank" aria-hidden="true" class="crossmark-reset crossmark-popup__content"></iframe></div></div></div></div>';var h=f.querySelector(".crossmark-overlay"),g=f.querySelector(".crossmark-popup"),p=f.querySelector(".crossmark-popup__offset"),m=f.querySelector(".crossmark-popup__inner"),_=f.querySelector(".crossmark-popup__content"),v=f.querySelector(".crossmark-popup__btn-close");s&&p.classList.add("is-ios"),document.body.appendChild(f),[h,g,v].map((function(e){r(e,(function(e){f.style.display="none",e.preventDefault(),e.stopPropagation()}))})),r(m,(function(e){e.stopPropagation()}));var b=!1;[].slice.call(document.querySelectorAll("[data-target=crossmark]"),0).map((function(e){e.style.cursor="pointer",e.setAttribute("href",t+a(d)),r(e,(function(e){b||(_.setAttribute("src",t+a(d)),b=!0),f.style.display="block",s&&(m.style.top=window.scrollY+"px"),e.preventDefault(),e.stopPropagation()}))}))}},UX.initCrossref=i,"loading"!==document.readyState?UX.initCrossref.init():document.addEventListener("DOMContentLoaded",(function(){UX.initCrossref.init()}))},379:function(e,t){var i;i={init:function init(){i.control()},copy:function copy(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=document.createElement("textarea");n.id="temp_element",n.value=document.location.protocol+"//"+document.location.host+t+e,n.setAttribute("readonly",""),document.body.appendChild(n);var o=document.querySelector("#temp_element");navigator.userAgent.match(/ipad|ipod|iphone/i)?o.setSelectionRange(0,999999):o.select(),document.execCommand("copy"),document.body.removeChild(n);var r=$.Event("link:copied");$(document).trigger(r,[i]),r.isDefaultPrevented()},control:function control(){0===$("figure .figure__image").length&&$(".download_Figures").closest(".article-tool").hide();var e=document.querySelectorAll(".article-tool__permissions");e.length>0&&e.forEach((function(e){var t=e.getAttribute("href");e.addEventListener("click",(function(){window.open(t,"popup","width=750,height=650")}))}));var t=document.querySelectorAll(".likeLink");t.length>0&&t.forEach((function(e){var t=e.getAttribute("data-url");e.addEventListener("click",(function(){i.copy(t,"/lti")}))}))}},UX.articleTools=i,$(".article-tools__ctrl").length&&UX.articleTools.init()}}]);
//# sourceMappingURL=vendors~article-38043b24fada0918b38f.js.map