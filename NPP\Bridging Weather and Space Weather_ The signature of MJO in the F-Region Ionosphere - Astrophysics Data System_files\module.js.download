define(["backbone","underscore","js/components/generic_module","./orcid_api"],function(e,t,i,o){var n=i.extend({activate:function(e){var i=e.getObject("DynamicConfig");if(!i)throw new Error("DynamicConfig is not available to Orcid module");var o=i.orcidRedirectUrlBase||location.protocol+"//"+location.host,n=i.orcidClientId,r=i.orcidApiEndpoint,c=i.orcidLoginEndpoint;if(!n||!r)throw new Error("Missing configuration for ORCID module: orcidApiEndpoint, orcidClientId");t.extend(i,{Orcid:{redirectUrlBase:o,apiEndpoint:r,clientId:n,worksUrl:r+"/{0}/orcid-works",loginUrl:c+"?scope=/orcid-profile/read-limited%20/orcid-works/create%20/orcid-works/update&response_type=code&access_type=offline&show_login=true&client_id="+n,exchangeTokenUrl:r+"/exchangeOAuthCode"}}),this.activateDependencies(e)},activateDependencies:function(e){var i=e.getService("OrcidApi");i||((i=new o).activate(e),e.addService("OrcidApi",i))}});return function(){return{activate:function(e){(new n).activate(e)}}}});
//# sourceMappingURL=module.js.map