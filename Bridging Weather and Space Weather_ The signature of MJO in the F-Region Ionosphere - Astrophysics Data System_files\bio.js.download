define(["underscore","jsonpath","js/modules/orcid/work"],function(i,t,e){var r={firstName:'$.name["given-names"].value',lastName:'$.name["family-name"].value',orcid:"$.name.path"};return function(e){this._root=e||{},this.get=function(e){return t.query(this._root,e)[0]},this.toADSFormat=function(){return{responseHeader:{params:{orcid:this.getOrcid(),firstName:this.getFirstName(),lastName:this.getLastName()}}}},i.reduce(r,function(e,t,r){return i.isString(r)&&r.slice&&(e["get"+(r[0].toUpperCase()+r.slice(1))]=i.partial(e.get,t)),e},this)}});
//# sourceMappingURL=bio.js.map