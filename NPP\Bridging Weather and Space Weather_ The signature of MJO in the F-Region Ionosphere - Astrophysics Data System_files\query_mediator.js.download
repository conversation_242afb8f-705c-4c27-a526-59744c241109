define(["underscore","jquery","cache","js/components/generic_module","js/mixins/dependon","js/mixins/add_secondary_sort","js/components/api_request","js/components/api_response","js/components/api_query_updater","js/components/api_feedback","js/components/json_response","js/components/api_targets","js/components/api_query","js/components/alerts","utils","analytics"],function(u,c,t,e,i,o,a,n,s,h,l,g,_,y,p,d){e=e.extend({initialize:function(e){e=e||{},this._cache=null,this.debug=e.debug||!1,this.queryUpdater=new s("QueryMediator"),this.failedRequestsCache=this._getNewCache({expiresAfterWrite:5}),this.maxRetries=e.maxRetries||3,this.recoveryDelayInMs=u.isNumber(e.recoveryDelayInMs)?e.recoveryDelayInMs:700,this.__searchCycle={waiting:{},inprogress:{},done:{},failed:{}},this.shortDelayInMs=u.isNumber(e.shortDelayInMs)?e.shortDelayInMs:300,this.longDelayInMs=u.isNumber(e.longDelayInMs)?e.longDelayInMs:100,this.monitoringDelayInMs=u.isNumber(e.monitoringDelayInMs)?e.monitoringDelayInMs:200,this.mostRecentQuery=new _},activateCache:function(e){this._cache=this._getNewCache((e||{}).cache)},_getNewCache:function(e){return new t(u.extend({maximumSize:100,expiresAfterWrite:1800},u.isObject(e)?e:{}))},activate:function(e,t){this.setBeeHive(e),this.setApp(t);e=this.getPubSub();e.subscribe(e.START_SEARCH,u.bind(this.getQueryAndStartSearchCycle,this)),e.subscribe(e.DELIVERING_REQUEST,u.bind(this.receiveRequests,this)),e.subscribe(e.EXECUTE_REQUEST,u.bind(this.executeRequest,this)),e.subscribe(e.GET_QTREE,u.bind(this.getQTree,this))},getQTree:function(e,t){e=new a({query:e,target:g.QTREE});this._executeRequest(e,t)},doQueryTranslation:function(e){var t=c.Deferred(),i=this.getPubSub(),e=new a({target:g.SERVICE_OBJECTS_QUERY,query:new _({query:e}),options:{type:"POST",contentType:"application/json"}});return i.subscribeOnce(i.DELIVERING_RESPONSE,function(e){t.resolve(e.toJSON())}),i.publish(i.EXECUTE_REQUEST,e),t.promise()},getQueryAndStartSearchCycle:function(i,s){var e,t,r=this,n=this.getPubSub();o.addSecondarySort(i),this.original_url&&i.get("q")&&-1!==i.get("q")[0].indexOf("simbid")?(t=i.clone(),e=i.get("q")[0],i.has("__original_query")&&(e=i.get("__original_query")[0]),t.set("q",e),this.original_url=this.queryUpdater.clean(t).url()):this.original_url=this.queryUpdater.clean(i).url(),this.original_query=i.get("q"),i.get("__bigquery")?(i.set("bigquery","bibcode\n"+i.get("__bigquery").join("\n")),i.unset("__bigquery"),i.get("q")||i.set("q","*:*"),i.add("fq","{!bitset}"),e=new a({target:g.MYADS_STORAGE+"/query",query:i,options:{type:"POST",contentType:"application/json",done:function(e){var e=new _({q:i.get("q"),__qid:e.qid}),t=i.get("__bigquerySource");t&&e.set("__bigquerySource",t[0]),i.get("sort")&&e.set("sort",i.get("sort")),r.startSearchCycle(e,s)},fail:function(e,t,i){console.warn("bigquery failed:",[].slice.apply(arguments).join(",")),n.publish(n.FEEDBACK,new h({code:h.CODES.SEARCH_CYCLE_FAILED_TO_START,error:{jqXHR:e,textStatus:t,errorThrown:i}}))}}}),this.getBeeHive().getService("Api").request(e)):-1<i.get("q")[0].indexOf("object:")?(t=function(e){i.set({q:e.query}),this.startSearchCycle(i,s)}.bind(this),this.doQueryTranslation(i.get("q")[0]).done(t)):this.startSearchCycle.apply(this,arguments)},startSearchCycle:function(e,t){var i,s;this.resetFailures(),this.getBeeHive().getObject("AppStorage")&&this.getBeeHive().getObject("AppStorage").clearSelectedPapers(),e.has("__saveBigQuery")&&this.mostRecentQuery.has("__qid")&&!e.has("__qid")&&(this.mostRecentQuery.set("q",e.get("q")),e=this.mostRecentQuery),this.mostRecentQuery=e,this.debug&&console.log("[QM]: received query:",this.hasApp()&&this.getApp().getPluginOrWidgetName(t.getId())||t.getId(),e.url()),e.keys().length<=0?console.error("[QM] : received empty query (huh?!)"):(-1<e.get("q")[0].indexOf("simbid")&&(e.add("__original_url",this.original_url),e.add("__original_query",this.original_query)),i=this.getPubSub(),this.__searchCycle.running&&this.__searchCycle.waiting&&u.keys(this.__searchCycle.waiting)&&(console.error("The previous search cycle did not finish, and there already comes the next!"),u.forEach(u.extend({},this.__searchCycle.waiting,this.__searchCycle.inprogress),function(e){e.request.__STALE=!0})),this.reset(),this.__searchCycle.initiator=t.getId(),this.__searchCycle.collectingRequests=!0,this.__searchCycle.query=e.clone(),t=e.clone(),t.unset("fl"),t.lock(),i.publish(i.INVITING_REQUEST,t),s=this,e=function(){s.__searchCycle.collectingRequests=!1,s.startExecutingQueries()&&s.monitorExecution()},this.shortDelayInMs?setTimeout(e,this.shortDelayInMs):e())},startExecutingQueries:function(e){var s,r=this,n=this.__searchCycle;if(!n.running&&!u.isEmpty(n.waiting)&&this.hasBeeHive()){n.running=!0;var o,t=this.getBeeHive().getService("Api"),a=this.getPubSub();if(a&&t)return(t=this.getApp().getPskOfPluginOrWidget("widget:Results"))&&n.waiting[t]&&(s=n.waiting[t],delete n.waiting[t]),!s&&n.waiting[n.initiator]&&(s=n.waiting[n.initiator],delete n.waiting[n.initiator]),s||(this.debug&&console.warn("DynamicConfig does not tell us which request to execute first (grabbing random one)."),s=n.waiting[t=u.keys(n.waiting)[0]],delete n.waiting[t]),o=s.key.getId(),n.inprogress[o]=s,this._executeRequest(s.request,s.key).done(function(e){function t(e){var t;s.request.__STALE||(n.done[o]=s,delete n.inprogress[o],e.response&&e.response.numFound&&(t=e.response.numFound),a.publish(a.FEEDBACK,new h({code:h.CODES.SEARCH_CYCLE_STARTED,query:n.query,request:s.request,numFound:t,cycle:n,response:e})),r.displayTugboatMessages())}function i(){u.each(u.keys(n.waiting),function(e){s=n.waiting[e],delete n.waiting[e],n.inprogress[e]=s;var t=e;r._executeRequest.call(r,s.request,s.key).done(function(){s.request.__STALE||(n.done[t]=n.inprogress[t],delete n.inprogress[t])}).fail(function(){s.request.__STALE||(n.failed[t]=n.inprogress[t],delete n.inprogress[t])}).always(function(){n.finished||u.isEmpty(n.inprogress)&&(a.publish(a.FEEDBACK,new h({code:h.CODES.SEARCH_CYCLE_FINISHED,cycle:n})),n.finished=!0)})})}e.then?Promise.resolve(e).then(function(e){t(e)}):t(e);r.longDelayInMs&&0<r.longDelayInMs?setTimeout(function(){i()},r.longDelayInMs):i()}).fail(function(e,t,i){r.__searchCycle.error=!0,a.publish(a.FEEDBACK,new h({code:h.CODES.SEARCH_CYCLE_FAILED_TO_START,cycle:n,request:this.request,error:{jqXHR:e,textStatus:t,errorThrown:i}}))}),!0}},monitorExecution:function(){if(this.hasBeeHive()){var e=this,t=this.getPubSub();if(t)if(this.__searchCycle.monitor+=1,100<this.__searchCycle.monitor)console.warn("Stopping monitoring of queries, it is running too long"),t.publish(t.FEEDBACK,new h({code:h.CODES.SEARCH_CYCLE_STOP_MONITORING,cycle:this.__searchCycle}));else{if(this.__searchCycle.inprogress&&u.isEmpty(this.__searchCycle.inprogress))return this.__searchCycle.finished?void 0:void t.publish(t.FEEDBACK,new h({code:h.CODES.SEARCH_CYCLE_FINISHED,cycle:this.__searchCycle}));var i=u.keys(this.__searchCycle.waiting).length,s=i+u.keys(this.__searchCycle.done).length+u.keys(this.__searchCycle.inprogress).length+u.keys(this.__searchCycle.failed).length;t.publish(t.FEEDBACK,new h({code:h.CODES.SEARCH_CYCLE_PROGRESS,msg:i/s,total:s,todo:i,cycle:this.__searchCycle})),setTimeout(function(){e.monitorExecution()},e.monitoringDelayInMs)}}},receiveRequests:function(e,t){this.debug&&console.log("[QM]: received request:",this.hasApp()&&this.getApp().getPluginOrWidgetName(t.getId())||t.getId(),e.url()),this.__searchCycle.collectingRequests?this.__searchCycle.waiting[t.getId()]={request:e,key:t}:this.executeRequest(e,t)},executeRequest:function(e,t){if(!(e instanceof a))throw new Error("Sir, I belive you forgot to send me a valid ApiRequest!");if(t)return this._executeRequest(e,t);throw new Error("Request executed, but no widget id provided!")},getFailCacheValue:function(e){var t=this.failedRequestsCache;return t&&t.getSync(e)||t._cache[e]&&t._cache[e].value},_executeRequest:function(e,t){e.get("query")&&e.get("query").get("__qid")&&(o=e.get("query").get("__qid")[0],e.set("target",g.MYADS_STORAGE+"/execute_query/"+o));var i,s,r,n=this.getPubSub(),o=this.getBeeHive().getService("Api"),a=this._getCacheKey(e);return(this.getFailCacheValue(a)||0)>=this.maxRetries?(this.onApiRequestFailure.apply({request:e,key:t,requestKey:a,qm:this},[{status:h.CODES.TOO_MANY_FAILURES},"Error","This request has reached maximum number of failures (wait before retrying)"]),c.Deferred().reject()):this._cache?(i=this._cache.getSync(a),s=this,i&&i.promise?(i.done(function(){s._cache.put(a,arguments),s.onApiResponse.apply({request:e,key:t,requestKey:a,qm:s},arguments)}),i.fail(function(){s._cache.invalidate(a),s.onApiRequestFailure.apply({request:e,pubsub:n,key:t,requestKey:a,qm:s},arguments)}),i):i?((r=c.Deferred()).done(function(){s.onApiResponse.apply({request:e,key:t,requestKey:a,qm:s},i)}),r.resolve(),r.promise()):(r=o.request(e,{done:function(){s._cache.put(a,arguments),s.onApiResponse.apply(this,arguments)},fail:function(){s._cache.invalidate(a),s.onApiRequestFailure.apply(this,arguments)},context:{request:e,key:t,requestKey:a,qm:s}}),this._cache.put(a,r),r)):o.request(e,{done:this.onApiResponse,fail:this.onApiRequestFailure,context:{request:e,key:t,requestKey:a,qm:this}})},onApiResponse:function(e,t,i){var s,r=this.qm;this.request.__STALE||((s=new(e.responseHeader&&e.responseHeader.params?n:l)(e)).setApiQuery(this.request.get("query")),r.debug&&console.log("[QM]: sending response:",r.hasApp()&&r.getApp().getPluginOrWidgetName(this.key.getId())||this.key.getId(),e),(e=r.getBeeHive().getService("PubSub"))&&e.publish(this.key,e.DELIVERING_RESPONSE+this.key.getId(),s),r.failedRequestsCache.getIfPresent(this.requestKey)&&r.failedRequestsCache.invalidate(this.requestKey))},onApiRequestFailure:function(e,t,i){var s,r=this.qm;if(!this.request.__STALE)return this.request.get("query"),r.debug&&console.warn("[QM]: request failed",e,t,i),s=r.getFailCacheValue(this.requestKey)||0,r.failedRequestsCache.put(this.requestKey,s+1),r.tryToRecover.apply(this,arguments)?(console.warn("[QM]: attempting recovery"),!0):(s=new h({code:h.CODES.API_REQUEST_ERROR,msg:t,request:this.request,error:e,psk:this.key,errorThrown:i,text:t}),(e=r.getBeeHive().getService("PubSub"))&&e.publish(this.key,e.FEEDBACK,s),!1)},tryToRecover:function(e,t,i){var s=this.qm,r=this.key,n=this.request,o=this.requestKey,a=e.status;if(a)switch(a){case 408:case 409:case 500:case 502:case 503:case 504:return d("send","event","introspection","retrying",a),setTimeout(function(){if(s._cache){var e=s._cache.getSync(o);if(e&&e.promise)s._cache.invalidate(o);else if(e)return}s._executeRequest.call(s,n,r)},s.recoveryDelayInMs),!0;default:d("send","event","introspection","not-retrying",a,JSON.stringify(s.mostRecentQuery.toJSON()))}},_getCacheKey:function(e){var t=e.get("query"),i=this.queryUpdater.clean(t),i=(e.set("query",i),e.url());return e.set("query",t),i},reset:function(){this.__searchCycle={waiting:{},inprogress:{},done:{},failed:{}},this._cache&&this._cache.invalidateAll()},getAlerter:function(){return this.getApp().getController(this.alertsController||"AlertsController")},displayTugboatMessages:function(){var s,e,r={AUTHOR_ANDED_WARNING:"Author search terms combined with AND rather than OR",ENTRY_DATE_OFFSET_ERROR:"Can not combine a date and offset (negative value) for the Entry Date",ENTRY_DATE_NON_NUMERIC_ERROR:"Found a non numeric value in the Entry Date",UNRECOGNIZABLE_VALUE:"Invalid value for {} supplied"};this.original_url&&(e=[].concat(p.qs("error_message",this.original_url,!1)||[],p.qs("warning_message",this.original_url,!1)||[]),s=p.qs("unprocessed_parameter",this.original_url,!1)||[],0<(e=u.reduce(e,function(e,t){var i;return t=t.toUpperCase(),u.has(r,t)&&(i=r[t],"UNRECOGNIZABLE_VALUE"===t&&0<s.length&&(t=encodeURIComponent(s.pop()),i=i.replace("{}",/^\w+$/.test(t)?t:"parameter")),e.push(i)),e},[])).length)&&(e.push('See our <a style="text-decoration: underline; font-weight: bold" href="/help/faq/#classic-search-translator">docs</a> for more information'),e=e.join("<br/>"),this.getAlerter().alert(new h({type:y.TYPE.INFO,msg:e})))},resetFailures:function(){this.failedRequestsCache.invalidateAll()}});return u.extend(e.prototype,i.BeeHive,i.App),e});
//# sourceMappingURL=query_mediator.js.map