
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"5",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":false},{"function":"__c","vtp_value":"google.com.br"},{"function":"__c","vtp_value":0}],
  "tags":[{"function":"__ogt_auto_events","priority":17,"vtp_enableScroll":true,"vtp_enableOutboundClick":false,"vtp_enableDownload":false,"vtp_enableHistoryEvents":true,"vtp_enableForm":true,"vtp_enableVideo":false,"vtp_enablePageView":true,"tag_id":10},{"function":"__ogt_dma","priority":7,"vtp_delegationMode":"ON","vtp_dmaDefault":"DENIED","tag_id":12},{"function":"__ogt_ip_mark","priority":7,"vtp_instanceOrder":0,"vtp_paramValue":"internal","vtp_ruleResult":["macro",1],"tag_id":14},{"function":"__ogt_1p_data_v2","priority":7,"vtp_isAutoEnabled":true,"vtp_autoPhoneEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_autoAddressEnabled":true,"vtp_autoEmailEnabled":true,"vtp_isAutoCollectPiiEnabledFlag":true,"tag_id":15},{"function":"__ccd_ga_first","priority":6,"vtp_instanceDestinationId":"G-CSLL4ZEK4L","tag_id":22},{"function":"__set_product_settings","priority":5,"vtp_instanceDestinationId":"G-CSLL4ZEK4L","vtp_foreignTldMacroResult":["macro",2],"vtp_isChinaVipRegionMacroResult":["macro",3],"tag_id":21},{"function":"__ccd_ga_regscope","priority":4,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-CSLL4ZEK4L","tag_id":20},{"function":"__ccd_em_page_view","priority":3,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-CSLL4ZEK4L","tag_id":19},{"function":"__ccd_conversion_marking","priority":2,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"],["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"video_complete\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"],["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"telephone_click\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"],["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"file_download\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"],["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"email_click\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"],["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"dap_event\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-CSLL4ZEK4L","tag_id":18},{"function":"__ccd_auto_redact","priority":1,"vtp_redactEmail":true,"vtp_instanceDestinationId":"G-CSLL4ZEK4L","tag_id":17},{"function":"__gct","vtp_trackingId":"G-CSLL4ZEK4L","vtp_sessionDuration":0,"tag_id":7},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-CSLL4ZEK4L","tag_id":16}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init_consent"}],
  "rules":[[["if",0],["add",10]],[["if",1],["add",0,2,3,11,9,8,7,6,5,4]],[["if",2],["add",1]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_auto_redact",[46,"a"],[50,"v",[46,"aF"],[36,[2,[15,"aF"],"replace",[7,[15,"u"],"\\$1"]]]],[50,"w",[46,"aF"],[52,"aG",[30,["c",[15,"aF"]],[15,"aF"]]],[52,"aH",[7]],[65,"aI",[2,[15,"aG"],"split",[7,""]],[46,[53,[52,"aJ",[7,["v",[15,"aI"]]]],[52,"aK",["d",[15,"aI"]]],[22,[12,[15,"aK"],[45]],[46,[53,[36,["d",["v",[15,"aF"]]]]]]],[22,[21,[15,"aK"],[15,"aI"]],[46,[53,[2,[15,"aJ"],"push",[7,[15,"aK"]]],[22,[21,[15,"aI"],[2,[15,"aI"],"toLowerCase",[7]]],[46,[53,[2,[15,"aJ"],"push",[7,["d",[2,[15,"aI"],"toLowerCase",[7]]]]]]],[46,[22,[21,[15,"aI"],[2,[15,"aI"],"toUpperCase",[7]]],[46,[53,[2,[15,"aJ"],"push",[7,["d",[2,[15,"aI"],"toUpperCase",[7]]]]]]]]]]]]],[22,[18,[17,[15,"aJ"],"length"],1],[46,[53,[2,[15,"aH"],"push",[7,[0,[0,"(?:",[2,[15,"aJ"],"join",[7,"|"]]],")"]]]]],[46,[53,[2,[15,"aH"],"push",[7,[16,[15,"aJ"],0]]]]]]]]],[36,[2,[15,"aH"],"join",[7,""]]]],[50,"x",[46,"aF","aG","aH"],[52,"aI",["z",[15,"aF"],[15,"aH"]]],[22,[28,[15,"aI"]],[46,[36,[15,"aF"]]]],[22,[28,[17,[15,"aI"],"search"]],[46,[36,[15,"aF"]]]],[41,"aJ"],[3,"aJ",[17,[15,"aI"],"search"]],[65,"aK",[15,"aG"],[46,[53,[52,"aL",[7,["v",[15,"aK"]],["w",[15,"aK"]]]],[65,"aM",[15,"aL"],[46,[53,[52,"aN",[30,[16,[15,"t"],[15,"aM"]],[43,[15,"t"],[15,"aM"],["b",[0,[0,"([?&]",[15,"aM"]],"=)([^&]*)"],"gi"]]]],[3,"aJ",[2,[15,"aJ"],"replace",[7,[15,"aN"],[0,"$1",[15,"r"]]]]]]]]]]],[22,[20,[15,"aJ"],[17,[15,"aI"],"search"]],[46,[36,[15,"aF"]]]],[22,[20,[16,[15,"aJ"],0],"&"],[46,[3,"aJ",[2,[15,"aJ"],"substring",[7,1]]]]],[22,[21,[16,[15,"aJ"],0],"?"],[46,[3,"aJ",[0,"?",[15,"aJ"]]]]],[22,[20,[15,"aJ"],"?"],[46,[3,"aJ",""]]],[43,[15,"aI"],"search",[15,"aJ"]],[36,["aA",[15,"aI"],[15,"aH"]]]],[50,"z",[46,"aF","aG"],[22,[20,[15,"aG"],[17,[15,"s"],"PATH"]],[46,[53,[3,"aF",[0,[15,"y"],[15,"aF"]]]]]],[36,["f",[15,"aF"]]]],[50,"aA",[46,"aF","aG"],[41,"aH"],[3,"aH",""],[22,[20,[15,"aG"],[17,[15,"s"],"URL"]],[46,[53,[41,"aI"],[3,"aI",""],[22,[30,[17,[15,"aF"],"username"],[17,[15,"aF"],"password"]],[46,[53,[3,"aI",[0,[15,"aI"],[0,[0,[0,[17,[15,"aF"],"username"],[39,[17,[15,"aF"],"password"],":",""]],[17,[15,"aF"],"password"]],"@"]]]]]],[3,"aH",[0,[0,[0,[17,[15,"aF"],"protocol"],"//"],[15,"aI"]],[17,[15,"aF"],"host"]]]]]],[36,[0,[0,[0,[15,"aH"],[17,[15,"aF"],"pathname"]],[17,[15,"aF"],"search"]],[17,[15,"aF"],"hash"]]]],[50,"aB",[46,"aF","aG"],[41,"aH"],[3,"aH",[2,[15,"aF"],"replace",[7,[15,"n"],[15,"r"]]]],[22,[30,[20,[15,"aG"],[17,[15,"s"],"URL"]],[20,[15,"aG"],[17,[15,"s"],"PATH"]]],[46,[53,[52,"aI",["z",[15,"aH"],[15,"aG"]]],[22,[20,[15,"aI"],[44]],[46,[36,[15,"aH"]]]],[52,"aJ",[17,[15,"aI"],"search"]],[52,"aK",[2,[15,"aJ"],"replace",[7,[15,"o"],[15,"r"]]]],[22,[20,[15,"aJ"],[15,"aK"]],[46,[36,[15,"aH"]]]],[43,[15,"aI"],"search",[15,"aK"]],[3,"aH",["aA",[15,"aI"],[15,"aG"]]]]]],[36,[15,"aH"]]],[50,"aC",[46,"aF"],[22,[20,[15,"aF"],[15,"q"]],[46,[53,[36,[17,[15,"s"],"PATH"]]]],[46,[22,[21,[2,[15,"p"],"indexOf",[7,[15,"aF"]]],[27,1]],[46,[53,[36,[17,[15,"s"],"URL"]]]],[46,[53,[36,[17,[15,"s"],"TEXT"]]]]]]]],[50,"aD",[46,"aF","aG"],[41,"aH"],[3,"aH",false],[52,"aI",["e",[15,"aF"]]],[38,[15,"aI"],[46,"string","array","object"],[46,[5,[46,[52,"aJ",["aB",[15,"aF"],[15,"aG"]]],[22,[21,[15,"aF"],[15,"aJ"]],[46,[53,[36,[15,"aJ"]]]]],[4]]],[5,[46,[53,[41,"aK"],[3,"aK",0],[63,[7,"aK"],[23,[15,"aK"],[17,[15,"aF"],"length"]],[33,[15,"aK"],[3,"aK",[0,[15,"aK"],1]]],[46,[53,[52,"aL",["aD",[16,[15,"aF"],[15,"aK"]],[17,[15,"s"],"TEXT"]]],[22,[21,[15,"aL"],[44]],[46,[53,[43,[15,"aF"],[15,"aK"],[15,"aL"]],[3,"aH",true]]]]]]]],[4]]],[5,[46,[54,"aK",[15,"aF"],[46,[53,[52,"aL",["aD",[16,[15,"aF"],[15,"aK"]],[17,[15,"s"],"TEXT"]]],[22,[21,[15,"aL"],[44]],[46,[53,[43,[15,"aF"],[15,"aK"],[15,"aL"]],[3,"aH",true]]]]]]],[4]]]]],[36,[39,[15,"aH"],[15,"aF"],[44]]]],[50,"aE",[46,"aF","aG"],[52,"aH",[30,[2,[15,"aF"],"getMetadata",[7,[17,[15,"h"],"W"]]],[7]]],[22,[20,[2,[15,"aH"],"indexOf",[7,[15,"aG"]]],[27,1]],[46,[53,[2,[15,"aH"],"push",[7,[15,"aG"]]]]]],[2,[15,"aF"],"setMetadata",[7,[17,[15,"h"],"W"],[15,"aH"]]]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","encodeUriComponent"]],[52,"e",["require","getType"]],[52,"f",["require","parseUrl"]],[52,"g",["require","internal.registerCcdCallback"]],[52,"h",[15,"__module_metadataSchema"]],[52,"i",[15,"__module_goldEventUsageId"]],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[52,"k",[17,[15,"a"],"redactEmail"]],[52,"l",[17,[15,"a"],"redactQueryParams"]],[52,"m",[39,[15,"l"],[2,[15,"l"],"split",[7,","]],[7]]],[22,[1,[28,[17,[15,"m"],"length"]],[28,[15,"k"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"n",["b","[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}","gi"]],[52,"o",["b",[0,"([A-Z0-9._-]|%25|%2B)+%40[A-Z0-9.-]","+\\.[A-Z]{2,}"],"gi"]],[52,"p",[7,"page_location","page_referrer","page_path","link_url","video_url","form_destination"]],[52,"q","page_path"],[52,"r","(redacted)"],[52,"s",[8,"TEXT",0,"URL",1,"PATH",2]],[52,"t",[8]],[52,"u",["b","([\\\\^$.|?*+(){}]|\\[|\\[)","g"]],[52,"y","http://."],["g",[15,"j"],[51,"",[7,"aF"],[22,[15,"k"],[46,[53,[52,"aG",[2,[15,"aF"],"getHitKeys",[7]]],[65,"aH",[15,"aG"],[46,[53,[22,[20,[15,"aH"],"_sst_parameters"],[46,[6]]],[52,"aI",[2,[15,"aF"],"getHitData",[7,[15,"aH"]]]],[22,[28,[15,"aI"]],[46,[6]]],[52,"aJ",["aC",[15,"aH"]]],[52,"aK",["aD",[15,"aI"],[15,"aJ"]]],[22,[21,[15,"aK"],[44]],[46,[53,[2,[15,"aF"],"setHitData",[7,[15,"aH"],[15,"aK"]]],["aE",[15,"aF"],[39,[2,[15,"aF"],"getMetadata",[7,[17,[15,"h"],"BE"]]],[17,[15,"i"],"W"],[17,[15,"i"],"O"]]]]]]]]]]]],[22,[17,[15,"m"],"length"],[46,[53,[65,"aG",[15,"p"],[46,[53,[52,"aH",[2,[15,"aF"],"getHitData",[7,[15,"aG"]]]],[22,[28,[15,"aH"]],[46,[6]]],[52,"aI",[39,[20,[15,"aG"],[15,"q"]],[17,[15,"s"],"PATH"],[17,[15,"s"],"URL"]]],[52,"aJ",["x",[15,"aH"],[15,"m"],[15,"aI"]]],[22,[21,[15,"aJ"],[15,"aH"]],[46,[53,[2,[15,"aF"],"setHitData",[7,[15,"aG"],[15,"aJ"]]],["aE",[15,"aF"],[39,[2,[15,"aF"],"getMetadata",[7,[17,[15,"h"],"BE"]]],[17,[15,"i"],"X"],[17,[15,"i"],"P"]]]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","first_visit"],[52,"g","session_start"],[41,"h"],[41,"i"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"j"],[52,"k",[8,"preHit",[15,"j"]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"k"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AM"],true]],[4]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"AQ"]]],[46,[53,[22,[28,[15,"h"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"f"]]],[3,"h",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"h"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AR"],true]],[4]]]]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"BB"]]],[46,[53,[22,[28,[15,"i"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"g"]]],[3,"i",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"i"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"BC"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"q",[46,"r"],[52,"s",[8]],[43,[15,"s"],[17,[15,"f"],"Q"],true],[43,[15,"s"],[17,[15,"f"],"BY"],true],[43,[15,"r"],"eventMetadata",[15,"s"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.setRemoteConfigParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmPageViewActivity"]],[52,"f",[15,"__module_metadataSchema"]],[52,"g","ae_block_history"],[52,"h","page_view"],[52,"i","isRegistered"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"A",[7,[15,"j"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnHistoryChange"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",[8,"interval",1000,"useV2EventName",true]],[52,"p",["l",[15,"o"]]],[22,[28,[15,"p"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["k","gtm.historyChange-v2",[51,"",[7,"r","s"],["s"],[52,"t",[16,[15,"r"],"gtm.oldUrl"]],[22,[20,[16,[15,"r"],"gtm.newUrl"],[15,"t"]],[46,[36]]],[52,"u",[16,[15,"r"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"u"],"pushState"],[21,[15,"u"],"popstate"]],[21,[15,"u"],"replaceState"]],[46,[53,[36]]]],[52,"v",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"v"],"page_location",[16,[15,"r"],"gtm.newUrl"]],[43,[15,"v"],"page_referrer",[15,"t"]]]]],[52,"w",[8,"eventId",[16,[15,"r"],"gtm.uniqueEventId"]]],[22,[21,[17,[15,"a"],"deferPageView"],false],[46,[53,[43,[15,"w"],"deferrable",true]]]],["q",[15,"w"]],["n",["m"],[15,"h"],[15,"v"],[15,"w"]]],[15,"p"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_first",[46,"a"],[50,"d",[46,"e"],[2,[15,"c"],"A",[7,[15,"e"]]]],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_taskPlatformDetection"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"e"],["d",[15,"e"]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"B",[7,[15,"a"]]]],[2,[15,"b"],"A",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"DJ"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"W"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"CF"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"BZ"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"X"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CF"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CG"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_auto_events",[46,"a"],[52,"b",["require","internal.getDestinationIds"]],[52,"c",["require","internal.setProductSettingsParameter"]],[41,"d"],[3,"d",[30,["b"],[7]]],[52,"e",[51,"",[7,"f","g"],[22,[15,"f"],[46,[36]]],[65,"h",[15,"d"],[46,[53,["c",[15,"h"],[15,"g"],true]]]]]],["e",[17,[15,"a"],"enableHistoryEvents"],"ae_block_history"],["e",[17,[15,"a"],"enableScroll"],"ae_block_scroll"],["e",[17,[15,"a"],"enableOutboundClick"],"ae_block_outbound_click"],["e",[17,[15,"a"],"enableForm"],"ae_block_form"],["e",[17,[15,"a"],"enableVideo"],"ae_block_video"],["e",[17,[15,"a"],"enableDownload"],"ae_block_downloads"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_dma",[46,"a"],[52,"b",["require","internal.declareConsentState"]],[52,"c",["require","internal.isDmaRegion"]],[52,"d",["require","internal.setDelegatedConsentType"]],[22,[1,[20,[17,[15,"a"],"delegationMode"],"ON"],["c"]],[46,[53,["d","ad_user_data","ad_storage"]]]],[22,[20,[17,[15,"a"],"dmaDefault"],"GRANTED"],[46,[53,["b",[8,"ad_user_data","granted"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_ip_mark",[46,"a"],[52,"b",["require","internal.appendRemoteConfigParameter"]],[52,"c",["require","internal.getDestinationIds"]],[52,"d",["require","internal.sortRemoteConfigParameters"]],[52,"e",[8,"instance_order",[17,[15,"a"],"instanceOrder"],"traffic_type",[17,[15,"a"],"paramValue"],"rule_result",[17,[15,"a"],"ruleResult"]]],[41,"f"],[3,"f",[30,["c"],[7]]],[65,"g",[15,"f"],[46,[53,["b",[15,"g"],"internal_traffic_results",[15,"e"]],["d",[15,"g"],"internal_traffic_results",[8,"sortKey","instance_order"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_custom_scripts"],[52,"v","allow_direct_google_requests"],[52,"w","allow_enhanced_conversions"],[52,"x","allow_google_signals"],[52,"y","auid"],[52,"z","aw_remarketing_only"],[52,"aA","discount"],[52,"aB","aw_feed_country"],[52,"aC","aw_feed_language"],[52,"aD","items"],[52,"aE","aw_merchant_id"],[52,"aF","aw_basket_type"],[52,"aG","client_id"],[52,"aH","conversion_cookie_prefix"],[52,"aI","conversion_id"],[52,"aJ","conversion_linker"],[52,"aK","conversion_api"],[52,"aL","cookie_deprecation"],[52,"aM","cookie_expires"],[52,"aN","cookie_prefix"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","developer_id"],[52,"aX","shipping"],[52,"aY","engagement_time_msec"],[52,"aZ","estimated_delivery_date"],[52,"bA","event_developer_id_string"],[52,"bB","event"],[52,"bC","event_timeout"],[52,"bD","first_party_collection"],[52,"bE","match_id"],[52,"bF","gdpr_applies"],[52,"bG","google_analysis_params"],[52,"bH","_google_ng"],[52,"bI","gpp_sid"],[52,"bJ","gpp_string"],[52,"bK","gsa_experiment_id"],[52,"bL","gtag_event_feature_usage"],[52,"bM","iframe_state"],[52,"bN","ignore_referrer"],[52,"bO","is_passthrough"],[52,"bP","_lps"],[52,"bQ","language"],[52,"bR","merchant_feed_label"],[52,"bS","merchant_feed_language"],[52,"bT","merchant_id"],[52,"bU","new_customer"],[52,"bV","page_hostname"],[52,"bW","page_path"],[52,"bX","page_referrer"],[52,"bY","page_title"],[52,"bZ","_platinum_request_status"],[52,"cA","quantity"],[52,"cB","restricted_data_processing"],[52,"cC","screen_resolution"],[52,"cD","send_page_view"],[52,"cE","server_container_url"],[52,"cF","session_duration"],[52,"cG","session_engaged_time"],[52,"cH","session_id"],[52,"cI","_shared_user_id"],[52,"cJ","delivery_postal_code"],[52,"cK","topmost_url"],[52,"cL","transaction_id"],[52,"cM","transport_url"],[52,"cN","update"],[52,"cO","_user_agent_architecture"],[52,"cP","_user_agent_bitness"],[52,"cQ","_user_agent_full_version_list"],[52,"cR","_user_agent_mobile"],[52,"cS","_user_agent_model"],[52,"cT","_user_agent_platform"],[52,"cU","_user_agent_platform_version"],[52,"cV","_user_agent_wow64"],[52,"cW","user_data"],[52,"cX","user_data_auto_latency"],[52,"cY","user_data_auto_meta"],[52,"cZ","user_data_auto_multi"],[52,"dA","user_data_auto_selectors"],[52,"dB","user_data_auto_status"],[52,"dC","user_data_mode"],[52,"dD","user_id"],[52,"dE","user_properties"],[52,"dF","us_privacy_string"],[52,"dG","value"],[52,"dH","_fpm_parameters"],[52,"dI","_host_name"],[52,"dJ","_in_page_command"],[52,"dK","non_personalized_ads"],[52,"dL","conversion_label"],[52,"dM","page_location"],[52,"dN","global_developer_id_string"],[52,"dO","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"F",[15,"e"],"H",[15,"f"],"I",[15,"g"],"J",[15,"h"],"K",[15,"i"],"L",[15,"j"],"X",[15,"k"],"AC",[15,"l"],"AD",[15,"m"],"AE",[15,"n"],"AG",[15,"o"],"AH",[15,"p"],"AJ",[15,"q"],"AN",[15,"r"],"AX",[15,"s"],"BE",[15,"t"],"BF",[15,"u"],"BG",[15,"v"],"BI",[15,"w"],"BJ",[15,"x"],"BP",[15,"y"],"BS",[15,"z"],"BT",[15,"aA"],"BU",[15,"aB"],"BV",[15,"aC"],"BW",[15,"aD"],"BX",[15,"aE"],"BY",[15,"aF"],"CG",[15,"aG"],"CL",[15,"aH"],"CM",[15,"aI"],"JT",[15,"dL"],"CN",[15,"aJ"],"CP",[15,"aK"],"CQ",[15,"aL"],"CS",[15,"aM"],"CW",[15,"aN"],"CX",[15,"aO"],"CY",[15,"aP"],"CZ",[15,"aQ"],"DA",[15,"aR"],"DB",[15,"aS"],"DC",[15,"aT"],"DD",[15,"aU"],"DH",[15,"aV"],"DI",[15,"aW"],"DU",[15,"aX"],"DW",[15,"aY"],"EA",[15,"aZ"],"EE",[15,"bA"],"EG",[15,"bB"],"EI",[15,"bC"],"EN",[15,"bD"],"EW",[15,"bE"],"FG",[15,"bF"],"JV",[15,"dN"],"FK",[15,"bG"],"FL",[15,"bH"],"FO",[15,"bI"],"FP",[15,"bJ"],"FR",[15,"bK"],"FS",[15,"bL"],"FU",[15,"bM"],"FV",[15,"bN"],"GA",[15,"bO"],"GB",[15,"bP"],"GC",[15,"bQ"],"GJ",[15,"bR"],"GK",[15,"bS"],"GL",[15,"bT"],"GP",[15,"bU"],"GS",[15,"bV"],"JU",[15,"dM"],"GT",[15,"bW"],"GU",[15,"bX"],"GV",[15,"bY"],"HD",[15,"bZ"],"HF",[15,"cA"],"HJ",[15,"cB"],"HN",[15,"cC"],"HQ",[15,"cD"],"HS",[15,"cE"],"HU",[15,"cF"],"HW",[15,"cG"],"HX",[15,"cH"],"HZ",[15,"cI"],"IA",[15,"cJ"],"JW",[15,"dO"],"IF",[15,"cK"],"II",[15,"cL"],"IJ",[15,"cM"],"IL",[15,"cN"],"IO",[15,"cO"],"IP",[15,"cP"],"IQ",[15,"cQ"],"IR",[15,"cR"],"IS",[15,"cS"],"IT",[15,"cT"],"IU",[15,"cU"],"IV",[15,"cV"],"IW",[15,"cW"],"IX",[15,"cX"],"IY",[15,"cY"],"IZ",[15,"cZ"],"JA",[15,"dA"],"JB",[15,"dB"],"JC",[15,"dC"],"JE",[15,"dD"],"JF",[15,"dE"],"JH",[15,"dF"],"JI",[15,"dG"],"JK",[15,"dH"],"JL",[15,"dI"],"JM",[15,"dJ"],"JQ",[15,"dK"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","allow_ad_personalization"],[52,"e","consent_state"],[52,"f","consent_updated"],[52,"g","conversion_linker_enabled"],[52,"h","cookie_options"],[52,"i","em_event"],[52,"j","event_start_timestamp_ms"],[52,"k","event_usage"],[52,"l","ga4_collection_subdomain"],[52,"m","handle_internally"],[52,"n","hit_type"],[52,"o","hit_type_override"],[52,"p","is_conversion"],[52,"q","is_external_event"],[52,"r","is_first_visit"],[52,"s","is_first_visit_conversion"],[52,"t","is_fpm_encryption"],[52,"u","is_fpm_split"],[52,"v","is_gcp_conversion"],[52,"w","is_google_signals_allowed"],[52,"x","is_server_side_destination"],[52,"y","is_session_start"],[52,"z","is_session_start_conversion"],[52,"aA","is_sgtm_ga_ads_conversion_study_control_group"],[52,"aB","is_sgtm_prehit"],[52,"aC","is_split_conversion"],[52,"aD","is_syn"],[52,"aE","prehit_for_retry"],[52,"aF","redact_ads_data"],[52,"aG","redact_click_ids"],[52,"aH","send_ccm_parallel_ping"],[52,"aI","send_user_data_hit"],[52,"aJ","speculative"],[52,"aK","syn_or_mod"],[52,"aL","transient_ecsid"],[52,"aM","transmission_type"],[52,"aN","user_data"],[52,"aO","user_data_from_automatic"],[52,"aP","user_data_from_automatic_getter"],[52,"aQ","user_data_from_code"],[52,"aR","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"H",[15,"e"],"I",[15,"f"],"J",[15,"g"],"K",[15,"h"],"Q",[15,"i"],"V",[15,"j"],"W",[15,"k"],"AE",[15,"l"],"AG",[15,"m"],"AH",[15,"n"],"AI",[15,"o"],"AM",[15,"p"],"AO",[15,"q"],"AQ",[15,"r"],"AR",[15,"s"],"AT",[15,"t"],"AU",[15,"u"],"AV",[15,"v"],"AW",[15,"w"],"BA",[15,"x"],"BB",[15,"y"],"BC",[15,"z"],"BD",[15,"aA"],"BE",[15,"aB"],"BG",[15,"aC"],"BH",[15,"aD"],"BM",[15,"aE"],"BP",[15,"aF"],"BQ",[15,"aG"],"BS",[15,"aH"],"BW",[15,"aI"],"BY",[15,"aJ"],"CB",[15,"aK"],"CC",[15,"aL"],"CD",[15,"aM"],"CE",[15,"aN"],"CF",[15,"aO"],"CG",[15,"aP"],"CH",[15,"aQ"],"CI",[15,"aR"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",42],[52,"f",43],[52,"g",44],[52,"h",45],[52,"i",46],[52,"j",47],[52,"k",113],[52,"l",129],[52,"m",142],[52,"n",156],[52,"o",168],[52,"p",174],[52,"q",178],[52,"r",212],[52,"s",240],[52,"t",241],[52,"u",243],[52,"v",252],[52,"w",253],[52,"x",254],[36,[8,"ES",[15,"v"],"DC",[15,"o"],"V",[15,"b"],"W",[15,"c"],"X",[15,"d"],"AC",[15,"e"],"AD",[15,"f"],"AE",[15,"g"],"AF",[15,"h"],"AG",[15,"i"],"AH",[15,"j"],"DG",[15,"p"],"DJ",[15,"q"],"EK",[15,"s"],"BN",[15,"k"],"EM",[15,"u"],"BZ",[15,"l"],"EL",[15,"t"],"EU",[15,"x"],"ET",[15,"w"],"CM",[15,"m"],"DV",[15,"r"],"CV",[15,"n"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_goldEventUsageId",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",1],[52,"c",2],[52,"d",5],[52,"e",6],[52,"f",7],[52,"g",8],[52,"h",9],[52,"i",11],[52,"j",15],[52,"k",16],[52,"l",20],[52,"m",21],[52,"n",23],[52,"o",24],[52,"p",27],[36,[8,"O",[15,"j"],"W",[15,"n"],"P",[15,"k"],"X",[15,"o"],"K",[15,"i"],"A",[15,"b"],"T",[15,"l"],"E",[15,"d"],"F",[15,"e"],"B",[15,"c"],"H",[15,"g"],"I",[15,"h"],"G",[15,"f"],"U",[15,"m"],"AA",[15,"p"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_platformDetection",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"f",[46],[68,"l",[53,[22,[28,["e",[17,[15,"b"],"EM"]]],[46,[53,[36]]]],[52,"l",[7]],[22,["g"],[46,[2,[15,"l"],"push",[7,"ac"]]]],[22,["h"],[46,[2,[15,"l"],"push",[7,"sqs"]]]],[22,["i"],[46,[2,[15,"l"],"push",[7,"dud"]]]],[22,["j"],[46,[2,[15,"l"],"push",[7,"woo"]]]],[22,["k"],[46,[2,[15,"l"],"push",[7,"fw"]]]],[22,[18,[17,[15,"l"],"length"],0],[46,[36,[8,"plf",[2,[15,"l"],"join",[7,"."]]]]]]],[46]]],[50,"g",[46],[68,"l",[53,[36,[28,[28,["c","script[data-requiremodule^=\"mage/\"]"]]]]],[46]],[36,false]],[50,"h",[46],[68,"l",[53,[36,[28,[28,["c","script[src^=\"//assets.squarespace.com/\"]"]]]]],[46]],[36,false]],[50,"i",[46],[68,"l",[53,[36,[28,[28,["c","script[id=\"d-js-core\"]"]]]]],[46]],[36,false]],[50,"j",[46],[68,"l",[53,[36,[28,[28,["c",[0,[0,"script[src*=\"woocommerce\"],","link[href*=\"woocommerce\"],"],"[class|=\"woocommerce\"]"]]]]]],[46]],[36,false]],[50,"k",[46],[68,"l",[53,[36,[28,[28,["c",[0,[0,"meta[content*=\"fourthwall\"],","script[src*=\"fourthwall\"],"],"link[href*=\"fourthwall\"]"]]]]]],[46]],[36,false]],[52,"b",[15,"__module_featureFlags"]],[52,"c",["require","internal.getFirstElementByCssSelector"]],[52,"d",[15,"__module_gtagSchema"]],[52,"e",["require","internal.isFeatureEnabled"]],[36,[8,"A",[15,"f"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j"],["c",[15,"j"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"h"]],[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"Q"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[22,[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"BE"]]]],[46,[53,["d",[15,"j"],[17,[15,"f"],"GU"],[2,[15,"k"],"getHitData",[7,[17,[15,"f"],"GU"]]]]]]],[2,[15,"k"],"setMetadata",[7,[17,[15,"e"],"BY"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g","ae_block_history"],[52,"h",[17,[15,"f"],"AH"]],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"j",[46,"m","n","o"],[50,"t",[46,"v"],[52,"w",[16,[15,"i"],[15,"v"]]],[22,[28,[15,"w"]],[46,[36]]],[53,[41,"x"],[3,"x",0],[63,[7,"x"],[23,[15,"x"],[17,[15,"w"],"length"]],[33,[15,"x"],[3,"x",[0,[15,"x"],1]]],[46,[53,[52,"y",[16,[15,"w"],[15,"x"]]],["q",[15,"p"],[17,[15,"y"],"name"],[17,[15,"y"],"value"]]]]]]],[50,"u",[46,"v"],[22,[30,[28,[15,"r"]],[21,[17,[15,"r"],"length"],2]],[46,[53,[36,false]]]],[41,"w"],[3,"w",[16,[15,"v"],[15,"s"]]],[22,[20,[15,"w"],[44]],[46,[53,[3,"w",[16,[15,"v"],[15,"r"]]]]]],[36,[28,[28,[15,"w"]]]]],[22,[28,[15,"n"]],[46,[36]]],[52,"p",[30,[17,[15,"m"],"instanceDestinationId"],[17,["c"],"containerId"]]],[52,"q",["h",[15,"f"],[15,"o"]]],[52,"r",[13,[41,"$0"],[3,"$0",["h",[15,"d"],[15,"o"]]],["$0"]]],[52,"s",[13,[41,"$0"],[3,"$0",["h",[15,"e"],[15,"o"]]],["$0"]]],[53,[41,"v"],[3,"v",0],[63,[7,"v"],[23,[15,"v"],[17,[15,"n"],"length"]],[33,[15,"v"],[3,"v",[0,[15,"v"],1]]],[46,[53,[52,"w",[16,[15,"n"],[15,"v"]]],[22,[30,[17,[15,"w"],"disallowAllRegions"],["u",[17,[15,"w"],"disallowedRegions"]]],[46,[53,["t",[17,[15,"w"],"redactFieldGroup"]]]]]]]]]],[50,"k",[46,"m"],[52,"n",[8]],[22,[28,[15,"m"]],[46,[36,[15,"n"]]]],[52,"o",[2,[15,"m"],"split",[7,","]]],[53,[41,"p"],[3,"p",0],[63,[7,"p"],[23,[15,"p"],[17,[15,"o"],"length"]],[33,[15,"p"],[3,"p",[0,[15,"p"],1]]],[46,[53,[52,"q",[2,[16,[15,"o"],[15,"p"]],"trim",[7]]],[22,[28,[15,"q"]],[46,[6]]],[52,"r",[2,[15,"q"],"split",[7,"-"]]],[52,"s",[16,[15,"r"],0]],[52,"t",[39,[20,[17,[15,"r"],"length"],2],[15,"q"],[44]]],[22,[30,[28,[15,"s"]],[21,[17,[15,"s"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"t"],[44]],[30,[23,[17,[15,"t"],"length"],4],[18,[17,[15,"t"],"length"],6]]],[46,[53,[6]]]],[43,[15,"n"],[15,"q"],true]]]]],[36,[15,"n"]]],[50,"l",[46,"m"],[22,[28,[17,[15,"m"],"settingsTable"]],[46,[36,[7]]]],[52,"n",[8]],[53,[41,"o"],[3,"o",0],[63,[7,"o"],[23,[15,"o"],[17,[17,[15,"m"],"settingsTable"],"length"]],[33,[15,"o"],[3,"o",[0,[15,"o"],1]]],[46,[53,[52,"p",[16,[17,[15,"m"],"settingsTable"],[15,"o"]]],[52,"q",[17,[15,"p"],"redactFieldGroup"]],[22,[28,[16,[15,"i"],[15,"q"]]],[46,[6]]],[43,[15,"n"],[15,"q"],[8,"redactFieldGroup",[15,"q"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"r",[16,[15,"n"],[15,"q"]]],[22,[17,[15,"p"],"disallowAllRegions"],[46,[53,[43,[15,"r"],"disallowAllRegions",true],[6]]]],[43,[15,"r"],"disallowedRegions",["k",[17,[15,"p"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"n"]]]]],[52,"b",["require","Object"]],[52,"c",["require","getContainerVersion"]],[52,"d",["require","internal.getCountryCode"]],[52,"e",["require","internal.getRegionCode"]],[52,"f",["require","internal.setRemoteConfigParameter"]],[52,"g",[15,"__module_activities"]],[52,"h",[17,[15,"g"],"A"]],[52,"i",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"A",[15,"j"],"B",[15,"l"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_taskPlatformDetection",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"d",[46,"e"],[52,"f",[2,[15,"c"],"A",[7]]],[22,[15,"f"],[46,[53,[2,[15,"e"],"mergeHitDataForKey",[7,[17,[15,"b"],"FK"],[15,"f"]]]]]]],[52,"b",[15,"__module_gtagSchema"]],[52,"c",[15,"__module_platformDetection"]],[36,[8,"A",[15,"d"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__ccd_auto_redact":{"2":true,"5":true}
,
"__ccd_conversion_marking":{"2":true,"5":true}
,
"__ccd_em_page_view":{"2":true,"5":true}
,
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__ccd_ga_regscope":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}
,
"__ogt_auto_events":{"2":true,"5":true}
,
"__ogt_dma":{"2":true,"5":true}
,
"__ogt_ip_mark":{"2":true,"5":true}
,
"__set_product_settings":{"2":true,"5":true}


}
,"blob":{"1":"5","10":"G-CSLL4ZEK4L|GT-TWZC7CH","11":true,"14":"59a0","15":"0","16":"MTU4NDc2ODg4MjQ4NjU4NzM5MzY=","17":"","19":"dataLayer","20":"","21":"www.googletagmanager.com","22":"eyIwIjoiQlIiLCIxIjoiQlItU1AiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20uYnIiLCI0IjoiIiwiNSI6ZmFsc2UsIjYiOmZhbHNlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"BR","31":"BR-SP","32":false,"34":"G-CSLL4ZEK4L","35":"G","36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BJlFZo1PLIDIEBtsGEdfkWR4ZCcviP2y+dqfwvtWFNjNYttgOKelwTaMUwDjc4u1vbVnCQhKBR3Ud3oXWze2eV4=\",\"version\":0},\"id\":\"e79a77ad-43af-44a5-bcca-e57a1f93b790\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BDw7DFfOeZAAWAqNIyQfysi0/JA10sR+FPZhR6dMiRh4sDLkXZ+q5KoIer8OYTaPCHAZDjlF4UCz/ecoB262OG0=\",\"version\":0},\"id\":\"90bb4489-4e6e-41bc-9502-3e9d807aa4dd\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BC//NaSUVUe7TL7gUUpav1au+A9txHuJj5FmEkTOnsN0kBHmVr42c4PoznnLpQHkIK6mHi4tfTzCe888FbA2mCE=\",\"version\":0},\"id\":\"63d5d28f-3fce-4587-986e-285cc423dfa1\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BB0vQ526NMz7UA93caIqPgXdDxDAVWQZndpQCFqrbGC9e/6V4njP+/9b14wWL2ZgZ65/wfrndtBmKkenASmKl/0=\",\"version\":0},\"id\":\"4aadf648-f1fb-4a07-bbd3-09468280293c\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BLZSd5uiWWgL76/7QnIswmgZarlkx5CYp0WjRYlmOlij9R1fcj5aGKOXVUsG1faHH5wRBRoWbgbG2ma6T3kkNP0=\",\"version\":0},\"id\":\"468470fe-89e8-44ed-821a-c25902de0bd0\"}]}","44":"101509157~103116026~103200004~103233427~104684208~104684211~105367987~105367989~105426769~105426771","46":{"1":"1000","10":"5940","11":"5840","12":"0.01","14":"1000","16":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","17":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","2":"9","20":"5000","21":"5000","22":"3.2.0","23":"0.0.0","25":"1","26":"4000","27":"100","3":"5","4":"ad_storage|analytics_storage|ad_user_data|ad_personalization","44":"15000","48":"30000","5":"ad_storage|analytics_storage|ad_user_data","6":"1","60":"0","7":"10","8":"20","9":"https://publickeyservice.keys.adm-services.goog/v1alpha/publicKeys:raw"},"5":"G-CSLL4ZEK4L","55":["G-CSLL4ZEK4L"],"6":"131934939","8":"res_ts:1754603574593169,srv_cl:805225108,ds:live,cv:5","9":"G-CSLL4ZEK4L"}
,"permissions":{
"__c":{}
,
"__ccd_auto_redact":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__ccd_ga_first":{"read_dom_elements":{"allowedCssSelectors":"any"}}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__ogt_auto_events":{}
,
"__ogt_dma":{"access_consent":{"consentTypes":[{"consentType":"ad_user_data","read":false,"write":true},{"consentType":"ad_storage","read":true,"write":false}]}}
,
"__ogt_ip_mark":{}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_auto_redact"
,
"__ccd_conversion_marking"
,
"__ccd_em_page_view"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__ogt_auto_events"
,
"__ogt_dma"
,
"__ogt_ip_mark"
,
"__set_product_settings"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=ca(this),ha=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ia={},la={},ma=function(a,b,c){if(!c||a!=null){var d=la[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},na=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ia?g=ia:g=ea;for(var h=0;h<d.length-1;h++){var m=d[h];if(!(m in g))break a;g=g[m]}var n=d[d.length-1],p=ha&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ba(ia,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(la[n]===void 0){var r=
Math.random()*1E9>>>0;la[n]=ha?ea.Symbol(n):"$jscp$"+r+"$"+n}ba(g,la[n],{configurable:!0,writable:!0,value:q})}}};na("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var oa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},pa;if(ha&&typeof Object.setPrototypeOf=="function")pa=Object.setPrototypeOf;else{var sa;a:{var ta={a:!0},va={};try{va.__proto__=ta;sa=va.a;break a}catch(a){}sa=!1}pa=sa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var wa=pa,xa=function(a,b){a.prototype=oa(b.prototype);a.prototype.constructor=a;if(wa)wa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Ar=b.prototype},l=function(a){var b=typeof ia.Symbol!="undefined"&&ia.Symbol.iterator&&a[ia.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ya=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},Aa=function(a){return a instanceof Array?a:ya(l(a))},Ca=function(a){return Ba(a,a)},Ba=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Da=ha&&typeof ma(Object,"assign")=="function"?ma(Object,"assign"):function(a,b){if(a==null)throw new TypeError("No nullish arg");a=Object(a);for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};
na("Object.assign",function(a){return a||Da},"es6");var Ea=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Fa=this||self,Ga=function(a,b){function c(){}c.prototype=b.prototype;a.Ar=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Fs=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ha=function(a,b){this.type=a;this.data=b};var Ia=function(){this.map={};this.C={}};Ia.prototype.get=function(a){return this.map["dust."+a]};Ia.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ia.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ia.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ja=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ia.prototype.ya=function(){return Ja(this,1)};Ia.prototype.sc=function(){return Ja(this,2)};Ia.prototype.ac=function(){return Ja(this,3)};var Ka=function(){};Ka.prototype.reset=function(){};var La=function(a,b){this.R=a;this.parent=b;this.M=this.C=void 0;this.Ab=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ia};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.nh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){if(!a.Ab)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=La.prototype;k.set=function(a,b){this.Ab||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.nb=function(){var a=new La(this.R,this);this.C&&a.Mb(this.C);a.Rc(this.H);a.Nd(this.M);return a};k.Gd=function(){return this.R};k.Mb=function(a){this.C=a};k.Nm=function(){return this.C};k.Rc=function(a){this.H=a};k.ej=function(){return this.H};k.Ra=function(){this.Ab=!0};k.Nd=function(a){this.M=a};k.pb=function(){return this.M};var Na=function(){this.value={};this.prefix="gtm."};Na.prototype.set=function(a,b){this.value[this.prefix+String(a)]=b};Na.prototype.get=function(a){return this.value[this.prefix+String(a)]};Na.prototype.has=function(a){return this.value.hasOwnProperty(this.prefix+String(a))};function Oa(){try{if(Map)return new Map}catch(a){}return new Na};var Pa=function(){this.values=[]};Pa.prototype.add=function(a){this.values.indexOf(a)===-1&&this.values.push(a)};Pa.prototype.has=function(a){return this.values.indexOf(a)>-1};var Qa=function(a,b){this.ia=a;this.parent=b;this.R=this.H=void 0;this.Ab=!1;this.M=function(d,e,f){return d.apply(e,f)};this.C=Oa();var c;a:{try{if(Set){c=new Set;break a}}catch(d){}c=new Pa}this.U=c};Qa.prototype.add=function(a,b){Ra(this,a,b,!1)};Qa.prototype.nh=function(a,b){Ra(this,a,b,!0)};var Ra=function(a,b,c,d){a.Ab||a.U.has(b)||(d&&a.U.add(b),a.C.set(b,c))};k=Qa.prototype;
k.set=function(a,b){this.Ab||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.U.has(a)||this.C.set(a,b))};k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.nb=function(){var a=new Qa(this.ia,this);this.H&&a.Mb(this.H);a.Rc(this.M);a.Nd(this.R);return a};k.Gd=function(){return this.ia};k.Mb=function(a){this.H=a};k.Nm=function(){return this.H};
k.Rc=function(a){this.M=a};k.ej=function(){return this.M};k.Ra=function(){this.Ab=!0};k.Nd=function(a){this.R=a};k.pb=function(){return this.R};var Sa=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.Wm=a;this.Gm=c===void 0?!1:c;this.debugInfo=[];this.C=b};xa(Sa,Error);var Ta=function(a){return a instanceof Sa?a:new Sa(a,void 0,!0)};var Ua=[];function Wa(a){return Ua[a]===void 0?!1:Ua[a]};var Xa=Oa();function Ya(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Za(a,e.value),c instanceof Ha);e=d.next());return c}
function Za(a,b){try{if(Wa(17)){var c=b[0],d=b.slice(1),e=String(c),f=Xa.has(e)?Xa.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Ta(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=l(b),h=g.next().value,m=ya(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Ta(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(Aa(m)))}catch(q){var p=a.Nm();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var ab=function(){this.H=new Ka;this.C=Wa(17)?new Qa(this.H):new La(this.H)};k=ab.prototype;k.Gd=function(){return this.H};k.Mb=function(a){this.C.Mb(a)};k.Rc=function(a){this.C.Rc(a)};k.execute=function(a){return this.Dj([a].concat(Aa(Ea.apply(1,arguments))))};k.Dj=function(){for(var a,b=l(Ea.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Za(this.C,c.value);return a};
k.Yo=function(a){var b=Ea.apply(1,arguments),c=this.C.nb();c.Nd(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Za(c,f.value);return d};k.Ra=function(){this.C.Ra()};var bb=function(){this.Ga=!1;this.da=new Ia};k=bb.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Ga||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Ga||this.da.remove(a)};k.ya=function(){return this.da.ya()};k.sc=function(){return this.da.sc()};k.ac=function(){return this.da.ac()};k.Ra=function(){this.Ga=!0};k.Ab=function(){return this.Ga};function cb(){for(var a=db,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function eb(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var db,fb;function gb(a){db=db||eb();fb=fb||cb();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(db[m],db[n],db[p],db[q])}return b.join("")}
function hb(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=fb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}db=db||eb();fb=fb||cb();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var ib={};function jb(a,b){ib[a]=ib[a]||[];ib[a][b]=!0}function kb(){delete ib.GA4_EVENT}function lb(){ib.GTAG_EVENT_FEATURE_CHANNEL=mb}function nb(a){var b=ib[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return gb(c.join("")).replace(/\.+$/,"")};function ob(){}function pb(a){return typeof a==="function"}function qb(a){return typeof a==="string"}function sb(a){return typeof a==="number"&&!isNaN(a)}function tb(a){return Array.isArray(a)?a:[a]}function ub(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function vb(a,b){if(!sb(a)||!sb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function wb(a,b){for(var c=new xb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function yb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function zb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function Ab(a){return Math.round(Number(a))||0}function Bb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function Cb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function Db(a){return a?a.replace(/^\s+|\s+$/g,""):""}function Eb(){return new Date(Date.now())}function Fb(){return Eb().getTime()}var xb=function(){this.prefix="gtm.";this.values={}};xb.prototype.set=function(a,b){this.values[this.prefix+a]=b};xb.prototype.get=function(a){return this.values[this.prefix+a]};xb.prototype.contains=function(a){return this.get(a)!==void 0};
function Gb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Hb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Ib(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Jb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Kb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Lb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Mb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Nb=/^\w{1,9}$/;function Ob(a,b){a=a||{};b=b||",";var c=[];yb(a,function(d,e){Nb.test(d)&&e&&c.push(d)});return c.join(b)}function Pb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Qb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Rb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Sb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Tb(){var a=w,b;a:{var c=a.crypto||a.msCrypto;if(c&&c.getRandomValues)try{var d=new Uint8Array(25);c.getRandomValues(d);b=btoa(String.fromCharCode.apply(String,Aa(d))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");break a}catch(e){}b=void 0}return b};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ub=globalThis.trustedTypes,Vb;function Wb(){var a=null;if(!Ub)return a;try{var b=function(c){return c};a=Ub.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Yb(){Vb===void 0&&(Vb=Wb());return Vb};var Zb=function(a){this.C=a};Zb.prototype.toString=function(){return this.C+""};function $b(a){var b=a,c=Yb(),d=c?c.createScriptURL(b):b;return new Zb(d)}function ac(a){if(a instanceof Zb)return a.C;throw Error("");};var bc=Ca([""]),dc=Ba(["\x00"],["\\0"]),ec=Ba(["\n"],["\\n"]),fc=Ba(["\x00"],["\\u0000"]);function hc(a){return a.toString().indexOf("`")===-1}hc(function(a){return a(bc)})||hc(function(a){return a(dc)})||hc(function(a){return a(ec)})||hc(function(a){return a(fc)});var ic=function(a){this.C=a};ic.prototype.toString=function(){return this.C};var jc=function(a){this.Mq=a};function kc(a){return new jc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var lc=[kc("data"),kc("http"),kc("https"),kc("mailto"),kc("ftp"),new jc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function mc(a){var b;b=b===void 0?lc:b;if(a instanceof ic)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof jc&&d.Mq(a))return new ic(a)}}var nc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function oc(a){var b;if(a instanceof ic)if(a instanceof ic)b=a.C;else throw Error("");else b=nc.test(a)?a:void 0;return b};function pc(a,b){var c=oc(b);c!==void 0&&(a.action=c)};function qc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var rc=function(a){this.C=a};rc.prototype.toString=function(){return this.C+""};var tc=function(){this.C=sc[0].toLowerCase()};tc.prototype.toString=function(){return this.C};function uc(a,b){var c=[new tc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof tc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var vc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function wc(a){return a===null?"null":a===void 0?"undefined":a};var w=window,xc=window.history,z=document,yc=navigator;function zc(){var a;try{a=yc.serviceWorker}catch(b){return}return a}var Ac=z.currentScript,Bc=Ac&&Ac.src;function Cc(a,b){var c=w,d=c[a];c[a]=d===void 0?b:d;return c[a]}function Dc(a){return(yc.userAgent||"").indexOf(a)!==-1}function Ec(){return Dc("Firefox")||Dc("FxiOS")}function Fc(){return(Dc("GSA")||Dc("GoogleApp"))&&(Dc("iPhone")||Dc("iPad"))}function Gc(){return Dc("Edg/")||Dc("EdgA/")||Dc("EdgiOS/")}
var Hc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Ic={height:1,onload:1,src:1,style:1,width:1};function Jc(a,b,c){b&&yb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Kc(a,b,c,d,e){var f=z.createElement("script");Jc(f,d,Hc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=$b(wc(a));f.src=ac(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Lc(){if(Bc){var a=Bc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Mc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Jc(g,c,Ic);d&&yb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=z.body&&z.body.lastChild||z.body||z.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Nc(a,b,c,d){return Oc(a,b,c,d)}function Pc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Qc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Rc(a){w.setTimeout(a,0)}function Sc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Tc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Uc(a){var b=z.createElement("div"),c=b,d,e=wc("A<div>"+a+"</div>"),f=Yb(),g=f?f.createHTML(e):e;d=new rc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof rc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Vc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Wc(a,b,c){var d;try{d=yc.sendBeacon&&yc.sendBeacon(a)}catch(e){jb("TAGGING",15)}d?b==null||b():Oc(a,b,c)}function Xc(a,b){try{return yc.sendBeacon(a,b)}catch(c){jb("TAGGING",15)}return!1}var Yc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Zc(a,b,c,d,e){if($c()){var f=ma(Object,"assign").call(Object,{},Yc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=w.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Ch)return e==null||e(),
!1;if(b){var h=Xc(a,b);h?d==null||d():e==null||e();return h}ad(a,d,e);return!0}function $c(){return typeof w.fetch==="function"}function dd(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function ed(){var a=w.performance;if(a&&pb(a.now))return a.now()}
function fd(){var a,b=w.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function gd(){return w.performance||void 0}function hd(){var a=w.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Oc=function(a,b,c,d){var e=new Image(1,1);Jc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},ad=Wc;function id(a,b){return this.evaluate(a)&&this.evaluate(b)}function jd(a,b){return this.evaluate(a)===this.evaluate(b)}function kd(a,b){return this.evaluate(a)||this.evaluate(b)}function ld(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function md(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function nd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=w.location.href;d instanceof bb&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var od=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,pd=function(a){if(a==null)return String(a);var b=od.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},qd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},rd=function(a){if(!a||pd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!qd(a,"constructor")&&!qd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
qd(a,b)},sd=function(a,b){var c=b||(pd(a)=="array"?[]:{}),d;for(d in a)if(qd(a,d)){var e=a[d];pd(e)=="array"?(pd(c[d])!="array"&&(c[d]=[]),c[d]=sd(e,c[d])):rd(e)?(rd(c[d])||(c[d]={}),c[d]=sd(e,c[d])):c[d]=e}return c};function td(a){if(a==void 0||Array.isArray(a)||rd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function ud(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var vd=function(a){a=a===void 0?[]:a;this.da=new Ia;this.values=[];this.Ga=!1;for(var b in a)a.hasOwnProperty(b)&&(ud(b)?this.values[Number(b)]=a[Number(b)]:this.da.set(b,a[b]))};k=vd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof vd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ga)if(a==="length"){if(!ud(b))throw Ta(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else ud(a)?this.values[Number(a)]=b:this.da.set(a,b)};k.get=function(a){return a==="length"?this.length():ud(a)?this.values[Number(a)]:this.da.get(a)};k.length=function(){return this.values.length};k.ya=function(){for(var a=this.da.ya(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.sc=function(){for(var a=this.da.sc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.ac=function(){for(var a=this.da.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){ud(a)?delete this.values[Number(a)]:this.Ga||this.da.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,Aa(Ea.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=Ea.apply(2,arguments);return b===void 0&&c.length===0?new vd(this.values.splice(a)):new vd(this.values.splice.apply(this.values,[a,b||0].concat(Aa(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,Aa(Ea.apply(0,arguments)))};k.has=function(a){return ud(a)&&this.values.hasOwnProperty(a)||this.da.has(a)};k.Ra=function(){this.Ga=!0;Object.freeze(this.values)};k.Ab=function(){return this.Ga};
function wd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var xd=function(a,b){this.functionName=a;this.Fd=b;this.da=new Ia;this.Ga=!1};k=xd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new vd(this.ya())};k.invoke=function(a){return this.Fd.call.apply(this.Fd,[new yd(this,a)].concat(Aa(Ea.apply(1,arguments))))};k.apply=function(a,b){return this.Fd.apply(new yd(this,a),b)};k.Kb=function(a){var b=Ea.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(Aa(b)))}catch(c){}};
k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Ga||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Ga||this.da.remove(a)};k.ya=function(){return this.da.ya()};k.sc=function(){return this.da.sc()};k.ac=function(){return this.da.ac()};k.Ra=function(){this.Ga=!0};k.Ab=function(){return this.Ga};var zd=function(a,b){xd.call(this,a,b)};xa(zd,xd);var Ad=function(a,b){xd.call(this,a,b)};xa(Ad,xd);var yd=function(a,b){this.Fd=a;this.J=b};
yd.prototype.evaluate=function(a){var b=this.J;return Array.isArray(a)?Za(b,a):a};yd.prototype.getName=function(){return this.Fd.getName()};yd.prototype.Gd=function(){return this.J.Gd()};var Bd=function(){this.map=new Map};Bd.prototype.set=function(a,b){this.map.set(a,b)};Bd.prototype.get=function(a){return this.map.get(a)};var Cd=function(){this.keys=[];this.values=[]};Cd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};Cd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function Dd(){try{return Map?new Bd:new Cd}catch(a){return new Cd}};var Ed=function(a){if(a instanceof Ed)return a;if(td(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};Ed.prototype.getValue=function(){return this.value};Ed.prototype.toString=function(){return String(this.value)};var Gd=function(a){this.promise=a;this.Ga=!1;this.da=new Ia;this.da.set("then",Fd(this));this.da.set("catch",Fd(this,!0));this.da.set("finally",Fd(this,!1,!0))};k=Gd.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Ga||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Ga||this.da.remove(a)};k.ya=function(){return this.da.ya()};k.sc=function(){return this.da.sc()};k.ac=function(){return this.da.ac()};
var Fd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new zd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof zd||(d=void 0);e instanceof zd||(e=void 0);var f=this.J.nb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new Ed(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Gd(h)})};Gd.prototype.Ra=function(){this.Ga=!0};Gd.prototype.Ab=function(){return this.Ga};function B(a,b,c){var d=Dd(),e=function(g,h){for(var m=g.ya(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof vd){var m=[];d.set(g,m);for(var n=g.ya(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof Gd)return g.promise.then(function(u){return B(u,b,1)},function(u){return Promise.reject(B(u,b,1))});if(g instanceof bb){var q={};d.set(g,q);e(g,q);return q}if(g instanceof zd){var r=function(){for(var u=
[],v=0;v<arguments.length;v++)u[v]=Hd(arguments[v],b,c);var x=new La(b?b.Gd():new Ka);b&&x.Nd(b.pb());return f(Wa(17)?g.apply(x,u):g.invoke.apply(g,[x].concat(Aa(u))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof Ed&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Hd(a,b,c){var d=Dd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||zb(g)){var m=new vd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(rd(g)){var p=new bb;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new zd("",function(){for(var u=Ea.apply(0,arguments),v=[],x=0;x<u.length;x++)v[x]=B(this.evaluate(u[x]),b,c);return f(this.J.ej()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new Ed(g)};return f(a)};var Id={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof vd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new vd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new vd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new vd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
Aa(Ea.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ta(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ta(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ta(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ta(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=wd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new vd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=wd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(Aa(Ea.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,Aa(Ea.apply(1,arguments)))}};var Jd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Kd=new Ha("break"),Ld=new Ha("continue");function Md(a,b){return this.evaluate(a)+this.evaluate(b)}function Nd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Od(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof vd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ta(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=B(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ta(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Jd.hasOwnProperty(e)){var m=2;m=1;var n=B(f,void 0,m);return Hd(d[e].apply(d,n),this.J)}throw Ta(Error("TypeError: "+e+" is not a function"));}if(d instanceof vd){if(d.has(e)){var p=d.get(String(e));if(p instanceof zd){var q=wd(f);return Wa(17)?p.apply(this.J,q):p.invoke.apply(p,[this.J].concat(Aa(q)))}throw Ta(Error("TypeError: "+e+" is not a function"));
}if(Id.supportedMethods.indexOf(e)>=0){var r=wd(f);return Id[e].call.apply(Id[e],[d,this.J].concat(Aa(r)))}}if(d instanceof zd||d instanceof bb||d instanceof Gd){if(d.has(e)){var t=d.get(e);if(t instanceof zd){var u=wd(f);return Wa(17)?t.apply(this.J,u):t.invoke.apply(t,[this.J].concat(Aa(u)))}throw Ta(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof zd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof Ed&&e==="toString")return d.toString();
throw Ta(Error("TypeError: Object has no '"+e+"' property."));}function Pd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.J;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Qd(){var a=Ea.apply(0,arguments),b=this.J.nb(),c=Ya(b,a);if(c instanceof Ha)return c}function Rd(){return Kd}
function Sd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ha)return d}}function Td(){for(var a=this.J,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.nh(c,d)}}}function Ud(){return Ld}function Vd(a,b){return new Ha(a,this.evaluate(b))}
function Wd(a,b){var c=Ea.apply(2,arguments),d;d=new vd;for(var e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(Aa(c));this.J.add(a,this.evaluate(g))}function Xd(a,b){return this.evaluate(a)/this.evaluate(b)}function Yd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof Ed,f=d instanceof Ed;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Zd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}
function $d(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ya(f,d);if(g instanceof Ha){if(g.type==="break")break;if(g.type==="return")return g}}}function ae(a,b,c){if(typeof b==="string")return $d(a,function(){return b.length},function(f){return f},c);if(b instanceof bb||b instanceof Gd||b instanceof vd||b instanceof zd){var d=b.ya(),e=d.length;return $d(a,function(){return e},function(f){return d[f]},c)}}
function be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ae(function(h){g.set(d,h);return g},e,f)}function ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ae(function(h){var m=g.nb();m.nh(d,h);return m},e,f)}function de(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ae(function(h){var m=g.nb();m.add(d,h);return m},e,f)}
function ee(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return fe(function(h){g.set(d,h);return g},e,f)}function ge(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return fe(function(h){var m=g.nb();m.nh(d,h);return m},e,f)}function he(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return fe(function(h){var m=g.nb();m.add(d,h);return m},e,f)}
function fe(a,b,c){if(typeof b==="string")return $d(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof vd)return $d(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ta(Error("The value is not iterable."));}
function ie(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof vd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.J,h=this.evaluate(d),m=g.nb();for(e(g,m);Za(m,b);){var n=Ya(m,h);if(n instanceof Ha){if(n.type==="break")break;if(n.type==="return")return n}var p=g.nb();e(m,p);Za(p,c);m=p}}
function je(a,b){var c=Ea.apply(2,arguments),d=this.J,e=this.evaluate(b);if(!(e instanceof vd))throw Error("Error: non-List value given for Fn argument names.");return new zd(a,function(){return function(){var f=Ea.apply(0,arguments),g=d.nb();g.pb()===void 0&&g.Nd(this.J.pb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new vd(h));var r=Ya(g,c);if(r instanceof Ha)return r.type===
"return"?r.data:r}}())}function ke(a){var b=this.evaluate(a),c=this.J;if(le&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function me(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ta(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof bb||d instanceof Gd||d instanceof vd||d instanceof zd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:ud(e)&&(c=d[e]);else if(d instanceof Ed)return;return c}function ne(a,b){return this.evaluate(a)>this.evaluate(b)}function oe(a,b){return this.evaluate(a)>=this.evaluate(b)}
function pe(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof Ed&&(c=c.getValue());d instanceof Ed&&(d=d.getValue());return c===d}function qe(a,b){return!pe.call(this,a,b)}function re(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ya(this.J,d);if(e instanceof Ha)return e}var le=!1;
function se(a,b){return this.evaluate(a)<this.evaluate(b)}function te(a,b){return this.evaluate(a)<=this.evaluate(b)}function ue(){for(var a=new vd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function ve(){for(var a=new bb,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function we(a,b){return this.evaluate(a)%this.evaluate(b)}
function xe(a,b){return this.evaluate(a)*this.evaluate(b)}function ye(a){return-this.evaluate(a)}function ze(a){return!this.evaluate(a)}function Ae(a,b){return!Yd.call(this,a,b)}function Be(){return null}function Ce(a,b){return this.evaluate(a)||this.evaluate(b)}function De(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Ee(a){return this.evaluate(a)}function Fe(){return Ea.apply(0,arguments)}function Ge(a){return new Ha("return",this.evaluate(a))}
function He(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ta(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof zd||d instanceof vd||d instanceof bb)&&d.set(String(e),f);return f}function Ie(a,b){return this.evaluate(a)-this.evaluate(b)}
function Je(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ha){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ha&&(g.type==="return"||g.type==="continue")))return g}
function Ke(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Le(a){var b=this.evaluate(a);return b instanceof zd?"function":typeof b}function Me(){for(var a=this.J,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Ne(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ya(this.J,e);if(f instanceof Ha){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ya(this.J,e);if(g instanceof Ha){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Oe(a){return~Number(this.evaluate(a))}function Pe(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Qe(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Re(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Se(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Te(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ue(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Ve(){}
function We(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ha)return d}catch(h){if(!(h instanceof Sa&&h.Gm))throw h;var e=this.J.nb();a!==""&&(h instanceof Sa&&(h=h.Wm),e.add(a,new Ed(h)));var f=this.evaluate(c),g=Ya(e,f);if(g instanceof Ha)return g}}function Xe(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Sa&&f.Gm))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ha)return e;if(c)throw c;if(d instanceof Ha)return d};var Ze=function(){this.C=new ab;Ye(this)};Ze.prototype.execute=function(a){return this.C.Dj(a)};var Ye=function(a){var b=function(c,d){var e=new Ad(String(c),d);e.Ra();var f=String(c);a.C.C.set(f,e);Xa.set(f,e)};b("map",ve);b("and",id);b("contains",ld);b("equals",jd);b("or",kd);b("startsWith",md);b("variable",nd)};Ze.prototype.Mb=function(a){this.C.Mb(a)};var af=function(){this.H=!1;this.C=new ab;$e(this);this.H=!0};af.prototype.execute=function(a){return bf(this.C.Dj(a))};var cf=function(a,b,c){return bf(a.C.Yo(b,c))};af.prototype.Ra=function(){this.C.Ra()};
var $e=function(a){var b=function(c,d){var e=String(c),f=new Ad(e,d);f.Ra();a.C.C.set(e,f);Xa.set(e,f)};b(0,Md);b(1,Nd);b(2,Od);b(3,Pd);b(56,Se);b(57,Pe);b(58,Oe);b(59,Ue);b(60,Qe);b(61,Re);b(62,Te);b(53,Qd);b(4,Rd);b(5,Sd);b(68,We);b(52,Td);b(6,Ud);b(49,Vd);b(7,ue);b(8,ve);b(9,Sd);b(50,Wd);b(10,Xd);b(12,Yd);b(13,Zd);b(67,Xe);b(51,je);b(47,be);b(54,ce);b(55,de);b(63,ie);b(64,ee);b(65,ge);b(66,he);b(15,ke);b(16,me);b(17,me);b(18,ne);b(19,oe);b(20,pe);b(21,qe);b(22,re);b(23,se);b(24,te);b(25,we);b(26,
xe);b(27,ye);b(28,ze);b(29,Ae);b(45,Be);b(30,Ce);b(32,De);b(33,De);b(34,Ee);b(35,Ee);b(46,Fe);b(36,Ge);b(43,He);b(37,Ie);b(38,Je);b(39,Ke);b(40,Le);b(44,Ve);b(41,Me);b(42,Ne)};af.prototype.Gd=function(){return this.C.Gd()};af.prototype.Mb=function(a){this.C.Mb(a)};af.prototype.Rc=function(a){this.C.Rc(a)};
function bf(a){if(a instanceof Ha||a instanceof zd||a instanceof vd||a instanceof bb||a instanceof Gd||a instanceof Ed||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var df=function(a){this.message=a};function ef(a){a.Ls=!0;return a};var ff=ef(function(a){return typeof a==="string"});function gf(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new df("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function hf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var jf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function kf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+gf(e)+c}a<<=2;d||(a|=32);return c=""+gf(a|b)+c}
function lf(a,b){var c;var d=a.Fh,e=a.Qm;d===void 0?c="":(e||(e=0),c=""+kf(1,1)+gf(d<<2|e));var f=a.Fp,g=a.Lp,h="4"+c+(f?""+kf(2,1)+gf(f):"")+(g?""+kf(12,1)+gf(g):""),m,n=a.kn;m=n&&jf.test(n)?""+kf(3,2)+n:"";var p,q=a.gn;p=q?""+kf(4,1)+gf(q):"";var r;var t=a.ctid;if(t&&b){var u=kf(5,3),v=t.split("-"),x=v[0].toUpperCase();if(x!=="GTM"&&x!=="OPT")r="";else{var y=v[1];r=""+u+gf(1+y.length)+(a.Nq||0)+y}}else r="";var A=a.yr,D=a.canonicalId,E=a.Na,L=a.Ps,F=h+m+p+r+(A?""+kf(6,1)+gf(A):"")+(D?""+kf(7,3)+
gf(D.length)+D:"")+(E?""+kf(8,3)+gf(E.length)+E:"")+(L?""+kf(9,3)+gf(L.length)+L:""),M;var U=a.Mp;U=U===void 0?{}:U;for(var fa=[],S=l(Object.keys(U)),Z=S.next();!Z.done;Z=S.next()){var ra=Z.value;fa[Number(ra)]=U[ra]}if(fa.length){var ka=kf(10,3),da;if(fa.length===0)da=gf(0);else{for(var Y=[],ja=0,za=!1,ua=0;ua<fa.length;ua++){za=!0;var Va=ua%6;fa[ua]&&(ja|=1<<Va);Va===5&&(Y.push(gf(ja)),ja=0,za=!1)}za&&Y.push(gf(ja));da=Y.join("")}var $a=da;M=""+ka+gf($a.length)+$a}else M="";var cc=a.Tq,Xb=a.lr,
rb=a.zr;return F+M+(cc?""+kf(11,3)+gf(cc.length)+cc:"")+(Xb?""+kf(13,3)+gf(Xb.length)+Xb:"")+(rb?""+kf(14,1)+gf(rb):"")};var mf=function(){function a(b){return{toString:function(){return b}}}return{Dn:a("consent"),Zj:a("convert_case_to"),bk:a("convert_false_to"),dk:a("convert_null_to"),ek:a("convert_true_to"),fk:a("convert_undefined_to"),Mr:a("debug_mode_metadata"),Pa:a("function"),Zg:a("instance_name"),cp:a("live_only"),ep:a("malware_disabled"),METADATA:a("metadata"),jp:a("original_activity_id"),ns:a("original_vendor_template_id"),ls:a("once_on_load"),hp:a("once_per_event"),am:a("once_per_load"),rs:a("priority_override"),
ws:a("respected_consent_types"),jm:a("setup_tags"),mh:a("tag_id"),xm:a("teardown_tags")}}();var If;var Jf=[],Kf=[],Lf=[],Mf=[],Nf=[],Of,Pf,Qf;function Rf(a){Qf=Qf||a}
function Sf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Jf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Mf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Lf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Tf(p[r])}Kf.push(p)}}
function Tf(a){}var Uf,Vf=[],Wf=[];function Xf(a,b){var c={};c[mf.Pa]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Yf(a,b,c){try{return Pf(Zf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Zf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=$f(a[e],b,c));return d},$f=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push($f(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Jf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[mf.Zg]);try{var m=Zf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=ag(m,{event:b,index:f,type:2,
name:h});Uf&&(d=Uf.Np(d,m))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[$f(a[n],b,c)]=$f(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=$f(a[q],b,c);Qf&&(p=p||Qf.Jq(r));d.push(r)}return Qf&&p?Qf.Sp(d):d.join("");case "escape":d=$f(a[1],b,c);if(Qf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Qf.Kq(a))return Qf.Zq(d);d=String(d);for(var t=2;t<a.length;t++)tf[a[t]]&&(d=tf[a[t]](d));return d;
case "tag":var u=a[1];if(!Mf[u])throw Error("Unable to resolve tag reference "+u+".");return{Km:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[mf.Pa]=a[1];var x=Yf(v,b,c),y=!!a[4];return y||x!==2?y!==(x===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},ag=function(a,b){var c=a[mf.Pa],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Of[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Vf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Kb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Jf[q];break;case 1:r=Mf[q];break;default:n="";break a}var t=r&&r[mf.Zg];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,x;if(f&&Wf.indexOf(c)===-1){Wf.push(c);
var y=Fb();u=e(g);var A=Fb()-y,D=Fb();v=If(c,h,b);x=A-(Fb()-D)}else if(e&&(u=e(g)),!e||f)v=If(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),td(u)?(Array.isArray(u)?Array.isArray(v):rd(u)?rd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),x!==void 0&&d.reportMacroDiscrepancy(d.id,c,x));return e?u:v};var bg=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};xa(bg,Error);bg.prototype.getMessage=function(){return this.message};function cg(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)cg(a[c],b[c])}};function dg(){return function(a,b){var c;var d=eg;a instanceof Sa?(a.C=d,c=a):c=new Sa(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function eg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)sb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function fg(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=gg(a),f=0;f<Kf.length;f++){var g=Kf[f],h=hg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Mf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function hg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function gg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Yf(Lf[c],a));return b[c]}};function ig(a,b){b[mf.Zj]&&typeof a==="string"&&(a=b[mf.Zj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(mf.dk)&&a===null&&(a=b[mf.dk]);b.hasOwnProperty(mf.fk)&&a===void 0&&(a=b[mf.fk]);b.hasOwnProperty(mf.ek)&&a===!0&&(a=b[mf.ek]);b.hasOwnProperty(mf.bk)&&a===!1&&(a=b[mf.bk]);return a};var jg=function(){this.C={}},lg=function(a,b){var c=kg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,Aa(Ea.apply(0,arguments)))})};function mg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new bg(c,d,g);}}
function ng(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(Aa(Ea.apply(1,arguments))));mg(e,b,d,g);mg(f,b,d,g)}}}};var qg=function(a,b){var c=this;this.H={};this.C=new jg;var d={},e={},f=ng(this.C,a,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(Aa(Ea.apply(1,arguments)))):{}});yb(b,function(g,h){function m(p){var q=Ea.apply(1,arguments);if(!n[p])throw og(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(Aa(q)))}var n={};yb(h,function(p,q){var r=pg(p,q);n[p]=r.assert;d[p]||(d[p]=r.V);r.Em&&!e[p]&&(e[p]=r.Em)});c.H[g]=function(p,q){var r=n[p];if(!r)throw og(p,
{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(Aa(t.slice(1))))}})},rg=function(a){return kg.H[a]||function(){}};function pg(a,b){var c=Xf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=og;try{return ag(c)}catch(d){return{assert:function(e){throw new bg(e,{},"Permission "+e+" is unknown.");},V:function(){throw new bg(a,{},"Permission "+a+" is unknown.");}}}}
function og(a,b,c){return new bg(a,b,c)};var sg=!1;var tg={};tg.qn=Bb('');tg.Zp=Bb('');
var xg=function(a){var b={},c=0;yb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(ug.hasOwnProperty(e))b[ug[e]]=g;else if(vg.hasOwnProperty(e)){var h=vg[e];b.hasOwnProperty(h)||(b[h]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=wg[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var t=String.fromCharCode(c<10?48+c:65+c-10);b["k"+t]=(""+String(e)).replace(/~/g,"~~");b["v"+t]=g;c++}}});var d=[];yb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
ug={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},vg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},wg=["ca",
"c2","c3","c4","c5"];var yg=[];function zg(a){switch(a){case 1:return 0;case 216:return 16;case 235:return 18;case 38:return 13;case 256:return 11;case 257:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 17;case 75:return 3;case 103:return 14;case 197:return 15;case 109:return 19;case 116:return 4;case 135:return 8;case 136:return 5;case 261:return 20}}function Ag(a,b){yg[a]=b;var c=zg(a);c!==void 0&&(Ua[c]=b)}function C(a){Ag(a,!0)}C(39);
C(145);C(153);C(144);C(120);C(5);C(111);C(139);
C(87);C(92);C(159);
C(132);C(20);C(72);
C(113);C(154);C(116);Ag(23,!1),C(24);C(29);Bg(26,25);
C(37);C(9);C(91);
C(123);C(158);C(71);C(136);C(127);C(27);C(69);C(135);C(95);C(38);
C(103);C(112);
C(101);
C(122);C(121);
C(21);C(134);
C(22);

C(141);
C(90);C(59);
C(175);C(177);
C(185);
C(197);C(200);
C(206);C(231);C(232);C(236);
C(244);C(241);
function G(a){return!!yg[a]}
function Bg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?C(b):C(a)};
var Cg=function(){this.events=[];this.C="";this.oa={};this.baseUrl="";this.M=0;this.R=this.H=!1;this.endpoint=0;G(89)&&(this.R=!0)};Cg.prototype.add=function(a){return this.U(a)?(this.events.push(a),this.C=a.H,this.oa=a.oa,this.baseUrl=a.baseUrl,this.M+=a.R,this.H=a.M,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.ia=a.eventId,this.la=a.priorityId,!0):!1};Cg.prototype.U=function(a){return this.events.length?this.events.length>=20||a.R+this.M>=16384?!1:this.baseUrl===a.baseUrl&&this.H===
a.M&&this.Ja(a):!0};Cg.prototype.Ja=function(a){var b=this;if(!this.R)return this.C===a.H;var c=Object.keys(this.oa);return c.length===Object.keys(a.oa).length&&c.every(function(d){return a.oa.hasOwnProperty(d)&&String(b.oa[d])===String(a.oa[d])})};var Dg={P:{Mn:1,Pn:2,ym:3,dm:4,lk:5,mk:6,To:7,Qn:8,So:9,Ln:10,Kn:11,qm:12,km:13,Tj:14,xn:15,zn:16,Yl:17,nk:18,Xl:19,Nn:20,fp:21,Cn:22,yn:23,An:24,kk:25,Rj:26,pp:27,Gl:28,Pl:29,Ol:30,Nl:31,Jl:32,Hl:33,Il:34}};Dg.P[Dg.P.Mn]="CREATE_EVENT_SOURCE";Dg.P[Dg.P.Pn]="EDIT_EVENT";Dg.P[Dg.P.ym]="TRAFFIC_TYPE";Dg.P[Dg.P.dm]="REFERRAL_EXCLUSION";Dg.P[Dg.P.lk]="ECOMMERCE_FROM_GTM_TAG";Dg.P[Dg.P.mk]="ECOMMERCE_FROM_GTM_UA_SCHEMA";Dg.P[Dg.P.To]="GA_SEND";Dg.P[Dg.P.Qn]="EM_FORM";Dg.P[Dg.P.So]="GA_GAM_LINK";
Dg.P[Dg.P.Ln]="CREATE_EVENT_AUTO_PAGE_PATH";Dg.P[Dg.P.Kn]="CREATED_EVENT";Dg.P[Dg.P.qm]="SIDELOADED";Dg.P[Dg.P.km]="SGTM_LEGACY_CONFIGURATION";Dg.P[Dg.P.Tj]="CCD_EM_EVENT";Dg.P[Dg.P.xn]="AUTO_REDACT_EMAIL";Dg.P[Dg.P.zn]="AUTO_REDACT_QUERY_PARAM";Dg.P[Dg.P.Yl]="MULTIPLE_PAGEVIEW_FROM_CONFIG";Dg.P[Dg.P.nk]="EM_EVENT_SENT_BEFORE_CONFIG";Dg.P[Dg.P.Xl]="LOADED_VIA_CST_OR_SIDELOADING";Dg.P[Dg.P.Nn]="DECODED_PARAM_MATCH";Dg.P[Dg.P.fp]="NON_DECODED_PARAM_MATCH";Dg.P[Dg.P.Cn]="CCD_EVENT_SGTM";
Dg.P[Dg.P.yn]="AUTO_REDACT_EMAIL_SGTM";Dg.P[Dg.P.An]="AUTO_REDACT_QUERY_PARAM_SGTM";Dg.P[Dg.P.kk]="DAILY_LIMIT_REACHED";Dg.P[Dg.P.Rj]="BURST_LIMIT_REACHED";Dg.P[Dg.P.pp]="SHARED_USER_ID_SET_AFTER_REQUEST";Dg.P[Dg.P.Gl]="GA4_MULTIPLE_SESSION_COOKIES";Dg.P[Dg.P.Pl]="INVALID_GA4_SESSION_COUNT";Dg.P[Dg.P.Ol]="INVALID_GA4_LAST_EVENT_TIMESTAMP";Dg.P[Dg.P.Nl]="INVALID_GA4_JOIN_TIMER";Dg.P[Dg.P.Jl]="GA4_STALE_SESSION_COOKIE_SELECTED";Dg.P[Dg.P.Hl]="GA4_SESSION_COOKIE_GS1_READ";Dg.P[Dg.P.Il]="GA4_SESSION_COOKIE_GS2_READ";var Eg={},Fg=(Eg.uaa=!0,Eg.uab=!0,Eg.uafvl=!0,Eg.uamb=!0,Eg.uam=!0,Eg.uap=!0,Eg.uapv=!0,Eg.uaw=!0,Eg);
var Ig=function(a,b){var c=a.events;if(c.length===1)return Gg(c[0],b);var d=[];a.C&&d.push(a.C);for(var e={},f=0;f<c.length;f++)yb(c[f].Od,function(t,u){u!=null&&(e[t]=e[t]||{},e[t][String(u)]=e[t][String(u)]+1||1)});var g={};yb(e,function(t,u){var v,x=-1,y=0;yb(u,function(A,D){y+=D;var E=(A.length+t.length+2)*(D-1);E>x&&(v=A,x=E)});y===c.length&&(g[t]=v)});Hg(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),m=[],n={},p=0;p<c.length;n={vj:void 0},p++){var q=[];n.vj={};yb(c[p].Od,function(t){return function(u,
v){g[u]!==""+v&&(t.vj[u]=v)}}(n));c[p].C&&q.push(c[p].C);Hg(n.vj,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:h,body:r}},Gg=function(a,b){var c=[];a.H&&c.push(a.H);b&&c.push("_s="+b);Hg(a.Od,c);var d=!1;a.C&&(c.push(a.C),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},Hg=function(a,b){yb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var Jg=function(a){var b=[];yb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},Kg=function(a,b,c,d,e,f,g,h){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=h;this.oa=a.oa;this.Od=a.Od;this.cj=a.cj;this.M=d;this.H=Jg(a.oa);this.C=Jg(a.cj);this.R=this.C.length;if(e&&this.R>16384)throw Error("EVENT_TOO_LARGE");};
var Ng=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Lg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Mg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Kb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Mg=/^[a-z$_][\w-$]*$/i,Lg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Og=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Pg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Qg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Rg=new xb;function Sg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Rg.get(e);f||(f=new RegExp(b,d),Rg.set(e,f));return f.test(a)}catch(g){return!1}}function Tg(a,b){return String(a).indexOf(String(b))>=0}
function Ug(a,b){return String(a)===String(b)}function Vg(a,b){return Number(a)>=Number(b)}function Wg(a,b){return Number(a)<=Number(b)}function Xg(a,b){return Number(a)>Number(b)}function Yg(a,b){return Number(a)<Number(b)}function Zg(a,b){return Kb(String(a),String(b))};var fh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,gh={Fn:"function",PixieMap:"Object",List:"Array"};
function hh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=fh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof zd?n="Fn":m instanceof vd?n="List":m instanceof bb?n="PixieMap":m instanceof Gd?n="PixiePromise":m instanceof Ed&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((gh[n]||n)+", which does not match required type ")+
((gh[h]||h)+"."));}}}function H(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof zd?d.push("function"):g instanceof vd?d.push("Array"):g instanceof bb?d.push("Object"):g instanceof Gd?d.push("Promise"):g instanceof Ed?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function ih(a){return a instanceof bb}function jh(a){return ih(a)||a===null||kh(a)}
function lh(a){return a instanceof zd}function mh(a){return lh(a)||a===null||kh(a)}function nh(a){return a instanceof vd}function oh(a){return a instanceof Ed}function I(a){return typeof a==="string"}function ph(a){return I(a)||a===null||kh(a)}function qh(a){return typeof a==="boolean"}function rh(a){return qh(a)||kh(a)}function sh(a){return qh(a)||a===null||kh(a)}function th(a){return typeof a==="number"}function kh(a){return a===void 0};function uh(a){return""+a}
function vh(a,b){var c=[];return c};function wh(a,b){var c=new zd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ta(g);}});c.Ra();return c}
function xh(a,b){var c=new bb,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];pb(e)?c.set(d,wh(a+"_"+d,e)):rd(e)?c.set(d,xh(a+"_"+d,e)):(sb(e)||qb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ra();return c};function yh(a,b){if(!I(a))throw H(this.getName(),["string"],arguments);if(!ph(b))throw H(this.getName(),["string","undefined"],arguments);var c={},d=new bb;return d=xh("AssertApiSubject",
c)};function zh(a,b){if(!ph(b))throw H(this.getName(),["string","undefined"],arguments);if(a instanceof Gd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new bb;return d=xh("AssertThatSubject",c)};function Ah(a){return function(){for(var b=Ea.apply(0,arguments),c=[],d=this.J,e=0;e<b.length;++e)c.push(B(b[e],d));return Hd(a.apply(null,c))}}function Bh(){for(var a=Math,b=Ch,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=Ah(a[e].bind(a)))}return c};function Dh(a){return a!=null&&Kb(a,"__cvt_")};function Eh(a){var b;return b};function Fh(a){var b;if(!I(a))throw H(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Gh(a){try{return encodeURI(a)}catch(b){}};function Hh(a){try{return encodeURIComponent(String(a))}catch(b){}};
var Ih=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},Jh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:Ih(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:Ih(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Lh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=Jh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Kh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Kh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Lh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Sg(d(c[0]),d(c[1]),!1);case 5:return Ug(d(c[0]),d(c[1]));case 6:return Zg(d(c[0]),d(c[1]));case 7:return Pg(d(c[0]),d(c[1]));case 8:return Tg(d(c[0]),d(c[1]));case 9:return Yg(d(c[0]),d(c[1]));case 10:return Wg(d(c[0]),d(c[1]));case 11:return Xg(d(c[0]),d(c[1]));case 12:return Vg(d(c[0]),d(c[1]));case 13:return Qg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Mh(a){if(!ph(a))throw H(this.getName(),["string|undefined"],arguments);};function Nh(a,b){if(!th(a)||!th(b))throw H(this.getName(),["number","number"],arguments);return vb(a,b)};function Oh(){return(new Date).getTime()};function Ph(a){if(a===null)return"null";if(a instanceof vd)return"array";if(a instanceof zd)return"function";if(a instanceof Ed){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Qh(a){function b(c){return function(d){try{return c(d)}catch(e){(sg||tg.qn)&&a.call(this,e.message)}}}return{parse:b(function(c){return Hd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(B(c))}),publicName:"JSON"}};function Sh(a){return Ab(B(a,this.J))};function Th(a){return Number(B(a,this.J))};function Uh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Vh(a,b,c){var d=null,e=!1;return e?d:null};var Ch="floor ceil round max min abs pow sqrt".split(" ");function Wh(){var a={};return{lq:function(b){return a.hasOwnProperty(b)?a[b]:void 0},nn:function(b,c){a[b]=c},reset:function(){a={}}}}function Xh(a,b){return function(){return zd.prototype.invoke.apply(a,[b].concat(Aa(Ea.apply(0,arguments))))}}
function Yh(a,b){if(!I(a))throw H(this.getName(),["string","any"],arguments);}
function Zh(a,b){if(!I(a)||!ih(b))throw H(this.getName(),["string","PixieMap"],arguments);};var $h={};var ai=function(a){var b=new bb;if(a instanceof vd)for(var c=a.ya(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof zd)for(var f=a.ya(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
$h.keys=function(a){hh(this.getName(),arguments);if(a instanceof vd||a instanceof zd||typeof a==="string")a=ai(a);if(a instanceof bb||a instanceof Gd)return new vd(a.ya());return new vd};
$h.values=function(a){hh(this.getName(),arguments);if(a instanceof vd||a instanceof zd||typeof a==="string")a=ai(a);if(a instanceof bb||a instanceof Gd)return new vd(a.sc());return new vd};
$h.entries=function(a){hh(this.getName(),arguments);if(a instanceof vd||a instanceof zd||typeof a==="string")a=ai(a);if(a instanceof bb||a instanceof Gd)return new vd(a.ac().map(function(b){return new vd(b)}));return new vd};
$h.freeze=function(a){(a instanceof bb||a instanceof Gd||a instanceof vd||a instanceof zd)&&a.Ra();return a};$h.delete=function(a,b){if(a instanceof bb&&!a.Ab())return a.remove(b),!0;return!1};function J(a,b){var c=Ea.apply(2,arguments),d=a.J.pb();if(!d)throw Error("Missing program state.");if(d.jr){try{d.Fm.apply(null,[b].concat(Aa(c)))}catch(e){throw jb("TAGGING",21),e;}return}d.Fm.apply(null,[b].concat(Aa(c)))};var bi=function(){this.H={};this.C={};this.M=!0;};bi.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};bi.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
bi.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:pb(b)?wh(a,b):xh(a,b)};function ci(a,b){var c=void 0;return c};function di(){var a={};
return a};var K={m:{La:"ad_personalization",aa:"ad_storage",W:"ad_user_data",ka:"analytics_storage",jc:"region",ja:"consent_updated",ug:"wait_for_update",Sn:"app_remove",Tn:"app_store_refund",Un:"app_store_subscription_cancel",Vn:"app_store_subscription_convert",Wn:"app_store_subscription_renew",Xn:"consent_update",qk:"add_payment_info",rk:"add_shipping_info",Td:"add_to_cart",Ud:"remove_from_cart",sk:"view_cart",Vc:"begin_checkout",Vd:"select_item",kc:"view_item_list",yc:"select_promotion",mc:"view_promotion",
sb:"purchase",Wd:"refund",Ob:"view_item",tk:"add_to_wishlist",Yn:"exception",Zn:"first_open",ao:"first_visit",na:"gtag.config",nc:"gtag.get",bo:"in_app_purchase",Wc:"page_view",co:"screen_view",eo:"session_start",fo:"source_update",ho:"timing_complete",io:"track_social",Xd:"user_engagement",jo:"user_id_update",Ne:"gclid_link_decoration_source",Oe:"gclid_storage_source",oc:"gclgb",tb:"gclid",uk:"gclid_len",Yd:"gclgs",Zd:"gcllp",ae:"gclst",Ia:"ads_data_redaction",Pe:"gad_source",Qe:"gad_source_src",
Xc:"gclid_url",vk:"gclsrc",Re:"gbraid",be:"wbraid",Pb:"allow_ad_personalization_signals",Ag:"allow_custom_scripts",Se:"allow_direct_google_requests",Bg:"allow_display_features",Ph:"allow_enhanced_conversions",Qb:"allow_google_signals",Qh:"allow_interest_groups",ko:"app_id",lo:"app_installer_id",mo:"app_name",no:"app_version",Yc:"auid",Pr:"auto_detection_enabled",wk:"aw_remarketing",Rh:"aw_remarketing_only",Cg:"discount",Dg:"aw_feed_country",Eg:"aw_feed_language",wa:"items",Fg:"aw_merchant_id",xk:"aw_basket_type",
Te:"campaign_content",Ue:"campaign_id",Ve:"campaign_medium",We:"campaign_name",Xe:"campaign",Ye:"campaign_source",Ze:"campaign_term",Rb:"client_id",yk:"rnd",Sh:"consent_update_type",oo:"content_group",po:"content_type",mb:"conversion_cookie_prefix",Th:"conversion_id",eb:"conversion_linker",Uh:"conversion_linker_disabled",Zc:"conversion_api",Gg:"cookie_deprecation",ub:"cookie_domain",wb:"cookie_expires",Bb:"cookie_flags",bd:"cookie_name",Sb:"cookie_path",Ua:"cookie_prefix",zc:"cookie_update",dd:"country",
fb:"currency",Vh:"customer_buyer_stage",af:"customer_lifetime_value",Wh:"customer_loyalty",Xh:"customer_ltv_bucket",bf:"custom_map",Hg:"gcldc",ed:"dclid",zk:"debug_mode",Da:"developer_id",qo:"disable_merchant_reported_purchases",fd:"dc_custom_params",ro:"dc_natural_search",Ak:"dynamic_event_settings",Bk:"affiliation",Ig:"checkout_option",Yh:"checkout_step",Ck:"coupon",cf:"item_list_name",Zh:"list_name",so:"promotions",de:"shipping",Dk:"tax",Jg:"engagement_time_msec",Kg:"enhanced_client_id",uo:"enhanced_conversions",
Qr:"enhanced_conversions_automatic_settings",df:"estimated_delivery_date",ai:"euid_logged_in_state",ef:"event_callback",vo:"event_category",Ac:"event_developer_id_string",wo:"event_label",gd:"event",Lg:"event_settings",Mg:"event_timeout",xo:"description",yo:"fatal",zo:"experiments",bi:"firebase_id",ee:"first_party_collection",Ng:"_x_20",qc:"_x_19",Ek:"flight_error_code",Fk:"flight_error_message",Gk:"fl_activity_category",Hk:"fl_activity_group",di:"fl_advertiser_id",Ik:"fl_ar_dedupe",ff:"match_id",
Jk:"fl_random_number",Kk:"tran",Lk:"u",Og:"gac_gclid",fe:"gac_wbraid",Mk:"gac_wbraid_multiple_conversions",Nk:"ga_restrict_domain",Ok:"ga_temp_client_id",Ao:"ga_temp_ecid",he:"gdpr_applies",Pk:"geo_granularity",hf:"value_callback",jf:"value_key",Cc:"google_analysis_params",ie:"_google_ng",je:"google_signals",Qk:"google_tld",kf:"gpp_sid",lf:"gpp_string",Pg:"groups",Rk:"gsa_experiment_id",nf:"gtag_event_feature_usage",Sk:"gtm_up",Dc:"iframe_state",pf:"ignore_referrer",ei:"internal_traffic_results",
Tk:"_is_fpm",Ec:"is_legacy_converted",Fc:"is_legacy_loaded",fi:"is_passthrough",hd:"_lps",xb:"language",Qg:"legacy_developer_id_string",Va:"linker",qf:"accept_incoming",Gc:"decorate_forms",ma:"domains",jd:"url_position",kd:"merchant_feed_label",ld:"merchant_feed_language",md:"merchant_id",Uk:"method",Bo:"name",Vk:"navigation_type",rf:"new_customer",Rg:"non_interaction",Co:"optimize_id",Wk:"page_hostname",tf:"page_path",Wa:"page_referrer",Cb:"page_title",Xk:"passengers",Yk:"phone_conversion_callback",
Do:"phone_conversion_country_code",Zk:"phone_conversion_css_class",Eo:"phone_conversion_ids",al:"phone_conversion_number",bl:"phone_conversion_options",Fo:"_platinum_request_status",Go:"_protected_audience_enabled",ke:"quantity",Sg:"redact_device_info",gi:"referral_exclusion_definition",Rr:"_request_start_time",Tb:"restricted_data_processing",Ho:"retoken",Io:"sample_rate",hi:"screen_name",Hc:"screen_resolution",fl:"_script_source",Jo:"search_term",nd:"send_page_view",od:"send_to",pd:"server_container_url",
Ko:"session_attributes_encoded",uf:"session_duration",Tg:"session_engaged",ii:"session_engaged_time",Ub:"session_id",Ug:"session_number",vf:"_shared_user_id",me:"delivery_postal_code",Sr:"_tag_firing_delay",Tr:"_tag_firing_time",Ur:"temporary_client_id",ji:"_timezone",ki:"topmost_url",Lo:"tracking_id",li:"traffic_type",Oa:"transaction_id",rc:"transport_url",il:"trip_type",rd:"update",Db:"url_passthrough",jl:"uptgs",wf:"_user_agent_architecture",xf:"_user_agent_bitness",yf:"_user_agent_full_version_list",
zf:"_user_agent_mobile",Af:"_user_agent_model",Bf:"_user_agent_platform",Cf:"_user_agent_platform_version",Df:"_user_agent_wow64",yb:"user_data",kl:"user_data_auto_latency",ml:"user_data_auto_meta",nl:"user_data_auto_multi",ol:"user_data_auto_selectors",pl:"user_data_auto_status",Eb:"user_data_mode",ql:"user_data_settings",Ma:"user_id",Vb:"user_properties",rl:"_user_region",Ef:"us_privacy_string",Fa:"value",sl:"wbraid_multiple_conversions",ud:"_fpm_parameters",ui:"_host_name",Ql:"_in_page_command",
xi:"_ip_override",Ul:"_is_passthrough_cid",Ei:"_measurement_type",Bd:"non_personalized_ads",Li:"_sst_parameters",op:"sgtm_geo_user_country",ce:"conversion_label",Ea:"page_location",Bc:"global_developer_id_string",ne:"tc_privacy_string"}};var ei={},fi=(ei[K.m.ja]="gcu",ei[K.m.oc]="gclgb",ei[K.m.tb]="gclaw",ei[K.m.uk]="gclid_len",ei[K.m.Yd]="gclgs",ei[K.m.Zd]="gcllp",ei[K.m.ae]="gclst",ei[K.m.Yc]="auid",ei[K.m.Cg]="dscnt",ei[K.m.Dg]="fcntr",ei[K.m.Eg]="flng",ei[K.m.Fg]="mid",ei[K.m.xk]="bttype",ei[K.m.Rb]="gacid",ei[K.m.ce]="label",ei[K.m.Zc]="capi",ei[K.m.Gg]="pscdl",ei[K.m.fb]="currency_code",ei[K.m.Vh]="clobs",ei[K.m.af]="vdltv",ei[K.m.Wh]="clolo",ei[K.m.Xh]="clolb",ei[K.m.zk]="_dbg",ei[K.m.df]="oedeld",ei[K.m.Ac]="edid",ei[K.m.Og]=
"gac",ei[K.m.fe]="gacgb",ei[K.m.Mk]="gacmcov",ei[K.m.he]="gdpr",ei[K.m.Bc]="gdid",ei[K.m.ie]="_ng",ei[K.m.kf]="gpp_sid",ei[K.m.lf]="gpp",ei[K.m.Rk]="gsaexp",ei[K.m.nf]="_tu",ei[K.m.Dc]="frm",ei[K.m.fi]="gtm_up",ei[K.m.hd]="lps",ei[K.m.Qg]="did",ei[K.m.kd]="fcntr",ei[K.m.ld]="flng",ei[K.m.md]="mid",ei[K.m.rf]=void 0,ei[K.m.Cb]="tiba",ei[K.m.Tb]="rdp",ei[K.m.Ub]="ecsid",ei[K.m.vf]="ga_uid",ei[K.m.me]="delopc",ei[K.m.ne]="gdpr_consent",ei[K.m.Oa]="oid",ei[K.m.jl]="uptgs",ei[K.m.wf]="uaa",ei[K.m.xf]=
"uab",ei[K.m.yf]="uafvl",ei[K.m.zf]="uamb",ei[K.m.Af]="uam",ei[K.m.Bf]="uap",ei[K.m.Cf]="uapv",ei[K.m.Df]="uaw",ei[K.m.kl]="ec_lat",ei[K.m.ml]="ec_meta",ei[K.m.nl]="ec_m",ei[K.m.ol]="ec_sel",ei[K.m.pl]="ec_s",ei[K.m.Eb]="ec_mode",ei[K.m.Ma]="userId",ei[K.m.Ef]="us_privacy",ei[K.m.Fa]="value",ei[K.m.sl]="mcov",ei[K.m.ui]="hn",ei[K.m.Ql]="gtm_ee",ei[K.m.xi]="uip",ei[K.m.Ei]="mt",ei[K.m.Bd]="npa",ei[K.m.op]="sg_uc",ei[K.m.Th]=null,ei[K.m.Hc]=null,ei[K.m.xb]=null,ei[K.m.wa]=null,ei[K.m.Ea]=null,ei[K.m.Wa]=
null,ei[K.m.ki]=null,ei[K.m.ud]=null,ei[K.m.Ne]=null,ei[K.m.Oe]=null,ei[K.m.Cc]=null,ei);function gi(a,b){if(a){var c=a.split("x");c.length===2&&(hi(b,"u_w",c[0]),hi(b,"u_h",c[1]))}}
function ii(a){var b=ji;b=b===void 0?ki:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(li(q.value)),r.push(li(q.quantity)),r.push(li(q.item_id)),r.push(li(q.start_date)),r.push(li(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ki(a){return mi(a.item_id,a.id,a.item_name)}function mi(){for(var a=l(Ea.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ni(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function hi(a,b,c){c===void 0||c===null||c===""&&!Fg[b]||(a[b]=c)}function li(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var oi={},pi=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=vb(0,1)===0,b=vb(0,1)===0,c++,c>30)return;return a},ri={nr:qi};function qi(a,b){var c=oi[b];if(!(vb(0,9999)<c.probability*(c.controlId2?4:2)*1E4))return a;var d=c.studyId,e=c.experimentId,f=c.controlId,g=c.controlId2;if(!((a.exp||{})[e]||(a.exp||{})[f]||g&&(a.exp||{})[g])){var h=pi()?0:1;g&&(h|=(pi()?0:1)<<1);h===0?si(a,e,d):h===1?si(a,f,d):h===2&&si(a,g,d)}return a}
function ti(a,b){return oi[b]?!!oi[b].active||oi[b].probability>.5||!!(a.exp||{})[oi[b].experimentId]:!1}function ui(a,b){for(var c=a.exp||{},d=l(Object.keys(c).map(Number)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c[f]===b)return f}}function si(a,b,c){var d=a.exp||{};d[b]=c;a.exp=d};var N={N:{Sj:"call_conversion",Qd:"ccm_conversion",Aa:"conversion",Mo:"floodlight",Gf:"ga_conversion",qe:"gcp_remarketing",Ci:"landing_page",Ya:"page_view",ve:"fpm_test_hit",Hb:"remarketing",Wb:"user_data_lead",zb:"user_data_web"}};function yi(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var zi=[],Ai=[],Bi,Ci;function Di(a,b){var c=Ei(a,!1);return c!==b?(Bi?Bi(a):zi.push(a),b):c}function Ei(a,b){b=b===void 0?!1:b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?!!data.blob[a]:b}function Fi(a){var b;b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}function Gi(){var a=Hi.M,b=Ii(54);return b===a||isNaN(b)&&isNaN(a)?b:(Bi?Bi(54):zi.push(54),a)}
function Ii(a){var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(a))?Number(data.blob[a]):0}function Ji(a,b){var c;c=c===void 0?"":c;if(!G(225))return b;var d,e,f,g=(d=(e=data)==null?void 0:(f=e.blob)==null?void 0:f[46])&&(d==null?0:d.hasOwnProperty(a))?String(d[a]):c;return g!==b?(Ci?Ci(a):Ai.push(a),b):g}
function Ki(){var a=Li,b=Mi;Bi=a;for(var c=l(zi),d=c.next();!d.done;d=c.next())a(d.value);zi.length=0;if(G(225)){Ci=b;for(var e=l(Ai),f=e.next();!f.done;f=e.next())b(f.value);Ai.length=0}};var Ni=function(){this.C=new Set;this.H=new Set},Oi=function(a){var b=Hi.U;a=a===void 0?[]:a;var c=[].concat(Aa(b.C)).concat([].concat(Aa(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Pi=function(){var a=[].concat(Aa(Hi.U.C));a.sort(function(b,c){return b-c});return a},Qi=function(){var a=Hi.U,b=Fi(44);a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Ri={},Si={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Ti={__paused:1,__tg:1},Ui;for(Ui in Si)Si.hasOwnProperty(Ui)&&(Ti[Ui]=1);var Vi=!1;function Wi(){var a=!1;a=!0;return a}var Xi=G(218)?Di(45,Wi()):Wi(),Yi,Zi=!1;Yi=Zi;var $i=null,aj=null,bj={},cj={},dj="";Ri.Mi=dj;var Hi=new function(){this.U=new Ni;this.H=this.C=!1;this.M=0;this.la=this.Ja=this.Xa="";this.ia=this.R=!1};function ej(){var a=Fi(18),b=a.length;return a[b-1]==="/"?a.substring(0,b-1):a}function fj(){return Hi.H?G(84)?Hi.M===0:Hi.M!==1:!1}function gj(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var hj=/:[0-9]+$/,ij=/^\d+\.fls\.doubleclick\.net$/;function jj(a,b,c,d){var e=kj(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function kj(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=ya(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function lj(a){try{return decodeURIComponent(a)}catch(b){}}function mj(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=nj(a.protocol)||nj(w.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:w.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||w.location.hostname).replace(hj,"").toLowerCase());return oj(a,b,c,d,e)}
function oj(a,b,c,d,e){var f,g=nj(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=pj(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(hj,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||jb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=jj(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function nj(a){return a?a.replace(":","").toLowerCase():""}function pj(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var qj={},rj=0;
function sj(a){var b=qj[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||jb("TAGGING",1),d="/"+d);var e=c.hostname.replace(hj,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};rj<5&&(qj[a]=b,rj++)}return b}function tj(a,b,c){var d=sj(a);return Rb(b,d,c)}
function uj(a){var b=sj(w.location.href),c=mj(b,"host",!1);if(c&&c.match(ij)){var d=mj(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var vj=/gtag[.\/]js/,wj=/gtm[.\/]js/,xj=!1;function yj(a){if(xj)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(vj.test(c))return"3";if(wj.test(c))return"2"}return"0"};function O(a){jb("GTM",a)};function zj(a,b){var c=Aj();c.pending||(c.pending=[]);ub(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Bj(){var a=w.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Cj=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Bj()};function Aj(){var a=Cc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Cj,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Bj());return c};function Dj(){return Ei(7)&&Ej().some(function(a){return a===Fi(5)})}function Fj(){return Fi(6)||"_"+Fi(5)}function Gj(){var a=Fi(10);return a?a.split("|"):[Fi(5)]}function Ej(){var a=Fi(9);return a?a.split("|").filter(function(b){return b.indexOf("GTM-")!==0}):[]}function Hj(){var a=Ij(Jj()),b=a&&a.parent;if(b)return Ij(b)}function Kj(){var a=Ij(Jj());if(a){for(;a.parent;){var b=Ij(a.parent);if(!b)break;a=b}return a}}
function Ij(a){var b=Aj();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function Lj(){var a=Aj();if(a.pending){for(var b,c=[],d=!1,e=Gj(),f=Ej(),g={},h=0;h<a.pending.length;g={ng:void 0},h++)g.ng=a.pending[h],ub(g.ng.target.isDestination?f:e,function(m){return function(n){return n===m.ng.target.ctid}}(g))?d||(b=g.ng.onLoad,d=!0):c.push(g.ng);a.pending=c;if(b)try{b(Fj())}catch(m){}}}
function Mj(){for(var a=Fi(5),b=Gj(),c=Ej(),d=function(n,p){var q={canonicalContainerId:Fi(6),scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};Ac&&(q.scriptElement=Ac);Bc&&(q.scriptSource=Bc);if(Hj()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var x=Hi.H,y=sj(v),A=x?y.pathname:""+y.hostname+y.pathname,D=z.scripts,E="",L=0;L<D.length;++L){var F=D[L];if(!(F.innerHTML.length===0||!x&&F.innerHTML.indexOf(q.scriptContainerId||
"SHOULD_NOT_BE_SET")<0||F.innerHTML.indexOf(A)<0)){if(F.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(L);break b}E=String(L)}}if(E){t=E;break b}}t=void 0}var M=t;if(M){xj=!0;r=M;break a}}var U=[].slice.call(z.scripts);r=q.scriptElement?String(U.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=yj(q)}var fa=p?e.destination:e.container,S=fa[n];S?(p&&S.state===0&&O(93),ma(Object,"assign").call(Object,S,q)):fa[n]=q},e=Aj(),f=l(b),g=f.next();!g.done;g=f.next())d(g.value,!1);for(var h=
l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Fj()]={};Lj()}function Nj(){var a=Fj();return!!Aj().canonical[a]}function Oj(a){return!!Aj().container[a]}function Pj(a){var b=Aj().destination[a];return!!b&&!!b.state}function Jj(){return{ctid:Fi(5),isDestination:Ei(7)}}function Qj(a,b,c){var d=Jj(),e=Aj().container[a];e&&e.state!==3||(Aj().container[a]={state:1,context:b,parent:d},zj({ctid:a,isDestination:!1},c))}
function Rj(){var a=Aj().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function Sj(){var a={};yb(Aj().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Tj(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Uj(){for(var a=Aj(),b=l(Gj()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};function Vj(a){a=a===void 0?[]:a;return Oi(a).join("~")}function Wj(){if(!G(118))return"";var a,b;return(((a=Ij(Jj()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};var Xj={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Yj=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Zj(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return sj(""+c+b).href}}function ak(a,b){if(fj()||Hi.C)return Zj(a,b)}
function bk(){return!!Ri.Mi&&Ri.Mi.split("@@").join("")!=="SGTM_TOKEN"}function ck(a){for(var b=l([K.m.pd,K.m.rc]),c=b.next();!c.done;c=b.next()){var d=P(a,c.value);if(d)return d}}function dk(a,b,c){c=c===void 0?"":c;if(!fj())return a;var d=b?Xj[a]||"":"";d==="/gs"&&(c="");return""+ej()+d+c}function ek(a){if(!fj())return a;for(var b=l(Yj),c=b.next();!c.done;c=b.next()){var d=c.value;if(Kb(a,""+ej()+d))return a+"&_uip="+encodeURIComponent("::")}return a};function fk(a){var b=String(a[mf.Pa]||"").replace(/_/g,"");return Kb(b,"cvt")?"cvt":b}var gk=w.location.search.indexOf("?gtm_latency=")>=0||w.location.search.indexOf("&gtm_latency=")>=0;var hk=Math.random(),ik,jk=Ii(27);ik=gk||hk<jk;var kk,lk=Ii(42);kk=gk||hk>=1-lk;function mk(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var nk=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};var ok,pk;a:{for(var qk=["CLOSURE_FLAGS"],rk=Fa,sk=0;sk<qk.length;sk++)if(rk=rk[qk[sk]],rk==null){pk=null;break a}pk=rk}var tk=pk&&pk[610401301];ok=tk!=null?tk:!1;function uk(){var a=Fa.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var vk,wk=Fa.navigator;vk=wk?wk.userAgentData||null:null;function xk(a){if(!ok||!vk)return!1;for(var b=0;b<vk.brands.length;b++){var c=vk.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function yk(a){return uk().indexOf(a)!=-1};function zk(){return ok?!!vk&&vk.brands.length>0:!1}function Ak(){return zk()?!1:yk("Opera")}function Bk(){return yk("Firefox")||yk("FxiOS")}function Ck(){return zk()?xk("Chromium"):(yk("Chrome")||yk("CriOS"))&&!(zk()?0:yk("Edge"))||yk("Silk")};function Dk(){return ok?!!vk&&!!vk.platform:!1}function Ek(){return yk("iPhone")&&!yk("iPod")&&!yk("iPad")}function Fk(){Ek()||yk("iPad")||yk("iPod")};var Gk=function(a){Gk[" "](a);return a};Gk[" "]=function(){};Ak();zk()||yk("Trident")||yk("MSIE");yk("Edge");!yk("Gecko")||uk().toLowerCase().indexOf("webkit")!=-1&&!yk("Edge")||yk("Trident")||yk("MSIE")||yk("Edge");uk().toLowerCase().indexOf("webkit")!=-1&&!yk("Edge")&&yk("Mobile");Dk()||yk("Macintosh");Dk()||yk("Windows");(Dk()?vk.platform==="Linux":yk("Linux"))||Dk()||yk("CrOS");Dk()||yk("Android");Ek();yk("iPad");yk("iPod");Fk();uk().toLowerCase().indexOf("kaios");Bk();Ek()||yk("iPod");yk("iPad");!yk("Android")||Ck()||Bk()||Ak()||yk("Silk");Ck();!yk("Safari")||Ck()||(zk()?0:yk("Coast"))||Ak()||(zk()?0:yk("Edge"))||(zk()?xk("Microsoft Edge"):yk("Edg/"))||(zk()?xk("Opera"):yk("OPR"))||Bk()||yk("Silk")||yk("Android")||Fk();var Hk={},Ik=null,Jk=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Ik){Ik={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Hk[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Ik[q]===void 0&&(Ik[q]=p)}}}for(var r=Hk[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,x=0;v<b.length-2;v+=3){var y=b[v],
A=b[v+1],D=b[v+2],E=r[y>>2],L=r[(y&3)<<4|A>>4],F=r[(A&15)<<2|D>>6],M=r[D&63];t[x++]=""+E+L+F+M}var U=0,fa=u;switch(b.length-v){case 2:U=b[v+1],fa=r[(U&15)<<2]||u;case 1:var S=b[v];t[x]=""+r[S>>2]+r[(S&3)<<4|U>>4]+fa+u}return t.join("")};var Kk=function(a){return decodeURIComponent(a.replace(/\+/g," "))};var Lk=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Mk=/#|$/,Nk=function(a,b){var c=a.search(Mk),d=Lk(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Kk(a.slice(d,e!==-1?e:0))},Ok=/[?&]($|#)/,Pk=function(a,b,c){for(var d,e=a.search(Mk),f=0,g,h=[];(g=Lk(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Ok,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Qk(a,b,c,d,e,f,g){var h=Nk(c,"fmt");if(d){var m=Nk(c,"random"),n=Nk(c,"label")||"";if(!m)return!1;var p=Jk(Kk(n)+":"+Kk(m));if(!mk(a,p,d))return!1}h&&Number(h)!==4&&(c=Pk(c,"rfmt",h));var q=Pk(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||Rk(g);Kc(q,function(){g==null||Sk(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||Sk(g);e==null||e()},f,r||void 0);return!0};var Tk={},Uk=(Tk[1]={},Tk[2]={},Tk[3]={},Tk[4]={},Tk);function Vk(a,b,c){var d=Wk(b,c);if(d){var e=Uk[b][d];e||(e=Uk[b][d]=[]);e.push(ma(Object,"assign").call(Object,{},a))}}function Xk(a,b){var c=Wk(a,b);if(c){var d=Uk[a][c];d&&(Uk[a][c]=d.filter(function(e){return!e.hn}))}}function Yk(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function Wk(a,b){var c=b;if(b[0]==="/"){var d;c=((d=w.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function Zk(a){var b=Ea.apply(1,arguments);kk&&(Vk(a,2,b[0]),Vk(a,3,b[0]));Wc.apply(null,Aa(b))}function $k(a){var b=Ea.apply(1,arguments);kk&&Vk(a,2,b[0]);return Xc.apply(null,Aa(b))}function al(a){var b=Ea.apply(1,arguments);kk&&Vk(a,3,b[0]);Nc.apply(null,Aa(b))}
function bl(a){var b=Ea.apply(1,arguments),c=b[0];kk&&(Vk(a,2,c),Vk(a,3,c));return Zc.apply(null,Aa(b))}function cl(a){var b=Ea.apply(1,arguments);kk&&Vk(a,1,b[0]);Kc.apply(null,Aa(b))}function dl(a){var b=Ea.apply(1,arguments);b[0]&&kk&&Vk(a,4,b[0]);Mc.apply(null,Aa(b))}function el(a){var b=Ea.apply(1,arguments);kk&&Vk(a,1,b[2]);return Qk.apply(null,Aa(b))};var fl={Ka:{pe:0,ue:1,Fi:2}};fl.Ka[fl.Ka.pe]="FULL_TRANSMISSION";fl.Ka[fl.Ka.ue]="LIMITED_TRANSMISSION";fl.Ka[fl.Ka.Fi]="NO_TRANSMISSION";var gl={Z:{Gb:0,Ha:1,xc:2,Ic:3}};gl.Z[gl.Z.Gb]="NO_QUEUE";gl.Z[gl.Z.Ha]="ADS";gl.Z[gl.Z.xc]="ANALYTICS";gl.Z[gl.Z.Ic]="MONITORING";function hl(){var a=Cc("google_tag_data",{});return a.ics=a.ics||new il}var il=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
il.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;jb("TAGGING",19);b==null?jb("TAGGING",18):jl(this,a,b==="granted",c,d,e,f,g)};il.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)jl(this,a[d],void 0,void 0,"","",b,c)};
var jl=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&qb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&w.setTimeout(function(){m[b]===t&&t.quiet&&(jb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=il.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())kl(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())kl(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&qb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Fd:b})};var kl=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Zm=!0)}};il.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.Zm){d.Zm=!1;try{d.Fd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var ll=!1,ml=!1,nl={},ol={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(nl.ad_storage=1,nl.analytics_storage=1,nl.ad_user_data=1,nl.ad_personalization=1,nl),usedContainerScopedDefaults:!1};function pl(a){var b=hl();b.accessedAny=!0;return(qb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,ol)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function ql(a){var b=hl();b.accessedAny=!0;return b.getConsentState(a,ol)}function rl(a){var b=hl();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function sl(){if(!Wa(7))return!1;var a=hl();a.accessedAny=!0;if(a.active)return!0;if(!ol.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(ol.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(ol.containerScopedDefaults[c.value]!==1)return!0;return!1}function tl(a,b){hl().addListener(a,b)}
function ul(a,b){hl().notifyListeners(a,b)}function vl(a,b){function c(){for(var e=0;e<b.length;e++)if(!rl(b[e]))return!0;return!1}if(c()){var d=!1;tl(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function wl(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];pl(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=qb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),tl(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):w.setTimeout(function(){m(c())},500)}}))};var xl={},yl=(xl[gl.Z.Gb]=fl.Ka.pe,xl[gl.Z.Ha]=fl.Ka.pe,xl[gl.Z.xc]=fl.Ka.pe,xl[gl.Z.Ic]=fl.Ka.pe,xl),zl=function(a,b){this.C=a;this.consentTypes=b};zl.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return pl(a)});case 1:return this.consentTypes.some(function(a){return pl(a)});default:qc(this.C,"consentsRequired had an unknown type")}};
var Al={},Bl=(Al[gl.Z.Gb]=new zl(0,[]),Al[gl.Z.Ha]=new zl(0,["ad_storage"]),Al[gl.Z.xc]=new zl(0,["analytics_storage"]),Al[gl.Z.Ic]=new zl(1,["ad_storage","analytics_storage"]),Al);var Dl=function(a){var b=this;this.type=a;this.C=[];tl(Bl[a].consentTypes,function(){Cl(b)||b.flush()})};Dl.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var Cl=function(a){return yl[a.type]===fl.Ka.Fi&&!Bl[a.type].isConsentGranted()},El=function(a,b){Cl(a)?a.C.push(b):b()},Fl=new Map;function Gl(a){Fl.has(a)||Fl.set(a,new Dl(a));return Fl.get(a)};var Hl={X:{un:"aw_user_data_cache",Lh:"cookie_deprecation_label",zg:"diagnostics_page_id",mi:"eab",No:"fl_user_data_cache",Ro:"ga4_user_data_cache",se:"ip_geo_data_cache",wi:"ip_geo_fetch_in_progress",Di:"local_cookie_cache_map",Zl:"nb_data",Gi:"page_experiment_ids",we:"pt_data",bm:"pt_listener_set",im:"service_worker_endpoint",lm:"shared_user_id",om:"shared_user_id_requested",kh:"shared_user_id_source"}};var Il=function(a){return ef(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(Hl.X);
function Jl(a,b){b=b===void 0?!1:b;if(Il(a)){var c,d,e=(d=(c=Cc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function Kl(a,b){var c=Jl(a,!0);c&&c.set(b)}function Ll(a){var b;return(b=Jl(a))==null?void 0:b.get()}function Ml(a,b){var c=Jl(a);if(!c){c=Jl(a,!0);if(!c)return;c.set(b)}return c.get()}function Nl(a,b){if(typeof b==="function"){var c;return(c=Jl(a,!0))==null?void 0:c.subscribe(b)}}function Ol(a,b){var c=Jl(a);return c?c.unsubscribe(b):!1};var Pl={},Ql=(Pl.tdp=1,Pl.exp=1,Pl.pid=1,Pl.dl=1,Pl.seq=1,Pl.t=1,Pl.v=1,Pl),Rl=["mcc"],Sl={},Tl={},Ul=!1;function Vl(a,b,c){Tl[a]=b;(c===void 0||c)&&Wl(a)}function Wl(a,b){Sl[a]!==void 0&&(b===void 0||!b)||Kb(Fi(5),"GTM-")&&a==="mcc"||(Sl[a]=!0)}
function Xl(a){a=a===void 0?!1:a;var b=Object.keys(Sl).filter(function(f){return Sl[f]===!0&&Tl[f]!==void 0&&(a||!Rl.includes(f))});Yl(b);var c=b.map(function(f){var g=Tl[f];typeof g==="function"&&(g=g());return g?"&"+f+"="+g:""}).join(""),d="https://"+Fi(21),e="/td?id="+Fi(5);return""+dk(d)+e+(""+c+"&z=0")}function Yl(a){a.forEach(function(b){Ql[b]||(Sl[b]=!1)})}
function Zl(a){a=a===void 0?!1:a;if(Hi.ia&&kk&&Fi(5)){var b=Gl(gl.Z.Ic);if(Cl(b))Ul||(Ul=!0,El(b,Zl));else{var c=Xl(a),d={destinationId:Fi(5),endpoint:61};a?bl(d,c,void 0,{Ch:!0},void 0,function(){al(d,c+"&img=1")}):al(d,c);Ul=!1}}}function $l(){Object.keys(Sl).filter(function(a){return Sl[a]&&!Ql[a]}).length>0&&Zl(!0)}var am;function bm(){if(Ll(Hl.X.zg)===void 0){var a=function(){Kl(Hl.X.zg,vb());am=0};a();w.setInterval(a,864E5)}else Nl(Hl.X.zg,function(){am=0});am=0}
function cm(){bm();Vl("v","3");Vl("t","t");Vl("pid",function(){return String(Ll(Hl.X.zg))});Vl("seq",function(){return String(++am)});Vl("exp",Vj());Pc(w,"pagehide",$l)};var dm=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],em=[K.m.pd,K.m.rc,K.m.ee,K.m.Rb,K.m.Ub,K.m.Ma,K.m.Va,K.m.Ua,K.m.ub,K.m.Sb],fm=!1,gm=!1,hm={},im={};function jm(){!gm&&fm&&(dm.some(function(a){return ol.containerScopedDefaults[a]!==1})||km("mbc"));gm=!0}function km(a){kk&&(Vl(a,"1"),Zl())}function lm(a,b){if(!hm[b]&&(hm[b]=!0,im[b]))for(var c=l(em),d=c.next();!d.done;d=c.next())if(P(a,d.value)){km("erc");break}};function mm(a){jb("HEALTH",a)};var nm={},om=!1;function pm(){function a(){c!==void 0&&Ol(Hl.X.se,c);try{var e=Ll(Hl.X.se);nm=JSON.parse(e)}catch(f){O(123),mm(2),nm={}}om=!0;b()}var b=qm,c=void 0,d=Ll(Hl.X.se);d?a(d):(c=Nl(Hl.X.se,a),rm())}
function rm(){function a(b){Kl(Hl.X.se,b||"{}");Kl(Hl.X.wi,!1)}if(!Ll(Hl.X.wi)){Kl(Hl.X.wi,!0);try{w.fetch("https://www.google.com/ccm/geo",{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(b){b.ok?b.text().then(function(c){a(c)},function(){a()}):a()},function(){a()})}catch(b){a()}}}function sm(){var a=Fi(22);try{return JSON.parse(hb(a))}catch(b){return O(123),mm(2),{}}}function tm(){return nm["0"]||""}function um(){return nm["1"]||""}
function vm(){var a=!1;a=!!nm["2"];return a}function wm(){return nm["6"]!==!1}function xm(){var a="";a=nm["4"]||"";return a}function ym(){var a="";a=nm["3"]||"";return a};var zm={},Am=Object.freeze((zm[K.m.Pb]=1,zm[K.m.Bg]=1,zm[K.m.Ph]=1,zm[K.m.Qb]=1,zm[K.m.wa]=1,zm[K.m.ub]=1,zm[K.m.wb]=1,zm[K.m.Bb]=1,zm[K.m.bd]=1,zm[K.m.Sb]=1,zm[K.m.Ua]=1,zm[K.m.zc]=1,zm[K.m.bf]=1,zm[K.m.Da]=1,zm[K.m.Ak]=1,zm[K.m.ef]=1,zm[K.m.Lg]=1,zm[K.m.Mg]=1,zm[K.m.ee]=1,zm[K.m.Nk]=1,zm[K.m.Cc]=1,zm[K.m.je]=1,zm[K.m.Qk]=1,zm[K.m.Pg]=1,zm[K.m.ei]=1,zm[K.m.Ec]=1,zm[K.m.Fc]=1,zm[K.m.Va]=1,zm[K.m.gi]=1,zm[K.m.Tb]=1,zm[K.m.nd]=1,zm[K.m.od]=1,zm[K.m.pd]=1,zm[K.m.uf]=1,zm[K.m.ii]=1,zm[K.m.me]=1,zm[K.m.rc]=
1,zm[K.m.rd]=1,zm[K.m.ql]=1,zm[K.m.Vb]=1,zm[K.m.ud]=1,zm[K.m.Li]=1,zm));Object.freeze([K.m.Ea,K.m.Wa,K.m.Cb,K.m.xb,K.m.hi,K.m.Ma,K.m.bi,K.m.oo]);
var Bm={},Cm=Object.freeze((Bm[K.m.Sn]=1,Bm[K.m.Tn]=1,Bm[K.m.Un]=1,Bm[K.m.Vn]=1,Bm[K.m.Wn]=1,Bm[K.m.Zn]=1,Bm[K.m.ao]=1,Bm[K.m.bo]=1,Bm[K.m.eo]=1,Bm[K.m.Xd]=1,Bm)),Dm={},Em=Object.freeze((Dm[K.m.qk]=1,Dm[K.m.rk]=1,Dm[K.m.Td]=1,Dm[K.m.Ud]=1,Dm[K.m.sk]=1,Dm[K.m.Vc]=1,Dm[K.m.Vd]=1,Dm[K.m.kc]=1,Dm[K.m.yc]=1,Dm[K.m.mc]=1,Dm[K.m.sb]=1,Dm[K.m.Wd]=1,Dm[K.m.Ob]=1,Dm[K.m.tk]=1,Dm)),Fm=Object.freeze([K.m.Pb,K.m.Se,K.m.Qb,K.m.zc,K.m.ee,K.m.pf,K.m.nd,K.m.rd]),Gm=Object.freeze([].concat(Aa(Fm))),Hm=Object.freeze([K.m.wb,
K.m.Mg,K.m.uf,K.m.ii,K.m.Jg]),Im=Object.freeze([].concat(Aa(Hm))),Jm={},Km=(Jm[K.m.aa]="1",Jm[K.m.ka]="2",Jm[K.m.W]="3",Jm[K.m.La]="4",Jm),Lm={},Mm=Object.freeze((Lm.search="s",Lm.youtube="y",Lm.playstore="p",Lm.shopping="h",Lm.ads="a",Lm.maps="m",Lm));function Nm(a){return typeof a!=="object"||a===null?{}:a}function Om(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Pm(a){if(a!==void 0&&a!==null)return Om(a)}function Qm(a){return typeof a==="number"?a:Pm(a)};function Rm(a){return a&&a.indexOf("pending:")===0?Sm(a.substr(8)):!1}function Sm(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Fb();return b<c+3E5&&b>c-9E5};var Tm=!1,Um=!1,Vm=!1,Wm=0,Xm=!1,Ym=[];function Zm(a){if(Wm===0)Xm&&Ym&&(Ym.length>=100&&Ym.shift(),Ym.push(a));else if($m()){var b=Fi(41),c=Cc(b,[]);c.length>=50&&c.shift();c.push(a)}}function an(){bn();Qc(z,"TAProdDebugSignal",an)}function bn(){if(!Um){Um=!0;cn();var a=Ym;Ym=void 0;a==null||a.forEach(function(b){Zm(b)})}}
function cn(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Sm(a)?Wm=1:!Rm(a)||Tm||Vm?Wm=2:(Vm=!0,Pc(z,"TAProdDebugSignal",an,!1),w.setTimeout(function(){bn();Tm=!0},200))}function $m(){if(!Xm)return!1;switch(Wm){case 1:case 0:return!0;case 2:return!1;default:return!1}};var dn=!1;function en(a,b){var c=Gj(),d=Ej();if($m()){var e=fn("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Zm(e)}}
function gn(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.kb;e=a.isBatched;var f;if(f=$m()){var g;a:switch(c.endpoint){case 19:case 47:g=!0;break a;default:g=!1}f=!g}if(f){var h=fn("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Zm(h)}}function hn(a){$m()&&gn(a())}
function fn(a,b){b=b===void 0?{}:b;b.groupId=jn;var c,d=b,e={publicId:kn};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'5',messageType:a};c.containerProduct=dn?"OGT":"GTM";c.key.targetRef=ln;return c}var kn="",ln={ctid:"",isDestination:!1},jn;
function mn(a){var b=Fi(5),c=Dj(),d=Fi(6);Wm=0;Xm=!0;cn();jn=a;kn=b;dn=Xi;ln={ctid:b,isDestination:c,canonicalId:d}};var nn=[K.m.aa,K.m.ka,K.m.W,K.m.La],on,pn;function qn(a){var b=a[K.m.jc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)yb(a,function(d){return function(e,f){if(e!==K.m.jc){var g=Om(f),h=b[d.cg],m=tm(),n=um();ml=!0;ll&&jb("TAGGING",20);hl().declare(e,g,h,m,n)}}}(c))}
function rn(a){jm();!pn&&on&&km("crc");pn=!0;var b=a[K.m.ug];b&&O(41);var c=a[K.m.jc];c?O(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)yb(a,function(e){return function(f,g){if(f!==K.m.jc&&f!==K.m.ug){var h=Pm(g),m=c[e.dg],n=Number(b),p=tm(),q=um();n=n===void 0?0:n;ll=!0;ml&&jb("TAGGING",20);hl().default(f,h,m,p,q,n,ol)}}}(d))}
function sn(a){ol.usedContainerScopedDefaults=!0;var b=a[K.m.jc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(um())&&!c.includes(tm()))return}yb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}ol.usedContainerScopedDefaults=!0;ol.containerScopedDefaults[d]=e==="granted"?3:2})}
function tn(a,b){jm();on=!0;yb(a,function(c,d){var e=Om(d);ll=!0;ml&&jb("TAGGING",20);hl().update(c,e,ol)});ul(b.eventId,b.priorityId)}function un(a){a.hasOwnProperty("all")&&(ol.selectedAllCorePlatformServices=!0,yb(Mm,function(b){ol.corePlatformServices[b]=a.all==="granted";ol.usedCorePlatformServices=!0}));yb(a,function(b,c){b!=="all"&&(ol.corePlatformServices[b]=c==="granted",ol.usedCorePlatformServices=!0)})}function vn(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return pl(b)})}
function wn(a,b){tl(a,b)}function xn(a,b){wl(a,b)}function yn(a,b){vl(a,b)}function zn(){var a=[K.m.aa,K.m.La,K.m.W];hl().waitForUpdate(a,500,ol)}function Hn(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;hl().clearTimeout(d,void 0,ol)}ul()}function In(){if(!Yi)for(var a=wm()?gj(Hi.Ja):gj(Hi.Xa),b=0;b<nn.length;b++){var c=nn[b],d=c,e=a[c]?"granted":"denied";hl().implicit(d,e)}};var Jn=!1;G(218)&&(Jn=Di(49,Jn));var Kn=!1,Ln=[];function Mn(){if(!Kn){Kn=!0;for(var a=Ln.length-1;a>=0;a--)Ln[a]();Ln=[]}};var Nn=w.google_tag_manager=w.google_tag_manager||{};function On(a,b){return Nn[a]=Nn[a]||b()}function Pn(){var a=Fi(5),b=Qn;Nn[a]=Nn[a]||b}function Rn(){var a=Fi(19);return Nn[a]=Nn[a]||{}}function Sn(){var a=Fi(19);return Nn[a]}function Tn(){var a=Nn.sequence||1;Nn.sequence=a+1;return a}w.google_tag_data=w.google_tag_data||{};function Un(){if(Nn.pscdl!==void 0)Ll(Hl.X.Lh)===void 0&&Kl(Hl.X.Lh,Nn.pscdl);else{var a=function(c){Nn.pscdl=c;Kl(Hl.X.Lh,c)},b=function(){a("error")};try{yc.cookieDeprecationLabel?(a("pending"),yc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Vn=0;function Wn(a){kk&&a===void 0&&Vn===0&&(Vl("mcc","1"),Vn=1)};function Xn(){var a=[],b=Number('')||0,c=Number('')||0;c||(c=b/100);var d=function(){var qa=!1;qa=!0;return qa}();a.push({Ba:21,studyId:21,experimentId:105102050,controlId:105102051,controlId2:105102052,probability:c,active:d,sa:0});var e=
Number('')||0,f=Number('')||0;f||(f=e/100);var g=function(){var qa=!1;return qa}();a.push({Ba:228,studyId:228,experimentId:105177154,controlId:105177155,controlId2:105255245,probability:f,active:g,sa:0});var h=Number('')||
0,m=Number('')||0;m||(m=h/100);var n=function(){var qa=!1;qa=!0;return qa}();a.push({Ba:244,studyId:244,experimentId:115596674,controlId:115596673,controlId2:0,probability:m,active:n,sa:0});var p=Number('')||0,
q=Number('')||0;q||(q=p/100);var r=function(){var qa=!1;return qa}();a.push({Ba:256,studyId:256,experimentId:115495938,controlId:115495939,controlId2:115495940,probability:q,active:r,sa:0});var t=Number('')||
0,u=Number('')||0;u||(u=t/100);var v=function(){var qa=!1;return qa}();a.push({Ba:257,studyId:257,experimentId:115495941,controlId:115495942,controlId2:115495943,probability:u,
active:v,sa:0});var x=Number('')||0,y=Number('')||0;y||(y=x/100);var A=function(){var qa=!1;qa=!0;return qa}();a.push({Ba:219,studyId:219,experimentId:104948811,
controlId:104948812,controlId2:0,probability:y,active:A,sa:0});var D=Number('')||0,E=Number('1')||0;E||(E=D/100);var L=function(){var qa=!1;
return qa}();a.push({Ba:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:E,active:L,sa:0});var F=Number('')||0,M=Number('')||0;M||(M=F/100);var U=function(){var qa=!1;
return qa}();a.push({Ba:255,studyId:255,experimentId:105391252,controlId:105391253,controlId2:105446120,probability:M,active:U,sa:0});var fa=Number('')||0,S=Number('')||0;S||(S=fa/100);var Z=function(){var qa=!1;return qa}();a.push({Ba:235,studyId:235,experimentId:105357150,controlId:105357151,
controlId2:0,probability:S,active:Z,sa:1});var ra=Number('')||0,ka=Number('')||0;ka||(ka=ra/100);var da=function(){var qa=!1;return qa}();a.push({Ba:218,studyId:218,experimentId:0,controlId:1,controlId2:0,probability:ka,
active:da,sa:0});var Y=Number('')||0,ja=Number('0.1')||0;ja||(ja=Y/100);var za=function(){var qa=!1;return qa}();a.push({Ba:203,studyId:203,experimentId:115480710,controlId:115480709,controlId2:115489982,probability:ja,
active:za,sa:0});var ua=Number('')||0,Va=Number('')||0;Va||(Va=ua/100);var $a=function(){var qa=!1;qa=!0;return qa}();a.push({Ba:197,studyId:197,experimentId:105113532,controlId:105113531,controlId2:0,probability:Va,active:$a,sa:0});var cc=Number('')||
0,Xb=Number('0.05')||0;Xb||(Xb=cc/100);var rb=function(){var qa=!1;return qa}();a.push({Ba:243,studyId:243,experimentId:115616985,controlId:115616986,controlId2:0,probability:Xb,active:rb,sa:0});var bd=Number('')||0,cd=Number('0.1')||
0;cd||(cd=bd/100);var Rh=function(){var qa=!1;return qa}();a.push({Ba:254,studyId:254,experimentId:115583767,controlId:115583768,controlId2:115583769,probability:cd,active:Rh,sa:0});var $G=Number('')||0,An=Number('')||
0;An||(An=$G/100);var aH=function(){var qa=!1;return qa}();a.push({Ba:253,studyId:253,experimentId:115583770,controlId:115583771,controlId2:115583772,probability:An,active:aH,sa:0});var bH=Number('')||0,Bn=Number('')||
0;Bn||(Bn=bH/100);var cH=function(){var qa=!1;return qa}();a.push({Ba:259,studyId:259,experimentId:105322302,controlId:105322303,controlId2:105322304,probability:Bn,active:cH,sa:0});var dH=Number('')||0,Cn=Number('')||0;Cn||(Cn=dH/100);
var eH=function(){var qa=!1;return qa}();a.push({Ba:249,studyId:249,experimentId:105440521,controlId:105440522,controlId2:0,focused:!0,probability:Cn,active:eH,sa:0});var fH=Number('')||0,Dn=Number('0.5')||0;Dn||(Dn=fH/100);var gH=function(){var qa=!1;return qa}();a.push({Ba:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,probability:Dn,active:gH,sa:1});var hH=Number('')||0,En=Number('0.5')||0;En||(En=hH/100);var iH=function(){var qa=!1;return qa}();a.push({Ba:196,
studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:En,active:iH,sa:0});var jH=Number('')||0,Fn=Number('0.01')||0;Fn||(Fn=jH/100);var kH=function(){var qa=!1;
return qa}();a.push({Ba:229,studyId:229,experimentId:105359938,controlId:105359937,controlId2:105359936,probability:Fn,active:kH,sa:0});var lH=Number('')||0,Gn=Number('')||0;Gn||(Gn=lH/100);var mH=function(){var qa=!1;
return qa}();a.push({Ba:225,studyId:225,experimentId:105476338,controlId:105476339,controlId2:105476599,probability:Gn,active:mH,sa:0});return a};var Q={A:{Hh:"accept_by_default",tg:"add_tag_timing",Tc:"allow_ad_personalization",Qj:"batch_on_navigation",Uj:"client_id_source",Je:"consent_event_id",Ke:"consent_priority_id",Lr:"consent_state",ja:"consent_updated",Rd:"conversion_linker_enabled",Ca:"cookie_options",wg:"create_dc_join",xg:"create_fpm_geo_join",yg:"create_fpm_signals_join",Sd:"create_google_join",Nh:"dc_random",Me:"em_event",Or:"endpoint_for_debug",pk:"enhanced_client_id_source",Oh:"enhanced_match_result",oe:"euid_mode_enabled",hb:"event_start_timestamp_ms",
xl:"event_usage",oi:"extra_tag_experiment_ids",Xr:"add_parameter",ri:"attribution_reporting_experiment",si:"counting_method",Wg:"send_as_iframe",Yr:"parameter_order",Xg:"parsed_target",Qo:"ga4_collection_subdomain",Kl:"gbraid_cookie_marked",Ml:"handle_internally",ba:"hit_type",vd:"hit_type_override",Hf:"ignore_hit_success_failure",ds:"is_config_command",ah:"is_consent_update",If:"is_conversion",Rl:"is_ecommerce",wd:"is_external_event",bh:"is_fallback_aw_conversion_ping_allowed",Jf:"is_first_visit",
Sl:"is_first_visit_conversion",eh:"is_fl_fallback_conversion_flow_allowed",xd:"is_fpm_encryption",yi:"is_fpm_split",Fb:"is_gcp_conversion",Tl:"is_google_signals_allowed",yd:"is_merchant_center",fh:"is_new_to_site",zi:"is_personalization",gh:"is_server_side_destination",te:"is_session_start",Vl:"is_session_start_conversion",es:"is_sgtm_ga_ads_conversion_study_control_group",hs:"is_sgtm_prehit",Wl:"is_sgtm_service_worker",Ai:"is_split_conversion",Xo:"is_syn",Kf:"join_id",Bi:"join_elapsed",Lf:"join_timer_sec",
xe:"tunnel_updated",qs:"prehit_for_retry",us:"promises",vs:"record_aw_latency",Dd:"redact_ads_data",ye:"redact_click_ids",gm:"remarketing_only",Ii:"send_ccm_parallel_ping",xs:"send_ccm_parallel_test_ping",Pf:"send_to_destinations",Ji:"send_to_targets",mp:"send_user_data_hit",Za:"source_canonical_id",xa:"speculative",rm:"speculative_in_message",sm:"suppress_script_load",tm:"syn_or_mod",zm:"transient_ecsid",Qf:"transmission_type",Qa:"user_data",As:"user_data_from_automatic",Bs:"user_data_from_automatic_getter",
Bm:"user_data_from_code",rp:"user_data_from_manual",Cm:"user_data_mode",Rf:"user_id_updated"}};var Yn={};function Zn(a,b){if(oi[b]){var c=oi[b],d=c.experimentId,e=c.probability;if(!(a.studies||{})[b]){var f=a.studies||{};f[b]=!0;a.studies=f;oi[b].active||(oi[b].probability>.5?si(a,d,b):e<=0||e>1||ri.nr(a,b))}}if(!Yn[b]){var g=ui(a,b);g&&Hi.U.H.add(g)}}var $n={};function ao(a){return ti(Ml(Hl.X.Gi,{}),a)||ti($n,a)}function bo(a){var b=R(a,Q.A.oi)||[];return Vj(b)}var co={};
function eo(){co={};var a,b,c=((a=w)==null?void 0:(b=a.location)==null?void 0:b.hash)||"";if(c.indexOf("_te=")!==0){var d=c.substring(5);if(d)for(var e=l(d.split("~")),f=e.next();!f.done;f=e.next()){var g=Number(f.value);g&&(co[g]=!0,C(g))}}for(var h=l(Xn()),m=h.next();!m.done;m=h.next()){var n=m.value,p=n,q=n=co[p.studyId]?ma(Object,"assign").call(Object,{},p,{active:!0}):p;q.controlId2&&q.probability<=.25||(q=ma(Object,"assign").call(Object,{},q,{controlId2:0}));oi[q.studyId]=q;n.focused&&(Yn[n.studyId]=
!0);if(n.sa===1){var r=n.studyId;Zn(Ml(Hl.X.Gi,{}),r);ao(r)&&C(r)}else if(n.sa===0){var t=n.studyId;Zn($n,t);ao(t)&&C(t)}}};var fo={Ff:{En:"cd",Gn:"ce",Hn:"cf",In:"cpf",Jn:"cu"}};var go=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,ho=/\s/;
function io(a,b){if(qb(a)){a=Db(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(go.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||ho.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function jo(a,b){for(var c={},d=0;d<a.length;++d){var e=io(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[ko[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var lo={},ko=(lo[0]=0,lo[1]=1,lo[2]=2,lo[3]=0,lo[4]=1,lo[5]=0,lo[6]=0,lo[7]=0,lo);var mo=Number(Ji(34,''))||500,no={},oo={},po={initialized:11,complete:12,interactive:13},qo={},ro=Object.freeze((qo[K.m.nd]=!0,qo)),so=void 0;function to(a,b){if(b.length&&kk){var c;(c=no)[a]!=null||(c[a]=[]);oo[a]!=null||(oo[a]=[]);var d=b.filter(function(e){return!oo[a].includes(e)});no[a].push.apply(no[a],Aa(d));oo[a].push.apply(oo[a],Aa(d));!so&&d.length>0&&(Wl("tdc",!0),so=w.setTimeout(function(){Zl();no={};so=void 0},mo))}}
function uo(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function vo(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;pd(t)==="object"?u=t[r]:pd(t)==="array"&&(u=t[r]);return u===void 0?ro[r]:u},f=uo(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=pd(m)==="object"||pd(m)==="array",q=pd(n)==="object"||pd(n)==="array";if(p&&q)vo(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function wo(){Vl("tdc",function(){so&&(w.clearTimeout(so),so=void 0);var a=[],b;for(b in no)no.hasOwnProperty(b)&&a.push(b+"*"+no[b].join("."));return a.length?a.join("!"):void 0},!1)};var xo={T:{Pj:1,Ki:2,Lj:3,ik:4,Mj:5,Uc:6,hk:7,bp:8,hm:9,Nj:10,Oj:11,Yg:12,El:13,Bl:14,Dl:15,Al:16,Cl:17,zl:18,tn:19,Oo:20,Po:21}};xo.T[xo.T.Pj]="ALLOW_INTEREST_GROUPS";xo.T[xo.T.Ki]="SERVER_CONTAINER_URL";xo.T[xo.T.Lj]="ADS_DATA_REDACTION";xo.T[xo.T.ik]="CUSTOMER_LIFETIME_VALUE";xo.T[xo.T.Mj]="ALLOW_CUSTOM_SCRIPTS";xo.T[xo.T.Uc]="ANY_COOKIE_PARAMS";xo.T[xo.T.hk]="COOKIE_EXPIRES";xo.T[xo.T.bp]="LEGACY_ENHANCED_CONVERSION_JS_VARIABLE";xo.T[xo.T.hm]="RESTRICTED_DATA_PROCESSING";xo.T[xo.T.Nj]="ALLOW_DISPLAY_FEATURES";
xo.T[xo.T.Oj]="ALLOW_GOOGLE_SIGNALS";xo.T[xo.T.Yg]="GENERATED_TRANSACTION_ID";xo.T[xo.T.El]="FLOODLIGHT_COUNTING_METHOD_UNKNOWN";xo.T[xo.T.Bl]="FLOODLIGHT_COUNTING_METHOD_STANDARD";xo.T[xo.T.Dl]="FLOODLIGHT_COUNTING_METHOD_UNIQUE";xo.T[xo.T.Al]="FLOODLIGHT_COUNTING_METHOD_PER_SESSION";xo.T[xo.T.Cl]="FLOODLIGHT_COUNTING_METHOD_TRANSACTIONS";xo.T[xo.T.zl]="FLOODLIGHT_COUNTING_METHOD_ITEMS_SOLD";xo.T[xo.T.tn]="ADS_OGT_V1_USAGE";xo.T[xo.T.Oo]="FORM_INTERACTION_PERMISSION_DENIED";xo.T[xo.T.Po]="FORM_SUBMIT_PERMISSION_DENIED";var yo={},zo=(yo[K.m.Qh]=xo.T.Pj,yo[K.m.pd]=xo.T.Ki,yo[K.m.rc]=xo.T.Ki,yo[K.m.Ia]=xo.T.Lj,yo[K.m.af]=xo.T.ik,yo[K.m.Ag]=xo.T.Mj,yo[K.m.zc]=xo.T.Uc,yo[K.m.Ua]=xo.T.Uc,yo[K.m.ub]=xo.T.Uc,yo[K.m.bd]=xo.T.Uc,yo[K.m.Sb]=xo.T.Uc,yo[K.m.Bb]=xo.T.Uc,yo[K.m.wb]=xo.T.hk,yo[K.m.Tb]=xo.T.hm,yo[K.m.Bg]=xo.T.Nj,yo[K.m.Qb]=xo.T.Oj,yo),Ao={},Bo=(Ao.unknown=xo.T.El,Ao.standard=xo.T.Bl,Ao.unique=xo.T.Dl,Ao.per_session=xo.T.Al,Ao.transactions=xo.T.Cl,Ao.items_sold=xo.T.zl,Ao);var mb=[];function Co(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(zo)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=zo[f],h=b;h=h===void 0?!1:h;jb("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(mb[g]=!0)}}};var Do=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.U=d;this.H=e;this.R=f;this.M=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Eo=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 2:c.push(a.C);break;case 1:c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 4:c.push(a.C),c.push(a.U),c.push(a.H),c.push(a.R)}return c},P=function(a,b,c,d){for(var e=l(Eo(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Fo=function(a){for(var b={},c=Eo(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Do.prototype.getMergedValues=function(a,b,c){function d(n){rd(n)&&yb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Eo(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Go=function(a){for(var b=[K.m.Xe,K.m.Te,K.m.Ue,K.m.Ve,K.m.We,K.m.Ye,K.m.Ze],c=Eo(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Ho=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.U={};this.C={};this.M={};this.ia={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Io=function(a,
b){a.H=b;return a},Jo=function(a,b){a.U=b;return a},Ko=function(a,b){a.C=b;return a},Lo=function(a,b){a.M=b;return a},Mo=function(a,b){a.ia=b;return a},No=function(a,b){a.R=b;return a},Oo=function(a,b){a.eventMetadata=b||{};return a},Po=function(a,b){a.onSuccess=b;return a},Qo=function(a,b){a.onFailure=b;return a},Ro=function(a,b){a.isGtmEvent=b;return a},So=function(a){return new Do(a.eventId,a.priorityId,a.H,a.U,a.C,a.M,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var To=new xb,Uo={},Vo={},Yo={name:Fi(19),set:function(a,b){sd(Mb(a,b),Uo);Wo()},get:function(a){return Xo(a,2)},reset:function(){To=new xb;Uo={};Wo()}};function Xo(a,b){return b!=2?To.get(a):Zo(a)}function Zo(a,b){var c=a.split(".");b=b||[];for(var d=Uo,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function $o(a,b){Vo.hasOwnProperty(a)||(To.set(a,b),sd(Mb(a,b),Uo),Wo())}
function ap(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=Xo(c,1);if(Array.isArray(d)||rd(d))d=sd(d,null);Vo[c]=d}}function Wo(a){yb(Vo,function(b,c){To.set(b,c);sd(Mb(b),Uo);sd(Mb(b,c),Uo);a&&delete Vo[b]})}function bp(a,b){var c,d=(b===void 0?2:b)!==1?Zo(a):To.get(a);pd(d)==="array"||pd(d)==="object"?c=sd(d,null):c=d;return c};var cp={sn:Number(Ji(3,'5')),Rs:Number(Ji(33,""))},dp=[],ep=!1;function fp(a){dp.push(a)}var gp=void 0,hp={},ip=void 0,jp=new function(){var a=5;cp.sn>0&&(a=cp.sn);this.H=a;this.C=0;this.M=[]},kp=1E3;
function lp(a,b){var c=gp;if(c===void 0)if(b)c=Tn();else return"";for(var d=[dk("https://"+Fi(21)),"/a","?id="+Fi(5)],e=l(dp),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Sc:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function mp(){if(Hi.ia&&(ip&&(w.clearTimeout(ip),ip=void 0),gp!==void 0&&np)){var a=Gl(gl.Z.Ic);if(Cl(a))ep||(ep=!0,El(a,mp));else{var b;if(!(b=hp[gp])){var c=jp;b=c.C<c.H?!1:Fb()-c.M[c.C%c.H]<1E3}if(b||kp--<=0)O(1),hp[gp]=!0;else{var d=jp,e=d.C++%d.H;d.M[e]=Fb();var f=lp(!0);al({destinationId:Fi(5),endpoint:56,eventId:gp},f);ep=np=!1}}}}function op(){if(ik&&Hi.ia){var a=lp(!0,!0);al({destinationId:Fi(5),endpoint:56,eventId:gp},a)}}var np=!1;
function pp(a){hp[a]||(a!==gp&&(mp(),gp=a),np=!0,ip||(ip=w.setTimeout(mp,500)),lp().length>=2022&&mp())}var qp=vb();function rp(){qp=vb()}function sp(){return[["v","3"],["t","t"],["pid",String(qp)]]};var tp={};function up(a,b,c){ik&&a!==void 0&&(tp[a]=tp[a]||[],tp[a].push(c+b),pp(a))}function vp(a){var b=a.eventId,c=a.Sc,d=[],e=tp[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete tp[b];return d};function wp(a,b,c,d){var e=io(a,!0);e&&xp.register(e,b,c,d)}function yp(a,b,c,d){var e=io(c,d.isGtmEvent);e&&(Vi&&(d.deferrable=!0),xp.push("event",[b,a],e,d))}function zp(a,b,c,d){var e=io(c,d.isGtmEvent);e&&xp.push("get",[a,b],e,d)}function Ap(a){var b=io(a,!0),c;b?c=Bp(xp,b).H:c={};return c}function Cp(a,b){var c=io(a,!0);c&&Dp(xp,c,b)}
var Ep=function(){this.U={};this.H={};this.M={};this.ia=null;this.C={};this.R=!1;this.status=1},Fp=function(a,b,c,d){this.H=Fb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Gp=function(){this.destinations={};this.C={};this.commands=[]},Bp=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Ep},Hp=function(a,b,c,d){if(d.C){var e=Bp(a,d.C),f=e.ia;if(f){var g=sd(c,null),h=sd(e.U[d.C.id],null),m=sd(e.C,null),n=sd(e.H,null),p=sd(a.C,null),q={};if(ik)try{q=
sd(Uo,null)}catch(x){O(72)}var r=d.C.prefix,t=function(x){up(d.messageContext.eventId,r,x)},u=So(Ro(Qo(Po(Oo(Mo(Lo(No(Ko(Jo(Io(new Ho(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var x=t;t=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var x=t;t=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{up(d.messageContext.eventId,
r,"1");var x=d.type,y=d.C.id;if(kk&&x==="config"){var A,D=(A=io(y))==null?void 0:A.ids;if(!(D&&D.length>1)){var E,L=Cc("google_tag_data",{});L.td||(L.td={});E=L.td;var F=sd(u.R);sd(u.C,F);var M=[],U;for(U in E)E.hasOwnProperty(U)&&vo(E[U],F).length&&M.push(U);M.length&&(to(y,M),jb("TAGGING",po[z.readyState]||14));E[y]=F}}f(d.C.id,b,d.H,u)}catch(fa){up(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():El(e.la,v)}}};
Gp.prototype.register=function(a,b,c,d){var e=Bp(this,a);e.status!==3&&(e.ia=b,e.status=3,e.la=Gl(c),Dp(this,a,d||{}),this.flush())};
Gp.prototype.push=function(a,b,c,d){c!==void 0&&(Bp(this,c).status===1&&(Bp(this,c).status=2,this.push("require",[{}],c,{})),Bp(this,c).R&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[Q.A.Pf]||(d.eventMetadata[Q.A.Pf]=[c.destinationId]),d.eventMetadata[Q.A.Ji]||(d.eventMetadata[Q.A.Ji]=[c.id]));this.commands.push(new Fp(a,c,b,d));d.deferrable||this.flush()};
Gp.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Lc:void 0,qh:void 0,Vi:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Bp(this,g).R?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Bp(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];yb(h,function(t,u){sd(Mb(t,u),b.C)});Co(h,!0);break;case "config":var m=Bp(this,
g);e.Lc={};yb(f.args[0],function(t){return function(u,v){sd(Mb(u,v),t.Lc)}}(e));var n=!!e.Lc[K.m.rd];delete e.Lc[K.m.rd];var p=g.destinationId===g.id;Co(e.Lc,!0);n||(p?m.C={}:m.U[g.id]={});m.R&&n||Hp(this,K.m.na,e.Lc,f);m.R=!0;p?sd(e.Lc,m.C):(sd(e.Lc,m.U[g.id]),O(70));d=!0;break;case "event":e.qh={};yb(f.args[0],function(t){return function(u,v){sd(Mb(u,v),t.qh)}}(e));Co(e.qh);Hp(this,f.args[1],e.qh,f);break;case "get":var q={},r=(q[K.m.jf]=f.args[0],q[K.m.hf]=f.args[1],q);Hp(this,K.m.nc,r,f);break;
case "container_config":e.Vi={},yb(f.args[0],function(t){return function(u,v){sd(Mb(u,v),t.Vi)}}(e)),sd(e.Vi,Bp(this,g).C)}this.commands.shift();Ip(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};
var Ip=function(a,b){if(b.type!=="require")if(b.C)for(var c=Bp(a,b.C).M[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.M)for(var g=f.M[b.type]||[],h=0;h<g.length;h++)g[h]()}},Dp=function(a,b,c){var d=sd(c,null);sd(Bp(a,b).H,d);Bp(a,b).H=d},xp=new Gp;function Jp(a){var b=a.location.href;if(a===a.top)return{url:b,Lq:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1],g;f&&((g=b)==null?void 0:g.indexOf(f))===-1&&(c=!1,b=f)}return{url:b,Lq:c}}function Kp(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Gk(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}}function Lp(){for(var a=w,b=a;a&&a!=a.parent;)a=a.parent,Kp(a)&&(b=a);return b};var Mp=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},Np=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var Op=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Pp=function(a){var b=w;if(b.top==b)return 0;if(a===void 0?0:a){var c=b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Kp(b.top)?1:2},Qp=function(a){a=a===void 0?document:a;return a.createElement("img")};function Rp(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Sp(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Tp(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)};function Up(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Qp(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=vc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Sp(e,"load",f);Sp(e,"error",f)};Rp(e,"load",f);Rp(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Vp(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Tp(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Wp(c,b)}
function Wp(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Up(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Xp=function(){this.ia=this.ia;this.R=this.R};Xp.prototype.ia=!1;Xp.prototype.dispose=function(){this.ia||(this.ia=!0,this.M())};Xp.prototype[ia.Symbol.dispose]=function(){this.dispose()};Xp.prototype.addOnDisposeCallback=function(a,b){this.ia?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};Xp.prototype.M=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function Yp(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Zp=function(a,b){b=b===void 0?{}:b;Xp.call(this);this.C=null;this.la={};this.Jc=0;this.U=null;this.H=a;var c;this.Xa=(c=b.timeoutMs)!=null?c:500;var d;this.Ja=(d=b.Gs)!=null?d:!1};xa(Zp,Xp);Zp.prototype.M=function(){this.la={};this.U&&(Sp(this.H,"message",this.U),delete this.U);delete this.la;delete this.H;delete this.C;Xp.prototype.M.call(this)};var aq=function(a){return typeof a.H.__tcfapi==="function"||$p(a)!=null};
Zp.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ja},d=Np(function(){return a(c)}),e=0;this.Xa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Xa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Yp(c),c.internalBlockOnErrors=b.Ja,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{bq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Zp.prototype.removeEventListener=function(a){a&&a.listenerId&&bq(this,"removeEventListener",null,a.listenerId)};
var dq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=cq(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&cq(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?cq(a.purpose.legitimateInterests,
b)&&cq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},cq=function(a,b){return!(!a||!a[b])},bq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if($p(a)){eq(a);var g=++a.Jc;a.la[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},$p=function(a){if(a.C)return a.C;a.C=Op(a.H,"__tcfapiLocator");return a.C},eq=function(a){if(!a.U){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.la[d.callId](d.returnValue,d.success)}catch(e){}};a.U=b;Rp(a.H,"message",b)}},fq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Yp(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Vp({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var gq={1:0,3:0,4:0,7:3,9:3,10:3};Ji(32,'');function hq(){return On("tcf",function(){return{}})}var iq=function(){return new Zp(w,{timeoutMs:-1})};
function jq(){var a=hq(),b=iq();aq(b)&&!kq()&&!lq()&&O(124);if(!a.active&&aq(b)){kq()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,hl().active=!0,a.tcString="tcunavailable");zn();try{b.addEventListener(function(c){if(c.internalErrorState!==0)mq(a),Hn([K.m.aa,K.m.La,K.m.W]),hl().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,lq()&&(a.active=!0),!nq(c)||kq()||lq()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in gq)gq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(nq(c)){var g={},h;for(h in gq)if(gq.hasOwnProperty(h))if(h==="1"){var m,n=c,p={kq:!0};p=p===void 0?{}:p;m=fq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.kq)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?dq(n,"1",0):!0:!1;g["1"]=m}else g[h]=dq(c,h,gq[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[K.m.aa]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(Hn([K.m.aa,K.m.La,K.m.W]),hl().active=!0):(r[K.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[K.m.W]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":Hn([K.m.W]),tn(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:oq()||""}))}}else Hn([K.m.aa,K.m.La,K.m.W])})}catch(c){mq(a),Hn([K.m.aa,K.m.La,K.m.W]),hl().active=!0}}}
function mq(a){a.type="e";a.tcString="tcunavailable"}function nq(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function kq(){return w.gtag_enable_tcf_support===!0}function lq(){return hq().enableAdvertiserConsentMode===!0}function oq(){var a=hq();if(a.active)return a.tcString}function pq(){var a=hq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function qq(a){if(!gq.hasOwnProperty(String(a)))return!0;var b=hq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var rq={vn:Ji(20,'5000'),wn:Ji(21,'5000'),On:Ji(15,''),Rn:Ji(14,'1000'),Uo:Ji(16,'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD'),Vo:Ji(17,'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD')},sq={Cp:Number(rq.vn)||-1,Dp:Number(rq.wn)||-1,Js:Number(rq.On)||0,Yp:Number(rq.Rn)||0,qq:rq.Uo.split("~"),rq:rq.Vo.split("~")};
ma(Object,"assign").call(Object,{},sq);var tq=[K.m.aa,K.m.ka,K.m.W,K.m.La],uq={},vq=(uq[K.m.aa]=1,uq[K.m.ka]=2,uq);function wq(a){if(a===void 0)return 0;switch(P(a,K.m.Pb)){case void 0:return 1;case !1:return 3;default:return 2}}function xq(){return(G(183)?sq.qq:sq.rq).indexOf(um())!==-1&&yc.globalPrivacyControl===!0}function yq(a){if(xq())return!1;var b=wq(a);if(b===3)return!1;switch(ql(K.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function zq(){return sl()||!pl(K.m.aa)||!pl(K.m.ka)}function Aq(){var a={},b;for(b in vq)vq.hasOwnProperty(b)&&(a[vq[b]]=ql(b));return"G1"+hf(a[1]||0)+hf(a[2]||0)}var Bq={},Cq=(Bq[K.m.aa]=0,Bq[K.m.ka]=1,Bq[K.m.W]=2,Bq[K.m.La]=3,Bq);function Dq(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Eq(a){for(var b="1",c=0;c<tq.length;c++){var d=b,e,f=tq[c],g=ol.delegatedConsentTypes[f];e=g===void 0?0:Cq.hasOwnProperty(g)?12|Cq[g]:8;var h=hl();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Dq(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Dq(m.declare)<<4|Dq(m.default)<<2|Dq(m.update)])}var n=b,p=(xq()?1:0)<<3,q=(sl()?1:0)<<2,r=wq(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[ol.containerScopedDefaults.ad_storage<<4|ol.containerScopedDefaults.analytics_storage<<2|ol.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(ol.usedContainerScopedDefaults?1:0)<<2|ol.containerScopedDefaults.ad_personalization]}
function Fq(){if(!pl(K.m.W))return"-";for(var a=Object.keys(Mm),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=ol.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Mm[m])}(ol.usedCorePlatformServices?ol.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Gq(){return wm()||(kq()||lq())&&pq()==="1"?"1":"0"}function Hq(){return(wm()?!0:!(!kq()&&!lq())&&pq()==="1")||!pl(K.m.W)}
function Iq(){var a="0",b="0",c;var d=hq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=hq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;wm()&&(h|=1);pq()==="1"&&(h|=2);kq()&&(h|=4);var m;var n=hq();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);hl().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Jq(){return um()==="US-CO"};var Kq;function Lq(){if(Bc===null)return 0;var a=gd();if(!a)return 0;var b=a.getEntriesByName(Bc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Mq={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Nq(a){a=a===void 0?{}:a;var b=Fi(5).split("-")[0].toUpperCase(),c,d={ctid:Fi(5),gn:Ii(15),kn:Fi(14),Nq:Ei(7)?2:1,yr:a.mn,canonicalId:Fi(6),lr:(c=Kj())==null?void 0:c.canonicalContainerId,zr:a.Pd===void 0?void 0:a.Pd?10:12};if(G(204)){var e;d.Lp=(e=Kq)!=null?e:Kq=Lq()}d.canonicalId!==a.Na&&(d.Na=a.Na);var f=Hj();d.Tq=f?f.canonicalContainerId:void 0;Xi?(d.Fh=Mq[b],d.Fh||(d.Fh=0)):d.Fh=Yi?13:10;Hi.H?(d.Qm=0,d.Fp=2):d.Qm=Hi.C?1:3;var g={6:!1};Hi.M===2?g[7]=!0:Hi.M===1&&(g[2]=!0);if(Bc){var h=
mj(sj(Bc),"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}d.Mp=g;return lf(d,a.oh)};function Oq(a,b,c,d){var e,f=Number(a.Pc!=null?a.Pc:void 0);f!==0&&(e=new Date((b||Fb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,wc:d}};var Pq=["ad_storage","ad_user_data"];function Qq(a,b){if(!a)return jb("TAGGING",32),10;if(b===null||b===void 0||b==="")return jb("TAGGING",33),11;var c=Rq(!1);if(c.error!==0)return jb("TAGGING",34),c.error;if(!c.value)return jb("TAGGING",35),2;c.value[a]=b;var d=Sq(c);d!==0&&jb("TAGGING",36);return d}
function Tq(a){if(!a)return jb("TAGGING",27),{error:10};var b=Rq();if(b.error!==0)return jb("TAGGING",29),b;if(!b.value)return jb("TAGGING",30),{error:2};if(!(a in b.value))return jb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(jb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Rq(a){a=a===void 0?!0:a;if(!pl(Pq))return jb("TAGGING",43),{error:3};try{if(!w.localStorage)return jb("TAGGING",44),{error:1}}catch(f){return jb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=w.localStorage.getItem("_gcl_ls")}catch(f){return jb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return jb("TAGGING",47),{error:12}}}catch(f){return jb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return jb("TAGGING",49),{error:4};
if(b.version!==1)return jb("TAGGING",50),{error:5};try{var e=Uq(b);a&&e&&Sq({value:b,error:0})}catch(f){return jb("TAGGING",48),{error:8}}return{value:b,error:0}}
function Uq(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,jb("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Uq(a[e.value])||c;return c}return!1}
function Sq(a){if(a.error)return a.error;if(!a.value)return jb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return jb("TAGGING",52),6}try{w.localStorage.setItem("_gcl_ls",c)}catch(d){return jb("TAGGING",53),7}return 0};var Vq={lg:"value",ib:"conversionCount",Ah:1},Wq={xh:9,Eh:10,lg:"timeouts",ib:"timeouts",Ah:0},Xq=[Vq,Wq,{xh:11,Eh:12,lg:"eopCount",ib:"endOfPageCount",Ah:0},{xh:11,Eh:12,lg:"errors",ib:"errors",Ah:0}];function Yq(a){var b;b=b===void 0?1:b;if(!Zq(a))return{};var c=$q(Xq),d=c[a.ib];if(d===void 0||d===-1)return c;var e={},f=ma(Object,"assign").call(Object,{},c,(e[a.ib]=d+b,e));return ar(f)?f:c}
function $q(a){var b;a:{var c=Tq("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&Zq(m)){var n=e[m.lg];n===void 0||Number.isNaN(n)?f[m.ib]=-1:f[m.ib]=Number(n)}else f[m.ib]=-1}return f}
function br(){for(var a=Yq(Vq),b=[],c=l(Xq),d=c.next();!d.done;d=c.next()){var e=d.value,f=a[e.ib];if(f===void 0||f<e.Ah)break;b.push(f.toString())}return b.join("~")}function ar(a,b){b=b||{};for(var c=Fb(),d=Oq(b,c,!0),e={},f=l(Xq),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.ib];m!==void 0&&m!==-1&&(e[h.lg]=m)}e.creationTimeMs=c;return Qq("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function Zq(a){return pl(["ad_storage","ad_user_data"])?!a.Eh||Wa(a.Eh):!1}
function cr(a){return pl(["ad_storage","ad_user_data"])?!a.xh||Wa(a.xh):!1};function dr(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var er={O:{lp:0,Kj:1,vg:2,Xj:3,Jh:4,Vj:5,Wj:6,Yj:7,Kh:8,vl:9,tl:10,ni:11,wl:12,Vg:13,Fl:14,Nf:15,kp:16,ze:17,Oi:18,Pi:19,Qi:20,wm:21,Ri:22,Mh:23,jk:24}};er.O[er.O.lp]="RESERVED_ZERO";er.O[er.O.Kj]="ADS_CONVERSION_HIT";er.O[er.O.vg]="CONTAINER_EXECUTE_START";er.O[er.O.Xj]="CONTAINER_SETUP_END";er.O[er.O.Jh]="CONTAINER_SETUP_START";er.O[er.O.Vj]="CONTAINER_BLOCKING_END";er.O[er.O.Wj]="CONTAINER_EXECUTE_END";er.O[er.O.Yj]="CONTAINER_YIELD_END";er.O[er.O.Kh]="CONTAINER_YIELD_START";er.O[er.O.vl]="EVENT_EXECUTE_END";
er.O[er.O.tl]="EVENT_EVALUATION_END";er.O[er.O.ni]="EVENT_EVALUATION_START";er.O[er.O.wl]="EVENT_SETUP_END";er.O[er.O.Vg]="EVENT_SETUP_START";er.O[er.O.Fl]="GA4_CONVERSION_HIT";er.O[er.O.Nf]="PAGE_LOAD";er.O[er.O.kp]="PAGEVIEW";er.O[er.O.ze]="SNIPPET_LOAD";er.O[er.O.Oi]="TAG_CALLBACK_ERROR";er.O[er.O.Pi]="TAG_CALLBACK_FAILURE";er.O[er.O.Qi]="TAG_CALLBACK_SUCCESS";er.O[er.O.wm]="TAG_EXECUTE_END";er.O[er.O.Ri]="TAG_EXECUTE_START";er.O[er.O.Mh]="CUSTOM_PERFORMANCE_START";er.O[er.O.jk]="CUSTOM_PERFORMANCE_END";var fr=[],gr={},hr={};function ir(a){if(Wa(19)&&fr.includes(a)){var b;(b=gd())==null||b.mark(a+"-"+er.O.Mh+"-"+(hr[a]||0))}}function jr(a){if(Wa(19)&&fr.includes(a)){var b=a+"-"+er.O.jk+"-"+(hr[a]||0),c={start:a+"-"+er.O.Mh+"-"+(hr[a]||0),end:b},d;(d=gd())==null||d.mark(b);var e,f,g=(f=(e=gd())==null?void 0:e.measure(b,c))==null?void 0:f.duration;g!==void 0&&(hr[a]=(hr[a]||0)+1,gr[a]=g+(gr[a]||0))}};var kr=["2","3"];function lr(a){return a.origin!=="null"};var mr;function nr(a,b){var c=Ml(Hl.X.Di,Oa()).get(a);if(c&&(!c.expires||(typeof c.expires==="string"?(new Date(c.expires)).getTime():c.expires.getTime())>=Date.now())&&c.value!==void 0)return b?decodeURIComponent(c.value):c.value}function or(a,b,c){var d=Ml(Hl.X.Di,Oa());d.set(a,{expires:c,value:b});Kl(Hl.X.Di,d)}var pr=0,qr=0;
function rr(a,b,c,d,e){try{ir("3");var f;Wa(20)&&!e&&(f=nr(a,c));var g,h=(g=sr(function(m){return m===a},b,c,d)[a])!=null?g:[];f!==void 0&&(h.includes(f)?qr++:pr++);return h}finally{jr("3")}}function sr(a,b,c,d){var e;if(tr(d)){for(var f={},g=String(b||ur()).split(";"),h=0;h<g.length;h++){var m=g[h].split("="),n=m[0].trim();if(n&&a(n)){var p=m.slice(1).join("=").trim();p&&c&&(p=decodeURIComponent(p));var q=void 0,r=void 0;((q=f)[r=n]||(q[r]=[])).push(p)}}e=f}else e={};return e}
function vr(a,b,c,d,e){if(tr(e)){var f=wr(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=xr(f,function(g){return g.Wp},b);if(f.length===1)return f[0];f=xr(f,function(g){return g.Vq},c);return f[0]}}}function yr(a,b,c,d){var e=ur(),f=window;lr(f)&&(f.document.cookie=a);var g=ur();return e!==g||c!==void 0&&rr(b,g,!1,d,!0).indexOf(c)>=0}
function zr(a,b,c,d){function e(x,y,A){if(A==null)return delete h[y],x;h[y]=A;return x+"; "+y+"="+A}function f(x,y){if(y==null)return x;h[y]=!0;return x+"; "+y}if(!tr(c.wc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Ar(b),g=a+"="+b);var h={};Wa(20)&&or(a,b,c.uj?new Date(Date.now()+Number(c.uj)*1E3):c.expires);g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,
"expires",m);g=e(g,"max-age",c.uj);g=e(g,"samesite",c.mr);c.secure&&(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Br(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(x){q=x;continue}r=!0;if(!Cr(u,c.path)&&yr(v,a,b,c.wc))return Wa(16)&&(mr=u),0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Cr(n,c.path)?1:yr(g,a,b,c.wc)?0:1}
function Dr(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");ir("2");var d=zr(a,b,c);jr("2");return d}function xr(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}
function wr(a,b,c){for(var d=[],e=rr(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Op:e[f],Pp:g.join("."),Wp:Number(n[0])||1,Vq:Number(n[1])||1})}}}return d}function Ar(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}var Er=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Fr=/(^|\.)doubleclick\.net$/i;
function Cr(a,b){return a!==void 0&&(Fr.test(window.document.location.hostname)||b==="/"&&Er.test(a))}function Gr(a){if(!a)return 1;var b=a;Wa(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Hr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}function Ir(a,b){var c=""+Gr(a),d=Hr(b);d>1&&(c+="-"+d);return c}
var ur=function(){return lr(window)?window.document.cookie:""},tr=function(a){return a&&Wa(7)?(Array.isArray(a)?a:[a]).every(function(b){return rl(b)&&pl(b)}):!0},Br=function(){var a=mr,b=[];a&&b.push(a);var c=window.document.location.hostname.split(".");if(c.length===4){var d=c[c.length-1];if(Number(d).toString()===d)return["none"]}for(var e=c.length-2;e>=0;e--){var f=c.slice(e).join(".");f!==a&&b.push(f)}var g=window.document.location.hostname;Fr.test(g)||Er.test(g)||b.push("none");return b};function Jr(a){var b=Math.round(Math.random()*2147483647);return a?String(b^dr(a)&2147483647):String(b)}function Kr(a){return[Jr(a),Math.round(Fb()/1E3)].join(".")}function Lr(a,b,c,d,e){var f=Gr(b),g;return(g=vr(a,f,Hr(c),d,e))==null?void 0:g.Pp};var Mr;function Nr(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Or,d=Pr,e=Qr();if(!e.init){Pc(z,"mousedown",a);Pc(z,"keyup",a);Pc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Rr(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Qr().decorators.push(f)}
function Sr(a,b,c){for(var d=Qr().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==z.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Ib(e,g.callback())}}return e}
function Qr(){var a=Cc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Tr=/(.*?)\*(.*?)\*(.*)/,Ur=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Vr=/^(?:www\.|m\.|amp\.)+/,Wr=/([^?#]+)(\?[^#]*)?(#.*)?/;function Xr(a){var b=Wr.exec(a);if(b)return{yj:b[1],query:b[2],fragment:b[3]}}function Yr(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Zr(a,b){var c=[yc.userAgent,(new Date).getTimezoneOffset(),yc.userLanguage||yc.language,Math.floor(Fb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Mr)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Mr=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Mr[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function $r(a){return function(b){var c=sj(w.location.href),d=c.search.replace("?",""),e=jj(d,"_gl",!1,!0)||"";b.query=as(e)||{};var f=mj(c,"fragment"),g;var h=-1;if(Kb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=as(g||"")||{};a&&bs(c,d,f)}}function cs(a,b){var c=Yr(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function bs(a,b,c){function d(g,h){var m=cs("_gl",g);m.length&&(m=h+m);return m}if(xc&&xc.replaceState){var e=Yr("_gl");if(e.test(b)||e.test(c)){var f=mj(a,"path");b=d(b,"?");c=d(c,"#");xc.replaceState({},"",""+f+b+c)}}}function ds(a,b){var c=$r(!!b),d=Qr();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Ib(e,f.query),a&&Ib(e,f.fragment));return e}
var as=function(a){try{var b=es(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=hb(d[e+1]);c[f]=g}jb("TAGGING",6);return c}}catch(h){jb("TAGGING",8)}};function es(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Tr.exec(d);if(f){c=f;break a}d=lj(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Zr(h,p)){m=!0;break a}m=!1}if(m)return h;jb("TAGGING",7)}}}
function fs(a,b,c,d,e){function f(p){p=cs(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Xr(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.yj+h+m}
function gs(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],x;for(x in n)if(n.hasOwnProperty(x)){var y=n[x];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(x),v.push(gb(String(y))))}var A=v.join("*");u=["1",Zr(A),A].join("*");d?(Wa(3)||Wa(1)||!p)&&hs("_gl",u,a,p,q):is("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Sr(b,1,d),f=Sr(b,2,d),g=Sr(b,4,d),h=Sr(b,3,d);c(e,!1,!1);c(f,!0,!1);Wa(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
js(m,h[m],a)}function js(a,b,c){c.tagName.toLowerCase()==="a"?is(a,b,c):c.tagName.toLowerCase()==="form"&&hs(a,b,c)}function is(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Wa(4)||d)){var h=w.location.href,m=Xr(c.href),n=Xr(h);g=!(m&&n&&m.yj===n.yj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=fs(a,b,c.href,d,e);nc.test(p)&&(c.href=p)}}
function hs(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=fs(a,b,f,d,e);nc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Or(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||gs(e,e.hostname)}}catch(g){}}function Pr(a){try{var b=a.getAttribute("action");if(b){var c=mj(sj(b),"host");gs(a,c)}}catch(d){}}function ks(a,b,c,d){Nr();var e=c==="fragment"?2:1;d=!!d;Rr(a,b,e,d,!1);e===2&&jb("TAGGING",23);d&&jb("TAGGING",24)}
function ls(a,b){Nr();Rr(a,[oj(w.location,"host",!0)],b,!0,!0)}function ms(){var a=z.location.hostname,b=Ur.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?lj(f[2])||"":lj(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Vr,""),m=e.replace(Vr,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ns(a,b){return a===!1?!1:a||b||ms()};var os=["1"],ps={},qs={};function rs(a,b){b=b===void 0?!0:b;var c=ss(a.prefix);if(ps[c])ts(a);else if(us(c,a.path,a.domain)){var d=qs[ss(a.prefix)]||{id:void 0,zh:void 0};b&&vs(a,d.id,d.zh);ts(a)}else{var e=uj("auiddc");if(e)jb("TAGGING",17),ps[c]=e;else if(b){var f=ss(a.prefix),g=Kr();ws(f,g,a);us(c,a.path,a.domain);ts(a,!0)}}}
function ts(a,b){if((b===void 0?0:b)&&Zq(Vq)){var c=Rq(!1);c.error!==0?jb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Sq(c)!==0&&jb("TAGGING",41)):jb("TAGGING",40):jb("TAGGING",39)}if(cr(Vq)&&$q([Vq])[Vq.ib]===-1){for(var d={},e=(d[Vq.ib]=0,d),f=l(Xq),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Vq&&cr(h)&&(e[h.ib]=0)}ar(e,a)}}
function vs(a,b,c){var d=ss(a.prefix),e=ps[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Fb()/1E3)));ws(d,h,a,g*1E3)}}}}function ws(a,b,c,d){var e;e=["1",Ir(c.domain,c.path),b].join(".");var f=Oq(c,d);f.wc=xs();Dr(a,e,f)}function us(a,b,c){var d=Lr(a,b,c,os,xs());if(!d)return!1;ys(a,d);return!0}
function ys(a,b){var c=b.split(".");c.length===5?(ps[a]=c.slice(0,2).join("."),qs[a]={id:c.slice(2,4).join("."),zh:Number(c[4])||0}):c.length===3?qs[a]={id:c.slice(0,2).join("."),zh:Number(c[2])||0}:ps[a]=b}function ss(a){return(a||"_gcl")+"_au"}function zs(a){function b(){pl(c)&&a()}var c=xs();vl(function(){b();pl(c)||wl(b,c)},c)}
function As(a){var b=ds(!0),c=ss(a.prefix);zs(function(){var d=b[c];if(d){ys(c,d);var e=Number(ps[c].split(".")[1])*1E3;if(e){jb("TAGGING",16);var f=Oq(a,e);f.wc=xs();var g=["1",Ir(a.domain,a.path),d].join(".");Dr(c,g,f)}}})}function Bs(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Lr(a,e.path,e.domain,os,xs());h&&(g[a]=h);return g};zs(function(){ks(f,b,c,d)})}function xs(){return["ad_storage","ad_user_data"]};function Cs(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Ij:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Ds(a,b){var c=Cs(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Ij]||(d[c[e].Ij]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Ij].push(g)}}return d};var Es={},Fs=(Es.k={fa:/^[\w-]+$/},Es.b={fa:/^[\w-]+$/,Bj:!0},Es.i={fa:/^[1-9]\d*$/},Es.h={fa:/^\d+$/},Es.t={fa:/^[1-9]\d*$/},Es.d={fa:/^[A-Za-z0-9_-]+$/},Es.j={fa:/^\d+$/},Es.u={fa:/^[1-9]\d*$/},Es.l={fa:/^[01]$/},Es.o={fa:/^[1-9]\d*$/},Es.g={fa:/^[01]$/},Es.s={fa:/^.+$/},Es);var Gs={},Ks=(Gs[5]={Gh:{2:Hs},rj:"2",ph:["k","i","b","u"]},Gs[4]={Gh:{2:Hs,GCL:Is},rj:"2",ph:["k","i","b"]},Gs[2]={Gh:{GS2:Hs,GS1:Js},rj:"GS2",ph:"sogtjlhd".split("")},Gs);function Ls(a,b,c){var d=Ks[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Gh[e];if(f)return f(a,b)}}}
function Hs(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Ks[b];if(f){for(var g=f.ph,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Fs[p];r&&(r.Bj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Ms(a,b,c){var d=Ks[b];if(d)return[d.rj,c||"1",Ns(a,b)].join(".")}
function Ns(a,b){var c=Ks[b];if(c){for(var d=[],e=l(c.ph),f=e.next();!f.done;f=e.next()){var g=f.value,h=Fs[g];if(h){var m=a[g];if(m!==void 0)if(h.Bj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Is(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Js(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Os=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Ps(a,b,c){if(Ks[b]){for(var d=[],e=rr(a,void 0,void 0,Os.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Ls(g.value,b,c);h&&d.push(Qs(h))}return d}}
function Rs(a){var b=Ss;if(Ks[2]){for(var c={},d=sr(a,void 0,void 0,Os.get(2)),e=Object.keys(d).sort(),f=l(e),g=f.next();!g.done;g=f.next())for(var h=g.value,m=l(d[h]),n=m.next();!n.done;n=m.next()){var p=Ls(n.value,2,b);p&&(c[h]||(c[h]=[]),c[h].push(Qs(p)))}return c}}function Ts(a,b,c,d,e){d=d||{};var f=Ir(d.domain,d.path),g=Ms(b,c,f);if(!g)return 1;var h=Oq(d,e,void 0,Os.get(c));return Dr(a,g,h)}function Us(a,b){var c=b.fa;return typeof c==="function"?c(a):c.test(a)}
function Qs(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Uf:void 0},c=b.next()){var e=c.value,f=a[e];d.Uf=Fs[e];d.Uf?d.Uf.Bj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Us(h,g.Uf)}}(d)):void 0:typeof f==="string"&&Us(f,d.Uf)||(a[e]=void 0):a[e]=void 0}return a};var Vs=function(){this.value=0};Vs.prototype.set=function(a){return this.value|=1<<a};var Ws=function(a,b){b<=0||(a.value|=1<<b-1)};Vs.prototype.get=function(){return this.value};Vs.prototype.clear=function(a){this.value&=~(1<<a)};Vs.prototype.clearAll=function(){this.value=0};Vs.prototype.equals=function(a){return this.value===a.value};function Xs(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Ys(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function Zs(){var a=String,b=w.location.hostname,c=w.location.pathname,d=b=Sb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Sb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(dr((""+b+e).toLowerCase()))};var $s={},at=($s.gclid=!0,$s.dclid=!0,$s.gbraid=!0,$s.wbraid=!0,$s),bt=/^\w+$/,ct=/^[\w-]+$/,dt={},et=(dt.aw="_aw",dt.dc="_dc",dt.gf="_gf",dt.gp="_gp",dt.gs="_gs",dt.ha="_ha",dt.ag="_ag",dt.gb="_gb",dt),ft=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,gt=/^www\.googleadservices\.com$/;function ht(){return["ad_storage","ad_user_data"]}function it(a){return!Wa(7)||pl(a)}function jt(a,b){function c(){var d=it(b);d&&a();return d}vl(function(){c()||wl(c,b)},b)}
function kt(a){return lt(a).map(function(b){return b.gclid})}function mt(a){return nt(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function nt(a){var b=ot(a.prefix),c=pt("gb",b),d=pt("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=lt(c).map(e("gb")),g=qt(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function rt(a,b,c,d,e){var f=ub(a,function(g){return g.gclid===b});f?(f.timestamp<c&&(f.timestamp=c,f.Oc=e),f.labels=st(f.labels||[],d||[])):a.push({version:"2",gclid:b,timestamp:c,labels:d,Oc:e})}function qt(a){for(var b=Ps(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=tt(f);h&&rt(c,g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function lt(a){for(var b=[],c=rr(a,z.cookie,void 0,ht()),d=l(c),e=d.next();!e.done;e=d.next()){var f=ut(e.value);f!=null&&(f.Oc=void 0,f.za=new Vs,f.ab=[1],vt(b,f))}b.sort(function(g,h){return h.timestamp-g.timestamp});return wt(b)}function xt(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function vt(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.za&&b.za&&h.za.equals(b.za)&&(e=h)}if(d){var m,n,p=(m=d.za)!=null?m:new Vs,q=(n=b.za)!=null?n:new Vs;p.value|=q.value;d.za=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Oc=b.Oc);d.labels=xt(d.labels||[],b.labels||[]);d.ab=xt(d.ab||[],b.ab||[])}else c&&e?ma(Object,"assign").call(Object,e,b):a.push(b)}
function zt(a){if(!a)return new Vs;var b=new Vs;if(a===1)return Ws(b,2),Ws(b,3),b;Ws(b,a);return b}
function At(){var a=Tq("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(ct))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Vs;typeof e==="number"?g=zt(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],za:g,ab:[2]}}catch(h){return null}}
function Bt(){var a=Tq("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(ct))return b;var f=new Vs,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],za:f,ab:[2]});return b},[])}catch(b){return null}}
function Ct(a){for(var b=[],c=rr(a,z.cookie,void 0,ht()),d=l(c),e=d.next();!e.done;e=d.next()){var f=ut(e.value);f!=null&&(f.Oc=void 0,f.za=new Vs,f.ab=[1],vt(b,f))}var g=At();g&&(g.Oc=void 0,g.ab=g.ab||[2],vt(b,g));if(Wa(14)){var h=Bt();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Oc=void 0;p.ab=p.ab||[2];vt(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return wt(b)}
function st(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function ot(a){return a&&typeof a==="string"&&a.match(bt)?a:"_gcl"}function Dt(a,b){if(a){var c={value:a,za:new Vs};Ws(c.za,b);return c}}
function Et(a,b,c){var d=sj(a),e=mj(d,"query",!1,void 0,"gclsrc"),f=Dt(mj(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=Dt(jj(g,"gclid",!1),3));e||(e=jj(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Ft(a,b){var c=sj(a),d=mj(c,"query",!1,void 0,"gclid"),e=mj(c,"query",!1,void 0,"gclsrc"),f=mj(c,"query",!1,void 0,"wbraid");f=Qb(f);var g=mj(c,"query",!1,void 0,"gbraid"),h=mj(c,"query",!1,void 0,"gad_source"),m=mj(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||jj(n,"gclid",!1);e=e||jj(n,"gclsrc",!1);f=f||jj(n,"wbraid",!1);g=g||jj(n,"gbraid",!1);h=h||jj(n,"gad_source",!1)}return Gt(d,e,m,f,g,h)}function Ht(){return Ft(w.location.href,!0)}
function Gt(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(ct))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&ct.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&ct.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&ct.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function It(a){for(var b=Ht(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Ft(w.document.referrer,!1),b.gad_source=void 0);Jt(b,!1,a)}
function Kt(a){It(a);var b=Et(w.location.href,!0,!1);b.length||(b=Et(w.document.referrer,!1,!0));a=a||{};Lt(a);if(b.length){var c=b[0],d=Fb(),e=Oq(a,d,!0),f=ht(),g=function(){it(f)&&e.expires!==void 0&&Qq("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.za.get()},expires:Number(e.expires)})};vl(function(){g();it(f)||wl(g,f)},f)}}
function Lt(a){var b;if(b=Wa(15)){var c=Mt();b=ft.test(c)||gt.test(c)||Nt()}if(b){var d;a:{for(var e=sj(w.location.href),f=kj(mj(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!at[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Xs(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(var v=10;u<t.length&&!(v--<=0);){var x=Ys(t,u);if(x===void 0)break;var y=l(x),A=y.next().value,D=y.next().value,E=A,L=D,F=E&7;if(E>>3===16382){if(F!==0)break;
var M=Ys(t,L);if(M===void 0)break;r=l(M).next().value===1;break c}var U;d:{var fa=void 0,S=t,Z=L;switch(F){case 0:U=(fa=Ys(S,Z))==null?void 0:fa[1];break d;case 1:U=Z+8;break d;case 2:var ra=Ys(S,Z);if(ra===void 0)break;var ka=l(ra),da=ka.next().value;U=ka.next().value+da;break d;case 5:U=Z+4;break d}U=void 0}if(U===void 0||U>t.length||U<=u)break;u=U}}catch(ja){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var Y=d;Y&&Ot(Y,7,a)}}
function Ot(a,b,c){c=c||{};var d=Fb(),e=Oq(c,d,!0),f=ht(),g=function(){if(it(f)&&e.expires!==void 0){var h=Bt()||[];vt(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),za:zt(b)},!0);Qq("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.za?m.za.get():0},expires:Number(m.expires)}}))}};vl(function(){it(f)?g():wl(g,f)},f)}
function Jt(a,b,c,d,e){c=c||{};e=e||[];var f=ot(c.prefix),g=d||Fb(),h=Math.round(g/1E3),m=ht(),n=!1,p=!1,q=function(){if(it(m)){var r=Oq(c,g,!0);r.wc=m;for(var t=function(U,fa){var S=pt(U,f);S&&(Dr(S,fa,r),U!=="gb"&&(n=!0))},u=function(U){var fa=["GCL",h,U];e.length>0&&fa.push(e.join("."));return fa.join(".")},v=l(["aw","dc","gf","ha","gp"]),x=v.next();!x.done;x=v.next()){var y=x.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],D=pt("gb",f);!b&&lt(D).some(function(U){return U.gclid===A&&U.labels&&
U.labels.length>0})||t("gb",u(A))}}if(!p&&a.gbraid&&it("ad_storage")&&(p=!0,!n)){var E=a.gbraid,L=pt("ag",f);if(b||!qt(L).some(function(U){return U.gclid===E&&U.labels&&U.labels.length>0})){var F={},M=(F.k=E,F.i=""+h,F.b=e,F);Ts(L,M,5,c,g)}}Pt(a,f,g,c)};vl(function(){q();it(m)||wl(q,m)},m)}
function Pt(a,b,c,d){if(a.gad_source!==void 0&&it("ad_storage")){var e=fd();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=pt("gs",b);if(g){var h=Math.floor((Fb()-(ed()||0))/1E3),m,n=Zs(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);Ts(g,m,5,d,c)}}}}
function Qt(a,b){var c=ds(!0);jt(function(){for(var d=ot(b.prefix),e=0;e<a.length;++e){var f=a[e];if(et[f]!==void 0){var g=pt(f,d),h=c[g];if(h){var m=Math.min(Rt(h),Fb()),n;b:{for(var p=m,q=rr(g,z.cookie,void 0,ht()),r=0;r<q.length;++r)if(Rt(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Oq(b,m,!0);t.wc=ht();Dr(g,h,t)}}}}Jt(Gt(c.gclid,c.gclsrc),!1,b)},ht())}
function St(a){var b=["ag"],c=ds(!0),d=ot(a.prefix);jt(function(){for(var e=0;e<b.length;++e){var f=pt(b[e],d);if(f){var g=c[f];if(g){var h=Ls(g,5);if(h){var m=tt(h);m||(m=Fb());var n;a:{for(var p=m,q=Ps(f,5),r=0;r<q.length;++r)if(tt(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Ts(f,h,5,a,m)}}}}},["ad_storage"])}function pt(a,b){var c=et[a];if(c!==void 0)return b+c}function Rt(a){return Tt(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function tt(a){return a?(Number(a.i)||0)*1E3:0}function ut(a){var b=Tt(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Tt(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!ct.test(a[2])?[]:a}
function Ut(a,b,c,d,e){if(Array.isArray(b)&&lr(w)){var f=ot(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=pt(a[m],f);if(n){var p=rr(n,z.cookie,void 0,ht());p.length&&(h[n]=p.sort()[p.length-1])}}return h};jt(function(){ks(g,b,c,d)},ht())}}
function Vt(a,b,c,d){if(Array.isArray(a)&&lr(w)){var e=["ag"],f=ot(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=pt(e[m],f);if(!n)return{};var p=Ps(n,5);if(p.length){var q=p.sort(function(r,t){return tt(t)-tt(r)})[0];h[n]=Ms(q,5)}}return h};jt(function(){ks(g,a,b,c)},["ad_storage"])}}function wt(a){return a.filter(function(b){return ct.test(b.gclid)})}
function Wt(a,b){if(lr(w)){for(var c=ot(b.prefix),d={},e=0;e<a.length;e++)et[a[e]]&&(d[a[e]]=et[a[e]]);jt(function(){yb(d,function(f,g){var h=rr(c+g,z.cookie,void 0,ht());h.sort(function(t,u){return Rt(u)-Rt(t)});if(h.length){var m=h[0],n=Rt(m),p=Tt(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Tt(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Jt(q,!0,b,n,p)}})},ht())}}
function Xt(a){var b=["ag"],c=["gbraid"];jt(function(){for(var d=ot(a.prefix),e=0;e<b.length;++e){var f=pt(b[e],d);if(!f)break;var g=Ps(f,5);if(g.length){var h=g.sort(function(q,r){return tt(r)-tt(q)})[0],m=tt(h),n=h.b,p={};p[c[e]]=h.k;Jt(p,!0,a,m,n)}}},["ad_storage"])}function Yt(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Zt(a){function b(h,m,n){n&&(h[m]=n)}if(sl()){var c=Ht(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:ds(!1)._gs);if(Yt(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ls(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ls(function(){return g},1)}}}function Nt(){var a=sj(w.location.href);return mj(a,"query",!1,void 0,"gad_source")}
function $t(a){if(!Wa(1))return null;var b=ds(!0).gad_source;if(b!=null)return w.location.hash="",b;if(Wa(2)){b=Nt();if(b!=null)return b;var c=Ht();if(Yt(c,a))return"0"}return null}function au(a){var b=$t(a);b!=null&&ls(function(){var c={};return c.gad_source=b,c},4)}function bu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function cu(a,b,c,d){var e=[];c=c||{};if(!it(ht()))return e;var f=lt(a),g=bu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Oq(c,p,!0);r.wc=ht();Dr(a,q,r)}return e}
function du(a,b){var c=[];b=b||{};var d=nt(b),e=bu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=ot(b.prefix),n=pt(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var x={},y=(x.k=r,x.i=""+v,x.b=(t||[]).concat([a]),x);Ts(n,y,5,b,u)}else if(h.type==="gb"){var A=[q,v,r].concat(t||[],[a]).join("."),D=Oq(b,u,!0);D.wc=ht();Dr(n,A,D)}}return c}
function eu(a,b){var c=ot(b),d=pt(a,c);if(!d)return 0;var e;e=a==="ag"?qt(d):lt(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function fu(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function gu(a){var b=Math.max(eu("aw",a),fu(it(ht())?Ds():{})),c=Math.max(eu("gb",a),fu(it(ht())?Ds("_gac_gb",!0):{}));c=Math.max(c,eu("ag",a));return c>b}
function Mt(){return z.referrer?mj(sj(z.referrer),"host"):""};
var hu=function(a,b){b=b===void 0?!1:b;var c=On("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},iu=function(a){return tj(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},ou=function(a,b,c,d,e){var f=ot(a.prefix);if(hu(f,!0)){var g=Ht(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=ju(),r=q.Ce,t=q.Mm;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,Zb:p});n&&h.push({gclid:n,Zb:"ds"});h.length===2&&O(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,Zb:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",Zb:"aw.ds"});ku(function(){var u=vn(lu());if(u){rs(a);var v=[],x=u?ps[ss(a.prefix)]:void 0;x&&v.push("auid="+x);if(vn(K.m.W)){e&&v.push("userId="+e);var y=Ll(Hl.X.lm);if(y===void 0)Kl(Hl.X.om,!0);else{var A=Ll(Hl.X.kh);v.push("ga_uid="+A+"."+y)}}var D=Mt(),E=u||!d?h:[];E.length===0&&(ft.test(D)||gt.test(D))&&E.push({gclid:"",Zb:""});if(E.length!==0||r!==void 0){D&&v.push("ref="+encodeURIComponent(D));var L=mu();v.push("url="+
encodeURIComponent(L));v.push("tft="+Fb());var F=ed();F!==void 0&&v.push("tfd="+Math.round(F));var M=Pp(!0);v.push("frm="+M);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var U={};c=So(Io(new Ho(0),(U[K.m.Pb]=xp.C[K.m.Pb],U)))}v.push("gtm="+Nq({Na:b}));zq()&&v.push("gcs="+Aq());v.push("gcd="+Eq(c));Hq()&&v.push("dma_cps="+Fq());v.push("dma="+Gq());yq(c)?v.push("npa=0"):v.push("npa=1");Jq()&&v.push("_ng=1");aq(iq())&&
v.push("tcfd="+Iq());var fa=pq();fa&&v.push("gdpr="+fa);var S=oq();S&&v.push("gdpr_consent="+S);G(23)&&v.push("apve=0");G(123)&&ds(!1)._up&&v.push("gtm_up=1");var Z=Vj();Z&&v.push("tag_exp="+Z);if(E.length>0)for(var ra=0;ra<E.length;ra++){var ka=E[ra],da=ka.gclid,Y=ka.Zb;if(!nu(a.prefix,Y+"."+da,x!==void 0)){var ja=Fi(36)+"?"+v.join("&");da!==""?ja=Y==="gb"?ja+"&wbraid="+da:ja+"&gclid="+da+"&gclsrc="+Y:Y==="aw.ds"&&(ja+="&gclsrc=aw.ds");Wc(ja)}}else if(r!==void 0&&!nu(a.prefix,"gad",x!==void 0)){var za=
Fi(36)+"?"+v.join("&");Wc(za)}}}})}},nu=function(a,b,c){var d=On("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},ju=function(){var a=sj(w.location.href),b=void 0,c=void 0,d=mj(a,"query",!1,void 0,"gad_source"),e=mj(a,"query",!1,void 0,"gad_campaignid"),f,g=a.hash.replace("#","").match(pu);f=g?g[1]:void 0;d&&f?(b=d,c=1):d?(b=d,c=2):f&&(b=f,c=3);return{Ce:b,Mm:c,rh:e}},mu=function(){var a=Pp(!1)===1?w.top.location.href:w.location.href;return a=a.replace(/[\?#].*$/,
"")},qu=function(a){var b=[];yb(a,function(c,d){d=wt(d);for(var e=[],f=0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},ru=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=uj("gcl"+a);if(d)return d.split(".")}var e=ot(b);if(e==="_gcl"){var f=!vn(lu())&&c,g;g=Ht()[a]||[];if(g.length>0)return f?["0"]:g}var h=pt(a,e);return h?kt(h):[]},ku=function(a){var b=lu();yn(function(){a();vn(b)||wl(a,b)},b)},lu=function(){return[K.m.aa,K.m.W]},pu=/^gad_source[_=](\d+)$/;
function su(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function tu(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function uu(){return["ad_storage","ad_user_data"]}function vu(a){if(G(38)&&!Ll(Hl.X.Zl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{su(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Kl(Hl.X.Zl,function(d){d.gclid&&Ot(d.gclid,5,a)}),tu(c)||O(178))})}catch(c){O(177)}};vl(function(){it(uu())?b():wl(b,uu())},uu())}};var wu=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function xu(a){return a.data.action!=="gcl_transfer"?(O(173),!0):a.data.gadSource?a.data.gclid?!1:(O(181),!0):(O(180),!0)}
function yu(a,b){if(G(a)){if(Ll(Hl.X.we))return O(176),Hl.X.we;if(Ll(Hl.X.bm))return O(170),Hl.X.we;var c=Lp();if(!c)O(171);else if(c.opener){var d=function(g){if(!wu.includes(g.origin))O(172);else if(!xu(g)){var h={gadSource:g.data.gadSource};G(229)&&(h.gclid=g.data.gclid);Kl(Hl.X.we,h);a===200&&g.data.gclid&&Ot(String(g.data.gclid),6,b);var m;(m=g.stopImmediatePropagation)==null||m.call(g);Sp(c,"message",d)}};if(Rp(c,"message",d)){Kl(Hl.X.bm,!0);for(var e=l(wu),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},
f.value);O(174);return Hl.X.we}O(175)}}};
var zu=function(a){var b={prefix:P(a.D,K.m.mb)||P(a.D,K.m.Ua),domain:P(a.D,K.m.ub),Pc:P(a.D,K.m.wb),flags:P(a.D,K.m.Bb)};a.D.isGtmEvent&&(b.path=P(a.D,K.m.Sb));return b},Bu=function(a,b){var c,d,e,f,g,h,m,n;c=a.Ae;d=a.Ee;e=a.Ie;f=a.Na;g=a.D;h=a.Fe;m=a.Is;n=a.rn;Au({Ae:c,Ee:d,Ie:e,Mc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,ou(b,f,g,h,n))},Du=function(a,b){if(!R(a,Q.A.xe)){var c=yu(119);if(c){var d=Ll(c),e=function(g){T(a,Q.A.xe,!0);var h=Cu(a,K.m.Pe),m=Cu(a,K.m.Qe);V(a,K.m.Pe,String(g.gadSource));
V(a,K.m.Qe,6);T(a,Q.A.ja);T(a,Q.A.Rf);V(a,K.m.ja);b();V(a,K.m.Pe,h);V(a,K.m.Qe,m);T(a,Q.A.xe,!1)};if(d)e(d);else{var f=void 0;f=Nl(c,function(g,h){e(h);Ol(c,f)})}}}},Au=function(a){var b,c,d,e;b=a.Ae;c=a.Ee;d=a.Ie;e=a.Mc;b&&(ns(c[K.m.qf],!!c[K.m.ma])&&(Qt(Eu,e),St(e),As(e)),Pp()!==2?(Kt(e),vu(e),yu(200,e)):It(e),Wt(Eu,e),Xt(e));c[K.m.ma]&&(Ut(Eu,c[K.m.ma],c[K.m.jd],!!c[K.m.Gc],e.prefix),Vt(c[K.m.ma],c[K.m.jd],!!c[K.m.Gc],e.prefix),Bs(ss(e.prefix),c[K.m.ma],c[K.m.jd],!!c[K.m.Gc],e),Bs("FPAU",c[K.m.ma],
c[K.m.jd],!!c[K.m.Gc],e));d&&(G(101)?Zt(Fu):Zt(Gu));au(Gu)},Hu=function(a,b){Array.isArray(b)||(b=[b]);var c=R(a,Q.A.ba);return b.indexOf(c)>=0},Eu=["aw","dc","gb"],Gu=["aw","dc","gb","ag"],Fu=["aw","dc","gb","ag","gad_source"];function Iu(a){var b=P(a.D,K.m.Fc),c=P(a.D,K.m.Ec);b&&!c?(a.eventName!==K.m.na&&a.eventName!==K.m.Xd&&O(131),a.isAborted=!0):!b&&c&&(O(132),a.isAborted=!0)}
function Ju(a){var b=vn(K.m.aa)?Nn.pscdl:"denied";b!=null&&V(a,K.m.Gg,b)}function Ku(a){var b=Pp(!0);V(a,K.m.Dc,b)}function Lu(a){Jq()&&V(a,K.m.ie,1)}function Mu(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&lj(a.substring(0,b))===void 0;)b--;return lj(a.substring(0,b))||""}function Nu(a){Ou(a,fo.Ff.Gn,P(a.D,K.m.wb))}function Ou(a,b,c){Cu(a,K.m.ud)||V(a,K.m.ud,{});Cu(a,K.m.ud)[b]=c}function Pu(a){T(a,Q.A.Qf,gl.Z.Ha)}
function Qu(a){var b=a.D.getMergedValues(K.m.Cc);b&&a.mergeHitDataForKey(K.m.Cc,b)}function Ru(a,b){b=b===void 0?!1:b;var c=R(a,Q.A.Pf),d=Su(a,"custom_event_accept_rules",!1)&&!b;if(c){var e=c.indexOf(a.target.destinationId)>=0,f=!0;G(240)&&R(a,Q.A.Ml)&&(f=R(a,Q.A.Za)===Fj());e&&f?T(a,Q.A.Hh,!0):(T(a,Q.A.Hh,!1),d||(a.isAborted=!0));G(240)&&(a.hasBeenAccepted()?a.isAborted=!0:R(a,Q.A.Hh)&&a.accept())}}
function Tu(a){kk&&(fm=!0,a.eventName===K.m.na?lm(a.D,a.target.id):(R(a,Q.A.Me)||(im[a.target.id]=!0),Wn(R(a,Q.A.Za))))}function Uu(a){};var Vu=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Wu=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Xu=/^\d+\.fls\.doubleclick\.net$/,Yu=/;gac=([^;?]+)/,Zu=/;gacgb=([^;?]+)/;
function $u(a,b){if(Xu.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(Vu)?lj(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function av(a,b,c){for(var d=it(ht())?Ds("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=cu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{iq:f?e.join(";"):"",hq:$u(d,Zu)}}function bv(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Wu)?b[1]:void 0}
function cv(a){var b={},c,d,e;Xu.test(z.location.host)&&(c=bv("gclgs"),d=bv("gclst"),e=bv("gcllp"));if(c&&d&&e)b.Yf=c,b.th=d,b.sh=e;else{var f=Fb(),g=qt((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Oc});h.length>0&&m.length>0&&n.length>0&&(b.Yf=h.join("."),b.th=m.join("."),b.sh=n.join("."))}return b}
function dv(a,b,c,d){d=d===void 0?!1:d;if(Xu.test(z.location.host)){var e=bv(c);if(e){if(d){var f=new Vs;Ws(f,2);Ws(f,3);return e.split(".").map(function(h){return{gclid:h,za:f,ab:[1]}})}return e.split(".").map(function(h){return{gclid:h,za:new Vs,ab:[1]}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Ct(g):lt(g)}if(b==="wbraid")return lt((a||"_gcl")+"_gb");if(b==="braids")return nt({prefix:a})}return[]}function ev(a){return Xu.test(z.location.host)?!(bv("gclaw")||bv("gac")):gu(a)}
function fv(a,b,c){var d;d=c?du(a,b):cu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function lv(){return On("dedupe_gclid",function(){return Kr()})};function rv(a,b,c,d){var e=Lc(),f;if(e===1)a:{var g=Fi(3);g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==w.location.protocol?a:b)+c};var Dv=[K.m.aa,K.m.W];var Iv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,Jv=/^www.googleadservices.com$/;function Kv(a){a||(a=Lv());return a.Ir?!1:a.zq||a.Aq||a.Cq||a.Bq||a.Ce||a.rh||a.jq||a.Zb==="aw.ds"||G(235)&&a.Zb==="aw.dv"||a.nq?!0:!1}
function Lv(){var a={},b=ds(!0);a.Ir=!!b._up;var c=Ht(),d=ju();a.zq=c.aw!==void 0;a.Aq=c.dc!==void 0;a.Cq=c.wbraid!==void 0;a.Bq=c.gbraid!==void 0;a.Zb=typeof c.gclsrc==="string"?c.gclsrc:void 0;a.Ce=d.Ce;a.rh=d.rh;var e=z.referrer?mj(sj(z.referrer),"host"):"";a.nq=Iv.test(e);a.jq=Jv.test(e);return a};var Mv=function(a,b,c){var d={};a.mergeHitDataForKey(K.m.Li,(d[b]=c,d))},Nv=function(a,b){var c=Su(a,K.m.Lg,a.D.M[K.m.Lg]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},Ov=function(a){var b=R(a,Q.A.Qa);if(rd(b))return b},Pv=function(a){if(R(a,Q.A.yd)||!ck(a.D))return!1;if(!P(a.D,K.m.pd)){var b=P(a.D,K.m.ee);return b===!0||b==="true"}return!0},Qv=function(a){return Su(a,K.m.je,P(a.D,K.m.je))||!!Su(a,"google_ng",!1)};var kg;function Rv(){var a=data.permissions||{};kg=new qg(Fi(5),a)};var Sv=Number(Ji(57,''))||5,Tv=Number(Ji(58,''))||50,Uv=vb();
var Wv=function(a,b){a&&(Vv("sid",a.targetId,b),Vv("cc",a.clientCount,b),Vv("tl",a.totalLifeMs,b),Vv("hc",a.heartbeatCount,b),Vv("cl",a.clientLifeMs,b))},Vv=function(a,b,c){b!=null&&c.push(a+"="+b)},Xv=function(){var a=z.referrer;if(a){var b;return mj(sj(a),"host")===((b=w.location)==null?void 0:b.host)?1:2}return 0},Yv="https://"+Fi(21)+"/a?",$v=function(){this.U=Zv;this.M=0};$v.prototype.H=function(a,b,c,d){var e=Xv(),f,g=[];f=w===w.top&&e!==0&&
b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&Vv("si",a.ig,g);Vv("m",0,g);Vv("iss",f,g);Vv("if",c,g);Wv(b,g);d&&Vv("fm",encodeURIComponent(d.substring(0,Tv)),g);this.R(g);};$v.prototype.C=function(a,b,c,d,e){var f=[];Vv("m",1,f);Vv("s",a,f);Vv("po",Xv(),f);b&&(Vv("st",b.state,f),Vv("si",b.ig,f),Vv("sm",b.pg,f));Wv(c,f);Vv("c",d,f);e&&Vv("fm",encodeURIComponent(e.substring(0,Tv)),f);this.R(f);
};$v.prototype.R=function(a){a=a===void 0?[]:a;!ik||this.M>=Sv||(Vv("pid",Uv,a),Vv("bc",++this.M,a),a.unshift("ctid="+Fi(5)+"&t=s"),this.U(""+Yv+a.join("&")))};function aw(a){return a.performance&&a.performance.now()||Date.now()}
var bw=function(a,b){var c=w,d;var e=function(f,g,h){h=h===void 0?{Um:function(){},Vm:function(){},Tm:function(){},onFailure:function(){}}:h;this.vp=f;this.C=g;this.M=h;this.ia=this.la=this.heartbeatCount=this.up=0;this.ih=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.ig=aw(this.C);this.pg=aw(this.C);this.U=10};e.prototype.init=function(){this.R(1);this.Ja()};e.prototype.getState=function(){return{state:this.state,
ig:Math.round(aw(this.C)-this.ig),pg:Math.round(aw(this.C)-this.pg)}};e.prototype.R=function(f){this.state!==f&&(this.state=f,this.pg=aw(this.C))};e.prototype.vm=function(){return String(this.up++)};e.prototype.Ja=function(){var f=this;this.heartbeatCount++;this.Xa({type:0,clientId:this.id,requestId:this.vm(),maxDelay:this.jh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ia++,g.isDead||f.ia>20){var m=g.isDead&&g.failure.failureType;
f.U=m||10;f.R(4);f.tp();var n,p;(p=(n=f.M).Tm)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.R(3),f.Am();else{if(f.heartbeatCount>g.stats.heartbeatCount+20){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.M).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.R(2);if(t!==2)if(f.ih){var u,v;(v=(u=f.M).Vm)==null||v.call(u)}else{f.ih=!0;var x,y;(y=(x=f.M).Um)==null||y.call(x)}f.ia=0;f.wp();f.Am()}}})};e.prototype.jh=function(){return this.state===2?
5E3:500};e.prototype.Am=function(){var f=this;this.C.setTimeout(function(){f.Ja()},Math.max(0,this.jh()-(aw(this.C)-this.la)))};e.prototype.Ap=function(f,g,h){var m=this;this.Xa({type:1,clientId:this.id,requestId:this.vm(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.M).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Xa=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.U},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Mf(t,7)},(p=f.maxDelay)!=null?p:5E3),r={request:f,jn:g,bn:m,Pq:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.la=aw(this.C);f.bn=!1;this.vp(f.request)};e.prototype.wp=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.bn&&this.sendRequest(h)}};e.prototype.tp=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Mf(this.H[g.value],this.U)};e.prototype.Mf=function(f,g){this.Jc(f);var h=f.request;h.failure={failureType:g};f.jn(h)};e.prototype.Jc=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Pq)};e.prototype.xq=function(f){this.la=aw(this.C);var g=this.H[f.requestId];if(g)this.Jc(g),g.jn(f);else{var h,m;(m=(h=this.M).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var cw;
var dw=function(){cw||(cw=new $v);return cw},Zv=function(a){El(Gl(gl.Z.Ic),function(){Oc(a)})},ew=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},fw=function(a){var b=a,c=Hi.la;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},gw=function(a){var b=Ll(Hl.X.im);return b&&b[a]},hw=function(a,
b,c,d,e){var f=this;this.H=d;this.U=this.R=!1;this.ia=null;this.initTime=c;this.C=15;this.M=this.Rp(a);w.setTimeout(function(){f.initialize()},1E3);Rc(function(){f.Gq(a,b,e)})};k=hw.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),ig:this.initTime,pg:Math.round(Fb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.M.Ap(a,b,c)};k.getState=function(){return this.M.getState().state};k.Gq=function(a,b,c){var d=w.location.origin,e=this,
f=Mc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?ew(h):"",p;G(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Mc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ia=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.M.xq(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Rp=function(a){var b=this,c=bw(function(d){var e;(e=b.ia)==null||e.postMessage(d,a.origin)},{Um:function(){b.R=!0;b.H.H(c.getState(),c.stats)},Vm:function(){},Tm:function(d){b.R?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.U||this.M.init();this.U=!0};function iw(){var a=ng(kg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function jw(a,b){var c=Math.round(Fb());b=b===void 0?!1:b;var d=w.location.origin;if(!d||!iw()||G(168))return;fj()&&!a&&(a=""+d+ej()+"/_/service_worker");var e=fw(a);if(e===null||gw(e.origin))return;if(!zc()){dw().H(void 0,void 0,6);return}var f=new hw(e,!!a,c||Math.round(Fb()),dw(),b);Ml(Hl.X.im,{})[e.origin]=f;}
var kw=function(a,b,c,d){var e;if((e=gw(a))==null||!e.delegate){var f=zc()?16:6;dw().C(f,void 0,void 0,b.commandType);d({failureType:f});return}gw(a).delegate(b,c,d);};
function lw(a,b,c,d,e){var f=fw();if(f===null){d(zc()?16:6);return}var g,h=(g=gw(f.origin))==null?void 0:g.initTime,m=Math.round(Fb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);kw(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function mw(a,b,c,d){var e=fw(a);if(e===null){d("_is_sw=f"+(zc()?16:6)+"te");return}var f=b?1:0,g=Math.round(Fb()),h,m=(h=gw(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;G(169)&&(p=!0);kw(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:w.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=gw(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function nw(a){if(G(10))return;var b=fj()||Hi.C||!!ck(a.D);G(245)&&(b=Hi.C||!!ck(a.D));if(b||G(168))return;jw(void 0,G(131));};function ow(){var a;a=a===void 0?document:a;var b;return!((b=a.featurePolicy)==null||!b.allowedFeatures().includes("attribution-reporting"))};var Aw=function(a){this.C=1;this.C>0||(this.C=1);this.onSuccess=a.D.onSuccess},Bw=function(a,b){return Pb(function(){a.C--;if(pb(a.onSuccess)&&a.C===0)a.onSuccess()},b>0?b:1)};var Cw=function(){var a;G(90)&&xm()!==""&&(a=xm());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},Dw=function(){var a="www";G(90)&&xm()&&(a=xm());return"https://"+a+".google-analytics.com/g/collect"};function Ew(a,b){var c=!!fj();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?ej()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?G(90)&&xm()?Cw():""+ej()+"/ag/g/c":Cw();case 16:return c?G(90)&&xm()?Dw():""+ej()+"/ga/g/c":Dw();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
ej()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?ej()+"/d/pagead/form-data":G(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Bp+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?ej()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 8:return"https://www.google.com/pagead/1p-conversion";case 63:return"https://www.googleadservices.com/pagead/conversion";case 64:return c?ej()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 65:return"https://www.google.com/pagead/1p-conversion";case 22:return c?ej()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?ej()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return c?ej()+
"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?ej()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return G(205)?"https://www.google.com/measurement/conversion/":c?ej()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?ej()+"/d/ccm/form-data":G(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 62:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:qc(a,"Unknown endpoint")}};function Gw(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};var Hw="email sha256_email_address phone_number sha256_phone_number first_name last_name".split(" "),Iw="first_name sha256_first_name last_name sha256_last_name street sha256_street city region country postal_code".split(" ");function Jw(a,b){if(!b._tag_metadata){for(var c={},d=0,e=0;e<a.length;e++)d+=Kw(a[e],b,c)?1:0;d>0&&(b._tag_metadata=c)}}function Kw(a,b,c){var d=b[a];if(d===void 0)return!1;c[a]=Array.isArray(d)?d.map(function(){return{mode:"c"}}):{mode:"c"};return!0}
function Lw(a,b,c){function d(f,g){g=String(g).substring(0,100);e.push(""+f+encodeURIComponent(g))}if(!c)return"";var e=[];d("i",String(a));d("f",b);c.mode&&d("m",c.mode);c.isPreHashed&&d("p","1");c.rawLength&&d("r",String(c.rawLength));c.normalizedLength&&d("n",String(c.normalizedLength));c.location&&d("l",c.location);c.selector&&d("s",c.selector);return e.join(".")};function Mw(){this.blockSize=-1};function Nw(a,b){this.blockSize=-1;this.blockSize=64;this.M=Fa.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.H=0;this.C=[];this.ia=a;this.U=b;this.la=Fa.Int32Array?new Int32Array(64):Array(64);Ow===void 0&&(Fa.Int32Array?Ow=new Int32Array(Pw):Ow=Pw);this.reset()}Ga(Nw,Mw);for(var Qw=[],Rw=0;Rw<63;Rw++)Qw[Rw]=0;var Sw=[].concat(128,Qw);
Nw.prototype.reset=function(){this.R=this.H=0;var a;if(Fa.Int32Array)a=new Int32Array(this.U);else{var b=this.U,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Tw=function(a){for(var b=a.M,c=a.la,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,x=0;x<64;x++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,A=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Ow[x]|0)|0)+(c[x]|0)|0)|0;v=u;u=t;t=r;r=q+A|0;q=p;p=n;n=m;m=A+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Nw.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.M[d++]=a.charCodeAt(c++),d==this.blockSize&&(Tw(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.M[d++]=g;d==this.blockSize&&(Tw(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.R+=b};Nw.prototype.digest=function(){var a=[],b=this.R*8;this.H<56?this.update(Sw,56-this.H):this.update(Sw,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.M[c]=b&255,b/=256;Tw(this);for(var d=0,e=0;e<this.ia;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Pw=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Ow;function Uw(){Nw.call(this,8,Vw)}Ga(Uw,Nw);var Vw=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Ww=/^[0-9A-Fa-f]{64}$/;function Xw(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Yw(a){var b=w;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Ww.test(a))return Promise.resolve(a);try{var d=Xw(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Zw(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Zw(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};
var cx=function(a,b){var c=G(178),d=["tv.1"],e=["tvd.1"],f=$w(a);if(f)return d.push(f),{lb:!1,Gj:d.join("~"),qg:{},Jd:c?e.join("~"):void 0};var g={},h=0;var m=0,n=ax(a,function(t,u,v){m++;var x=t.value,y;if(v){var A=u+"__"+h++;y="${userData."+A+"|sha256}";g[A]=x}else y=encodeURIComponent(encodeURIComponent(x));t.index!==void 0&&(u+=t.index);d.push(u+"."+y);if(c){var D=Lw(m,u,t.metadata);D&&e.push(D)}}).lb,p=e.join("~");
var q=d.join("~"),r={userData:g};return b===2?{lb:n,Gj:q,qg:r,Xp:"tv.1~${"+(q+"|encrypt}"),encryptionKeyString:bx(),Jd:c?p:void 0}:{lb:n,Gj:q,qg:r,Jd:c?p:void 0}},ex=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=dx(a);return ax(b,function(){}).lb},ax=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=fx[g.name];if(h){var m=gx(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{lb:d,jj:c}},gx=function(a){var b=hx(a.name),
c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(ix.test(e)||Ww.test(e))}return d},hx=function(a){return jx.indexOf(a)!==-1},bx=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BJlFZo1PLIDIEBtsGEdfkWR4ZCcviP2y+dqfwvtWFNjNYttgOKelwTaMUwDjc4u1vbVnCQhKBR3Ud3oXWze2eV4\x3d\x22,\x22version\x22:0},\x22id\x22:\x22e79a77ad-43af-44a5-bcca-e57a1f93b790\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BDw7DFfOeZAAWAqNIyQfysi0/JA10sR+FPZhR6dMiRh4sDLkXZ+q5KoIer8OYTaPCHAZDjlF4UCz/ecoB262OG0\x3d\x22,\x22version\x22:0},\x22id\x22:\x2290bb4489-4e6e-41bc-9502-3e9d807aa4dd\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BC//NaSUVUe7TL7gUUpav1au+A9txHuJj5FmEkTOnsN0kBHmVr42c4PoznnLpQHkIK6mHi4tfTzCe888FbA2mCE\x3d\x22,\x22version\x22:0},\x22id\x22:\x2263d5d28f-3fce-4587-986e-285cc423dfa1\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BB0vQ526NMz7UA93caIqPgXdDxDAVWQZndpQCFqrbGC9e/6V4njP+/9b14wWL2ZgZ65/wfrndtBmKkenASmKl/0\x3d\x22,\x22version\x22:0},\x22id\x22:\x224aadf648-f1fb-4a07-bbd3-09468280293c\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BLZSd5uiWWgL76/7QnIswmgZarlkx5CYp0WjRYlmOlij9R1fcj5aGKOXVUsG1faHH5wRBRoWbgbG2ma6T3kkNP0\x3d\x22,\x22version\x22:0},\x22id\x22:\x22468470fe-89e8-44ed-821a-c25902de0bd0\x22}]}'},mx=function(a){if(w.Promise){var b=void 0;return b}},qx=function(a,b,c){if(w.Promise)try{var d=dx(a),e=nx(d).then(ox);return e}catch(g){}},sx=function(a){try{return ox(rx(dx(a)))}catch(b){}},lx=function(a){var b=void 0;
return b},ox=function(a){var b=G(178),c=a.Qc,d=["tv.1"],e=["tvd.1"],f=$w(c);if(f)return d.push(f),{hc:d.join("~"),jj:!1,lb:!1,ij:!0,Jd:b?e.join("~"):void 0};var g=c.filter(function(q){return!gx(q)}),h=0,m=ax(g,function(q,r){h++;var t=q.value,u=q.index;u!==void 0&&(r+=u);d.push(r+"."+t);if(b){var v=Lw(h,r,q.metadata);v&&e.push(v)}}),n=m.jj,p=m.lb;return{hc:encodeURIComponent(d.join("~")),jj:n,lb:p,ij:!1,Jd:b?e.join("~"):void 0}},$w=function(a){if(a.length===1&&a[0].name==="error_code")return fx.error_code+
"."+a[0].value},px=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(fx[d.name]&&d.value)return!0}return!1},dx=function(a){function b(t,u,v,x,y){var A=tx(t);if(A!=="")if(Ww.test(A)){y&&(y.isPreHashed=!0);var D={name:u,value:A,index:x};y&&(D.metadata=y);m.push(D)}else{var E=v(A),L={name:u,value:E,index:x};y&&(L.metadata=y,E&&(y.rawLength=String(A).length,y.normalizedLength=E.length));m.push(L)}}function c(t,u){var v=t;if(qb(v)||
Array.isArray(v)){v=tb(t);for(var x=0;x<v.length;++x){var y=tx(v[x]),A=Ww.test(y);u&&!A&&O(89);!u&&A&&O(88)}}}function d(t,u){var v=t[u];c(v,!1);var x=ux[u];t[x]&&(t[u]&&O(90),v=t[x],c(v,!0));return v}function e(t,u,v,x){var y=t._tag_metadata||{},A=t[u],D=y[u];c(A,!1);var E=ux[u];if(E){var L=t[E],F=y[E];L&&(A&&O(90),A=L,D=F,c(A,!0))}if(x!==void 0)b(A,u,v,x,D);else{A=tb(A);D=tb(D);for(var M=0;M<A.length;++M)b(A[M],u,v,void 0,D[M])}}function f(t,u,v){if(G(178))e(t,u,v,void 0);else for(var x=tb(d(t,
u)),y=0;y<x.length;++y)b(x[y],u,v)}function g(t,u,v,x){if(G(178))e(t,u,v,x);else{var y=d(t,u);b(y,u,v,x)}}function h(t){return function(u){O(64);return t(u)}}var m=[];if(w.location.protocol!=="https:")return m.push({name:"error_code",value:"e3",index:void 0}),m;f(a,"email",vx);f(a,"phone_number",wx);f(a,"first_name",h(xx));f(a,"last_name",h(xx));var n=a.home_address||{};f(n,"street",h(yx));f(n,"city",h(yx));f(n,"postal_code",h(zx));f(n,"region",h(yx));f(n,"country",h(zx));for(var p=tb(a.address||
{}),q=0;q<p.length;q++){var r=p[q];g(r,"first_name",xx,q);g(r,"last_name",xx,q);g(r,"street",yx,q);g(r,"city",yx,q);g(r,"postal_code",zx,q);g(r,"region",yx,q);g(r,"country",zx,q)}return m},Ax=function(a){var b=a?dx(a):[];return ox({Qc:b})},Bx=function(a){return a&&a!=null&&Object.keys(a).length>0&&w.Promise?dx(a).some(function(b){return b.value&&hx(b.name)&&!Ww.test(b.value)}):!1},tx=function(a){return a==null?"":qb(a)?Db(String(a)):"e0"},zx=function(a){return a.replace(Cx,"")},xx=function(a){return yx(a.replace(/\s/g,
""))},yx=function(a){return Db(a.replace(Dx,"").toLowerCase())},wx=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Ex.test(a)?a:"e0"},vx=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Fx.test(c))return c}return"e0"},rx=function(a){try{return a.forEach(function(b){if(b.value&&hx(b.name)){var c;var d=b.value,e=w;if(d===""||d==="e0"||Ww.test(d))c=d;else try{var f=new Uw;
f.update(Xw(d));c=Zw(f.digest(),e)}catch(g){c="e2"}b.value=c}}),{Qc:a}}catch(b){return{Qc:[]}}},nx=function(a){return a.some(function(b){return b.value&&hx(b.name)})?w.Promise?Promise.all(a.map(function(b){return b.value&&hx(b.name)?Yw(b.value).then(function(c){b.value=c}):Promise.resolve()})).then(function(){return{Qc:a}}).catch(function(){return{Qc:[]}}):Promise.resolve({Qc:[]}):Promise.resolve({Qc:a})},Dx=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Fx=/^\S+@\S+\.\S+$/,Ex=/^\+\d{10,15}$/,Cx=/[.~]/g,
ix=/^[0-9A-Za-z_-]{43}$/,Gx={},fx=(Gx.email="em",Gx.phone_number="pn",Gx.first_name="fn",Gx.last_name="ln",Gx.street="sa",Gx.city="ct",Gx.region="rg",Gx.country="co",Gx.postal_code="pc",Gx.error_code="ec",Gx),Hx={},ux=(Hx.email="sha256_email_address",Hx.phone_number="sha256_phone_number",Hx.first_name="sha256_first_name",Hx.last_name="sha256_last_name",Hx.street="sha256_street",Hx);var jx=Object.freeze(["email","phone_number","first_name","last_name","street"]);function Ix(a,b){b&&yb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};function Jx(a,b){var c=Cu(a,K.m.Cc);if(c&&typeof c==="object")for(var d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value,g=c[f];g!==void 0&&(g===null&&(g=""),b["gap."+f]=String(g))}};
var Lx=function(a){for(var b={},c=function(p,q){b[p]=q===!0?"1":q===!1?"0":encodeURIComponent(String(q))},d=l(Object.keys(a.C)),e=d.next();!e.done;e=d.next()){var f=e.value,g=Cu(a,f);if(f.indexOf("_&")===0)c(f.substring(2),g);else{var h=Kx[f];h&&g!==void 0&&g!==""&&(!R(a,Q.A.ye)||f!==K.m.Xc&&f!==K.m.ed&&f!==K.m.be&&f!==K.m.Re||(g="0"),c(h,g))}}c("gtm",Nq({Na:R(a,Q.A.Za),Pd:a.D.isGtmEvent}));zq()&&c("gcs",Aq());c("gcd",Eq(a.D));Hq()&&c("dma_cps",Fq());c("dma",Gq());aq(iq())&&c("tcfd",Iq());var m=bo(a);
m&&c("tag_exp",m);Wj()&&c("ptag_exp",Wj());if(R(a,Q.A.tg)){c("tft",Fb());var n=ed();n!==void 0&&c("tfd",Math.round(n))}G(24)&&c("apve","1");(G(25)||G(26))&&c("apvf",$c()?G(26)?"f":"sb":"nf");yl[gl.Z.Ha]!==fl.Ka.ue||Bl[gl.Z.Ha].isConsentGranted()||c("limited_ads",!0);Jx(a,b);return b},Mx=function(a,b,c){var d=b.D;gn({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},kb:{eventId:d.eventId,priorityId:d.priorityId},Wi:{eventId:R(b,Q.A.Je),priorityId:R(b,Q.A.Ke)}})},Nx=function(a,
b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.D.eventId,priorityId:b.D.priorityId};Mx(a,b,c);bl(d,a,void 0,{Ch:!0,method:"GET"},function(){},function(){al(d,a+"&img=1")})},Ox=function(a){var b=Gc()||Ec()?"www.google.com":"www.googleadservices.com",c=[];yb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},Px=function(a){if(R(a,Q.A.ba)===N.N.Ya){var b=
Lx(a),c=[];a.target.destinationId&&c.push("tid="+a.target.destinationId);yb(b,function(r,t){c.push(r+"="+t)});var d=vn([K.m.aa,K.m.W])?45:46,e=Ew(d)+"?"+c.join("&");Mx(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(G(26)&&$c()){bl(g,e,void 0,{Ch:!0},function(){},function(){al(g,e+"&img=1")});var h=vn([K.m.aa,K.m.W]),m=Cu(a,K.m.hd)==="1",n=Cu(a,K.m.Uh)==="1";if(h&&m&&!n){var p=Ox(b),q=Gc()||Ec()?58:57;Nx(p,a,q)}}else $k(g,e)||al(g,
e+"&img=1");if(pb(a.D.onSuccess))a.D.onSuccess()}},Qx={},Kx=(Qx[K.m.ja]="gcu",Qx[K.m.oc]="gclgb",Qx[K.m.tb]="gclaw",Qx[K.m.Pe]="gad_source",Qx[K.m.Qe]="gad_source_src",Qx[K.m.Xc]="gclid",Qx[K.m.vk]="gclsrc",Qx[K.m.Re]="gbraid",Qx[K.m.be]="wbraid",Qx[K.m.Yc]="auid",Qx[K.m.yk]="rnd",Qx[K.m.Uh]="ncl",Qx[K.m.Hg]="gcldc",Qx[K.m.ed]="dclid",Qx[K.m.Ac]="edid",Qx[K.m.gd]="en",Qx[K.m.he]="gdpr",Qx[K.m.Bc]="gdid",Qx[K.m.ie]="_ng",Qx[K.m.kf]="gpp_sid",Qx[K.m.lf]="gpp",Qx[K.m.nf]="_tu",Qx[K.m.Sk]="gtm_up",Qx[K.m.Dc]=
"frm",Qx[K.m.hd]="lps",Qx[K.m.Qg]="did",Qx[K.m.Vk]="navt",Qx[K.m.Ea]="dl",Qx[K.m.Wa]="dr",Qx[K.m.Cb]="dt",Qx[K.m.fl]="scrsrc",Qx[K.m.vf]="ga_uid",Qx[K.m.ne]="gdpr_consent",Qx[K.m.ji]="u_tz",Qx[K.m.Ma]="uid",Qx[K.m.Ef]="us_privacy",Qx[K.m.Bd]="npa",Qx);var Rx={};Rx.O=er.O;var Sx={ks:"L",np:"S",Cs:"Y",Kr:"B",Wr:"E",bs:"I",zs:"TC",Zr:"HTC"},Tx={np:"S",Vr:"V",Nr:"E",ys:"tag"},Ux={},Vx=(Ux[Rx.O.Pi]="6",Ux[Rx.O.Qi]="5",Ux[Rx.O.Oi]="7",Ux);function Wx(){function a(c,d){var e=nb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var Xx=!1;
function py(a){}function qy(a){}
function ry(){}function sy(a){}
function ty(a){}function uy(a){}
function vy(){}
function wy(a,b){}
function xy(a,b,c){}
function yy(){};var zy=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function Ay(a,b,c,d,e,f,g,h){var m=ma(Object,"assign").call(Object,{},zy);c&&(m.body=c,m.method="POST");ma(Object,"assign").call(Object,m,e);h==null||Rk(h);w.fetch(b,m).then(function(n){h==null||Sk(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function t(){p.read().then(function(u){var v;v=u.done;var x=q.decode(u.value,{stream:!v});By(d,x);v?(f==null||f(),r()):t()}).catch(function(){r()})}t()})}}).catch(function(){h==null||Sk(h);
g?g():G(128)&&(b+="&_z=retryFetch",c?$k(a,b,c):Zk(a,b))})};var Cy=function(a){this.M=a;this.C=""},Dy=function(a,b){a.H=b;return a},By=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}Ey(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},Fy=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c={};Ey(a,(c[b.fallback_url_method]=
[b.fallback_url],c.options={},c))}}},Ey=function(a,b){b&&(Gy(b.send_pixel,b.options,a.M),Gy(b.create_iframe,b.options,a.R),Gy(b.fetch,b.options,a.H))};function Hy(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function Gy(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=rd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var Iy=function(a,b){this.Sq=a;this.timeoutMs=b;this.Sa=void 0},Rk=function(a){a.Sa||(a.Sa=setTimeout(function(){a.Sq();a.Sa=void 0},a.timeoutMs))},Sk=function(a){a.Sa&&(clearTimeout(a.Sa),a.Sa=void 0)};var rz=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),sz={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},tz={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},uz="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function vz(){var a=Xo("gtm.allowlist")||Xo("gtm.whitelist");a&&O(9);Xi&&!G(212)?a=["google","gtagfl","lcl","zone","cmpPartners"]:G(212)&&(a=void 0);rz.test(w.location&&w.location.hostname)&&(Xi?O(116):(O(117),wz&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Jb(Cb(a),sz),c=Xo("gtm.blocklist")||Xo("gtm.blacklist");c||(c=Xo("tagTypeBlacklist"))&&O(3);c?O(8):c=[];rz.test(w.location&&w.location.hostname)&&(c=Cb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));Cb(c).indexOf("google")>=0&&O(2);var d=c&&Jb(Cb(c),tz),e={};return function(f){var g=f&&f[mf.Pa];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=cj[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(Xi&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){O(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=wb(d,h||[]);t&&
O(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:Xi&&h.indexOf("cmpPartners")>=0?!xz():b&&b.indexOf("sandboxedScripts")!==-1?0:wb(d,uz))&&(u=!0);return e[g]=u}}function xz(){var a=ng(kg.C,Fi(5),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var wz=!1;wz=!0;G(218)&&(wz=Di(48,wz));function yz(a,b,c,d,e){if(!Oj(a)){d.loadExperiments=Pi();Qj(a,d,e);var f=zz(a),g=function(){Aj().container[a]&&(Aj().container[a].state=3);Az()},h={destinationId:a,endpoint:0};if(fj())cl(h,ej()+"/"+f,void 0,g);else{var m=Kb(a,"GTM-"),n=bk(),p=c?"/gtag/js":"/gtm.js",q=ak(b,p+f);if(!q){var r=Fi(3)+p;n&&Bc&&m&&(r=Bc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=rv("https://","http://",r+f)}cl(h,q,void 0,g)}}}function Az(){Rj()||yb(Sj(),function(a,b){Bz(a,b.transportUrl,b.context);O(92)})}
function Bz(a,b,c,d){if(!Pj(a))if(c.loadExperiments||(c.loadExperiments=Pi()),Rj()){var e;(e=Aj().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:Jj()});Aj().destination[a].state=0;zj({ctid:a,isDestination:!0},d);O(91)}else{var f;(f=Aj().destination)[a]!=null||(f[a]={context:c,state:1,parent:Jj()});Aj().destination[a].state=1;zj({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(fj())cl(g,ej()+("/gtd"+zz(a,!0)));else{var h="/gtag/destination"+zz(a,!0),m=ak(b,
h);m||(m=rv("https://","http://",Fi(3)+h));cl(g,m)}}}function zz(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a),d=Fi(19);d!=="dataLayer"&&(c+="&l="+d);if(!Kb(a,"GTM-")||b)c=G(130)?c+(fj()?"&sc=1":"&cx=c"):c+"&cx=c";var e=c,f,g={gn:Ii(15),kn:Fi(14)};f=lf(g);c=e+("&gtm="+f);bk()&&(c+="&sign="+Ri.Mi);var h=Hi.M;h===1?c+="&fps=fc":h===2&&(c+="&fps=fe");return c};var Cz=function(){this.H=0;this.C={}};Cz.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,He:c};return d};Cz.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var Ez=function(a,b){var c=[];yb(Dz.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.He===void 0||b.indexOf(e.He)>=0)&&c.push(e.listener)});return c};function Fz(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:Fi(5)}};function Gz(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var Iz=function(a,b){this.C=!1;this.R=[];this.eventData={tags:[]};this.U=!1;this.H=this.M=0;Hz(this,a,b)},Jz=function(a,b,c,d){if(Ti.hasOwnProperty(b)||b==="__zone")return-1;var e={};rd(d)&&(e=sd(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},Kz=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},Lz=function(a){if(!a.C){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.C=!0;a.R.length=0}},Hz=function(a,b,c){b!==void 0&&a.Sf(b);c&&w.setTimeout(function(){Lz(a)},
Number(c))};Iz.prototype.Sf=function(a){var b=this,c=Hb(function(){Rc(function(){a(Fi(5),b.eventData)})});this.C?c():this.R.push(c)};var Mz=function(a){a.M++;return Hb(function(){a.H++;a.U&&a.H>=a.M&&Lz(a)})},Nz=function(a){a.U=!0;a.H>=a.M&&Lz(a)};var Oz={};function Pz(){return w[Qz()]}
function Qz(){return w.GoogleAnalyticsObject||"ga"}function Tz(){var a=Fi(5);}
function Uz(a,b){return function(){var c=Pz(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var $z=["es","1"],aA={},bA={};function cA(a,b){if(ik){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";aA[a]=[["e",c],["eid",a]];pp(a)}}function dA(a){var b=a.eventId,c=a.Sc;if(!aA[b])return[];var d=[];bA[b]||d.push($z);d.push.apply(d,Aa(aA[b]));c&&(bA[b]=!0);return d};var eA={},fA={},gA={};function hA(a,b,c,d){ik&&G(120)&&((d===void 0?0:d)?(gA[b]=gA[b]||0,++gA[b]):c!==void 0?(fA[a]=fA[a]||{},fA[a][b]=Math.round(c)):(eA[a]=eA[a]||{},eA[a][b]=(eA[a][b]||0)+1))}function iA(a){var b=a.eventId,c=a.Sc,d=eA[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete eA[b];return e.length?[["md",e.join(".")]]:[]}
function jA(a){var b=a.eventId,c=a.Sc,d=fA[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete fA[b];return e.length?[["mtd",e.join(".")]]:[]}function kA(){for(var a=[],b=l(Object.keys(gA)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+gA[d])}return a.length?[["mec",a.join(".")]]:[]};var lA={},mA={};function nA(a,b,c){if(ik&&b){var d=fk(b);lA[a]=lA[a]||[];lA[a].push(c+d);var e=b[mf.Pa];if(!e)throw Error("Error: No function name given for function call.");var f=(Of[e]?"1":"2")+d;mA[a]=mA[a]||[];mA[a].push(f);pp(a)}}function oA(a){var b=a.eventId,c=a.Sc,d=[],e=lA[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=mA[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete lA[b],delete mA[b]);return d};function pA(a,b,c){c=c===void 0?!1:c;qA().addRestriction(0,a,b,c)}function rA(a,b,c){c=c===void 0?!1:c;qA().addRestriction(1,a,b,c)}function sA(){var a=Fj();return qA().getRestrictions(1,a)}var tA=function(){this.container={};this.C={}},uA=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
tA.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=uA(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
tA.prototype.getRestrictions=function(a,b){var c=uA(this,b);if(a===0){var d,e;return[].concat(Aa((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),Aa((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(Aa((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),Aa((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
tA.prototype.getExternalRestrictions=function(a,b){var c=uA(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};tA.prototype.removeExternalRestrictions=function(a){var b=uA(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function qA(){return On("r",function(){return new tA})};function vA(a,b,c,d){var e=Mf[a],f=wA(a,b,c,d);if(!f)return null;var g=$f(e[mf.jm],c,[]);if(g&&g.length){var h=g[0];f=vA(h.index,{onSuccess:f,onFailure:h.Km===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function wA(a,b,c,d){function e(){function x(){mm(3);var M=Fb()-F;Fz(1,a,Mf[a][mf.Zg]);nA(c.id,f,"7");Kz(c.Kc,E,"exception",M);G(109)&&xy(c,f,Rx.O.Oi);L||(L=!0,h())}if(f[mf.ep])h();else{var y=Zf(f,c,[]),A=y[mf.Dn];if(A!=null)for(var D=0;D<A.length;D++)if(!vn(A[D])){h();return}var E=Jz(c.Kc,String(f[mf.Pa]),Number(f[mf.mh]),y[mf.METADATA]),L=!1;y.vtp_gtmOnSuccess=function(){if(!L){L=!0;var M=Fb()-F;nA(c.id,Mf[a],"5");Kz(c.Kc,E,"success",M);G(109)&&xy(c,f,Rx.O.Qi);g()}};y.vtp_gtmOnFailure=function(){if(!L){L=
!0;var M=Fb()-F;nA(c.id,Mf[a],"6");Kz(c.Kc,E,"failure",M);G(109)&&xy(c,f,Rx.O.Pi);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);nA(c.id,f,"1");G(109)&&wy(c,f);var F=Fb();try{ag(y,{event:c,index:a,type:1})}catch(M){x(M)}G(109)&&xy(c,f,Rx.O.wm)}}var f=Mf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=$f(f[mf.xm],c,[]);if(n&&n.length){var p=n[0],q=vA(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;
g=q;h=p.Km===2?m:q}if(f[mf.am]||f[mf.hp]){var r=f[mf.am]?Nf:c.Cr,t=g,u=h;if(!r[a]){var v=xA(a,r,Hb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function xA(a,b,c){var d=[],e=[];b[a]=yA(d,e,c);return{onSuccess:function(){b[a]=zA;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=AA;for(var f=0;f<e.length;f++)e[f]()}}}function yA(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function zA(a){a()}function AA(a,b){b()};var DA=function(a,b){for(var c=[],d=0;d<Mf.length;d++)if(a[d]){var e=Mf[d];var f=Mz(b.Kc);try{var g=vA(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[mf.Pa];if(!h)throw Error("Error: No function name given for function call.");var m=Of[h];c.push({pn:d,priorityOverride:(m?m.priorityOverride||0:0)||Gz(e[mf.Pa],1)||0,execute:g})}else BA(d,b),f()}catch(p){f()}}c.sort(CA);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function EA(a,b){if(!Dz)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=Ez(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=Mz(b);try{d[e](a,f)}catch(g){f()}}return!0}function CA(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.pn,h=b.pn;f=g>h?1:g<h?-1:0}return f}
function BA(a,b){if(ik){var c=function(d){var e=b.isBlocked(Mf[d])?"3":"4",f=$f(Mf[d][mf.jm],b,[]);f&&f.length&&c(f[0].index);nA(b.id,Mf[d],e);var g=$f(Mf[d][mf.xm],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var FA=!1,Dz;function GA(){Dz||(Dz=new Cz);return Dz}
function HA(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(G(109)){}if(d==="gtm.js"){if(FA)return!1;FA=!0}var e=!1,f=sA(),g=sd(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}cA(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:IA(g,e),Cr:[],logMacroError:function(t,u,v){O(6);mm(0);Fz(2,u,v)},cachedModelValues:JA(),Kc:new Iz(function(){if(G(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,
0))},m),originalEventData:g};G(120)&&ik&&(n.reportMacroDiscrepancy=hA);G(109)&&ty(n.id);var p=fg(n);G(109)&&uy(n.id);e&&(p=KA(p));G(109)&&sy(b);var q=DA(p,n),r=EA(a,n.Kc);Nz(n.Kc);d!=="gtm.js"&&d!=="gtm.sync"||Tz();return LA(p,q)||r}function JA(){var a={};a.event=bp("event",1);a.ecommerce=bp("ecommerce",1);a.gtm=bp("gtm");a.eventModel=bp("eventModel");return a}
function IA(a,b){var c=vz();return function(d){if(c(d))return!0;var e=d&&d[mf.Pa];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Fj();f=qA().getRestrictions(0,g);var h=a;b&&(h=sd(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=cj[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function KA(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Mf[c][mf.Pa]);if(Si[d]||Mf[c][mf.jp]!==void 0||Gz(d,2))b[c]=!0}return b}function LA(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Mf[c]&&!Ti[String(Mf[c][mf.Pa])])return!0;return!1};function MA(){GA().addListener("gtm.init",function(a,b){Hi.ia=!0;Zl();b()})};var NA=!1,OA=0,PA=[];function QA(a){if(!NA){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){NA=!0;for(var e=0;e<PA.length;e++)Rc(PA[e])}PA.push=function(){for(var f=Ea.apply(0,arguments),g=0;g<f.length;g++)Rc(f[g]);return 0}}}function RA(){if(!NA&&OA<140){OA++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");QA()}catch(c){w.setTimeout(RA,50)}}}
function SA(){var a=w;NA=!1;OA=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")QA();else{Pc(z,"DOMContentLoaded",QA);Pc(z,"readystatechange",QA);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&RA()}Pc(a,"load",QA)}}function TA(a){NA?a():PA.push(a)};function UA(a,b){return arguments.length===1?VA("set",a):VA("set",a,b)}function WA(a,b){return arguments.length===1?VA("config",a):VA("config",a,b)}function XA(a,b,c){c=c||{};c[K.m.od]=a;return VA("event",b,c)}function VA(){return arguments};var YA=function(){this.messages=[];this.C=[]};YA.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=ma(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};YA.prototype.listen=function(a){this.C.push(a)};
YA.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};YA.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function ZA(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[Q.A.Za]=Fi(6);$A().enqueue(a,b,c)}function aB(){var a=bB;$A().listen(a)}
function $A(){return On("mb",function(){return new YA})};var cB={},dB={};function eB(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Aj:void 0,gj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Aj=io(g,b),e.Aj){var h=Ej();ub(h,function(r){return function(t){return r.Aj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=cB[g]||[];e.gj={};m.forEach(function(r){return function(t){r.gj[t]=!0}}(e));for(var n=Gj(),p=0;p<n.length;p++)if(e.gj[n[p]]){c=c.concat(Ej());break}var q=dB[g]||[];q.length&&(c=c.concat(q))}}return{tj:c,Qq:d}}
function fB(a){yb(cB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function gB(a){yb(dB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var hB=!1,iB=!1;function jB(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=sd(b,null),b[K.m.ef]&&(d.eventCallback=b[K.m.ef]),b[K.m.Mg]&&(d.eventTimeout=b[K.m.Mg]));return d}function kB(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Tn()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function lB(a,b){var c=a&&a[K.m.od];c===void 0&&(c=Xo(K.m.od,2),c===void 0&&(c="default"));if(qb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?qb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=eB(d,b.isGtmEvent),f=e.tj,g=e.Qq;if(g.length)for(var h=mB(a),m=0;m<g.length;m++){var n=io(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=Aj().destination[q];r&&r.state===0||Bz(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{tj:jo(f,b.isGtmEvent),
Ep:jo(t,b.isGtmEvent)}}}var nB=void 0,oB=void 0;function pB(a,b,c){var d=sd(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&O(136);var e=sd(b,null);sd(c,e);ZA(WA(Gj()[0],e),a.eventId,d)}function mB(a){for(var b=l([K.m.pd,K.m.rc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||xp.C[d];if(e)return e}}
var qB={config:function(a,b){var c=kB(a,b);if(!(a.length<2)&&qb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!rd(a[2])||a.length>3)return;d=a[2]}var e=io(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Ei(7)){var m=Ij(Jj());if(Tj(m)){var n=m.parent,p=n.isDestination;h={Uq:Ij(n),Oq:p};break a}}h=void 0}var q=h;q&&(f=q.Uq,g=q.Oq);cA(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Ej().indexOf(r)===-1:Gj().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[K.m.Fc]){var u=mB(d);if(t)Bz(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;nB?pB(b,v,nB):oB||(oB=sd(v,null))}else yz(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(O(128),g&&O(130),b.inheritParentConfig)){var x;var y=d;oB?(pB(b,oB,y),x=!1):(!y[K.m.rd]&&Ei(11)&&nB||(nB=sd(y,null)),x=!0);x&&f.containers&&f.containers.join(",");return}kk&&(Vn===1&&(Sl.mcc=!1),Vn=2);if(Ei(11)&&!t&&!d[K.m.rd]){var A=iB;iB=!0;if(A)return}hB||O(43);if(!b.noTargetGroup)if(t){gB(e.id);
var D=e.id,E=d[K.m.Pg]||"default";E=String(E).split(",");for(var L=0;L<E.length;L++){var F=dB[E[L]]||[];dB[E[L]]=F;F.indexOf(D)<0&&F.push(D)}}else{fB(e.id);var M=e.id,U=d[K.m.Pg]||"default";U=U.toString().split(",");for(var fa=0;fa<U.length;fa++){var S=cB[U[fa]]||[];cB[U[fa]]=S;S.indexOf(M)<0&&S.push(M)}}delete d[K.m.Pg];var Z=b.eventMetadata||{};Z.hasOwnProperty(Q.A.wd)||(Z[Q.A.wd]=!b.fromContainerExecution);b.eventMetadata=Z;delete d[K.m.ef];for(var ra=t?[e.id]:Ej(),ka=0;ka<ra.length;ka++){var da=
d,Y=ra[ka],ja=sd(b,null),za=io(Y,ja.isGtmEvent);za&&xp.push("config",[da],za,ja)}}}}},consent:function(a,b){if(a.length===3){O(39);var c=kB(a,b),d=a[1],e={},f=Nm(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===K.m.ug?Array.isArray(h)?NaN:Number(h):g===K.m.jc?(Array.isArray(h)?h:[h]).map(Om):Pm(h)}b.fromContainerExecution||(e[K.m.W]&&O(139),e[K.m.La]&&O(140));d==="default"?rn(e):d==="update"?tn(e,c):d==="declare"&&b.fromContainerExecution&&qn(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&qb(c)){var d=void 0;if(a.length>2){if(!rd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=jB(c,d),f=kB(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=lB(d,b);if(m){for(var n=m.tj,p=m.Ep,q=p.map(function(M){return M.id}),r=p.map(function(M){return M.destinationId}),t=n.map(function(M){return M.id}),u=l(Ej()),v=u.next();!v.done;v=u.next()){var x=v.value;r.indexOf(x)<0&&t.push(x)}cA(g,
c);for(var y=l(t),A=y.next();!A.done;A=y.next()){var D=A.value,E=sd(b,null),L=sd(d,null);delete L[K.m.ef];var F=E.eventMetadata||{};F.hasOwnProperty(Q.A.wd)||(F[Q.A.wd]=!E.fromContainerExecution);F[Q.A.Ji]=q.slice();F[Q.A.Pf]=r.slice();E.eventMetadata=F;yp(c,L,D,E)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[K.m.od]=q.join(","):delete e.eventModel[K.m.od];hB||O(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[Q.A.tm]&&(b.noGtmEvent=!0);e.eventModel[K.m.Ec]&&(b.noGtmEvent=!0);
return b.noGtmEvent?void 0:e}}},get:function(a,b){O(53);if(a.length===4&&qb(a[1])&&qb(a[2])&&pb(a[3])){var c=io(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){hB||O(43);var f=mB();if(ub(Ej(),function(h){return c.destinationId===h})){kB(a,b);var g={};sd((g[K.m.jf]=d,g[K.m.hf]=e,g),null);zp(d,function(h){Rc(function(){e(h)})},c.id,b)}else Bz(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){hB=!0;var c=kB(a,b),d=c.eventId,
e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&qb(a[1])&&pb(a[2])){if(lg(a[1],a[2]),O(74),a[1]==="all"){O(75);var b=!1;try{b=a[2](Fi(5),"unknown",{})}catch(c){}b||O(76)}}else O(73)},set:function(a,b){var c=void 0;a.length===2&&rd(a[1])?c=sd(a[1],null):a.length===3&&qb(a[1])&&(c={},rd(a[2])||Array.isArray(a[2])?c[a[1]]=sd(a[2],null):c[a[1]]=a[2]);if(c){var d=kB(a,b),e=d.eventId,f=d.priorityId;
sd(c,null);Fi(5);var g=sd(c,null);xp.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},rB={policy:!0};var tB=function(a){if(sB(a))return a;this.value=a};tB.prototype.getUntrustedMessageValue=function(){return this.value};var sB=function(a){return!a||pd(a)!=="object"||rd(a)?!1:"getUntrustedMessageValue"in a};tB.prototype.getUntrustedMessageValue=tB.prototype.getUntrustedMessageValue;var uB=!1,vB=[];function wB(){if(!uB){uB=!0;for(var a=0;a<vB.length;a++)Rc(vB[a])}}function xB(a){uB?Rc(a):vB.push(a)};var yB=0,zB={},AB=[],BB=[],CB=!1,DB=!1;function EB(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function FB(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return GB(a)}function HB(a,b){if(!sb(b)||b<0)b=0;var c=Sn(),d=0,e=!1,f=void 0;f=w.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(w.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function IB(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(zb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function JB(){var a;if(BB.length)a=BB.shift();else if(AB.length)a=AB.shift();else return;var b;var c=a;if(CB||!IB(c.message))b=c;else{CB=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Tn(),f=Tn(),c.message["gtm.uniqueEventId"]=Tn());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};AB.unshift(n,c);b=h}return b}
function KB(){for(var a=!1,b;!DB&&(b=JB());){DB=!0;delete Uo.eventModel;Wo();var c=b,d=c.message,e=c.messageContext;if(d==null)DB=!1;else{e.fromContainerExecution&&ap();try{if(pb(d))try{d.call(Yo)}catch(L){}else if(Array.isArray(d)){if(qb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=Xo(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(L){}}}else{var n=void 0;if(zb(d))a:{if(d.length&&qb(d[0])){var p=qB[d[0]];if(p&&(!e.fromContainerExecution||!rB[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,t=r._clear||e.overwriteModelFields,u=l(Object.keys(r)),v=u.next();!v.done;v=u.next()){var x=v.value;x!=="_clear"&&(t&&$o(x),$o(x,r[x]))}$i||($i=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=Tn(),r["gtm.uniqueEventId"]=y,$o("gtm.uniqueEventId",y)),q=HA(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&Wo(!0);var A=d["gtm.uniqueEventId"];if(typeof A==="number"){for(var D=zB[String(A)]||[],E=0;E<D.length;E++)BB.push(LB(D[E]));D.length&&BB.sort(EB);
delete zB[String(A)];A>yB&&(yB=A)}DB=!1}}}return!a}
function MB(){if(G(109)){var a=!Hi.R;}var c=KB();if(G(109)){}try{var e=w[Fi(19)],f=Fi(5),g=e.hide;if(g&&g[f]!==void 0&&
g.end){g[f]=!1;var h=!0,m;for(m in g)if(g.hasOwnProperty(m)&&g[m]===!0){h=!1;break}h&&(g.end(),g.end=null)}}catch(n){Fi(5)}return c}function bB(a){if(yB<a.notBeforeEventId){var b=String(a.notBeforeEventId);zB[b]=zB[b]||[];zB[b].push(a)}else BB.push(LB(a)),BB.sort(EB),Rc(function(){DB||KB()})}function LB(a){return{message:a.message,messageContext:a.messageContext}}
function NB(){function a(f){var g={};if(sB(f)){var h=f;f=sB(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=Cc(Fi(19),[]),c=Rn();c.pruned===!0&&O(83);zB=$A().get();aB();TA(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});xB(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Nn.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new tB(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});AB.push.apply(AB,h);var m=d.apply(b,f),n=Math.max(100,Number(Ji(1,'1000'))||300);if(this.length>n)for(O(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return KB()&&p};var e=b.slice(0).map(function(f){return a(f)});AB.push.apply(AB,e);if(!Hi.R){if(G(109)){}Rc(MB)}}var GB=function(a){return w[Fi(19)].push(a)};function OB(a){GB(a)};function PB(){var a,b=sj(w.location.href);(a=b.hostname+b.pathname)&&Vl("dl",encodeURIComponent(a));var c;var d=Fi(5);if(d){var e=Ei(7)?1:0,f,g=Jj(),h=Ij(g),m=(f=h&&h.context)&&f.fromContainerExecution?1:0,n=f&&f.source||0,p=Fi(6);c=d+";"+p+";"+m+";"+n+";"+e}else c=void 0;var q=c;q&&Vl("tdp",q);var r=Pp(!0);r!==void 0&&Vl("frm",String(r))};var QB={},RB=void 0;
function SB(){if($m()||kk)Vl("csp",function(){return Object.keys(QB).join("~")||void 0},!1),w.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){O(179);var b=Yk(a.effectiveDirective);if(b){var c;var d=Wk(b,a.blockedURI);c=d?Uk[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=
n.value;if(!p.hn){p.hn=!0;if(G(59)){var q={eventId:p.eventId,priorityId:p.priorityId};if($m()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if($m()){var u=fn("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Zm(u)}}}TB(p.endpoint)}}Xk(b,a.blockedURI)}}}}})}
function TB(a){var b=String(a);QB.hasOwnProperty(b)||(QB[b]=!0,Wl("csp",!0),RB===void 0&&G(171)&&(RB=w.setTimeout(function(){if(G(171)){var c=Sl.csp;Sl.csp=!0;Sl.seq=!1;var d=Xl(!1);Sl.csp=c;Sl.seq=!0;Kc(d+"&script=1")}RB=void 0},500)))};var UB=void 0;function VB(){G(236)&&w.addEventListener("pageshow",function(a){a&&(Vl("bfc",function(){return UB?"1":"0"}),a.persisted?(UB=!0,Wl("bfc",!0),Zl()):UB=!1)})};function WB(){var a;var b=Hj();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Vl("pcid",e)};var XB=/^(https?:)?\/\//;
function YB(){var a=Kj();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=gd())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(XB,"")===d.replace(XB,""))){b=g;break a}}O(146)}else O(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Vl("rtg",String(a.canonicalContainerId)),Vl("slo",String(p)),Vl("hlo",a.htmlLoadOrder||"-1"),
Vl("lst",String(a.loadScriptType||"0")))}else O(144)};

function sC(){};var tC=function(){};tC.prototype.toString=function(){return"undefined"};var uC=new tC;function BC(){G(212)&&Xi&&(lg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}}),pA(Fj(),function(a){var b,c;b=a.entityId;c=a.securityGroups;var d="__"+b;return Gz(d,5)||!(!Of[d]||!Of[d][5])||c.includes("cmpPartners")}))};function CC(a,b){function c(g){var h=sj(g),m=mj(h,"protocol"),n=mj(h,"host",!0),p=mj(h,"port"),q=mj(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function DC(a){return EC(a)?1:0}
function EC(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=sd(a,{});sd({arg1:c[d],any_of:void 0},e);if(DC(e))return!0}return!1}switch(a["function"]){case "_cn":return Tg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Og.length;g++){var h=Og[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Pg(b,c);case "_eq":return Ug(b,c);case "_ge":return Vg(b,c);case "_gt":return Xg(b,c);case "_lc":return Qg(b,c);case "_le":return Wg(b,
c);case "_lt":return Yg(b,c);case "_re":return Sg(b,c,a.ignore_case);case "_sw":return Zg(b,c);case "_um":return CC(b,c)}return!1};var FC=function(){this.C=this.gppString=void 0};FC.prototype.reset=function(){this.C=this.gppString=void 0};var GC=new FC;[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var HC=function(a,b,c,d){Xp.call(this);this.ih=b;this.Mf=c;this.Jc=d;this.Xa=new Map;this.jh=0;this.la=new Map;this.Ja=new Map;this.U=void 0;this.H=a};xa(HC,Xp);HC.prototype.M=function(){delete this.C;this.Xa.clear();this.la.clear();this.Ja.clear();this.U&&(Sp(this.H,"message",this.U),delete this.U);delete this.H;delete this.Jc;Xp.prototype.M.call(this)};
var IC=function(a){if(a.C)return a.C;a.Mf&&a.Mf(a.H)?a.C=a.H:a.C=Op(a.H,a.ih);var b;return(b=a.C)!=null?b:null},KC=function(a,b,c){if(IC(a))if(a.C===a.H){var d=a.Xa.get(b);d&&d(a.C,c)}else{var e=a.la.get(b);if(e&&e.sj){JC(a);var f=++a.jh;a.Ja.set(f,{Dh:e.Dh,Vp:e.Rm(c),persistent:b==="addEventListener"});a.C.postMessage(e.sj(c,f),"*")}}},JC=function(a){a.U||(a.U=function(b){try{var c;c=a.Jc?a.Jc(b):void 0;if(c){var d=c.Xq,e=a.Ja.get(d);if(e){e.persistent||a.Ja.delete(d);var f;(f=e.Dh)==null||f.call(e,
e.Vp,c.payload)}}}catch(g){}},Rp(a.H,"message",a.U))};var LC=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},MC=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},NC={Rm:function(a){return a.listener},sj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Dh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},OC={Rm:function(a){return a.listener},sj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Dh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function PC(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,Xq:b.__gppReturn.callId}}
var QC=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Xp.call(this);this.caller=new HC(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},PC);this.caller.Xa.set("addEventListener",LC);this.caller.la.set("addEventListener",NC);this.caller.Xa.set("removeEventListener",MC);this.caller.la.set("removeEventListener",OC);this.timeoutMs=c!=null?c:500};xa(QC,Xp);QC.prototype.M=function(){this.caller.dispose();Xp.prototype.M.call(this)};
QC.prototype.addEventListener=function(a){var b=this,c=Np(function(){a(RC,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);KC(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(SC,!0);return}a(TC,!0)}}})};
QC.prototype.removeEventListener=function(a){KC(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var TC={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},RC={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},SC={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function UC(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){GC.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");GC.C=d}}function VC(){try{var a=new QC(w,{timeoutMs:-1});IC(a.caller)&&a.addEventListener(UC)}catch(b){}};function WC(){var a=[["cv",Fi(1)],["rv",Fi(14)],["tc",Mf.filter(function(c){return c}).length]],b=Ii(15);b&&a.push(["x",b]);Vj()&&a.push(["tag_exp",Vj()]);return a};function XC(a){a.Sc&&(qr=pr=0);return[["cd",""+pr],["cd",""+qr]]};var YC={},ZC={};function Li(a){YC[a]=(YC[a]||0)+1}function Mi(a){ZC[a]=(ZC[a]||0)+1}function $C(a,b){for(var c=[],d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;c.push(f+"."+b[f])}return c.length===0?[]:[[a,c.join("~")]]}function aD(){return $C("bdm",YC)}function bD(){return $C("vcm",ZC)};var cD={},dD={};function eD(a){var b=a.eventId,c=a.Sc,d=[],e=cD[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=dD[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete cD[b],delete dD[b]);return d};function fD(){return!1}function gD(){var a={};return function(b,c,d){}};function hD(){var a=iD;return function(b,c,d){var e=d&&d.event;jD(c);var f=Dh(b)?void 0:1,g=new bb;yb(c,function(r,t){var u=Hd(t,void 0,f);u===void 0&&t!==void 0&&O(44);g.set(r,u)});a.Mb(dg());var h={Fm:rg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Sf:e!==void 0?function(r){e.Kc.Sf(r)}:void 0,Jb:function(){return b},log:function(){},cq:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},jr:!!Gz(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(fD()){var m=gD(),n,p;h.rb={Hj:[],Tf:{},bc:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Bh:Wh()};h.log=function(r){var t=Ea.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=cf(a,h,[b,g]);a.Mb();q instanceof Ha&&(q.type==="return"?q=q.data:q=void 0);return B(q,void 0,f)}}function jD(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;pb(b)&&(a.gtmOnSuccess=function(){Rc(b)});pb(c)&&(a.gtmOnFailure=function(){Rc(c)})};function kD(a){}kD.K="internal.addAdsClickIds";function lD(a,b){var c=this;}lD.publicName="addConsentListener";var mD=!1;function nD(a){for(var b=0;b<a.length;++b)if(mD)try{a[b]()}catch(c){O(77)}else a[b]()}function oD(a,b,c){var d=this,e;if(!I(a)||!lh(b)||!ph(c))throw H(this.getName(),["string","function","string|undefined"],arguments);nD([function(){J(d,"listen_data_layer",a)}]);e=GA().addListener(a,B(b),c===null?void 0:c);return e}oD.K="internal.addDataLayerEventListener";function pD(a,b,c){}pD.publicName="addDocumentEventListener";function qD(a,b,c,d){}qD.publicName="addElementEventListener";function rD(a){return a.J.pb()};function sD(a){}sD.publicName="addEventCallback";
var tD=function(a){return typeof a==="string"?a:String(Tn())},wD=function(a,b){uD(a,"init",!1)||(vD(a,"init",!0),b())},uD=function(a,b,c){var d=xD(a);return Gb(d,b,c)},yD=function(a,b,c,d){var e=xD(a),f=Gb(e,b,d);e[b]=c(f)},vD=function(a,b,c){xD(a)[b]=c},xD=function(a){var b=On("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},zD=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":dd(a,"className"),"gtm.elementId":a.for||Sc(a,"id")||"","gtm.elementTarget":a.formTarget||
dd(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||dd(a,"href")||a.src||a.code||a.codebase||"";return d};
function HD(a){}HD.K="internal.addFormAbandonmentListener";function ID(a,b,c,d){}
ID.K="internal.addFormData";var JD={},KD=[],LD={},MD=0,ND=0;
function UD(a,b){}UD.K="internal.addFormInteractionListener";
function aE(a,b){}aE.K="internal.addFormSubmitListener";
function fE(a){}fE.K="internal.addGaSendListener";function gE(a){if(!a)return{};var b=a.cq;return Fz(b.type,b.index,b.name)}function hE(a){return a?{originatingEntity:gE(a)}:{}};function pE(a){var b=Nn.zones;return b?b.getIsAllowedFn(Gj(),a):function(){return!0}}function qE(){var a=Nn.zones;a&&a.unregisterChild(Gj())}
function rE(){rA(Fj(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Nn.zones;return c?c.isActive(Gj(),b):!0});pA(Fj(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return pE(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var sE=function(a,b){this.tagId=a;this.canonicalId=b};
function tE(a,b){var c=this;return a}tE.K="internal.loadGoogleTag";function uE(a){return new zd("",function(b){var c=this.evaluate(b);if(c instanceof zd)return new zd("",function(){var d=Ea.apply(0,arguments),e=this,f=sd(rD(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.J.nb();h.Nd(f);return c.Kb.apply(c,[h].concat(Aa(g)))})})};function vE(a,b,c){var d=this;}vE.K="internal.addGoogleTagRestriction";var wE={},xE=[];
function EE(a,b){}
EE.K="internal.addHistoryChangeListener";function FE(a,b,c){}FE.publicName="addWindowEventListener";function GE(a,b){return!0}GE.publicName="aliasInWindow";function HE(a,b,c){if(!I(a)||!I(b))throw H(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Ap(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!rd(e[d[f]]))throw Error("apendRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}if(e[d[f]]===void 0)e[d[f]]=[];else if(!Array.isArray(e[d[f]]))throw Error("appendRemoteConfigParameter failed, destination is not an array: "+
d[f]);e[d[f]].push(B(c,this.J));}HE.K="internal.appendRemoteConfigParameter";function IE(a){var b;return b}
IE.publicName="callInWindow";function JE(a){}JE.publicName="callLater";function KE(a){}KE.K="callOnDomReady";function LE(a){}LE.K="callOnWindowLoad";function ME(a,b){var c;return c}ME.K="internal.computeGtmParameter";function NE(a,b){var c=this;}NE.K="internal.consentScheduleFirstTry";function OE(a,b){var c=this;}OE.K="internal.consentScheduleRetry";function PE(a){var b;return b}PE.K="internal.copyFromCrossContainerData";function QE(a,b){var c;var d=Hd(c,this.J,Dh(rD(this).Jb())?2:1);d===void 0&&c!==void 0&&O(45);return d}QE.publicName="copyFromDataLayer";
function RE(a){var b=void 0;return b}RE.K="internal.copyFromDataLayerCache";function SE(a){var b;return b}SE.publicName="copyFromWindow";function TE(a){var b=void 0;return Hd(b,this.J,1)}TE.K="internal.copyKeyFromWindow";var UE=function(a){return a===gl.Z.Ha&&yl[a]===fl.Ka.ue&&!vn(K.m.aa)};var VE=function(){return"0"},WE=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];G(102)&&b.push("gbraid");return tj(a,b,"0")};var XE={},YE={},ZE={},$E={},aF={},bF={},cF={},dF={},eF={},fF={},gF={},hF={},iF={},jF={},kF={},lF={},mF={},nF={},oF={},pF={},qF={},rF={},sF={},tF={},uF={},vF={},wF=(vF[K.m.Ma]=(XE[2]=[UE],XE),vF[K.m.vf]=(YE[2]=[UE],YE),vF[K.m.ff]=(ZE[2]=[UE],ZE),vF[K.m.kl]=($E[2]=[UE],$E),vF[K.m.ml]=(aF[2]=[UE],aF),vF[K.m.nl]=(bF[2]=[UE],bF),vF[K.m.ol]=(cF[2]=[UE],cF),vF[K.m.pl]=(dF[2]=[UE],dF),vF[K.m.Eb]=(eF[2]=[UE],eF),vF[K.m.wf]=(fF[2]=[UE],fF),vF[K.m.xf]=(gF[2]=[UE],gF),vF[K.m.yf]=(hF[2]=[UE],hF),vF[K.m.zf]=(iF[2]=
[UE],iF),vF[K.m.Af]=(jF[2]=[UE],jF),vF[K.m.Bf]=(kF[2]=[UE],kF),vF[K.m.Cf]=(lF[2]=[UE],lF),vF[K.m.Df]=(mF[2]=[UE],mF),vF[K.m.tb]=(nF[1]=[UE],nF),vF[K.m.Xc]=(oF[1]=[UE],oF),vF[K.m.ed]=(pF[1]=[UE],pF),vF[K.m.be]=(qF[1]=[UE],qF),vF[K.m.Re]=(rF[1]=[function(a){return G(102)&&UE(a)}],rF),vF[K.m.fd]=(sF[1]=[UE],sF),vF[K.m.Ea]=(tF[1]=[UE],tF),vF[K.m.Wa]=(uF[1]=[UE],uF),vF),xF={},yF=(xF[K.m.tb]=VE,xF[K.m.Xc]=VE,xF[K.m.ed]=VE,xF[K.m.be]=VE,xF[K.m.Re]=VE,xF[K.m.fd]=function(a){if(!rd(a))return{};var b=sd(a,
null);delete b.match_id;return b},xF[K.m.Ea]=WE,xF[K.m.Wa]=WE,xF),zF={},AF={},BF=(AF[Q.A.Qa]=(zF[2]=[UE],zF),AF),CF={};var DF=function(a,b,c,d){this.C=a;this.M=b;this.R=c;this.U=d};DF.prototype.getValue=function(a){a=a===void 0?gl.Z.Gb:a;if(!this.M.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.U(this.C):this.C};DF.prototype.H=function(){return pd(this.C)==="array"||rd(this.C)?sd(this.C,null):this.C};
var EF=function(){},FF=function(a,b){this.conditions=a;this.C=b},GF=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new DF(c,e,g,a.C[b]||EF)},HF,IF;var JF,KF=!1;function LF(){KF=!0;if(G(218)&&Di(52,!1))JF=productSettings,productSettings=void 0;else{}JF=JF||{}}function MF(a){KF||LF();return JF[a]};var NF=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;T(this,g,d[g])}},Cu=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,Q.A.Qf))},V=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(HF!=null||(HF=new FF(wF,yF)),e=GF(HF,b,c));d[b]=e};
NF.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return V(this,a,b),!0;if(!rd(c))return!1;V(this,a,ma(Object,"assign").call(Object,c,b));return!0};var OF=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
NF.prototype.copyToHitData=function(a,b,c){var d=P(this.D,a);d===void 0&&(d=b);if(qb(d)&&c!==void 0&&G(92))try{d=c(d)}catch(e){}d!==void 0&&V(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===Q.A.Qf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,Q.A.Qf))},T=function(a,b,c){var d=a.metadata,e;c===void 0?e=c:(IF!=null||(IF=new FF(BF,CF)),e=GF(IF,b,c));d[b]=e},PF=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},Su=function(a,b,c){var d=MF(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c},QF=function(a){for(var b=new NF(a.target,a.eventName,a.D),c=OF(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;V(b,f,c[f])}for(var g=PF(a),h=l(Object.keys(g)),m=h.next();!m.done;m=h.next()){var n=m.value;T(b,n,g[n])}b.isAborted=a.isAborted;return b},RF=function(a){var b=a.D,c=b.eventId,d=b.priorityId;return d?c+"_"+d:String(c)};
NF.prototype.accept=function(){var a=Ml(Hl.X.mi,{}),b=RF(this),c=this.target.destinationId;a[b]||(a[b]={});a[b][c]=Fj();var d=Hl.X.mi;if(Il(d)){var e;(e=Jl(d))==null||e.notify()}};NF.prototype.hasBeenAccepted=function(a){var b=Ll(Hl.X.mi);if(!b)return!1;var c=b[RF(this)];return c?c[a!=null?a:this.target.destinationId]!==void 0:!1};function SF(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Cu(a,b)},setHitData:function(b,c){V(a,b,c)},setHitDataIfNotDefined:function(b,c){Cu(a,b)===void 0&&V(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){T(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return P(a.D,b)},ob:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return rd(c)?a.mergeHitDataForKey(b,c):!1},accept:function(){a.accept()},hasBeenAccepted:function(b){return a.hasBeenAccepted(b)}}};function TF(a,b){var c;if(!ih(a)||!jh(b))throw H(this.getName(),["Object","Object|undefined"],arguments);var d=B(b)||{},e=B(a,this.J,1).ob(),f=e.D;d.omitEventContext&&(f=So(new Ho(e.D.eventId,e.D.priorityId)));var g=new NF(e.target,e.eventName,f);if(!d.omitHitData)for(var h=OF(e),m=l(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;V(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=PF(e),r=l(Object.keys(q)),t=r.next();!t.done;t=
r.next()){var u=t.value;T(g,u,q[u])}g.isAborted=e.isAborted;c=Hd(SF(g),this.J,1);return c}TF.K="internal.copyPreHit";function UF(a,b){var c=null;return Hd(c,this.J,2)}UF.publicName="createArgumentsQueue";function VF(a){return Hd(function(c){var d=Pz();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
Pz(),n=m&&m.getByName&&m.getByName(f);return(new w.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.J,1)}VF.K="internal.createGaCommandQueue";function WF(a){return Hd(function(){if(!pb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.J,
Dh(rD(this).Jb())?2:1)}WF.publicName="createQueue";function XF(a,b){var c=null;if(!I(a)||!ph(b))throw H(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new Ed(new RegExp(a,d))}catch(e){}return c}XF.K="internal.createRegex";function YF(a){if(!ih(a))throw H(this.getName(),["Object"],arguments);for(var b=a.ya(),c=l(b),d=c.next();!d.done;d=c.next()){var e=d.value;e!==K.m.jc&&J(this,"access_consent",e,"write")}var f=rD(this),g=f.eventId,h=hE(f),m=B(a);ZA(VA("consent","declare",m),g,h);}YF.K="internal.declareConsentState";function ZF(a){var b="";return b}ZF.K="internal.decodeUrlHtmlEntities";function $F(a,b,c){var d;return d}$F.K="internal.decorateUrlWithGaCookies";function aG(){}aG.K="internal.deferCustomEvents";function bG(a){return cG?z.querySelector(a):null}
function dG(a,b){if(!cG)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var eG=!1;
if(z.querySelectorAll)try{var fG=z.querySelectorAll(":root");fG&&fG.length==1&&fG[0]==z.documentElement&&(eG=!0)}catch(a){}var cG=eG;function gG(){var a=w.screen;return{width:a?a.width:0,height:a?a.height:0}}
function hG(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!w.getComputedStyle)return!0;var c=w.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=w.getComputedStyle(d,null))}return!1}
var CG=function(a){a=a||{fg:!0,gg:!0,Ej:void 0};a.Yb=a.Yb||{email:!0,phone:!1,address:!1};var b=qG(a),c=rG[b];if(c&&Fb()-c.timestamp<200)return c.result;var d=sG(),e=d.status,f=[],g,h,m=[];if(!G(33)){if(a.Yb&&a.Yb.email){var n=tG(d.elements);f=uG(n,a&&a.Vf);g=vG(f);n.length>10&&(e="3")}!a.Ej&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(wG(f[p],!!a.fg,!!a.gg));m=m.slice(0,10)}else if(a.Yb){}g&&(h=wG(g,!!a.fg,!!a.gg));var L={elements:m,Ym:h,status:e};rG[b]={timestamp:Fb(),result:L};
return L},DG=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},FG=function(a){var b=EG(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},EG=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():void 0}},wG=function(a,b,c){var d=a.element,
e={qa:a.qa,type:a.ra,tagName:d.tagName};b&&(e.querySelector=GG(d));c&&(e.isVisible=!hG(d));return e},qG=function(a){var b=!(a==null||!a.fg)+"."+!(a==null||!a.gg);a&&a.Vf&&a.Vf.length&&(b+="."+a.Vf.join("."));a&&a.Yb&&(b+="."+a.Yb.email+"."+a.Yb.phone+"."+a.Yb.address);return b},vG=function(a){if(a.length!==0){var b;b=HG(a,function(c){return!IG.test(c.qa)});b=HG(b,function(c){return c.element.tagName.toUpperCase()==="INPUT"});b=HG(b,function(c){return!hG(c.element)});return b[0]}},uG=function(a,b){b&&
b.length!==0||(b=[]);for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&dG(a[d].element,g)){e=!1;break}}a[d].ra===BG.Nb&&G(227)&&(IG.test(a[d].qa)||a[d].element.tagName.toUpperCase()==="A"&&a[d].element.hasAttribute("href")&&a[d].element.getAttribute("href").indexOf("mailto:")!==-1)&&(e=!1);e&&c.push(a[d])}return c},HG=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},GG=function(a){var b;if(a===z.body)b="body";else{var c;if(a.id)c="#"+
a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===a){e=g+1;break a}e=-1}else e=1}d=GG(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},tG=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(JG);if(f){var g=f[0],h;if(w.location){var m=oj(w.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=
!1;h||b.push({element:d,qa:g,ra:BG.Nb})}}}return b},sG=function(){var a=[],b=z.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(KG.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(LG.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||G(33)&&MG.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},JG=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,
IG=/support|noreply/i,KG="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),LG=["BR"],NG=yi(Ji(36,''),2),BG={Nb:"1",Cd:"2",sd:"3",zd:"4",Le:"5",Of:"6",hh:"7",Ni:"8",Ih:"9",Hi:"10"},rG={},MG=["INPUT","SELECT"],OG=EG(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
function AH(a){var b;J(this,"detect_user_provided_data","auto");var c=B(a)||{},d=CG({fg:!!c.includeSelector,gg:!!c.includeVisibility,Vf:c.excludeElementSelectors,Yb:c.fieldFilters,Ej:!!c.selectMultipleElements});b=new bb;var e=new vd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(BH(f[g]));d.Ym!==void 0&&b.set("preferredEmailElement",BH(d.Ym));b.set("status",d.status);if(G(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(yc&&
yc.userAgent||"")){}return b}
var CH=function(a){switch(a){case BG.Nb:return"email";case BG.Cd:return"phone_number";case BG.sd:return"first_name";case BG.zd:return"last_name";case BG.Ni:return"street";case BG.Ih:return"city";case BG.Hi:return"region";case BG.Of:return"postal_code";case BG.Le:return"country"}},BH=function(a){var b=new bb;b.set("userData",a.qa);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(G(33)){}else switch(a.type){case BG.Nb:b.set("type","email")}return b};AH.K="internal.detectUserProvidedData";
function FH(a,b){return f}FH.K="internal.enableAutoEventOnClick";
function NH(a,b){return p}NH.K="internal.enableAutoEventOnElementVisibility";function OH(){}OH.K="internal.enableAutoEventOnError";var PH={},QH=[],RH={},SH=0,TH=0;
function ZH(a,b){var c=this;return d}ZH.K="internal.enableAutoEventOnFormInteraction";
function dI(a,b){var c=this;return f}dI.K="internal.enableAutoEventOnFormSubmit";
function iI(){var a=this;}iI.K="internal.enableAutoEventOnGaSend";var jI={},kI=[];
var mI=function(a,b){var c=""+b;if(jI[c])jI[c].push(a);else{var d=[a];jI[c]=d;var e=lI("gtm.historyChange-v2"),f=-1;kI.push(function(g){f>=0&&w.clearTimeout(f);b?f=w.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},lI=function(a){var b=w.location.href,c={source:null,state:w.history.state||null,url:pj(sj(b)),cb:mj(sj(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.cb!==d.cb){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.cb,
"gtm.newUrlFragment":d.cb,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;GB(h)}}},nI=function(a,b){var c=w.history,d=c[a];if(pb(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=w.location.href;b({source:a,state:e,url:pj(sj(h)),cb:mj(sj(h),"fragment")})}}catch(e){}},pI=function(a){w.addEventListener("popstate",function(b){var c=oI(b);a({source:"popstate",state:b.state,url:pj(sj(c)),cb:mj(sj(c),
"fragment")})})},qI=function(a){w.addEventListener("hashchange",function(b){var c=oI(b);a({source:"hashchange",state:null,url:pj(sj(c)),cb:mj(sj(c),"fragment")})})},oI=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||w.location.href};
function rI(a,b){var c=this;if(!jh(a))throw H(this.getName(),["Object|undefined","any"],arguments);nD([function(){J(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!uD(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<kI.length;n++)kI[n](m)},f=tD(b),mI(f,e),vD(d,"reg",mI)):g=lI("gtm.historyChange");qI(g);pI(g);nI("pushState",
g);nI("replaceState",g);vD(d,"init",!0)}else if(d==="ehl"){var h=uD(d,"reg");h&&(f=tD(b),h(f,e))}d==="hl"&&(f=void 0);return f}rI.K="internal.enableAutoEventOnHistoryChange";var sI=["http://","https://","javascript:","file://"];
function wI(a,b){var c=this;return h}wI.K="internal.enableAutoEventOnLinkClick";var xI,yI;
function JI(a,b){var c=this;return d}JI.K="internal.enableAutoEventOnScroll";function KI(a){return function(){if(a.limit&&a.wj>=a.limit)a.yh&&w.clearInterval(a.yh);else{a.wj++;var b=Fb();GB({event:a.eventName,"gtm.timerId":a.yh,"gtm.timerEventNumber":a.wj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.on,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.on,"gtm.triggers":a.Hr})}}}
function LI(a,b){
return f}LI.K="internal.enableAutoEventOnTimer";var sc=Ca(["data-gtm-yt-inspected-"]),NI=["www.youtube.com","www.youtube-nocookie.com"],OI,PI=!1;
function ZI(a,b){var c=this;return e}ZI.K="internal.enableAutoEventOnYouTubeActivity";PI=!1;function $I(a,b){if(!I(a)||!jh(b))throw H(this.getName(),["string","Object|undefined"],arguments);var c=b?B(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Kh(f,c);return e}$I.K="internal.evaluateBooleanExpression";var aJ;function bJ(a){var b=!1;return b}bJ.K="internal.evaluateMatchingRules";var mJ=function(a,b){var c=a.D;if(b===void 0?0:b){var d=c.getMergedValues(K.m.Da);Ob(d)&&V(a,K.m.Qg,Ob(d))}var e=c.getMergedValues(K.m.Da,1,Nm(xp.C[K.m.Da])),f=c.getMergedValues(K.m.Da,2),g=Ob(e,"."),h=Ob(f,".");g&&V(a,K.m.Bc,g);h&&V(a,K.m.Ac,h)};var nJ="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function oJ(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function pJ(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=ma(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function qJ(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function rJ(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function sJ(a){if(!rJ(a))return null;var b=oJ(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(nJ).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var tJ=function(a){var b={};b[K.m.wf]=a.architecture;b[K.m.xf]=a.bitness;a.fullVersionList&&(b[K.m.yf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[K.m.zf]=a.mobile?"1":"0";b[K.m.Af]=a.model;b[K.m.Bf]=a.platform;b[K.m.Cf]=a.platformVersion;b[K.m.Df]=a.wow64?"1":"0";return b},uJ=function(a){var b=0,c=function(h,m){try{a(h,m)}catch(n){}},d=w,e=pJ(d);if(e)c(e);else{var f=qJ(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),
1E3);var g=d.setTimeout(function(){c.jg||(c.jg=!0,O(106),c(null,Error("Timeout")))},b);f.then(function(h){c.jg||(c.jg=!0,O(104),d.clearTimeout(g),c(h))}).catch(function(h){c.jg||(c.jg=!0,O(105),d.clearTimeout(g),c(null,h))})}else c(null)}},wJ=function(){var a=w;if(rJ(a)&&(vJ=Fb(),!qJ(a))){var b=sJ(a);b&&(b.then(function(){O(95)}),b.catch(function(){O(96)}))}},vJ;var xJ=function(a){if(!rJ(w))O(87);else if(vJ!==void 0){O(85);var b=pJ(w);if(b){if(b)for(var c=tJ(b),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;V(a,f,c[f])}}else O(86)}};var zJ=function(a){var b=yJ[a.target.destinationId];if(!a.isAborted&&b)for(var c=SF(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},AJ=function(a,b){var c=yJ[a];c||(c=yJ[a]=[]);c.push(b)},yJ={};var BJ=function(a){zJ(a);};function CJ(){var a=w.__uspapi;if(pb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var DJ=function(a){if(a.eventName!==K.m.na)a.isAborted=!0;else if(G(24)){var b=vn(Dv);T(a,Q.A.ye,P(a.D,K.m.Ia)!=null&&P(a.D,K.m.Ia)!==!1&&!b);var c=zu(a),d=P(a.D,K.m.eb)!==!1;d||V(a,K.m.Uh,"1");var e=ot(c.prefix),f=R(a,Q.A.gh);if(!R(a,Q.A.ja)&&!R(a,Q.A.Rf)&&!R(a,Q.A.xe)){var g=P(a.D,K.m.Db),h=P(a.D,K.m.Va)||{};Au({Ae:d,Ee:h,Ie:g,Mc:c});if(!f&&!hu(e)){a.isAborted=!0;return}}if(f)a.isAborted=!0;else{V(a,K.m.gd,K.m.Wc);if(R(a,Q.A.ja))V(a,K.m.gd,K.m.Xn),V(a,K.m.ja,"1");else if(R(a,Q.A.Rf))V(a,K.m.gd,
K.m.jo);else if(R(a,Q.A.xe))V(a,K.m.gd,K.m.fo);else{var m=Ht();V(a,K.m.Xc,m.gclid);V(a,K.m.ed,m.dclid);V(a,K.m.vk,m.gclsrc);Cu(a,K.m.Xc)||Cu(a,K.m.ed)||(V(a,K.m.be,m.wbraid),V(a,K.m.Re,m.gbraid));V(a,K.m.Wa,Mt());V(a,K.m.Ea,mu());if(G(27)&&Bc){var n=mj(sj(Bc),"host");n&&V(a,K.m.fl,n)}if(!R(a,Q.A.xe)){var p=ju();V(a,K.m.Pe,p.Ce);V(a,K.m.Qe,p.Mm)}V(a,K.m.Dc,Pp(!0));var q=Lv();Kv(q)&&V(a,K.m.hd,"1");V(a,K.m.yk,lv());ds(!1)._up==="1"&&V(a,K.m.Sk,"1")}fm=!0;V(a,K.m.Cb);V(a,K.m.Yc);b&&(V(a,K.m.Cb,Mu()),
d&&(rs(c),V(a,K.m.Yc,ps[ss(c.prefix)])));V(a,K.m.oc);V(a,K.m.tb);if(!Cu(a,K.m.Xc)&&!Cu(a,K.m.ed)&&ev(e)){var r=mt(c);r.length>0&&V(a,K.m.oc,r.join("."))}else if(!Cu(a,K.m.be)&&b){var t=kt(e+"_aw");t.length>0&&V(a,K.m.tb,t.join("."))}V(a,K.m.Vk,fd());a.D.isGtmEvent&&(a.D.C[K.m.Pb]=xp.C[K.m.Pb]);yq(a.D)?V(a,K.m.Bd,!1):V(a,K.m.Bd,!0);T(a,Q.A.tg,!0);var u=CJ();u!==void 0&&V(a,K.m.Ef,u||"error");var v=pq();v&&V(a,K.m.he,v);if(G(137))try{var x=Intl.DateTimeFormat().resolvedOptions().timeZone;V(a,K.m.ji,
x||"-")}catch(E){V(a,K.m.ji,"e")}var y=oq();y&&V(a,K.m.ne,y);var A=GC.gppString;A&&V(a,K.m.lf,A);var D=GC.C;D&&V(a,K.m.kf,D);T(a,Q.A.xa,!1)}}else a.isAborted=!0};
var EJ=function(a,b,c){b=b===void 0?!0:b;c=c===void 0?{}:c;if(a.eventName===K.m.nc&&!a.D.isGtmEvent){var d=P(a.D,K.m.hf);if(typeof d==="function"&&!R(a,Q.A.ja)){var e=String(P(a.D,K.m.jf)),f=e;c[e]&&(f=c[e]);var g=Cu(a,f)||P(a.D,e);if(b){if(typeof d==="function")if(e===K.m.tb&&g!==void 0){var h=g.split(".");h.length===0?d(void 0):h.length===1?d(h[0]):d(h)}else if(e===K.m.Ko&&G(258)){var m,n={};vn(Dv)&&(n.auid=Cu(a,K.m.Yc));var p=Lv();if(Kv(p))n.gad_source=p.Ce,n.gad_campaignid=p.rh,n.session_start_time_usec=
(Date.now()*1E3).toString(),n.landing_page_url=w.location.href,n.landing_page_referrer=z.referrer,n.landing_page_user_agent=yc.userAgent;else{var q=R(a,Q.A.Ca);n.gad_source=cv(q.prefix).Yf}m=btoa(JSON.stringify(n)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");d(m)}else d(g)}else d(g)}a.isAborted=!0}};var FJ=function(a){if(R(a,Q.A.Rd)&&vn(Dv)&&(R(a,Q.A.ba)!==N.N.Hb||!G(4))){var b=R(a,Q.A.Ca),c=R(a,Q.A.ba)!==N.N.Hb&&R(a,Q.A.ba)!==N.N.Wb&&R(a,Q.A.ba)!==N.N.zb&&a.eventName!==K.m.nc;rs(b,c);V(a,K.m.Yc,ps[ss(b.prefix)])}};var GJ=function(a){T(a,Q.A.Rd,P(a.D,K.m.eb)!==!1);T(a,Q.A.Ca,zu(a));T(a,Q.A.Dd,P(a.D,K.m.Ia)!=null&&P(a.D,K.m.Ia)!==!1);T(a,Q.A.Tc,yq(a.D))};function HJ(a){var b=nb("GTAG_EVENT_FEATURE_CHANNEL");b&&(V(a,K.m.nf,b),lb())};var IJ=function(a){R(a,Q.A.Tc)?V(a,K.m.Bd,"0"):V(a,K.m.Bd,"1")};var JJ=function(a,b){if(b===void 0||b){var c=CJ();c!==void 0&&V(a,K.m.Ef,c||"error")}var d=pq();d&&V(a,K.m.he,d);var e=oq();e&&V(a,K.m.ne,e)};var KJ=function(a){ds(!1)._up==="1"&&V(a,K.m.fi,"1")};
var LJ=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},MJ=function(a,b,c){if(a!==void 0)return Array.isArray(a)?a.map(function(){return{mode:"m",location:b,selector:c}}):{mode:"m",location:b,selector:c}},NJ=function(a,b,c,d,e){if(!c)return!1;for(var f=String(c.value),g,h=void 0,m=f.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(E){return E.trim()}).filter(function(E){return E&&!Kb(E,"#")&&!Kb(E,".")}),n=0;n<m.length;n++){var p=m[n];if(Kb(p,"dataLayer."))g=Xo(p.substring(10)),
h=MJ(g,"d",p);else{var q=p.split(".");g=w[q.shift()];for(var r=0;r<q.length;r++)g=g&&g[q[r]];h=MJ(g,"j",p)}if(g!==void 0)break}if(g===void 0&&cG)try{var t=cG?z.querySelectorAll(f):null;if(t&&t.length>0){g=[];for(var u=0;u<t.length&&u<(b==="email"||b==="phone_number"?5:1);u++)g.push(Tc(t[u])||Db(t[u].value));g=g.length===1?g[0]:g;h=MJ(g,"c",f)}}catch(E){O(149)}if(G(60)){for(var v,x,y=0;y<m.length;y++){var A=m[y];v=Xo(A);if(v!==void 0){x=MJ(v,"d",A);break}}var D=g!==void 0;e[b]=LJ(v!==void 0,D);D||
(g=v,h=x)}return g?(a[b]=g,d&&h&&(d[b]=h),!0):!1},OJ={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};
var PJ=function(a,b){b=b===void 0?!1:b;if(Su(a,"ccd_add_1p_data",!1)&&vn(Dv)){var c=a.D.M[K.m.ql];if(rd(c)&&c.enable_code){var d=P(a.D,K.m.yb);if(d===null)T(a,Q.A.Bm,null);else{if(c.enable_code&&rd(d)){if(G(178)&&d){Jw(Hw,d);for(var e=tb(d.address),f=0;f<e.length;f++){var g=e[f];g&&Jw(Iw,g)}var h=d.home_address;h&&Jw(Iw,h)}T(a,Q.A.Bm,d)}if(rd(c.selectors)){var m={},n=Q.A.rp,p;var q=c.selectors,r=b?m:void 0,t=G(178);r=r===void 0?{}:r;t=t===void 0?!1:t;if(q){var u={},v=!1,x={};v=NJ(u,"email",q.email,
x,r)||v;v=NJ(u,"phone_number",q.phone,x,r)||v;u.address=[];for(var y=q.name_and_address||[],A=0;A<y.length;A++){var D={},E={};v=NJ(D,"first_name",y[A].first_name,E,r)||v;v=NJ(D,"last_name",y[A].last_name,E,r)||v;v=NJ(D,"street",y[A].street,E,r)||v;v=NJ(D,"city",y[A].city,E,r)||v;v=NJ(D,"region",y[A].region,E,r)||v;v=NJ(D,"country",y[A].country,E,r)||v;v=NJ(D,"postal_code",y[A].postal_code,E,r)||v;u.address.push(D);t&&(D._tag_metadata=E)}t&&(u._tag_metadata=x);p=v?u:void 0}else p=void 0;T(a,n,p);if(b){for(var L=
a.mergeHitDataForKey,F=K.m.Cc,M,U=[],fa=Object.keys(OJ),S=0;S<fa.length;S++){var Z=fa[S],ra=OJ[Z],ka=void 0,da=(ka=m[Z])!=null?ka:"0";U.push(ra+"-"+da)}M=U.join("~");L.call(a,F,{ec_data_layer:M})}}}}}};function qK(){return qq(7)&&qq(9)&&qq(10)};function lL(a,b,c,d){}lL.K="internal.executeEventProcessor";function mL(a){var b;return Hd(b,this.J,1)}mL.K="internal.executeJavascriptString";function nL(a){var b;return b};function oL(a){var b="";return b}oL.K="internal.generateClientId";function pL(a){var b={};return Hd(b)}pL.K="internal.getAdsCookieWritingOptions";function qL(a,b){var c=!1;return c}qL.K="internal.getAllowAdPersonalization";function rL(){var a;return a}rL.K="internal.getAndResetEventUsage";function sL(a,b){b=b===void 0?!0:b;var c;return c}sL.K="internal.getAuid";var tL=null;
function uL(){var a=new bb;J(this,"read_container_data"),G(49)&&tL?a=tL:(a.set("containerId",'G-CSLL4ZEK4L'),a.set("version",'5'),a.set("environmentName",''),a.set("debugMode",sg),a.set("previewMode",tg.qn),a.set("environmentMode",tg.Zp),a.set("firstPartyServing",fj()||Hi.C),a.set("containerUrl",Bc),a.Ra(),G(49)&&(tL=a));return a}
uL.publicName="getContainerVersion";function vL(a,b){b=b===void 0?!0:b;var c;return c}vL.publicName="getCookieValues";function wL(){var a="";return a}wL.K="internal.getCorePlatformServicesParam";function xL(){return tm()}xL.K="internal.getCountryCode";function yL(){var a=[];a=Ej();return Hd(a)}yL.K="internal.getDestinationIds";function zL(a){var b=new bb;return b}zL.K="internal.getDeveloperIds";function AL(a){var b;return b}AL.K="internal.getEcsidCookieValue";function BL(a,b){var c=null;return c}BL.K="internal.getElementAttribute";function CL(a){var b=null;return b}CL.K="internal.getElementById";function DL(a){var b="";return b}DL.K="internal.getElementInnerText";function EL(a,b){var c=null;return Hd(c)}EL.K="internal.getElementProperty";function FL(a){var b;return b}FL.K="internal.getElementValue";function GL(a){var b=0;return b}GL.K="internal.getElementVisibilityRatio";function HL(a){var b=null;return b}HL.K="internal.getElementsByCssSelector";
function IL(a){var b;if(!I(a))throw H(this.getName(),["string"],arguments);J(this,"read_event_data",a);var c;a:{var d=a,e=rD(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var x=[],y="",A=l(n),D=A.next();!D.done;D=
A.next()){var E=D.value;E===m?(x.push(y),y=""):y=E===g?y+"\\":E===h?y+".":y+E}y&&x.push(y);for(var L=l(x),F=L.next();!F.done;F=L.next()){if(f==null){c=void 0;break a}f=f[F.value]}c=f}else c=void 0}b=Hd(c,this.J,1);return b}IL.K="internal.getEventData";function JL(a){var b=null;if(!I(a))throw H(this.getName(),["string"],arguments);J(this,"read_dom_elements","css",a);try{var c=bG(a);c&&(b=new Ed(c))}catch(d){return null}return b}JL.K="internal.getFirstElementByCssSelector";var KL={};KL.disableUserDataWithoutCcd=G(223);KL.enableDecodeUri=G(92);KL.enableGaAdsConversions=G(122);KL.enableGaAdsConversionsClientId=G(121);KL.enableOverrideAdsCps=G(170);KL.enableUrlDecodeEventUsage=G(139);function LL(){return Hd(KL)}LL.K="internal.getFlags";function ML(){var a;return a}ML.K="internal.getGsaExperimentId";function NL(){return new Ed(uC)}NL.K="internal.getHtmlId";function OL(a){var b;return b}OL.K="internal.getIframingState";function PL(a,b){var c={};return Hd(c)}PL.K="internal.getLinkerValueFromLocation";function QL(){var a=new bb;return a}QL.K="internal.getPrivacyStrings";function RL(a,b){var c;if(!I(a)||!I(b))throw H(this.getName(),["string","string"],arguments);var d=MF(a)||{};c=Hd(d[b],this.J);return c}RL.K="internal.getProductSettingsParameter";function SL(a,b){var c;return c}SL.publicName="getQueryParameters";function TL(a,b){var c;return c}TL.publicName="getReferrerQueryParameters";function UL(a){var b="";return b}UL.publicName="getReferrerUrl";function VL(){return um()}VL.K="internal.getRegionCode";function WL(a,b){var c;return c}WL.K="internal.getRemoteConfigParameter";function XL(){var a=new bb;a.set("width",0);a.set("height",0);return a}XL.K="internal.getScreenDimensions";function YL(){var a="";return a}YL.K="internal.getTopSameDomainUrl";function ZL(){var a="";return a}ZL.K="internal.getTopWindowUrl";function $L(a){var b="";return b}$L.publicName="getUrl";function aM(){J(this,"get_user_agent");return yc.userAgent}aM.K="internal.getUserAgent";function bM(){var a;return a?Hd(tJ(a)):a}bM.K="internal.getUserAgentClientHints";var dM=function(a){var b=a.eventName===K.m.Wc&&sl()&&Pv(a),c=R(a,Q.A.Wl),d=R(a,Q.A.Qj),e=R(a,Q.A.If),f=R(a,Q.A.te),g=R(a,Q.A.wg),h=R(a,Q.A.Sd),m=R(a,Q.A.xg),n=R(a,Q.A.yg),p=!!Ov(a)||!!R(a,Q.A.Oh);return!(!$c()&&yc.sendBeacon===void 0||e||p||f||g||h||n||m||b||c||!d&&cM)},cM=!1;
var eM=function(a){var b=0,c=0;return{start:function(){b=Fb()},stop:function(){c=this.get()},get:function(){var d=0;a.nj()&&(d=Fb()-b);return d+c}}},fM=function(){this.C=void 0;this.H=0;this.isActive=this.isVisible=this.M=!1;this.U=this.R=void 0};k=fM.prototype;k.Zo=function(a){var b=this;if(!this.C){this.M=z.hasFocus();this.isVisible=!z.hidden;this.isActive=!0;var c=function(e,f,g){Pc(e,f,function(h){b.C.stop();g(h);b.nj()&&b.C.start()})},d=w;c(d,"focus",function(){b.M=!0});c(d,"blur",function(){b.M=
!1});c(d,"pageshow",function(e){b.isActive=!0;e.persisted&&O(56);b.U&&b.U()});c(d,"pagehide",function(){b.isActive=!1;b.R&&b.R()});c(z,"visibilitychange",function(){b.isVisible=!z.hidden});Pv(a)&&!Ec()&&c(d,"beforeunload",function(){cM=!0});this.Cj(!0);this.H=0}};k.Cj=function(a){if((a===void 0?0:a)||this.C)this.H+=this.uh(),this.C=eM(this),this.nj()&&this.C.start()};k.Gr=function(a){var b=this.uh();b>0&&V(a,K.m.Jg,b)};k.yq=function(a){V(a,K.m.Jg);this.Cj();this.H=0};k.nj=function(){return this.M&&
this.isVisible&&this.isActive};k.mq=function(){return this.H+this.uh()};k.uh=function(){return this.C&&this.C.get()||0};k.ir=function(a){this.R=a};k.fn=function(a){this.U=a};var gM=function(a){jb("GA4_EVENT",a)};var hM=function(a){var b=R(a,Q.A.xl);if(Array.isArray(b))for(var c=0;c<b.length;c++)gM(b[c]);var d=nb("GA4_EVENT");d&&V(a,"_eu",d)};function iM(){var a=w;return a.gaGlobal=a.gaGlobal||{}}function jM(){var a=iM();a.hid=a.hid||vb();return a.hid}function kM(a,b){var c=iM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};var lM=["GA1"];
var mM=function(a,b,c){var d=R(a,Q.A.Uj);if(d===void 0||c<=d)V(a,K.m.Rb,b),T(a,Q.A.Uj,c)},oM=function(a,b){var c=Cu(a,K.m.Rb);if(P(a.D,K.m.Fc)&&P(a.D,K.m.Ec)||b&&c===b)return c;if(c){c=""+c;if(!nM(c,a))return O(31),a.isAborted=!0,"";kM(c,vn(K.m.ka));return c}O(32);a.isAborted=!0;return""},pM=function(a){var b=R(a,Q.A.Ca),c=b.prefix+"_ga",d=Lr(b.prefix+"_ga",b.domain,b.path,lM,K.m.ka);if(!d){var e=String(P(a.D,K.m.bd,""));e&&e!==c&&(d=Lr(e,b.domain,b.path,lM,K.m.ka))}return d},nM=function(a,b){var c;
var d=R(b,Q.A.Ca),e=d.prefix+"_ga",f=Oq(d,void 0,void 0,K.m.ka);if(P(b.D,K.m.zc)===!1&&pM(b)===a)c=!0;else{var g;g=[lM[0],Ir(d.domain,d.path),a].join(".");c=Dr(e,g,f)!==1}return c};
var sM=function(a){var b=new RegExp("^"+(((a==null?void 0:a.prefix)||"")+"_ga_\\w+$")),c=Rs(function(p){return b.test(p)}),d={},e;for(e in c)if(c.hasOwnProperty(e)){var f=qM(c[e]);if(f){var g=Ns(f,2);if(g){var h=rM(g);if(h){var m=void 0,n=(((m=a)==null?void 0:m.prefix)||"").length+4;d["G-"+e.substring(n)]=h}}}}return d},tM=function(a){if(a){var b;a:{var c=(Kb(a,"s")&&a.indexOf(".")===-1?"GS2":"GS1")+".1."+a;try{b=Ls(c,2);break a}catch(d){}b=void 0}return b}},qM=function(a){if(a&&a.length!==0){for(var b,
c=-Infinity,d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;if(f.t!==void 0){var g=Number(f.t);!isNaN(g)&&g>c&&(c=g,b=f)}}return b}},Ss=function(a){a&&(a==="GS1"?gM(Dg.P.Hl):a==="GS2"&&gM(Dg.P.Il))},rM=function(a){var b=tM(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||gM(Dg.P.Pl);d||gM(Dg.P.Ol);isNaN(e)&&gM(Dg.P.Nl);if(c&&d&&!isNaN(e)){var f=b.h,g=f&&f!=="0"?String(f):void 0,h=b.d?String(b.d):void 0,m={};return m.s=String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=h,m.j=e,m.l=b.l===
"1",m.h=g,m}}};
var vM=function(a,b,c){if(!b)return a;if(!a)return b;var d=rM(a);if(!d)return b;var e,f=Ab((e=P(c.D,K.m.uf))!=null?e:30),g=R(c,Q.A.hb);if(!(Math.floor(g/1E3)>d.t+f*60))return a;var h=rM(b);if(!h)return a;h.o=d.o+1;var m;return(m=uM(h))!=null?m:b},xM=function(a,b){var c=R(b,Q.A.Ca),d=wM(b,c),e=tM(a);if(!e)return!1;var f=Oq(c||{},void 0,void 0,Os.get(2));Dr(d,void 0,f);return Ts(d,e,2,c)!==1},yM=function(a){var b=R(a,Q.A.Ca),c;var d=wM(a,b),e;b:{var f=Ss,g=Ks[2];if(g){var h,m=Gr(b.domain),n=Hr(b.path),
p=Object.keys(g.Gh),q=Os.get(2),r;if(h=(r=vr(d,m,n,p,q))==null?void 0:r.Op){var t=Ls(h,2,f);e=t?Qs(t):void 0;break b}}e=void 0}if(e){var u=Ps(d,2,Ss);if(u&&u.length>1){gM(Dg.P.Gl);var v=qM(u);v&&v.t!==e.t&&(gM(Dg.P.Jl),e=v)}c=Ns(e,2)}else c=void 0;return c},zM=function(a){var b=R(a,Q.A.hb),c={};c.s=Cu(a,K.m.Ub);c.o=Cu(a,K.m.Ug);var d;d=Cu(a,K.m.Tg);var e=(c.g=d,c.t=Math.floor(b/1E3),c.d=R(a,Q.A.Kf),c.j=R(a,Q.A.Lf)||0,c.l=!!R(a,K.m.ai),c.h=Cu(a,K.m.Kg),c);return uM(e)},uM=function(a){if(a.s&&a.o){var b=
{},c=(b.s=a.s,b.o=String(a.o),b.g=Ab(a.g)?"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return Ns(c,2)}},wM=function(a,b){return b.prefix+"_ga_"+a.target.ids[ko[6]]};
var AM=function(a){var b=P(a.D,K.m.Va),c=a.D.M[K.m.Va];if(c===b)return c;var d=sd(b,null);c&&c[K.m.ma]&&(d[K.m.ma]=(d[K.m.ma]||[]).concat(c[K.m.ma]));return d},BM=function(a,b){var c=ds(!0);return c._up!=="1"?{}:{clientId:c[a],qb:c[b]}},CM=function(a,b,c){var d=ds(!0),e=d[b];e&&(mM(a,e,2),nM(e,a));var f=d[c];f&&xM(f,a);return{clientId:e,qb:f}},DM=function(){var a=oj(w.location,"host"),b=oj(sj(z.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},EM=function(a){if(!P(a.D,
K.m.Db))return{};var b=R(a,Q.A.Ca),c=b.prefix+"_ga",d=wM(a,b);ls(function(){var e;if(vn("analytics_storage"))e={};else{var f={_up:"1"},g;g=Cu(a,K.m.Rb);e=(f[c]=g,f[d]=zM(a),f)}return e},1);return!vn("analytics_storage")&&DM()?BM(c,d):{}},GM=function(a){var b=AM(a)||{},c=R(a,Q.A.Ca),d=c.prefix+"_ga",e=wM(a,c),f={};ns(b[K.m.qf],!!b[K.m.ma])&&(f=CM(a,d,e),f.clientId&&f.qb&&(FM=!0));b[K.m.ma]&&ks(function(){var g={},h=pM(a);h&&(g[d]=h);var m=yM(a);m&&(g[e]=m);var n=rr("FPLC",void 0,void 0,K.m.ka);n.length&&
(g._fplc=n[0]);return g},b[K.m.ma],b[K.m.jd],!!b[K.m.Gc]);return f},FM=!1;var HM=function(a){if(!R(a,Q.A.yd)&&ck(a.D)){var b=AM(a)||{},c=(ns(b[K.m.qf],!!b[K.m.ma])?ds(!0)._fplc:void 0)||(rr("FPLC",void 0,void 0,K.m.ka).length>0?void 0:"0");V(a,"_fplc",c)}};function IM(a){(Pv(a)||fj())&&V(a,K.m.rl,um()||tm());!Pv(a)&&fj()&&V(a,K.m.xi,"::")}function JM(a){if(fj()&&!Pv(a)&&(xm()||V(a,K.m.Tk,!0),G(78))){Nu(a);Ou(a,fo.Ff.In,Qm(P(a.D,K.m.Ua)));var b=fo.Ff.Jn;var c=P(a.D,K.m.zc);Ou(a,b,c===!0?1:c===!1?0:void 0);Ou(a,fo.Ff.Hn,Qm(P(a.D,K.m.Bb)));Ou(a,fo.Ff.En,Ir(Pm(P(a.D,K.m.ub)),Pm(P(a.D,K.m.Sb))))}};var LM=function(a,b){On("grl",function(){return KM()})(b)||(O(35),a.isAborted=!0)},KM=function(){var a=Fb(),b=a+864E5,c=20,d=5E3;return function(e){var f=Fb();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.Up=d,e.Hp=c);return g}};
var MM=function(a){var b=Cu(a,K.m.Wa);return mj(sj(b),"host",!0)},NM=function(a){if(P(a.D,K.m.pf)!==void 0)a.copyToHitData(K.m.pf);else{var b=P(a.D,K.m.gi),c,d;a:{if(FM){var e=AM(a)||{};if(e&&e[K.m.ma])for(var f=MM(a),g=e[K.m.ma],h=0;h<g.length;h++)if(g[h]instanceof RegExp){if(g[h].test(f)){d=!0;break a}}else if(f.indexOf(g[h])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=MM(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(V(a,K.m.pf,"1"),
gM(Dg.P.dm))}};
var OM=function(a,b){zq()&&(a.gcs=Aq(),R(b,Q.A.ah)&&(a.gcu="1"));a.gcd=Eq(b.D);a.npa=R(b,Q.A.Tc)?"0":"1";Jq()&&(a._ng="1")},PM=function(a){if(R(a,Q.A.yd))return{url:dk("https://www.merchant-center-analytics.goog",void 0,"")+"/mc/collect",endpoint:20};var b=Zj(ck(a.D),"/g/collect");if(b)return{url:b,endpoint:16};var c=Qv(a),d=P(a.D,K.m.Qb),e=c&&!vm()&&d!==!1&&qK()&&vn(K.m.aa)&&vn(K.m.ka)?17:16;return{url:Ew(e),endpoint:e}},QM={};QM[K.m.Rb]="cid";QM[K.m.Sh]="gcut";QM[K.m.Zc]="are";QM[K.m.Gg]="pscdl";
QM[K.m.bi]="_fid";QM[K.m.Pk]="_geo";QM[K.m.Bc]="gdid";QM[K.m.ie]="_ng";QM[K.m.Dc]="frm";QM[K.m.pf]="ir";QM[K.m.Tk]="fp";QM[K.m.xb]="ul";QM[K.m.Rg]="ni";QM[K.m.Go]="pae";QM[K.m.Sg]="_rdi";QM[K.m.Hc]="sr";QM[K.m.Lo]="tid";QM[K.m.li]="tt";QM[K.m.Eb]="ec_mode";QM[K.m.Ul]="gtm_up";QM[K.m.wf]="uaa";QM[K.m.xf]="uab";QM[K.m.yf]="uafvl";QM[K.m.zf]="uamb";QM[K.m.Af]="uam";QM[K.m.Bf]=
"uap";QM[K.m.Cf]="uapv";QM[K.m.Df]="uaw";QM[K.m.rl]="ur";QM[K.m.xi]="_uip";QM[K.m.Fo]="_prs";QM[K.m.hd]="lps";QM[K.m.Yd]="gclgs";QM[K.m.ae]="gclst";QM[K.m.Zd]="gcllp";var RM={};RM[K.m.Te]="cc";RM[K.m.Ue]="ci";RM[K.m.Ve]="cm";RM[K.m.We]="cn";RM[K.m.Ye]="cs";RM[K.m.Ze]="ck";RM[K.m.fb]=
"cu";RM[K.m.nf]="_tu";RM[K.m.Ea]="dl";RM[K.m.Wa]="dr";RM[K.m.Cb]="dt";RM[K.m.Tg]="seg";RM[K.m.Ub]="sid";RM[K.m.Ug]="sct";RM[K.m.Ma]="uid";G(145)&&(RM[K.m.tf]="dp");var SM={};SM[K.m.Jg]="_et";SM[K.m.Ac]="edid";G(94)&&(SM._eu="_eu");var TM={};TM[K.m.Te]="cc";TM[K.m.Ue]="ci";TM[K.m.Ve]="cm";TM[K.m.We]="cn";TM[K.m.Ye]="cs";TM[K.m.Ze]="ck";var UM={},VM=(UM[K.m.yb]=1,
UM),WM=function(a,b,c){function d(F,M){if(M!==void 0&&!Am.hasOwnProperty(F)){M===null&&(M="");var U;var fa=M;F!==K.m.Kg?U=!1:R(a,Q.A.oe)||Pv(a)?(e.ecid=fa,U=!0):U=void 0;if(!U&&F!==K.m.ai){var S=M;M===!0&&(S="1");M===!1&&(S="0");S=String(S);var Z;if(QM[F])Z=QM[F],e[Z]=S;else if(RM[F])Z=RM[F],g[Z]=S;else if(SM[F])Z=SM[F],f[Z]=S;else if(F.charAt(0)==="_")e[F]=S;else{var ra;TM[F]?ra=!0:F!==K.m.Xe?ra=!1:(typeof M!=="object"&&v(F,M),ra=!0);ra||v(F,M)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;
e.gtm=Nq({Na:R(a,Q.A.Za)});e._p=G(159)?$i:jM();if(c&&(c.lb||c.ij)&&(G(125)||(e.em=c.hc),c.Ib)){var h=c.Ib.Be;h&&!G(8)&&(h=h.replace(/./g,"*"));h&&(e.eme=h)}R(a,Q.A.Sd)&&(e._gaz=1);OM(e,a);Hq()&&(e.dma_cps=Fq());e.dma=Gq();aq(iq())&&(e.tcfd=Iq());var m=bo(a);m&&(g.tag_exp=m);Wj()&&(e.ptag_exp=Wj());var n=Cu(a,K.m.Bc);n&&(e.gdid=n);f.en=String(a.eventName);if(R(a,Q.A.Jf)){var p=R(a,Q.A.Sl);f._fv=p?2:1}R(a,Q.A.fh)&&(f._nsi=1);if(R(a,Q.A.te)){var q=R(a,Q.A.Vl);f._ss=q?2:1}R(a,Q.A.If)&&(f._c=1);R(a,Q.A.wd)&&
(f._ee=1);if(R(a,Q.A.Rl)){var r=Cu(a,K.m.wa)||P(a.D,K.m.wa);if(Array.isArray(r))for(var t=0;t<r.length&&t<200;t++)f["pr"+(t+1)]=xg(r[t])}var u=Cu(a,K.m.Ac);u&&(f.edid=u);Jx(a,f);for(var v=function(F,M){if(typeof M!=="object"||!VM[F]){var U="ep."+F,fa="epn."+F;F=sb(M)?fa:U;var S=sb(M)?U:fa;f.hasOwnProperty(S)&&delete f[S];f[F]=String(M)}},x=l(Object.keys(a.C)),y=x.next();!y.done;y=x.next()){var A=y.value;d(A,Cu(a,A))}(function(F){Pv(a)&&typeof F==="object"&&yb(F||{},function(M,U){typeof U!=="object"&&
(e["sst."+M]=String(U))})})(Cu(a,K.m.Li));Ix(e,Cu(a,K.m.ud));var D=Cu(a,K.m.Vb)||{};P(a.D,K.m.Qb,void 0,4)===!1&&(e.ngs="1");yb(D,function(F,M){M!==void 0&&((M===null&&(M=""),F!==K.m.Ma||g.uid)?b[F]!==M&&(f[(sb(M)?"upn.":"up.")+String(F)]=String(M),b[F]=M):g.uid=String(M))});if(fj()&&!xm()){var E=R(a,Q.A.Kf);E?e._gsid=E:e.njid="1"}var L=PM(a);Kg.call(this,{oa:e,Od:g,cj:f},L.url,L.endpoint,Pv(a),void 0,a.target.destinationId,a.D.eventId,a.D.priorityId)};xa(WM,Kg);
var XM=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},YM=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;var e;try{e=encodeURIComponent(c||"/")}catch(f){e=encodeURIComponent("/")}b.encoded_path=e;return b},ZM=function(a,b,c,d,e){var f=0,g=new w.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(h){if(g.status===200){var m=g.responseText.substring(f);f=h.loaded;By(c,
m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},aN=function(a,b,c){var d;return d=Dy(new Cy(function(e,f){var g=XM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionsrc="");al(a,g,void 0,Fy(d,f),h)}),function(e,f){var g=XM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&
(h.attributionReporting={eventSourceEligible:!1,triggerEligible:!0});f.process_response?$M(a,g,void 0,d,h,Fy(d,f)):bl(a,g,void 0,h,void 0,Fy(d,f))})},bN=function(a,b,c,d,e){Vk(a,2,b);var f=aN(a,d,e);$M(a,b,c,f)},$M=function(a,b,c,d,e,f){$c()?Ay(a,b,c,d,e,void 0,f):ZM(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},cN=function(a,b,c){var d=sj(b),e=YM(d),f=Hy(d);!G(132)||Dc("; wv")||Dc("FBAN")||Dc("FBAV")||Fc()?bN(a,f,c,e):mw(f,c,e,function(g){bN(a,f,c,
e,g)})};var dN={AW:Hl.X.un,G:Hl.X.Ro,DC:Hl.X.No};function eN(a){var b=dx(a);return""+dr(b.map(function(c){return c.value}).join("!"))}function fN(a){var b=io(a);return b&&dN[b.prefix]}function gN(a,b){var c=a[b];c&&(c.clearTimerId&&w.clearTimeout(c.clearTimerId),c.clearTimerId=w.setTimeout(function(){delete a[b]},36E5))};
var hN=function(a,b,c,d){var e=a+"?"+b;d?$k(c,e,d):Zk(c,e)},jN=function(a,b,c,d,e){var f=b,g=ed();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var h=a+"?"+b;iN&&(d=!Kb(h,Dw())&&!Kb(h,Cw()));if(d&&!cM)cN(e,h,c);else{var m=b;$c()?bl(e,a+"?"+m,c,{Ch:!0})||hN(a,m,e,c):hN(a,m,e,c)}},kN=function(a,b){function c(y){r.push(y+"="+encodeURIComponent(""+a.oa[y]))}var d=b.qr,e=b.vr,f=b.ur,g=b.rr,h=b.oq,m=b.Iq,n=b.Hq,p=b.gq,q=b.Br;if(d||e||f||g){var r=[];a.oa._ng&&c("_ng");c("tid");c("cid");c("gtm");r.push("aip=1");
a.Od.uid&&!n&&r.push("uid="+encodeURIComponent(""+a.Od.uid));c("dma");a.oa.dma_cps!=null&&c("dma_cps");a.oa.gcs!=null&&c("gcs");c("gcd");a.oa.npa!=null&&c("npa");a.oa.frm!=null&&c("frm");d&&(q&&r.push("tag_exp="+q),Wj()&&r.push("ptag_exp="+Wj()),hN("https://stats.g.doubleclick.net/g/collect","v=2&"+r.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),gn({targetId:String(a.oa.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+r.join("&"),
parameterEncoding:2,endpoint:19},kb:b.kb}));if(e&&(q&&r.push("tag_exp="+q),Wj()&&r.push("ptag_exp="+Wj()),r.push("z="+vb()),!m)){var t=h&&Kb(h,"google.")&&h!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",h):void 0;if(t){var u=t+r.join("&");al({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},u);gn({targetId:String(a.oa.tid),request:{url:u,parameterEncoding:2,endpoint:47},kb:b.kb})}}if(f){var v="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",
p?p+".":"");r=[];c("_gsid");c("gtm");a.oa._geo&&c("_geo");hN(v,r.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});gn({targetId:String(a.oa.tid),request:{url:v+"?"+r.join("&"),parameterEncoding:2,endpoint:18},kb:b.kb})}if(g){r=[];r.push("v=2");c("_gsid");c("gtm");a.oa._geo&&c("_geo");var x="https://{ga4CollectionSubdomain.}google-analytics.com/g/s/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");hN(x,r.join("&"),{destinationId:a.destinationId||
"",endpoint:62,eventId:a.eventId,priorityId:a.priorityId});gn({targetId:String(a.oa.tid),request:{url:x+"?"+r.join("&"),parameterEncoding:2,endpoint:62},kb:b.kb})}}},iN=!1;var lN=function(){this.M=1;this.R={};this.H=-1;this.C=new Cg};k=lN.prototype;k.Lb=function(a,b){var c=this,d=new WM(a,this.R,b),e={eventId:a.D.eventId,priorityId:a.D.priorityId},f=dM(a),g,
h;f&&this.C.U(d)||this.flush();var m=f&&this.C.add(d);if(m){if(this.H<0){var n=w,p=n.setTimeout,q;Pv(a)?mN?(mN=!1,q=nN):q=oN:q=5E3;this.H=p.call(n,function(){c.flush()},q)}}else{var r=Gg(d,this.M++),t=r.params,u=r.body;g=t;h=u;jN(d.baseUrl,t,u,d.M,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,priorityId:d.priorityId});var v=R(a,Q.A.wg),x=R(a,Q.A.Sd),y=R(a,Q.A.yg),A=R(a,Q.A.xg),D=P(a.D,K.m.Qh)!==!1,E=yq(a.D),L={qr:v,vr:x,ur:y,rr:A,oq:ym(),Es:D,Ds:E,Iq:vm(),Hq:R(a,Q.A.oe),
kb:e,D:a.D,gq:xm(),Br:bo(a)};kN(d,L)}py(a.D.eventId);hn(function(){if(m){var F=Gg(d),M=F.body;g=F.params;h=M}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+g,parameterEncoding:2,postBody:h,endpoint:d.endpoint},kb:e,isBatched:!1}})};k.add=function(a){if(G(100)){var b=R(a,Q.A.Oh);if(b){V(a,K.m.Eb,R(a,Q.A.Cm));V(a,K.m.Rg,"1");this.Lb(a,b);return}}var c=Ov(a);if(G(100)&&c){var d;var e=a.target.destinationId,f;var g=c,h=fN(e);if(h){var m=eN(g);f=(Ll(h)||{})[m]}else f=void 0;var n=f;
d=n?n.sentTo[e]:void 0;if(d&&d+6E4>Fb())c=void 0,V(a,K.m.Eb);else{var p=c,q=a.target.destinationId,r=fN(q);if(r){var t=eN(p),u=Ll(r)||{},v=u[t];if(v)v.timestamp=Fb(),v.sentTo=v.sentTo||{},v.sentTo[q]=Fb(),v.pending=!0;else{var x={};u[t]={pending:!0,timestamp:Fb(),sentTo:(x[q]=Fb(),x)}}gN(u,t);Kl(r,u)}}}!c||cM||G(125)&&!G(93)?this.Lb(a):this.wr(a)};k.flush=function(){if(this.C.events.length){var a=Ig(this.C,this.M++);jN(this.C.baseUrl,a.params,a.body,this.C.H,{destinationId:this.C.destinationId||"",
endpoint:this.C.endpoint,eventId:this.C.ia,priorityId:this.C.la});this.C=new Cg;this.H>=0&&(w.clearTimeout(this.H),this.H=-1)}};k.Jm=function(a,b){var c=Cu(a,K.m.Eb);V(a,K.m.Eb);b.then(function(d){var e={},f=(e[Q.A.Oh]=d,e[Q.A.Cm]=c,e),g=XA(a.target.destinationId,K.m.Xd,a.D.C);ZA(g,a.D.eventId,{eventMetadata:f})})};k.wr=function(a){var b=this,c=Ov(a);if(Bx(c)){var d=qx(c,G(93));d?G(100)?(this.Jm(a,d),this.Lb(a)):d.then(function(g){b.Lb(a,g)},function(){b.Lb(a)}):this.Lb(a)}else{var e=Ax(c);if(G(93)){var f=
mx(e);f?G(100)?(this.Jm(a,f),this.Lb(a)):f.then(function(g){b.Lb(a,g)},function(){b.Lb(a,e)}):this.Lb(a,e)}else this.Lb(a,e)}};var nN=yi(Ji(24,''),500),oN=yi(Ji(56,''),5E3),mN=!0;
var pN=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;pN(a+"."+f,b[f],c)}else c[a]=b;return c},qN=function(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!vn(e)}return b},sN=function(a,b){var c=rN.filter(function(e){return!vn(e)});if(c.length){var d=qN(c);wn(c,function(){for(var e=qN(c),f=[],g=l(c),h=g.next();!h.done;h=g.next()){var m=h.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){T(b,Q.A.ah,!0);var n=f.map(function(p){return Km[p]}).join(".");n&&Mv(b,"gcut",n);a(b)}})}},tN=function(a){Pv(a)&&Mv(a,"navt",fd())},uN=function(a){Pv(a)&&Mv(a,"lpc",Zs())},vN=function(a){if(Pv(a)){var b=P(a.D,K.m.Tb),c;b===!0&&(c="1");b===!1&&(c="0");c&&Mv(a,"rdp",c)}},wN=function(a){G(147)&&Pv(a)&&P(a.D,K.m.Se,!0)===!1&&V(a,K.m.Se,0)},xN=function(a,b){if(Pv(b)){var c=R(b,Q.A.If);(b.eventName==="page_view"||c)&&sN(a,b)}},yN=function(a){if(Pv(a)&&a.eventName===K.m.Xd&&R(a,Q.A.ah)){var b=
Cu(a,K.m.Sh);b&&(Mv(a,"gcut",b),Mv(a,"syn",1))}},zN=function(a){Pv(a)&&T(a,Q.A.xa,!1)},AN=function(a){Pv(a)&&(R(a,Q.A.xa)&&Mv(a,"sp",1),R(a,Q.A.Xo)&&Mv(a,"syn",1),R(a,Q.A.Me)&&(Mv(a,"em_event",1),Mv(a,"sp",1)))},BN=function(a){if(Pv(a)){var b=$i;b&&Mv(a,"tft",Number(b))}},CN=function(a){function b(e){var f=pN(K.m.yb,e);yb(f,function(g,h){V(a,g,h)})}if(Pv(a)){var c=Su(a,"ccd_add_1p_data",!1)?1:0;Mv(a,"ude",c);var d=P(a.D,K.m.yb);d!==void 0?(b(d),V(a,K.m.Eb,"c")):b(R(a,Q.A.Qa));T(a,Q.A.Qa)}},DN=function(a){if(Pv(a)){var b=
CJ();b&&Mv(a,"us_privacy",b);var c=pq();c&&Mv(a,"gdpr",c);var d=oq();d&&Mv(a,"gdpr_consent",d);var e=GC.gppString;e&&Mv(a,"gpp",e);var f=GC.C;f&&Mv(a,"gpp_sid",f)}},EN=function(a){Pv(a)&&sl()&&P(a.D,K.m.Ia)&&Mv(a,"adr",1)},FN=function(a){if(Pv(a)){var b=G(90)?xm():"";b&&Mv(a,"gcsub",b)}},GN=function(a){if(Pv(a)){P(a.D,K.m.Qb,void 0,4)===!1&&Mv(a,"ngs",1);vm()&&Mv(a,"ga_rd",1);qK()||Mv(a,"ngst",1);var b=ym();b&&Mv(a,"etld",b)}},HN=function(a){},IN=function(a){Pv(a)&&sl()&&Mv(a,"rnd",lv())},rN=[K.m.aa,K.m.W];
var JN=function(a,b){var c;a:{var d=zM(a);if(d){if(xM(d,a)){c=d;break a}O(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:oM(a,b),qb:e}},KN=function(a,b,c,d,e){var f=Pm(P(a.D,K.m.Rb));if(P(a.D,K.m.Fc)&&P(a.D,K.m.Ec))f?mM(a,f,1):(O(127),a.isAborted=!0);else{var g=f?1:8;T(a,Q.A.fh,!1);f||(f=pM(a),g=3);f||(f=b,g=5);if(!f){var h=vn(K.m.ka),m=iM();f=!m.from_cookie||h?m.vid:void 0;g=6}f?f=""+f:(f=Kr(),g=7,T(a,Q.A.Jf,!0),T(a,Q.A.fh,!0));mM(a,f,g)}var n=R(a,Q.A.hb),p=Math.floor(n/1E3),q=void 0;R(a,Q.A.fh)||
(q=yM(a)||c);var r=Ab(P(a.D,K.m.uf,30));r=Math.min(475,r);r=Math.max(5,r);var t=Ab(P(a.D,K.m.ii,1E4)),u=rM(q);T(a,Q.A.Jf,!1);T(a,Q.A.te,!1);T(a,Q.A.Lf,0);u&&u.j&&T(a,Q.A.Lf,Math.max(0,u.j-Math.max(0,p-u.t)));var v=!1;if(!u){T(a,Q.A.Jf,!0);v=!0;var x={};u=(x.s=String(p),x.o=1,x.g=!1,x.t=p,x.l=!1,x.h=void 0,x)}p>u.t+r*60&&(v=!0,u.s=String(p),u.o++,u.g=!1,u.h=void 0);if(v)T(a,Q.A.te,!0),d.yq(a);else if(d.mq()>t||a.eventName===K.m.Wc)u.g=!0;R(a,Q.A.oe)?P(a.D,K.m.Ma)?u.l=!0:(u.l&&!G(9)&&(u.h=void 0),u.l=
!1):u.l=!1;var y=u.h;if(R(a,Q.A.oe)||Pv(a)){var A=P(a.D,K.m.Kg),D=A?1:8;A||(A=y,D=4);A||(A=Jr(),D=7);var E=A.toString(),L=D,F=R(a,Q.A.pk);if(F===void 0||L<=F)V(a,K.m.Kg,E),T(a,Q.A.pk,L)}e?(a.copyToHitData(K.m.Ub,u.s),a.copyToHitData(K.m.Ug,u.o),a.copyToHitData(K.m.Tg,u.g?1:0)):(V(a,K.m.Ub,u.s),V(a,K.m.Ug,u.o),V(a,K.m.Tg,u.g?1:0));T(a,K.m.ai,u.l?1:0);fj()&&T(a,Q.A.Kf,u.d||Tb())};var MN=function(a){for(var b={},c=String(LN.cookie).split(";"),d=0;d<c.length;d++){var e=c[d].split("="),f=e[0].trim();if(f&&a(f)){var g=e.slice(1).join("=").trim();g&&(g=decodeURIComponent(g));var h=void 0,m=void 0;((h=b)[m=f]||(h[m]=[])).push(g)}}return b};var NN=window,LN=document,ON=function(a){var b=NN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||LN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&NN["ga-disable-"+a]===!0)return!0;try{var c=NN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(f){}for(var d=MN(function(f){return f==="AMP_TOKEN"}).AMP_TOKEN||[],e=0;e<d.length;e++)if(d[e]=="$OPT_OUT")return!0;return LN.getElementById("__gaOptOutExtension")?!0:!1};
var QN=function(a){return!a||PN.test(a)||Cm.hasOwnProperty(a)},RN=function(a){var b=K.m.Hc,c;c||(c=function(){});Cu(a,b)!==void 0&&V(a,b,c(Cu(a,b)))},SN=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b),d=lj(c);d&&(c=d);return b===-1?c:""+c+a.substring(b)},TN=function(a){P(a.D,K.m.Db)&&(vn(K.m.ka)||P(a.D,K.m.Rb)||V(a,K.m.Ul,!0));var b;var c;c=c===void 0?3:c;var d=w.location.href;if(d){var e=sj(d).search.replace("?",""),f=jj(e,"_gl",!1,!0)||"";b=f?es(f,c)!==void 0:!1}else b=!1;b&&Pv(a)&&
Mv(a,"glv",1);if(a.eventName!==K.m.na)return{};P(a.D,K.m.Db)&&Zt(["aw","dc"]);au(["aw","dc"]);var g=GM(a),h=EM(a);return Object.keys(g).length?g:h},UN={bq:Ji(31,'')},VN={},WN=(VN[K.m.Te]=1,VN[K.m.Ue]=1,VN[K.m.Ve]=1,VN[K.m.We]=1,VN[K.m.Ye]=1,VN[K.m.Ze]=1,VN),PN=/^(_|ga_|google_|gtag\.|firebase_).*$/,XN=[Ru,Pu,DJ,Tu,mJ,zJ],YN=function(a){this.M=a;this.C=this.qb=this.clientId=void 0;this.Ja=this.U=!1;this.Xa=0;this.R=!1;this.ia={lj:!1};this.la=new lN;this.H=
new fM};k=YN.prototype;k.ar=function(a,b,c){var d=this,e=io(this.M);if(e)if(c.eventMetadata[Q.A.wd]&&a.charAt(0)==="_")c.onFailure();else{a!==K.m.na&&a!==K.m.nc&&QN(a)&&O(58);ZN(c.C);var f=new NF(e,a,c);T(f,Q.A.hb,b);var g=[K.m.ka],h=Pv(f);T(f,Q.A.gh,h);if(Su(f,K.m.je,P(f.D,K.m.je))||h)g.push(K.m.aa),g.push(K.m.W);uJ(function(){yn(function(){d.er(f)},g)});G(88)&&a===K.m.na&&Su(f,"ga4_ads_linked",!1)&&El(Gl(gl.Z.Ha),function(){d.Yq(a,c,f)})}else c.onFailure()};k.Yq=function(a,b,c){function d(){for(var h=
l(XN),m=h.next();!m.done;m=h.next()){var n=m.value;n(f);if(f.isAborted)break}R(f,Q.A.xa)||f.isAborted||Px(f)}var e=io(this.M),f=new NF(e,a,b);T(f,Q.A.ba,N.N.Ya);T(f,Q.A.xa,!0);T(f,Q.A.gh,R(c,Q.A.gh));var g=[K.m.aa,K.m.W];yn(function(){d();vn(g)||xn(function(h){var m,n;m=h.consentEventId;n=h.consentPriorityId;T(f,Q.A.ja,!0);T(f,Q.A.Je,m);T(f,Q.A.Ke,n);d()},g)},g)};k.er=function(a){var b=this;try{Ru(a);if(a.isAborted){kb();return}G(165)||(this.C=a);$N(a);aO(a);bO(a);cO(a);G(138)&&(a.isAborted=!0);Iu(a);
var c={};LM(a,c);if(a.isAborted){a.D.onFailure();kb();return}G(165)&&(this.C=a);var d=c.Hp;c.Up===0&&gM(Dg.P.kk);d===0&&gM(Dg.P.Rj);Tu(a);T(a,Q.A.Qf,gl.Z.xc);dO(a);eO(a);this.ap(a);this.H.Gr(a);fO(a);PJ(a,G(60));gO(a);hO(a);this.dn(TN(a));var e=a.eventName===K.m.na;e&&(this.R=!0);iO(a);e&&!a.isAborted&&this.Xa++>0&&gM(Dg.P.Yl);mJ(a);jO(a);KN(a,this.clientId,this.qb,this.H,!this.Ja);kO(a);lO(a);mO(a);nO(a,this.ia);oO(a);pO(a);qO(a);rO(a);sO(a);tO(a);HM(a);NM(a);IN(a);HN(a);GN(a);FN(a);EN(a);DN(a);
BN(a);AN(a);yN(a);wN(a);vN(a);uN(a);tN(a);IM(a);JM(a);P(a.D,K.m.Sg)&&!Pv(a)||xJ(a);uO(a);vO(a);Ku(a);Ju(a);Qu(a);EJ(a,!1);wO(a);BJ(a);xO(a);CN(a);zN(a);yO(a);!this.R&&R(a,Q.A.Me)&&gM(Dg.P.nk);hM(a);if(R(a,Q.A.xa)||a.isAborted){a.D.onFailure();kb();return}this.dn(JN(a,this.clientId));this.Ja=!0;this.Dr(a);zO(a);xN(function(f){b.Dm(f)},a);this.H.Cj();AO(a);HJ(a);Uu(a);if(a.isAborted){a.D.onFailure();kb();return}this.Dm(a);a.D.onSuccess()}catch(f){a.D.onFailure()}kb()};k.Dm=function(a){this.la.add(a)};
k.dn=function(a){var b=a.clientId,c=a.qb;b&&c&&(this.clientId=b,this.qb=c)};k.flush=function(){this.la.flush()};k.Dr=function(a){var b=this;if(!this.U){var c=vn(K.m.W),d=vn(K.m.ka);wn([K.m.W,K.m.ka,K.m.aa],function(){var e=vn(K.m.W),f=vn(K.m.ka),g=!1,h={},m={};if(d!==f&&b.C&&b.qb&&b.clientId){var n=b.clientId,p;var q=rM(b.qb);p=q?q.h:void 0;if(f){var r=pM(b.C);if(r){b.clientId=r;var t=yM(b.C);t&&(b.qb=vM(t,b.qb,b.C))}else nM(b.clientId,b.C),kM(b.clientId,!0);xM(b.qb,b.C);g=!0;h[K.m.Ok]=n;G(69)&&p&&
(h[K.m.Ao]=p)}else b.qb=void 0,b.clientId=void 0,w.gaGlobal={}}e&&!c&&(g=!0,m[Q.A.ah]=!0,h[K.m.Sh]=Km[K.m.W]);if(g){var u=XA(b.M,K.m.Xd,h);ZA(u,a.D.eventId,{eventMetadata:m})}d=f;c=e;b.ia.lj=!0});this.U=!0}};k.ap=function(a){a.eventName!==K.m.nc&&this.H.Zo(a)};var bO=function(a){var b=z.location.protocol;b!=="http:"&&b!=="https:"&&(O(29),a.isAborted=!0)},cO=function(a){yc&&yc.loadPurpose==="preview"&&(O(30),a.isAborted=!0)},dO=function(a){var b={prefix:String(P(a.D,K.m.Ua,"")),path:String(P(a.D,K.m.Sb,
"/")),flags:String(P(a.D,K.m.Bb,"")),domain:String(P(a.D,K.m.ub,"auto")),Pc:Number(P(a.D,K.m.wb,63072E3))};T(a,Q.A.Ca,b)},fO=function(a){R(a,Q.A.yd)?T(a,Q.A.oe,!1):Su(a,"ccd_add_ec_stitching",!1)&&T(a,Q.A.oe,!0)},gO=function(a){if(G(91)&&!G(88)&&Su(a,"ga4_ads_linked",!1)&&a.eventName===K.m.na){var b=P(a.D,K.m.eb)!==!1;if(b){var c=zu(a);c.Pc&&(c.Pc=Math.min(c.Pc,7776E3));Au({Ae:b,Ee:Nm(P(a.D,K.m.Va)),Ie:!!P(a.D,K.m.Db),Mc:c})}}},hO=function(a){var b=yq(a.D);P(a.D,K.m.Tb)===!0&&(b=!1);T(a,Q.A.Tc,b)},
iO=function(a){a.eventName===K.m.na&&(P(a.D,K.m.nd,!0)?(a.D.C[K.m.Da]&&(a.D.H[K.m.Da]=a.D.C[K.m.Da],a.D.C[K.m.Da]=void 0,V(a,K.m.Da)),a.eventName=K.m.Wc):a.isAborted=!0)},eO=function(a){function b(c,d){Am[c]||d===void 0||V(a,c,d)}yb(a.D.H,b);yb(a.D.C,b)},kO=function(a){var b=Go(a.D),c=function(d,e){WN[d]&&V(a,d,e)};rd(b[K.m.Xe])?yb(b[K.m.Xe],function(d,e){c((K.m.Xe+"_"+d).toLowerCase(),e)}):yb(b,c)},zO=function(a){if(G(132)&&Pv(a)&&!(Dc("; wv")||Dc("FBAN")||Dc("FBAV")||Fc())&&vn(K.m.ka)){T(a,Q.A.Wl,
!0);Pv(a)&&Mv(a,"sw_exp",1);a:{if(!G(132)||!Pv(a))break a;var b=Zj(ck(a.D),"/_/service_worker");jw(b);}}},lO=function(a){if(!P(a.D,K.m.Ec)||!P(a.D,K.m.Fc)){var b=a.copyToHitData,c=K.m.Ea,d="",e=z.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f="/"+f);var g=e.search||"";if(g&&g[0]==="?")for(var h=g.substring(1).split("&"),m=0;m<h.length;++m){var n=h[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&
(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Qb(n[1])))}d=e.protocol+"//"+e.hostname+f+g}b.call(a,c,d,SN);var p=a.copyToHitData,q=K.m.Wa,r;a:{var t=rr("_opt_expid",void 0,void 0,K.m.ka)[0];if(t){var u=lj(t);if(u){var v=u.split("$");if(v.length===3){r=v[2];break a}}}var x=Nn.ga4_referrer_override;if(x!==void 0)r=x;else{var y=Xo("gtm.gtagReferrer."+a.target.destinationId),A=z.referrer;r=y?""+y:A}}p.call(a,q,r||void 0,SN);a.copyToHitData(K.m.Cb,z.title);a.copyToHitData(K.m.xb,(yc.language||"").toLowerCase());
var D=gG();a.copyToHitData(K.m.Hc,D.width+"x"+D.height);G(145)&&a.copyToHitData(K.m.tf,void 0,SN);G(87)&&Kv()&&a.copyToHitData(K.m.hd,"1")}},nO=function(a,b){b.lj&&(T(a,Q.A.ja,!0),b.lj=!1,fj()&&T(a,Q.A.Kf,Tb()))},oO=function(a){var b=R(a,Q.A.Lf);b=b||0;var c=!!R(a,Q.A.ja),d=b===0||c;T(a,Q.A.Bi,d);d&&T(a,Q.A.Lf,60)},pO=function(a){T(a,Q.A.wg,!1);T(a,Q.A.Sd,!1);if(!Pv(a)&&!R(a,Q.A.yd)&&P(a.D,K.m.Qb)!==!1&&qK()&&vn([K.m.aa,K.m.ka])){var b=Qv(a);(R(a,Q.A.te)||P(a.D,K.m.Ok))&&T(a,Q.A.wg,!!b);b&&R(a,Q.A.Bi)&&
R(a,Q.A.Tl)&&T(a,Q.A.Sd,!0)}},qO=function(a){T(a,Q.A.xg,!1);T(a,Q.A.yg,!1);if(!xm()&&fj()&&!Pv(a)&&!R(a,Q.A.yd)&&R(a,Q.A.Bi)){var b=R(a,Q.A.Sd);R(a,Q.A.Kf)&&(b?T(a,Q.A.yg,!0):T(a,Q.A.xg,!0))}},tO=function(a){a.copyToHitData(K.m.li);for(var b=P(a.D,K.m.ei)||[],c=0;c<b.length;c++){var d=b[c];if(d.rule_result){a.copyToHitData(K.m.li,d.traffic_type);gM(Dg.P.ym);break}}},AO=function(a){a.copyToHitData(K.m.Pk);P(a.D,K.m.Sg)&&(V(a,K.m.Sg,!0),Pv(a)||RN(a))},wO=function(a){a.copyToHitData(K.m.Ma);a.copyToHitData(K.m.Vb)},
mO=function(a){Su(a,"google_ng")&&!vm()?a.copyToHitData(K.m.ie,1):Lu(a)},yO=function(a){var b=P(a.D,K.m.Fc);b&&gM(Dg.P.qm);R(a,Q.A.Me)&&gM(Dg.P.Tj);var c=Ij(Jj());(b||Tj(c)||c&&c.parent&&c.context&&c.context.source===5)&&gM(Dg.P.Xl)},$N=function(a){if(ON(a.target.destinationId))O(28),a.isAborted=!0;else if(G(144)){var b=Hj();if(b&&Array.isArray(b.destinations))for(var c=0;c<b.destinations.length;c++)if(ON(b.destinations[c])){O(125);a.isAborted=!0;break}}},uO=function(a){ow()&&V(a,K.m.Zc,"1")},aO=
function(a){if(UN.bq.replace(/\s+/g,"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=Nv(a);b&&b.blacklisted&&(a.isAborted=!0)}},rO=function(a){var b=function(c){return!!c&&c.conversion};T(a,Q.A.If,b(Nv(a)));R(a,Q.A.Jf)&&T(a,Q.A.Sl,b(Nv(a,"first_visit")));R(a,Q.A.te)&&T(a,Q.A.Vl,b(Nv(a,"session_start")))},sO=function(a){Em.hasOwnProperty(a.eventName)&&(T(a,Q.A.Rl,!0),a.copyToHitData(K.m.wa),a.copyToHitData(K.m.fb))},xO=function(a){if(!Pv(a)&&R(a,Q.A.If)&&vn(K.m.aa)&&Su(a,"ga4_ads_linked",
!1)){var b=zu(a),c=ot(b.prefix),d=cv(c);V(a,K.m.Yd,d.Yf);V(a,K.m.ae,d.th);V(a,K.m.Zd,d.sh)}},vO=function(a){if(G(122)){var b=xm();b&&T(a,Q.A.Qo,b)}},jO=function(a){T(a,Q.A.Tl,Qv(a)&&P(a.D,K.m.Qb)!==!1&&qK()&&!vm())};function ZN(a){yb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[K.m.Vb]||{};yb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var CO=function(a){if(!BO(a)){var b=!1,c=function(){!b&&BO(a)&&(b=!0,Qc(z,"visibilitychange",c),G(5)&&Qc(z,"prerenderingchange",c),O(55))};Pc(z,"visibilitychange",c);G(5)&&Pc(z,"prerenderingchange",c);O(54)}},BO=function(a){if(G(5)&&"prerendering"in z?z.prerendering:z.visibilityState==="prerender")return!1;a();return!0};function DO(a,b){CO(function(){var c=io(a);if(c){var d=EO(c,b);wp(a,d,gl.Z.xc)}});}function EO(a,b){var c=function(){};var d=new YN(a.id),e=a.prefix==="MC";c=function(f,g,h,m){e&&(m.eventMetadata[Q.A.yd]=!0);d.ar(g,h,m)};FO(a,d,b);return c}
function FO(a,b,c){var d=b.H,e={},f={eventId:c,eventMetadata:(e[Q.A.Qj]=!0,e),deferrable:!0};d.ir(function(){cM=!0;xp.flush();d.uh()>=1E3&&yc.sendBeacon!==void 0&&yp(K.m.Xd,{},a.id,f);b.flush();d.fn(function(){cM=!1;d.fn()})});}var GO=Dg.P.lk,HO=Dg.P.mk;var IO=EO;function JO(a,b){if(G(240)){var c=Ej();c&&c.indexOf(b)>-1&&(a[Q.A.Ml]=!0)}}function LO(a,b,c){var d=this;}LO.K="internal.gtagConfig";
function NO(a,b){}
NO.publicName="gtagSet";function OO(){var a={};return a};function PO(a){}PO.K="internal.initializeServiceWorker";function QO(a,b){}QO.publicName="injectHiddenIframe";var RO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function SO(a,b,c,d,e){}SO.K="internal.injectHtml";var WO={};
function YO(a,b,c,d){}var ZO={dl:1,id:1},$O={};
function aP(a,b,c,d){}G(160)?aP.publicName="injectScript":YO.publicName="injectScript";aP.K="internal.injectScript";function bP(){var a=!1;a=!!nm["5"];return a}bP.K="internal.isAutoPiiEligible";function cP(a){var b=!0;return b}cP.publicName="isConsentGranted";function dP(a){var b=!1;return b}dP.K="internal.isDebugMode";function eP(){return wm()}eP.K="internal.isDmaRegion";function fP(a){var b=!1;return b}fP.K="internal.isEntityInfrastructure";function gP(a){var b=!1;if(!th(a))throw H(this.getName(),["number"],[a]);b=G(a);return b}gP.K="internal.isFeatureEnabled";function hP(){var a=!1;return a}hP.K="internal.isFpfe";function iP(){var a=!1;return a}iP.K="internal.isGcpConversion";function jP(){var a=!1;return a}jP.K="internal.isLandingPage";function kP(){var a=!1;return a}kP.K="internal.isOgt";function lP(){var a;return a}lP.K="internal.isSafariPcmEligibleBrowser";function mP(){var a=Qh(function(b){rD(this).log("error",b)});a.publicName="JSON";return a};function nP(a){var b=void 0;return Hd(b)}nP.K="internal.legacyParseUrl";function oP(){return!1}
var pP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function qP(){}qP.publicName="logToConsole";function rP(a,b){}rP.K="internal.mergeRemoteConfig";function sP(a,b,c){c=c===void 0?!0:c;var d=[];return Hd(d)}sP.K="internal.parseCookieValuesFromString";function tP(a){var b=void 0;if(typeof a!=="string")return;a&&Kb(a,"//")&&(a=z.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(x){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=Hd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=sj(a)}catch(x){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=lj(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Hd(n);
return b}tP.publicName="parseUrl";function uP(a){}uP.K="internal.processAsNewEvent";function vP(a,b,c){var d;return d}vP.K="internal.pushToDataLayer";function wP(a){var b=Ea.apply(1,arguments),c=!1;if(!I(a))throw H(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(B(f.value,this.J,1));try{J.apply(null,d),c=!0}catch(g){return!1}return c}wP.publicName="queryPermission";function xP(a){var b=this;}xP.K="internal.queueAdsTransmission";function yP(a){var b=void 0;return b}yP.publicName="readAnalyticsStorage";function zP(){var a="";return a}zP.publicName="readCharacterSet";function AP(){return Fi(19)}AP.K="internal.readDataLayerName";function BP(){var a="";return a}BP.publicName="readTitle";function CP(a,b){var c=this;if(!I(a)||!lh(b))throw H(this.getName(),["string","function"],arguments);AJ(a,function(d){b.invoke(c.J,Hd(d,c.J,1))});}CP.K="internal.registerCcdCallback";function DP(a,b){return!0}DP.K="internal.registerDestination";var EP=["config","event","get","set"];function FP(a,b,c){}FP.K="internal.registerGtagCommandListener";function GP(a,b){var c=!1;return c}GP.K="internal.removeDataLayerEventListener";function HP(a,b){}
HP.K="internal.removeFormData";function IP(){}IP.publicName="resetDataLayer";function JP(a,b,c){var d=void 0;return d}JP.K="internal.scrubUrlParams";function KP(a){}KP.K="internal.sendAdsHit";function LP(a,b,c,d){if(arguments.length<2||!jh(d)||!jh(c))throw H(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?B(c):{},f=B(a),g=Array.isArray(f)?f:[f];b=String(b);var h=d?B(d):{},m=rD(this);h.originatingEntity=gE(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};sd(e,q);var r={};sd(h,r);var t=XA(p,b,q);ZA(t,h.eventId||m.eventId,r)}}}LP.K="internal.sendGtagEvent";function MP(a,b,c){}MP.publicName="sendPixel";function NP(a,b){}NP.K="internal.setAnchorHref";function OP(a){}OP.K="internal.setContainerConsentDefaults";function PP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}PP.publicName="setCookie";function QP(a){}QP.K="internal.setCorePlatformServices";function RP(a,b){}RP.K="internal.setDataLayerValue";function SP(a){}SP.publicName="setDefaultConsentState";function TP(a,b){if(!I(a)||!I(b))throw H(this.getName(),["string","string"],arguments);J(this,"access_consent",a,"write");J(this,"access_consent",b,"read");wm()&&(ol.delegatedConsentTypes[a]=b);}TP.K="internal.setDelegatedConsentType";function UP(a,b){}UP.K="internal.setFormAction";function VP(a,b,c){c=c===void 0?!1:c;}VP.K="internal.setInCrossContainerData";function WP(a,b,c){return!1}WP.publicName="setInWindow";function XP(a,b,c){if(!I(a)||!I(b)||arguments.length!==3)throw H(this.getName(),["string","string","any"],arguments);var d=MF(a)||{};d[b]=B(c,this.J);var e=a;KF||LF();JF[e]=d;}XP.K="internal.setProductSettingsParameter";function YP(a,b,c){if(!I(a)||!I(b)||arguments.length!==3)throw H(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Ap(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!rd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=B(c,this.J,1);}YP.K="internal.setRemoteConfigParameter";function ZP(a,b){}ZP.K="internal.setTransmissionMode";function $P(a,b,c,d){var e=this;}$P.publicName="sha256";function aQ(a,b,c){if(!I(a)||!I(b)||!ih(c))throw H(this.getName(),["string","string","Object"],arguments);for(var d=b.split("."),e=Ap(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)throw Error("sortRemoteConfigParameters failed, path points to an undefined object: "+d[f]);if(!rd(e[d[f]]))throw Error("sortRemoteConfigParameters failed, path contains a non-object type: "+d[f]);e=e[d[f]]}if(e[d[f]]===void 0)throw Error("sortRemoteConfigParameters failed, destination is undefined "+
d[f]);if(!Array.isArray(e[d[f]]))throw Error("sortRemoteConfigParameters failed, destination is not an array: "+d[f]);var g=B(c)||{},h=g.sortKey;if(!h)throw Error("sortRemoteConfigParameters failed, option.sortKey is required");var m=g.ascending!==!1;e[d[f]].sort(function(n,p){if(n[h]===void 0||p[h]===void 0)throw Error("sortRemoteConfigParameters failed, object does not have required property: "+h);return m?n[h]-p[h]:p[h]-n[h]});}
aQ.K="internal.sortRemoteConfigParameters";function bQ(a){}bQ.K="internal.storeAdsBraidLabels";function cQ(a,b){var c=void 0;return c}cQ.K="internal.subscribeToCrossContainerData";function dQ(a){}dQ.K="internal.taskSendAdsHits";var eQ={},fQ={};eQ.getItem=function(a){var b=null;J(this,"access_template_storage");var c=rD(this).Jb();fQ[c]&&(b=fQ[c].hasOwnProperty("gtm."+a)?fQ[c]["gtm."+a]:null);return b};eQ.setItem=function(a,b){J(this,"access_template_storage");var c=rD(this).Jb();fQ[c]=fQ[c]||{};fQ[c]["gtm."+a]=b;};
eQ.removeItem=function(a){J(this,"access_template_storage");var b=rD(this).Jb();if(!fQ[b]||!fQ[b].hasOwnProperty("gtm."+a))return;delete fQ[b]["gtm."+a];};eQ.clear=function(){J(this,"access_template_storage"),delete fQ[rD(this).Jb()];};eQ.publicName="templateStorage";function gQ(a,b){var c=!1;return c}gQ.K="internal.testRegex";function hQ(a){var b;return b};function iQ(a,b){}iQ.K="internal.trackUsage";function jQ(a,b){var c;return c}jQ.K="internal.unsubscribeFromCrossContainerData";function kQ(a){}kQ.publicName="updateConsentState";function lQ(a){var b=!1;return b}lQ.K="internal.userDataNeedsEncryption";var mQ;function nQ(a,b,c){mQ=mQ||new bi;mQ.add(a,b,c)}function oQ(a,b){var c=mQ=mQ||new bi;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=pb(b)?wh(a,b):xh(a,b)}
function pQ(){return function(a){var b;var c=mQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.J.pb();if(e){var f=!1,g=e.Jb();if(g){Dh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function qQ(){var a=function(c){return void oQ(c.K,c)},b=function(c){return void nQ(c.publicName,c)};b(lD);b(sD);b(GE);b(IE);b(JE);b(QE);b(SE);b(UF);b(mP());b(WF);b(uL);b(vL);b(SL);b(TL);b(UL);b($L);b(NO);b(QO);b(cP);b(qP);b(tP);b(wP);b(zP);b(BP);b(MP);b(PP);b(SP);b(WP);b($P);b(eQ);b(kQ);nQ("Math",Bh());nQ("Object",$h);nQ("TestHelper",di());nQ("assertApi",yh);nQ("assertThat",zh);nQ("decodeUri",Eh);nQ("decodeUriComponent",Fh);nQ("encodeUri",Gh);nQ("encodeUriComponent",Hh);nQ("fail",Mh);nQ("generateRandom",
Nh);nQ("getTimestamp",Oh);nQ("getTimestampMillis",Oh);nQ("getType",Ph);nQ("makeInteger",Sh);nQ("makeNumber",Th);nQ("makeString",Uh);nQ("makeTableMap",Vh);nQ("mock",Yh);nQ("mockObject",Zh);nQ("fromBase64",nL,!("atob"in w));nQ("localStorage",pP,!oP());nQ("toBase64",hQ,!("btoa"in w));a(kD);a(oD);a(ID);a(UD);a(aE);a(fE);a(vE);a(EE);a(HE);a(KE);a(LE);a(ME);a(NE);a(OE);a(PE);a(RE);a(TE);a(TF);a(VF);a(XF);a(YF);a(ZF);a($F);a(aG);a(AH);a(FH);a(NH);a(OH);a(ZH);a(dI);a(iI);a(rI);a(wI);a(JI);a(LI);a(ZI);a($I);
a(bJ);a(lL);a(mL);a(oL);a(pL);a(qL);a(rL);a(sL);a(xL);a(yL);a(zL);a(AL);a(BL);a(CL);a(DL);a(EL);a(FL);a(GL);a(HL);a(IL);a(JL);a(LL);a(ML);a(NL);a(OL);a(PL);a(QL);a(RL);a(VL);a(WL);a(XL);a(YL);a(ZL);a(bM);a(LO);a(PO);a(SO);a(aP);a(bP);a(dP);a(eP);a(fP);a(gP);a(hP);a(iP);a(jP);a(kP);a(lP);a(nP);a(tE);a(rP);a(sP);a(uP);a(vP);a(xP);a(AP);a(CP);a(DP);a(FP);a(GP);a(HP);a(JP);a(KP);a(LP);a(NP);a(OP);a(QP);a(RP);a(TP);a(UP);a(VP);a(XP);a(YP);a(ZP);a(aQ);a(bQ);a(cQ);a(dQ);a(gQ);a(iQ);a(jQ);a(lQ);oQ("internal.IframingStateSchema",
OO());
G(104)&&a(wL);G(160)?b(aP):b(YO);G(177)&&b(yP);return pQ()};var iD;
function rQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;iD=new af;sQ();If=hD();var e=iD,f=qQ(),g=new Ad("require",f);g.Ra();e.C.C.set("require",g);Xa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&cg(n,d[m]);try{iD.execute(n),G(120)&&ik&&n[0]===50&&h.push(n[1])}catch(r){}}G(120)&&(Vf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");cj[q]=["sandboxedScripts"]}tQ(b)}function sQ(){iD.Rc(function(a,b,c){Nn.SANDBOXED_JS_SEMAPHORE=Nn.SANDBOXED_JS_SEMAPHORE||0;Nn.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Nn.SANDBOXED_JS_SEMAPHORE--}})}function tQ(a){a&&yb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");cj[e]=cj[e]||[];cj[e].push(b)}})};function uQ(a){ZA(UA("developer_id."+a,!0),0,{})};var vQ=Array.isArray;function wQ(a,b){return sd(a,b||null)}function W(a){return window.encodeURIComponent(a)}function xQ(a,b,c){Oc(a,b,c)}
function yQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=mj(sj(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function zQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function AQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=zQ(b,"parameter","parameterValue");e&&(c=wQ(e,c))}return c}function BQ(a,b,c){return a===void 0||a===c?b:a}function CQ(){try{if(!G(243))return null;var a=[],b;a:{try{b=!!bG('script[data-requiremodule^="mage/"]');break a}catch(g){}b=!1}b&&a.push("ac");var c;a:{try{c=!!bG('script[src^="//assets.squarespace.com/"]');break a}catch(g){}c=!1}c&&a.push("sqs");var d;a:{try{d=!!bG('script[id="d-js-core"]');break a}catch(g){}d=!1}d&&a.push("dud");var e;a:{try{e=!!bG('script[src*="woocommerce"],link[href*="woocommerce"],[class|="woocommerce"]');break a}catch(g){}e=!1}e&&a.push("woo");var f;a:{try{f=!!bG('meta[content*="fourthwall"],script[src*="fourthwall"],link[href*="fourthwall"]');
break a}catch(g){}f=!1}f&&a.push("fw");if(a.length>0)return{plf:a.join(".")}}catch(g){}return null};function DQ(a,b,c){return Kc(a,b,c,void 0)}function EQ(a,b){return Xo(a,b||2)}function FQ(a,b){w[a]=b}function GQ(a,b,c){var d=w;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}var HQ={};var X={securityGroups:{}};
X.securityGroups.access_template_storage=["google"],X.__access_template_storage=function(){return{assert:function(){},V:function(){return{}}}},X.__access_template_storage.F="access_template_storage",X.__access_template_storage.isVendorTemplate=!0,X.__access_template_storage.priorityOverride=0,X.__access_template_storage.isInfrastructure=!1,X.__access_template_storage["5"]=!1;

X.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){X.__read_event_data=b;X.__read_event_data.F="read_event_data";X.__read_event_data.isVendorTemplate=!0;X.__read_event_data.priorityOverride=0;X.__read_event_data.isInfrastructure=!1;X.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!qb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Ng(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},V:a}})}();




X.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){X.__detect_history_change_events=b;X.__detect_history_change_events.F="detect_history_change_events";X.__detect_history_change_events.isVendorTemplate=!0;X.__detect_history_change_events.priorityOverride=0;X.__detect_history_change_events.isInfrastructure=!1;X.__detect_history_change_events["5"]=!1})(function(){return{assert:function(){},V:a}})}();


X.securityGroups.read_container_data=["google"],X.__read_container_data=function(){return{assert:function(){},V:function(){return{}}}},X.__read_container_data.F="read_container_data",X.__read_container_data.isVendorTemplate=!0,X.__read_container_data.priorityOverride=0,X.__read_container_data.isInfrastructure=!1,X.__read_container_data["5"]=!1;
X.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){X.__listen_data_layer=b;X.__listen_data_layer.F="listen_data_layer";X.__listen_data_layer.isVendorTemplate=!0;X.__listen_data_layer.priorityOverride=0;X.__listen_data_layer.isInfrastructure=!1;X.__listen_data_layer["5"]=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!qb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},V:a}})}();
X.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){X.__detect_user_provided_data=b;X.__detect_user_provided_data.F="detect_user_provided_data";X.__detect_user_provided_data.isVendorTemplate=!0;X.__detect_user_provided_data.priorityOverride=0;X.__detect_user_provided_data.isInfrastructure=!1;X.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},V:a}})}();




X.securityGroups.access_consent=["google"],function(){function a(b,c,d){var e={consentType:c,read:!1,write:!1};switch(d){case "read":e.read=!0;break;case "write":e.write=!0;break;default:throw Error("Invalid "+b+" request "+d);}return e}(function(b){X.__access_consent=b;X.__access_consent.F="access_consent";X.__access_consent.isVendorTemplate=!0;X.__access_consent.priorityOverride=0;X.__access_consent.isInfrastructure=!1;X.__access_consent["5"]=!1})(function(b){for(var c=b.vtp_consentTypes||[],d=
b.vtp_createPermissionError,e=[],f=[],g=0;g<c.length;g++){var h=c[g],m=h.consentType;h.read&&e.push(m);h.write&&f.push(m)}return{assert:function(n,p,q){if(!qb(p))throw d(n,{},"Consent type must be a string.");if(q==="read"){if(e.indexOf(p)>-1)return}else if(q==="write"){if(f.indexOf(p)>-1)return}else throw d(n,{},"Access type must be either 'read', or 'write', was "+q);throw d(n,{},"Prohibited "+q+" on consent type: "+p+".");},V:a}})}();




X.securityGroups.read_dom_elements=["google"],function(){function a(b,c,d){return{type:c,value:d}}(function(b){X.__read_dom_elements=b;X.__read_dom_elements.F="read_dom_elements";X.__read_dom_elements.isVendorTemplate=!0;X.__read_dom_elements.priorityOverride=0;X.__read_dom_elements.isInfrastructure=!1;X.__read_dom_elements["5"]=!1})(function(b){var c=b.vtp_allowedElementIds||"none",d=b.vtp_allowedCssSelectors||"none",e=b.vtp_elementIds||[],f=b.vtp_cssSelectors||[],g=b.vtp_createPermissionError;return{assert:function(h,
m,n){switch(m){case "id":if(c==="none")break;if(c==="any"||e.indexOf(n)>-1)return;break;case "css":if(d==="none")break;if(d==="any"||f.indexOf(n)>-1)return;break;default:throw g(h,{},"Unknown selector type "+m+".");}throw g(h,{},"Prohibited selector value "+n+" for selector type "+m+".");},V:a}})}();
X.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){X.__gct=b;X.__gct.F="gct";X.__gct.isVendorTemplate=!0;X.__gct.priorityOverride=0;X.__gct.isInfrastructure=!1;X.__gct["5"]=!0})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[K.m.uf]=d);c[K.m.Lg]=b.vtp_eventSettings;c[K.m.Ak]=b.vtp_dynamicEventSettings;c[K.m.je]=b.vtp_googleSignals===1;c[K.m.Qk]=b.vtp_foreignTld;c[K.m.Nk]=b.vtp_restrictDomain===
1;c[K.m.ei]=b.vtp_internalTrafficResults;var e=K.m.Va,f=b.vtp_linker;f&&f[K.m.ma]&&(f[K.m.ma]=a(f[K.m.ma]));c[e]=f;var g=K.m.gi,h=b.vtp_referralExclusionDefinition;h&&h.include_conditions&&(h.include_conditions=a(h.include_conditions));c[g]=h;Cp(b.vtp_trackingId,c);DO(b.vtp_trackingId,b.vtp_gtmEventId);Rc(b.vtp_gtmOnSuccess)})}();



X.securityGroups.get=["google"],X.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=XA(String(b.streamId),d,c);ZA(f,e.eventId,e);a.vtp_gtmOnSuccess()},X.__get.F="get",X.__get.isVendorTemplate=!0,X.__get.priorityOverride=0,X.__get.isInfrastructure=!1,X.__get["5"]=!1;



var Qn={dataLayer:Yo,callback:function(a){bj.hasOwnProperty(a)&&pb(bj[a])&&bj[a]();delete bj[a]},bootstrap:0};
function IQ(){Pn();Mj();Az();Ib(cj,X.securityGroups);var a=Ij(Jj()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;en(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||O(142);Uf={Np:ig}}var JQ=!1;G(218)&&(JQ=Di(47,JQ));
function qm(){try{if(JQ||!Uj()){Qi();G(218)&&(Hi.C=Di(50,Hi.C));
Hi.Xa=Ji(4,'ad_storage|analytics_storage|ad_user_data|ad_personalization');Hi.Ja=Ji(5,'ad_storage|analytics_storage|ad_user_data');Hi.la=Ji(11,'5840');Hi.la=Ji(10,'5940');
G(218)&&(Hi.R=Di(51,Hi.R));if(G(109)){}Ua[7]=!0;var a=On("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});mn(a);Mn();VC();jq();Un();if(Nj()){Fi(5);qE();qA().removeExternalRestrictions(Fj());}else{
wJ();eo();Sf();Of=X;Pf=DC;Rv();rQ();IQ();BC();om||(nm=sm(),G(244)&&nm["0"]&&Ml(Hl.X.se,JSON.stringify(nm)));In();NB();Ji(6,'1');Ji(7,'10');Ji(35,'');SA();uB=!1;z.readyState==="complete"?wB():Pc(w,"load",wB);
MA();ik&&(fp(sp),w.setInterval(rp,864E5),fp(WC),fp(dA),fp(Wx),fp(vp),fp(eD),fp(oA),G(120)&&(fp(iA),fp(jA),fp(kA)),YC={},ZC={},fp(aD),fp(bD),Ki(),G(261)&&fp(XC));kk&&(cm(),wo(),PB(),YB(),WB(),Vl("bt",String(Hi.H?2:Hi.C?1:0)),Vl("ct",String(Hi.H?0:Hi.C?1:3)),SB(),VB());sC();mm(1);rE();aj=Fb();Qn.bootstrap=aj;Hi.R&&MB();G(109)&&ry();G(134)&&(typeof w.name==="string"&&
Kb(w.name,"web-pixel-sandbox-CUSTOM")&&hd()?uQ("dMDg0Yz"):w.Shopify&&(uQ("dN2ZkMj"),hd()&&uQ("dNTU0Yz")))}}}catch(b){mm(4),op()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Sm(n)&&(m=h.yl)}function c(){m&&Bc?g(m):a()}if(!w[Fi(37)]){var d=!1;if(z.referrer){var e=sj(z.referrer);d=oj(e,"host")===Fi(38)}if(!d){var f=rr(Fi(39));d=!(!f.length||!f[0].length)}d&&(w[Fi(37)]=!0,Kc(Fi(40)))}var g=function(u){var v="GTM",x="GTM";Xi&&(v="OGT",x="GTAG");var y=Fi(23),A=w[y];A||(A=[],w[y]=A,Kc("https://"+Fi(3)+"/debug/bootstrap?id="+Fi(5)+"&src="+x+"&cond="+String(u)+"&gtm="+Nq()));var D={messageType:"CONTAINER_STARTING",
data:{scriptSource:Bc,containerProduct:v,debug:!1,id:Fi(5),targetRef:{ctid:Fi(5),isDestination:Dj(),canonicalId:Fi(6)},aliases:Gj(),destinations:Ej()}};D.data.resume=function(){a()};Ei(2)&&(D.data.initialPublish=!0);A.push(D)},h={Wo:1,Ll:2,fm:3,gk:4,yl:5};h[h.Wo]="GTM_DEBUG_LEGACY_PARAM";h[h.Ll]="GTM_DEBUG_PARAM";h[h.fm]="REFERRER";h[h.gk]="COOKIE";h[h.yl]="EXTENSION_PARAM";var m=void 0,n=void 0,p=mj(w.location,"query",!1,void 0,"gtm_debug");Sm(p)&&(m=h.Ll);if(!m&&z.referrer){var q=sj(z.referrer);
oj(q,"host")===Fi(24)&&(m=h.fm)}if(!m){var r=rr("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.gk)}m||b();if(!m&&Rm(n)){var t=!1;Pc(z,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);w.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){!JQ||sm()["0"]?qm():pm()});

})()

