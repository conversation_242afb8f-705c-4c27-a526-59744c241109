/* GptMeasurement HouseHold ImplementationValidator LegacyLayer SignalsAbs SignalsAutoInsertion SignalsBsc SignalsIds SignalsTvp SignalsVlp Visibility */
(()=>{var an=Object.create;var rt=Object.defineProperty,ln=Object.defineProperties,Wt=Object.getOwnPropertyDescriptor,dn=Object.getOwnPropertyDescriptors,un=Object.getOwnPropertyNames,Kt=Object.getOwnPropertySymbols,cn=Object.getPrototypeOf,Yt=Object.prototype.hasOwnProperty,mn=Object.prototype.propertyIsEnumerable;var bt=(n,e,t)=>e in n?rt(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,L=(n,e)=>{for(var t in e||(e={}))Yt.call(e,t)&&bt(n,t,e[t]);if(Kt)for(var t of Kt(e))mn.call(e,t)&&bt(n,t,e[t]);return n},Ue=(n,e)=>ln(n,dn(e)),i=(n,e)=>rt(n,"name",{value:e,configurable:!0});var zt=(n,e)=>()=>(n&&(e=n(n=0)),e);var pn=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports);var gn=(n,e,t,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of un(e))!Yt.call(n,o)&&o!==t&&rt(n,o,{get:()=>e[o],enumerable:!(r=Wt(e,o))||r.enumerable});return n};var fn=(n,e,t)=>(t=n!=null?an(cn(n)):{},gn(e||!n||!n.__esModule?rt(t,"default",{value:n,enumerable:!0}):t,n));var d=(n,e,t,r)=>{for(var o=r>1?void 0:r?Wt(e,t):e,l=n.length-1,c;l>=0;l--)(c=n[l])&&(o=(r?c(e,t,o):c(o))||o);return r&&o&&rt(e,t,o),o};var u=(n,e,t)=>bt(n,typeof e!="symbol"?e+"":e,t);var T=(n,e,t)=>new Promise((r,o)=>{var l=E=>{try{f(t.next(E))}catch(y){o(y)}},c=E=>{try{f(t.throw(E))}catch(y){o(y)}},f=E=>E.done?r(E.value):Promise.resolve(E.value).then(l,c);f((t=t.apply(n,e)).next())});var m,s=zt(()=>{m={"21618997260":{adServerId:6241,customTracking:{}}}});var a=zt(()=>{});var en=pn((Dv,Zr)=>{s();a();function Yo(n,e){if(typeof n!="function")throw new TypeError(`Expected the first argument to be a \`function\`, got \`${typeof n}\`.`);let t,r=0;return i(function(...l){clearTimeout(t);let c=Date.now(),f=c-r,E=e-f;E<=0?(r=c,n.apply(this,l)):t=setTimeout(()=>{r=Date.now(),n.apply(this,l)},E)},"throttled")}i(Yo,"throttle");Zr.exports=Yo});s();a();s();a();s();a();s();a();s();a();s();a();s();a();var U=[];for(let n=0;n<256;++n)U.push((n+256).toString(16).slice(1));function Xt(n,e=0){return(U[n[e+0]]+U[n[e+1]]+U[n[e+2]]+U[n[e+3]]+"-"+U[n[e+4]]+U[n[e+5]]+"-"+U[n[e+6]]+U[n[e+7]]+"-"+U[n[e+8]]+U[n[e+9]]+"-"+U[n[e+10]]+U[n[e+11]]+U[n[e+12]]+U[n[e+13]]+U[n[e+14]]+U[n[e+15]]).toLowerCase()}i(Xt,"unsafeStringify");s();a();var wt,vn=new Uint8Array(16);function Dt(){if(!wt){if(typeof crypto=="undefined"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");wt=crypto.getRandomValues.bind(crypto)}return wt(vn)}i(Dt,"rng");s();a();s();a();var Sn=typeof crypto!="undefined"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),It={randomUUID:Sn};function En(n,e,t){var o,l,c;if(It.randomUUID&&!e&&!n)return It.randomUUID();n=n||{};let r=(c=(l=n.random)!=null?l:(o=n.rng)==null?void 0:o.call(n))!=null?c:Dt();if(r.length<16)throw new Error("Random bytes length must be >= 16");if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,e){if(t=t||0,t<0||t+16>e.length)throw new RangeError(`UUID byte range ${t}:${t+15} is out of buffer bounds`);for(let f=0;f<16;++f)e[t+f]=r[f];return e}return Xt(r)}i(En,"v4");var Pt=En;var pe=i(()=>Pt(),"uuid");s();a();var Qe=i((n,e)=>(...t)=>{try{let r=n(...t);return r instanceof Promise?r.then(o=>o,o=>e(o,...t)):r}catch(r){return e(r,...t)}},"withErrorHandler");s();a();var He,Ke,We,ie=class ie{constructor(e,t){u(this,"collections",new Set);u(this,"children",new ie.Set);u(this,"beforeDestroyed");this.beforeDestroyed=t!=null?t:()=>{};for(let[r,o]of Object.entries(e))Object.defineProperty(this,r,{value:o});e.parent!=null&&(e.parent.destroyed?this.destroy():e.parent.children.add(this))}static Create(e,t){return new ie(e,t)}static CreateRoot(e,t){return new ie(e,t)}destroy(){if(this.beforeDestroyed!=null){try{this.beforeDestroyed(this)}catch(e){}for(let e of this.children)e.destroy();this.beforeDestroyed=void 0;for(let e of this.collections)e.delete(this)}return this}get destroyed(){return this.beforeDestroyed==null}};i(ie,"EntityClass"),u(ie,"Set",(He=class extends Set{add(t){return t.destroyed||(t.collections.add(this),super.add(t)),this}delete(t){return t.collections.delete(this),super.delete(t)}clear(){for(let t of this.values())t.collections.delete(this);super.clear()}peek(){return this.values().next().value}conjure(t){let r=this.peek();return r==null&&(r=t(),this.add(r)),r}},i(He,"EntitySet"),He)),u(ie,"Map",(Ke=class extends Map{set(t,r){return t.destroyed||(t.collections.add(this),super.set(t,r)),this}delete(t){return t.collections.delete(this),super.delete(t)}clear(){for(let t of this.keys())t.collections.delete(this);return super.clear()}summon(t,r){let o=super.get(t);return o==null&&(o=r,this.set(t,o)),o}conjure(t,r){let o=super.get(t);return o==null&&(o=r(),this.set(t,o)),o}},i(Ke,"EntityMap"),Ke)),u(ie,"Link",(We=class extends ie.Map{get(t){return super.conjure(t,()=>new ie.Set)}},i(We,"EntityLink"),We));var Fe=ie,v;(l=>(l.Set=Fe.Set,l.Map=Fe.Map,l.Link=Fe.Link,l.Create=Fe.Create,l.CreateRoot=Fe.CreateRoot))(v||(v={}));s();a();var mt=class mt{constructor(e,t,r){u(this,"_message","unknown");u(this,"_trace",[]);u(this,"_silenced",!1);if(e instanceof mt)return e.addToTrace(r,t);typeof e=="object"&&e!=null&&"message"in e&&typeof e.message=="string"?this._message=e.message:this._message=`${e}`,this.addToTrace(r,t)}get message(){return this._message}get trace(){return this._trace}get silenced(){return this._silenced}silence(){return this._silenced=!0,this}addToTrace(e,t){return this._trace.unshift(e),this.maybeCompleteTrace(t),this}maybeCompleteTrace(e){return T(this,null,function*(){let t=this._trace.length;yield Promise.resolve(),yield Promise.resolve(),yield Promise.resolve(),!this.silenced&&t===this._trace.length&&e(this)})}};i(mt,"TracedError");var nt=mt;var Ot=class Ot{constructor(e){u(this,"moduleInstances",new Map);u(this,"methodsToExecute",[]);u(this,"errorHandlers",[]);u(this,"activeSession",null);u(this,"executedMethodIndex",0);this.initializeModules(e)}initializeModules(e){let t=e.map(r=>this.createModule(r));for(let r of t)this.applyModuleDecoration(r),this.applyExecuteDecoration(r),this.applyPluginDecoration(r),this.applyErrorDecoration(r),this.applyCatchErrorDecoration(r)}get session(){if(this.activeSession==null)throw new Error("Core has no active session");return this.activeSession}inject(e){let t=this.modules,r=[];for(let o of e){let l=t[o.name];l==null?r.push(o):this.moduleInstances.set(o,l)}this.initializeModules(r),console.log("Injected new modules:",r.map(o=>o.name)),this.execute()}get(e){return this.getModule(e)}execute(){this.activeSession==null&&(this.activeSession=v.CreateRoot({id:pe()}));for(let e of this.methodsToExecute.slice(this.executedMethodIndex))e();this.executedMethodIndex=this.methodsToExecute.length}reset(){this.activeSession!=null&&(this.activeSession.destroy(),this.activeSession=null,this.executedMethodIndex=0)}get modules(){let e={};for(let[t,r]of this.moduleInstances)e[t.name]=r;return e}getModule(e){let t=this.moduleInstances.get(e);if(t==null)throw new Error(`module not in core: ${e.name}`);return t}createModule(e){if(e.prototype.moduleConfig==null)throw new Error(`not a module: ${e.name}`);let t=new e;return t.core=this,this.moduleInstances.set(e,t),t}applyModuleDecoration(e){var r;let t=e.constructor.prototype.moduleConfig;for(let{method:o}of t)e[o]=Qe(e[o].bind(e),(l,...c)=>{throw this.traceError(l,`${e.constructor.name}.${o}(${c.map(f=>typeof f).join(",")})`)});(r=e.plugins)!=null||(e.plugins={}),e.pluginFunctions={};for(let o of Object.keys(e.plugins))e.pluginFunctions[o]=[],e.plugins[o]=(...l)=>{let c=[];for(let f of e.pluginFunctions[o])f(c,...l);return c}}applyExecuteDecoration(e){var r;let t=(r=e.constructor.prototype.executeConfig)!=null?r:[];for(let{method:o}of t)this.methodsToExecute.push(Qe(e[o],l=>{this.traceError(l,"@execute")}))}applyPluginDecoration(e){var r;let t=(r=e.constructor.prototype.pluginConfig)!=null?r:[];for(let{PluggableClass:o,category:l,method:c}of t)this.getModule(o).pluginFunctions[l].push(Qe((f,...E)=>{f.push(e[c](...E))},f=>{this.traceError(f,`@plugin(${o.name},'${l}')`)}))}applyErrorDecoration(e){var r;let t=(r=e.constructor.prototype.errorConfig)!=null?r:[];for(let{method:o}of t)this.errorHandlers.push(Qe(e[o],l=>{l instanceof nt&&l.silence()}))}applyCatchErrorDecoration(e){var r;let t=(r=e.constructor.prototype.catchErrorConfig)!=null?r:[];for(let{method:o}of t)e[o]=Qe(e[o],l=>{this.traceError(l,"@catchError")})}traceError(e,t){return new nt(e,r=>this.reportError(r),t)}reportError(e){for(let t of this.errorHandlers)t(e.message,[...e.trace])}};i(Ot,"Core");var ot=Ot;s();a();s();a();var xt=[],pt=xt;var p=i(n=>{let e=n.prototype,t=Object.getOwnPropertyNames(e).filter(r=>{var o;return r!=="constructor"&&((o=Object.getOwnPropertyDescriptor(e,r))==null?void 0:o.value)instanceof Function});e.moduleConfig=t.map(r=>({method:r})),xt.push(n)},"moduleClass"),P=i(n=>e=>{},"attachModule"),w=i((n,e)=>{var t;n.executeConfig=(t=n.executeConfig)!=null?t:[],n.executeConfig.push({method:e})},"executeMethod"),h=i((n,e)=>(t,r)=>{var o;t.pluginConfig=(o=t.pluginConfig)!=null?o:[],t.pluginConfig.push({PluggableClass:n,category:e,method:r})},"pluginMethod"),Z=i((n,e)=>{var t;n.catchErrorConfig=(t=n.catchErrorConfig)!=null?t:[],n.catchErrorConfig.push({method:e})},"catchErrorMethod"),Lt=i((n,e)=>{var t;n.errorConfig=(t=n.errorConfig)!=null?t:[],n.errorConfig.push({method:e})},"errorMethod");s();a();var it=["BSC","ABS","CCT","CBS"],Nt=["IDS"],st=["VLP","TVP"],Tn=[...it,...Nt,...st],As=[...it,...Nt,...st].filter(n=>n!=="CCT"),Jt=i(n=>Tn.includes(n.toUpperCase()),"isSignal"),Zt=i(n=>st.includes(n.toUpperCase()),"isSlotSignal"),er=i(n=>Nt.includes(n.toUpperCase()),"isUserSignal"),tr=i(n=>it.includes(n.toUpperCase()),"isPageSignal");s();a();s();a();s();a();var A=class{constructor(){u(this,"plugins",{slotDiscovered:i((e,t)=>[],"slotDiscovered")});u(this,"slots",new v.Set)}getSlot(e,t){for(let r of this.slots)if(r.adServer===e&&r.elementId===t)return r}getSlots(e){let t=[...this.slots];return e==null?t:t.filter(r=>r.adServer===e)}createSlot(e,t,r,o,l){let c=this.getSlot(e,r);c!=null&&c.destroy();let f=v.Create({parent:this.core.session,adServer:e,elementId:r,adUnit:o,uuid:pe(),creationTime:Date.now()});return this.slots.add(f),l==null||l(f),this.plugins.slotDiscovered(f,t),f}};i(A,"SlotStore"),A=d([p],A);s();a();var Ee=i((...n)=>{let e=[];for(let t of n)if(t!=null)for(let r of t)e.includes(r)||e.push(r);return e},"mergeUnique");var R=class{constructor(){u(this,"plugins",{getAdServerTargeting:i((e,t)=>[],"getAdServerTargeting"),setAdServerTargeting:i((e,t,r)=>[],"setAdServerTargeting")});u(this,"globalTargeting",{});u(this,"slotTargeting",new v.Map)}_getSlotTargeting(e){return this.slotTargeting.summon(e,{})}_getTargetingKeys(e){return Ee(Object.keys(this.globalTargeting),Object.keys(this._getSlotTargeting(e)))}_getTargetingValues(e,t){return Ee(this.globalTargeting[t],this._getSlotTargeting(e)[t])}getTargeting(e){let t={};if(e==null)for(let r of Object.keys(this.globalTargeting))t[r]=[...this.globalTargeting[r]];else for(let r of this._getTargetingKeys(e))t[r]=this._getTargetingValues(e,r);return t}setSlotTargeting(e,t,r=!1){var c;let o=Object.keys(t),l=this._getSlotTargeting(e);for(let f of o)l[f]=Ee(t[f],r?(c=this.slotTargeting.get(e))==null?void 0:c[f]:null);this.setAdServerTargeting(e,o)}setGlobalTargeting(e,t=!1){let r=Object.keys(e);for(let o of r)this.globalTargeting[o]=Ee(e[o],t?this.globalTargeting[o]:null);for(let o of this.core.get(A).getSlots())this.setAdServerTargeting(o,r)}getAdServerTargeting(e,t){var r;return(r=this.plugins.getAdServerTargeting(e,t).find(o=>o.length!==0))!=null?r:[]}setAdServerTargeting(e,t){t==null&&(t=this._getTargetingKeys(e));for(let r of t)this.plugins.setAdServerTargeting(e,r,this._getTargetingValues(e,r))}_onSlotDiscovered(e){this.setAdServerTargeting(e)}};i(R,"TargetingHub"),d([h(A,"slotDiscovered")],R.prototype,"_onSlotDiscovered",1),R=d([p],R);s();a();s();a();s();a();var yn=[[/((\?)|&)(?:amp_lite|fr=operanews)/g,"$2"],[/(^https?:\/\/)(www\.)/g,"$1"],[/(((\?)|&|%3F|%26|;)(?:utm_campaign|utm_source|utm_content|utm_term|utm_viral|utm_medium|utm_identifier|utm_key|fb_source|referrer|referer|ref|rand|rnd|randid|\.?rand|\.?partner|cb|count|adid|session|sessionid|session_redirect|param\d|userinfo|uid|refresh|ocid|ncid|clickenc|fbclid|amp_js_v|amp_gsa|ns_source|ns_campaign|ns_mchanel|ns_mchannel|ns_linkname|ns_fee|src|ex_cid|usqp|source|xid|trkid|utm_social-type|mbid|utm_brand|__twitter_impression|utm_referrer|CMP|cmp|_cmp|cq_cmp)=[^&]*)/g,"$3"],[/[/.]amp(?:\/[?&]*)?$/g,""],[/((\?)|&)(?:outputType|isFollow|suppressMediaBar|cid|ICID|icid|bdk|wtu_id_h|utm_placement|intcmp|_native_ads|traffic_source|entry|\.tsrc|autoplay|autostart|dc_data|influencer|pubdate|utm_hp_ref|redirect|service|refresh_ce|refresh_cens|xcmg|target_id|_amp)=[^&]*/g,"$2"],[/(\?|&)(?:amp)(?:&|$|=[^&]*)/g,"$1"]],An=[[/(?:\/|\/\?|\?|&|\/\$0|#[\s\S]*)+$/g,""],[/(?:\/|\/\?|\?|&|\/\$0|#[\s\S]*)+$/g,""]],rr=i((n,e)=>{for(let[t,r]of n)e=e.replace(t,r);return e},"replaceByRules"),nr=i(n=>{let e=rr(yn,n);return rr(An,e)},"normalizeUrl");s();a();var or=i(n=>n.split("?",1)[0].split("#",1)[0],"stripUrlQuery");s();a();var S=class{constructor(){u(this,"timeFunction",new v.Map)}window(){return window}document(){return this.window().document}pageUrl(){var e,t;return(t=(e=this.window().location)==null?void 0:e.href)!=null?t:""}hostname(){return new URL(this.pageUrl()).hostname}time(){return this.timeFunction.conjure(this.core.session,()=>{let e=this.window(),t=!1;try{t=typeof e.performance.now()=="number"}catch(r){}return t?()=>Math.floor(e.performance.now()):()=>e.Date.now()})()}sendBeacon(e){try{let t=this.window().navigator;typeof(t==null?void 0:t.sendBeacon)=="function"?t.sendBeacon(e):new Image().src=e}catch(t){}}};i(S,"Env"),S=d([p],S);s();a();var ir=i((n,e)=>{try{let t=new URLSearchParams(n),r=t.get("ctx"),o=t.get("cmp");if(r!=null&&o!=null)return{ctx:r,cmp:o,prefetch:e,legacy:!0}}catch(t){}return null},"parseInfo"),sr=i(n=>{var e;return n.pathname!=="/signals/pub.js"?null:(e=ir(n.search,!0))!=null?e:ir(n.hash.substring(1).split("?")[0],!1)},"parseLegacyPagetagUrl");var Rn=1e3,_n=/^\/dvtag\/([^/]+)\/([^/]+)\/pub\.js$/,D=class{constructor(){u(this,"visit");u(this,"tagInfo");u(this,"plugins",{newVisit:i(e=>[],"newVisit"),overrideTagInfo:i(e=>[],"overrideTagInfo")});u(this,"_normalizeUrl",nr)}_initialize(){this.getTagInfo(),this.updateVisit(!1),this.plugins.newVisit(this.visit),this._installNavigationListener()}getTagInfo(){var e;if(this.tagInfo==null){try{this.tagInfo=this._parseTagInfo()}catch(r){}(e=this.tagInfo)!=null||(this.tagInfo={ctx:"unknown",cmp:"unknown"});let t=this.plugins.overrideTagInfo(this.tagInfo);t.length>0&&(this.tagInfo=L(L({},this.tagInfo),t[0]))}return this.tagInfo}_parseTagInfo(){let e=this.getCurrentScriptSrc();if(e==null)return null;let t=new URL(e,"https://pub.doubleverify.com"),r=t.pathname,o=_n.exec(r);if(o!=null){let[,l,c]=o;return{ctx:l,cmp:c}}return sr(t)}getCurrentScriptSrc(){var e,t;return(t=(e=this.core.get(S).window().document.currentScript)==null?void 0:e.getAttribute("src"))!=null?t:null}getCurrentScriptBaseUrl(){let e=this.getCurrentScriptSrc();if(e==null)return null;try{let{origin:t,pathname:r}=new URL(e),o=r.split("/").slice(0,-1).join("/");return`${t}${o}/`}catch(t){return null}}updateVisit(e=!0){var r,o;let t=this.getStrippedPageUrl();if(this.visit==null||this.visit.strippedUrl!==t){let l=pe();this.visit={strippedUrl:t,normalizedUrl:this._normalizeUrl(this.core.get(S).window().location.href),uuid:l,sessionUuid:(o=(r=this.visit)==null?void 0:r.sessionUuid)!=null?o:l},e&&this.plugins.newVisit(this.visit)}return this.visit}getVisit(){var e;return(e=this.visit)!=null?e:this.updateVisit(!1)}getStrippedPageUrl(){return or(this.core.get(S).window().location.href)}getNormalizedPageUrl(){return this.getVisit().normalizedUrl}getVisitUuid(){return this.getVisit().uuid}getSessionUuid(){return this.getVisit().sessionUuid}_installNavigationListener(){var r;let e=this.core.get(S).window(),t=i(()=>this.updateVisit(!0),"cb");((r=e.navigation)==null?void 0:r.addEventListener)!=null?e.navigation.addEventListener("navigatesuccess",t):setInterval(t,Rn)}};i(D,"Context"),d([w],D.prototype,"_initialize",1),D=d([p],D);s();a();s();a();var qe=i(()=>(...n)=>[],"apiPlugin"),N=class{constructor(){u(this,"plugins",{queueAdRequest:qe(),defineSlot:qe(),adRendered:qe(),adRemoved:qe(),getTargeting:qe(),addEventListener:i((e,t)=>[],"addEventListener"),removeEventListener:i((e,t)=>[],"removeEventListener"),toggleDebugMode:qe(),_debugScriptLoaded:qe()})}_initialize(){var r;let e=this.core.get(S).window(),t=(r=e.dvtag)!=null?r:{};e.dvtag=t,t.queueAdRequest=this.queueAdRequest,t.defineSlot=this.defineSlot,t.adRendered=this.adRendered,t.adRemoved=this.adRemoved,t.getTargeting=this.getTargeting,t.addEventListener=this.addEventListener,t.removeEventListener=this.removeEventListener,t.toggleDebugMode=this.toggleDebugMode,t._debugScriptLoaded=this._debugScriptLoaded}queueAdRequest(...e){this.plugins.queueAdRequest(...e)}defineSlot(...e){this.plugins.defineSlot(...e)}adRendered(...e){this.plugins.adRendered(...e)}adRemoved(...e){this.plugins.adRemoved(...e)}getTargeting(...e){var t;return(t=this.plugins.getTargeting(...e)[0])!=null?t:{}}addEventListener(e,t){this.plugins.addEventListener(e,t)}removeEventListener(e,t){this.plugins.removeEventListener(e,t)}toggleDebugMode(...e){this.plugins.toggleDebugMode(...e)}_debugScriptLoaded(...e){this.plugins._debugScriptLoaded(...e)}};i(N,"Api"),d([w],N.prototype,"_initialize",1),N=d([p],N);s();a();var Mt=class Mt{constructor(){u(this,"pending",[]);u(this,"flushing",!1)}push(e){this.pending.push(e),this.flush()}flush(){return T(this,null,function*(){var t;if(this.flushing)return;this.flushing=!0;let e=this.pending.shift();for(;e!=null;){try{yield e.promise}catch(r){}try{(t=e.callback)==null||t.call(e)}catch(r){}e=this.pending.shift()}this.flushing=!1})}};i(Mt,"AsyncQueue");var gt=Mt;var C=class{constructor(){u(this,"plugins",{adRequestQueued:i(e=>[],"adRequestQueued"),adRequestTimedOut:i(e=>[],"adRequestTimedOut"),adRequestBeforeRelease:i(e=>[],"adRequestBeforeRelease"),adRequestCompleted:i(e=>[],"adRequestCompleted")});u(this,"queue",new gt);u(this,"adRequestCounter",0)}_queueAdRequest(e={}){var l,c;let t={index:this.adRequestCounter++,status:"queued",timeoutValue:(l=e.timeout)!=null?l:0,onDvtagReadyCalled:(c=e.timestamp)!=null?c:0,waitingStarted:new Date().getTime(),internal:e.internal};this.core.get(D).updateVisit(),this.plugins.adRequestQueued(t);let r=e.callback;r===void 0?e.callback=()=>{}:r===null?t.status="tag-timeout":e.callback=()=>{t.status="queue-timeout",t.callbackCalled=new Date().getTime(),e.callback=null,this.plugins.adRequestTimedOut(t);try{r()}catch(f){}};let o=i(()=>{if(e.callback!=null){e.callback=null,t.status="success",t.callbackCalled=new Date().getTime(),this.plugins.adRequestBeforeRelease(t);try{r==null||r()}catch(f){}}else this.plugins.adRequestBeforeRelease(t);t.waitingEnded=new Date().getTime(),this.plugins.adRequestCompleted(t)},"onAsyncOperationsCompleted");this.queue.push({callback:o,adRequest:t})}delayAdRequests(e){this.queue.push({promise:e})}};i(C,"AdRequestQueue"),d([h(N,"queueAdRequest")],C.prototype,"_queueAdRequest",1),C=d([p],C);s();a();s();a();s();a();var H=class{getShortCommitId(){return"1a4d010"}getBuildTimestamp(){return 1758830421191}getAuthToken(){return"r0tB3M60rIgRhYqLUQutrKZGG/c6Iq2Qt2esorMj1NQ32U8JEU1lx8cHlg/zQkSg3qN0oa0AKdev1wHgkyyKAbPb6RhgGnKGC4hYWVdK4jXJ2bfWlixCIayX4ieexXVslYq4CeQTS6LW+CoKHtaPO1d+5OYrtk0="}};i(H,"DefinedVariables"),H=d([p],H);var ee=class{constructor(){u(this,"cache",new v.Map)}_performFetchJson(e,t){return T(this,null,function*(){let r=this.core.get(S).window().fetch,o=e;if(t!=null&&t.authToken){let l=this.core.get(H).getAuthToken(),c=new URL(e);c.searchParams.set("token",l),o=c.href}try{return yield r(o).then(l=>l.json())}catch(l){return null}})}fetchJson(e,t){return T(this,null,function*(){let r=this.cache.summon(this.core.session,{}),o=r[e];return o==null&&(o=this._performFetchJson(e,t),r[e]=o),yield o})}};i(ee,"Fetch"),ee=d([p],ee);s();a();s();a();var ft=i(n=>n==null?"":`@${n}`,"getPositionSuffix");s();a();var bn=/^(\d+)x(\d+)$/,ar=i(n=>n==="all"?[]:n.split(",").map(e=>{let t=e.match(bn);return t==null?{label:e}:{width:parseInt(t[1],10),height:parseInt(t[2],10)}}),"stringToSlotSizes"),Ut=i(n=>n.length===0?"all":n.map(({width:e,height:t,label:r})=>r!=null?r:`${e}x${t}`).join(","),"slotSizesToString"),lr=i(n=>n==null?"":n.length>0&&n.every(({label:r})=>r==null)?Ut(n):"","slotSizesToStringIfSpecific");var wn="https://pub.doubleverify.com/dvtag/signals/",te=class{_getBaseUrl(e){let t=this.core.get(D),{ctx:r,cmp:o}=t.getTagInfo(),l=t.getNormalizedPageUrl();try{l=new URL(l).origin}catch(f){}let c=new URL(e,wn);return c.searchParams.set("ctx",r),c.searchParams.set("cmp",o),c.searchParams.set("url",l),c}getUserTargetingUrl(e){let t=this._getBaseUrl("ids/pub.json");return e.includes("IDS")&&t.searchParams.set("ids","1"),t.href}getPageTargetingUrl(e,t){let r=this._getBaseUrl("bsc/pub.json");return r.searchParams.set("url",t),e.includes("BSC")&&r.searchParams.set("bsc","1"),e.includes("ABS")&&r.searchParams.set("abs","1"),e.includes("CBS")&&r.searchParams.set("cbs","1"),e.includes("CCT")&&r.searchParams.set("cct","1"),r.href}getSlotTargetingUrl(e,t){var l;let r=this._getBaseUrl("vlp/pub.json");e.includes("VLP")&&r.searchParams.set("vlp","1"),e.includes("TVP")&&r.searchParams.set("tvp","1");let o=0;for(let c of t){let{id:f,position:E,sizes:y}=(l=c.adUnit)!=null?l:{},I=f+ft(E);r.searchParams.set(`slot-${o++}-${I}`,lr(y))}return r.href}};i(te,"SignalsUrlBuilder"),te=d([p],te);var be=class{constructor(){u(this,"cache",{})}_fetchFreshSignals(e,t){return T(this,null,function*(){if(!t.some(tr))return{};let r=this.core.get(te).getPageTargetingUrl(t,e),o=yield this.core.get(ee).fetchJson(r,{authToken:!0});return o instanceof Object?o:{}})}fetchSignals(e,t=3){return T(this,null,function*(){let r=this.core.get(D),o=r.getNormalizedPageUrl(),l=r.getStrippedPageUrl();this.cache[l]==null&&(this.cache[l]=this._fetchFreshSignals(o,e));let c=yield this.cache[l];return this.core.get(D).getStrippedPageUrl()!==l&&t>0?this.fetchSignals(e,t-1):c})}};i(be,"PageSignals"),be=d([p],be);s();a();var we=class{constructor(){u(this,"cache",{});u(this,"pendingSlots",[]);u(this,"pendingBulkRequest",null)}_scheduleBulkRequest(e){return T(this,null,function*(){yield Promise.resolve();let t=this.core.get(te).getSlotTargetingUrl(e,this.pendingSlots);this.pendingSlots=[],this.pendingBulkRequest=null;let r=yield this.core.get(ee).fetchJson(t,{authToken:!0});return r instanceof Array?r:[]})}_fetchFreshSignals(e,t){return T(this,null,function*(){if(!t.some(Zt))return{};let r=this.pendingSlots.push(e)-1;this.pendingBulkRequest==null&&(this.pendingBulkRequest=this._scheduleBulkRequest(t));let l=(yield this.pendingBulkRequest)[r];return l instanceof Object?l:{}})}_getCacheKey(e){var f,E;let{id:t,position:r,sizes:o}=(f=e.adUnit)!=null?f:{},l=t+ft(r),c=(E=o==null?void 0:o.map(y=>{var I;return(I=y.label)!=null?I:`${y.width}x${y.height}`}).sort().join(","))!=null?E:"";return`${l}#${c}`}fetchSignals(e,t){return T(this,null,function*(){let r=this._getCacheKey(e);return this.cache[r]==null&&(this.cache[r]=this._fetchFreshSignals(e,t)),this.cache[r]})}};i(we,"SlotSignals"),we=d([p],we);s();a();var De=class{constructor(){u(this,"cache")}_fetchFreshSignals(e){return T(this,null,function*(){if(!e.some(er))return{};let t=this.core.get(te).getUserTargetingUrl(e),r=yield this.core.get(ee).fetchJson(t,{authToken:!0});return r instanceof Object?r:{}})}fetchSignals(e){return T(this,null,function*(){return this.cache==null&&(this.cache=this._fetchFreshSignals(e)),this.cache})}};i(De,"UserSignals"),De=d([p],De);var b=class{constructor(){u(this,"plugins",{getEnabledSignals:i(()=>[],"getEnabledSignals"),userTargetingUpdated:i(e=>[],"userTargetingUpdated"),pageTargetingUpdated:i(e=>[],"pageTargetingUpdated"),slotTargetingUpdated:i((e,t)=>[],"slotTargetingUpdated")});u(this,"dataForMigrationTracking",{userTargeting:{},pageTargeting:{},slotTargeting:new Map})}getEnabledSignals(){return this.plugins.getEnabledSignals()}getDataForMigrationTracking(){return this.dataForMigrationTracking}_initialize(){this.getUserTargeting(),this.getPageTargeting()}_onAdRequestQueued(){this.getPageTargeting()}_onSlotDiscovered(e){this.getSlotTargeting(e)}getUserTargeting(){return T(this,null,function*(){let e=this._fetchUserTargeting();return this.core.get(C).delayAdRequests(e),e})}getPageTargeting(){return T(this,null,function*(){let e=this._fetchPageTargeting();return this.core.get(C).delayAdRequests(e),e})}getSlotTargeting(e){return T(this,null,function*(){let t=this._fetchSlotTargeting(e);return this.core.get(C).delayAdRequests(t),t})}_fetchUserTargeting(){return T(this,null,function*(){let e=yield this.core.get(De).fetchSignals(this.getEnabledSignals());return this.core.get(R).setGlobalTargeting(e),this.dataForMigrationTracking.userTargeting=e,this.plugins.userTargetingUpdated(e),e})}_fetchPageTargeting(){return T(this,null,function*(){let e=yield this.core.get(be).fetchSignals(this.getEnabledSignals());return this.core.get(R).setGlobalTargeting(e),this.dataForMigrationTracking.pageTargeting=e,this.plugins.pageTargetingUpdated(e),e})}_fetchSlotTargeting(e){return T(this,null,function*(){let t=yield this.core.get(we).fetchSignals(e,this.getEnabledSignals());return this.core.get(R).setSlotTargeting(e,t),this.dataForMigrationTracking.slotTargeting.set(e,t),this.plugins.slotTargetingUpdated(e,t),t})}};i(b,"Signals"),d([w],b.prototype,"_initialize",1),d([h(C,"adRequestQueued")],b.prototype,"_onAdRequestQueued",1),d([h(A,"slotDiscovered")],b.prototype,"_onSlotDiscovered",1),b=d([p],b);var ke=class{_getEnabledSignals(){return"IDS"}};i(ke,"SignalsIds"),d([h(b,"getEnabledSignals")],ke.prototype,"_getEnabledSignals",1),ke=d([p],ke);s();a();var $e=class{_getEnabledSignals(){return"BSC"}};i($e,"SignalsBsc"),d([h(b,"getEnabledSignals")],$e.prototype,"_getEnabledSignals",1),$e=d([p],$e);s();a();var Be=class{_getEnabledSignals(){return"ABS"}};i(Be,"SignalsAbs"),d([h(b,"getEnabledSignals")],Be.prototype,"_getEnabledSignals",1),Be=d([p],Be);s();a();var je=class{_getEnabledSignals(){return"VLP"}};i(je,"SignalsVlp"),d([h(b,"getEnabledSignals")],je.prototype,"_getEnabledSignals",1),je=d([p],je);s();a();var Ge=class{_getEnabledSignals(){return"TVP"}};i(Ge,"SignalsTvp"),d([h(b,"getEnabledSignals")],Ge.prototype,"_getEnabledSignals",1),Ge=d([p],Ge);s();a();s();a();s();a();var ze=i(n=>{if(n==null)return[];let e=dr(n);return e!=null?[e]:Array.isArray(n)?n.map(dr).filter(t=>t!=null):[]},"parseSlotSizes"),dr=i(n=>{if(n==null)return null;if(typeof n=="string")return{label:n};if(Array.isArray(n)){if(n.length===1&&typeof n[0]=="string")return{label:n[0]};if(n.length===2&&typeof n[0]=="number"&&typeof n[1]=="number")return{width:n[0],height:n[1]}}if(typeof n=="object"){let{label:e,width:t,height:r}=n;if(typeof t=="number"&&typeof r=="number")return{width:t,height:r};if(typeof e=="string")return{label:e}}return null},"parseSlotSize");s();a();var ur=i(n=>{try{return n.getSizes()}catch(e){return[]}},"getGptSlotSizesSafe");s();a();var ht=i(n=>{let e=n.split("/").map(o=>o.trim()).filter(o=>o.length>0);if(e.length<2)return;let t=e[0].split(",",1)[0],r=Number(t);return Number.isNaN(r)?void 0:`${r}`},"getNetworkCodeFromAdUnit");var vt=i((n,e)=>{let t=n.getAdUnitPath(),r=ht(t);return{id:t,network:r,sizes:ze(ur(n)),position:n.getTargeting(e)[0]}},"gptSlotToAdUnit");s();a();s();a();s();a();s();a();var cr=i(n=>{let e=n.renderedSize;return Array.isArray(e)?`${e[0]}x${e[1]}`:e},"toCreativeSizeString");s();a();var mr=i(n=>{switch(n){case"gpt":return"104";case"ast":return"90";default:return"0"}},"toAdServerCode");s();a();s();a();var Ft="dvp_spos";var F=class{_getAdServers(){try{return m}catch(e){return{}}}isAdServerConfigured(e){return e!=null&&e in this._getAdServers()}getAdServerConfig(e){var t;return e==null||!this.isAdServerConfigured(e)?{}:(t=this._getAdServers()[e])!=null?t:{}}getAdServerCustomTracking(e){var t;return e==null||!this.isAdServerConfigured(e)?{}:(t=this._getAdServers()[e].customTracking)!=null?t:{}}getAdServerId(e){if(!(e==null||!this.isAdServerConfigured(e)))return this._getAdServers()[e].adServerId}getAdPositionKey(e){var t,r;return e==null?Ft:(r=(t=this._getAdServers()[e])==null?void 0:t.adPositionKey)!=null?r:Ft}};i(F,"AdServers"),F=d([p],F);s();a();var re=class{constructor(){u(this,"pq",null)}getPq(){return this.pq==null&&(this.pq=this._initialize()),this.pq}getPts(){let e=this.getPq();return e.PTS==null&&(e.PTS={}),e.PTS}setApi(e){let t=this.getPq();t!=null&&(t.init=()=>{},t.loadSignals=e.loadSignals,t.loadSignalsForSlots=e.loadSignalsForSlots,t.getTargeting=e.getTargeting)}_pushToDvtagCmd(...e){var r,o;let t=this.core.get(S).window();t.dvtag=(r=t.dvtag)!=null?r:{},t.dvtag.cmd=(o=t.dvtag.cmd)!=null?o:[],t.dvtag.cmd.push(...e)}_initialize(){var o,l;let e=this.core.get(S).window();e.PQ=(o=e.PQ)!=null?o:{};let t=e.PQ;if(t.loaded)return{cmd:[],PTS:{}};t.loaded=!0,t.PTS=(l=t.PTS)!=null?l:{};let r=t.cmd;return t.cmd={push:i(c=>this._pushToDvtagCmd(c),"push")},Array.isArray(r)&&this._pushToDvtagCmd(...r),t}};i(re,"LegacyPq"),re=d([p],re);s();a();var pr=i(n=>{var r,o,l;let e=pe(),t={id:(o=(r=n.adUnitPath)!=null?r:n.invCode)!=null?o:n.tagId,sizes:ze(ar((l=n.sizes)!=null?l:"all")),position:n.position};return{elementId:e,adUnit:t}},"parseLegacyAdUnit");s();a();var gr=i((n,e)=>{var o;let t=(o=n.IDS)==null?void 0:o[0];if(t==null)return;let r=parseInt(t);isNaN(r)||(e.IDS=r)},"setUserTargetingOnPts"),fr=i((n,e)=>{for(let t of it)n[t]!=null&&(e[t]=n[t])},"setPageTargetingOnPts"),hr=i((n,e,t)=>{var f,E,y,I,ct,Ht;let{adUnit:r}=n;if((r==null?void 0:r.id)==null)return;let o=r.id,l=Ut((f=r.sizes)!=null?f:[]),c=r.position;for(let Ce of st){let Ct=e[Ce];Ct!=null&&((E=t[Ce])!=null||(t[Ce]={}),(I=(y=t[Ce])[o])!=null||(y[o]={}),t[Ce][o][l]=Ct[0],c!=null&&((Ht=(ct=t[Ce][o])[c])!=null||(ct[c]={}),t[Ce][o][c][l]=Ct[0]))}},"setSlotTargetingOnPts");var ge=class{_initialize(){this.core.get(re).setApi({loadSignals:this.loadSignals.bind(this),loadSignalsForSlots:this.loadSignalsForSlots.bind(this),getTargeting:this.getTargeting.bind(this)})}_setUserTargetingOnPts(e){let t=this.core.get(re).getPts();gr(e,t)}_setPageTargetingOnPts(e){let t=this.core.get(re).getPts();fr(e,t)}_setSlotTargetingOnPts(e,t){let r=this.core.get(re).getPts();hr(e,t,r)}loadSignals(e,t){return T(this,null,function*(){yield this._onDvtagReady("loadSignals",t)})}loadSignalsForSlots(e,t,r){return T(this,null,function*(){let o=this.core.get(b),l=this.core.get(A);for(let f of e){let E=f.getSlotElementId(),y=l.getSlot("gpt",E);if(y==null){let I=this.core.get(F).getAdPositionKey(f.getAdUnitPath());y=l.createSlot("gpt",0,E,vt(f,I))}o.getSlotTargeting(y)}let c=typeof t=="function"?t:r;yield this._onDvtagReady("loadSignalsForSlots",c)})}getTargeting(e,t){return T(this,null,function*(){var c;let r=this.core.get(b),o=this.core.get(A);for(let f of(c=e.adUnits)!=null?c:[]){let{elementId:E,adUnit:y}=pr(f),I=o.createSlot("legacy-api",0,E,y);r.getSlotTargeting(I)}let l=i(()=>t==null?void 0:t(void 0,this.core.get(re).getPts()),"callback");yield this._onDvtagReady("getTargeting",l)})}_getPubAdsTargeting(e){var t,r,o;return(o=(r=(t=this.core.get(S).window().googletag)==null?void 0:t.pubads)==null?void 0:r.call(t).getTargeting(e))!=null?o:[]}_onDvtagReady(e,t){return T(this,null,function*(){yield new Promise(r=>{this.core.get(S).window().dvtag.queueAdRequest({callback:i(()=>{try{t==null||t()}catch(o){}r()},"callback"),timestamp:new Date().getTime(),timeout:750,internal:!0})})})}};i(ge,"LegacyLayer"),d([w],ge.prototype,"_initialize",1),d([h(b,"userTargetingUpdated")],ge.prototype,"_setUserTargetingOnPts",1),d([h(b,"pageTargetingUpdated")],ge.prototype,"_setPageTargetingOnPts",1),d([h(b,"slotTargetingUpdated")],ge.prototype,"_setSlotTargetingOnPts",1),ge=d([p],ge);s();a();s();a();s();a();var Te=class{constructor(){u(this,"plugins",{adRemoved:i(e=>[],"adRemoved")})}_monitorAdRemoval(e){let t=this.core.get(S).document().getElementById(e.elementId);if(t==null)return;v.Create({parent:e,observer:new MutationObserver(o=>{let l=o.length,c=0,f=t.children.length;for(let E=o.length-1;E>=0;E--){let y=o[E];if(f===0){this.plugins.adRemoved(e);break}if(c===l-1)break;let I=y.removedNodes.length-y.addedNodes.length;f+=I,c++}})},({parent:o,observer:l})=>{l.disconnect(),this.plugins.adRemoved(o)}).observer.observe(t,{childList:!0})}};i(Te,"AdRemoved"),d([h(A,"slotDiscovered")],Te.prototype,"_monitorAdRemoval",1),Te=d([p],Te);var _=class{constructor(){u(this,"adEmpty",new v.Map);u(this,"adRequestInFlight",new v.Map);u(this,"nonEmptyAdResponses",new v.Map);u(this,"creatives",new v.Link);u(this,"plugins",{adRequested:i(e=>[],"adRequested"),adRequestedOrInferred:i(e=>[],"adRequestedOrInferred"),adRendered:i((e,t)=>[],"adRendered"),adRemoved:i(e=>[],"adRemoved")})}setAdRequested(e){this.adRequestInFlight.set(e,!0),this.plugins.adRequested(e),this.plugins.adRequestedOrInferred(e)}setAdRendered(e,t){this.adRequestInFlight.get(e)!==!0&&this.plugins.adRequestedOrInferred(e),this._setAdRemoved(e),this.adEmpty.set(e,!1),this.adRequestInFlight.set(e,!1),t!=null&&this.nonEmptyAdResponses.set(e,this.nonEmptyAdResponses.summon(e,0)+1);let r=null;if(t!=null){let o=this.core.get(S).time();r=v.Create(L({parent:e,timestamp:o},t)),this.creatives.get(e).add(r)}this.plugins.adRendered(e,r)}_setAdRemoved(e){if(this.adEmpty.get(e)===!1){this.adEmpty.set(e,!0);for(let t of this.creatives.get(e))t.destroy();this.plugins.adRemoved(e)}}getNonEmptyAdRenderedCount(e){return this.nonEmptyAdResponses.summon(e,0)}};i(_,"AdServerHub"),d([h(Te,"adRemoved")],_.prototype,"_setAdRemoved",1),_=d([p],_);s();a();s();a();s();a();var Et=i(n=>{if(typeof n=="string"||Array.isArray(n)&&n.length===2&&n.every(e=>typeof e=="number"))return n;if(n!=null&&typeof n.width=="number"&&typeof n.height=="number")return[n.width,n.height]},"cleanSizeInput");s();a();var K=i(n=>{if(typeof n=="string")return n;if(typeof n=="number")return`${n}`},"cleanStringInput");s();a();var ne=class{getPubAds(){var t,r;let e=this.core.get(S).window();try{return(r=(t=e.googletag)==null?void 0:t.pubads)==null?void 0:r.call(t)}catch(o){return}}onPubAdsReady(e){var o,l,c,f;let t=this.core.get(S).window();t.googletag=(o=t.googletag)!=null?o:{},t.googletag.cmd=(l=t.googletag.cmd)!=null?l:[];let r=this.getPubAds();if(r!=null)e(r);else try{(f=(c=t.googletag.cmd)==null?void 0:c.push)==null||f.call(c,()=>{r=this.getPubAds(),r!=null&&e(r)})}catch(E){}}};i(ne,"GoogletagPubAds"),ne=d([p],ne);s();a();var W=class{constructor(){u(this,"gptReference",new v.Map)}_scanForSlots(){this.core.get(ne).onPubAdsReady(e=>{e.getSlots().map(t=>this.maybeDiscoverSlot(t,0))})}getGptSlot(e){return this.gptReference.get(e)}maybeDiscoverSlot(e,t){let r=this.core.get(A).getSlot("gpt",e.getSlotElementId());return r==null||this.gptReference.get(r)!==e?this._createSlot(e,t):r}_createSlot(e,t){let r=e.getSlotElementId(),o=e.getAdUnitPath(),l=ht(o),c=this.core.get(F).getAdPositionKey(l),f=vt(e,c);return this.core.get(A).createSlot("gpt",t,r,f,y=>this.gptReference.set(y,e))}};i(W,"GoogletagSlots"),d([w,h(C,"adRequestQueued")],W.prototype,"_scanForSlots",1),W=d([p],W);var fe=class{constructor(){u(this,"plugins",{adRequested:i(e=>[],"adRequested"),adRendered:i((e,t)=>[],"adRendered")})}_getHtmlSafe(e){var t,r;try{return(r=(t=e.getHtml)==null?void 0:t.call(e))!=null?r:void 0}catch(o){return}}_initialize(){this.core.get(ne).onPubAdsReady(e=>{e.addEventListener("slotRequested",t=>{let r=this.core.get(W).maybeDiscoverSlot(t.slot,1);this.plugins.adRequested(r)}),e.addEventListener("slotRenderEnded",t=>{let r=this.core.get(W).maybeDiscoverSlot(t.slot,2);if(t.isEmpty){this.plugins.adRendered(r,null);return}let o={type:"display",advertiserId:K(t.advertiserId),campaignId:K(t.campaignId),creativeId:K(t.creativeId),lineItemId:K(t.lineItemId),renderedSize:Et(t.size),html:this._getHtmlSafe(t.slot)};this.plugins.adRendered(r,o)})})}};i(fe,"GoogletagEvents"),d([w],fe.prototype,"_initialize",1),fe=d([p],fe);s();a();s();a();var qt=i(n=>typeof n=="string"||typeof n=="number"?[`${n}`]:n instanceof Array?Ee(n.filter(e=>typeof e=="string"||typeof e=="number").map(e=>`${e}`)):[],"cleanTargetingInput");var ye=class{getAdServerTargeting(e,t){var l,c,f;if(e.adServer!=="gpt")return[];let r=[];try{r=qt((c=(l=this.core.get(ne).getPubAds())==null?void 0:l.getTargeting)==null?void 0:c.call(l,t))}catch(E){}let o=[];try{o=qt((f=this.core.get(W).getGptSlot(e))==null?void 0:f.getTargeting(t))}catch(E){}return Ee(r,o)}setAdServerTargeting(e,t,r){var o;if(e.adServer==="gpt")try{(o=this.core.get(W).getGptSlot(e))==null||o.setTargeting(t,r)}catch(l){}}};i(ye,"GoogletagTargeting"),ye=d([p],ye);var j=class{_onAdRequested(e){this.core.get(_).setAdRequested(e)}_onAdRendered(e,t){this.core.get(_).setAdRendered(e,t)}_getAdServerTargeting(e,t){return e.adServer!=="gpt"?[]:this.core.get(ye).getAdServerTargeting(e,t)}_setAdServerTargeting(e,t,r){e.adServer==="gpt"&&this.core.get(ye).setAdServerTargeting(e,t,r)}};i(j,"Googletag"),d([h(fe,"adRequested")],j.prototype,"_onAdRequested",1),d([h(fe,"adRendered")],j.prototype,"_onAdRendered",1),d([h(R,"getAdServerTargeting")],j.prototype,"_getAdServerTargeting",1),d([h(R,"setAdServerTargeting")],j.prototype,"_setAdServerTargeting",1),j=d([p],j);s();a();s();a();s();a();s();a();var vr=i((n,e)=>(e==null?void 0:e.structuredClone)!=null?e.structuredClone(n):JSON.parse(JSON.stringify(n)),"deepClone");s();a();s();a();var Sr=i(n=>{if(typeof n!="object"||n===null||Array.isArray(n))return!1;let e=n;return e.gdprApplies!==void 0&&typeof e.gdprApplies!="boolean"&&e.gdprApplies!==0&&e.gdprApplies!==1||e.eventStatus!=="tcloaded"&&e.eventStatus!=="cmpuishown"&&e.eventStatus!=="useractioncomplete"&&e.eventStatus!==void 0||e.tcString!==void 0&&typeof e.tcString!="string"?!1:e.gdprApplies===!1||e.gdprApplies===0||e.gdprApplies===void 0?!(e.tcString!==void 0||e.purpose!==void 0||e.vendor!==void 0||e.publisher!==void 0):!(typeof e.tcString!="string"||!Dn(e.purpose)||!In(e.vendor)||!Pn(e.publisher))},"isValidTCData"),Dn=i(n=>{if(typeof n!="object"||n===null)return!1;let e=n;return yt(e.consents)&&yt(e.legitimateInterests)},"isValidPurposeObject"),In=i(n=>{if(typeof n!="object"||n===null)return!1;let e=n;return yt(e.consents)&&yt(e.legitimateInterests)},"isValidVendorObject"),Pn=i(n=>{if(typeof n!="object"||n===null||n===void 0)return!1;let e=n;return!(e.restrictions!==void 0&&!On(e.restrictions))},"isValidPublisherObject"),yt=i(n=>typeof n!="object"||n===null?!1:Object.entries(n).every(([e,t])=>{let r=parseInt(e,10);return!isNaN(r)&&(typeof t=="boolean"||t===void 0)}),"isRecordOfBooleansOrUndefined"),On=i(n=>typeof n!="object"||n===null?!1:Object.entries(n).every(([e,t])=>{let r=parseInt(e,10);return isNaN(r)||typeof t!="object"||t===null?!1:Object.entries(t).every(([o,l])=>{let c=parseInt(o,10),f=Number(l);return!isNaN(c)&&!isNaN(f)&&f>=0&&f<=3})}),"isValidPublisherRestrictions");var at=126,Er=2,xn=20,Ln=250;var se=class{constructor(){u(this,"firstResultPromise",new v.Map);u(this,"tcState",new v.Map);u(this,"declaredPurposes",new v.Map);u(this,"plugins",{signalReady:i(()=>[],"signalReady")})}getTcState(){return L({},this.tcState.conjure(this.core.session,()=>({apiAvailable:this._getCmpApi()!=null,applies:"",signalSettled:!1,tcString:""})))}getDeclaredPurposes(){return L({},this.declaredPurposes.conjure(this.core.session,()=>({purposes:[],flexiblePurposes:[2,7,8,10],legIntPurposes:[2,7,8,10]})))}getTcStateAsync(){return T(this,null,function*(){return yield this._getTcStatePromise(),this.getTcState()})}apiAvailable(){return this.getTcState().apiAvailable}applies(){let e=this.getTcState();return e.applies==="1"?!0:e.applies==="0"?!1:void 0}hasLegalBasis(e){let{applies:t,tcString:r,purpose:o,vendor:l}=this.getTcState();if(t!=="1"||r===""||o==null||l==null)return;let c=this._getActiveLegalBasis(e);if(c!=null)return c===0?!1:c===1?o.consents[e]===!0&&l.consent===!0:o.legitimateInterests[e]===!0&&l.legitimateInterest===!0}_getTcStatePromise(){return T(this,null,function*(){return this.firstResultPromise.conjure(this.core.session,()=>this._monitorTcState()).catch(()=>{})})}_monitorTcState(){return T(this,null,function*(){let e=yield this._getCmpApiAsync();if(e!=null){try{e("getVendorList",Er,(t,r)=>{var o;if(r===!0&&((o=t==null?void 0:t.vendors)==null?void 0:o[at])!=null){let{purposes:l,flexiblePurposes:c,legIntPurposes:f}=t.vendors[at];this._setDeclaredPurposes({purposes:l,flexiblePurposes:c,legIntPurposes:f})}})}catch(t){}yield new Promise(t=>{try{e("addEventListener",Er,(r,o)=>{o===!0&&Sr(r)&&this._setTcState(r),t()})}catch(r){t()}})}})}_getCmpApiAsync(){return T(this,null,function*(){let e=this.core.get(S).window(),t=this._getCmpApi(),r=0;for(;t==null&&r<xn;)r++,yield new Promise(o=>e.setTimeout(o,Ln)),t=this._getCmpApi();return t})}_getCmpApi(){let t=this.core.get(S).window().__tcfapi;if(typeof t=="function")return t}_setDeclaredPurposes(e){this.declaredPurposes.set(this.core.session,{purposes:[...e.purposes],flexiblePurposes:[...e.flexiblePurposes],legIntPurposes:[...e.legIntPurposes]})}_setTcState(e){let t=this._convertTcDataToTcState(e);this.tcState.set(this.core.session,t),t.signalSettled&&this.plugins.signalReady()}_convertTcDataToTcState(e){let t=e.gdprApplies==null?"":e.gdprApplies===!0||e.gdprApplies===1?"1":"0",r=e.eventStatus==="tcloaded"||e.eventStatus==="useractioncomplete"||t==="0",o={apiAvailable:!0,applies:t,signalSettled:r,tcString:""};return!(e.gdprApplies===!0||e.gdprApplies===1)||e.tcString===""?o:Ue(L({},o),{tcString:e.tcString,purpose:vr(e.purpose),vendor:{legitimateInterest:e.vendor.legitimateInterests[at]===!0,consent:e.vendor.consents[at]===!0},publisherRestrictions:this._extractDvRestrictions(e.publisher.restrictions)})}_extractDvRestrictions(e){let t={};for(let[r,o]of Object.entries(e)){let l=o[at];l!=null&&(t[Number(r)]=l)}return t}_getActiveLegalBasis(e){let t=this.getDeclaredPurposes(),{publisherRestrictions:r}=this.getTcState();if(r==null)return;let o=r[e];if(o===0)return 0;let l=t.legIntPurposes.includes(e)?2:t.purposes.includes(e)?1:0;if(l===0)return 0;if(o===1||o===2){let c=o===1?1:2,f=t.flexiblePurposes.includes(e);return l===c||f?c:0}return l}};i(se,"Gdpr"),d([w],se.prototype,"_getTcStatePromise",1),se=d([p],se);s();a();var Nn="https://vtrk.doubleverify.com",Tr="dvtag",Mn=1,x=class{constructor(){u(this,"sharedDataPoints")}_onAdRequestCompleted(e){if(e.index!==0)return;let{timeoutValue:t,onDvtagReadyCalled:r,callbackCalled:o}=e,l=o!=null?o-r:t,{applies:c,apiAvailable:f}=this.core.get(se).getTcState();this.fireEvent("ad-request",{cd111:e.status,cm106:e.index,cm107:l,cd112:e.internal?"legacy":"modern",cd115:c,cm116:f?1:0},{sampleInterval:Mn})}_getSharedDataPoints(){if(this.sharedDataPoints==null){let e=this.core.get(D),t=this.core.get(H),r=this.core.get(S),{ctx:o,cmp:l,legacy:c}=e.getTagInfo(),f=r.hostname(),E=e.getSessionUuid(),y=t.getShortCommitId();this.sharedDataPoints={cd102:y,cd103:"",cd107:E,cd109:c?"legacy":"modern",cd108:"",cd105:o,cd106:l,cd104:f}}return this.sharedDataPoints}fireEvent(e,t,{sampleInterval:r=1,trackerUuid:o=pe(),spoofWrapperId:l=!1}={}){if(Math.random()>=1/r)return;t=Ue(L(L({},t),this._getSharedDataPoints()),{cd101:e,cm101:r});let c=Ue(L({ec:Tr,ea:t.cd101,ctx:l?Tr:t.cd105,cmp:t.cd106},t),{cid:o,t:"event",v:1,z:o,cd160:o}),f=new URL(Nn);for(let[E,y]of Object.entries(c))y!=null&&f.searchParams.set(E,`${y}`);this.core.get(S).sendBeacon(f.href)}};i(x,"Events"),d([h(C,"adRequestCompleted")],x.prototype,"_onAdRequestCompleted",1),x=d([p],x);s();a();var Un=.01,ae=class{constructor(){u(this,"plugins",{overrideActivationRate:i(()=>[],"overrideActivationRate")});u(this,"_random",Math.random);u(this,"_falconEnabled")}isFalconEnabled(){var e;if(this._falconEnabled==null){let t=(e=this.plugins.overrideActivationRate()[0])!=null?e:Un;this._falconEnabled=this._random()<t}return this._falconEnabled}};i(ae,"FalconTest"),ae=d([p],ae);s();a();s();a();s();a();var yr=10,q=class{constructor(){u(this,"errors",new v.Map)}_getErrors(){return this.errors.summon(this.core.session,[])}toSafeCallback(e){return(...t)=>this._callSafely(e,t)}_callSafely(e,t){return e(...t)}_handleError(e,t){let r=this._getErrors(),o=t.join("=>");if(r.unshift(`${e}: ${o}`),r.length>yr){r.length=yr;return}this.core.get(x).fireEvent("log",{cd111:"error",cd112:e,cd113:o})}};i(q,"ErrorHandler"),d([Z],q.prototype,"_callSafely",1),d([Lt],q.prototype,"_handleError",1),q=d([p],q);var Y=class{observe(e,t,r,o,...l){let c=this.core.get(q).toSafeCallback(o),f=new t(c,...l);return f.observe(r),v.Create({parent:e},()=>{f.disconnect()})}addEventListener(e,t,r,o){let l=this.core.get(q).toSafeCallback(o);return t.addEventListener(r,l),v.Create({parent:e},()=>{t.removeEventListener(r,l)})}};i(Y,"Subscriptions"),Y=d([p],Y);var Fn=5e3,M=class{constructor(){u(this,"plugins",{pageVisible:i(()=>[],"pageVisible"),pageHide:i(()=>[],"pageHide"),pageHideThrottled:i(()=>[],"pageHideThrottled")});u(this,"hidden",new v.Map);u(this,"lastFiredThrottled",new v.Map)}_initialize(){this._installListeners(),this._maybeFirePlugins()}_installListeners(){let e=this.core.get(S).window(),t=this.core.get(S).document();this.core.get(Y).addEventListener(this.core.session,t,"visibilitychange",()=>this._maybeFirePlugins());for(let r of["pagehide","pageshow"])this.core.get(Y).addEventListener(this.core.session,e,r,()=>this._maybeFirePlugins())}isHidden(){var e,t;return((e=this.isDocumentHidden())!=null?e:!1)||((t=this.isDocumentVisibilityStateHidden())!=null?t:!1)}isDocumentHidden(){try{return this.core.get(S).document().hidden===!0}catch(e){return}}isDocumentVisibilityStateHidden(){try{let e=this.core.get(S).document().visibilityState;return typeof e=="string"&&e==="hidden"}catch(e){return}}documentHasFocus(){try{return this.core.get(S).document().hasFocus()===!0}catch(e){return}}_maybeFirePlugins(){let e=this.hidden.get(this.core.session),t=this.isHidden();if(this.hidden.set(this.core.session,t),e!==t&&this.plugins[t?"pageHide":"pageVisible"](),t){let r=this.core.get(S).time(),o=this.lastFiredThrottled.get(this.core.session);if(o!=null&&r-o<=Fn)return;this.lastFiredThrottled.set(this.core.session,r),this.plugins.pageHideThrottled()}}};i(M,"PageVisibility"),d([w],M.prototype,"_initialize",1),M=d([p],M);s();a();var G=class{setTimeout(e,t,r,o=!1){let l=this.core.get(S).window(),c=this.core.get(q).toSafeCallback(t),f=i(()=>{c(),f=i(()=>{},"callbackOnce")},"callbackOnce"),E=v.Create({parent:e},()=>{l.clearTimeout(y),o&&f()}),y=l.setTimeout(()=>{f(),E.destroy()},r);return E}setInterval(e,t,r,o=!1){let l=this.core.get(S).window(),c=this.core.get(q).toSafeCallback(t),f=l.setInterval(c,r);return v.Create({parent:e},()=>{l.clearInterval(f),o&&c()})}entityExistsAfterTimeout(e,t){return new Promise(r=>{v.Create({parent:this.setTimeout(e,()=>r(!0),t)},()=>r(!1))})}};i(G,"Timeouts"),G=d([p],G);s();a();var le=class{constructor(){u(this,"_count",0);u(this,"plugins",{onCountUpdated:i(e=>[],"onCountUpdated")})}getCount(){return this._count}increment(){this._count+=1,this.plugins.onCountUpdated(this._count)}decrement(){this._count-=1,this.plugins.onCountUpdated(this._count)}};i(le,"MeasurementScriptCounter"),le=d([p],le);var qn="blockingtime",kn=5*60*1e3,$n=60*1e3,Bn=20;var z=class{constructor(){u(this,"monitors",new v.Set);u(this,"state",new v.Map)}_getMonitor(){return this.monitors.conjure(()=>v.Create({parent:this.core.session}))}_getState(){return this.state.conjure(this._getMonitor(),()=>({trackerCount:0,monitorStart:0,lastAvgScriptCountUpdate:0,summedBlockingTime:0,totalLongTaskTime:0,longTaskCount:0,avgScriptCountNumerator:0,currentScriptCount:0,lastTrackerSent:void 0,scriptInsertions:0}))}_initialize(){var l;let e=this.core.get(S),t=this._getMonitor(),r=this._getState();r.monitorStart=e.time(),r.lastAvgScriptCountUpdate=e.time();let{PerformanceObserver:o}=e.window();o==null||((l=o.supportedEntryTypes)==null?void 0:l.includes("longtask"))!==!0||(this.core.get(Y).observe(t,o,{type:"longtask",buffered:!0},this._onPerformanceObserverEntryList),this.core.get(G).setTimeout(t,this._onMonitoringWindowClosed,kn))}_onPerformanceObserverEntryList(e){let t=this._getState();e.getEntries().forEach(r=>{let o=Math.max(0,r.duration-50);t.totalLongTaskTime+=r.duration,t.longTaskCount+=1,t.summedBlockingTime+=o})}_onMonitoringWindowClosed(){this._sendTracker(),this._getMonitor().destroy()}_onScriptCountUpdate(e){let t=this._getState();t.scriptInsertions+=e>t.currentScriptCount?1:0,this._updateAvgScriptCount(),t.currentScriptCount=e}_updateAvgScriptCount(){let e=this._getState();e.avgScriptCountNumerator+=e.currentScriptCount*(this.core.get(S).time()-e.lastAvgScriptCountUpdate),e.lastAvgScriptCountUpdate=this.core.get(S).time()}_sendTracker(){let e=this._getState();if(e.lastTrackerSent!=null&&this.core.get(S).time()-e.lastTrackerSent<$n)return;this._updateAvgScriptCount();let t=this.core.get(S).time()-e.monitorStart,r={cd110:this.core.get(ae).isFalconEnabled()?"falcon":"cdn",cm106:e.trackerCount,cm107:t,cm108:e.summedBlockingTime,cm109:e.totalLongTaskTime,cm110:e.longTaskCount,cm111:e.avgScriptCountNumerator/t,cm112:e.scriptInsertions};this.core.get(x).fireEvent(qn,r,{spoofWrapperId:!0}),e.trackerCount+=1,e.lastTrackerSent=this.core.get(S).time(),e.trackerCount>=Bn&&this._getMonitor().destroy()}};i(z,"BlockingTimeMonitor"),d([w],z.prototype,"_initialize",1),d([Z],z.prototype,"_onPerformanceObserverEntryList",1),d([Z],z.prototype,"_onMonitoringWindowClosed",1),d([h(le,"onCountUpdated")],z.prototype,"_onScriptCountUpdate",1),d([h(M,"pageHideThrottled")],z.prototype,"_sendTracker",1),z=d([p],z);s();a();s();a();var jn=Object.defineProperty,Gn=Object.defineProperties,Vn=Object.getOwnPropertyDescriptors,Ar=Object.getOwnPropertySymbols,Qn=Object.prototype.hasOwnProperty,Hn=Object.prototype.propertyIsEnumerable,Rr=i((n,e,t)=>e in n?jn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,"__defNormalProp"),lt=i((n,e)=>{for(var t in e||(e={}))Qn.call(e,t)&&Rr(n,t,e[t]);if(Ar)for(var t of Ar(e))Hn.call(e,t)&&Rr(n,t,e[t]);return n},"__spreadValues"),_r=i((n,e)=>Gn(n,Vn(e)),"__spreadProps");var Xe=(n=>(n.GAM="gam",n.XANDR="xandr",n.FREEWHEEL="freewheel",n.CUSTOM="custom",n))(Xe||{});var kt=(n=>(n[n.Disabled=0]="Disabled",n[n.Dryrun=1]="Dryrun",n))(kt||{});var lg=typeof TextDecoder=="function"?new TextDecoder:void 0,dg=typeof TextEncoder=="function"?new TextEncoder:void 0,Kn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Wn=Array.prototype.slice.call(Kn),ug=(n=>{let e={};return n.forEach((t,r)=>e[t]=r),e})(Wn),cg=String.fromCharCode.bind(String),mg=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):n=>new Uint8Array(Array.prototype.slice.call(n,0));var Yn="__DVPUB_OT__",zn=i(n=>Yn+new URLSearchParams(n).toString().replace(/=/g,":").replace(/&/g,"|").replace(/\+/g,"%20").replace(/[^a-zA-Z0-9:|%_]/g,e=>`%${e.charCodeAt(0).toString(16)}`),"encodeParams"),Cr=i((n,e)=>Object.entries(e).reduce((r,[o,l])=>(r[`${n}${o}`]=l,r),{}),"prefixKeys"),Xn=i(n=>{let e={};for(let[t,r]of Object.entries(n))e[t]=`${r}`;return e},"stringifyValues"),br=i((n,e,t)=>{if(n==="custom"||n==="freewheel")return{};let r=Xn(_r(lt({},e.passthroughParams),{dvp_uptpid:e.pageId,dvp_uptsid:e.slotId,dvp_uptsacnt:e.slotAdCount,dvp_qtpid:e.pageId})),o=zn(lt(lt({},e.wrapperMode!=null?{m:String(e.wrapperMode)}:{}),Cr("_",r)));return n==="gam"?_r(lt(lt({dvpub_ot_params:o},e.wrapperMode!=null?{dv_upt_cwm:String(e.wrapperMode)}:{}),Cr("dv__",r)),{pts_pid:e.pageId,pts_sid:e.slotId}):n==="xandr"?{[t!=null?t:"pt1"]:o}:{}},"generateOmniTagTargeting");s();a();var wr=i(n=>{let e={};for(let[t,r]of Object.entries(n))r!=null&&(e[t]=typeof r=="string"?[r]:r);return e},"toTargeting");var Jn={gpt:Xe.GAM,ast:Xe.XANDR,custom:Xe.CUSTOM,"legacy-api":Xe.CUSTOM},Zn="pt9",V=class{constructor(){u(this,"plugins",{disableCreativeWrapperMeasurement:i(e=>[],"disableCreativeWrapperMeasurement")});u(this,"targetingSet",new v.Map);u(this,"targetingReliable",new v.Map)}_setCreativeWrapperTargeting(e){let t=e.uuid,r=this.core.get(D).getVisitUuid(),o=this._getNextSlotAdCount(e),l=this.plugins.disableCreativeWrapperMeasurement(e).some(c=>c===!0)?kt.Disabled:void 0;this.core.get(R).setSlotTargeting(e,wr(br(Jn[e.adServer],{slotId:t,pageId:r,slotAdCount:o,wrapperMode:l,passthroughParams:{dvp_uptacf:"1"}},Zn))),this.targetingSet.set(e,!0)}_onNewVisit(){let e=this.core.get(A).getSlots();for(let t of e)this._setCreativeWrapperTargeting(t)}_onAdRequested(e){this.targetingReliable.set(e,this.targetingSet.get(e)===!0)}isTargetingReliable(e){return this.targetingReliable.get(e)===!0}_getNextSlotAdCount(e){return this.core.get(_).getNonEmptyAdRenderedCount(e)+1}};i(V,"CreativeWrapperTargeting"),d([h(A,"slotDiscovered"),h(_,"adRendered")],V.prototype,"_setCreativeWrapperTargeting",1),d([h(D,"newVisit")],V.prototype,"_onNewVisit",1),d([h(_,"adRequested")],V.prototype,"_onAdRequested",1),V=d([p],V);s();a();s();a();s();a();var Dr=i((n,e,t="")=>{if(n.length<=e)return n;let r=e-t.length;return r<0?t:n.slice(0,r)+t},"truncate");s();a();var Ir="https://cdn.doubleverify.com/dvtp_src.js",Pr="https://cdn.dv.tech/dvbm.js";s();a();var Or={cmp:"cmp",ctx:"ctx",isSellerTag:"seltag",blocking:"blk",t2te:"t2te",deepIFrameClickHandlers:"dich",measurementActivationPath:"dvp_pubaap",unityTagBuildTimestamp:"dvp_uptbts",unityTagBuildCommitId:"dvp_uptbcid",gdprApplies:"gdpr",gdprTcString:"gdpr_consent",adServerCode:"adsrv",adServerId:"dvp_gptmasid",pageVisitId:"dvp_uptpid",slotId:"dvp_uptsid",slotElementId:"dvp_uptseid",selectedVariation:"dvp_gptmvr",selectedVariationFraction:"dvp_gptmvrfr",customDimension1:"c1",customDimension2:"c2",customDimension3:"c3",customDimension4:"c4",customDimension5:"c5",customDimension6:"c6",customDimension7:"c7",customDimension8:"c8",customDimension9:"c9",customDimension10:"c10",adPosition:"spos",qtPageId:"dvp_qtpid",qtSlotId:"dvp_qtsid",qtObservabilityIDS:"mpt_pub_qtids",qtObservabilityBSCPageLevel:"mpt_pub_qtbscpl",headerBidding:"dvp_gptmhb",advertiserId:"sadv",orderId:"ord",lineItemId:"litm",creativeId:"scrt",placementCode:"splc",size:"unit",slotAdCount:"dvp_uptsacnt",locatedElementType:"dvp_gptmle",adLocationMechanism:"alm",usedAdFormat:"dvp_pubuaf",adDetectionDiagnostics:"dvp_pubadd",adLocatingDiagnostics:"dvp_pubald",creativeWrapperAvailable:"dvp_gptmcw",isFalconTestEnabledDVP:"dvp_pubaft",isFalconTestEnabledEE:"ee_dp_pubaft",measurementInjectionStrategy:"dvp_gptmit"};var O=class{constructor(){u(this,"parameters",new v.Map);u(this,"_truncate",Dr)}_set(e,t){var r;this.parameters.set(e,L(L({},(r=this.parameters.get(e))!=null?r:{}),t))}_getScriptUrl(e){let t=[],r=this.parameters.summon(e,{});for(let[o,l]of Object.entries(r)){let c=Or[o];l!=null&&t.push([c,encodeURIComponent(l)])}return`${this.core.get(ae).isFalconEnabled()?Pr:Ir}#${t.map(([o,l])=>`${o}=${l}`).join("&")}`}createScriptElement(e,t){let r=this._getScriptUrl(e),l=this.core.get(S).document().createElement("script");return l.setAttribute("src",r),l.setAttribute("async",""),l.dvAdElement=t,l}setIdentifiers(e){let{ctx:t,cmp:r}=this.core.get(D).getTagInfo();this._set(e,{ctx:t,cmp:r})}setConstants(e){this._set(e,{isSellerTag:1,t2te:0,blocking:this.core.get(ae).isFalconEnabled()?0:void 0,measurementActivationPath:2,deepIFrameClickHandlers:1,unityTagBuildTimestamp:this.core.get(H).getBuildTimestamp(),unityTagBuildCommitId:this.core.get(H).getShortCommitId()})}setGdpr(e){let t=this.core.get(se),{applies:r,tcString:o}=t.getTcState();this._set(e,{gdprApplies:r,gdprTcString:o})}setAdUnit(e){this._set(e,{adServerCode:mr(e.adServer),adServerId:this.core.get(F).getAdServerId(e.adUnit.network),pageVisitId:this.core.get(D).getVisitUuid(),slotId:e.uuid,slotElementId:this._truncate(e.elementId,30,"~")})}_getCustomDimension(e,t){var l;let o=(l=this.core.get(F).getAdServerCustomTracking(e.adUnit.network)[t])==null?void 0:l.key;return o!=null?this.core.get(R).getAdServerTargeting(e,o).join(","):void 0}setCustomDimensions(e){this._set(e,{customDimension1:this._getCustomDimension(e,"c1"),customDimension2:this._getCustomDimension(e,"c2"),customDimension3:this._getCustomDimension(e,"c3"),customDimension4:this._getCustomDimension(e,"c4"),customDimension5:this._getCustomDimension(e,"c5"),customDimension6:this._getCustomDimension(e,"c6"),customDimension7:this._getCustomDimension(e,"c7"),customDimension8:this._getCustomDimension(e,"c8"),customDimension9:this._getCustomDimension(e,"c9"),customDimension10:this._getCustomDimension(e,"c10")})}_getAdPosition(e){let t=this.core.get(F).getAdPositionKey(e.adUnit.network);return this.core.get(R).getAdServerTargeting(e,t)[0]}setQualityTargeting(e){this._set(e,{adPosition:this._getAdPosition(e),qtPageId:this.core.get(D).getVisitUuid(),qtSlotId:e.uuid})}setQtObservability(e,{bscPageLevel:t,ids:r}){this._set(e,{qtObservabilityBSCPageLevel:t,qtObservabilityIDS:r})}setAdInstance(e,t){this._set(e,{advertiserId:t.advertiserId,orderId:t.campaignId,lineItemId:t.lineItemId,creativeId:t.creativeId,placementCode:e.adUnit.id,size:cr(t)})}setEngagement(e){this._set(e,{slotAdCount:this.core.get(_).getNonEmptyAdRenderedCount(e)})}_getLocatedElementType(e){let{sameOriginIframe:t,crossOriginIframe:r}=e;return t!=null?1:r!=null?2:0}setAdLocation(e,t){this._set(e,{locatedElementType:this._getLocatedElementType(t),adLocationMechanism:"manual"})}setDetectedAdFormat(e,t){t!=null&&this._set(e,L({},t))}setDebug(e){this._set(e,{isFalconTestEnabledDVP:1,isFalconTestEnabledEE:1,measurementInjectionStrategy:0})}};i(O,"MeasurementScript"),O=d([p],O);var eo="80000200",Ae=class{_onAdRequested(e){var l,c;let t=this.core.get(R).getTargeting(e),r=null;((l=t.IDS)==null?void 0:l.length)>0&&(r=t.IDS[0]==="1"?1:0);let o=null;((c=t.BSC)==null?void 0:c.length)>0&&(o=this._isPageLevelBsc(t.BSC)?1:0),this.core.get(O).setQtObservability(e,{ids:r,bscPageLevel:o})}_isPageLevelBsc(e){return!e.includes(eo)}};i(Ae,"QtObservability"),d([h(_,"adRequestedOrInferred")],Ae.prototype,"_onAdRequested",1),Ae=d([p],Ae);s();a();var Ie=class{constructor(){u(this,"plugins",{shouldPerformIntentionalDuplicateMeasurement:i(e=>[],"shouldPerformIntentionalDuplicateMeasurement"),shouldEnforcePLEMetricsCompliance:i(()=>[],"shouldEnforcePLEMetricsCompliance")})}shouldPerformIntentionalDuplicateMeasurement(e){return this.plugins.shouldPerformIntentionalDuplicateMeasurement(e).some(t=>t===!0)}shouldEnforcePLEMetricsCompliance(){return this.plugins.shouldEnforcePLEMetricsCompliance().some(e=>e===!0)}};i(Ie,"MeasurementFlags"),Ie=d([p],Ie);s();a();var Re=class{constructor(){u(this,"iframeElements",new v.Map)}_applyDebugInfo(e,t){t.setAttribute("id",`gptm-${e.elementId}`),t.dataset.slotId=e.uuid,t.dataset.slotAdCount=`${this.core.get(_).getNonEmptyAdRenderedCount(e)}`}injectScriptElement(e,t){let r=this.core.get(S).document(),o=r.createElement("iframe");this.iframeElements.set(e,o),this._applyDebugInfo(e,o),o.style.display="none",o.addEventListener("load",()=>{var l;(l=o.contentDocument)==null||l.body.appendChild(t)}),r.body.appendChild(o)}removeScriptElement(e){var t;try{(t=this.iframeElements.get(e))==null||t.remove(),this.iframeElements.delete(e)}catch(r){}}};i(Re,"MeasurementScriptInjection"),Re=d([p],Re);s();a();s();a();s();a();var xr=i((n,e)=>{let t=null;try{t=n.contentDocument}catch(r){}if(t==null)return!1;if(n.src===""||n.src==="about:blank")return!0;try{return new URL(n.src).origin===new URL(e).origin}catch(r){return!1}},"isIframeSameOrigin");var Lr={gpt:"google_ads_iframe_",ast:"utif_"},Pe=class{constructor(){u(this,"_isIframeSameOrigin",xr)}_getIframeDocument(e){try{return e.contentDocument}catch(t){return null}}_findIframe(e,t){if(Lr[t]==null)return null;let o=e.querySelectorAll(`iframe[id^="${Lr[t]}"]`);return o.length===1?o[0]:null}getAdContainer(e,t){if((t==null?void 0:t.type)!=="display")return{};let{elementId:r,adServer:o}=e,c=this.core.get(S).document().getElementById(r);if(c==null)return{};let f=this._findIframe(c,o);if(f==null)return{slotElement:c};let E=this._getIframeDocument(f);return this._isIframeSameOrigin(f,this.core.get(S).pageUrl())?{slotElement:c,sameOriginIframe:f,sameOriginIframeDocument:E}:{slotElement:c,crossOriginIframe:f}}};i(Pe,"AdLocator"),Pe=d([p],Pe);s();a();s();a();var to=(n=>(n[n.DEFAULT=0]="DEFAULT",n[n.WEATHER_CHANNEL_LWIM=1]="WEATHER_CHANNEL_LWIM",n[n.CELTRA=2]="CELTRA",n[n.CLIPCENTRIC=4]="CLIPCENTRIC",n[n.WEATHER_CHANNEL_MWIM=8]="WEATHER_CHANNEL_MWIM",n))(to||{}),ro=(n=>(n[n.NO_ERROR=0]="NO_ERROR",n[n.UNKNOWN_ERROR=1]="UNKNOWN_ERROR",n[n.TIMEOUT=2]="TIMEOUT",n[n.NO_AD_FOUND=4]="NO_AD_FOUND",n[n.MULTIPLE_ADS_LOCATED=8]="MULTIPLE_ADS_LOCATED",n[n.ABORTED=16]="ABORTED",n))(ro||{}),Je,Oe=(Je=class extends Error{constructor(t,r,o){super(r,o);u(this,"errorCode");this.errorCode=t}},i(Je,"m"),Je),Nr=i(n=>n instanceof Oe?n:n instanceof Error?new Oe(1,n.message,{cause:n}):new Oe(1,String(n)),"C"),no=(n=>(n[n.NO_ERROR=0]="NO_ERROR",n[n.UNKNOWN_ERROR=1]="UNKNOWN_ERROR",n[n.MULTIPLE_AD_FORMATS_DETECTED=2]="MULTIPLE_AD_FORMATS_DETECTED",n[n.DOM_EXCEPTION=4]="DOM_EXCEPTION",n))(no||{}),Ze,_e=(Ze=class extends Error{constructor(t,r,o){super(r,o);u(this,"errorCode");this.errorCode=t}},i(Ze,"i"),Ze),Ur=i(n=>n instanceof _e?n:n instanceof Error?new _e(1,n.message,{cause:n}):new _e(1,String(n)),"p"),oo=i(n=>{var e;return(e=n.ownerDocument)!=null?e:n},"U"),io=i((n,e)=>oo(e).evaluate(n,e,null,XPathResult.ORDERED_NODE_ITERATOR_TYPE,null),"X"),so=i(n=>(n==null?void 0:n.nodeType)===Node.ELEMENT_NODE?n:null,"v"),At=i((n,e)=>{let t=[],r=io(n,e),o=r.iterateNext();for(;o!=null;){let l=so(o);l!=null&&t.push(l),o=r.iterateNext()}return t},"s"),Fr=i((n,e)=>{let t=At(n,e);return t.length>0?t:!1},"u"),ao="celtra-ad-v",lo=`.//div[contains(concat(' ', @class), ' ${ao}')]`,uo=/<div[^>]*class\s*=\s*(['"])(?:(?:(?!\1)\S)+\s+)*\s*celtra-ad-v/g,co={name:"Celtra",id:2,detectDetached:i(n=>{let e=n.matchAll(uo);if(e.next().value===void 0)return!1;if(e.next().value!==void 0)throw new _e(2);return!0},"detectDetached"),detect(n){let e=go(n);return e===null?null:mo(e)}},mo=i(n=>e=>T(void 0,null,function*(){e.throwIfAborted();let t=(yield po(n,e)).celtra.viewabilityObservee;if(t==null)throw new Oe(4);return t}),"G"),po=i((n,e)=>T(void 0,null,function*(){return yield new Promise((t,r)=>{var o;((o=n.celtra)==null?void 0:o.loaded)===!0?t(n):(n.addEventListener("celtraLoaded",()=>{t(n)},{once:!0,passive:!0,signal:e}),e.addEventListener("abort",()=>{r(new Oe(16))},{once:!0,passive:!0}))})}),"K"),go=i(n=>{let e=At(lo,n);if(e.length===0)return null;if(e.length!==1)throw new _e(2,`Found ${e.length} Celtra tag elements`);return e[0]},"z"),qr=i(n=>{var t;let e=(t=n.ownerDocument.defaultView)==null?void 0:t.top;if(e==null)throw new Error("Window does not have a top property");return e},"D"),fo=1e3,kr=i((r,o)=>T(void 0,[r,o],function*(n,{signal:e,retryInterval:t=fo}){let l=null;do{if(e.aborted)throw new Oe(16);l=n(),l===null&&(yield ho(t))}while(l===null);return l}),"f"),ho=i(n=>T(void 0,null,function*(){return yield new Promise(e=>setTimeout(e,n))}),"Y"),$r="ccfid",vo=/<script[^>]*id\s*=\s*(['"])\s*ccfid(?:(?!\1).)*\1/g,So=`.//script[contains(@id,"${$r}")]`,Eo=i(n=>{if(n==null)throw new Error("Clipcentric script node is null");if(!n.hasAttribute("id"))throw new Error("Clipcentric script node does not have an id attribute");let e=n.getAttribute("id");if(e==null)throw new Error("Clipcentric script node id attribute is null");return e.replace($r,"")},"J"),To={name:"clipcentric",id:4,detectDetached:i(n=>{let e=n.matchAll(vo);if(e.next().value===void 0)return!1;if(e.next().value!==void 0)throw new _e(2);return!0},"detectDetached"),detect(n){let e=At(So,n);if(e.length===0)return null;if(e.length>1)throw new _e(2);let[t]=e,r=Eo(t);return yo(r,n)}},yo=i((n,e)=>t=>T(void 0,null,function*(){let r=`.//div[@data-cc-id="${n}"]`;return yield kr(()=>Ao(e,r),{signal:t})}),"Q"),Ao=i((n,e)=>{let t=qr(n).document.documentElement,r=Fr(e,t);if(r!==!1&&r.length===1)return r[0];if(r!==!1&&r.length>1)throw new Oe(8);return null},"Z"),Ro=(n=>(n.DETECTED="DETECTED",n.NOT_DETECTED="NOT_DETECTED",n.DETECTION_FAILED="DETECTION_FAILED",n))(Ro||{}),Br=i((n,e)=>{let t=e.matchAll(n);if(t.next().value===void 0)return!1;if(t.next().value!==void 0)throw new _e(2);return!0},"A"),jr=i((n,e)=>{let t=At(n,e);if(t.length===0)return null;if(t.length>1)throw new _e(2);return t[0]},"F"),Gr=i((n,e)=>t=>T(void 0,null,function*(){return yield kr(()=>_o(n,e),{signal:t})}),"h"),_o=i((n,e)=>{let t=qr(n).document.documentElement,r=Fr(e,t);if(r!==!1&&r.length===1)return r[0];if(r!==!1&&r.length>1)throw new Oe(8);return null},"tt"),Co='.//div[@id="labBG"]',bo='.//div[@id="labBG"]',wo=/<div[^>]*id\s*=\s*(['"])\s*labBG\s*\1/g,Do={name:"Weather Channel LWIM",id:1,detectDetached:Br.bind(null,wo),detect(n){return jr(Co,n)!=null?Gr(n,bo):null}},Io='.//body[@data-product="labAdMwim"]',Po='.//div[@id="labAdDiv"]',Oo=/<body[^>]*data-product\s*=\s*(['"])\s*labAdMwim\s*\1/g,xo={name:"Weather Channel MWIM",id:8,detectDetached:Br.bind(null,Oo),detect(n){return jr(Io,n)!=null?Gr(n,Po):null}},Lo=i((n,e)=>()=>T(void 0,null,function*(){return yield new Promise(t=>{let r=new AbortController,{signal:o}=r,l=setTimeout(()=>{r.abort(),t({errorCode:2,diagnostics:Mr(2,e)})},5e3);Promise.resolve(n(o)).then(c=>{clearTimeout(l),t({adNode:c,errorCode:0})}).catch(c=>{clearTimeout(l),t({errorCode:Nr(c).errorCode,diagnostics:Mr(Nr(c).errorCode,e)})})})}),"O"),Mr=i((n,e)=>`${n}|${e}:${n}`,"N"),No=i(n=>{try{return n&&(n.contentDocument||n.contentWindow&&n.contentWindow.document||frames&&frames[n.name]&&frames[n.name].document)}catch(e){return null}},"mt"),Mo=i((n,e)=>{try{if(No(n)===null)return!1;if(n.src===""||n.src==="about:blank")return!0;let t=new URL(n.src),r=new URL(e.location.href);return t.origin===r.origin}catch(t){return!1}},"M"),Uo=i(n=>Array.from(n.querySelectorAll("iframe")).filter(e=>Mo(e,window)),"I"),Vr=i(n=>{let e=Uo(n);if(e.length===0)return[n];let t=e.map(r=>r.contentDocument).filter(r=>r!==null).flatMap(r=>Vr(r.documentElement));return[n,...t]},"g"),Fo=i((n,e,t)=>{let r=e.map(c=>`${c.adFormat}:0`).join(","),o=t.map(c=>`${c.adFormat}:${c.reason.errorCode}`).join(","),l=e.length>0&&t.length>0?",":"";return`${n}|${r}${l}${o}`},"b"),qo=i((n,e)=>{let t=0;n.length>1&&(t|=2);for(let r of e)t|=r.reason.errorCode;return t},"W"),Qr=i(n=>{let e=n.filter(l=>l.type==="DETECTED"),t=n.filter(l=>l.type==="DETECTION_FAILED"),r=qo(e,t),o=e.length===1;return r===0?{errorCode:r,detectedFormat:o?e[0]:null}:{errorCode:r,detectedFormat:null,diagnostics:Fo(r,e,t)}},"y"),ko=i((n,e)=>{let t=$o(n,e);return Qr(t)},"P"),$o=i((n,e)=>Vr(n).flatMap(t=>e.map(r=>Bo(r,t))),"st"),Bo=i((n,e)=>{try{let t=n.detect(e);return t===null?{type:"NOT_DETECTED",adFormat:n.id}:{type:"DETECTED",adFormat:n.id,locate:Lo(t,n.id)}}catch(t){return{type:"DETECTION_FAILED",adFormat:n.id,reason:Ur(t)}}},"lt"),jo=i((n,e)=>{let t=e.map(r=>Go(r,n));return Qr(t)},"S"),Go=i((n,e)=>{try{return n.detectDetached(e)?{type:"DETECTED",adFormat:n.id}:{type:"NOT_DETECTED",adFormat:n.id}}catch(t){return{type:"DETECTION_FAILED",adFormat:n.id,reason:Ur(t)}}},"dt"),Hr=[co,To,Do,xo],Kr=i(n=>jo(n,Hr),"He"),Wr=i(n=>ko(n,Hr),"Ue");s();a();var dt=i(n=>{var e,t;return(t=(e=n.sameOriginIframe)!=null?e:n.crossOriginIframe)!=null?t:n.slotElement},"getDefaultElement");s();a();var Yr=i(n=>n.readyState==="interactive"||n.readyState==="complete","isDocumentReady"),zr=i((n,e=2e3)=>T(void 0,null,function*(){let{crossOriginIframe:t,sameOriginIframe:r,sameOriginIframeDocument:o}=n;return t!=null?"cross-origin":r==null?"no-iframe":Yr(o)?"prev-ready":yield new Promise(l=>{let c=i(()=>{let E;try{E=Yr(o)}catch(y){f("error");return}E&&f("state-change")},"listener"),f=i(E=>{try{o.removeEventListener("readystatechange",c)}catch(y){}l(E)},"cleanupAndResolve");try{o.addEventListener("readystatechange",c)}catch(E){l("error")}setTimeout(()=>f("timeout"),e)})}),"waitForIFrameReadyState");var xe=class{constructor(){u(this,"plugins",{});u(this,"_waitForIFrameReadyState",zr)}locateAd(e,t){return T(this,null,function*(){var y,I;if(e.slotElement==null)return{adRef:dt(e),usedAdFormat:0,detectionDiagnostics:{errorCode:null,diagnostics:"No ad slot element"},locationDiagnostics:{errorCode:null}};let r=0,o,l=null;if(t.html!==void 0){if(l=Kr(t.html),l.errorCode!==0)return this.generateFallbackAdLocationResult(e,l);if(l.detectedFormat===null)return this.generateDefaultAdLocationResult(e,l);let ct=Date.now();o=yield this._waitForIFrameReadyState(e),r=Date.now()-ct}let c=Wr(e.slotElement);if(c.errorCode!==0)return this.generateFallbackAdLocationResult(e,c);if(c.detectedFormat===null)return l!==null&&this.core.get(x).fireEvent("detect-custom-ad-format",{cd120:o,cd130:this.getDetectedFormatId((y=l==null?void 0:l.detectedFormat)==null?void 0:y.adFormat),cd131:this.getDetectedFormatId(0),cm120:r},{sampleInterval:1,spoofWrapperId:!0}),this.generateDefaultAdLocationResult(e,c);let f=c.detectedFormat,E=yield f.locate();return this.isAdSuccess(E)?(this.core.get(x).fireEvent("detect-custom-ad-format",{cd120:o,cd130:this.getDetectedFormatId((I=l==null?void 0:l.detectedFormat)==null?void 0:I.adFormat),cd131:this.getDetectedFormatId(c.detectedFormat.adFormat),cm120:r},{sampleInterval:100,spoofWrapperId:!0}),{adRef:E.adNode,usedAdFormat:f.adFormat,detectionDiagnostics:{errorCode:c.errorCode},locationDiagnostics:{errorCode:E.errorCode}}):this.generateFallbackAdLocationResult(e,c,E)})}isAdSuccess(e){return e.errorCode===0}getDetectedFormatId(e){return e===void 0?"0":`${e}`}generateDefaultAdLocationResult(e,t){var r;return{adRef:dt(e),usedAdFormat:0,detectionDiagnostics:{errorCode:t.errorCode,diagnostics:(r=t.diagnostics)!=null?r:""},locationDiagnostics:{errorCode:null}}}generateFallbackAdLocationResult(e,t,r){var o;return Ue(L({},this.generateDefaultAdLocationResult(e,t)),{locationDiagnostics:r!==void 0?{errorCode:(o=r==null?void 0:r.errorCode)!=null?o:0,diagnostics:r==null?void 0:r.diagnostics}:{errorCode:null}})}};i(xe,"AdLocation"),xe=d([p],xe);s();a();var Le=class{constructor(){u(this,"committedAdCandidates",new v.Set);u(this,"plugins",{adCandidateCommitted:i(e=>[],"adCandidateCommitted")})}commitAdCandidate(e){this.committedAdCandidates.add(e),this.plugins.adCandidateCommitted(e)}getAdCandidates(){return Array.from(this.committedAdCandidates)}};i(Le,"AdLocationHub"),Le=d([p],Le);var Vo=5e3,k=class{constructor(){u(this,"plugins",{adCandidateChanged:i((e,t)=>[],"adCandidateChanged"),adCandidateCommitted:i((e,t)=>[],"adCandidateCommitted"),adCandidatePubliclyCommitted:i(e=>[],"adCandidatePubliclyCommitted")});u(this,"adCandidate",new v.Link)}_changeAdCandidate(e,t,r){let o=this.adCandidate.get(e),l=o.peek();return(l==null?void 0:l.element)!==t&&(l==null||l.destroy(),l=v.Create({parent:e,element:t}),o.add(l),this.plugins.adCandidateChanged(l,r)),l}_schedulePublicCommit(e){let t=this.core.get(S).time(),r=Vo-(t-e.parent.timestamp);r<=0?this.plugins.adCandidatePubliclyCommitted(e):this.core.get(G).setTimeout(e,()=>this.plugins.adCandidatePubliclyCommitted(e),r)}_onAdRendered(e,t){this._onAdRenderedAsync(e,t)}_onAdRenderedAsync(e,t){return T(this,null,function*(){var r;try{if((t==null?void 0:t.type)!=="display")return;let o=this.core.get(Pe).getAdContainer(e,t);if(o.slotElement==null)return;let l=dt(o);this._changeAdCandidate(t,l,{usedAdFormat:0,adContainer:o});let c=yield this.core.get(xe).locateAd(o,t);if(t.destroyed)return;let f={usedAdFormat:c.usedAdFormat,adContainer:o,adDetectionDiagnostics:c.detectionDiagnostics.diagnostics,adLocatingDiagnostics:c.locationDiagnostics.diagnostics},E=this._changeAdCandidate(t,(r=c.adRef)!=null?r:l,f);this.core.get(Le).commitAdCandidate(E),this.plugins.adCandidateCommitted(E,f),this._schedulePublicCommit(E)}catch(o){}})}};i(k,"AutoAdLocation"),d([h(_,"adRendered")],k.prototype,"_onAdRendered",1),d([Z],k.prototype,"_onAdRenderedAsync",1),k=d([p],k);var de=class{_isEligibleForMeasurement(e){return e.adServer==="custom"||e.adServer==="ast"||this.core.get(F).isAdServerConfigured(e.adUnit.network)}_isReadyForMeasurement(e){return this._isEligibleForMeasurement(e)&&(e.adServer==="custom"||this.core.get(V).isTargetingReliable(e))}_disableCreativeWrapperMeasurement(e){return this._isEligibleForMeasurement(e)&&!this.core.get(Ie).shouldPerformIntentionalDuplicateMeasurement(e)}_onSlotDiscovered(e){this._isEligibleForMeasurement(e)&&(this.core.get(O).setIdentifiers(e),this.core.get(O).setConstants(e),this.core.get(O).setAdUnit(e))}_onAdRequested(e){this._isEligibleForMeasurement(e)&&(this.core.get(O).setCustomDimensions(e),this.core.get(O).setQualityTargeting(e))}_onAdCandidateCommitted({parent:e,element:t},r){let o=e.parent;if((e==null?void 0:e.type)!=="display"||!this._isReadyForMeasurement(o))return;this.core.get(O).setAdInstance(o,e),this.core.get(O).setEngagement(o),this.core.get(O).setAdLocation(o,r.adContainer),this.core.get(O).setDebug(o),this.core.get(O).setGdpr(o),this.core.get(O).setDetectedAdFormat(o,r);let l=this.core.get(O).createScriptElement(o,t);this.core.get(le).increment(),this.core.get(Re).injectScriptElement(o,l)}_onAdRemoved(e){this.core.get(le).decrement(),this.core.get(Re).removeScriptElement(e)}};i(de,"GptMeasurement"),d([h(V,"disableCreativeWrapperMeasurement")],de.prototype,"_disableCreativeWrapperMeasurement",1),d([h(A,"slotDiscovered")],de.prototype,"_onSlotDiscovered",1),d([h(_,"adRequestedOrInferred")],de.prototype,"_onAdRequested",1),d([h(k,"adCandidateCommitted")],de.prototype,"_onAdCandidateCommitted",1),d([h(_,"adRemoved")],de.prototype,"_onAdRemoved",1),de=d([p,P(j),P(z),P(Ae)],de);s();a();s();a();s();a();s();a();s();a();var Xr=[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1];var B=class{constructor(){u(this,"plugins",{adVisibilityChanged:i((e,t)=>[],"adVisibilityChanged")});u(this,"lastEntry",new v.Map)}_onAdCandidateCommitted(e){let t=this.core.get(S).window();t.IntersectionObserver!=null&&this.core.get(Y).observe(e,t.IntersectionObserver,e.element,r=>this._onIntersectionObserverEvent(e,r),{threshold:Xr})}get(e){return this.lastEntry.get(e)}getMonitoredCandidates(){return[...this.lastEntry.keys()]}_onIntersectionObserverEvent(e,t){for(let r of t)this.lastEntry.set(e,r),this.plugins.adVisibilityChanged(e,r)}};i(B,"AdVisibility"),d([h(k,"adCandidateCommitted")],B.prototype,"_onAdCandidateCommitted",1),d([Z],B.prototype,"_onIntersectionObserverEvent",1),B=d([p],B);s();a();s();a();var ue=class{constructor(){u(this,"listeners",new v.Map)}_getListeners(){return this.listeners.conjure(this.core.session,()=>({slotVisibilityChanged:new Set,impressionViewable:new Set}))}_addEventListener(e,t){let r=this._getListeners()[e];r!=null&&r.add(t)}_removeEventListener(e,t){let r=this._getListeners()[e];r!=null&&r.delete(t)}fireEvent(e,t){let r=this._getListeners()[e];for(let o of r)try{o(t)}catch(l){}}};i(ue,"OutboundEvents"),d([h(N,"addEventListener")],ue.prototype,"_addEventListener",1),d([h(N,"removeEventListener")],ue.prototype,"_removeEventListener",1),ue=d([p],ue);var ce=class{constructor(){u(this,"committed",new v.Set);u(this,"buffer",new v.Map)}_onAdCandidatePubliclyCommitted(e){var r;this.committed.add(e);let t=(r=this.buffer.get(e))!=null?r:[];for(let{type:o,payload:l}of t)this.core.get(ue).fireEvent(o,l);this.buffer.delete(e)}fireEvent(e,t,r){this.committed.has(e)?this.core.get(ue).fireEvent(t,r):this.buffer.summon(e,[]).push({type:t,payload:r})}};i(ce,"BufferedEvents"),d([h(k,"adCandidatePubliclyCommitted")],ce.prototype,"_onAdCandidatePubliclyCommitted",1),ce=d([p],ce);var Qo=1e3,Ho=.5,Ko=.3,Wo=242e3,Q=class{constructor(){u(this,"plugins",{impressionViewable:i(e=>[],"impressionViewable")});u(this,"viewableTimer",new v.Link);u(this,"viewable",new v.Set)}_onAdVisibilityChanged(e,t){this._startOrStopTimer(e,t)}_onPageHide(){for(let e of this.viewableTimer.keys())this._clearTimer(e)}_onPageVisible(){this.core.get(B).getMonitoredCandidates().forEach(e=>{let t=this.core.get(B).get(e);t!=null&&this._startOrStopTimer(e,t)})}_startOrStopTimer(e,t){if(this.viewable.has(e)||this.core.get(M).isHidden())return;let r=this._isLargeAd(t.boundingClientRect)?Ko:Ho;t.intersectionRatio>=r?this._startTimer(e):this._clearTimer(e)}_startTimer(e){this.viewableTimer.get(e).conjure(()=>this.core.get(G).setTimeout(e,()=>this._onTimeout(e),Qo))}_onTimeout(e){let t=e.parent.parent;this.viewable.add(e),this.plugins.impressionViewable(e),this.core.get(ce).fireEvent(e,"impressionViewable",{slot:{elementId:t.elementId,source:t.adServer},timestamp:Date.now()})}_clearTimer(e){var t;(t=this.viewableTimer.get(e).peek())==null||t.destroy()}isMobile(){var o;let{navigator:e}=this.core.get(S).window(),t=(o=e.userAgentData)==null?void 0:o.mobile;if(typeof t=="boolean")return t;let{userAgent:r}=e;return r.includes("Mobi")||r.includes("Tablet")}_isLargeAd(e){return this.isMobile()?!1:e.width*e.height>=Wo}};i(Q,"ImpressionViewable"),d([h(B,"adVisibilityChanged")],Q.prototype,"_onAdVisibilityChanged",1),d([h(M,"pageHide")],Q.prototype,"_onPageHide",1),d([h(M,"pageVisible")],Q.prototype,"_onPageVisible",1),Q=d([p],Q);s();a();var tn=fn(en());var X=class{constructor(){u(this,"fireEvent",new v.Map);u(this,"plugins",{slotVisibilityChanged:i((e,t)=>[],"slotVisibilityChanged")})}_onAdVisibilityChanged(e,t){let r=e.parent.parent;this.fireEvent.conjure(e,()=>(0,tn.default)(l=>{this.plugins.slotVisibilityChanged(e,l),this.core.get(ce).fireEvent(e,"slotVisibilityChanged",{slot:{elementId:r.elementId,source:r.adServer},visibility:{percentage:l*100},timestamp:Date.now()})},200))(t.intersectionRatio)}_onPageHide(){this.fireEvent.forEach(e=>e(0))}_onPageVisible(){this.fireEvent.forEach((e,t)=>{var o;let r=(o=this.core.get(B).get(t))==null?void 0:o.intersectionRatio;r!=null&&e(r)})}};i(X,"SlotVisibilityChanged"),d([h(B,"adVisibilityChanged")],X.prototype,"_onAdVisibilityChanged",1),d([h(M,"pageHide")],X.prototype,"_onPageHide",1),d([h(M,"pageVisible")],X.prototype,"_onPageVisible",1),X=d([p],X);s();a();var zo=1e-4,Xo=10,Jo=9.5*60*1e3,Zo=10*60*1e3,ei=5e3;var J=class{constructor(){u(this,"enabled",!1);u(this,"lastTimestamp",new v.Map);u(this,"lastBucketIndex",new v.Map);u(this,"buckets",new v.Map);u(this,"viewableTimestamp",new v.Map);u(this,"trackerCount",new v.Map);u(this,"lastTrackerSendTime",new v.Map);u(this,"visitId",new v.Map)}_initialize(){let{Math:e}=this.core.get(S).window();this.enabled=e.random()<zo}_onImpressionViewable(e){if(!this.enabled)return;let{Date:t}=this.core.get(S).window();this.viewableTimestamp.set(e,t.now())}_onVisibilityChanged(e,t){this.enabled&&this._updateBuckets(e,t)}_onAdCandidateCommitted(e){this.enabled&&(this.visitId.set(e,this.core.get(D).getVisitUuid()),this.core.get(G).setTimeout(e,()=>this._sendTracker(e),Jo,!0))}_onPageHide(){if(this.enabled)for(let e of this.visitId.keys())this._sendTracker(e)}_sendTracker(e){var y;let t=this.core.get(S).time(),r=e.parent.parent,o=this.lastTrackerSendTime.get(e),l=(y=this.trackerCount.get(e))!=null?y:0;if(l>=Xo||t-e.parent.timestamp>Zo||o!=null&&t-o<ei)return;let c=this.viewableTimestamp.get(e),f={cd110:this.visitId.get(e),cd111:r.uuid,cm106:this.core.get(_).getNonEmptyAdRenderedCount(r),cm107:l,cm108:c!=null?c/1e3:void 0,cm109:this.core.get(Q).isMobile()?1:0},E=this._updateBuckets(e);for(let I=0;I<E.length;I++)f[`cm${110+I}`]=E[I];this.core.get(x).fireEvent("visibility-api",f,{spoofWrapperId:!0}),this.trackerCount.set(e,l+1),this.lastTrackerSendTime.set(e,t)}_getBucketIndex(e){return e===0?0:Math.floor(Math.min(e,1)*10)+1}_updateBuckets(e,t){let r=this.core.get(S).time(),o=this.buckets.conjure(e,()=>[0,0,0,0,0,0,0,0,0,0,0,0]),l=this.lastTimestamp.summon(e,r),c=this.lastBucketIndex.summon(e,0);return this.lastTimestamp.set(e,r),t!=null&&this.lastBucketIndex.set(e,this._getBucketIndex(t)),o[c]+=r-l,o}};i(J,"VisibilityValidation"),d([w],J.prototype,"_initialize",1),d([h(Q,"impressionViewable")],J.prototype,"_onImpressionViewable",1),d([h(X,"slotVisibilityChanged")],J.prototype,"_onVisibilityChanged",1),d([h(k,"adCandidateCommitted")],J.prototype,"_onAdCandidateCommitted",1),d([h(M,"pageHideThrottled")],J.prototype,"_onPageHide",1),J=d([p],J);var et=class{};i(et,"Visibility"),et=d([p,P(X),P(Q),P(J)],et);s();a();s();a();s();a();var rn=i((n,e=!0)=>{let t=document.createElement("script");return t.setAttribute("src",n),e&&t.setAttribute("async",""),t},"createScriptElement");s();a();s();a();var Ne=i(()=>{var n,e,t;return Math.floor((t=(e=(n=window.performance)==null?void 0:n.now)==null?void 0:e.call(n))!=null?t:Date.now())},"perfNowOrDateNow");s();a();var $t=class $t{constructor(){u(this,"_firstQueuedAdRequest",1/0);u(this,"_lastQueuedAdRequest",-1/0);u(this,"_firstReleasedAdRequest",1/0);u(this,"_lastReleasedAdRequest",-1/0);u(this,"_lastReleasedAdRequestTime",null);u(this,"_hasBeenSuccessful",!1)}setAdRequestQueued(e){this._firstQueuedAdRequest=Math.min(this._firstQueuedAdRequest,e),this._lastQueuedAdRequest=Math.max(this._lastQueuedAdRequest,e)}setAdRequestReleased(e){this._firstReleasedAdRequest=Math.min(this._firstReleasedAdRequest,e),this._lastReleasedAdRequest=Math.max(this._lastReleasedAdRequest,e),this._lastReleasedAdRequestTime=Ne()}getLastQueuedAdRequest(){return Number.isFinite(this._lastQueuedAdRequest)?this._lastQueuedAdRequest:null}getLastReleasedAdRequest(){return Number.isFinite(this._lastReleasedAdRequest)?this._lastReleasedAdRequest:null}getLastReleasedAdRequestTime(){return this._lastReleasedAdRequestTime}getLastSuccessfulAdRequest(){let e=Math.max(this._firstQueuedAdRequest,this._firstReleasedAdRequest),t=Math.min(this._lastQueuedAdRequest,this._lastReleasedAdRequest);return t<e?null:t}getPreviouslySuccessful(){return this._hasBeenSuccessful}markAdServerAdRequest(){this.getLastSuccessfulAdRequest()!=null&&(this._hasBeenSuccessful=!0),this._firstQueuedAdRequest=1/0,this._lastQueuedAdRequest=-1/0,this._firstReleasedAdRequest=1/0,this._lastReleasedAdRequest=-1/0,this._lastReleasedAdRequestTime=null}};i($t,"SlotAdRequestTracker");var Rt=$t;s();a();var _t=(c=>(c.UNKNOWN="UNKNOWN",c.SEEN="SEEN",c.NOT_READY="NOT_READY",c.POSSIBLE_TIMEOUT="POSSIBLE_TIMEOUT",c.PREV_READY="PREV_READY",c.READY="READY",c))(_t||{});var Bt="dv_upt_slot_ready",ti=["https://securepubads.g.doubleclick.net/gampad/ads?","https://pagead2.googlesyndication.com/gampad/ads?"],ri=750,$=class{constructor(){u(this,"plugins",{onSlotRequested:i(()=>[],"onSlotRequested"),onSlotRequestValidated:i(()=>[],"onSlotRequestValidated")});u(this,"validateAdRequests",!1);u(this,"lastAdRequestQueued",null);u(this,"lastAdRequestReleased",null);u(this,"state",{adRequestTracker:new v.Map,triggered:new v.Map})}_getSlotAdRequestTracker(e){return this.state.adRequestTracker.conjure(e,()=>new Rt)}_onAdRequestQueued({index:e}){this.lastAdRequestQueued=e;for(let t of this.core.get(A).getSlots())this._getSlotAdRequestTracker(t).setAdRequestQueued(e),this.state.triggered.set(t,!1)}_onAdRequestBeforeRelease({index:e}){let t=this.core.get(R);this.lastAdRequestReleased=e;for(let r of this.core.get(A).getSlots())this._getSlotAdRequestTracker(r).setAdRequestReleased(e),this.validateAdRequests&&t.setSlotTargeting(r,{[Bt]:["1"]})}_onSlotDiscovered(e,t){t===0&&this.lastAdRequestQueued!=null&&this._getSlotAdRequestTracker(e).setAdRequestQueued(this.lastAdRequestQueued)}_determineReadyState(e){let t=this._getSlotAdRequestTracker(e),r=t.getLastQueuedAdRequest(),o=t.getLastSuccessfulAdRequest(),l=t.getPreviouslySuccessful();return o!=null?"READY":l?"PREV_READY":r!=null?"NOT_READY":Date.now()-e.creationTime>ri?"SEEN":"UNKNOWN"}getSlotReadyStateOnAdServerAdRequest(e){let t=this._determineReadyState(e);if(t==="READY"){if(this.state.triggered.get(e)===!0)return{readyState:"PREV_READY"};this.state.triggered.set(e,!0);let r=this._getSlotAdRequestTracker(e).getLastReleasedAdRequestTime();return{readyState:t,readyFor:Ne()-r}}return{readyState:t}}_onSlotRequested(e){let{readyState:t,readyFor:r}=this.getSlotReadyStateOnAdServerAdRequest(e);this._getSlotAdRequestTracker(e).markAdServerAdRequest(),this.validateAdRequests&&this.core.get(R).setSlotTargeting(e,{[Bt]:["0"]}),this.plugins.onSlotRequested(e,t,r)}enableAdRequestValidation(){if(!this.validateAdRequests)try{let e=this._processResourceRequest.bind(this);new PerformanceObserver(r=>r.getEntries().forEach(e)).observe({type:"resource"}),this.validateAdRequests=!0}catch(e){}}_processResourceRequest(e){var t,r,o;try{if(ti.every(f=>!e.name.startsWith(f)))return;let c=(r=(t=new URL(e.name).searchParams.get("prev_scp"))==null?void 0:t.split("|"))!=null?r:[];for(let f of c){let y=((o=new URLSearchParams(f).get(Bt))==null?void 0:o[0])==="1";this.plugins.onSlotRequestValidated(y)}}catch(l){}}};i($,"SlotReadinessTracker"),d([h(C,"adRequestQueued")],$.prototype,"_onAdRequestQueued",1),d([h(C,"adRequestBeforeRelease"),h(C,"adRequestTimedOut")],$.prototype,"_onAdRequestBeforeRelease",1),d([h(A,"slotDiscovered")],$.prototype,"_onSlotDiscovered",1),d([h(_,"adRequested")],$.prototype,"_onSlotRequested",1),$=d([p],$);var jt="__dvtag_debug_mode",nn="1",he=class{constructor(){u(this,"enabled",!1);u(this,"debugScriptResolve",i(e=>{},"debugScriptResolve"))}_initialize(){let e=this.core.get(S).window();try{this.enabled=e===e.top&&e.sessionStorage.getItem(jt)===nn}catch(t){}this.enabled&&(this.core.get(C).delayAdRequests(new Promise(t=>{this.debugScriptResolve=t})),this._loadDebugScript())}_toggleDebugMode(){let e=this.core.get(S).window();this.enabled?e.sessionStorage.removeItem(jt):e.sessionStorage.setItem(jt,nn),e.location.reload()}debugScriptLoaded(){this.debugScriptResolve(void 0)}_loadDebugScript(){return T(this,null,function*(){let e=this.core.get(S).document(),t=this.core.get(D).getCurrentScriptBaseUrl(),r=rn(`${t}debug.js`,!0);e.readyState==="loading"&&(yield new Promise(o=>e.addEventListener("DOMContentLoaded",()=>o(void 0)))),e.head.appendChild(r)})}};i(he,"DebugScriptLoader"),d([w],he.prototype,"_initialize",1),d([h(N,"toggleDebugMode")],he.prototype,"_toggleDebugMode",1),he=d([P($),p],he);s();a();var Gt="qt_loaded",on="pts_pid";s();a();var Vt=i(n=>[...new Set(n.filter(Jt).map(e=>e.toLowerCase()))],"getGreenlightSignal");var Me=class{_autoInsertGlobalTargeting(e){this.core.get(R).setGlobalTargeting({[Gt]:Vt(Object.keys(e))},!0)}_autoInsertSlotTargeting(e,t){this.core.get(R).setSlotTargeting(e,{[Gt]:Vt(Object.keys(t))},!0)}};i(Me,"SignalsAutoInsertion"),d([h(b,"userTargetingUpdated"),h(b,"pageTargetingUpdated")],Me.prototype,"_autoInsertGlobalTargeting",1),d([h(b,"slotTargetingUpdated")],Me.prototype,"_autoInsertSlotTargeting",1),Me=d([p,P(j),P(he)],Me);s();a();s();a();var ni=on,me=class{constructor(){u(this,"restoreCount",0)}getRestoreCount(){return this.restoreCount}_restoreTargeting(){let e=this.core.get(R);for(let t of this.core.get(A).getSlots())e.getAdServerTargeting(t,ni).length===0&&(e.setAdServerTargeting(t),this.restoreCount+=1)}};i(me,"TargetingRestorer"),d([h(C,"adRequestQueued")],me.prototype,"_restoreTargeting",1),me=d([p],me);s();a();var Qt=class Qt{constructor(){u(this,"total",0);u(this,"dataPoints",0)}push(e){return this.total+=e,this.dataPoints+=1,this}get(){return this.total/this.dataPoints}getRounded(){return Math.round(this.get())}};i(Qt,"Average");var ut=Qt;var oi="implcheck",ii=1e-4,si=5e3,ai=5,oe=class{constructor(){u(this,"enabled",!1);u(this,"totalInternalAdRequestQueuedCount",0);u(this,"totalAdRequestsCompletedCount",0);u(this,"totalSlotsRequestedCount",0);u(this,"totalSlotsRequestedOkCount",0);u(this,"totalTimeoutCount",0);u(this,"trackersSent",0);u(this,"totalValidatedAdServerRequests",{ok:0,notOk:0});u(this,"lastOnDvtagReadyCall",NaN);u(this,"slotReqAvgTimeSinceCall",new ut);u(this,"slotReqAvgTimeSinceDone",new ut);u(this,"batch",null)}isDebugForceEnabled(){return!1}_initialize(){this.enabled=this.core.get(oe).isDebugForceEnabled()||Math.random()<ii,this.enabled&&this.core.get($).enableAdRequestValidation()}_onAdRequestQueued(e){this.enabled&&(this.lastOnDvtagReadyCall=Ne(),e.internal&&(this.totalInternalAdRequestQueuedCount+=1))}_onAdRequestCompleted(e){this.enabled&&(this.totalAdRequestsCompletedCount+=1,(e.status==="tag-timeout"||e.status==="queue-timeout")&&(this.totalTimeoutCount+=1))}_onSlotRequested(e,t,r){if(!this.enabled)return;let o=this._getOrStartBatch();this.totalSlotsRequestedCount+=1,t==="READY"&&(this.totalSlotsRequestedOkCount+=1),o.slots[t].push(e.elementId),r!=null&&(this.slotReqAvgTimeSinceDone.push(r),this.slotReqAvgTimeSinceCall.push(Ne()-this.lastOnDvtagReadyCall));let l=this._validateAdServerTargeting(e);l===1?o.targeting.extendedCount+=1:l===0&&(o.targeting.reducedCount+=1)}_validateAdServerTargeting(e){let t=this.core.get(R),r=2,o=t.getTargeting(e);for(let[l,c]of Object.entries(o)){let f=t.getAdServerTargeting(e,l);if(c.length>f.length||c.some(E=>!f.includes(E)))return 0;f.length>c.length&&(r=1)}return r}_onSlotRequestValidated(e){e?this.totalValidatedAdServerRequests.ok+=1:this.totalValidatedAdServerRequests.notOk+=1}_sendTracker(){let e=this.core.get(me).getRestoreCount(),{slots:t,targeting:r,start:o}=this._getOrStartBatch(),l={cd110:t.READY.join(),cd111:t.PREV_READY.join(),cd112:t.SEEN.join(),cd113:t.NOT_READY.join(),cd114:t.UNKNOWN.join(),cd115:t.POSSIBLE_TIMEOUT.join(),cm106:this.trackersSent,cm107:o,cm109:this.totalTimeoutCount,cm110:this.slotReqAvgTimeSinceCall.getRounded(),cm111:this.slotReqAvgTimeSinceDone.getRounded(),cm112:this.totalSlotsRequestedOkCount,cm113:this.totalValidatedAdServerRequests.ok,cm114:this.totalValidatedAdServerRequests.notOk,cm115:e,cm116:this.totalAdRequestsCompletedCount,cm117:this.totalInternalAdRequestQueuedCount,cm118:this.totalSlotsRequestedCount,cm119:r.reducedCount,cm120:r.extendedCount};this.core.get(x).fireEvent(oi,l),this.batch=null,this.trackersSent+=1,this.trackersSent>=ai&&(this.enabled=!1)}_getOrStartBatch(){return this.batch==null&&(this.batch={start:Ne(),slots:Object.fromEntries(Object.values(_t).map(e=>[e,[]])),targeting:{extendedCount:0,reducedCount:0}},setTimeout(()=>this._sendTracker(),si)),this.batch}};i(oe,"ImplementationValidator"),d([w],oe.prototype,"_initialize",1),d([h(C,"adRequestQueued")],oe.prototype,"_onAdRequestQueued",1),d([h(C,"adRequestCompleted")],oe.prototype,"_onAdRequestCompleted",1),d([h($,"onSlotRequested")],oe.prototype,"_onSlotRequested",1),d([h($,"onSlotRequestValidated")],oe.prototype,"_onSlotRequestValidated",1),oe=d([p],oe);s();a();s();a();var ve=class{_defineSlot(e,t={}){if(typeof e!="string"||t==null||typeof t!="object")return;let r={id:K(t.id),network:K(t.network),sizes:t.sizes!=null?ze(t.sizes):void 0,position:K(t.position)};this.core.get(A).createSlot("custom",0,e,r)}_adRendered(e,t){if(typeof e!="string"||t==null||typeof t!="object")return;let r=this.core.get(A).getSlot("custom",e);if(r==null)return;let o={type:"display",advertiserId:K(t.advertiserId),campaignId:K(t.campaignId),creativeId:K(t.creativeId),lineItemId:K(t.lineItemId),renderedSize:Et(t.renderedSize)};this.core.get(_).setAdRendered(r,o)}_getTargeting(e){let t=this.core.get(R);if(typeof e!="string")return t.getTargeting();let r=this.core.get(A).getSlot("custom",e);return t.getTargeting(r)}};i(ve,"CustomAdServer"),d([h(N,"defineSlot")],ve.prototype,"_defineSlot",1),d([h(N,"getTargeting")],ve.prototype,"_getTargeting",1),ve=d([p],ve);s();a();var Se=class{_initialize(){this._flushCommandBuffer()}_flushCommandBuffer(){return T(this,null,function*(){var l;yield Promise.resolve();let e=this.core.get(S).window(),t=(l=e.dvtag)!=null?l:{};e.dvtag=t,t.cmd instanceof Array||(t.cmd=[]);let r=t.cmd,o=r.length;t.cmd={push:i((...c)=>{for(let f of c)try{f()}catch(E){}return o+=c.length,o},"push")},t.cmd.push(...r)})}};i(Se,"CommandBuffer"),d([w],Se.prototype,"_initialize",1),d([Z],Se.prototype,"_flushCommandBuffer",1),Se=d([p],Se);var tt=class{};i(tt,"HouseHold"),tt=d([p,P(N),P(C),P(Se),P(x),P(q),P(ve),P(me),P(V)],tt);s();a();s();a();var sn=i(n=>{var e,t,r,o,l,c;try{let f={};for(let[E,y,I]of(t=(e=window.dvtag)==null?void 0:e._overrides)!=null?t:[])(r=f[E])!=null||(f[E]={}),(l=(o=f[E])[y])!=null||(o[y]=I);for(let E of n)for(let[y,I]of Object.entries((c=f[E.name])!=null?c:{}))E.prototype[y]=I}catch(f){}},"applyDebugOverrides");var li=i(()=>{var n,e;((n=window.dvtag)==null?void 0:n._core)==null&&(sn(pt),window.dvtag=(e=window.dvtag)!=null?e:{},window.dvtag._core=new ot(pt),window.dvtag._core.execute())},"main");try{li()}catch(n){}})();
