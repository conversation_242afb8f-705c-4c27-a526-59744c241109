define(["underscore","jquery","backbone","module","js/components/beehive","js/mixins/api_access"],function(h,d,e,t,i,n){function r(e){e=e||{},this.aid=h.uniqueId("application"),this.debug=!0,h.extend(this,h.pick(e,["timeout","debug"])),this.initialize.apply(this,arguments)}function o(){this.container={}}var s="function"==typeof window.__setAppLoadingProgress?window.__setAppLoadingProgress:function(){};return h.extend(o.prototype,{has:function(e){return this.container.hasOwnProperty(e)},get:function(e){return this.container[e]},remove:function(e){delete this.container[e]},add:function(e,t){this.container[e]=t}}),h.extend(r.prototype,{initialize:function(e,t){this.__beehive=new i,this.__modules=new o,this.__controllers=new o,this.__widgets=new o,this.__plugins=new o,this.__barbarianRegistry={},this.__barbarianInstances={}},shim:function(){window.location.origin||(window.location.origin=window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:""))},loadModules:function(e,t){var i,n=[],r=this,o=e.core,s=(o&&h.each(["controllers","modules","services","objects"],function(e){o[e]&&(i=r._loadModules(e,o[e]))&&n.push(i)}),e.plugins),a=e.widgets,u=(t&&t.eagerLoad?(s&&(i=r._loadModules("plugins",s))&&n.push(i),a&&(i=r._loadModules("widgets",a))&&n.push(i)):(s&&h.each(s,function(e,t){var i={};i[t]=e,r.__plugins.add(t,r._loadModules("plugins",i,!1,!0))}),a&&h.each(a,function(e,t){var i={};i[t]=e,r.__widgets.add(t,r._loadModules("widgets",i,!1,!0))})),1===n.length&&n.push(i),0),c=(n.map(function(e){return e.then(function(){r.logModuleLoaded(u+=1,n.length)})}),d.Deferred());return d.when.apply(d,n).then(function(){h.each(arguments,function(e,t){h.isArray(e)&&(r.debug&&console.log("application: registering "+e[0]),r._registerLoadedModules.apply(r,e))})}).done(function(){c.resolve()}).fail(function(){console.error("Generic error - we were not successul in loading all modules for config",e),arguments.length&&console.error(arguments),c.reject.apply(c,arguments)}),c.promise()},getBeeHive:function(){return this.__beehive},_registerLoadedModules:function(e,t){var i,n,r,o,s,a=this.getBeeHive(),u=this,c=function(e,t){if(t)return u.debug&&console.log("Creating instance of: "+e),t.prototype?new t:t&&t.hasOwnProperty(e)?t[e]:t;console.warn("Object "+e+" is empty, cannot instantiate it!")};if("controllers"===e)r=h.bind(this.hasController,this),s=h.bind(function(e){this.__controllers.remove(e)},this),o=h.bind(function(e,t){this.__controllers.add(e,t)},this);else if("services"===e)r=h.bind(a.hasService,a),s=h.bind(a.removeService,a),o=h.bind(a.addService,a);else if("objects"===e)r=h.bind(a.hasObject,a),s=h.bind(a.removeObject,a),o=h.bind(a.addObject,a);else if("modules"===e)c=function(e,t){return t},r=h.bind(this.hasModule,this),s=h.bind(function(e){this.__modules.remove(e)},this),o=h.bind(function(e,t){this.__modules.add(e,t)},this);else if("widgets"===e)r=h.bind(this.hasWidget,this),s=h.bind(function(e){this.__widgets.remove(e)},this),o=h.bind(function(e,t){this.__widgets.add(e,t)},this),c=function(e,t){return t};else{if("plugins"!==e)throw new Error("Unknown section: "+e);r=h.bind(this.hasPlugin,this),s=h.bind(function(e){this.__plugins.remove(e)},this),o=h.bind(function(e,t){this.__plugins.add(e,t)},this),c=function(e,t){return t}}h.each(h.pairs(t),function(e){i=e[0],n=e[1],r(i)&&s(i);e=c(i,n);e?o(i,e):console.warn("Removing "+i+"(because it is null!)")})},_checkPrescription:function(e){h.each(h.pairs(e),function(e,t,i){var n=e[0],e=e[1];if(!h.isString(n)||!h.isString(e))throw new Error("You are kidding me, the key/implementation must be string values")})},_loadModules:function(e,t,i,n){function r(){return o.debug&&console.time("startLoading"+e),require(u,l,g),o._setTimeout(c).promise()}var o=this,s=(this._checkPrescription(t),this.debug&&console.log("application: loading "+e,t),{}),a=h.keys(t),u=h.values(t),c=d.Deferred(),l=function(){o.debug&&console.timeEnd("startLoading"+e);var n=arguments;h.each(a,function(e,t,i){s[e]=n[t]});try{c.resolve(e,s)}catch(e){var t=o.getService("PubSub").getHardenedInstance();t.publish(t.NAVIGATE,"404",{message:"Page Not Found or Internal Error\n              <p>Error: <code>".concat(e.message,"</code></p>\n            ")})}o.debug&&console.log("Loaded: type="+e+" state="+c.state(),s)},g=function(e){var t=e.requireModules&&e.requireModules[0];o.debug&&console.warn("Error loading impl="+t,e.requireMap),i?o.debug&&console.warn("Ignoring error"):c.reject(e)};return(r.lazyLoad=n)?r:r()},_setTimeout:function(e){return setTimeout(function(){"resolved"!==e.state()&&e.reject("Timeout, application is loading too long")},this.timeout||6e4),e},logModuleLoaded:function(e,t){s(function(e){return e+50/t},"Loading Modules ".concat(e," of ").concat(t))},destroy:function(){this.getBeeHive().destroy()},activate:function(e){var i=this.getBeeHive(),n=this;n.debug&&console.log("application: beehive.activate()"),i.activate(i),h.each(this.getAllControllers(),function(e){n.debug&&console.log("application: controllers: "+e[0]+".activate(beehive, app)");e=e[1];"activate"in e&&e.activate(i,n)}),h.each(this.getAllModules(),function(t){try{n.debug&&console.log("application: modules: "+t[0]+".activate(beehive)");var e=t[1];"activate"in e&&e.activate(i)}catch(e){console.error("Error activating:"+t[0]),console.error(e)}}),this.__activated=!0},isActivated:function(){return this.__activated||!1},hasService:function(e){return this.getBeeHive().hasService(e)},getService:function(e){return this.getBeeHive().getService(e)},hasObject:function(e){return this.getBeeHive().hasObject(e)},getObject:function(e){return this.getBeeHive().getObject(e)},hasController:function(e){return this.__controllers.has(e)},getController:function(e){return this.__controllers.get(e)},hasModule:function(e){return this.__modules.has(e)},getModule:function(e){return this.__modules.get(e)},hasWidget:function(e){return this.__widgets.has(e)},getWidgetRefCount:function(e,t){t=this.__barbarianInstances[(t||"widget:")+e];return t?t.counter:-1},incrRefCount:function(e,t){e=e+":"+t;if(!this.__barbarianInstances[e])throw Error("Invalid operation"+e+" is not initialized");this.__barbarianInstances[e].counter++},getWidget:function(t){var i,n,r=d.Deferred(),o=this;return 1<arguments.length?(i={},n=[],h.each(arguments,function(e){var t=e;n.push(o._getWidget(e).fail(function(){throw console.error("Error loading: "+e),h.each(i,function(e,t){o.returnWidget(t),delete i[t]}),er}).done(function(e){i[t]=e}))}),d.when.apply(d,n).done(function(){r.resolve(i)})):t&&this._getWidget(t).done(function(e){r.resolve(e)}),setTimeout(function(){r.done(function(e){h.isArray(t)?h.each(t,function(e){o.returnWidget(e)}):o.returnWidget(t)})},1),r.promise()},_getWidget:function(e){return this._getThing("widget",e)},_getThing:function(t,i){var n=d.Deferred(),r=this;return this._lazyLoadIfNecessary(t,i).done(function(){var e=r._getOrCreateBarbarian(t,i);r.incrRefCount(t,i),n.resolve(e)}),n.promise()},returnWidget:function(e){var t=this.__barbarianInstances["widget:"+e];if(!t||!t.parent.dontKillMe)return t?(t.counter--,this._killBarbarian("widget:"+e),t.counter):-1},hasPlugin:function(e){return this.__plugins.has(e)},getPlugin:function(t){var e=d.Deferred(),i=this,n={};return 1<arguments.length?(n={},h.each(arguments,function(t){if(t)try{n[t]=i._getPlugin(t)}catch(e){throw console.error("Error loading: "+t),h.each(n,function(e,t){i.returnPlugin(t),delete n[t]}),e}})):t&&(n=this._getPlugin(t)),setTimeout(function(){e.done(function(e){h.isArray(t)?h.each(t,function(e){i.returnPlugin(e)}):i.returnPlugin(t)})},1),e.resolve(n),e.promise()},getPluginRefCount:function(e){return this.getWidgetRefCount(e,"plugin:")},_getPlugin:function(e){return this._getThing("plugin",e)},returnPlugin:function(e){var t=this.__barbarianInstances["plugin:"+e];return t?(t.counter--,this._killBarbarian("plugin:"+e),t.counter):-1},getPluginOrWidgetName:function(e){if(!h.isString(e))throw Error("The psk argument must be a string");if(this.__barbarianRegistry[e])return this.__barbarianRegistry[e]},getPluginOrWidgetByPubSubKey:function(e){var t=this.getPluginOrWidgetName(e);if(void 0!==t){if(this._isBarbarianAlive(t))return this._getBarbarian(t);throw new Error("Eeeek, thisis unexpectEED bEhAvjor! Cant find barbarian with ID: "+e)}},getPskOfPluginOrWidget:function(e){e.split(":");if(this._isBarbarianAlive(e)){e=this._getBarbarian(e);if(e.getPubSub&&e.getPubSub().getCurrentPubSubKey)return e.getPubSub().getCurrentPubSubKey().getId()}},_getOrCreateBarbarian:function(e,t){var i,n,r=e+":"+t;if("plugin"===e&&!this.hasPlugin(t)||"widget"===e&&!this.hasWidget(t))throw new Error("We cannot give you "+r+" (cause there is no constructor for it)");return this._isBarbarianAlive(r)?this._getBarbarian(r):(e=new(("plugin"===e?this.__plugins:this.__widgets).get(t)),t=this.getBeeHive().getHardenedInstance(),n=this.getService("PubSub"),h.keys(n._issuedKeys),"activate"in e&&(this.debug&&console.log("application: "+r+".activate(beehive)"),i=e.activate(t)),n=h.without(h.keys(n._issuedKeys),h.keys(n._issuedKeys)),this._registerBarbarian(r,e,i,t,n),e)},_lazyLoadIfNecessary:function(e,t){var i,n=d.Deferred(),r=this;if("plugin"===e)i=r.__plugins;else{if("widget"!==e)throw new Error(e+" cannot be lazy loaded, sorry");i=r.__widgets}e=i.get(t);return null==e?n.reject(t+" does not exist"):e&&e.lazyLoad?e().done(function(e,t){r._registerLoadedModules(e,t),n.resolve()}):n.resolve(),n.promise()},_isBarbarianAlive:function(e){return!!this.__barbarianInstances[e]},_getBarbarian:function(e){return this.__barbarianInstances[e].parent},_registerBarbarian:function(e,t,i,n,r){this._killBarbarian(e),"getBeeHive"in t?this.__barbarianRegistry[t.getBeeHive().getService("PubSub").getCurrentPubSubKey().getId()]=e:this.__barbarianRegistry[n.getService("PubSub").getCurrentPubSubKey().getId()]=e;var o=[];i&&(o=this._registerBarbarianChildren(e,i)),r&&h.each(r,function(e){this.__barbarianRegistry[e]&&delete r[e]},this),this.__barbarianInstances[e]={parent:t,children:o,beehive:n,counter:0,psk:n.getService("PubSub").getCurrentPubSubKey(),bastards:r}},_registerBarbarianChildren:function(i,e){var n=[];return h.each(e,function(e,t){t=i+"-"+(e.name||t);if(this.debug&&console.log("adding child object to registry: "+t),this._isBarbarianAlive(t))throw new Error("Contract breach, there already exists instance with a name: "+t);if("getBeeHive"in e){e=e.getBeeHive().getService("PubSub").getCurrentPubSubKey().getId();if(this.__barbarianRegistry[e])throw new Error("Contract breach, the child of "+i+"is using the same pub-sub-key");this.__barbarianRegistry[e]=t}n.unshift(t)},this),n},_killBarbarian:function(i,e){var t=this.__barbarianInstances[i];!t||0<t.counter&&!0!==e||(t.children&&h.each(t.children,function(e){this._killBarbarian(e,!0)},this),h.each(this.__barbarianRegistry,function(e,t){e===i&&delete this.__barbarianRegistry[t]},this),e=this.getService("PubSub"),t.psk&&e.unsubscribe(t.psk),t.bastards,t.parent.destroy(),delete this.__barbarianInstances[i],"setBeeHive"in t.parent&&t.parent.setBeeHive({fake:"one"}),t=null,this.debug&&console.log("Destroyed: "+i))},getAllControllers:function(){return h.pairs(this.__controllers.container)},getAllModules:function(){return h.pairs(this.__modules.container)},getAllPlugins:function(i){i=i||"plugin:";var n=d.Deferred(),r=[];return h.each(this.__barbarianInstances,function(e,t){-1<t.indexOf(i)&&r.unshift(t.replace(i,""))}),(-1<i.indexOf("plugin:")?this.getPlugin:this.getWidget).apply(this,r).done(function(e){var t=[];1<r.length?t=h.pairs(e):1===r.length&&(t=[[r[0],e]]),n.resolve(t)}),n.promise()},getAllWidgets:function(){return this.getAllPlugins("widget:")},getAllServices:function(){return this.getBeeHive().getAllServices()},getAllObjects:function(){return this.getBeeHive().getAllObjects()},triggerMethodOnAll:function(t,i){this.triggerMethod(this.getAllControllers(),"controllers",t,i),this.triggerMethod(this.getAllModules(),"modules",t,i);var n=this;this.getAllPlugins().done(function(e){e.length&&n.triggerMethod(e,"plugins",t,i)}),this.getAllWidgets().done(function(e){e.length&&n.triggerMethod(e,"widgets",t,i)}),this.triggerMethod(this.getBeeHive().getAllServices(),"BeeHive:services",t,i),this.triggerMethod(this.getBeeHive().getAllObjects(),"BeeHive:objects",t,i)},triggerMethod:function(e,i,n,r){var o=this;return h.map(e,function(e){var t=e[1];n in t?(o.debug&&console.log("application.triggerMethod: "+i+": "+e[0]+"."+n+"()"),t[n].call(t,r)):h.isFunction(n)&&(o.debug&&console.log("application.triggerMethod: "+i+": "+e[0]+" customCallback()"),n.call(t,i+":"+e[0],r))})}}),r.extend=e.Model.extend,r.extend(n)});
//# sourceMappingURL=application.js.map