(window.webpackJsonp=window.webpackJsonp||[]).push([[41],{399:function(n,e){window.requestIdleCallback=window.requestIdleCallback||function(n){var e=Date.now();return setTimeout((function(){n({didTimeout:!1,timeRemaining:function timeRemaining(){return Math.max(0,50-(Date.now()-e))}})}),1)},window.cancelIdleCallback=window.cancelIdleCallback||function(n){clearTimeout(n)}}}]);
//# sourceMappingURL=requestidlecallback-polyfill-0da7751fc8fe950704c2.js.map