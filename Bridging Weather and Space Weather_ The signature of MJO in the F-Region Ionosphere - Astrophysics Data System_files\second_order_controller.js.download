define(["js/components/generic_module","js/mixins/dependon","js/components/api_query","js/components/api_request","js/components/api_targets","analytics"],function(e,t,i,n,s,r){var o=e.extend({initialize:function(e){this.options=_.defaults({},e,{maxQueryRows:6e3,transformDebounce:1e3}),this.options.transformDebounce&&(this.transform=_.debounce(this.transform,this.options.transformDebounce))},activate:function(e){this.setBeeHive(e.getHardenedInstance());e=this.getBeeHive().getService("PubSub");e.subscribe(e.CUSTOM_EVENT,_.bind(this.onCustomEvent,this))},onCustomEvent:function(e){e.startsWith("second-order-search/")&&this.transform.apply(this,[e.split("/")[1]].concat(_.rest(arguments)))},getSelectedIds:function(){var e=this.getBeeHive().getObject("AppStorage");return e&&e.getSelectedPapers&&e.getSelectedPapers()||[]},getCurrentQuery:function(){var e=this.getBeeHive().getObject("AppStorage");if(e&&e.getCurrentQuery&&e.getCurrentQuery()instanceof i)return e.getCurrentQuery()},getBigQueryResponse:function(e){var t=this.getPubSub(),r=$.Deferred(),e=new i({bigquery:"bibcode\n".concat(e.join("\n")),q:"*:*",fq:"{!bitset}",sort:"date desc"}),e=new n({target:s.MYADS_STORAGE+"/query",query:e,options:{type:"POST",done:function(e){e=e.qid;return r.resolve(e)},fail:function(e){return r.reject(e)}}});return t.publish(t.EXECUTE_REQUEST,e),r.promise()},sendQuery:function(e){var t=this.getPubSub(),r=$.Deferred(),e=new n({target:s.SEARCH,query:e,options:{type:"GET",done:function(e){return r.resolve(e)},fail:function(e){return r.reject(e)}}});return t.publish(t.EXECUTE_REQUEST,e),r.promise()},validField:function(e){return _.contains(_.values(o.FIELDS),e)},submitAnalyticsEvent:function(e){r("send","event","interaction","second-order-operation",e)},transform:function(e,t){if(!e||!this.validField(e))throw"must pass in a valid field";var t=_.defaults({},t,{onlySelected:!1,libraryId:null,ids:[],query:null}),r=0<t.ids.length?t.ids:this.getSelectedIds();0!==r.length&&t.onlySelected||e===o.FIELDS.LIMIT||e===o.FIELDS.LIBRARY||e===o.FIELDS.EXCLUDE?e===o.FIELDS.LIBRARY?this.startSearch(e,t.libraryId):this.getQidAndStartSearch(e,r):this.transformCurrentQuery(e,t.query)},handleError:function(e){var t="Error occurred",e=(e.responseJSON&&e.responseJSON.error&&(t=e.responseJSON.error),this.getPubSub());throw e.publish(e.CUSTOM_EVENT,"second-order-search/error",{message:t}),t},transformCurrentQuery:function(e,t){var r,n,s=this.getPubSub(),t=t instanceof i?t:this.getCurrentQuery();t&&(r=t.clone(),(n=[]).push(r.get("q")),_.forEach(Object.keys(r.toJSON()),function(e){e.startsWith("fq_")&&n.push(r.get(e))}),t=new i({q:"".concat(e,"(").concat(n.join(" AND "),")"),sort:"score desc"}),s.publish(s.NAVIGATE,"search-page",{q:t}))},getQidAndStartSearch:function(t,e){var r=this;this.getBigQueryResponse(e).then(function(e){r.startSearch(t,e)}).fail(function(){return r.handleError.apply(r,arguments)})},startSearch:function(e,t){if(!t)throw"no id";e===o.FIELDS.LIMIT?(r=this.getCurrentQuery()||new i,r=new i({q:"docs(".concat(t,")"),sort:r.get("sort")||"score desc"})):e===o.FIELDS.LIBRARY?r=new i({q:"docs(library/".concat(t,")"),sort:"date desc"}):e===o.FIELDS.EXCLUDE?(r=(n=this.getCurrentQuery()||new i).clone()).set({q:"-docs(".concat(t,") ").concat(n.get("q"))}):r=new i({q:"".concat(e,"(docs(").concat(t,"))"),sort:"score desc"});var r,n=this.getPubSub();n.publish(n.NAVIGATE,"search-page",{q:r}),this.submitAnalyticsEvent(e)}});return o.FIELDS={USEFUL:"useful",SIMILAR:"similar",TRENDING:"trending",REVIEWS:"reviews",LIMIT:"limit",LIBRARY:"library",EXCLUDE:"exclude"},_.extend(o.prototype,t.BeeHive),o});
//# sourceMappingURL=second_order_controller.js.map