(()=>{let e={LANDING:"landing-page",SEARCH:"search-page",ABSTRACT:"abstract-page"},a={slow:30,default:7},t=()=>{if("connection"in navigator){var e=navigator.connection.effectiveType;if("slow-2g"===e||"2g"===e||"3g"===e)return a.slow}return a.default},o=window.location["/"===window.location.pathname?"hash":"pathname"].replace(/#/g,""),n=e=>console.debug("[DEBUG] "+e),i=e=>{var a=document.getElementById("app-error-container"),o=document.getElementById("app-error-message");a.classList.remove("hidden"),o.innerHTML=e},r=()=>new Promise((e,a)=>{n("Loading discovery.config"),require({waitSeconds:t()},["config/discovery.config"],function(){n("Loaded discovery.config successfully"),e()},function(e){n("Failed to load discovery.config"),a(e)})}),d=()=>new Promise((e,a)=>{n("Loading main.config"),require({waitSeconds:t()},["config/main.config"],function(){n("Loaded main.config successfully"),e()},function(e){n("Failed to load main.config"),a(e)})}),s=o=>new Promise((e,a)=>{n(`Loading ${o}.config`),require({waitSeconds:t()},[`config/${o}.config`],function(){n(`Loaded ${o}.config successfully`),e()},function(e){n(`Failed to load ${o}.config`),a(e)})}),c=()=>o.startsWith("/abs")?s(e.ABSTRACT):o.startsWith("/search")?s(e.SEARCH):"/"===o?s(e.LANDING):d(),l=a=>{var o=document.cookie.split("; ");for(let e=0;e<o.length;e+=1){var[t,n]=o[e].split("=");if(t===a)return decodeURIComponent(n)}return null},u=e=>{document.cookie=e+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax"},p=async()=>{if(navigator.onLine){let a=(()=>{try{return parseInt(l("app_load_attempts"),10)||1}catch(e){return n("Failed to parse app_load_attempts cookie: "+e),1}})();if(3===a)n("Failed to load config after 3 attempts"),i(`
        <p>Failed to load.</p>
        <p>Maybe your network or our server is down? Please try again later.</p>
      `),u("app_load_attempts");else{var o;for(o of[c,d,r])try{return await o(),void u("app_load_attempts")}catch(e){n(`Attempt ${a}: ${o.name} failed`,e)}i(`Failed to load. Refreshing... (attempt ${a}/3)`),setTimeout(()=>{((e,a,o)=>{o=new Date(Date.now()+60*o*1e3).toUTCString();document.cookie=`${e}=${encodeURIComponent(a)}; expires=${o}; path=/; SameSite=Lax`})("app_load_attempts",a+1,5),window.location.reload()},5e3)}}else i(`
        <p>It seems you are offline.</p>
        <p>Please check your network connection and try again.</p>
      `),window.addEventListener("online",()=>p(),{once:!0})},f=e=>{window.requirejs?(n("RequireJS is ready, configuring..."),window.requirejs.config({urlArgs:"v=v1.10.1"}),"function"==typeof e&&e()):setTimeout(f,10)};f(p)})(),(()=>{var e;/^localhost$/.exec(window.location.hostname)||/^(ui|qa|dev|devui|demo)\.adsabs\.harvard\.edu$/.test(window.location.hostname)||([e]=document.getElementsByTagName("body"),e.classList.add("is-proxied"))})(),window.getCanonicalUrl=()=>{let a=/^https:\/\/(ui|qa|dev|devui|demo)\.adsabs\.harvard\.edu$/;var[e]=[{env:"ui",url:"https://ui.adsabs.harvard.edu"},{env:"qa",url:"https://qa.adsabs.harvard.edu"},{env:"dev",url:"https://dev.adsabs.harvard.edu"},{env:"devui",url:"https://devui.adsabs.harvard.edu"},{env:"demo",url:"https://demo.adsabs.harvard.edu"}].filter(({url:e})=>!a.test(e));return void 0!==e?"https://"+[e.env,"adsabs","harvard","edu"].join("."):"https://"+["ui","adsabs","harvard","edu"].join(".")};
//# sourceMappingURL=init.js.map