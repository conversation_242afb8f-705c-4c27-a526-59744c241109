define(["underscore","bootstrap","jquery","backbone","js/components/generic_module","js/mixins/dependon","js/components/pubsub_events","js/mixins/link_generator_mixin","js/components/api_query","js/components/api_request","js/mixins/hardened","js/components/api_targets","js/components/api_query_updater","js/components/api_feedback","js/modules/orcid/work","js/modules/orcid/profile","js/modules/orcid/bio"],function(l,e,p,t,r,i,n,o,s,a,c,d,u,h,f,g,m){r=r.extend({initialize:function(){this.userData={},this.addCache=[],this.deleteCache=[],this.getUserProfileCache=[],this.authData=null,this.addWait=3e3,this.deleteWait=100,this.profileWait=100,this.maxAddChunkSize=100,this.maxDeleteChunkSize=10,this.db={},this.clearDBWait=3e4,this.dbUpdatePromise=null,this.maxQuerySize=100,this.queryUpdater=new u("orcid_api"),this.orcidApiTimeout=3e4,this.adsQueryTimeout=10,this.dirty=!0},activate:function(e){var t=e.getService("PersistentStorage"),r=e.getObject("DynamicConfig");this.setBeeHive(e),this.config={},l.extend(this.config,r.Orcid),t&&(e=t.get("Orcid"))&&e.authData&&this.saveAccessData(e.authData),this._addWork=l.debounce(this._addWork,this.addWait),this._getUserProfile=l.debounce(this._getUserProfile,this.profileWait),this._deleteWork=l.debounce(this._deleteWork,this.deleteWait)},checkAccessOrcidApiAccess:function(){return this.hasAccess()?this.getUserProfile():p.Deferred().reject().promise()},hasAccess:function(){return!(!this.authData||!this.authData.expires)&&this.authData.expires>(new Date).getTime()},signIn:function(e){this.getPubSub().publish(this.getPubSub().APP_EXIT,{type:"orcid",url:this.config.loginUrl+"&redirect_uri="+encodeURIComponent(this.config.redirectUrlBase+(e||"/user/orcid"))}),this.getPubSub().publish(this.getPubSub().ORCID_ANNOUNCEMENT,"login")},setADSUserData:function(e){var t=this.getBeeHive().getService("Api").url+d.ORCID_PREFERENCES+"/"+this.authData.orcid,t=this.createRequest(t,{method:"POST"},e);return t.fail(function(){console.error.apply(console,["ADS ORCiD preferences could not be set"].concat(arguments))}),t},getADSUserData:function(){var t=this,e=this.getBeeHive().getService("Api").url+d.ORCID_PREFERENCES+"/"+this.authData.orcid,e=this.createRequest(e);return e.fail(function(){t.signOut(),t.getBeeHive().getObject("User").setOrcidMode(0);var e=t.getPubSub();e.publish(e.ALERT,new h({title:"Expired ORCID login",msg:["Your ORCID login has expired.","Please reload the page and sign in again to access the page.","",'<button onclick="location.reload()" class="btn btn-primary" role="button">Reload</button>'].join("<br/>"),modal:!0,type:"warning"}))}),e},getUserBio:function(){var t=new p.Deferred,e=this.getBeeHive().getService("Api").url+d.ORCID_NAME+"/"+this.authData.orcid,e=this.createRequest(e);return e.fail(function(){console.error.apply(console,["ADS name could not be retrieved"].concat(arguments)),t.reject()}),e.done(function(e){e=new m(e);t.resolve(e)}),t.promise()},signOut:function(){this.saveAccessData(null),this.getPubSub().publish(this.getPubSub().ORCID_ANNOUNCEMENT,"logout")},hasExchangeCode:function(e){return!!this.getExchangeCode(e)},getExchangeCode:function(e){return this.getUrlParameter("code",e||window.location.search)},getUrlParameter:function(e,t){for(var r=t.substring(1).split("&"),i=0;i<r.length;i++){var n=r[i].split("=");if(n[0]===e)return decodeURIComponent(n[1])}},getAccessData:function(e){var t=this.getBeeHive().getService("Api"),r=p.Deferred(),i={url:this.config.exchangeTokenUrl,done:l.bind(r.resolve,r),fail:l.bind(r.reject,r),always:l.bind(r.always,r),headers:{Accept:"application/json",Authorization:t.access_token}};return t.request(new a({target:this.config.exchangeTokenUrl,query:new s({code:e})}),i),r.promise()},saveAccessData:function(e){var t,r=this.getBeeHive(),r=(e&&!e.expires&&e.expires_in&&(e.expires=(new Date).getTime()+(1e3*e.expires_in-1e3)),this.authData=e,r.getService("PersistentStorage"));r&&((t=r.get("Orcid")||{}).authData=e,r.set("Orcid",t))},getUrl:function(e,t){e=this.config.apiEndpoint+"/"+this.authData.orcid+{profile_bib:"/orcid-profile/simple",profile_full:"/orcid-profile/full?update=True",works:"/orcid-works",work:"/orcid-work"}[e],t=l.isArray(t)?t.join(","):t;return t&&(e+="/"+t),e},createRequest:function(e,t,r){if(l.isUndefined(e))throw new Error("Url must be defined");var i=p.Deferred(),e=this.sendData(e,r,t||{});return e.done(l.bind(i.resolve,i)),e.fail(l.bind(i.reject,i)),e.always(l.bind(i.always,i)),i.promise()},_getUserProfile:function(){var e=this.createRequest(this.getUrl("profile_full")),r=this.getUserProfileCache.splice(0);e.done(function(t){l.forEach(r,function(e){orcidProfile=new g(t),e.resolve(orcidProfile.setWorks(l.map(t,function(e,t){return new f(e)})))})}),e.fail(function(){var t=arguments;l.forEach(r,function(e){e.reject.apply(e,t)})})},getUserProfile:function(){var e=p.Deferred();return this.getUserProfileCache.push(e),this._getUserProfile.call(this),e.promise()},getWork:function(e){var t=p.Deferred();return this.createRequest(this.getUrl("works",e)).done(function(e){t.resolve(new f(e))}).fail(l.bind(t.reject,t)),t.promise()},getWorks:function(e){if(!l.isArray(e))throw new TypeError("putcodes must be an Array");for(var t=p.Deferred(),r=[],i=0,n=e.length;i<n;i+=50){var o=e.slice(i,i+50),o=this.getUrl("works")+"/"+o.join(",");r.push(l.partial(this.createRequest,o))}var s=p.when.apply(p,r);return s.done(l.bind(t.resolve,t)),s.fail(function(){t.reject(arguments)}),t.promise()},updateWork:function(e){var t;if(l.isPlainObject(e))return(t=e["put-code"])?(t=this.getUrl("works",t),this.createRequest(t,{method:"PUT"},e)):p.Deferred().reject().promise();throw new TypeError("Work should be a simple object")},deleteWork:function(e){var t;return(e?(t=p.Deferred(),this.deleteCache.push({id:l.uniqueId(),putCode:e,promise:t}),this._deleteWork.call(this),t):p.Deferred().reject("No Putcode found")).promise()},_deleteWork:function(){for(var e,i=this,t=this.deleteCache.slice(0),n=[],r=[],o=0;o<t.length;o+=this.maxDeleteChunkSize)e=t.slice(o,o+this.maxDeleteChunkSize),r.push(e);l.forEach(r,function(e,r){l.forEach(e,function(t){n.push(t.promise.promise()),l.delay(function(){i.createRequest(i.getUrl("works",t.putCode),{beforeSend:function(e){e._id=t.id},method:"DELETE"}).done(l.bind(t.promise.resolve,t.promise)).fail(l.bind(t.promise.reject,t.promise)).always(l.bind(t.promise.always,t.promise));var e=i.deleteCache.indexOf(t);i.deleteCache.splice(e,e+1)},i.deleteWait*r)})});p.when.apply(p,n).always(function(){i.deleteCache=l.reduce(i.deleteCache,function(e,t){return"pending"===t.promise.state()?t.promise.reject():e.push(t),e},[])})},addWork:function(e){var t;if(l.isPlainObject(e))return t=p.Deferred(),this.addCache.push({id:l.uniqueId(),work:e,promise:t}),this._addWork.call(this),t.promise();throw new TypeError("Should be plain object")},_addWork:function(){var n=this,e=l.map(n.addCache,"work"),t=l.map(n.addCache,"id"),e=n._addWorks(e,t);e.done(function(e){l.forEach(e,function(e,t){var r=l.find(n.addCache,function(e){return e.id===t});if(!r)return console.error("No Cache entry found"),!0;var i=r.promise,e=(e?e.error?409===e.error["response-code"]?i.resolve(r.work):i.reject():i.resolve(new f(e.work)):i.reject(),n.addCache.indexOf(r));n.addCache.splice(e,e+1)})}),e.fail(function(e){var r=arguments;l.forEach(e,function(e){var t,e=l.findIndex(n.addCache,{id:e});0<=e&&(t=n.addCache[e].promise,n.addCache.splice(e,e+1),"pending"===t.state())&&t.reject.apply(t,r)})})},_addWorks:function(e,t){var r=this;if(!l.isArray(e)||!l.isArray(t))throw new TypeError("works and ids must be arrays");for(var i=p.Deferred(),n=[],o=0;o<e.length;o+=this.maxAddChunkSize){var s=e.slice(o,o+this.maxAddChunkSize),a=t.slice(o,o+this.maxAddChunkSize),c={bulk:[]},s=(l.each(s,function(e){c.bulk.push({work:e})}),this.getUrl("works"));n.push(this.createRequest(s,{beforeSend:function(e){e.cacheIds=a},method:"POST"},c))}return p.when.apply(p,n).then(function(){var e=l.isArray(arguments[0])?arguments:[arguments],e=l.reduce(e,function(r,e){var i=e&&e[0]&&e[0].bulk,e=e&&e[2]&&e[2].cacheIds;return l.forEach(e,function(e,t){r[e]=i[t]}),r},{});i.resolve(e)},function(e){r.setDirty(),i.reject.apply(i,[e.cacheIds].concat(arguments))}),i.promise()},sendData:function(e,t,r){var i=p.Deferred(),n=(r=r||{},{type:"GET",url:e,contentType:"application/json",cache:!!this.dbUpdatePromise,timeout:this.orcidApiTimeout,done:l.bind(i.resolve,i),fail:l.bind(i.reject,i),always:l.bind(i.always,i)}),t=(t?(n.dataType="json",n.data=JSON.stringify(t),n.converters={"* text":window.String,"text html":!0,"text json":function(e){return p.parseJSON(e=e||"{}")},"text xml":p.parseXML}):n.data=null,l.extend(n,r),this.getBeeHive().getService("Api"));return n.headers||(n.headers={}),n.headers.Authorization=t.access_token,!n.headers["Orcid-Authorization"]&&this.authData&&(n.headers["Orcid-Authorization"]="Bearer "+this.authData.access_token),n.headers["Content-Type"]||(n.headers["Content-Type"]="application/json"),n.headers.Accept||(n.headers.Accept="application/json"),t.request(new a({target:e,query:new s,options:n})),i.promise()},_checkIdsInADS:function(e){function t(){i.resolve({})}var r=this.getBeeHive().getService("Api"),i=p.Deferred();e.set("fl","bibcode, doi, alternate_bibcode"),e.set("rows","5000");return r.request(new a({target:d.SEARCH,query:e,options:{done:function(e){if(!e||!e.response||!e.response.docs)return i.resolve({});e=l.reduce(e.response.docs,function(t,e){var r=e.bibcode.toLowerCase();return t["identifier:"+r]=r,l.each(e.doi,function(e){e="identifier:"+e.toLowerCase().replace("doi:","");t[e]=r}),l.each(e.alternate_bibcode,function(e){e="identifier:"+e.toLowerCase();t[e]=r}),t},{});i.resolve(e)},fail:t}})).fail(t),function e(t){return"pending"===i.state()&&t<=0?i.reject("Request Timeout"):void("resolved"!==i.state()&&l.delay(e,1e3,--t))}(this.adsQueryTimeout),i.promise()},isSourcedByADS:function(e){return-1<e.getSourceName().indexOf("NASA Astrophysics Data System")},_buildQuery:function(e){return l.isEmpty(e)||!e.identifier||(e=l.filter(e.identifier,function(e){return!l.isEmpty(e.trim())||"NONE"===e}).join(" OR "),l.isEmpty(e))?null:new s({q:"identifier:("+e+")"})},updateDatabase:function(e){var u=this;if(this.dbUpdatePromise&&"pending"===this.dbUpdatePromise.state())return this.dbUpdatePromise.promise();this.dbUpdatePromise=p.Deferred();function t(e){var e=e.getWorks(),i=[],n={};if(l.forEach(e,function(e,t){var r="identifier:";(r+=e.getIdentifier())&&(i.push(r),n[r.toLowerCase()]={sourcedByADS:u.isSourcedByADS(e),putcode:e.getPutCode(),idx:t})}),i.length&&0<u.maxQuerySize)for(var t=[],i=i.sort(),r=l.range(0,i.length,u.maxQuerySize),o=0;o<r.length;o++){for(var s={},a=r[o];a<r[o]+u.maxQuerySize&&!(a>=i.length);a++){var c=i[a].split(":");s[c[0]]||(s[c[0]]=[]),s[c[0]].push(u.queryUpdater.quoteIfNecessary(c[1]))}var d=u._buildQuery(s);d&&t.push(u._checkIdsInADS(d))}else h(n);p.when.apply(p,t).then(function(i){l.each(n,function(e,t){var r=i[t];r?n[t].bibcode=r:n[t].idx=-1}),h(n)},function(){console.error.apply(console,["Error processing response from ADS"].concat(arguments)),h(n)})}var h=function(e){var t=u.dbUpdatePromise;u.setClean(),u.db=e,t&&t.resolve(),setTimeout(function(){t&&"pending"!==t.state()&&(t=null,u.setDirty())},u.clearDBWait)};return e?t(e):this.getUserProfile().done(t).fail(function(){u.dbUpdatePromise.reject.apply(u.dbUpdatePromise,arguments)}),u.dbUpdatePromise.promise()},getRecordInfo:function(e){function t(e){function r(e,t){e=("identifier:"+e).toLowerCase(),(e=n.db[e])&&(e.sourcedByADS?i.isCreatedByADS=!0:i.isCreatedByOthers=!0,-1<e.idx&&(i.isKnownToADS=!0),i=l.extend({},i,e))}var i={isCreatedByADS:!1,isCreatedByOthers:!1,isKnownToADS:!1,provenance:null},e=l.pick(e,"identifier","bibcode","doi","alternate_bibcode");return l.each(e,function(e,t){l.isArray(e)?l.each(e,r):r(e)}),i}var n=this,r=p.Deferred();return this.needsUpdate()?this.updateDatabase().done(function(){r.resolve(t(e))}).fail(l.bind(r.reject,r)):r.resolve(t(e)),r.promise()},needsUpdate:function(){return this.dirty},setDirty:function(){this.dirty=!0},setClean:function(){this.dirty=!1},hardenedInterface:{hasAccess:"boolean indicating access to ORCID Api",getUserProfile:"get user profile",getUserBio:"get user bio",signIn:"login",signOut:"logout",getADSUserData:"",setADSUserData:"",getRecordInfo:"provides info about a document",addWork:"add a new orcid work",deleteWork:"remove an entry from orcid",updateWork:"update an orcid work",getWork:"get an orcid work",getWorks:"get an array of orcid works"}});return l.extend(r.prototype,i.BeeHive),l.extend(r.prototype,c),r});
//# sourceMappingURL=orcid_api.js.map