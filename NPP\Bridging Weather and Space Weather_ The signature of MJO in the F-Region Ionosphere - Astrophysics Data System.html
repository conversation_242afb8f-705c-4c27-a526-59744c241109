<!DOCTYPE html>
<!-- saved from url=(0062)https://ui.adsabs.harvard.edu/abs/2024AGUFMSA13C..06A/abstract -->
<html class="js" lang="en"><!--<![endif]--><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>Bridging Weather and Space Weather: The signature of MJO in the F-Region Ionosphere - Astrophysics Data System</title>

  <!-- favicon -->
  <link rel="apple-touch-icon" sizes="180x180" href="https://ui.adsabs.harvard.edu/styles/favicon/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="https://ui.adsabs.harvard.edu/styles/favicon/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="https://ui.adsabs.harvard.edu/styles/favicon/favicon-16x16.png">
  <link rel="manifest" href="https://ui.adsabs.harvard.edu/styles/favicon/site.webmanifest">
  <link rel="mask-icon" href="https://ui.adsabs.harvard.edu/styles/favicon/safari-pinned-tab.svg" color="#5bbad5">
  <meta name="apple-mobile-web-app-title" content="ADS">
  <meta name="application-name" content="ADS">
  <meta name="msapplication-TileColor" content="#ffc40d">
  <meta name="theme-color" content="#ffffff">
  <!-- /favicon -->
  <!-- Google Tag Manager -->
  <script async="" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/gtm.js.download"></script><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-NT2453N');</script>
  <!-- End Google Tag Manager -->

  <link rel="stylesheet" href="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/styles.css">
  <meta name="robots" content="noarchive">

  <link rel="canonical" href="http://ui.adsabs.harvard.edu/abs/2024AGUFMSA13C..06A/abstract">
  <meta name="description" content="The Madden-Julian Oscillation (MJO) is a significant atmospheric phenomenon known to influence weather patterns and tropical weather systems extensively. This study investigates how the MJO affects space weather in the F-region ionosphere, bridging weather and space weather research. Using data from the COSMIC-2 GIS (Global Ionospheric Specification), we explore how atmospheric tides map the MJO&#39;s effects into the ionosphere. We established a robust connection between the MJO in the Mesosphere Lower Thermosphere (MLT) region (using SABER temperature data) and the ionosphere (using COSMIC-2 GIS electron density data). It suggests the E-region dynamo is likely the coupling mechanism. For instance, there is approximately 20% MJO modulation in the MLT region and the Equatorial Ionization Anomaly (EIA) electron density in the ionosphere for Diurnal Eastward propagating tide (DE3). For statistical analysis and to understand the role of the MJO phase, we utilized the SD-WACCM-X model output for both neutral temperature and electron density over 22 years (2002-2022). We found a strong phase alignment between the two regions. The predictability of MJO phases using the RMM index can be helpful in the forecasting capability of the ionospheric state and will be beneficial for space weather predictions.">
  <!-- Open Graph -->
  <meta property="og:type" content="abstract">
  <meta property="og:title" content="Bridging Weather and Space Weather: The signature of MJO in the F-Region Ionosphere">
  <meta property="og:site_name" content="ADS">
  <meta property="og:description" content="The Madden-Julian Oscillation (MJO) is a significant atmospheric phenomenon known to influence weather patterns and tropical weather systems extensively. This study investigates how the MJO affects space weather in the F-region ionosphere, bridging weather and space weather research. Using data from the COSMIC-2 GIS (Global Ionospheric Specification), we explore how atmospheric tides map the MJO&#39;s effects into the ionosphere. We established a robust connection between the MJO in the Mesosphere Lower Thermosphere (MLT) region (using SABER temperature data) and the ionosphere (using COSMIC-2 GIS electron density data). It suggests the E-region dynamo is likely the coupling mechanism. For instance, there is approximately 20% MJO modulation in the MLT region and the Equatorial Ionization Anomaly (EIA) electron density in the ionosphere for Diurnal Eastward propagating tide (DE3). For statistical analysis and to understand the role of the MJO phase, we utilized the SD-WACCM-X model output for both neutral temperature and electron density over 22 years (2002-2022). We found a strong phase alignment between the two regions. The predictability of MJO phases using the RMM index can be helpful in the forecasting capability of the ionospheric state and will be beneficial for space weather predictions.">
  <meta property="og:url" content="https://ui.adsabs.harvard.edu/abs/2024AGUFMSA13C..06A/abstract">
  <meta property="og:image" content="https://ui.adsabs.harvard.edu/styles/img/transparent_logo.svg">
  <meta property="article:published_time" content="12/2024">
  <meta property="article:author" content="Aggarwal, Deepali">
  <meta property="article:author" content="Oberheide, Jens">
  <meta property="article:author" content="Kumari, Komal">
  <!-- citation_* -->
  <meta name="citation_journal_title" content="AGU Fall Meeting Abstracts">
  <meta name="citation_authors" content="Aggarwal, Deepali;Oberheide, Jens;Kumari, Komal">
  <meta name="citation_title" content="Bridging Weather and Space Weather: The signature of MJO in the F-Region Ionosphere">
  <meta name="citation_date" content="12/2024">
  <meta name="citation_volume" content="2024">
  <meta name="citation_firstpage" content="SA13C-06">
  <meta name="citation_language" content="en">
  <meta name="citation_abstract_html_url" content="https://ui.adsabs.harvard.edu/abs/2024AGUFMSA13C..06A/abstract">
  <meta name="citation_publication_date" content="12/2024">
  <link title="schema(PRISM)" rel="schema.prism" href="http://prismstandard.org/namespaces/1.2/basic/">
  <meta name="prism.publicationDate" content="12/2024">
  <meta name="prism.publicationName" content="AGUFM">
  <meta name="prism.volume" content="2024">
  <meta name="prism.startingPage" content="SA13C-06">
  <link title="schema(DC)" rel="schema.dc" href="http://purl.org/dc/elements/1.1/">
  <meta name="dc.date" content="12/2024">
  <meta name="dc.source" content="AGUFM">
  <meta name="dc.title" content="Bridging Weather and Space Weather: The signature of MJO in the F-Region Ionosphere">
  <meta name="dc.creator" content="Aggarwal, Deepali">
  <meta name="dc.creator" content="Oberheide, Jens">
  <meta name="dc.creator" content="Kumari, Komal">
  <!-- twitter card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:description" content="The Madden-Julian Oscillation (MJO) is a significant atmospheric phenomenon known to influence weather patterns and tropical weather systems extensively. This study investigates how the MJO affects space weather in the F-region ionosphere, bridging weather and space weather research. Using data from the COSMIC-2 GIS (Global Ionospheric Specification), we explore how atmospheric tides map the MJO&#39;s effects into the ionosphere. We established a robust connection between the MJO in the Mesosphere Lower Thermosphere (MLT) region (using SABER temperature data) and the ionosphere (using COSMIC-2 GIS electron density data). It suggests the E-region dynamo is likely the coupling mechanism. For instance, there is approximately 20% MJO modulation in the MLT region and the Equatorial Ionization Anomaly (EIA) electron density in the ionosphere for Diurnal Eastward propagating tide (DE3). For statistical analysis and to understand the role of the MJO phase, we utilized the SD-WACCM-X model output for both neutral temperature and electron density over 22 years (2002-2022). We found a strong phase alignment between the two regions. The predictability of MJO phases using the RMM index can be helpful in the forecasting capability of the ionospheric state and will be beneficial for space weather predictions.">
  <meta name="twitter:title" content="Bridging Weather and Space Weather: The signature of MJO in the F-Region Ionosphere">
  <meta name="twitter:site" content="@adsabs">
  <meta name="twitter:domain" content="ADS">
  <meta name="twitter:image:src" content="https://ui.adsabs.harvard.edu/styles/img/transparent_logo.svg">
  <meta name="twitter:creator" content="@adsabs">
  
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="config/abstract-page.config" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/abstract-page.config.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="config/abstract-page.bundle" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/abstract-page.bundle.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="config/discovery.vars" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/discovery.vars.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="config/utils" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/utils.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="regenerator-runtime" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/runtime.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="array-flat-polyfill" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/array-flat-polyfill.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="polyfill" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/polyfill.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/dark-mode-switch" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/dark-mode-switch.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="jquery" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/jquery.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="router" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/router.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/application" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/application.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/mixins/discovery_bootstrap" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/discovery_bootstrap.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/mixins/api_access" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/api_access.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="underscore" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/lodash.compat.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/beehive" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/beehive.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/services_container" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/services_container.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="d3" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/d3.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="backbone" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/backbone-min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="backbone-validation" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/backbone-validation-amd-min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="d3-cloud" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/d3.layout.cloud.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/query_mediator" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/query_mediator.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/bugutils/diagnostics" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/diagnostics.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/modules/orcid/module" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/module.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/second_order_controller" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/second_order_controller.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/hotkeys_controller" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/hotkeys_controller.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/experiments" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/experiments.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="react" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/react.production.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="react-dom" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/react-dom.production.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="react-redux" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/react-redux.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="prop-types" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/prop-types.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="redux" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/redux.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="redux-thunk" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/redux-thunk.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/services/api" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/api.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/services/pubsub" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/pubsub.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/apps/discovery/navigator" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/navigator.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/services/storage" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/storage.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/history_manager" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/history_manager.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/user" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/user.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/session" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/session.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/page_managers/master" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/master.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/app_storage" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/app_storage.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/csrf_manager" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/csrf_manager.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/library_controller" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/library_controller.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/doc_stash_controller" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/doc_stash_controller.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/mixins/add_secondary_sort" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/add_secondary_sort.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/modules/orcid/orcid_api" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/orcid_api.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="hotkeys" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/hotkeys.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="moment" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/moment.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/services/default_pubsub" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/default_pubsub.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/persistent_storage" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/persistent_storage.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/navigator" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/navigator(1).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/modules/orcid/work" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/work.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/modules/orcid/profile" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/profile.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/modules/orcid/bio" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/bio.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/transition" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/transition.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/components/transition_catalog" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/transition_catalog.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="jsonpath" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/jsonpath.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="marionette" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/backbone.marionette.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="sprintf" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/sprintf.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="persist-js" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/persist-min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="jquery-ui" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/jquery-ui.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="bootstrap" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/bootstrap.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="bowser" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/es5.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="select2" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/select2.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="clipboard" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/clipboard.min.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/wraps/similar" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/similar.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/resources/widget.jsx" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/widget.jsx.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/associated/widget.jsx" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/widget.jsx(1).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/resources/redux/configure-store" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/configure-store.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/resources/redux/modules/api" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/api(1).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/resources/redux/modules/ui" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/ui.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/resources/containers/app" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/app.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/associated/redux/configure-store" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/configure-store(1).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/associated/redux/modules/api" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/api(2).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/associated/redux/modules/ui" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/ui(1).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/associated/containers/app" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/app(1).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/resources/redux/middleware/api" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/api(3).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/resources/redux/middleware/ui" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/ui(2).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/resources/components/app.jsx" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/app.jsx.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/associated/redux/middleware/api" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/api(4).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/associated/redux/middleware/ui" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/ui(3).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="js/widgets/associated/components/app.jsx" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/app.jsx(1).js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="mathjax" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/MathJax.js.download"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="filesaver" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/FileSaver.min.js.download"></script><!--<base href="/">--><base href=".">
  <style>
  .btn-full-ads {
      color: #fff !important;
      background-color: #1a1a1a !important;
      border-color: #1a1a1a !important;
      margin-top: 9px !important;
      padding-bottom: 10px !important;
      padding-top: 10px !important;
  }
  .btn-full-ads:hover, .btn-full-ads:focus, .btn-full-ads:active, .btn-full-ads.active, .open>.dropdown-toggle.btn-full-ads {
      color: #000 !important;
      background-color: #ddd !important;
      border-color: #1a1a1a !important;
  }

  .dropdown-toggle:hover .dropdown-menu {
    display: block;
  }

  .navbar-nav.navbar-right:last-child {
    margin-right: -15px !important;
  }

  .navbar-right {
    @media screen (min-width: $screen-sm) {
      float: right!important;
    }
  }

  /*the container must be positioned relative:*/
  .autocomplete {
    position: relative;
    display: inline-block;
  }

  .autocomplete-items {
    position: absolute;
    border: 1px solid #d4d4d4;
    border-bottom: none;
    border-top: none;
    z-index: 99;
    /*position the autocomplete items to be the same width as the container:*/
    top: 100%;
    left: 0;
    right: 0;
  }

  .autocomplete-items div {
    padding: 10px;
    cursor: pointer;
    background-color: #fff;
    border-bottom: 1px solid #d4d4d4;
  }

  /*when hovering an item:*/
  .autocomplete-items div:hover {
    background-color: #e9e9e9;
  }

  /*when navigating through the items using the arrow keys:*/
  .autocomplete-active {
    background-color: #d7dfec !important;
    color: #000000;
  }
  </style>

<meta id="dcngeagmmhegagicpcmpinaoklddcgon"><style type="text/css">.MathJax_Hover_Frame {border-radius: .25em; -webkit-border-radius: .25em; -moz-border-radius: .25em; -khtml-border-radius: .25em; box-shadow: 0px 0px 15px #83A; -webkit-box-shadow: 0px 0px 15px #83A; -moz-box-shadow: 0px 0px 15px #83A; -khtml-box-shadow: 0px 0px 15px #83A; border: 1px solid #A6D ! important; display: inline-block; position: absolute}
.MathJax_Menu_Button .MathJax_Hover_Arrow {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px; -khtml-border-radius: 4px; font-family: 'Courier New',Courier; font-size: 9px; color: #F0F0F0}
.MathJax_Menu_Button .MathJax_Hover_Arrow span {display: block; background-color: #AAA; border: 1px solid; border-radius: 3px; line-height: 0; padding: 4px}
.MathJax_Hover_Arrow:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_Hover_Arrow:hover span {background-color: #CCC!important}
</style><style type="text/css">#MathJax_About {position: fixed; left: 50%; width: auto; text-align: center; border: 3px outset; padding: 1em 2em; background-color: #DDDDDD; color: black; cursor: default; font-family: message-box; font-size: 120%; font-style: normal; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; border-radius: 15px; -webkit-border-radius: 15px; -moz-border-radius: 15px; -khtml-border-radius: 15px; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_About.MathJax_MousePost {outline: none}
.MathJax_Menu {position: absolute; background-color: white; color: black; width: auto; padding: 2px; border: 1px solid #CCCCCC; margin: 0; cursor: default; font: menu; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
.MathJax_MenuItem {padding: 2px 2em; background: transparent}
.MathJax_MenuArrow {position: absolute; right: .5em; padding-top: .25em; color: #666666; font-size: .75em}
.MathJax_MenuActive .MathJax_MenuArrow {color: white}
.MathJax_MenuArrow.RTL {left: .5em; right: auto}
.MathJax_MenuCheck {position: absolute; left: .7em}
.MathJax_MenuCheck.RTL {right: .7em; left: auto}
.MathJax_MenuRadioCheck {position: absolute; left: 1em}
.MathJax_MenuRadioCheck.RTL {right: 1em; left: auto}
.MathJax_MenuLabel {padding: 2px 2em 4px 1.33em; font-style: italic}
.MathJax_MenuRule {border-top: 1px solid #CCCCCC; margin: 4px 1px 0px}
.MathJax_MenuDisabled {color: GrayText}
.MathJax_MenuActive {background-color: Highlight; color: HighlightText}
.MathJax_MenuDisabled:focus, .MathJax_MenuLabel:focus {background-color: #E8E8E8}
.MathJax_ContextMenu:focus {outline: none}
.MathJax_ContextMenu .MathJax_MenuItem:focus {outline: none}
#MathJax_AboutClose {top: .2em; right: .2em}
.MathJax_Menu .MathJax_MenuClose {top: -10px; left: -10px}
.MathJax_MenuClose {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; font-family: 'Courier New',Courier; font-size: 24px; color: #F0F0F0}
.MathJax_MenuClose span {display: block; background-color: #AAA; border: 1.5px solid; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; line-height: 0; padding: 8px 0 6px}
.MathJax_MenuClose:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_MenuClose:hover span {background-color: #CCC!important}
.MathJax_MenuClose:hover:focus {outline: none}
</style><style type="text/css">.MathJax_Preview .MJXf-math {color: inherit!important}
</style><style type="text/css">.MJX_Assistive_MathML {position: absolute!important; top: 0; left: 0; clip: rect(1px, 1px, 1px, 1px); padding: 1px 0 0 0!important; border: 0!important; height: 1px!important; width: 1px!important; overflow: hidden!important; display: block!important; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none}
.MJX_Assistive_MathML.MJX_Assistive_MathML_Block {width: 100%!important}
</style><style type="text/css">#MathJax_Zoom {position: absolute; background-color: #F0F0F0; overflow: auto; display: block; z-index: 301; padding: .5em; border: 1px solid black; margin: 0; font-weight: normal; font-style: normal; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; -webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box; box-shadow: 5px 5px 15px #AAAAAA; -webkit-box-shadow: 5px 5px 15px #AAAAAA; -moz-box-shadow: 5px 5px 15px #AAAAAA; -khtml-box-shadow: 5px 5px 15px #AAAAAA; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_ZoomOverlay {position: absolute; left: 0; top: 0; z-index: 300; display: inline-block; width: 100%; height: 100%; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}
#MathJax_ZoomFrame {position: relative; display: inline-block; height: 0; width: 0}
#MathJax_ZoomEventTrap {position: absolute; left: 0; top: 0; z-index: 302; display: inline-block; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}
</style><style type="text/css">.MathJax_Preview {color: #888}
#MathJax_Message {position: fixed; left: 1em; bottom: 1.5em; background-color: #E6E6E6; border: 1px solid #959595; margin: 0px; padding: 2px 8px; z-index: 102; color: black; font-size: 80%; width: auto; white-space: nowrap}
#MathJax_MSIE_Frame {position: absolute; top: 0; left: 0; width: 0px; z-index: 101; border: 0px; margin: 0px; padding: 0px}
.MathJax_Error {color: #CC0000; font-style: italic}
</style>
























<style type="text/css">.MJXp-script {font-size: .8em}
.MJXp-right {-webkit-transform-origin: right; -moz-transform-origin: right; -ms-transform-origin: right; -o-transform-origin: right; transform-origin: right}
.MJXp-bold {font-weight: bold}
.MJXp-italic {font-style: italic}
.MJXp-scr {font-family: MathJax_Script,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-frak {font-family: MathJax_Fraktur,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-sf {font-family: MathJax_SansSerif,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-cal {font-family: MathJax_Caligraphic,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-mono {font-family: MathJax_Typewriter,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-largeop {font-size: 150%}
.MJXp-largeop.MJXp-int {vertical-align: -.2em}
.MJXp-math {display: inline-block; line-height: 1.2; text-indent: 0; font-family: 'Times New Roman',Times,STIXGeneral,serif; white-space: nowrap; border-collapse: collapse}
.MJXp-display {display: block; text-align: center; margin: 1em 0}
.MJXp-math span {display: inline-block}
.MJXp-box {display: block!important; text-align: center}
.MJXp-box:after {content: " "}
.MJXp-rule {display: block!important; margin-top: .1em}
.MJXp-char {display: block!important}
.MJXp-mo {margin: 0 .15em}
.MJXp-mfrac {margin: 0 .125em; vertical-align: .25em}
.MJXp-denom {display: inline-table!important; width: 100%}
.MJXp-denom > * {display: table-row!important}
.MJXp-surd {vertical-align: top}
.MJXp-surd > * {display: block!important}
.MJXp-script-box > *  {display: table!important; height: 50%}
.MJXp-script-box > * > * {display: table-cell!important; vertical-align: top}
.MJXp-script-box > *:last-child > * {vertical-align: bottom}
.MJXp-script-box > * > * > * {display: block!important}
.MJXp-mphantom {visibility: hidden}
.MJXp-munderover {display: inline-table!important}
.MJXp-over {display: inline-block!important; text-align: center}
.MJXp-over > * {display: block!important}
.MJXp-munderover > * {display: table-row!important}
.MJXp-mtable {vertical-align: .25em; margin: 0 .125em}
.MJXp-mtable > * {display: inline-table!important; vertical-align: middle}
.MJXp-mtr {display: table-row!important}
.MJXp-mtd {display: table-cell!important; text-align: center; padding: .5em 0 0 .5em}
.MJXp-mtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-mlabeledtr {display: table-row!important}
.MJXp-mlabeledtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mlabeledtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-merror {background-color: #FFFF88; color: #CC0000; border: 1px solid #CC0000; padding: 1px 3px; font-style: normal; font-size: 90%}
.MJXp-scale0 {-webkit-transform: scaleX(.0); -moz-transform: scaleX(.0); -ms-transform: scaleX(.0); -o-transform: scaleX(.0); transform: scaleX(.0)}
.MJXp-scale1 {-webkit-transform: scaleX(.1); -moz-transform: scaleX(.1); -ms-transform: scaleX(.1); -o-transform: scaleX(.1); transform: scaleX(.1)}
.MJXp-scale2 {-webkit-transform: scaleX(.2); -moz-transform: scaleX(.2); -ms-transform: scaleX(.2); -o-transform: scaleX(.2); transform: scaleX(.2)}
.MJXp-scale3 {-webkit-transform: scaleX(.3); -moz-transform: scaleX(.3); -ms-transform: scaleX(.3); -o-transform: scaleX(.3); transform: scaleX(.3)}
.MJXp-scale4 {-webkit-transform: scaleX(.4); -moz-transform: scaleX(.4); -ms-transform: scaleX(.4); -o-transform: scaleX(.4); transform: scaleX(.4)}
.MJXp-scale5 {-webkit-transform: scaleX(.5); -moz-transform: scaleX(.5); -ms-transform: scaleX(.5); -o-transform: scaleX(.5); transform: scaleX(.5)}
.MJXp-scale6 {-webkit-transform: scaleX(.6); -moz-transform: scaleX(.6); -ms-transform: scaleX(.6); -o-transform: scaleX(.6); transform: scaleX(.6)}
.MJXp-scale7 {-webkit-transform: scaleX(.7); -moz-transform: scaleX(.7); -ms-transform: scaleX(.7); -o-transform: scaleX(.7); transform: scaleX(.7)}
.MJXp-scale8 {-webkit-transform: scaleX(.8); -moz-transform: scaleX(.8); -ms-transform: scaleX(.8); -o-transform: scaleX(.8); transform: scaleX(.8)}
.MJXp-scale9 {-webkit-transform: scaleX(.9); -moz-transform: scaleX(.9); -ms-transform: scaleX(.9); -o-transform: scaleX(.9); transform: scaleX(.9)}
.MathJax_PHTML .noError {vertical-align: ; font-size: 90%; text-align: left; color: black; padding: 1px 3px; border: 1px solid}
</style></head>

<body data-new-gr-c-s-check-loaded="14.1255.0" data-gr-ext-installed=""><div id="MathJax_Message" style="display: none;"></div>
  <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NT2453N"
  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->
  
   



 
<a href="https://ui.adsabs.harvard.edu/#main-content" id="skip-to-main-content" class="sr-only sr-only-focusable" data-dont-handle="true">Skip to main content</a>

<div id="aria-announcement-container" role="status">
  Now on    article abstract page 
</div>
<div id="app-container">
    <div id="body-template-container"><div class="s-master-page-manager"><div id="navbar-container">
  <div data-widget="NavbarWidget"><div><nav class="navbar fluid navbar-inverse" aria-label="main navigation">
        <!-- Brand and toggle get grouped for better mobile display -->
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar" aria-hidden="true"></span>
                <span class="icon-bar" aria-hidden="true"></span>
                <span class="icon-bar" aria-hidden="true"></span>
            </button>
            <a class="navbar-brand s-navbar-brand" href="https://ui.adsabs.harvard.edu/#">
                <img class="s-ads-icon" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/transparent_logo.svg" alt="Astrophysics Data System Home Page">
                <h1 style="font-weight: bold;">ads</h1>
            </a>
        </div>

        <!-- Collect the nav links, forms, and other content for toggling -->
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">

            <ul class="nav navbar-nav navbar-right">

                <li class="dropdown">
                  <button type="button" class="btn btn-link dropdown-toggle feedback-button" data-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-comment feedback-icon" aria-hidden="true"></i> Feedback <span class="caret"></span>
                  </button>
                  <ul class="dropdown-menu" role="menu">
                    <li>
                      <a href="javascript:void(0);" data-target="#feedback-modal" data-toggle="modal" data-feedback-view="list">
                        <i class="fa fa-pencil" aria-hidden="true"></i> Submit Updates
                      </a>
                    </li>
                    <li>
                      <a href="javascript:void(0);" data-target="#feedback-modal" data-toggle="modal" data-feedback-view="general">
                        <i class="fa fa-envelope" aria-hidden="true"></i> General Feedback
                      </a>
                    </li>
                  </ul>
                </li>


                <li class="dropdown orcid-dropdown s-orcid-dropdown">
                    <button class="btn dropdown-toggle btn-link" data-toggle="dropdown" aria-expanded="false">
                        <span class="s-orcid-title"> <img src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/orcid-inactive.svg" alt="orcid symbol">  ORCID  <span class="caret"></span></span></button>
                    <ul class="dropdown-menu" role="menu">
                      <li class="no-hover-style">
                        <a href="javascript:void(0);" class="orcid-sign-in">Sign in to ORCID to claim papers in the ADS.</a>
                      </li>
                    </ul>
                </li>

                <!--end "if/else orcidLoggedIn"-->


                <li class="dropdown">
                    <button type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                      <i class="fa fa-question-circle about-page-icon" aria-hidden="true"></i> About <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" role="menu">
                        <li> <a href="https://ui.adsabs.harvard.edu/about/" target="_blank" rel="noopener"> <i class="fa fa-question-circle" aria-hidden="true"></i> About ADS </a></li>
                        <li> <a href="https://ui.adsabs.harvard.edu/help/whats_new/" target="_blank" rel="noopener"> <i class="fa fa-bullhorn" aria-hidden="true"></i> What's New </a></li>
                        <li> <a href="https://ui.adsabs.harvard.edu/blog/" target="_blank" rel="noopener"> <i class="fa fa-newspaper-o" aria-hidden="true"></i> ADS Blog </a></li>
                        <li> <a href="https://ui.adsabs.harvard.edu/help/" target="_blank" rel="noopener"> <i class="fa fa-info-circle" aria-hidden="true"></i> ADS Help Pages </a></li>
						            <li> <a href="https://ui.adsabs.harvard.edu/help/legacy/" target="_blank" rel="noopener"> <i class="fa fa-archive" aria-hidden="true"></i> ADS Legacy Services </a></li>
                        <li> <a href="https://ui.adsabs.harvard.edu/about/careers/" target="_blank" rel="noopener"> <i class="fa fa-group" aria-hidden="true"></i> Careers@ADS </a></li>
                    </ul>
                </li>


                <li class="register"><a href="https://ui.adsabs.harvard.edu/#user/account/register">Sign Up</a></li>
                <li class="login"><a href="https://ui.adsabs.harvard.edu/#user/account/login">Log In</a></li>

            </ul>

        </div>
        <!-- /.navbar-collapse -->
    
</nav>
<!-- /.container-fluid -->
</div></div>
</div>

<div id="alerts-container">
  <div data-widget="AlertsWidget"><span class="alert-banner"></span></div>
</div>

<div id="content-container">
          <div class="dynamic-container s-dynamic-container">
            


          <div><div id="abstract-page-layout" class="s-abstract-page-layout">

    <div class="s-stable-search-bar-height">
        <div class="s-search-bar-full-width-container">
            <div class="col-xs-0 col-sm-12 col-md-2 s-back-button-container"></div>
            <div class="col-xs-12 col-sm-9 col-md-7 s-search-bar-row" id="search-bar-row" data-widget="SearchWidget"><div class="s-search-bar-widget"><div role="search">
  <div class="row">
    <h2 class="sr-only">Search Bar to Enter New Query</h2>
    <div class="col-sm-12 s-quick-add" aria-label="quick add fields">
      <div id="expanded-search-form" class="unselectable">
        <div id="field-options">
          <span class="s-field-options">quick field:</span>

          <span class="visible-sm-inline visible-md-inline visible-lg-inline">
            <button class="btn btn-link btn-sm" title="" data-toggle="tooltip" data-field="author" data-punc="&quot;" data-original-title="search for an author, e.g. author:&quot;Kurtz,M&quot;">Author</button>
            <button class="btn btn-link btn-sm" title="" data-toggle="tooltip" data-field="first-author" data-punc="&quot;" data-original-title="search for a first author, e.g. author:&quot;^Kurtz,M&quot;">First Author</button>
            <button class="btn btn-link btn-sm" title="" data-toggle="tooltip" data-field="abs" data-punc="&quot;" data-original-title="limit search to abstract, title and keyword fields">Abstract</button>
          </span>

          <span class="visible-lg-inline">
            <button class="btn btn-link btn-sm" title="" data-toggle="tooltip" data-field="year" data-original-title="e.g. year:1999 or year:1999-2016">Year</button>
            <button class="btn btn-link btn-sm" title="" data-toggle="tooltip" data-field="full" data-punc="&quot;" data-original-title="search titles, abstract, text, keywords and acknowledgements of the articles">Fulltext</button>
          </span>

          <!--container for additional fields/operators-->
          <span id="option-dropdown-container"><label for="quick-add-dropdown" class="sr-only">Select a field or operator</label>
<select id="quick-add-dropdown" class="quick-add-dropdown select2-hidden-accessible" tabindex="-1" aria-hidden="true">
  <optgroup label="fields">
    <option value="abs">abstract</option>
    <option value="abstract">abstract only</option>
    <option value="ack">acknowledgements</option>
    <option value="aff">affiliation</option>
    <option value="arxiv_class">arXiv category</option>
    <option value="author_count">author count</option>
    <option value="author" data-field="author">author</option>
    <option value="bibcode">bibcode</option>
    <option value="bibgroup">bibliographic group</option>
    <option value="bibstem">bib abbrev, e.g. ApJ</option>
    <option value="body">body of article</option>
    <option value="data">data archive</option>
    <option value="collection">collection</option>
    <option value="citation_count">citation count</option>
    <option value="doctype">doctype</option>
    <option value="doi">doi</option>
    <option value="entdate" data-default-value="[2000-01-01 TO 2010-01-01]">entdate</option>
    <option value="first-author" data-field="first-author">first author</option>
    <option value="full" data-field="full">fulltext</option>
    <option value="identifier">identifier</option>
    <option value="inst">inst</option>
    <option value="keyword">keyword</option>
    <option value="object">object</option>
    <option value="orcid">orcid</option>
    <option value="page">page</option>
    <option value="property">property</option>
    <option value="pub">publication full name</option>
    <option value="pubdate" data-default-value="[2000-01 TO 2010-01]">date published</option>
    <option value="title">title</option>
    <option value="volume">volume</option>
    <option value="year" data-field="year">year</option>
  </optgroup>
  <optgroup label="operators">
    <option value="citations">citations()</option>
    <option value="pos">pos()</option>
    <option value="references">references()</option>
    <option value="reviews">reviews()</option>
    <option value="similar">similar()</option>
    <option value="topn">topn()</option>
    <option value="trending">trending()</option>
    <option value="useful">useful()</option>
  </optgroup>
  <optgroup label="special characters">
    <option value="?">single wildcard: ?</option>
    <option value="*">wildcard: *</option>
    <option value="=">exact match: =</option>
  </optgroup>
</select><span class="select2 select2-container select2-container--default" dir="ltr" style="width: auto;"><span class="selection"><span class="select2-selection select2-selection--single" role="combobox" aria-haspopup="true" aria-expanded="false" tabindex="0" aria-labelledby="select2-quick-add-dropdown-container"><span class="select2-selection__rendered" id="select2-quick-add-dropdown-container" title="abstract"><span class="select2-selection__placeholder">All Search Terms</span></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span></span></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>
</span>
        </div>
      </div>
    </div>
  </div>
  <div class="row"></div>
  <div class="row">
    <div class="col-sm-12 search-bar-input-row s-search-bar-input-row">
      <form name="main-query" action="https://ui.adsabs.harvard.edu/search/q=%s">
        <div class="form-group has-feedback">
          <div class="input-group">
                        <input type="text" class="form-control q ui-autocomplete-input" name="q" autofocus="" id="query-search-input" aria-label="Start typing a query here to begin an ADS Search" autocomplete="off" value="">

            <button type="button" class="icon-clear s-clear-control hidden"></button>

            <span class="input-group-btn">
                      <button type="submit" class="btn btn-primary search-submit s-search-submit" aria-label="submit">
                          <i class="fa fa-search" aria-hidden="true"></i></button>
                  </span>

          </div>
          <!-- /input-group -->
        </div>
        <!-- /form-group-->
      </form>
    </div>
  </div>
</div>
<div class="row">
  <div class="s-num-found" role="status" aria-atomic="true" aria-labelledby="num-found-title">
    <div class="sr-only" id="num-found-title">
      Your search returned 0 results
    </div>
    <span class="s-light-font description">Your search returned</span>
      <span class="num-found-container" style="font-weight: bold;">0</span>
      <span class="s-light-font"> results </span>
  </div>
</div>

</div></div>
        </div>
    </div>
    <div class="nav-button-container s-nav-button-container" id="nav-button-container">
      <button id="abs-nav-menu-toggle" class="btn btn-primary-faded toggle-menu s-toggle-menu "><i class="fa fa-bars" aria-hidden="true"></i> Show Menu</button>
      <button id="abs-full-txt-toggle" class="s-abs-full-txt-toggle-btn btn btn-primary-faded ">Full Text Sources</button>
    </div>
    <div class="s-dynamic-page-body" id="dynamic-page-body">
        <div class="s-abstract-content" style="overflow: hidden">

            <div class="col-sm-2 nav-container s-nav-container" id="left-column" style=""> <div>
<nav aria-labelledby="abstract-nav-label">
    <div class="s-nav-header s-view-nav" id="abstract-nav-label"> <i class="icon-list" aria-hidden="true"></i> <h3>view </h3> </div>

    <a href="https://ui.adsabs.harvard.edu/#abs/2024AGUFMSA13C..06A/abstract" data-widget-id="ShowAbstract">
    <div class="abstract-nav s-nav  s-nav-selected  ">
            <span class="s-content">
            Abstract
            </span>
    </div>
    </a>

    <a href="javascript:void(0);" aria-disabled="true" data-widget-id="ShowCitations">
    <div class="abstract-nav s-nav   s-nav-inactive ">
            <span class="s-content">
            Citations
            <span class="num-items"> </span>
            </span>
    </div>
    </a>

    <a href="javascript:void(0);" aria-disabled="true" data-widget-id="ShowReferences">
    <div class="abstract-nav s-nav   s-nav-inactive ">
            <span class="s-content">
            References
            <span class="num-items"> </span>
            </span>
    </div>
    </a>

    <a href="javascript:void(0);" aria-disabled="true" data-widget-id="ShowCoreads">
    <div class="abstract-nav s-nav   s-nav-inactive ">
            <span class="s-content">
            Co-Reads
            </span>
    </div>
    </a>

    <a href="https://ui.adsabs.harvard.edu/#abs/2024AGUFMSA13C..06A/similar" data-widget-id="ShowSimilar">
    <div class="abstract-nav s-nav  ">
            <span class="s-content">
            Similar Papers
            </span>
    </div>
    </a>

    <a href="javascript:void(0);" aria-disabled="true" data-widget-id="ShowToc">
    <div class="abstract-nav s-nav   s-nav-inactive ">
            <span class="s-content">
            Volume Content
            </span>
    </div>
    </a>

    <a href="javascript:void(0);" aria-disabled="true" data-widget-id="ShowGraphics">
    <div class="abstract-nav s-nav   s-nav-inactive ">
            <span class="s-content">
            Graphics
            </span>
    </div>
    </a>

    <a href="https://ui.adsabs.harvard.edu/#abs/2024AGUFMSA13C..06A/metrics" data-widget-id="ShowMetrics">
    <div class="abstract-nav s-nav  ">
            <span class="s-content">
            Metrics
            </span>
    </div>
    </a>


    <a href="https://ui.adsabs.harvard.edu/#abs//exportcitation" data-widget-id="ShowExportcitation__default">
      <div class="abstract-nav s-nav  ">
        <span class="content">
          Export Citation
        </span>
      </div>
    </a>
</nav>
</div></div>

            <div class="col-sm-9 col-md-7 s-middle-column" id="middle-column">
                <!--id is for screen readers-->
                <div class="main-content-container s-main-content-container" id="main-content" tabindex="-1">

                    <div id="current-subview">
                        <div data-widget="ShowAbstract"><article class="s-abstract-metadata"><!--<div id="article-navigation">|</div>-->



<h2 class="s-abstract-title">
  Bridging Weather and Space Weather: The signature of MJO in the F-Region Ionosphere </h2>

<button class="btn btn-xs btn-default-flat s-toggle-aff print-hidden" id="toggle-aff">
  Show affiliations
</button>
<span id="pending-aff" style="display: none;"><i class="fa fa-spinner fa-spin" aria-hidden="true"></i><span class="sr-only">Loading affiliations</span></span>
<span id="fail-aff" class="text-danger" style="display: none;"><i class="fa fa-times" aria-hidden="true"></i><span class="sr-only">affiliations loading</span></span>
 
<div id="authors-and-aff" class="s-authors-and-aff">
  <ul class="list-inline">
    <li class="author">
      <a href="https://ui.adsabs.harvard.edu/search/q=author:%22Aggarwal%2C+Deepali%22&amp;sort=date%20desc,%20bibcode%20desc">
        Aggarwal, Deepali
      </a>
      <span>
        <a class="orcid-author" href="https://ui.adsabs.harvard.edu/search/q=orcid:0009-0009-4866-8273&amp;sort=date%20desc,%20bibcode%20desc">
          <img class="inactive" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/orcid-inactive.svg" alt="search by orcid">
          <img class="active hidden" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/orcid-active.svg" alt="search by orcid">
        </a>
        
      </span>
      <span class="affiliation hide"> (<i>0</i>)</span>;
    </li>
        <li class="author">
      <a href="https://ui.adsabs.harvard.edu/search/q=author:%22Oberheide%2C+Jens%22&amp;sort=date%20desc,%20bibcode%20desc">
        Oberheide, Jens
      </a>
      <span>
        <a class="orcid-author" href="https://ui.adsabs.harvard.edu/search/q=orcid:0000-0001-6721-2540&amp;sort=date%20desc,%20bibcode%20desc">
          <img class="inactive" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/orcid-inactive.svg" alt="search by orcid">
          <img class="active hidden" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/orcid-active.svg" alt="search by orcid">
        </a>
        
      </span>
      <span class="affiliation hide"> (<i>1</i>)</span>;
    </li>
        <li class="author">
      <a href="https://ui.adsabs.harvard.edu/search/q=author:%22Kumari%2C+Komal%22&amp;sort=date%20desc,%20bibcode%20desc">
        Kumari, Komal
      </a>
      <span>
    
      </span>
      <span class="affiliation hide"> (<i>2</i>)</span>
    </li>
        </ul>
</div>

<div class="s-abstract-text">
  <h4 class="sr-only">Abstract</h4>
  <p>
     The Madden-Julian Oscillation (MJO) is a significant atmospheric phenomenon known to influence weather patterns and tropical weather systems extensively. This study investigates how the MJO affects space weather in the F-region ionosphere, bridging weather and space weather research. Using data from the COSMIC-2 GIS (Global Ionospheric Specification), we explore how atmospheric tides map the MJO's effects into the ionosphere. We established a robust connection between the MJO in the Mesosphere Lower Thermosphere (MLT) region (using SABER temperature data) and the ionosphere (using COSMIC-2 GIS electron density data). It suggests the E-region dynamo is likely the coupling mechanism. For instance, there is approximately 20% MJO modulation in the MLT region and the Equatorial Ionization Anomaly (EIA) electron density in the ionosphere for Diurnal Eastward propagating tide (DE3). For statistical analysis and to understand the role of the MJO phase, we utilized the SD-WACCM-X model output for both neutral temperature and electron density over 22 years (2002-2022). We found a strong phase alignment between the two regions. The predictability of MJO phases using the RMM index can be helpful in the forecasting capability of the ionospheric state and will be beneficial for space weather predictions.   </p>
</div>

<br>
<dl class="s-abstract-dl-horizontal">
  <dt>Publication:</dt>
  <dd>
    <div id="article-publication">AGU Fall Meeting 2024, held in Washington, D.C., 9-13 December 2024, Session: SPA-Aeronomy / Response of the Ionosphere-Thermosphere-Mesosphere to Terrestrial Weather and Connections to External Drivers I Oral, id. SA13C-06.</div>
  </dd>
    
  <dt>Pub Date:</dt>
  <dd>December 2024</dd>
 
  <dt>Bibcode:</dt>
  <dd>
    <button class="btn btn-link copy-btn" style="padding: 0px" id="abs-bibcode-copy" data-clipboard-text="2024AGUFMSA13C..06A" aria-label="copy bibcode">
      2024AGUFMSA13C..06A
      <i class="icon-help" aria-hidden="true" data-toggle="popover" data-container="body" data-content="The bibcode is assigned by the ADS as a unique identifier for the paper." data-original-title="" title=""></i>
    </button>
    <span id="abs-bibcode-copy-msg" class="text-info" style="display: none;">Copied!</span>
  </dd>

  </dl>
<br>
<a class="small pull-right text-faded" href="https://ui.adsabs.harvard.edu/feedback/correctabstract?bibcode=2024AGUFMSA13C..06A" title="Provide feedback or suggest corrections">
  <span class="fa-stack">
    <i class="fa fa-comment-o fa-stack-2x" aria-hidden="true"></i>
    <i class="fa fa-exclamation fa-stack-1x" aria-hidden="true"></i>
  </span>
  Feedback/Corrections?
</a>

</article></div>
                        <div data-widget="ShowCitations"></div>
                        <div data-widget="ShowReferences"></div>
                        <div data-widget="ShowCoreads"></div>
                        <div data-widget="ShowSimilar"></div>
                        <div data-widget="ShowToc"></div>
                        <div data-widget="ShowGraphics"></div>
                        <div data-widget="ShowExportcitation" data-origin="abstract"></div>
                        <div data-widget="ShowMetrics" data-allow-redirect="false"></div>
                        <div data-widget="MetaTagsWidget"><div></div></div>
                    </div>
                </div>
            </div>
            <div class="col-sm-3" id="resources-container" style="">
                <div class="resources-container">
                    <div class="s-right-col-widget-container" style="max-height: 50vh;
                    overflow-y: auto;">
                        <div data-widget="ShowResources"><div><div><div class="resources__container"><div class="resources__full__list"><div class="resources__header__row"><i class="fa fa-file-text-o" aria-hidden="true"></i><div class="resources__header__title">full text sources</div></div><div class="resources__content"><div class="resources__content__title">Publisher</div><div class="resources__content__links"><span><a href="https://ui.adsabs.harvard.edu/link_gateway/2024AGUFMSA13C..06A/PUB_HTML" target="_blank" rel="noopener" title="Electronic on-line publisher article (HTML) SIGN IN REQUIRED" class="resources__content__link "><span class="sr-only">Electronic on-line publisher article (HTML)</span><i class="fa fa-file-text" aria-hidden="true"></i></a></span></div></div></div></div><div class="resources__container"></div></div></div></div>
                        <div data-widget="ShowAssociated"><div></div></div>
                    </div>
                </div>
            </div>

            <div class="s-right-col-container col-sm-3 s-right-column" id="right-col-container">
                <div style="padding:10px;">
                <div data-widget="ShowLibraryAdd"><div class="query-info-widget s-query-info-widget"></div></div>
                <div data-widget="ShowGraphicsSidebar"><div class="s-graphics-sidebar graphics-sidebar"></div></div>
                </div>
            </div>

            <div class="col-lg-1"></div>
        </div>
    </div>
</div>

</div></div>
        </div>

<div id="footer-container">
  <div data-widget="FooterWidget"><div class="footer s-footer"><footer>
  <div class="__footer_wrapper">
    <div class="__footer_brand">
      © The SAO Astrophysics Data System
      <div class="__footer_brand_extra">
        <p>
          <i class="fa fa-envelope" aria-hidden="true"></i>
          adshelp[at]cfa.harvard.edu
        </p>
        <p>
          The ADS is operated by the Smithsonian Astrophysical Observatory under NASA Cooperative Agreement
          <em>80NSSC25M7105</em>
        </p>
      </div>
      <div class="__footer_brand_logos">
        <div class="logo1">
          <a href="http://www.si.edu/" target="_blank" rel="noopener">
            <img id="smithsonian-logo" src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/smithsonian-logo.svg" alt="Smithsonian logo">
          </a>
        </div>
        <div class="logo2">
          <a href="https://www.cfa.harvard.edu/" target="_blank" rel="noopener">
            <img src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/cfa.png" alt="Harvard Center for Astrophysics logo" id="cfa-logo">
          </a>
        </div>
        <div class="logo3">
          <a href="http://www.nasa.gov/" target="_blank" rel="noopener">
            <img src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/nasa-partner.svg" alt="NASA logo" id="nasa-logo">
          </a>
        </div>
      </div>
      <div class="__footer_brand_disclaimer">
        *The material contained in this document is based upon work supported by a National Aeronautics and Space
        Administration (NASA) grant or cooperative agreement. Any opinions, findings, conclusions or recommendations
        expressed in this material are those of the author and do not necessarily reflect the views of NASA.
      </div>
    </div>
    <div class="__footer_list">
      <div class="__footer_list_title">
        Resources
      </div>
      <ul class="__footer_links">
        <li>
          <a href="https://ui.adsabs.harvard.edu/about/" target="_blank" rel="noopener">
            <i class="fa fa-question-circle" aria-hidden="true"></i> About ADS
          </a>
        </li>
        <li>
          <a href="https://ui.adsabs.harvard.edu/help/" target="_blank" rel="noopener">
            <i class="fa fa-info-circle" aria-hidden="true"></i> ADS Help
          </a>
        </li>
        <li>
          <a href="https://adsabs.github.io/Status-Page/" target="_blank" rel="noopener">
            <i class="fa fa-heartbeat" aria-hidden="true"></i> System Status
          </a>
        </li>
        <li>
          <a href="https://ui.adsabs.harvard.edu/help/whats_new/" target="_blank" rel="noopener">
            <i class="fa fa-bullhorn" aria-hidden="true"></i> What's New
          </a>
        </li>
        <li>
          <a href="https://ui.adsabs.harvard.edu/about/careers/" target="_blank" rel="noopener">
            <i class="fa fa-group" aria-hidden="true"></i> Careers@ADS
          </a>
        </li>
        <li>
          <a href="https://ui.adsabs.harvard.edu/help/accessibility/" target="_blank" rel="noopener">
            <i class="fa fa-universal-access" aria-hidden="true"></i>
            Web Accessibility Policy
          </a>
        </li>
      </ul>
    </div>
    <div class="__footer_list">
      <div class="__footer_list_title">
        Social
      </div>
      <ul class="__footer_links">
        <li>
          <a href="https://twitter.com/adsabs" target="_blank" rel="noopener">
            <i class="fa fa-twitter" aria-hidden="true"></i> @adsabs
          </a>
        </li>
        <li>
          <a href="https://ui.adsabs.harvard.edu/blog/" target="_blank" rel="noopener">
            <i class="fa fa-newspaper-o" aria-hidden="true"></i> ADS Blog
          </a>
        </li>
      </ul>
    </div>
    <div class="__footer_list">
      <div class="__footer_list_title">
        Project
      </div>
      <ul class="__footer_links">
        <li>
          <a href="https://ui.adsabs.harvard.edu/core">Switch to basic HTML</a>
        </li>
        <li>
          <a href="https://ui.adsabs.harvard.edu/help/privacy/" target="_blank" rel="noopener">Privacy Policy</a>
        </li>
        <li>
          <a href="https://ui.adsabs.harvard.edu/help/terms" target="_blank" rel="noopener">Terms of Use</a>
        </li>
        <li>
          <a href="https://www.cfa.harvard.edu/sao" target="_blank" rel="noopener">Smithsonian Astrophysical Observatory</a>
        </li>
        <li>
          <a href="https://www.si.edu/" target="_blank" rel="noopener">Smithsonian Institution</a>
        </li>
        <li>
          <a href="https://www.nasa.gov/" target="_blank" rel="noopener">NASA</a>
        </li>
      </ul>
    </div>
  </div>
</footer>
</div></div>
</div>
</div></div>
  </div>
  <div id="darkSwitch" class="darkmode-toggle" title="Turn on dark mode">🌓</div>

  <script>
    function autocomplete(searchBox, autoValues) {
      // Arguments: the text field element and an array of possible autocompleted values
      var currentFocus; // selected autocomplete option
      // Function to be run when the user types
      searchBox.addEventListener("input", function(e) {
          var a, b, i, val = this.value;
          // close any list of autocomplete values
          closeAllLists();
          if (!val) { return false;}
          val = val.split(/\s+/);
          val = val[val.length - 1];
          if (!val) { return false;}
          currentFocus = -1;
          // Create a DIV element that will contain the items (values):
          a = document.createElement("DIV");
          a.setAttribute("id", this.id + "autocomplete-list");
          a.setAttribute("class", "autocomplete-items");
          // Append the DIV element as a child of the autocomplete container:
          this.parentNode.appendChild(a);
          for (i = 0; i < autoValues.length; i++) {
            // Check if the item starts with the same letters as the text field value:
            if (autoValues[i].match.substr(0, val.length).toUpperCase() == val.toUpperCase()) {
              // Create a DIV element for each matching element:
              b = document.createElement("DIV");
              b.innerHTML = autoValues[i].label;
              if ("desc" in autoValues[i]) {
                b.innerHTML += " <i>" + autoValues[i].desc + "</i>";
              }
              if (autoValues[i].value.startsWith(autoValues[i].match) ) {
                b.innerHTML += " | <strong>" + autoValues[i].match.substr(0, val.length) + "</strong>";
                b.innerHTML += autoValues[i].match.substr(val.length);
              }
              // Insert a input field that will hold the current array item's value:
              b.innerHTML += "<input type='hidden' value='" + autoValues[i].value + "'>";
              // Listen to clicks on the item value (DIV element):
              b.addEventListener("click", function(e) {
                  var terms = searchBox.value.split(/\s+/);
                  // Remove the current part of the input used for matching
                  terms.pop();
                  // Insert the value for the autocomplete text field:
                  terms.push(this.getElementsByTagName("input")[0].value);
                  searchBox.value = terms.join(" ");
                  // Move cursor position inside quotes/parenthesis if needed
                  searchBox.focus();
                  if (searchBox.value[searchBox.value.length-1] === '"' || searchBox.value[searchBox.value.length-1] === ')') {
                    searchBox.setSelectionRange(searchBox.value.length-1, searchBox.value.length-1);
                  }
                  // Close the list of autocompleted values
                  closeAllLists();
              });
              a.appendChild(b);
            }
          }
          if (a.children.length > 0) {
            // By default, enter will select the first entry
            currentFocus = 0;
            addActive(a.children);
          }
      });
      /*execute a function presses a key on the keyboard:*/
      searchBox.addEventListener("keydown", function(e) {
          var x = document.getElementById(this.id + "autocomplete-list");
          if (x) x = x.getElementsByTagName("div");
          if (e.keyCode == 40) {
            // If the arrow DOWN key is pressed, increase the currentFocus variable:
            currentFocus++;
            addActive(x);
          } else if (e.keyCode == 38) { //up
            // If the arrow UP key is pressed, decrease the currentFocus variable:
            currentFocus--;
            /*and and make the current item more visible:*/
            addActive(x);
          } else if (e.keyCode == 13) {
            // If the ENTER key is pressed:
            if (currentFocus > -1) {
              // Prevent the form from being submitted:
              e.preventDefault();
              // Simulate a click on the "active" item:
              if (x) x[currentFocus].click();
              currentFocus = -1;
            }
          }
      });
      function addActive(x) {
        // Classify an item as "active":
        if (!x) return false;
        // Remove the "active" class on all items:
        removeActive(x);
        if (currentFocus >= x.length) currentFocus = 0;
        if (currentFocus < 0) currentFocus = (x.length - 1);
        // Add class "autocomplete-active":
        x[currentFocus].classList.add("autocomplete-active");
      }
      function removeActive(x) {
        // Remove the "active" class from all autocomplete items:
        for (var i = 0; i < x.length; i++) {
          x[i].classList.remove("autocomplete-active");
        }
      }
      function closeAllLists(elmnt) {
        // Close all autocomplete lists in the document, except the one passed as an argument:
        var x = document.getElementsByClassName("autocomplete-items");
        for (var i = 0; i < x.length; i++) {
          if (elmnt != x[i] && elmnt != searchBox) {
            x[i].parentNode.removeChild(x[i]);
          }
        }
      }
      // Any other clicks in the document:
      document.addEventListener("click", function (e) {
          closeAllLists(e.target);
      });
    }

    var autoList = [
        { value: 'author:""', label: 'Author', match: 'author:"' },
        { value: 'author:"^"', label: 'First Author', match: 'first author' },
        { value: 'author:"^"', label: 'First Author', match: 'author:"^' },
        { value: 'bibcode:""', label: 'Bibcode', desc: 'e.g. bibcode:1989ApJ...342L..71R', match: 'bibcode:"' },
        { value: 'bibstem:""', label: 'Publication', desc: 'e.g. bibstem:ApJ', match: 'bibstem:"' },
        { value: 'bibstem:""', label: 'Publication', desc: 'e.g. bibstem:ApJ', match: 'publication (bibstem)' },
        { value: 'arXiv:', label: 'arXiv ID', match: 'arxiv:' },
        { value: 'doi:', label: 'DOI', match: 'doi:' },
        { value: 'full:""', label: 'Full text search', desc: 'title, abstract, and body', match: 'full:' },
        { value: 'full:""', label: 'Full text search', desc: 'title, abstract, and body', match: 'fulltext' },
        { value: 'full:""', label: 'Full text search', desc: 'title, abstract, and body', match: 'text' },
        { value: 'year:', label: 'Year', match: 'year' },
        { value: 'year:1999-2005', label: 'Year Range', desc: 'e.g. 1999-2005', match: 'year range' },
        { value: 'aff:""', label: 'Affiliation', match: 'aff:' },
        { value: 'abs:""', label: 'Search abstract + title + keywords', match: 'abs:' },
        { value: 'database:astronomy', label: 'Limit to papers in the astronomy database', match: 'database:astronomy' },
        { value: 'database:physics', label: 'Limit to papers in the physics database', match: 'database:physics' },
        { value: 'title:""', label: 'Title', match: 'title:"' },
        { value: 'orcid:', label: 'ORCiD identifier', match: 'orcid:' },
        { value: 'object:', label: 'SIMBAD object (e.g. object:LMC)', match: 'object:' },
        { value: 'property:refereed', label: 'Limit to refereed', desc: '(property:refereed)', match: 'refereed' },
        { value: 'property:refereed', label: 'Limit to refereed', desc: '(property:refereed)', match: 'property:refereed' },
        { value: 'property:notrefereed', label: 'Limit to non-refereed', desc: '(property:notrefereed)', match: 'property:notrefereed' },
        { value: 'property:notrefereed', label: 'Limit to non-refereed', desc: '(property:notrefereed)', match: 'notrefereed' },
        { value: 'property:eprint', label: 'Limit to eprints', desc: '(property:eprint)', match: 'eprint' },
        { value: 'property:eprint', label: 'Limit to eprints', desc: '(property:eprint)', match: 'property:eprint' },
        { value: 'property:openaccess', label: 'Limit to open access', desc: '(property:openaccess)', match: 'property:openaccess' },
        { value: 'property:openaccess', label: 'Limit to open access', desc: '(property:openaccess)', match: 'openaccess' },
        { value: 'doctype:software', label: 'Limit to software', desc: '(doctype:software)', match: 'software' },
        { value: 'doctype:software', label: 'Limit to software', desc: '(doctype:software)', match: 'doctype:software' },
        { value: 'property:inproceedings', label: 'Limit to papers in conference proceedings', desc: '(property:inproceedings)', match: 'proceedings' },
        { value: 'property:inproceedings', label: 'Limit to papers in conference proceedings', desc: '(property:inproceedings)', match: 'property:inproceedings' },
        { value: 'citations()', label: 'Citations', desc: 'Get papers citing your search result set', match: 'citations(' },
        { value: 'references()', label: 'References', desc: 'Get papers referenced by your search result set', match: 'references(' },
        { value: 'trending()', label: 'Trending', desc: 'Get papers most read by users who recently read your search result set', match: 'trending(' },
        { value: 'reviews()', label: 'Review Articles', desc: 'Get most relevant papers that cite your search result set', match: 'reviews(' },
        { value: 'useful()', label: 'Useful', desc: 'Get papers most frequently cited by your search result set', match: 'useful(' },
        { value: 'similar()', label: 'Similar', desc: 'Get papers that have similar full text to your search result set', match: 'similar(' },
      ];

    // initiate the autocomplete function on the "q" element, and pass along the operators array as possible autocomplete values:
    inputBox = document.getElementById("q")
    if (inputBox) {
      inputBox.focus() // autofucs
      inputBox.setSelectionRange(inputBox.value.length, inputBox.value.length); // bring cursor to the end
      autocomplete(inputBox, autoList);
    }
  </script>


  
  
  <script>
    (function() {
      // turn off no-js if we have javascript
      document.documentElement.className = document.documentElement.className.replace("no-js", "js");

      function getCookie(cname) {
        var name = cname + "=";
        var decodedCookie = decodeURIComponent(document.cookie);
        var ca = decodedCookie.split(';');
        for (var i = 0; i < ca.length; i++) {
          var c = ca[i];
          while (c.charAt(0) == ' ') {
            c = c.substring(1);
          }
          if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
          }
        }
        return "";
      }

      (function() {

        // looks for the cookie, and sets true if its 'always'
        const coreCookie = getCookie('core') === 'always';

        // only load bumblebee if we detect the core cookie and we are on abstract page
        if (coreCookie || (!(/^\/abs\//.test(document.location.pathname)) && !coreCookie)) {
          return;
        }

        window.__PRERENDERED = true;

        const addScript = function(args, cb) {
          const script = document.createElement('script');
          Object.keys(args).forEach((key) => {
            script.setAttribute(key, args[key]);
          });
          script.onload = function() { cb && cb(script); };
          document.body.appendChild(script);
        }

        window.require = {
          waitSeconds: 0,
          baseUrl: '/'
        };
        addScript({
          src: '/libs/require.js'
        }, () => {
          addScript({
            src: '/config/init.js'
          });
        });
      })();

    })();
  </script><script src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/require.js.download"></script>
  

  <!-- Add Sentry SDK -->
  <script src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/46062cbe0aeb7a3b2bb4c3a9b8cd1ac7.min.js.download" crossorigin="anonymous"></script>
  <script>
    // Set the environment variable
    window.process = { env: { NODE_ENV: window.ENV ?? 'production' } };

    window.getSentry = (cb, errCb) => {
      if (typeof errCb === 'function') {
        errCb.call(null, new Error('Failed to get Sentry'));
      }
    };

    window.sentryOnLoad = function() {
      try {
        Sentry.init({
          dsn: 'https://<EMAIL>/4507341192036352',

          // Performance Monitoring
          tracesSampleRate: window.ENV === 'development' ? 1.0 : 0.75,

          // Session Replay
          replaysSessionSampleRate: window.ENV === 'development' ? 1.0 : 0.1,
          replaysOnErrorSampleRate: 1.0,
          environment: window.ENV ?? 'production',
          integrations: [
            Sentry.browserTracingIntegration(),
            Sentry.replayIntegration({
              maskAllText: false,
              blockAllMedia: false,
              unmask: ['[name=q]'],
            }),
          ],
          tracePropagationTargets: [
            'localhost',
            'https://ui.adsabs.harvard.edu',
            'https://devui.adsabs.harvard.edu',
            'https://qa.adsabs.harvard.edu',
          ],
        });
      } catch (e) {
        console.error('Could not initialize Sentry', e);
      }
    };

    window.getSentry = function(cb, errCb = () => {}) {
      try {
        if (!window.Sentry) {
          throw new Error('Sentry not found');
        }
        return cb.call(window, Sentry);
      } catch (e) {
        console.warn('unable to run sentry callback', e);
        if (typeof errCb === 'function') {
          errCb.call(window, e);
        }
      }
    };
  </script>
  <!-- END Add Sentry SDK -->

<script src="./Bridging Weather and Space Weather_ The signature of MJO in the F-Region Ionosphere - Astrophysics Data System_files/init.js.download"></script><div class="modal fade" id="feedback-modal">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
        <p class="modal-title">
          <span class="btn-group" id="feedback-back-btn" style="display: none">
            <button class="btn btn-sm btn-default">
              <i class="fa fa-chevron-left" aria-hidden="true"></i> Back
            </button>
          </span>
          <span id="feedback-modal-title">How may we help you?</span>
        </p>
      </div>
      <div class="modal-body">
        <div class="list-group" id="feedback-select-group">
          <a href="https://ui.adsabs.harvard.edu/feedback/correctabstract" id="feedback_submit_abstract_link" class="list-group-item">
            <h4>Missing/Incorrect Record</h4>
            <p>Submit a missing record or correct an existing record.</p>
          </a>
          <a href="https://ui.adsabs.harvard.edu/feedback/missingreferences" class="list-group-item">
            <h4>Missing References</h4>
            <p>Submit missing references to an existing ADS record.</p>
          </a>
          <a href="https://ui.adsabs.harvard.edu/feedback/associatedarticles" class="list-group-item">
            <h4>Associated Articles</h4>
            <p>
              Submit associated articles to an existing record (e.g. arXiv /
              published paper).
            </p>
          </a>
          <a href="javascript:void(0);" class="list-group-item" id="open-general-feedback">
            <h4>General Feedback</h4>
            <p>Send your comments and suggestions for improvements.</p>
          </a>
        </div>
        <form id="feedback-general-form" class="feedback-form" style="display: none">
          <input type="hidden" name="_subject" value="Bumblebee Feedback">
          <input type="hidden" name="feedback-type" value="feedback">

          <input type="text" name="_gotcha" style="display:none">

          <div class="form-group half-width">
            <label>
              Name
              <input class="form-control" type="text" name="name" required="" autofocus="">
            </label>
          </div>
          <div class="form-group half-width">
            <label>
              Email
              <input class="form-control" type="email" name="_replyto" required="">
            </label>
          </div>

          <div class="form-group">
            <label>Feedback <br>
              <textarea rows="8" cols="50" name="comments" required="" style="min-width: 500px; min-height: 180px"></textarea>
            </label>
          </div>
          <div class="form-group">
            <button class="btn btn-success" type="submit" value="Send">
              Submit
            </button>
          </div>

          <div class="form-group">
            <p class="alert alert-info text-center">
              You can also reach us at
              <span class="s-text-bold">adshelp [at] cfa.harvard.edu</span>
            </p>
          </div>
          <input type="hidden" name="origin" value="bbb_feedback">
          <div class="form-group">
            <div class="g-recaptcha">
              <small class="recaptcha-msg">This site is protected by reCAPTCHA and the Google
                <a href="https://policies.google.com/privacy" target="_blank" rel="noreferrer">Privacy Policy</a> and
                <a href="https://policies.google.com/terms" target="_blank" rel="noreferrer">Terms of Service</a> apply.</small>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">
          Close without submitting
        </button>
      </div>
    </div>
    <!-- /.modal-content -->
  </div>
  <!-- /.modal-dialog -->
</div>
<!-- /.modal -->
<ul id="ui-id-1" tabindex="0" class="ui-menu ui-widget ui-widget-content ui-autocomplete ui-front" style="display: none;"></ul><div role="status" aria-live="assertive" aria-relevant="additions" class="ui-helper-hidden-accessible"></div></body><grammarly-desktop-integration data-grammarly-shadow-root="true"><template shadowrootmode="open"><style>
      div.grammarly-desktop-integration {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select:none;
        user-select:none;
      }

      div.grammarly-desktop-integration:before {
        content: attr(data-content);
      }
    </style><div aria-label="grammarly-integration" role="group" tabindex="-1" class="grammarly-desktop-integration" data-content="{&quot;mode&quot;:&quot;full&quot;,&quot;isActive&quot;:true,&quot;isUserDisabled&quot;:false}"></div></template></grammarly-desktop-integration></html>