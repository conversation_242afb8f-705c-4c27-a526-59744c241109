(window.webpackJsonp=window.webpackJsonp||[]).push([[3],{206:function(e,t,r){"use strict";var n,a=r(0),c=r(7),l=r(11),s=(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}),__decorate=function(e,t,r,n){var a,c=arguments.length,l=c<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)l=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(a=e[s])&&(l=(c<3?a(l):c>3?a(t,r,l):a(t,r))||l);return c>3&&l&&Object.defineProperty(t,r,l),l},__metadata=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},u=function(){function Configs(){}return Configs.prototype.notePopup=function(e){return'<button class="footNoteClose footNote__cycleElement" aria-label="close button">\n                    <i class="icon-tools_close" aria-hidden="true"></i>\n                </button>\n                <div class="footNotePopup__item footNote__cycleElement">'+e+'</div>\n                <span class="chevronDown"></span>'},Configs=__decorate([Object(a.j)()],Configs)}(),p=function(e){function Texts(){return null!==e&&e.apply(this,arguments)||this}return s(Texts,e),Texts}(a.d),g=function(e){function ClassList(){var t=null!==e&&e.apply(this,arguments)||this;return t.accordionControl=".accordion__control",t.jsOpen="js--open",t.citedByArticleSection=".article-section__citedBy",t}return s(ClassList,e),ClassList}(a.d),h=function(e){function Selectors(){var t=null!==e&&e.apply(this,arguments)||this;return t.supportInfo=new a.e("a[href$='#support-information-section']","document"),t.supportInfoControl=new a.e(".article-section__supporting a.accordion__control","document"),t.openResearch=new a.e("a[href$='#open-research-section']","document"),t.openResearchControl=new a.e(".article-section__open-research a.accordion__control","document"),t.links=new a.e('[data-db-target-of=sections] a, .scrollableLink, .scrollableLinks a, .comment-link a, .PdfLink a[href="#accessDenialLayout"], .article-section__full a[href*="#"], [href="#citedby-section"]',"document"),t}return s(Selectors,e),Selectors}(a.f),v=function(e){function Elements(){var t=null!==e&&e.apply(this,arguments)||this;return t.links=[],t}return s(Elements,e),Elements}(a.b),m=function(){function GoToSection(e){this.wrapper=e,this.isTablet=!1}return GoToSection.prototype.initialize=function(){this.elements.initialize(this.wrapper),this.addEventListeners()},GoToSection.prototype.isTabletOn=function(){this.isTablet=!0},GoToSection.prototype.isTabletOff=function(){this.isTablet=!1},GoToSection.prototype.addEventListeners=function(){var e=this;this.domUtils.addEventListener(this.elements.supportInfo,"click",(function(){return e.elements.supportInfoControl.click()})),this.domUtils.addEventListener(this.elements.openResearch,"click",(function(){return e.elements.openResearchControl.click()})),this.domUtils.addEventListener(this.elements.links,"click",(function(t){return e.flowControl(t)})),this.domUtils.addEventListener(this.elements.links,"keydown",(function(t){return e.flowControl(t)})),this.domUtils.addEventListener(document,"keydown",(function(t){return e.escapeKeyHandler(t)}))},GoToSection.prototype.flowControl=function(e){var t=this;if("keydown"!==e.type||e.keyCode===c.a.RETURN){var r=e.currentTarget;r.classList.contains("open-figure-link")||r.classList.contains("bibLink")||r.classList.contains("tab-link")||r.closest(".accordion__control")||r.closest(".figure__toggle-link")||(e.preventDefault(),e.stopPropagation(),this.domUtils.getElements("html , body").forEach((function(e){return e.classList.remove("lock-screen")})),r.classList.contains("noteLink")?(this.openFootNote(r),window.addEventListener("resize",(function(){t.domUtils.getElements(".footNotePopup").length&&t.openFootNote(r)}))):r.classList.contains("backToText")?this.domUtils.getElement(r.getAttribute("href")).focus():r.classList.contains("goToText")||r.closest(".article-header__references-container")||r.getAttribute("href").indexOf("#accessDenialLayout")>-1?(this.scrollToTarget(r,!0),this.domUtils.getElement(r.getAttribute("href")).focus()):(this.scrollToTarget(r,!0),window.UX.dropBlock.on.hide(),this.isTablet&&(this.domUtils.getElement("[data-db-target-of=sections]").click(),this.domUtils.getElement(".w-slide__back").click())),r.closest(".sections__drop.js--open")&&window.UX.dropBlock.on.hide())}},GoToSection.prototype.escapeKeyHandler=function(e){"Escape"===e.key&&this.closeFootNote()},GoToSection.prototype.scrollToTarget=function(e,t){var r=t?this.domUtils.getElementById(e.hash.slice(1)):e,n=null==(r=r||this.domUtils.getElement('[name="'+e.hash.slice(1)+'"]'))?void 0:r.closest(this.classList.accordionControl);if(n&&(null==r?void 0:r.closest(this.classList.citedByArticleSection))){var a=null==r?void 0:r.closest(this.classList.citedByArticleSection),c=this.domUtils.getElement(this.classList.accordionControl,a);c.classList.contains(this.classList.jsOpen)||c.click()}else n&&!n.classList.contains(this.classList.jsOpen)&&n.click();r?(r.scrollIntoView({behavior:"smooth",block:"start"}),r.setAttribute("tabindex","0"),setTimeout((function(){r.focus()}),750),"a"!==r.tagName&&"button"!==r.tagName&&r.setAttribute("tabindex","-1")):this.redirectToExternalLink(e)},GoToSection.prototype.redirectToExternalLink=function(e){window.location.assign(e.href)},GoToSection.prototype.openFootNote=function(e){var t=this;this.closeFootNote(),this.elements.noteLink=e;var r=this.createFootNote(e),n=r.noteContainer,a=r.chevronDown,c=r.noteClose,l=this.isNodeInsideNode(e,"TBODY");l instanceof HTMLElement?l.appendChild(n):document.body.appendChild(n),window.UX.utils.focusCycleInitiator(n),n.querySelector(".footNotePopup__item").focus({preventScroll:!0});var s=n.offsetHeight,u=0,p=0,g=0,h=0,v=0;l instanceof HTMLElement?(u=e.offsetTop,p=e.offsetLeft,g=e.offsetLeft-l.offsetLeft,h=l.offsetWidth,v=l.offsetHeight,n.classList.add("insideTable")):(u=e.getBoundingClientRect().top+document.documentElement.scrollTop,p=e.getBoundingClientRect().left,g=e.offsetLeft,h=e.parentElement.offsetWidth),this.isNodeInsideNode(e,"THEAD")?(a.classList.add("inverse"),n.style.top=u+35+"px",n.classList.add("insideHead")):n.style.top=u-s-5+"px",h/2>g?(n.style.left=p+2+"px",a.classList.add("left")):(n.style.left=p-n.offsetWidth+22+"px",a.classList.add("right")),v/2>u&&(a.classList.add("inverse"),n.style.top=u+35+"px"),c.addEventListener("click",(function(){return t.closeFootNote()}))},GoToSection.prototype.closeFootNote=function(){var e,t,r,n;(this.elements.noteLink||this.domUtils.getElement(".footNotePopup"))&&(null===(e=this.elements.noteLink)||void 0===e||e.setAttribute("aria-expanded","false"),null===(t=this.elements.noteLink)||void 0===t||t.removeAttribute("aria-controls"),null===(r=this.domUtils.getElement(".footNotePopup"))||void 0===r||r.remove(),null===(n=this.elements.noteLink)||void 0===n||n.focus(),this.elements.noteLink=null)},GoToSection.prototype.createFootNote=function(e){var t=document.createElement("div");t.classList.add("footNotePopup");var r=e.getAttribute("data-noteid"),n=e.getAttribute("id"),a=e.getAttribute("href").substring(1),c=null==r?void 0:r.split(" ");if(c.length>1){var l=document.createElement("div");c.forEach((function(e){var t=document.querySelector("li#"+e).cloneNode(!0);null==l||l.appendChild(t)})),t.innerHTML=this.configs.notePopup(l.innerHTML)}else{var s=document.querySelector("li#"+r).innerHTML;t.innerHTML=this.configs.notePopup(s)}return e.setAttribute("aria-expanded","true"),e.setAttribute("aria-controls",a),t.setAttribute("id",a),t.setAttribute("aria-labelledby",n),{noteContainer:t,chevronDown:this.domUtils.getElement(".chevronDown",t),noteClose:this.domUtils.getElement(".footNoteClose",t)}},GoToSection.prototype.isNodeInsideNode=function(e,t){return e.tagName===t?e:e.parentNode!==document.body&&this.isNodeInsideNode(e.parentNode,t)},__decorate([Object(a.i)(),__metadata("design:type",u)],GoToSection.prototype,"configs",void 0),__decorate([Object(l.a)("md"),__metadata("design:type",Object)],GoToSection.prototype,"isTablet",void 0),GoToSection=__decorate([Object(a.c)(h,v,g,p),__metadata("design:paramtypes",[HTMLElement])],GoToSection)}();t.a=m},373:function(e,t,r){"use strict";r(374);var n=r(206),a=r(7);!function(){var e=UX.figureViewer,t=document.querySelectorAll(".figure__image"),r=[];e.removeFigureTitle=function(){},e.additionalBrowse=function(){$("#pane-pcw-figures").hasClass("empty")&&(e.$browseContainer.empty(),e.$figures.each((function(){var t=$(this).clone(!0),r=t.find(".figure-links");r.length&&r.remove(),e.$browseContainer.append(t)})))},e.height=function(t){var r=$(window).innerHeight();e.$topRegHeight=$(".figure-viewer__reg__top").innerHeight();var n=r-e.$topRegHeight;if(e.isMobile){n-=e.$captionRegion.find(".figure-viewer__title").height();var a=document.querySelector(".figure-viewer__cent__right");a&&(a.style.height="".concat(n,"px"))}$(".figure-viewer__cent__left").find("figure").height(n)},e.on.additionalOnShow=function(){var e=document.querySelector(".figure-viewer");UX.utils.focusCycleInitiator(e);var t=document.querySelector(".js--adFocusableEl");if(t&&-1===t.tabIndex&&(t.tabIndex=0),r=UX.utils.disableNodes(e),e.querySelector(".figure__highResImgReplacer")){var n=e.querySelector(".figure-viewer__hold__fig figure");null==n||n.addEventListener("click",(function(){e.querySelector(".zoom-in").click()}))}},e.on.additionalOnHide=function(){var t=document.querySelector(".js--adFocusableEl");t&&0===t.tabIndex&&(t.tabIndex=-1),r.forEach((function(e){e.inert=!1})),e.$focusedElementBeforeOpened.focus()},1===t.length&&e.$figureNav.hide(),e.additionalReplace=function(){var t,r=new n.a,a=document.getElementById("figure-viewer"),c=null==a?void 0:a.querySelector(".figure__image"),l=null==a?void 0:a.querySelectorAll(".section_image"),s=null==c||null===(t=c.src)||void 0===t?void 0:t.split(".").pop().trim(),u=UX.utils.convertToArray(null==a?void 0:a.querySelectorAll(".figure-viewer__hold__figcap a:not(.linkBehavior)"));null==u||u.forEach((function(t){t.addEventListener("click",(function(t){e.on.hide(),null==r||r.flowControl(t)}))})),"svg"===s&&fetch(c.src).then((function(e){return e.text()})).then((function(e){var t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.innerHTML=e;var r=null==t?void 0:t.querySelector("feFlood");(null==r?void 0:r.getAttribute("flood-color"))||c.classList.add("bg-white")})).catch((function(e){return console.error(e)})),null==l||l.forEach((function(e){var t=document.createElement("canvas");t.width=e.width,t.height=e.height;var r=t.getContext("2d");r.drawImage(e,0,0);var n=r.getImageData(0,0,1,1).data;0===n[0]&&0===n[1]&&0==n[2]&&e.classList.add("bg-white")}))},e.zoom.panzoom={init:function init(){if(void 0!==$.fn.panzoom){e.$image.panzoom({$zoomIn:e.$zoominbtn,$zoomOut:e.$zoomoutbtn,$zoomRange:e.$zoomrange,$reset:e.$zoomreset,panOnlyWhenZoomed:!1,minScale:1});var t=document.querySelector(".direction-control");e.$image.on("panzoomchange",(function(r){if("1"===e.$zoomrange.val())e.$image.css("transform","matrix(1, 0, 0, 1, 0, 0)"),$(this).removeClass("zoomed"),t.classList.add("hidden");else{$(this).addClass("zoomed"),t.classList.remove("hidden");var n=document.querySelector(".figure-viewer");UX.utils.focusCycleInitiator(n)}}))}}};var c=document.querySelectorAll(".inline-equation__construct .figure__image");null==c||c.forEach((function(e){e.closest(".figure")||e.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation()}))}));var l=function moveControlHandler(e){var t=document.querySelector(".figure-viewer__hold__fig figure"),r="";r="none"===t.style.transform?"matrix(1, 0, 0, 1, 0, 0)":t.style.transform;var n=e.target.getAttribute("direction");t.style.transform=function updateMatrix(e,t){var r=e.match(/[-0-9.]+/g).map(Number);switch(t){case"up":r[5]-=50;break;case"down":r[5]+=50;break;case"left":r[4]-=50;break;case"right":r[4]+=50}return"matrix(".concat(r.join(", "),")")}(r,n)};document.querySelectorAll(".direction-control span").forEach((function(e){e.addEventListener("click",(function(e){return l(e)})),e.addEventListener("keydown",(function(e){"Enter"!==e.key&&e.keyCode!==a.a.RETURN&&" "!==e.key&&e.keyCode!==a.a.SPACE||l(e)}))}))}()},375:function(e,t,r){"use strict";var n=r(14),a=r.n(n),c=(r(376),r(223),r(196),r(55),r(68)),l=r(60);function _regenerator(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function i(r,n,a,l){var s=n&&n.prototype instanceof Generator?n:Generator,u=Object.create(s.prototype);return _regeneratorDefine2(u,"_invoke",function(r,n,a){var l,s,u,p=0,g=a||[],h=!1,v={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function d(t,r){return l=t,s=0,u=e,v.n=r,c}};function d(r,n){for(s=r,u=n,t=0;!h&&p&&!a&&t<g.length;t++){var a,l=g[t],m=v.p,b=l[2];r>3?(a=b===n)&&(u=l[(s=l[4])?5:(s=3,3)],l[4]=l[5]=e):l[0]<=m&&((a=r<2&&m<l[1])?(s=0,v.v=n,v.n=l[1]):m<b&&(a=r<3||l[0]>n||n>b)&&(l[4]=r,l[5]=n,v.n=b,s=0))}if(a||r>1)return c;throw h=!0,n}return function(a,g,m){if(p>1)throw TypeError("Generator is already running");for(h&&1===g&&d(g,m),s=g,u=m;(t=s<2?e:u)||!h;){l||(s?s<3?(s>1&&(v.n=-1),d(s,u)):v.n=u:v.v=u);try{if(p=2,l){if(s||(a="next"),t=l[a]){if(!(t=t.call(l,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=l.return)&&t.call(l),s<2&&(u=TypeError("The iterator does not provide a '"+a+"' method"),s=1);l=e}else if((t=(h=v.n<0)?u:r.call(n,v))!==c)break}catch(t){l=e,s=1,u=t}finally{p=1}}return{value:t,done:h}}}(r,a,l),!0),u}var c={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}t=Object.getPrototypeOf;var l=[][n]?t(t([][n]())):(_regeneratorDefine2(t={},n,(function(){return this})),t),s=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(l);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,_regeneratorDefine2(e,a,"GeneratorFunction")),e.prototype=Object.create(s),e}return GeneratorFunction.prototype=GeneratorFunctionPrototype,_regeneratorDefine2(s,"constructor",GeneratorFunctionPrototype),_regeneratorDefine2(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",_regeneratorDefine2(GeneratorFunctionPrototype,a,"GeneratorFunction"),_regeneratorDefine2(s),_regeneratorDefine2(s,a,"Generator"),_regeneratorDefine2(s,n,(function(){return this})),_regeneratorDefine2(s,"toString",(function(){return"[object Generator]"})),(_regenerator=function _regenerator(){return{w:i,m:f}})()}function _regeneratorDefine2(e,t,r,n){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}(_regeneratorDefine2=function _regeneratorDefine(e,t,r,n){function o(t,r){_regeneratorDefine2(e,t,(function(e){return this._invoke(t,r,e)}))}t?a?a(e,t,{value:r,enumerable:!n,configurable:!n,writable:!n}):e[t]=r:(o("next",0),o("throw",1),o("return",2))})(e,t,r,n)}var s,u,p=[],g=document.querySelector("article"),h=null==g?void 0:g.getAttribute("data-getFTR-references-enabled");UX.loader.load.related=function(){},UX.loader.load.references=function(){},UX.loader.load.figures=function(){},UX.loader.getContentSuccess=function(e){if("related"===e){var t,r=document.getElementById("pane-pcw-related");null==r||r.setAttribute("aria-hidden","false"),null===(t=UX.accordion)||void 0===t||t.logic($("#pane-pcw-related .accordion-tabbed"))}if("details"===e&&($(".tooltip").length&&(UX.tooltip?UX.tooltip.reinitialize():UX.tooltip=(new l.a).initialize()),UX.loader.crossMarkAccessibility(),UX.loader.altMetricLoader()),"figure"===e&&$("#pane-pcw-figures").find(".cloudflare-stream-player").length&&$.getScript(UX.loader.cloudFlareScript),"references"===e){var n=$(".js--scrollTab");n.length&&UX.tab.on.select.external(n)}UX.loader.showAudioSupportMsg(),UX.loader.streamErrorHandler("#pane-pcw-figures"),window.location.href.includes("utm")&&(UX.utm.setElements(),UX.utm.init()),"figure"===e&&(s=document.querySelectorAll("#pane-pcw-figures .figure__video .cloudflare-stream-player"),u=document.querySelectorAll("#pane-pcw-figures .figure__audio audio"),UX.loader.videoMedia(s,"figureTab"),UX.loader.audioMedia(u),document.addEventListener("figureTabTypeset",UX.utils.mathOnlyFallbackViewer),UX.loader.captionTruncate(".tab__content figcaption.figure__caption .figure__caption",5,"show","hide"));var a=document.querySelector(".tabs__wrapper .tab__content"),c=null==a?void 0:a.querySelector(".active:not(#pane-pcw-related)");if((null==c?void 0:c.innerHTML.includes("mathrm"))||(null==c?void 0:c.innerHTML.includes("textrm"))||(null==c?void 0:c.innerHTML.includes("math")))var p=setInterval((function(){c.hasChildNodes()&&MathJax.typesetPromise([a]).then((function(){clearInterval(p),document.dispatchEvent(new Event("figureTabTypeset"))})).catch((function(e){return console.log(e.message)}))}),2e3)},UX.loader.additionalController=function(){var e,t,r,n=null,a=null,l=null,p=null,g=null,v=$("body");if(UX.loader.abstractLangs(),UX.loader.referencesHash(),UX.utils.menuRoleButton(),v.one("click ",".related-tab",(function(e){void 0!==UX.loader.related&&(UX.loader.element=UX.loader.related,UX.loader.target=UX.loader.tabRelated,UX.loader.isArticleTabEmpty($(this))&&(UX.loader.load.articleTab(UX.loader.related,UX.loader.relatedTab,UX.loader.tabRelated,"related",!1,!1),$(UX.loader.target).removeClass("empty"),UX.accordion.hideAllBoolean=!1),$(".creative-work .meta").truncate({lines:1}))})),v.on("focus",".related-tab",(function(e){UX.loader.isMobile||$(this).trigger("click")})),v.one("click",".references-tab",(function(e){if(UX.loader.isArticleTabEmpty($(this))){var t=Array.from(document.querySelectorAll(".article-section__references .article-accordion")),r=document.getElementById("pane-pcw-references"),n=[];null==t||t.forEach((function(e){var t,r=null===(t=e.querySelector(".section__title"))||void 0===t?void 0:t.textContent,a=null==e?void 0:e.querySelector(".accordion__content > ul"),c="";"references"!==(null==r?void 0:r.toLowerCase())&&(c='<h3 class="ref-heading">'.concat(r,"</h3>"));var l='<div class="separator references-tab__collection">'.concat(c).concat(null==a?void 0:a.outerHTML,"</div>");n.push(l)}));var a='<div class="separator">'.concat(n.join(""),"</div>");if(r.insertAdjacentHTML("beforeend",a),h){var l=document.querySelectorAll(".getFTR .data-doi"),s=new c.a;l&&UX.loader.removeGetFTRElement(l),s.referencesTab()}}})),v.on("focus",".references-tab",(function(e){UX.loader.isMobile||$(this).trigger("click")})),null===(e=document.querySelector("#article_Ctrl"))||void 0===e||e.addEventListener("mousedown",(function(e){e.preventDefault()})),v.one("click",".figures-tab",(function(e){void 0!==UX.loader.figure&&(UX.loader.element=UX.loader.figure,UX.loader.target=UX.loader.tabFigure,UX.loader.isArticleTabEmpty($(this))&&(UX.loader.load.articleTab(UX.loader.figure,UX.loader.figuresTab,UX.loader.tabFigure,"figure",!1,!1),$(UX.loader.target).removeClass("empty")))})),v.on("focus",".figures-tab",(function(e){UX.loader.isMobile||$(this).trigger("click")})),$(document).on("click",".abstract-group .article-section__abstract .lang",(function(e){e.preventDefault(),$(".article-row-left").height("auto"),n=$(this).data("lang-of"),a=$(this).closest(".abstract-group").find("[data-lang="+n+"]");var t=document.querySelectorAll('[id^="abstract-graphical-"]'),r=document.querySelector(".abstract-group").querySelectorAll('[id^="section-"]');null==t||t.forEach((function(e){(null==e?void 0:e.id)!=="abstract-graphical-".concat(n)?e.style.display="none":e.style.display="unset"})),null==r||r.forEach((function(e){var t=e.getAttribute("lang");e.style.display=t===n?"block":"none"})),$(".lang").removeClass("active"),a.find("[data-lang-of="+n+"]").addClass("active"),a.show(),$(".article-row-left").outerHeight()<$(window).outerHeight()&&$(".article-row-left").height($(window).outerHeight()-$("header").outerHeight()),UX.coolbar.holder.empty(),UX.coolbar.init()})),$(document).on("click",".figure-viewer .figureLink, .figure-viewer .bibLink",(function(){$(".figure-viewer__ctrl__close").trigger("click")})),v.on("click",".bibLink",(function(e){var t;e.preventDefault(),e.stopPropagation();var r=$(this),n=$(".article-row-right").closest("article");(r.hasClass("js--scrollTab")||(n.find(".js--scrollTab").removeClass("js--scrollTab"),r.addClass("js--scrollTab")),UX.loader.isMobile)&&$(".w-slide__back").addClass("js--bib");var a=null===(t=e.target)||void 0===t?void 0:t.getAttribute("href"),c=null==a?void 0:a.slice(1),l=document.querySelector(".references-tab__collection li[data-bib-id='".concat(c,"']"));null==l||l.setAttribute("tabindex","0"),null==l||l.focus()})),$(document).on("click",".back-to-article",(function(){var e=$(this),t=e.data("back-to");$('.js--scrollTab[href="'+t+'"]').focus(),e.removeClass("js--show")})),$(window).on("load",(function(){var e=document.querySelector(".noteList-container");e&&e.classList.add("hidden"),$(".coolBar").length&&$(".w-slide").length&&$(".w-slide").addClass("padding-top"),$(".hideOnJSLoad").remove(),$(".showOnJSLoad").removeClass("showOnJSLoad"),$(".coolBar--accessDenail .accordion-tabbed__control").on("click",(function(e){$(this).hasClass("external")||(e.preventDefault(),e.stopPropagation(),$(this).next(".accordion-tabbed__content").toggle(),$(this).attr("aria-expanded","true"===$(this).attr("aria-expanded")?"false":"true"),$(this).parent(".accordion-tabbed__tab").toggleClass("js--open"))}))})),v.on("click",".more",(function(){return UX.loader.captionShowMore($(this)),!1})),v.on("click",".less",(function(){return UX.loader.captionShowLess($(this)),!1})),$(".corrections-label").length>0){var m=$(".corrections-container").hasClass("article-related")?"Article(s)":"Correction(s)";$(".corrections-body > ul").addClass("corrections-list"),$(".corrections-label").addClass("w-slide__btn").attr({"data-slide-target":".corrections-body","data-label":m})}v.on("click",".article-section__references:has(.getFTR) .accordion__control:not(.js--open)",(function(e){!function referencesBottom(e){var t=e.data("references");if(h){var r=document.querySelectorAll(".getFTR .data-doi"),n=new c.a;r&&UX.loader.removeGetFTRElement(r),n.articleReferences(e[0])}$(".article-section__references:has(.getFTR) .accordion__control").hasClass("clicked")||$.get(t).done((function(e){$(".article-section__references:has(.getFTR) .accordion__control").addClass("clicked")})).fail((function(e,t,r){console.log("error occure on the server "+r)}))}($(this))})),$(window).load((function(){"references-section"===window.location.href.split("#")[1]&&$(".article-section__references:has(.getFTR) .accordion__control:not(.js--open)").first().click()})),$(document).on("click",".w-slide .accordion-tabbed__tab:not(.js--open)",(function(e){$(this).children(".author-name").length&&(l=$(this),p=l.closest(".w-slide__content"),g=l.closest(".accordion-tabbed"),window.setTimeout((function(){(p.outerHeight()<l.position().top+l.outerHeight()||p.scrollTop()>l.position().top-g.position().top)&&p.animate({scrollTop:l.position().top-g.position().top},250)}),500))})),$(document).on("click",".skip-links a",(function(e){var t=$(this).attr("href"),r=$(".article-row "+t);if(r.length>1){if(e.preventDefault(),r.closest().is(".transplanted-clone"))return!1;$(window).scrollTop(r.offset().top),r.focus()}})),$(window).on("load",(function(){var e=$(document).find("#support-information-section"),t=e.closest(".accordion__control");window.location.href.indexOf("#support-information-section")>0&&e.length&&!t.hasClass("js--open")&&e.trigger("click"),$(".article-section__table thead").each((function(e,t){var r=$(t).children(),n=0;r.length>1&&r.each((function(e,t){var r=$(t).find("th"),a=$(t).outerHeight();0!==e&&r.css("top",n),n+=a}))})),$("#gs-casa-r").length&&$("#gs-casa-r").css("z-index","10")})),$(".fixedCoolBar").length&&(UX.loader.fixedCoolBar(),$(window).on("scroll resize",(function(){UX.loader.fixedCoolBar()}))),$(".seamlessAccessDenial__wrapper").length&&setTimeout(UX.loader.seamlessAccessDelay,5e3),window.addEventListener("load",(function(){var e=document.querySelector(".w-slide--list");((null==e?void 0:e.querySelector(".details-tab"))||UX.loader.isMobile)&&UX.loader.load.articleTab(UX.loader.details,UX.loader.detailsTab,UX.loader.tabDetails,"details")}));var b=document.querySelector(".articleAdvert"),y=null==b?void 0:b.querySelector(".advert-rail");null==y||y.insertAdjacentHTML("afterend",'<div class="sr-only js--adFocusableEl" aria-hidden="true" tabindex="-1"></div>');var _=document.querySelector(".article-section__supporting");if(_){var w=_.dataset.suppl,T=_.querySelector(".accordion__control");T&&(T.addEventListener("click",(function showSupplOnClick(e){e.currentTarget.dataset.triggered||(e.currentTarget.dataset.triggered="true",w&&fetch(w).then((function(){return!1})))})),T.addEventListener("keydown",(function showSupplOnKeydown(e){"Enter"===e.key&&(e.currentTarget.dataset.triggered||(e.currentTarget.dataset.triggered="true",w&&fetch(w).then((function(){return!1}))))})))}s=document.querySelectorAll("#article__content .figure__video .cloudflare-stream-player"),u=document.querySelectorAll("#article__content .figure__audio audio"),r=null===(t=document.querySelector(".cloudFlareScript"))||void 0===t?void 0:t.value,UX.loader.showAudioSupportMsg(),UX.loader.streamErrorHandler(".article__content"),UX.loader.fixedToolbarPosition(),UX.loader.setTPRAriaLabel(),UX.loader.audioMedia(u);var k=setInterval((function(){UX.loader.videoMedia(s),r&&clearInterval(k)}),3e3)},UX.loader.videoMedia=function(e,t){if(e.length){var r=0,n=0,a=Array.from(e),c=["playing","pause","seeking","waiting","ended","stalled"];a.forEach((function(e,a){var l=e,s=Stream(l),u=a;e.setAttribute("title","Video"),"figureTab"===t&&0===p.filter((function(e){return e.videoId===u})).length?p.push({videoId:u,videoTime:s.currentTime}):"figureViewer"===t&&p.map((function(e){return e.videoId===u?s.currentTime=e.videoTime:e})),c.forEach((function(e){s.addEventListener(e,(function(e){!function streamAnalysis(e,a,c,l){var s=c.closest(".figure__video").querySelector(".track-media-url");if(s){var u=s.value,g=l.autoplay,h=l.currentTime,v=e.type;if("playing"===v&&(r=h),h>0?n=h-r:(r=0,n=0),"figureTab"===t&&p.map((function(e){return e.videoId===a?e.videoTime=h:e})),u&&void 0!==l.currentTime){switch(v){case"pause":if(l.currentTime===l.duration)return!1}var m="".concat(u,"&mediaEventType=").concat(v,"&mediaPosition1=").concat(h,"&mediaPlayingPeriod=").concat(n,"&autoplay=").concat(g);fetch(m).then((function(){return!1}))}}}(e,u,l,s)}))})),window.addEventListener("beforeunload",(function(e){!function onBrowserClose(e,t,a){if(t.currentTime>0&&!t.paused&&!t.ended){var c=a.closest(".figure__video").querySelector(".track-media-url").value,l=t.autoplay,s=t.currentTime;n=s-r;var u=c?"".concat(c,"&mediaEventType=").concat("pause","&mediaPosition1=").concat(s,"&mediaPlayingPeriod=").concat(n,"&autoplay=").concat(l):"";fetch(u).then((function(){return!1}))}}(0,s,l)}))}))}},UX.loader.audioMedia=function(e){if(e.length){var t=0,r=0,n=["playing","pause","seeking","waiting","ended","stalled"],a=function streamAnalysis(e){var n=e.target.closest(".figure__audio").querySelector(".track-media-url");if(n){var a=n.value,c=e.target.autoplay,l=e.type,s=e.target.currentTime;if("playing"===l&&(t=s),s>0?r=s-t:(t=0,r=0),a&&void 0!==e.target.currentTime){switch(l){case"pause":if(e.target.currentTime===e.target.duration)return!1}var u="".concat(a,"&mediaEventType=").concat(l,"&mediaPosition1=").concat(s,"&mediaPlayingPeriod=").concat(r,"&autoplay=").concat(c);fetch(u).then((function(){return!1}))}}};e.forEach((function(e){n.forEach((function(t){e.addEventListener(t,a)}))})),window.addEventListener("beforeunload",(function onBrowserClose(){e.forEach((function(e){if(e.currentTime>0&&!e.paused&&!e.ended){var n=e.closest(".figure__audio").querySelector(".track-media-url").value,a=e.autoplay,c=e.currentTime;r=c-t;var l=n?"".concat(n,"&mediaEventType=").concat("pause","&mediaPosition1=").concat(c,"&mediaPlayingPeriod=").concat(r,"&autoplay=").concat(a):"";fetch(l).then((function(){return!1}))}}))}))}},UX.loader.setTPRAriaLabel=function(){var e=$("body");if(e.find(".tpr__link").length){var t=e.find(".tpr__link"),r=t[0].host.split(".")[0];t.attr("aria-label",r)}},UX.loader.abstractLangs=function(){var e=[],t=[],r=[],n=$(".abstract-group .article-section__abstract");$(".article-section__abstract:not(#abstract-graphical-)").hide(),new MutationObserver((function(e,t){var r=document.querySelector(".lang.active");if(r){var n=r.getAttribute("hreflang"),a=document.getElementById("abstract-graphical-".concat(n));a&&(a.style.display="block"),t.disconnect()}})).observe(document,{childList:!0,subtree:!0}),n.each((function(){if($(this).find(".lang").length){for(var n,a=0;a<=e.length;a++)if(e[a]===$(this).find(".lang").html())return void $(this).find(".lang").remove();e.push($(this).find(".lang").html()),t.push($(this).attr("id")),r.push(null!==(n=$(this).attr("lang-name"))&&void 0!==n&&n.includes(";")?$(this).attr("lang-name").split(";")[0]:$(this).attr("lang-name")),$(this).find(".lang").remove(),$(this).find(".article-section__header").after("<div class='lang-container'></div>")}})),e.length<=1&&($(".lang-container").remove(),$(".article-section__abstract").show());for(var a=0;a<e.length;a++)$(".lang-container").append("<a class='lang' href='#"+t[a]+"' hreflang='"+e[a]+"' data-lang-of='"+e[a]+"'>"+e[a]+"<span>This link goes to a "+r[a]+" section</span></a>");$(".lang").show(),n.find(".lang:first-child").addClass("active").show(),$(".article-section__abstract[data-lang= "+e[0]+" ]").show()},UX.loader.referencesHash=function(){"#reference"===window.location.hash&&setTimeout((function(){$(".references-tab").click()}),200)},UX.loader.captionTruncate=function(e,t,r,n){$(e).each((function(){$(this).truncate({lines:t,seeMoreLink:!0,seeMoreText:r,seeLessText:n})}))},UX.figureViewer.additionalBehaviorOnShow=function(e){var t=null;return void 0!==e.find("figcaption .figure__caption").data("originalContent")?t=e.find("figcaption .figure__caption").data("originalContent"):(t=e.find("figcaption, .figcaption").clone()).find("strong").remove(),s=document.querySelectorAll("#figure-viewer .figure__video .cloudflare-stream-player"),u=document.querySelectorAll("#figure-viewer .figure__audio audio"),UX.loader.videoMedia(s,"figureViewer"),UX.loader.audioMedia(u),t},$(window).on("load resize orientationchange",(function(){var e=".article-citation .loa-authors .accordion-tabbed";UX.loader.captionTruncate(".tab__content figcaption.figure__caption .figure__caption",5,"show","hide"),UX.loader.captionTruncate(".disclosures",2,"show","hide"),UX.loader.captionTruncate(".article-header__references-container:not(.no-truncate)",1,"More","Less"),UX.loader.captionTruncate(".aboutThisBook .content",6,"More","Less"),UX.loader.isMobile?($(e).truncate("destroy"),UX.loader.rebuild.responsive(),e=".article-citation .loa.mobile-authors",$(e).truncate({lines:2,type:"list",seeMoreLink:!0,seeMoreText:"See all authors",seeLessText:"See fewer authors",lessLinkEllipsis:!0,isMobile:!0,mobileTarget:"#sb-1",isSliding:!0})):$(e).truncate({lines:2,type:"list",seeMoreLink:!0,seeMoreText:"See all authors",seeLessText:"See fewer authors",lessLinkEllipsis:!0,isSliding:!1}),UX.loader.fixedToolbarPosition()})),document.addEventListener("mathjax-initilized",(function(){setTimeout((function(){UX.utils.fallbackImageViewer()}),1e3)})),UX.utils.lazyFallbackImage(),UX.loader.load.articleTabs=function(){var e=UX.utils.convertToArray(document.querySelectorAll(".article-row-right__tabLinks"));if(e.length){var t=!1,r=function detailsTabLoader(e){e.currentTarget.classList.contains("details-tab")&&!t&&(UX.loader.load.articleTab(UX.loader.details,UX.loader.detailsTab,UX.loader.tabDetails,"details",!1,!1),t=!0)};e.forEach((function(e){["click","focus"].forEach((function(t){e.addEventListener(t,r)}))}))}},UX.loader.articleTabsTrigger=function(){var e=document.querySelector("[data-default-tab]"),t=null==e?void 0:e.dataset.defaultTab,r=window.location.hash,n={figures:"figures",references:"references",related:"related",details:"details"},a="#reference"===r?"references":null==e?void 0:e.dataset.defaultTab;e&&function setDefaultTab(){var r,a,c,l,s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";(e.querySelector("[class*=".concat(e.getAttribute("data-default-tab"),"]")).removeAttribute("tabindex"),r=e.querySelector(".".concat(s,"-tab")))?(a=r.parentElement,c=e.querySelector('[id="pane-pcw-'.concat(s,'"]'))):(s=t,a=null===(l=r=e.querySelector(".".concat(t,"-tab")))||void 0===l?void 0:l.parentElement,c=e.querySelector('[id="pane-pcw-'.concat(t,'"]')));if(t&&!UX.loader.isMobile)switch(a.classList.add("active"),c.classList.add("active"),n[s]){case"figures":UX.loader.load.articleTab(UX.loader.figure,UX.loader.figuresTab,UX.loader.tabFigure,"figure",!1,!0);break;case"references":UX.loader.load.articleTab(UX.loader.references,UX.loader.referencesTab,UX.loader.tabReferences,"references",!1,!0);break;case"related":UX.loader.load.articleTab(UX.loader.related,UX.loader.relatedTab,UX.loader.tabRelated,"related",!1,!0);break;case"details":UX.loader.load.articleTab(UX.loader.details,UX.loader.detailsTab,UX.loader.tabDetails,"details",!1,!0)}}(a)},UX.loader.fixedToolbarPosition=function(){$(".fixedToolbar").length&&$(".fixedToolbar").css("top",$(".pageHeader").height())},UX.loader.showAudioSupportMsg=function(){UX.test.isIE()&&$(".figure__audio audio").length&&$(".figure__audio audio").each((function(e,t){var r=$(t),n=r.find("source").attr("type");(n.indexOf("wav")>-1||n.indexOf("ogg")>-1)&&(r.next(".media-error").removeClass("hidden"),r.remove())}))},UX.loader.streamErrorHandler=function(e){var t=document.querySelector(e);if(t){var r=t.querySelectorAll(".cloudflare-stream-player, audio"),n=function(){var e=a()(_regenerator().m((function _callee(e){var t,r,n,a,c,l,s,u,p;return _regenerator().w((function(g){for(;;)switch(g.n){case 0:if(!(t=e.target.closest(".figure__image"))){g.n=3;break}if(r=t.querySelector(".media-error"),n=t.dataset.trackMediaUrl,a=e.target.autoplay,c=e.type,l=e.target.attributes.poster.textContent,s=/time\s*=\s*(\d*)/i,u=l.replace(s,"time=1"),!1,p="",n&&(p="".concat(n,"&mediaEventType=").concat(c,"&errorMessage=error&autoplay=").concat(a),fetch(p).then((function(){return!1}))),!l){g.n=2;break}return g.n=1,UX.loader.checkPoster(u);case 1:if(!g.v){g.n=2;break}return g.a(2);case 2:e.target.remove(),null==r||r.classList.remove("hidden");case 3:return g.a(2)}}),_callee)})));return function mediaError(t){return e.apply(this,arguments)}}();r.length&&r.forEach((function(e){e.addEventListener("error",n)}))}},UX.loader.checkPoster=function(){var e=a()(_regenerator().m((function _callee2(e){var t;return _regenerator().w((function(r){for(;;)switch(r.p=r.n){case 0:return r.p=0,r.n=1,fetch(e);case 1:if(200!==(t=r.v).status){r.n=2;break}return r.a(2,!0);case 2:throw new Error("HTTP error: ".concat(t.status));case 3:r.n=5;break;case 4:return r.p=4,r.v,r.a(2,!1);case 5:return r.a(2)}}),_callee2,null,[[0,4]])})));return function(t){return e.apply(this,arguments)}}(),UX.loader.fixedCoolBar=function(){var e=$(".fixedCoolBar"),t=e.offset().top,r=e.offset().left,n=e.outerWidth(),a=e.outerHeight();$(window).scrollTop()>t?(e[0].style.top="0",e.css("padding-top",a),e.find(".stickybar").addClass("js--fixed").css({top:0,left:r,width:n})):(e[0].style.top="0",e.css("padding-top","0"),e.find(".stickybar").removeClass("js--fixed").css({top:"auto",width:"auto",left:"auto"}))},UX.loader.seamlessAccessDelay=function(){var e=$(".seamlessAccessDenial__wrapper"),t=e.find(".js--loader"),r=e.find(".text"),n="/action/ssostart?redirectUri=".concat(encodeURIComponent("".concat(window.location.pathname,"?saml_referrer")));t.length&&r.append('<a class="temp-link" href="'.concat(n,'">(or click to choose manually)</a>'))},UX.loader.crossMarkAccessibility=function(){var e=document.querySelector(".crossMarkTrigger");if(e){e.addEventListener("click",(function iframeAccessibility(e){var t=Math.floor(10*Math.random());e.currentTarget.dataset.focusTrigger="".concat(t),e.currentTarget.classList.add("js--opened"),setTimeout((function crossMarkInit(){var e=UX.utils.convertToArray(document.querySelectorAll("iframe.crossmark-popup__content")),r="js--currentIndex",n="js--fakeFocus",a=function crossMarkCloseBtn(e){var t=e.target.dataset.focusTriggerTarget,r=document.querySelector('.crossMarkTrigger[data-focus-trigger="'.concat(t,'"]'));null==r||r.focus(),null==r||r.classList.remove("js--opened")};e.length&&e.forEach((function(e){e.title="CrossMark";var a=e.closest("#crossmark-widget");if(a)if("block"===a.style.display){var c=a.querySelector(".crossmark-popup__logo"),l=a.querySelector(".crossmark-popup__inner"),s=a.querySelector(".crossmark-popup__footer"),u=document.createElement("div"),p=a.querySelector(".crossmark-popup__btn-close");if(a.setAttribute("tabindex","-1"),a.setAttribute("role","dialog"),a.setAttribute("aria-label","CrossMark dialog"),p&&(p.focus(),p.classList.add(r),p.setAttribute("aria-label","Close dialog"),p.dataset.focusTriggerTarget="".concat(t)),u.classList.add(n),u.setAttribute("tabindex","0"),u.setAttribute("aria-hidden","true"),l){if(!s){l.insertAdjacentHTML("beforeend",'\n                                                <div class="crossmark-popup__footer">\n                                                    <div class="crossmark-popup__footer-right">\n                                                        <a href="http://crossmark.crossref.org/" target="_blank">Help</a>\n                                                    </div>\n                                                </div>\n                                            ');var g=l.querySelector(".crossmark-popup__content-wrapper"),h=l.querySelector(".crossmark-popup__footer");g.style.paddingBottom="".concat(h?"".concat(h.offsetHeight,"px"):"")}l.appendChild(u)}null==c||c.parentElement.setAttribute("aria-label","Crossmark logo");var v=UX.utils.convertToArray(a.querySelectorAll('a, button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'));v.length&&(v.forEach((function(e,t){e.dataset.focusOrder=t,e.addEventListener("focus",(function(e){var t,c=null==a||null===(t=a.querySelector(".".concat(r)))||void 0===t?void 0:t.dataset.focusOrder;(v.map((function(e){return e.classList.remove(r)})),e.target.classList.add(r),e.target.classList.contains(n))&&(e.target.dataset.focusOrder>c&&v[0].focus())}))})),v[0].addEventListener("focusout",(function(e){e.relatedTarget&&void 0===e.relatedTarget.dataset.focusOrder&&v[v.length-2].focus()})))}else a.parentElement.removeChild(a)}));var c=UX.utils.convertToArray(document.querySelectorAll(".crossmark-popup__btn-close"));c.length&&c.forEach((function(e){return e.addEventListener("click",a)})),document.addEventListener("keydown",(function closeModalOnEsc(e){if(e.key.includes("Esc")){var t=document.getElementById("crossmark-widget");if(t&&"block"===t.style.display){t.style.display="none";var r=document.querySelector(".crossMarkTrigger.js--opened");null==r||r.focus(),null==r||r.classList.remove("js--opened")}}}))}))})),e.addEventListener("keydown",(function(t){"Enter"===t.key&&e.click()}))}},UX.loader.altMetricLoader=function(){var e=document.querySelector(".altmetric-embed");if(e){var t=setInterval((function altMetricChecker(){var n=e.querySelector("a[data-badge-popover]"),a=document.querySelector(".number-of-downloads"),c=document.querySelector(".cited-by-count");if(n||a||c){clearInterval(t);var l=document.querySelector(".metrics"),s=null==n?void 0:n.querySelector("img"),u=null==s?void 0:s.alt,p=null==l?void 0:l.querySelector(".altmetric-embed"),g=!(null!=s&&s.closest(".altmetric-embed")),h=null==p?void 0:p.dataset.uuid,v=document.querySelector('.altmetric-popover[data-uuid="'.concat(h,'"]')),m=null==v?void 0:v.querySelector(".altmetric-popover-content"),b=new Event("mouseout"),y=new Event("mouseover"),_=document.querySelector(".w-slide"),w=document.querySelector(".w-slide__content"),T=function closePopover(){null==n||n.dispatchEvent(b),null==n||n.focus(),null==n||n.setAttribute("aria-expanded","false")};(c||a||p)&&l.classList.add("js--show"),g&&e.classList.add("js--hide"),n&&function setTriggerAttributes(e){e.setAttribute("role","button"),e.setAttribute("aria-expanded","false"),e.setAttribute("id","altMetric"),e.setAttribute("aria-label",u),e.setAttribute("href","javascript:void(0)")}(n),null==n||n.addEventListener("click",(function triggerClickHandler(e){e.preventDefault(),e.target.dispatchEvent(y),null==n||n.setAttribute("aria-expanded","true"),m&&setTimeout((function(){var e=v.querySelector(".js--adFocusableEl"),t=v.querySelector(".js--backToLink");m.setAttribute("tabindex","-1"),m.focus(),!t&&m.insertAdjacentHTML("beforeend",'<a href="javascript:void(0)" role="button" aria-label="back to altmetric link" class="sr-only js--backToLink"></a>'),!e&&m.insertAdjacentHTML("afterend",'<div class="sr-only js--adFocusableEl" aria-hidden="true" tabindex="0"></div>'),m.addEventListener("click",(function backToLinkHandler(e){return e.target.classList.contains("js--backToLink")&&T()}))}),350)})),null==n||n.addEventListener("mouseover",(function EscapeMouseOver(){document.body.addEventListener("keydown",(function bodyKeyDownHandler(e){"Escape"===e.key&&"block"===v.style.display&&(null==n||n.dispatchEvent(b))}))})),_.addEventListener("click",T),w.addEventListener("scroll",(function(){var e;T(),null===(e=UX.tooltip)||void 0===e||e.hideTooltip()})),m&&(m.setAttribute("aria-labelledby",null==n?void 0:n.id),m.setAttribute("id","".concat(null==n?void 0:n.id,"-").concat(h)),null==n||n.setAttribute("aria-controls",m.id),m.addEventListener("focusout",(function focusOutHandler(e){return e.relatedTarget&&!e.relatedTarget.closest(".altmetric-popover-content")&&T()})),m.addEventListener("keydown",(function popoverEscapeHandler(e){return"Escape"===e.key&&T()})))}else 6e4===r&&clearInterval(t);r+=500}),500),r=0;UX.utils.fallbackImageViewer()}},UX.loader.removeGetFTRElement=function(e){e.forEach((function(e){if(""===e.textContent.trim()){var t=e.nextElementSibling,r=null==t?void 0:t.nextElementSibling;null==r||r.classList.add("first-link"),(null==t?void 0:t.classList.contains("getFTR__content"))&&t.remove()}}))}}}]);
//# sourceMappingURL=article-2f6b083f50707027adf0.js.map