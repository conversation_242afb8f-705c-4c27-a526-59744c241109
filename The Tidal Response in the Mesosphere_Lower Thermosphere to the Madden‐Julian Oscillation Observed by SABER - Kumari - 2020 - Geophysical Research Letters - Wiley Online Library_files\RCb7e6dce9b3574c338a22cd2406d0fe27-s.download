// For license information, see `https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RCb7e6dce9b3574c338a22cd2406d0fe27-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RCb7e6dce9b3574c338a22cd2406d0fe27-source.min.js', "!function(){const t=window.dataLayer.push;window.dataLayer.push=function(){for(const a of arguments)\"object\"!=typeof a||null===a||a.hasOwnProperty(\"event\")||\"consent\"!=a[0]||\"update\"!=a[1]||(t.apply(window.dataLayer,arguments),window.uetq=window.uetq||[],window.uetq.push(\"consent\",\"update\",{ad_storage:a[2].ad_storage}),window.dispatchEvent(new CustomEvent(\"GCMv2DataLayerPush\",{detail:a})));return t.apply(window.dataLayer,arguments)}}();");