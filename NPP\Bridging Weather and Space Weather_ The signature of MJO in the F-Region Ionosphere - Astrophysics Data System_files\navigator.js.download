function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}function _regenerator(){var f,e="function"==typeof Symbol?Symbol:{},t=e.iterator||"@@iterator",r=e.toStringTag||"@@toStringTag";function i(e,t,r,i){var n,o,a,s,c,u,g,d,l,t=t&&t.prototype instanceof h?t:h,t=Object.create(t.prototype);return _regeneratorDefine2(t,"_invoke",(n=e,o=r,g=i||[],d=!1,l={p:u=0,n:0,v:f,a:b,f:b.bind(f,4),d:function(e,t){return a=e,s=0,c=f,l.n=t,p}},function(e,t,r){if(1<u)throw TypeError("Generator is already running");for(d&&1===t&&b(t,r),s=t,c=r;(v=s<2?f:c)||!d;){a||(s?s<3?(1<s&&(l.n=-1),b(s,c)):l.n=c:l.v=c);try{if(u=2,a){if(v=a[e=s?e:"next"]){if(!(v=v.call(a,c)))throw TypeError("iterator result is not an object");if(!v.done)return v;c=v.value,s<2&&(s=0)}else 1===s&&(v=a.return)&&v.call(a),s<2&&(c=TypeError("The iterator does not provide a '"+e+"' method"),s=1);a=f}else if((v=(d=l.n<0)?c:n.call(o,l))!==p)break}catch(e){a=f,s=1,c=e}finally{u=1}}return{value:v,done:d}}),!0),t;function b(e,t){for(s=e,c=t,v=0;!d&&u&&!r&&v<g.length;v++){var r,i=g[v],n=l.p,o=i[2];3<e?(r=o===t)&&(c=i[(s=i[4])?5:s=3],i[4]=i[5]=f):i[0]<=n&&((r=e<2&&n<i[1])?(s=0,l.v=t,l.n=i[1]):n<o&&(r=e<3||i[0]>t||o<t)&&(i[4]=e,i[5]=t,l.n=o,s=0))}if(r||1<e)return p;throw d=!0,t}}var p={};function h(){}function n(){}function o(){}var v=Object.getPrototypeOf,e=[][t]?v(v([][t]())):(_regeneratorDefine2(v={},t,function(){return this}),v),a=o.prototype=h.prototype=Object.create(e);function s(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,o):(e.__proto__=o,_regeneratorDefine2(e,r,"GeneratorFunction")),e.prototype=Object.create(a),e}return _regeneratorDefine2(a,"constructor",n.prototype=o),_regeneratorDefine2(o,"constructor",n),_regeneratorDefine2(o,r,n.displayName="GeneratorFunction"),_regeneratorDefine2(a),_regeneratorDefine2(a,r,"Generator"),_regeneratorDefine2(a,t,function(){return this}),_regeneratorDefine2(a,"toString",function(){return"[object Generator]"}),(_regenerator=function(){return{w:i,m:s}})()}function _regeneratorDefine2(e,t,r,i){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}(_regeneratorDefine2=function(e,t,r,i){function n(t,r){_regeneratorDefine2(e,t,function(e){return this._invoke(t,r,e)})}t?o?o(e,t,{value:r,enumerable:!i,configurable:!i,writable:!i}):e[t]=r:(n("next",0),n("throw",1),n("return",2))})(e,t,r,i)}function asyncGeneratorStep(e,t,r,i,n,o,a){try{var s=e[o](a),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(i,n)}function _asyncToGenerator(s){return function(){var e=this,a=arguments;return new Promise(function(t,r){var i=s.apply(e,a);function n(e){asyncGeneratorStep(i,t,r,n,o,"next",e)}function o(e){asyncGeneratorStep(i,t,r,n,o,"throw",e)}n(void 0)})}}define(["jquery","backbone","underscore","js/components/navigator","js/components/api_feedback","js/components/api_query_updater","js/components/json_response","js/components/api_query","js/components/api_request","js/components/api_targets","hbs!404","hbs!js/apps/discovery/templates/orcid-modal-template","js/mixins/api_access","react-redux"],function(y,e,S,t,m,n,r,w,P,A,i,_,o,O){t=t.extend({start:function(d){var u=this,c=new n("navigator"),g=function(e){u.getPubSub().publish(u.getPubSub().FEEDBACK,new m(e))},l=function(e){u.getPubSub().publish(u.getPubSub().PAGE_CHANGE,e)},b=["Results","MyAdsFreeform","QueryInfo","AffiliationFacet","AuthorFacet","DatabaseFacet","RefereedFacet","KeywordFacet","BibstemFacet","BibgroupFacet","DataFacet","ObjectFacet","NedObjectFacet","VizierFacet","GraphTabs","QueryDebugInfo","ExportDropdown","VisualizationDropdown","SearchWidget","Sort","BreadcrumbsWidget","PubtypeFacet","OrcidSelector"],e=["TOCWidget","SearchWidget","ShowResources","ShowAssociated","ShowGraphicsSidebar","ShowLibraryAdd","MetaTagsWidget"];function f(e){return!d.getBeeHive().getObject("User").isLoggedIn()&&(d.getService("Navigator").navigate("authentication-page",{subView:"login",redirect:!0,next:e}),1)}function t(o,a,s){return function(e,t){var r,i=y.Deferred(),n=this;return f(n.endpoint)?i.resolve():((r=t.subView||a)||console.error("no subview or default view provided /to the navigator function!"),d.getObject("MasterPageManager").show("SettingsPage",[o,"UserNavbarWidget"]).then(function(){d.getWidget("SettingsPage").done(function(e){e.setActive(o,r)}),n.route="#user/settings/"+r,n.title="Settings"+(s?" | "+s:""),l("settings-page"),i.resolve()})),i.promise()}}function r(e,t){var r=y.Deferred(),i=this,o=t.widgetName,a=t.additional,n=a.format||"bibtex",s=t.subView,c=t.id,u=t.publicView;function g(i){var n=y.Deferred();return d.getObject("LibraryController").getLibraryBibcodes(i).done(function(r){d.getWidget("LibraryListWidget").then(function(e){var t=e.model.get("sort");d.getWidget(o).then(function(e){a=S.extend({},a,{sort:t}),e.renderWidgetForListOfBibcodes(r,a),d.getWidget("IndividualLibraryWidget").then(function(e){e.setSubView({subView:s,publicView:u,id:i}),n.resolve()})})})}),n.promise()}return"ExportWidget"===o&&"authoraff"===n&&(o="AuthorAffiliationTool"),d.getWidget(o).done(function(e){e.reset?e.reset():e.resetWidget&&e.resetWidget()}).done(function(){"ExportWidget"!==o||"classic"!==n?u?d.getObject("MasterPageManager").show("PublicLibrariesPage",["IndividualLibraryWidget",o]).then(function(){g(c).done(function(){i.route="#public-libraries/"+t.id,r.resolve()})}):d.getObject("MasterPageManager").show("LibrariesPage",["IndividualLibraryWidget","UserNavbarWidget",o]).then(function(){g(c).done(function(){i.route="#user/libraries/"+t.id,l("libraries-page"),r.resolve()})}):r.resolve()}),r.promise()}function i(e,t){var i=y.Deferred(),n=this,o=(t=t||{},d.getObject("AppStorage").getCurrentQuery());if(!o&&t.q)o=t.q;else if(!o&&!t.q)return i.resolve().promise();g({code:m.CODES.MAKE_SPACE});var a=S.map(e.split("-").slice(1),function(e){return e[0].toUpperCase()+e.slice(1)}).join("");return d.getObject("MasterPageManager").show("SearchPage",[a].concat(b.slice(1))).done(function(){var r="#search/"+c.clean(o.clone()).url()+"/"+e.split("-").slice(1).join("-");t&&t.onlySelected?d.getWidget(a).done(function(e){var t=d.getObject("AppStorage").getSelectedPapers();e.renderWidgetForListOfBibcodes(t),n.route=r,i.resolve()}):d.getWidget(a).done(function(e){e.renderWidgetForCurrentQuery({currentQuery:o}),n.route=r,i.resolve()})}),i.promise()}this.set("index-page",function(t){var r=this,i=y.Deferred();return d.getObject("MasterPageManager").show("LandingPage",["SearchWidget","RecommenderWidget"]).then(function(){return d.getWidget("LandingPage").then(function(e){t&&"SearchWidget"===t.origin?e.setActive("SearchWidget"):e.setActive("SearchWidget",null,{origin:"index-page"}),r.route="",r.title="",i.resolve()})}),i.promise()}),this.set("SearchWidget",function(){var e=this,t=y.Deferred();return S.bind(u.get("index-page").execute,this,{origin:"SearchWidget"})().then(function(){e.route="",e.title="",t.resolve()}),t.promise()}),this.set("404",(()=>{var t=_asyncToGenerator(_regenerator().m(function e(t){var r,i=arguments;return _regenerator().w(function(e){for(;;)switch(e.n){case 0:return r=1<i.length&&void 0!==i[1]?i[1]:{},e.n=1,d.getObject("MasterPageManager");case 1:return e.v.show("ErrorPage"),e.n=2,d.getWidget("ErrorPage");case 2:e.v.setMessage(r),0<Object.keys(r).length&&(this.replace=!0),this.route="404";case 3:return e.a(2)}},e,this)}));return function(e){return t.apply(this,arguments)}})()),this.set("ClassicSearchForm",function(t,e){var r=e.query,i=y.Deferred(),n=this;return d.getObject("MasterPageManager").show("LandingPage",[t]).then(function(){d.getWidget("LandingPage").done(function(e){e.setActive(t),e.widgets[t].applyQueryParams(r)}),n.route="#classic-form",n.title="Classic Form",i.resolve()}),i.promise()}),this.set("PaperSearchForm",function(){var e=y.Deferred(),t=this;return d.getObject("MasterPageManager").show("LandingPage",["PaperSearchForm"]).then(function(){d.getWidget("LandingPage").done(function(e){e.setActive("PaperSearchForm")}),t.route="#paper-form",t.title="Paper Form",e.resolve()}),e.promise()}),this.set("LibraryImport",function(e,t){var r=y.Deferred(),i=this;return f(i.endpoint)?r.resolve():d.getObject("MasterPageManager").show("SettingsPage",["LibraryImport","UserNavbarWidget"]).then(function(){d.getWidget("SettingsPage").done(function(e){e.setActive("LibraryImport"),i.route="#user/settings/libraryimport",i.title="Library Import",l("settings-page"),r.resolve()})}),r.promise()}),this.set("UserSettings",t("UserSettings",void 0)),this.set("UserPreferences",t("UserPreferences","application","Search Settings")),this.set("MyAdsDashboard",function(){var t=y.Deferred(),r=this;return f(r.endpoint)?t.resolve():d.getObject("MasterPageManager").show("SettingsPage",["MyAdsDashboard","UserNavbarWidget"]).then(function(){d.getWidget("SettingsPage").done(function(e){e.setActive("MyAdsDashboard"),r.route="#user/settings/myads",r.title="myADS Notifications",l("settings-page"),t.resolve()})}),t.promise()}),this.set("LibraryActionsWidget",function(){var t=y.Deferred(),r=this;return(f(r.endpoint)?t.resolve():(d.getObject("MasterPageManager").show("LibrariesPage",["LibraryActionsWidget","UserNavbarWidget"]).then(function(){d.getWidget("LibraryActionsWidget").done(function(e){e.reset(),r.route="#user/libraries/actions",l("libraries-page"),t.resolve()})}),t)).promise()}),this.set("AllLibrariesWidget",function(e,t){var r=y.Deferred(),i=this;return f(i.endpoint)?r.resolve():(t=t||"libraries",d.getObject("MasterPageManager").show("LibrariesPage",["AllLibrariesWidget","UserNavbarWidget"]).then(function(){d.getWidget("AllLibrariesWidget").done(function(e){e.setSubView({view:t}),e.reset(),i.route="#user/libraries/",i.title="My Libraries",l("libraries-page")}),r.resolve()})),r.promise()}),this.set("LibraryAdminView",function(e){var t=y.Deferred();return d.getObject("MasterPageManager").show("LibrariesPage",["IndividualLibraryWidget","UserNavbarWidget"]).then(function(){d.getWidget("IndividualLibraryWidget").done(function(e){e.setSubView({subView:"admin"})}),l("libraries-page"),t.resolve()}),t.promise()}),this.set("IndividualLibraryWidget",function(e,t){var r=y.Deferred(),i=this;if(t.publicView=t.publicView||!1,!t.publicView&&f(i.endpoint))return r.resolve().promise();this.route=t.publicView?"#public-libraries/"+t.id:"#user/libraries/"+t.id;var n=t.publicView;return d.getObject("MasterPageManager").show(n?"PublicLibrariesPage":"LibrariesPage",n?["IndividualLibraryWidget","LibraryListWidget"]:["IndividualLibraryWidget","LibraryListWidget","UserNavbarWidget"]).then(function(){d.getObject("LibraryController").getLibraryMetadata(t.id,!t.publicView).done(function(e){t.editRecords=S.contains(["write","admin","owner"],e.permission)&&!t.publicView,i.title=t.publicView?"Public":"Private Library | "+e.name,d.getWidget("LibraryListWidget","IndividualLibraryWidget").then(function(e){e.LibraryListWidget.setData(t),e.IndividualLibraryWidget.setSubView(t),n&&l("libraries-page"),r.resolve()})})}),r.promise()}),this.set("library-export",r),this.set("library-visualization",r),this.set("library-metrics",r),this.set("library-citation_helper",r),this.set("home-page",function(){var e=y.Deferred(),t=this;return(f(t.endpoint)?e.resolve():(d.getObject("MasterPageManager").show("HomePage",[]).then(function(){l("home-page"),t.title="Home",t.route="#user/home",e.resolve()}),e)).promise()}),this.set("authentication-page",function(e,t){var r=y.Deferred(),i=(t=t||{}).subView||"login",n=d.getBeeHive().getObject("User").isLoggedIn(),o=this;return n?u.get("index-page").execute().then(function(){r.resolve()}):(t.redirect&&(o.replace=!0),d.getObject("MasterPageManager").show("AuthenticationPage",["Authentication"]).then(function(){d.getWidget("Authentication").done(function(e){t.next&&e.setNextNavigation(t.next),e.setSubView(i),o.route="#user/account/"+i,o.title="login"===i?"Sign In":"reset-password-1"===i||"reset-password-2"===i?"Reset Password":"resend-verification-email"===i?"Resend Verification Eamil":"Register",r.resolve()})})),r.promise()}),this.set("results-page",function(e,t){var n=this,o=y.Deferred();return d.getObject("MasterPageManager").show("SearchPage",b).done(function(){var e,t,r,i=d.getObject("AppStorage").getCurrentQuery();i&&i.get("__original_url")?(e="#search/"+i.get("__original_url"),i.unset("__original_url")):e="#search/"+c.clean(i).url(),i instanceof w&&(t={},i.has("p_")?(r=(e=>{if(S.isString(e))try{return parseInt(e)}catch(e){}return!1})(i.get("p_")[0]),t.page=r):e+="&p_=0",S.isEmpty(t)||d.getWidget("Results").then(function(e){S.isFunction(e.updatePagination)&&e.updatePagination(t)})),i&&i.get("__qid")&&(e+="&__qid="+i.get("__qid")[0]),n.title=i&&i.get("q").length&&i.get("q")[0],n.route=e,g({code:m.CODES.UNMAKE_SPACE}),o.resolve()}),o.promise()}),this.set("export",function(e,t){var r=y.Deferred(),i=t.format||"bibtex",n=d.getObject("AppStorage"),o=this;return d.getObject("MasterPageManager").show("SearchPage",["ExportWidget"].concat(b.slice(1))).then(function(){d.getWidget("ExportWidget").done(function(e){o.route="#search/".concat(n.getCurrentQuery().url(),"/export-").concat(i),"authoraff"===i?t.onlySelected&&n.hasSelectedPapers()?e.getAuthorAffForm({bibcodes:n.getSelectedPapers()}):e.getAuthorAffForm({currentQuery:n.getCurrentQuery()}):(g({code:m.CODES.MAKE_SPACE}),t.onlySelected&&n.hasSelectedPapers()?e.renderWidgetForListOfBibcodes(n.getSelectedPapers(),{format:i,sort:n.hasCurrentQuery()&&n.getCurrentQuery().get("sort")[0]}):!1===t.onlySelected&&n.hasCurrentQuery()?e.renderWidgetForCurrentQuery({format:i,currentQuery:n.getCurrentQuery(),numFound:n.get("numFound")}):void 0===t.onlySelected&&n.hasSelectedPapers()?e.renderWidgetForListOfBibcodes(n.getSelectedPapers(),{format:i}):n.hasCurrentQuery()?e.exportQuery({format:i,currentQuery:n.getCurrentQuery(),numFound:n.get("numFound")}):(d.getController("AlertsController").alert({msg:"There are no records to export yet (please search or select some)"}),u.get("results-page")()))}).done(function(){r.resolve()})}),r.promise()}),this.set("export-query",function(){var t=y.Deferred(),e=d.getService("Api"),r=d.getObject("AppStorage").getCurrentQuery(),i=d.getController("AlertsController"),r=new w({query:r.url()});return e.request(new P({query:r,target:A.MYADS_STORAGE+"/query",options:{done:function(){},type:"POST",xhrFields:{withCredentials:!1}}})).done(function(e){i.alert(new m({code:m.CODES.ALERT,msg:'The query has been saved. You can insert the following snippet in a webpage: <br/><img src="'+A.MYADS_STORAGE+"/query2svg/"+e.qid+'"></img><br/><br/><textarea rows="10" cols="50"><a href="'+location.protocol+"//"+location.host+location.pathname+"#execute-query/"+e.qid+'"><img src="'+A.MYADS_STORAGE+"/query2svg/"+e.qid+'"></img></a></textarea>',modal:!0})),t.resolve()}),t.promise()}),this.set("search-page",function(e,n){var o=!1;try{o=-1<document.referrer.indexOf("tugboat/adsabs")}catch(e){}var t,a=y.Deferred(),s=(t=(t=n.page?S.map(n.page.split("-").slice(1),function(e){return e[0].toUpperCase()+e.slice(1)}).join(""):t)&&-1<["Metrics","AuthorNetwork","PaperNetwork","ConceptCloud","BubbleChart"].indexOf(t)?[t].concat(b.slice(1)):b,this),r=n&&n.context||{};return h(t,r).then(function(){function t(){var t,e=(e=u.getBeeHive().getObject("AppStorage").getCurrentQuery())||n.q,r=c.clean(e);e.has("__qid")&&r.set("__qid",e.get("__qid")),s.route="#search/"+r.url(),e.has("__bigquerySource")?s.title=e.get("__bigquerySource")[0]:s.title=e.get("q").length&&e.get("q")[0],o&&(s.route+="&__tb=1"),(r=e)instanceof w&&(t={},r.has("p_")?(r=(e=>{if(S.isString(e))try{return parseInt(e)}catch(e){}return!1})(r.get("p_")[0]),t.page=r):s.route+="&p_=0",S.isEmpty(t)||d.getWidget("Results").then(function(e){S.isFunction(e.updatePagination)&&e.updatePagination(t)})),n.page&&u.get(n.page)?S.bind(u.get(n.page).execute,s,n.page,{q:e})(n.page).then(function(){a.resolve()}):a.resolve()}function r(e){e&&e.code===m.CODES.SEARCH_CYCLE_FINISHED&&(t(),i.unsubscribe(i.FEEDBACK,r))}g({code:m.CODES.UNMAKE_SPACE});var i=u.getPubSub();i.subscribe(i.FEEDBACK,r),i.publish(i.START_SEARCH,n.q)}),a.promise()}),this.set("execute-query",function(e,t){var r=y.Deferred();return d.getService("Api").request(new P({target:A.MYADS_STORAGE+"/query/"+t,options:{done:function(e){e=(new w).load(JSON.parse(e.query).query);u.getPubSub().publish(u.getPubSub().START_SEARCH,e),r.resolve()},fail:function(){d.getController("AlertsController").alert(new m({code:m.CODES.ERROR,msg:"The query with the given UUID cannot be found"})),u.get("index-page").execute().then(function(){r.resolve()})},type:"GET",xhrFields:{withCredentials:!1}}})),r.promise()}),this.set("user-action",function(e,t){var r,i,n="",o="",a=y.Deferred(),s=t.token,t=t.subView;function c(t,e,r){u.get("index-page").execute().then(function(){var e=t.responseJSON&&t.responseJSON.error?t.responseJSON.error:"error unknown";u.getPubSub().publish(u.getPubSub().ALERT,new m({code:0,title:o,msg:" <b>"+e+"</b> <br/>"+n,modal:!0,type:"danger"})),a.reject()})}if("register"===t)o="Registration failed.",n="<p>Please try again, or contact <b> <EMAIL> for support </b></p>",r=A.VERIFY+"/"+s,i=function(t){u.getApiAccess({reconnect:!0}).done(function(){u.get("index-page").execute().then(function(){var e="<p>You have been successfully registered with the email</p> <p><b>"+t.email+"</b></p>";u.getPubSub().publish(u.getPubSub().ALERT,new m({code:0,title:"Welcome to ADS",msg:e,modal:!0,type:"success"})),a.resolve()})}).fail(function(){this.apply(c,arguments)})};else if("change-email"===t)o="Attempt to change email failed",n="Please try again, <NAME_EMAIL> for support",r=A.VERIFY+"/"+s,i=function(t){u.getApiAccess({reconnect:!0}).done(function(){u.get("index-page").execute().then(function(){var e="Your new ADS email is <b>"+t.email+"</b>";u.getPubSub().publish(u.getPubSub().ALERT,new m({code:0,title:"Email has been changed.",msg:e,modal:!0,type:"success"})),a.resolve()})}).fail(function(){this.apply(c,arguments)})};else{if("reset-password"!==t)return a.reject("Unknown subView: "+t),a.promise();i=function(){u.getBeeHive().getObject("Session").setChangeToken(s),u.getPubSub().publish(u.getPubSub().NAVIGATE,"authentication-page",{subView:"reset-password-2"}),a.resolve()},o="Password reset failed",n="Reset password token was invalid.",r=A.RESET_PASSWORD+"/"+s}t=new P({target:r,options:{type:"GET",context:u,done:i,fail:c}});return u.getBeeHive().getService("Api").request(t),a.promise()}),this.set("orcid-instructions",function(){var e=this,t=y.Deferred();return d.getObject("MasterPageManager").show("OrcidInstructionsPage").then(function(){e.route="#orcid-instructions",e.title="Orcid Instructions"}),t.promise()}),this.set("orcid-page",function(e,t){var r=y.Deferred(),i=d.getService("OrcidApi"),n=d.getService("PersistentStorage"),o=d.getObject("AppStorage"),a=d.getObject("User"),s=this;if(i.hasAccess()||!i.hasExchangeCode())return i.hasAccess()?(n.get("orcidAuthenticating")&&(n.remove("orcidAuthenticating"),i.getADSUserData().done(function(e){e.hasOwnProperty("authorizedUser")||'["UserPreferences",{"subView":"orcid"}]'===JSON.stringify(o.get("stashedNav"))||d.getController("AlertsController").alert(new m({code:m.CODES.ALERT,msg:_({adsLoggedIn:d.getObject("User").isLoggedIn()}),type:"success",title:"You are now logged in to ORCID",modal:!0}))}).fail(function(e){console.warn(e)})),d.getObject("MasterPageManager").show("OrcidPage",["OrcidBigWidget","SearchWidget"]).then(function(){s.route="/user/orcid",s.title="My Orcid",r.resolve()})):u.get("index-page").execute().then(function(){s.route="",r.resolve()}),r.promise();n?n.set("orcidAuthenticating",!0):console.warn("no persistent storage service available"),i.getAccessData(i.getExchangeCode()).done(function(e){i.saveAccessData(e),a.setOrcidMode(!0),u.getPubSub().publish(u.getPubSub().APP_EXIT,{url:window.location.pathname+(t&&S.isString(t)?t:window.location.hash)})}).fail(function(){a.setOrcidMode(!1),console.warn("Unsuccessful login to ORCID"),d.getController("AlertsController").alert(new m({code:m.CODES.ALERT,msg:"Error getting OAuth code to access ORCID",modal:!0,events:{click:"button[data-dismiss=modal]"}})).done(function(){u.get("index-page").execute()})})}),this.set("show-author-network",function(e,t){return i.apply(this,arguments)}),this.set("show-concept-cloud",function(e,t){return i.apply(this,arguments)}),this.set("show-paper-network",function(e,t){return i.apply(this,arguments)}),this.set("show-bubble-chart",function(e,t){return i.apply(this,arguments)}),this.set("show-metrics",function(e,t){return i.apply(this,arguments)}),this.set("visualization-closed",this.get("results-page"));function a(e,t){var r=y.Deferred();return d.getObject("MasterPageManager").show("DetailsPage",e).then(function(){return d.getWidget("DetailsPage").then(function(e){r.resolve(e)})}),r.promise()}function s(e){var t=y.Deferred(),r=u.getPubSub();return(19===e.length&&/^\d{4}[A-z].*\d[A-z]$/.test(e)?t.resolve(e):(e=new P({target:A.SEARCH,query:new w({q:"identifier:"+e,fl:"bibcode",rows:1}),options:{done:function(e){e&&e.response&&0<e.response.numFound&&t.resolve(e.response.docs[0].bibcode),t.resolve("null")},fail:function(){t.resolve("null")}}}),r.publish(r.EXECUTE_REQUEST,e),t)).promise()}function p(e){var t=e.id,r=e.bibcode,i=e.page,n=e.prefix,e=e.subView,o=u.getPubSub();o.publish(o.DISPLAY_DOCUMENTS,new w({q:"identifier:".concat(r)})),i.setActive(t,e),n?(o=d.getObject("AppStorage").getDocumentTitle())&&-1===o.indexOf(n)&&(this.title=n+" | "+o):(i=S.find(u.getBeeHive().getObject("DocStashController").getDocs()||[],{bibcode:r}))&&(this.title=i.title&&i.title[0])}var h=function(e,t){return d.getObject("MasterPageManager").show("SearchPage",e,t)},v=(this.set("verify-abstract",function(){y.Deferred;var t=new w({q:"identifier:"+this.queryUpdater.quoteIfNecessary(bibcode),fl:"bibcode"}),e=new P({query:t,target:A.SEARCH,options:{done:function(e){subPage&&(subPage[0].toUpperCase(),subPage.slice(1),bibcode,subPage),e.response&&e.response.docs&&e.response.docs[0]?(bibcode=e.response.docs[0].bibcode,u.getPubSub().publish(u.getPubSub().DISPLAY_DOCUMENTS,new w({q:"bibcode:"+bibcode}))):e.response&&e.response.docs&&!e.response.docs.length&&console.error("the query  "+t.get("q")[0]+"  did not return any bibcodes")},fail:function(){console.log("Cannot identify page to load, bibcode: "+bibcode),u.getPubSub().publish(this.getPubSub().NAVIGATE,"index-page")}}});this.getPubSub().publish(this.getPubSub().EXECUTE_REQUEST,e)}),this.set("ShowAbstract",function(r,i){var n=this,o=y.Deferred();return a([r].concat(e)).then(function(t){i.bibcode&&s(i.bibcode).then(function(e){p.call(n,{id:r,bibcode:e,page:t}),n.route=i.href,n.replace=!0,o.resolve()})}),o.promise()}),this.set("ShowCitations",function(r,i){var n=this,o=y.Deferred();return a([r].concat(e)).then(function(t){i.bibcode&&s(i.bibcode).then(function(e){p.call(n,{id:r,bibcode:e,page:t,prefix:"Citations"}),n.route=i.href,n.replace=!0,o.resolve()})}),o.promise()}),this.set("ShowReferences",function(r,i){var n=this,o=y.Deferred();return a([r].concat(e)).then(function(t){i.bibcode&&s(i.bibcode).then(function(e){p.call(n,{id:r,bibcode:e,page:t,prefix:"References"}),n.route=i.href,n.replace=!0,o.resolve()})}),o.promise()}),this.set("ShowCoreads",function(r,i){var n=this,o=y.Deferred();return a([r].concat(e)).then(function(t){i.bibcode&&s(i.bibcode).then(function(e){p.call(n,{id:r,bibcode:e,page:t,prefix:"Co-Reads"}),n.route=i.href,n.replace=!0,o.resolve()})}),o.promise()}),this.set("ShowSimilar",function(r,i){var n=this,o=y.Deferred();return a([r].concat(e)).then(function(t){i.bibcode&&s(i.bibcode).then(function(e){p.call(n,{id:r,bibcode:e,page:t,prefix:"Similar Papers"}),n.route=i.href,n.replace=!0,o.resolve()})}),o.promise()}),this.set("ShowToc",function(r,i){var n=this,o=y.Deferred();return a([r].concat(e)).then(function(t){i.bibcode&&s(i.bibcode).then(function(e){p.call(n,{id:r,bibcode:e,page:t,prefix:"Volume Content"}),n.route=i.href,n.replace=!0,o.resolve()})}),o.promise()}),this.set("ShowMetrics",function(r,i){var n=this,o=y.Deferred();return a([r].concat(e)).then(function(t){i.bibcode&&s(i.bibcode).then(function(e){p.call(n,{id:r,bibcode:e,page:t,prefix:"Metrics"}),n.route=i.href,n.replace=!0,o.resolve()})}),o.promise()}),this.set("ShowExportcitation",function(r,i){var n=this,o=y.Deferred(),a=i.subView||"default";return d.getObject("MasterPageManager").show("DetailsPage",[r].concat(e)).done(function(){d.getWidget("DetailsPage").done(function(t){i.bibcode&&s(i.bibcode).then(function(e){if(u.getPubSub().publish(u.getPubSub().DISPLAY_DOCUMENTS,new w({q:"identifier:"+e})),"null"===e)return t.setActive(null);t.widgets[r].ingestBroadcastedPayload(e),t.setActive(r,a),n.route=i.href,n.replace=!0,o.resolve()})})}),o.promise()}),this.set("ShowGraphics",function(r,i){var n=this,o=y.Deferred();return a([r].concat(e)).then(function(t){i.bibcode&&s(i.bibcode).then(function(e){p.call(n,{bibcode:e,page:t,id:r,prefix:"Graphics"}),n.route=i.href,n.replace=!0,o.resolve()})}),o.promise()}),this.set("show-author-affiliation-tool",function(e,r){var i=y.Deferred(),n=this,o=d.getObject("AppStorage").getCurrentQuery();return d.getObject("MasterPageManager").show("SearchPage",["AuthorAffiliationTool"].concat(b.slice(1))).done(function(){g({code:m.CODES.MAKE_SPACE}),d.getWidget("AuthorAffiliationTool").done(function(e){var t;r&&r.onlySelected?(t=d.getObject("AppStorage").getSelectedPapers(),e.renderWidgetForListOfBibcodes(t)):e.renderWidgetForCurrentQuery({currentQuery:o}),n.route="#search/"+c.clean(o).url(),i.resolve()})}),i.promise()}),(()=>{var t=_asyncToGenerator(_regenerator().m(function e(t){var r,i;return _regenerator().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,d._getWidget("ReactPageManager");case 1:return r=e.v,e.n=2,d._getWidget(t);case 2:return i=e.v,r.widgets[t]=i.render(),r.view=r.createView({widgets:_defineProperty({},t,r.widgets[t])}),e.n=3,d.getObject("MasterPageManager").show("ReactPageManager",[t]);case 3:return e.a(2,i)}},e)}));return function(e){return t.apply(this,arguments)}})());this.set("ShowFeedback",(()=>{var r=_asyncToGenerator(_regenerator().m(function e(t,r){var i,n,o,a;return _regenerator().w(function(e){for(;;)switch(e.n){case 0:return i=r.subview,n=r.bibcode,e.n=1,v("ShowFeedback");case 1:o=e.v,O.batch(function(){o.dispatch({type:"SET_FORM",payload:i}),o.dispatch({type:"SET_BIBCODE",payload:n})}),a=o.getState().main.form,l("feedback-".concat(a)),this.title="Feedback";case 2:return e.a(2)}},e,this)}));return function(e,t){return r.apply(this,arguments)}})())}});return S.extend(t.prototype,o),t});
//# sourceMappingURL=navigator.js.map