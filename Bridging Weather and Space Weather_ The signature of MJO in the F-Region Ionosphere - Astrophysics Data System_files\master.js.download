define(["js/widgets/base/base_widget","js/components/generic_module","js/page_managers/controller","hbs!js/page_managers/templates/aria-announcement","hbs!js/page_managers/templates/master-page-manager","marionette","js/mixins/dependon"],function(e,t,n,i,o,s,a){var c=Backbone.Model.extend({defaults:function(){return{id:void 0,isSelected:!1,object:void 0,options:void 0}}}),r=Backbone.Collection.extend({model:c,selectOne:function(t){var n=null;this.each(function(e){e.id==t?n=e:e.set("isSelected",!1,{silent:!0})}),n.set("isSelected",!0)}}),l=Backbone.Model.extend({defaults:function(){return{name:void 0,numCalled:0,numAttached:0,ariaAnnouncement:void 0}}}),d=s.ItemView.extend({className:"s-master-page-manager",constructor:function(e){(e=e||{}).collection||(e.collection=new r),e.model||(e.model=new l),e.template=o,s.ItemView.prototype.constructor.call(this,e)},changeManager:function(){var e=this.collection.findWhere({isSelected:!0}),t=e.attributes.object.show.apply(e.attributes.object,e.attributes.options);this.$(".dynamic-container").children().detach(),this.$(".dynamic-container").append(t.$el),e.attributes.numAttach+=1,$(document.documentElement).scrollTop(0),$(".s-search-bar-full-width-container").removeClass("s-search-bar-motion"),$(".s-quick-add").removeClass("hidden")},changeWithinManager:function(){var e=this.collection.findWhere({isSelected:!0});e.attributes.object.show.apply(e.attributes.object,e.attributes.options),e.attributes.numAttach+=1}}),c=n.extend({initialize:function(e){this.view=new d(e=e||{}),this.collection=this.view.collection,this.model=this.view.model,n.prototype.initialize.apply(this,arguments)},activate:function(e){this.setBeeHive(e);e=this.getPubSub();e.subscribe(e.ARIA_ANNOUNCEMENT,this.handleAriaAnnouncement)},assemble:function(e){return this.setApp(e),n.prototype.assemble.call(this,e)},show:function(t,n,i){function o(e){c.get("isSelected")?(c.set({options:n,object:e}),r.view.changeWithinManager()):(c.set({options:n,object:e}),r.collection.selectOne(t),r.view.changeManager()),i&&e.provideContext&&e.provideContext.call(e,i);var e=r.currentChild;r.currentChild=t,e&&e!=t&&(e=r.collection.find({id:e}))&&e.get("object")&&e.set("numDetach",e.get("numDetach")+1),r.getPubSub().publish(r.getPubSub().ARIA_ANNOUNCEMENT,t),s.resolve()}var s=$.Deferred(),a=this.getApp(),c=(this.collection.find({id:t})||this.collection.add({id:t}),this.collection.find({id:t})),r=this;return c.get("object")?o(c.get("object")):a._getWidget(t).then(function(e){c.set("object",e),e||console.error("unable to find page manager: "+t),e.assemble?e.assemble(a).then(function(){o(e)}):(console.error("eeeek, "+t+" has no assemble() method!"),s.reject())}),s.promise()},getCurrentActiveChild:function(){return this.collection.get(this.currentChild).get("object")},disAssemble:function(){_.each(this.collection.models,function(e){if(e.attributes.isSelected){var t=e.get("object");if(t.disAssemble)t.disAssemble(this.getApp());else{if(!t.destroy)throw new Error("Contract breach, no way to get ridd of the widget/page manager");t.destroy()}}e.set({isSelected:!1,object:null}),this.assembled=!1},this)},handleAriaAnnouncement:function(e){$("a#skip-to-main-content").remove(),$("div#aria-announcement-container").remove(),$("#app-container").before(i({page:e}))}});return _.extend(c.prototype,a.App),c});
//# sourceMappingURL=master.js.map