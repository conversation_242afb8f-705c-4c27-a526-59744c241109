define(["underscore","backbone","js/components/api_query","js/components/api_request"],function(e,t,o,r){return{onBootstrap:function(e){var t,s,n=this.getBeeHive();e.access_token?(n.getService("Api").setVals({access_token:"".concat(e.token_type," ").concat(e.access_token),refresh_token:e.refresh_token,expires_at:e.expires_at}),console.warn("Redefining access_token: "+e.access_token),s=n.getObject("User"),t=e.anonymous?void 0:e.username,s.setUser(t),(s=n.getService("PersistentStorage"))&&s.set&&s.set("appConfig",e)):console.warn("bootstrap didn't provide access_token!")},getApiAccess:function(e){e=e||{};var t=this.getBeeHive().getService("Api"),s=this,n=$.Deferred();return t[e.tokenRefresh?"_request":"request"](new r({query:new o,target:this.bootstrapUrls?this.bootstrapUrls[0]:"/accounts/bootstrap"}),{done:function(t){window.getSentry(function(e){e.setUser({id:t.access_token,anonymous:t.anonymous})}),e.reconnect&&s.onBootstrap(t),n.resolve(t)},fail:function(){n.reject.apply(n,arguments)},type:"GET"}),n}}});
//# sourceMappingURL=api_access.js.map