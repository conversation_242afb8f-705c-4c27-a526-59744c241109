!function (r) {
  "function" == typeof define && define.amd ? define(r) : r();
}(function () {
  Array.prototype.flat || Object.defineProperty(Array.prototype, "flat", {
    configurable: !0,
    value: function r() {
      var t = isNaN(arguments[0]) ? 1 : Number(arguments[0]);
      return t ? Array.prototype.reduce.call(this, function (e, a) {
        return Array.isArray(a) ? e.push.apply(e, r.call(a, t - 1)) : e.push(a), e;
      }, []) : Array.prototype.slice.call(this);
    },
    writable: !0
  }), Array.prototype.flatMap || Object.defineProperty(Array.prototype, "flatMap", {
    configurable: !0,
    value: function value(r) {
      return Array.prototype.map.apply(this, arguments).flat();
    },
    writable: !0
  });
});
