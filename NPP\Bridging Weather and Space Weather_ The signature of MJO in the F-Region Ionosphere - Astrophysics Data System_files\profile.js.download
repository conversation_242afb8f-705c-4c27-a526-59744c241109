define(["underscore","jsonpath","js/modules/orcid/work"],function(e,r,s){var n={workSummaries:"$"};return function(t){this._root=t||{},this.works=[],this.get=function(t){t=r.query(this._root,t);return e.isEmpty(t)?null:e.isArray(t)&&t.length<=1?t[0]:t},this.getWorks=function(){return this.works},this.setWorks=function(t){return this.works=t,this},this.toADSFormat=function(){var t=e.sortBy(this.getWorks(),function(t){return t.getTitle()});return{responseHeader:{params:{}},response:{numFound:(t=e.map(t,function(t){return t.toADSFormat()})).length,start:0,docs:t}}},e.reduce(n,function(t,r,s){return e.isString(s)&&s.slice&&(t["get"+(s[0].toUpperCase()+s.slice(1))]=e.partial(t.get,r)),t},this),this.works=e.map(this.getWorkSummaries(),function(t){return new s(t)})}});
//# sourceMappingURL=profile.js.map