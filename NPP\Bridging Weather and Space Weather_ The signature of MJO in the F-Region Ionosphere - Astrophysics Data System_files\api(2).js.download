function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var r,o=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,r)),o}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}define([],function(){var r={CURRENT_QUERY_UPDATED:"[api] CURRENT_QUERY_UPDATED",FETCH_DATA:"[api] FETCH_DATA",QUERY_PROVIDED:"[api] QUERY_PROVIDED",RECEIVED_RESPONSE:"[api] RECEIVED_RESPONSE",SEND_ANALYTICS:"[api] SEND_ANALYTICS",SET_BIBCODE:"[api] SET_BIBCODE",FALLBACK_ON_ERROR:"[api] FALLBACK_ON_ERROR"},o={bibcode:null,query:null};return{reducer:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:o,t=1<arguments.length?arguments[1]:void 0;switch(t.type){case r.SET_BIBCODE:return _objectSpread(_objectSpread({},e),{},{bibcode:t.result});case r.CURRENT_QUERY_UPDATED:return _objectSpread(_objectSpread({},e),{},{query:t.result});case r.RESET:return o;default:return e}},initialState:o,actions:r,displayDocuments:function(e){return{type:r.QUERY_PROVIDED,result:e}},processResponse:function(e){return{type:r.RECEIVED_RESPONSE,result:e}},fallbackOnError:function(){return{type:r.FALLBACK_ON_ERROR}},setBibcode:function(e){return{type:r.SET_BIBCODE,result:e}}}});
//# sourceMappingURL=api.js.map