<!DOCTYPE html>
<!-- saved from url=(0064)https://static.addtoany.com/menu/sm.25.html#type=core&event=load -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>A2A</title><meta name="robots" content="noindex"><script>
!function(e){var o=document.cookie,t=0<o.length;var n,a={feed:[],page:[]};t&&["feed","page"].forEach(function(e){var t=e+"_services",n=o.indexOf(t+"=");-1!=n&&(-1==(t=o.indexOf(";",n=n+t.length+1))&&(t=o.length),n=decodeURIComponent(o.substring(n,t)),a[e]=n.split(","))}),n={a2a:!0,h1:(t=!1,"function"==typeof performance.getEntriesByType&&(n=(n=performance.getEntriesByType("navigation")[0])&&n.nextHopProtocol?n.nextHopProtocol:"",t=/^http\/1/.test(n)),t),user_services:{feed:a.feed,page:a.page}},e.postMessage&&e.parent.postMessage(n,"*")}(window);</script><meta id="dcngeagmmhegagicpcmpinaoklddcgon"></head><body style="background-color:transparent" data-new-gr-c-s-check-loaded="14.1255.0" data-gr-ext-installed=""></body><grammarly-desktop-integration data-grammarly-shadow-root="true"><template shadowrootmode="open"><style>
      div.grammarly-desktop-integration {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select:none;
        user-select:none;
      }

      div.grammarly-desktop-integration:before {
        content: attr(data-content);
      }
    </style><div aria-label="grammarly-integration" role="group" tabindex="-1" class="grammarly-desktop-integration" data-content="{&quot;mode&quot;:&quot;full&quot;,&quot;isActive&quot;:true,&quot;isUserDisabled&quot;:false}"></div></template></grammarly-desktop-integration></html>