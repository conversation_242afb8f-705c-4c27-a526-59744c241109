(window.webpackJsonp=window.webpackJsonp||[]).push([[21],{191:function(e,t){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector)},192:function(e,t,i){"use strict";i(191);Element.prototype.closest||(Element.prototype.closest=function(e){var t=this;if(!document.documentElement.contains(t))return null;do{if(t.matches(e))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return null})},205:function(e,t,i){"use strict";i(212);!function(e){var t=null,i=null;e.stickyElements.selectors.header=".pageHeader",e.test.isIE()&&(t=new MutationObserver((function(e){e.forEach((function(e){"absolute"===$(e.target).css("position")&&$(e.target).css("top","0")}))})),(i=document.querySelector(".article-row-right"))&&t.observe(i,{attributes:!0,attributeFilter:["style"]}))}(UX)},212:function(e,t,i){"use strict";var n=i(35),o=i(26),r=i(8),s=i.n(r),a=i(9),c=i.n(a),l=!1;if("undefined"!=typeof window&&window.getComputedStyle){var d=document.createElement("div");["","-webkit-","-moz-","-ms-"].some((function(e){try{d.style.position=e+"sticky"}catch(e){}return""!=d.style.position}))&&(l=!0)}else l=!0;var h=!1,f="undefined"!=typeof ShadowRoot,u={top:null,left:null},p=[];function extend(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])}function parseNumeric(e){return parseFloat(e)||0}function getDocOffsetTop(e){for(var t=0;e;)t+=e.offsetTop,e=e.offsetParent;return t}var m=function(){return c()((function Sticky(e){if(s()(this,Sticky),!(e instanceof HTMLElement))throw new Error("First argument must be HTMLElement");if(p.some((function(t){return t._node===e})))throw new Error("Stickyfill is already applied to this node");this._node=e,this._stickyMode=null,this._active=!1,p.push(this),this.refresh()}),[{key:"refresh",value:function refresh(){if(!l&&!this._removed){this._active&&this._deactivate();var e=this._node,t=getComputedStyle(e),i={position:t.position,top:t.top,display:t.display,marginTop:t.marginTop,marginBottom:t.marginBottom,marginLeft:t.marginLeft,marginRight:t.marginRight,cssFloat:t.cssFloat};if(!isNaN(parseFloat(i.top))&&"table-cell"!=i.display&&"none"!=i.display){this._active=!0;var n=e.style.position;"sticky"!=t.position&&"-webkit-sticky"!=t.position||(e.style.position="static");var o=e.parentNode,r=f&&o instanceof ShadowRoot?o.host:o,s=e.getBoundingClientRect(),a=r.getBoundingClientRect(),c=getComputedStyle(r);this._parent={node:r,styles:{position:r.style.position},offsetHeight:r.offsetHeight},this._offsetToWindow={left:s.left,right:document.documentElement.clientWidth-s.right},this._offsetToParent={top:s.top-a.top-parseNumeric(c.borderTopWidth),left:s.left-a.left-parseNumeric(c.borderLeftWidth),right:-s.right+a.right-parseNumeric(c.borderRightWidth)},this._styles={position:n,top:e.style.top,bottom:e.style.bottom,left:e.style.left,right:e.style.right,width:e.style.width,marginTop:e.style.marginTop,marginLeft:e.style.marginLeft,marginRight:e.style.marginRight};var d=parseNumeric(i.top);this._limits={start:s.top+window.pageYOffset-d,end:a.top+window.pageYOffset+r.offsetHeight-parseNumeric(c.borderBottomWidth)-e.offsetHeight-d-parseNumeric(i.marginBottom)};var h=c.position;"absolute"!=h&&"relative"!=h&&(r.style.position="relative"),this._recalcPosition();var u=this._clone={};u.node=document.createElement("div"),extend(u.node.style,{width:s.right-s.left+"px",height:s.bottom-s.top+"px",marginTop:i.marginTop,marginBottom:i.marginBottom,marginLeft:i.marginLeft,marginRight:i.marginRight,cssFloat:i.cssFloat,padding:0,border:0,borderSpacing:0,fontSize:"1em",position:"static"}),o.insertBefore(u.node,e),u.docOffsetTop=getDocOffsetTop(u.node)}}}},{key:"_recalcPosition",value:function _recalcPosition(){if(this._active&&!this._removed){var e=u.top<=this._limits.start?"start":u.top>=this._limits.end?"end":"middle";if(this._stickyMode!=e){switch(e){case"start":extend(this._node.style,{position:"absolute",left:this._offsetToParent.left+"px",right:this._offsetToParent.right+"px",top:this._offsetToParent.top+"px",bottom:"auto",width:"auto",marginLeft:0,marginRight:0,marginTop:0});break;case"middle":extend(this._node.style,{position:"fixed",left:this._offsetToWindow.left+"px",right:this._offsetToWindow.right+"px",top:this._styles.top,bottom:"auto",width:"auto",marginLeft:0,marginRight:0,marginTop:0});break;case"end":extend(this._node.style,{position:"absolute",left:this._offsetToParent.left+"px",right:this._offsetToParent.right+"px",top:"auto",bottom:0,width:"auto",marginLeft:0,marginRight:0})}this._stickyMode=e}}}},{key:"_fastCheck",value:function _fastCheck(){this._active&&!this._removed&&(Math.abs(getDocOffsetTop(this._clone.node)-this._clone.docOffsetTop)>1||Math.abs(this._parent.node.offsetHeight-this._parent.offsetHeight)>1)&&this.refresh()}},{key:"_deactivate",value:function _deactivate(){var e=this;this._active&&!this._removed&&(this._clone.node.parentNode.removeChild(this._clone.node),delete this._clone,extend(this._node.style,this._styles),delete this._styles,p.some((function(t){return t!==e&&t._parent&&t._parent.node===e._parent.node}))||extend(this._parent.node.style,this._parent.styles),delete this._parent,this._stickyMode=null,this._active=!1,delete this._offsetToWindow,delete this._offsetToParent,delete this._limits)}},{key:"remove",value:function remove(){var e=this;this._deactivate(),p.some((function(t,i){if(t._node===e._node)return p.splice(i,1),!0})),this._removed=!0}}])}(),g={stickies:p,Sticky:m,forceSticky:function forceSticky(){l=!1,init(),this.refreshAll()},addOne:function addOne(e){if(!(e instanceof HTMLElement)){if(!e.length||!e[0])return;e=e[0]}for(var t=0;t<p.length;t++)if(p[t]._node===e)return p[t];return new m(e)},add:function add(e){if(e instanceof HTMLElement&&(e=[e]),e.length){for(var t=[],i=function _loop(){var i=e[n];return i instanceof HTMLElement?p.some((function(e){if(e._node===i)return t.push(e),!0}))?0:void t.push(new m(i)):(t.push(void 0),0)},n=0;n<e.length;n++)i();return t}},refreshAll:function refreshAll(){p.forEach((function(e){return e.refresh()}))},removeOne:function removeOne(e){if(!(e instanceof HTMLElement)){if(!e.length||!e[0])return;e=e[0]}p.some((function(t){if(t._node===e)return t.remove(),!0}))},remove:function remove(e){if(e instanceof HTMLElement&&(e=[e]),e.length)for(var t=function _loop2(){var t=e[i];p.some((function(e){if(e._node===t)return e.remove(),!0}))},i=0;i<e.length;i++)t()},removeAll:function removeAll(){for(;p.length;)p[0].remove()}};function init(){var e,t,i;h||(h=!0,checkScroll(),window.addEventListener("scroll",checkScroll),window.addEventListener("resize",g.refreshAll),window.addEventListener("orientationchange",g.refreshAll),"hidden"in document?(t="hidden",i="visibilitychange"):"webkitHidden"in document&&(t="webkitHidden",i="webkitvisibilitychange"),i?(document[t]||startFastCheckTimer(),document.addEventListener(i,(function(){document[t]?function stopFastCheckTimer(){clearInterval(e)}():startFastCheckTimer()}))):startFastCheckTimer());function checkScroll(){window.pageXOffset!=u.left?(u.top=window.pageYOffset,u.left=window.pageXOffset,g.refreshAll()):window.pageYOffset!=u.top&&(u.top=window.pageYOffset,u.left=window.pageXOffset,p.forEach((function(e){return e._recalcPosition()})))}function startFastCheckTimer(){e=setInterval((function(){p.forEach((function(e){return e._fastCheck()}))}),500)}}l||init();var v=g;i(84),i(191),i(192);!function(e){e.stickyElements={selectors:{header:".header",scrollThenFix:".scrollThenFix",stickyElements:[".fixed-element"],dynamicHeader:[".pb-ad",".pb-las",".cookiePolicy-popup",".header__dropzone"],stickoParent:".sticko__parent"},header:null,isHeaderSticky:!0,dynamicHeaderHeight:!1,vPort:"screen-sm",isMobile:!1,init:function init(){this.header=document.querySelector(this.selectors.header),this.isHeaderSticky&&this.selectors.stickyElements.unshift(this.selectors.header),this._isDynamicHeaderHeight(),this.start(),this.responsive(),this.extra()},responsive:function responsive(){var e=this;$(document).on("smartResize",(function(){return e.start()})),$(document).on("".concat(this.vPort,"-on"),(function(){return e.isMobile=!0})),$(document).on("".concat(this.vPort,"-off"),(function(){return e.isMobile=!1}))},extra:function extra(){},start:function start(){var e=this;if(this.computeHeaderHeight(),this.selectors.stickyElements.length){var t=document.querySelectorAll(this.selectors.stickyElements.join(","));if(!t||!t.length)return;t.forEach((function(t){var i=t.matches(e.selectors.header);e.makeSticky(t,i)}))}},makeSticky:function makeSticky(e,t){this.setTopValue(e,t),this.postStickyActions(e,t),n.a&&v.addOne(e)},setTopValue:function setTopValue(e,t){this._canSetTopValue(e,t)&&(e.style.top=this.headerHeight+"px")},postStickyActions:function postStickyActions(e,t){this._has(e,this.selectors.stickoParent,!0)&&this.stickoActions(e),this._has(e,this.selectors.scrollThenFix)&&this.scrollThenFixActions(e,t)},stickoActions:function stickoActions(e){var t=e.querySelector(this.selectors.stickoParent)||e;this.isHeaderSticky?t.style.height="calc(100vh - ".concat(this.headerHeight,"px)"):t.style.height="100vh",document.addEventListener("change:auto-hide-bar",(function(){e.style.top="".concat(Object(o.a)(),"px")}))},scrollThenFixActions:function scrollThenFixActions(e,t){e.classList.add("fixed-element");var i=e.querySelector(this.selectors.scrollThenFix).offsetTop;i-=t?0:this.headerHeight,e.style.top="".concat(-1*i,"px")},_isDynamicHeaderHeight:function _isDynamicHeaderHeight(){if(!this.dynamicHeaderHeight)for(var e=0;e<this.selectors.dynamicHeader.length&&(this.dynamicHeaderHeight=this._has(this.header,this.selectors.dynamicHeader[e],!0),!this.dynamicHeaderHeight);e++);},computeHeaderHeight:function computeHeaderHeight(){this.headerHeight=Object(o.a)(this.selectors.header)},_has:function _has(e,t,i){return e&&(null!==e.querySelector(t)||i&&e.matches(t))},_canSetTopValue:function _canSetTopValue(e,t){return!t&&this.dynamicHeaderHeight&&null===e.closest(this.selectors.header)}}}(UX)},323:function(e,t,i){"use strict";i.r(t);i(205),i(55);$((function(){var e=document.querySelectorAll(".skip-links button");if(e.length){var t=function handleSkip(e){var t=e.currentTarget.getAttribute("data-id"),i=document.querySelector(t),n=null==i?void 0:i.getBoundingClientRect();window.scrollTo({top:n.top+window.scrollY,behavior:"smooth"}),i.focus()};e.forEach((function(e){e.addEventListener("click",t)}))}$(".logo-container a, .logo-container-mobile a, .institution > a").attr("tabindex","2"),$(".indivLogin").on("click",(function(){var e=this;setTimeout((function(){$(e).attr("aria-expanded",!$(".navigation-login-dropdown-container").hasClass("hidden"))}),0)})),$(document).on("click",(function(){setTimeout((function(){$(".indivLogin").attr("aria-expanded",!$(".navigation-login-dropdown-container").hasClass("hidden"))}),0)}));var i=$(".institution-wrapper-mobile"),n=$(".institution-wrapper"),o=!1;n.removeAttr("aria-hidden"),i.attr("aria-hidden","true"),$(document).on("screen-md-on",(function(){o=!0,n.attr("aria-hidden","true"),i.removeAttr("aria-hidden")})),$(document).on("screen-md-off",(function(){o=!1,n.removeAttr("aria-hidden"),i.attr("aria-hidden","true")})),$(".pb-ui").length&&$("[aria-haspop]").length&&$.each($("[aria-haspop]"),(function(e,t){$(t).removeAttr("aria-haspop").attr("aria-haspopup","true")})),$(window).on("load orientationchange",(function(){$(".fixed-element").length&&o&&setTimeout((function(){UX.stickyElements.init()}),150)})),$(window).on("resize",(function(){$(".fixed-element").length&&UX.stickyElements.init()}));var r=function removeBackdropEl(){UX.utils.convertToArray(document.querySelectorAll(".modal-backdrop.in")).forEach((function(e){e.parentNode.removeChild(e)}))},s=UX.utils.convertToArray(document.querySelectorAll(".modal__dialog .close"));s.length&&s.forEach((function(e){e.addEventListener("click",(function(e){r()}))}));var a=UX.utils.convertToArray(document.querySelectorAll(".modal"));a.length&&a.forEach((function(e){e.addEventListener("click",(function(e){e.target===e.currentTarget&&r()}))})),document.addEventListener("keydown",(function(e){var t=UX.utils.convertToArray(document.querySelectorAll(".modal-backdrop.in"));t.length&&e.key.indexOf("Esc")>-1&&(t.forEach((function(e){e.parentNode.removeChild(e)})),UX.modal.$close&&UX.modal.$close.trigger("click"))}));var c=document.querySelector(".js--fixed-switch"),l=c&&c.querySelector(".scrollable-element");if(c&&l)if(UX.test.isIE()){var d=l.parentElement,h=l.offsetHeight,f=l.offsetTop,u=d.style.paddingTop,p=function setScrollStatus(){window.pageYOffset>f&&!l.classList.contains("js--fixed")?(l.classList.add("js--fixed"),d.style.paddingTop="".concat(h,"px")):window.pageYOffset<=f&&l.classList.contains("js--fixed")&&(l.classList.remove("js--fixed"),d.style.paddingTop=u)};p(),window.addEventListener("scroll",p)}else c.classList.add("fixed-element"),l.classList.add("scrollThenFix"),UX.stickyElements.init();var m=UX.utils.convertToArray(document.querySelectorAll(".advert-rail, .advert-leaderboard"));if(m.length){window.addEventListener("load",(function hideAdsIfCorrupted(){m.forEach((function(e){setTimeout((function(){void 0===e.dataset.googleQueryId&&"none"!==e.style.display&&(e.style.display="none")}),1e3)}))}))}var g=document.querySelector(".show-request-reset-password");null==g||g.addEventListener("click",(function(){var e=document.querySelector(".g-recaptcha");if(e){var t=e.parentNode;e.remove(),setTimeout((function(){null==t||t.append(e)}),5e3)}}));var v=Array.from(document.querySelectorAll(".pb-las .advert"));v&&setTimeout((function(){v.forEach((function(e){e.className+=" advert--no-spinner"}))}),3e3)}))}}]);
//# sourceMappingURL=headerAlt-292f3a65a55ab0b0afa4.js.map