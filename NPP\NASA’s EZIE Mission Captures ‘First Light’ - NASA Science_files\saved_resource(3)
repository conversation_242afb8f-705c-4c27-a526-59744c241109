(()=>{var e,_,r={635:e=>{"use strict";e.exports=wp.i18n}},i={};function __webpack_require__(e){var _=i[e];if(void 0!==_)return _.exports;var t=i[e]={exports:{}};return r[e](t,t.exports,__webpack_require__),t.exports}__webpack_require__.m=r,__webpack_require__.n=e=>{var _=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(_,{a:_}),_},__webpack_require__.d=(e,_)=>{for(var r in _)__webpack_require__.o(_,r)&&!__webpack_require__.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:_[r]})},__webpack_require__.f={},__webpack_require__.e=e=>Promise.all(Object.keys(__webpack_require__.f).reduce(((_,r)=>(__webpack_require__.f[r](e,_),_)),[])),__webpack_require__.u=e=>"chunks/"+{601:"missing-or-empty-page-title",845:"add-label-to-unllabeled-form-fields"}[e]+"."+{601:"4959e5b06fa76f7c234a",845:"f7620ac8d625de10f467"}[e]+".js",__webpack_require__.miniCssF=e=>{},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),__webpack_require__.o=(e,_)=>Object.prototype.hasOwnProperty.call(e,_),e={},_="accessibility-checker-pro:",__webpack_require__.l=(r,i,t,a)=>{if(e[r])e[r].push(i);else{var c,o;if(void 0!==t)for(var u=document.getElementsByTagName("script"),n=0;n<u.length;n++){var p=u[n];if(p.getAttribute("src")==r||p.getAttribute("data-webpack")==_+t){c=p;break}}c||(o=!0,(c=document.createElement("script")).charset="utf-8",c.timeout=120,__webpack_require__.nc&&c.setAttribute("nonce",__webpack_require__.nc),c.setAttribute("data-webpack",_+t),c.src=r),e[r]=[i];var b=(_,i)=>{c.onerror=c.onload=null,clearTimeout(l);var t=e[r];if(delete e[r],c.parentNode&&c.parentNode.removeChild(c),t&&t.forEach((e=>e(i))),_)return _(i)},l=setTimeout(b.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=b.bind(null,c.onerror),c.onload=b.bind(null,c.onload),o&&document.head.appendChild(c)}},__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;__webpack_require__.g.importScripts&&(e=__webpack_require__.g.location+"");var _=__webpack_require__.g.document;if(!e&&_&&(_.currentScript&&(e=_.currentScript.src),!e)){var r=_.getElementsByTagName("script");if(r.length)for(var i=r.length-1;i>-1&&!e;)e=r[i--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),__webpack_require__.p=e})(),(()=>{var e={976:0};__webpack_require__.f.j=(_,r)=>{var i=__webpack_require__.o(e,_)?e[_]:void 0;if(0!==i)if(i)r.push(i[2]);else{var t=new Promise(((r,t)=>i=e[_]=[r,t]));r.push(i[2]=t);var a=__webpack_require__.p+__webpack_require__.u(_),c=new Error;__webpack_require__.l(a,(r=>{if(__webpack_require__.o(e,_)&&(0!==(i=e[_])&&(e[_]=void 0),i)){var t=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;c.message="Loading chunk "+_+" failed.\n("+t+": "+a+")",c.name="ChunkLoadError",c.type=t,c.request=a,i[1](c)}}),"chunk-"+_,_)}};var _=(_,r)=>{var i,t,[a,c,o]=r,u=0;if(a.some((_=>0!==e[_]))){for(i in c)__webpack_require__.o(c,i)&&(__webpack_require__.m[i]=c[i]);if(o)o(__webpack_require__)}for(_&&_(r);u<a.length;u++)t=a[u],__webpack_require__.o(e,t)&&e[t]&&e[t][0](),e[t]=0},r=self.webpackChunkaccessibility_checker_pro=self.webpackChunkaccessibility_checker_pro||[];r.forEach(_.bind(null,0)),r.push=_.bind(null,r.push.bind(r))})(),(()=>{const e=window.edac_frontend_fixes||{};e?.missing_or_empty_page_title?.enabled&&__webpack_require__.e(601).then(__webpack_require__.bind(__webpack_require__,138)).then((e=>{e.default()})),e?.add_label_to_unlabelled_form_fields?.enabled&&__webpack_require__.e(845).then(__webpack_require__.bind(__webpack_require__,689)).then((e=>{e.default()}))})()})();
//# sourceMappingURL=frontendFixes.bundle.js.map;
;
/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,d)=>{for(var o in d)e.o(d,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:d[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},t={};function d(e){"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",e):e())}e.d(t,{default:()=>d}),(window.wp=window.wp||{}).domReady=t.default})();;
