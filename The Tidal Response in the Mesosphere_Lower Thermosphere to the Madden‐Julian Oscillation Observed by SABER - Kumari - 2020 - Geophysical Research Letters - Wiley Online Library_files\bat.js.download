!function(){function UET(e){this.stringExists=function(e){return e&&e.length>0},this.domain="bat.bing.com",this.domainCl="bat.bing.net",this.URLLENGTHLIMIT=4096,this.pageLoadEvt="pageLoad",this.customEvt="custom",this.pageViewEvt="page_view",void 0===e.Ver||"1"!==e.Ver&&1!==e.Ver?e.Ver=2:e.Ver=1,this.uetConfig={},this.uetConfig.enableAdStorage=!0,this.uetConfig.consent={enabled:!1,adStorageAllowed:!0,adStorageUpdated:!1,timeoutId:null,waitForUpdate:0,enforced:!1},this.uetConfig.tcf={enabled:!1,vendorId:1126,hasVendor:!1,hasLoaded:!1,auto:!1,listenerId:void 0,timeoutId:null,gdprApplies:void 0,adStorageAllowed:void 0,measurementAllowed:void 0,personalizationAllowed:void 0},this.uetConfig.cusig={hasLoaded:!1,timeoutId:null,blob:{}},this.beaconParams={},this.supportsCORS=this.supportsXDR=!1,this.paramValidations={string_currency:{type:"regex",regex:/^[a-zA-Z]{3}$/,error:"{p} value must be ISO standard currency code"},number:{type:"num",digits:3,max:999999999999},integer:{type:"num",digits:0,max:999999999999},hct_los:{type:"num",digits:0,max:30},date:{type:"regex",regex:/^\d{4}-\d{2}-\d{2}$/,error:"{p} value must be in YYYY-MM-DD date format"},pid:{type:"pid"},enum:{type:"enum",error:"{p} value must be one of the allowed values"},array:{type:"array",error:"{p} must be an array with 1+ elements"},object:{type:"object",error:"{p} must be an object with 1+ elements"}},this.knownParams={event_action:{beacon:"ea"},event_category:{beacon:"ec"},event_label:{beacon:"el"},event_value:{type:"number",beacon:"ev"},event_id:{},page_title:{},page_location:{},page_path:{},ecomm_prodid:{beacon:"prodid"},ecomm_pagetype:{type:"enum",values:["home","searchresults","category","product","cart","purchase","other"],beacon:"pagetype"},ecomm_totalvalue:{type:"number"},ecomm_category:{},ecomm_query:{},ecomm_exp:{},hct_base_price:{type:"number"},hct_booking_xref:{},hct_checkin_date:{type:"date"},hct_checkout_date:{type:"date"},hct_length_of_stay:{type:"hct_los"},hct_partner_hotel_id:{},hct_total_price:{type:"number"},hct_pagetype:{type:"enum",values:["home","searchresults","offerdetail","conversionintent","conversion","property","cart","purchase","cancel","other"]},travel_destid:{},travel_originid:{},travel_pagetype:{type:"enum",values:["home","searchresults","offerdetail","conversionintent","conversion","cancel","other"]},travel_startdate:{type:"date"},travel_enddate:{type:"date"},travel_totalvalue:{type:"number"},flight_destid:{},flight_originid:{},flight_pagetype:{type:"enum",values:["home","searchresults","offerdetail","cart","purchase","cancel","other"]},flight_startdate:{type:"date"},flight_enddate:{type:"date"},flight_totalvalue:{type:"number"},affiliation:{},brs_response_id:{},checkout_option:{},checkout_step:{type:"integer"},content_id:{},content_type:{},coupon:{},currency:{type:"string_currency",beacon:"gc"},description:{},fatal:{},method:{},name:{},revenue_value:{type:"number",beacon:"gv"},screen_name:{},search_term:{},shipping:{type:"number"},tax:{type:"number"},transaction_id:{},rep:{},vid:{},tpp:{},gtm_tag_source:{},items:{type:"array"},"items.brand":{},"items.category":{},"items.creative_name":{},"items.creative_slot":{},"items.id":{},"items.list_name":{},"items.list_position":{type:"integer"},"items.location_id":{},"items.name":{},"items.price":{type:"number"},"items.quantity":{type:"number"},"items.variant":{},promotions:{type:"array"},"promotions.creative_name":{},"promotions.creative_slot":{},"promotions.id":{},"promotions.name":{},pid:{type:"object"},"pid.em":{type:"pid"},"pid.email":{type:"pid",beacon:"em"},"pid.ph":{type:"pid"},"pid.phone_number":{type:"pid",beacon:"ph"}},this.knownEvents={add_payment_info:[],add_to_cart:["revenue_value","currency","items"],add_to_wishlist:["revenue_value","currency","items"],begin_checkout:["revenue_value","currency","items","coupon"],checkout_progress:["revenue_value","currency","items","coupon","checkout_step","checkout_option"],exception:["description","fatal"],generate_lead:["revenue_value","currency","transaction_id"],login:["method"],page_view:["page_title","page_location","page_path","rep","tpp","gtm_tag_source","pid"],purchase:["transaction_id","revenue_value","currency","tax","shipping","items","coupon"],refund:["transaction_id","revenue_value","currency","tax","shipping","items"],remove_from_cart:["revenue_value","currency","items"],screen_view:["screen_name"],search:["search_term"],select_content:["items","promotions","content_type","content_id"],set_checkout_option:["checkout_step","checkout_option"],share:["method","content_type","content_id"],sign_up:["method"],view_item:["items"],view_item_list:["items"],view_promotion:["promotions"],view_search_results:["search_term"]},this.pageLevelParams={},this.legacyValidPageLoadKeyNames={rep:1,pid:1},this.legacyValidCustomEventKeyNames={ec:1,el:1,ev:1,ea:1,gv:1,gc:1,prodid:1,pagetype:1},this.ambiguousValidCustomEventKeyNames={ecomm_prodid:1,ecomm_pagetype:1,rep:1,pid:1},this.validRetailPageTypeValues={home:1,searchresults:1,category:1,product:1,cart:1,purchase:1,other:1},this.invalidKeyException="Invalid data: Key Name: ",this.invalidEventException="Invalid event type: Event Type: ",this.invalidPageTypeException="Invalid pagetype value: ",this.invalidProdIdException="The prodid value must be within 1 to 50 characters.",this.missingPageTypeException="The pagetype parameter is required when you include the prodid parameter.",this.goalCurrencyFormatException="gc value must be ISO standard currency code",this.missingHotelParametersException="The hotel total price (hct_total_price) and currency parameters are required when you include other hotel parameters.",this.hotelVariableRevenueException="The variable revenue parameter (revenue_value) cannot be sent when you include other hotel parameters.",this.evq=e.q instanceof Array?e.q:[],this.evqDispatch=!1,this.evqCDispatch=!1,this.processEarly={set:1,consent:1,config:1},this.pageLoadDispatch=!1,this.documentLoaded=!1,this.deferLoad=!1,this.uetLoaded=!1,this.eventPushQueue=[],this.uetInstance=this,this.domainName=null,this.sessionCookieName="_uetsid",this.sessionExpirationTime=86400,this.visitorExpirationTime=33696e3,this.cookieIdMaxLength=36,this.insightsDataMaxLength=128,this.insightsCookieMaxLength=this.cookieIdMaxLength+1+this.insightsDataMaxLength,this.msClkIdCookieValuePrefix="_uet",this.msClkIdCookieName="_uetmsclkid",this.msClkIdParamName="msclkid",this.msClkIdExpirationTime=7776e3,this.lengthMsClkId=32,this.msClkId=null,this.subVersion=2,this.previousPage=null,this.snippetEventQueue=[],this.midOverride=!1,this.plOverride=null,this.tzEnforced=["Europe/Amsterdam"],this.checkuetHostdocumentload=function(){var e=this.uetInstance;if(e.documentLoaded||!document.body||document.readyState&&"interactive"!==document.readyState&&"complete"!==document.readyState&&"loaded"!==document.readyState||e.deferLoad||(e.documentLoaded=!0),e.documentLoaded)if(e.uetConfig.disableContainer||e.uetConfig.cusig.hasLoaded||e.uetConfig.cusig.timeoutId){if(!1===e.evqCDispatch){e.dispatchq(!0),e.evqCDispatch=!0;for(var t=0;t<e.eventPushQueue.length;t++)e.eventPushQueue[t]instanceof Array&&e.processEarly[e.eventPushQueue[t][0]]&&e._push(e.eventPushQueue[t])}if(e.uetConfig.consent.enabled&&!e.uetConfig.consent.timeoutId&&e.uetConfig.consent.waitForUpdate>0)e.uetConfig.consent.timeoutId=setTimeout((function(){e.checkuetHostdocumentload()}),e.uetConfig.consent.waitForUpdate);else if(!e.uetConfig.tcf.enabled||e.uetConfig.tcf.hasLoaded||e.uetConfig.tcf.timeoutId){if(!e.uetLoaded){if(e.enforceConsent(),e.eventPushQueue.length>0){for(t=0;t<e.eventPushQueue.length;t++)e.eventPushQueue[t]instanceof Array&&!e.processEarly[e.eventPushQueue[t][0]]&&e._push(e.eventPushQueue[t]);e.eventPushQueue=[]}e.uetLoaded=!0}}else e.uetConfig.tcf.timeoutId=setTimeout((function(){e.checkuetHostdocumentload()}),500)}else e.uetConfig.cusig.timeoutId=setTimeout((function(){e.preEnforce(),e.checkuetHostdocumentload()}),1500);else setTimeout((function(){e.checkuetHostdocumentload()}),5)},this.tcfSubscribe=function(e){this.uetConfig.tcf.listenerId||"function"==typeof window.__tcfapi&&(this.uetConfig.tcf.enabled=!0,this.uetConfig.tcf.auto=e,window.__tcfapi("addEventListener",2,this.tcfCallback.bind(this)))},this.tcfCallback=function(e,t){if(t){e.listenerId&&(this.uetConfig.tcf.listenerId=e.listenerId),this.uetConfig.tcf.adStorageAllowed=void 0,this.uetConfig.tcf.measurementAllowed=void 0,this.uetConfig.tcf.personalizationAllowed=void 0;try{if(this.uetConfig.tcf.gdprApplies=e.gdprApplies,!0===e.gdprApplies){var cc=function(t){return!0===e.purpose.consents[t]||!0===e.purpose.legitimateInterests[t]};this.uetConfig.tcf.hasVendor=!0===e.vendor.consents[this.uetConfig.tcf.vendorId]||!0===e.vendor.legitimateInterests[this.uetConfig.tcf.vendorId],this.uetConfig.tcf.hasVendor&&(this.uetConfig.tcf.adStorageAllowed=cc(1),this.uetConfig.tcf.measurementAllowed=cc(7)&&cc(9)&&cc(10),this.uetConfig.tcf.personalizationAllowed=cc(3)&&cc(4))}}catch(i){}this.uetConfig.tcf.hasLoaded||(this.uetConfig.tcf.hasLoaded=!0,clearTimeout(this.uetConfig.tcf.timeoutId),this.uetConfig.tcf.timeoutId=null),this.handleCookieIds(),!0===this.uetLoaded&&this.enforceConsent(),!1!==this.uetConfig.tcf.auto&&!0!==this.uetConfig.tcf.hasVendor||this.fireConsentPing("tcf"),!0!==this.uetLoaded&&this.checkuetHostdocumentload()}},this.getClUrl=function(e){return!1===this.uetConfig.enableAdStorage||!1===this.uetConfig.uetLoaded?e.replace(this.domain,this.domainCl):e},this.isAdStorageAllowed=function(){return!(this.beaconParams.Ver<2||!1===this.uetConfig.cookieAllowed||!1===this.uetConfig.enableAdStorage)},this.sanitizeTagId=function(e){1!==e.Ver&&this.stringExists(e.tagId)&&!this.stringExists(e.ti)&&(e.ti=e.tagId.toString()),e.ti&&(e.ti=e.ti.toString(),e.ti=e.ti.replace(/[^\w-]/g,""))},this.isDuplicate=function(e){try{return!(!e||!e.q||"object"!=typeof e.q)&&(!(2!==e.Ver||!e.q.beaconParams||2!==e.q.beaconParams.Ver)&&!(!e.q.beaconParams.ti||e.ti!==e.q.beaconParams.ti))}catch(t){return!1}},this.loadConfig=function(){if(this.uetConfig.cookieAllowed=!0,!1===e.storeConvTrackCookies&&(this.uetConfig.cookieAllowed=!1),this.uetConfig.cookieDomain="",e.hasOwnProperty("cookieDomain")&&e.cookieDomain&&"string"==typeof e.cookieDomain&&(this.uetConfig.cookieDomain=e.cookieDomain),this.uetConfig.cookieFlags="",e.hasOwnProperty("cookieFlags")&&e.cookieFlags&&"string"==typeof e.cookieFlags&&(this.uetConfig.cookieFlags=e.cookieFlags),this.uetConfig.navTimingApi=!1,!0===e.navTimingApi&&(this.uetConfig.navTimingApi=!0),this.uetConfig.errorBeaconLevel=0,e.hasOwnProperty("errorBeaconLevel")){var t=e.errorBeaconLevel;"number"==typeof t&&t%1==0&&t>=0&&t<=2&&(this.uetConfig.errorBeaconLevel=t)}this.uetConfig.disableAutoPageView=!1,!0===e.disableAutoPageView&&(this.uetConfig.disableAutoPageView=!0),this.uetConfig.disableVisibilityEvents=!1,!0===e.disableVisibilityEvents&&(this.uetConfig.disableVisibilityEvents=!0),this.uetConfig.removeQueryFromUrls=!1,!0===e.removeQueryFromUrls&&(this.uetConfig.removeQueryFromUrls=!0),this.uetConfig.allRep=!1,!0===e.allRep&&(this.uetConfig.allRep=!0);var i="_uetmsdns";(e.hasOwnProperty("msDnsCookie")&&e.msDnsCookie&&"string"==typeof e.msDnsCookie&&(i=e.msDnsCookie),this.uetConfig.msDns="1"===this.getCookie(i,"",1),this.uetConfig.disableUetVid=!1,!0===e.disableUetVid&&(this.uetConfig.disableUetVid=!0),this.uetConfig.vidCookie="_uetvid",this.uetConfig.uidCookie="_uetuid",e.hasOwnProperty("uidCookie")&&e.uidCookie&&"string"==typeof e.uidCookie&&(this.uetConfig.uidCookie=e.uidCookie),this.uetConfig.gtmTagSource=void 0,e.hasOwnProperty("gtmTagSource")&&e.gtmTagSource&&"string"==typeof e.gtmTagSource&&(this.uetConfig.gtmTagSource=e.gtmTagSource),this.uetConfig.gtagPid=!1,e.hasOwnProperty("pagePid")&&e.pagePid&&"object"==typeof e.pagePid?this.pageLevelParams.pid=e.pagePid:e.hasOwnProperty("gtagPid")&&!0===e.gtagPid&&(this.uetConfig.gtagPid=!0),this.uetConfig.enableAutoSpaTracking=!1,!0===e.enableAutoSpaTracking&&(this.uetConfig.enableAutoSpaTracking=!0),this.uetConfig.enableAutoConsent=!0,!1===e.enableAutoConsent&&(this.uetConfig.enableAutoConsent=!1),this.uetConfig.disableContainer=!1,e.hasOwnProperty("disableContainer")&&(this.uetConfig.disableContainer=!0===e.disableContainer),e.hasOwnProperty("alt")&&(this.uetConfig.imgAlt=e.alt),e.hasOwnProperty("clarityProjectId")&&e.clarityProjectId&&"string"==typeof e.clarityProjectId)&&((p=document.createElement("script")).src="https://clarity.microsoft.com/js/"+encodeURIComponent(e.clarityProjectId),p.type="text/javascript",p.setAttribute("crossorigin","anonymous"),p.async=1,p.onload=this.clarityOnLoad,document.head.appendChild(p));void 0!==window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest&&(this.supportsCORS=!0),"undefined"!=typeof XDomainRequest&&(this.supportsXDR=!0);var o="https:",validBatDebug=function(e){return e.match(/^[0-9]{13}$/)||e.match(/^[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$/)};this.uetConfig.dbgCookie="_uetdbg";var n=this.getQueryParam(window.location.href,"bat_debug");this.stringExists(n)&&validBatDebug(n)?this.setCookie(this.uetConfig.dbgCookie,n,0,!0,!1,"",this.cookieIdMaxLength):n=this.getCookie(this.uetConfig.dbgCookie,"",this.cookieIdMaxLength),this.stringExists(n)&&validBatDebug(n)&&(this.uetConfig.batDebug=n);var s=0;1===e.Ver&&void 0!==e.advertiserId&&(s=e.advertiserId),this.postURL=o+"//"+this.domain+"/action/"+s,this.urlPrefix=this.postURL+"?",this.errPrefix=o+"//"+this.domainCl+"/action-err/"+s+"?",this.previewPrefix=o+"//"+this.domain+"/actionp/"+s+"?";var r=1!==e.Ver?{ti:0,Ver:0,tm:1,Tag:0,EXT_Data:0}:{Ver:0,tagId:0,Tag:0,Sig:0,EXT_Data:0};for(var h in e)r.hasOwnProperty(h)&&(this.beaconParams[h]=1===r[h]?encodeURIComponent(e[h]):e[h]);this.beaconParams.mid=this.getUuidV4(!0),this.beaconParams.bo=0;for(var d=void 0,u=0;u<5&&(d="ueto_"+this.makeRandomStr(10),window[d]);u++);if(this.stringExists(e.ti)){var l=window.navigator.userLanguage||window.navigator.language;this.stringExists(l)&&0===l.indexOf("de")&&-1!==["56004448","5798164","20132024","4000835","4074038"].indexOf(e.ti)&&(this.uetConfig.deBlock=!0,this.uetConfig.cookieAllowed=!1)}try{this.stringExists(d)&&!window[d]?window[d]=this.uetInstance:d=void 0}catch(w){}if(this.stringExists(e.ti)&&!this.uetConfig.disableContainer){var p=document.createElement("script"),g=o+"//"+this.domain+"/p/action/"+encodeURIComponent(e.ti)+".js";this.stringExists(this.uetConfig.batDebug)&&(g+="?bat_debug="+this.uetConfig.batDebug),p.src=g,p.type="text/javascript",p.async=1,d&&p.setAttribute("data-ueto",d),document.head.appendChild(p)}var m=[],f=this;if(navigator.userAgentData&&navigator.userAgentData.getHighEntropyValues&&"Windows"===navigator.userAgentData.platform&&m.push(navigator.userAgentData.getHighEntropyValues(["platformVersion"]).then((function(e){e.hasOwnProperty("platformVersion")&&(f.uetConfig.uach={},f.uetConfig.uach.pv=encodeURIComponent(e.platformVersion))}),(function(e){return new Error("Error requesting UA CH: "+e)}))),"cookieDeprecationLabel"in navigator&&m.push(navigator.cookieDeprecationLabel.getValue().then((function(e){f.stringExists(e)&&(f.uetConfig.cdl=encodeURIComponent(e))}),(function(e){return new Error("Error requesting cookieDeprecationLabel: "+e)}))),m.length>0&&window.Promise&&window.Promise.allSettled){this.deferLoad=!0;var v=setTimeout((function(){f.deferLoad=!1}),50);Promise.allSettled(m).then((function(e){f.deferLoad=!1,clearTimeout(v)}))}if(this.stringExists(this.uetConfig.batDebug)&&window.opener&&this.stringExists(e.ti)){var C="https://ui.ads.microsoft.com";if(document.referrer&&window.URL){var y=new URL(document.referrer);"https:"===y.protocol&&/\.microsoft\.com$/.test(y.hostname)&&(C=y.origin)}window.opener.postMessage({type:"AckBatDbgMode",tagId:e.ti},C)}},this.makeRandomStr=function(e){for(var t="",i=0;i<Math.ceil(e/2);i++)t+=this._SB(Math.floor(256*Math.random()));return t.slice(-e)},this.push=function(){var e,t,i=arguments,o=!1;if(1===i.length)if(e="event",i[0]===this.pageLoadEvt)t=[this.pageViewEvt,{}],o=!0;else{if(i[0]instanceof Array){if(!(i[0].length>0))return;i[0]=i[0][0]}t=["",i[0]||{}],o=!0}else{if(!(i.length>1&&i[0]!==this.pageLoadEvt))return;e=i[0],t=Array.prototype.slice.call(i,1)}this.uetInstance.uetLoaded||this.evqCDispatch&&this.processEarly[e]?this.uetInstance._push([e,t,o]):this.uetInstance.eventPushQueue.push([e,t,o])},this._push=function(e){if(e[1]instanceof Array)if("event"===e[0]){var t=e[1][1]||{},i=e[1][0];if(null==i)return;var o=i===this.pageViewEvt?this.pageLoadEvt:this.customEvt;this.evt(o,i,t,e[2])}else if("set"===e[0]){if("object"!=typeof e[1][0])return;for(var n in e[1][0])if(this.knownParams.hasOwnProperty(n)&&(this.pageLevelParams[n]=e[1][0][n],"pid"===n&&!0===this.pageLoadDispatch)){var s=this.validateSubparams({pid:e[1][0][n]},"");s.hasOwnProperty("pid")&&this.firePidEvent(s.pid)}}else if("consent"===e[0]){var r=e[1][1],h=e[1][0];if(null===r||"object"!=typeof r)return;var d=!1;if(r.hasOwnProperty("source")&&this.stringExists(r.source)&&0===r.source.indexOf("gtm_"))if("gtm_update"===r.source&&r.hasOwnProperty("ad_storage")){if(!0!==this.uetConfig.cusig.blob.ec&&!0!==this.uetConfig.cusig.blob.ea)return void this.fireSendBeacon("gtmConsent",{gasc:"denied"!==r.ad_storage?"G":"D"});d=!0}else if("gtm_auto"!==r.source)return;if(this.uetConfig.consent.enabled=!0,"default"===h){if(r.hasOwnProperty("ad_storage")&&!1===this.uetConfig.consent.adStorageUpdated&&(this.uetConfig.consent.adStorageAllowed="denied"!==r.ad_storage,this.uetConfig.consent.enforced=!1,!0===this.uetConfig.tcf.auto&&(this.uetConfig.tcf.enabled=!1,this.uetConfig.tcf.auto=!1)),this.handleCookieIds(),this.fireConsentPing("default"),r.hasOwnProperty("wait_for_update")){var u=parseInt(r.wait_for_update,10);!isNaN(u)&&u>0&&(u=Math.min(u,1e4),this.uetConfig.consent.waitForUpdate=u)}}else"update"===h&&r.hasOwnProperty("ad_storage")&&(this.uetConfig.consent.adStorageAllowed="denied"!==r.ad_storage,this.uetConfig.consent.adStorageUpdated=!0,this.uetConfig.consent.enforced=!1,!0===this.uetConfig.tcf.auto&&(this.uetConfig.tcf.enabled=!1,this.uetConfig.tcf.auto=!1),this.handleCookieIds(),d&&this.fireSendBeacon("gtmConsent",{gasc:"denied"!==r.ad_storage?"G":"D"}),this.fireConsentPing("update"),this.uetConfig.consent.timeoutId&&!0!==this.uetLoaded&&(clearTimeout(this.uetConfig.consent.timeoutId),this.checkuetHostdocumentload()))}else if("config"===e[0]){r=e[1][1],h=e[1][0];if(null===r||"object"!=typeof r)return;"tcf"===h&&r.hasOwnProperty("enabled")&&!0===r.enabled&&this.tcfSubscribe(!1)}},this.dispatchq=function(e){var t,i;e||(this.evqDispatch=!0,!1===this.uetConfig.disableVisibilityEvents&&"onpagehide"in window&&(window.addEventListener("pageshow",this.firePageShow.bind(this)),window.addEventListener("pagehide",this.firePageHide.bind(this))),!0===this.uetConfig.enableAutoSpaTracking&&("onpopstate"in window&&window.addEventListener("popstate",this.documentUrlChanged.bind(this,"popstate")),this.wrapHistoryMethod("pushState"),this.wrapHistoryMethod("replaceState")));for(var o=0;o<this.evq.length;o++)if("object"==typeof this.evq[o]){var n=this.evq[o];if("set"===t)e&&this.push(t,n);else if("consent"===t||"config"===t)e&&void 0!==i&&this.push(t,i,n);else if(e);else if("event"===t){var s=!1;for(var r in this.legacyValidCustomEventKeyNames)if(n.hasOwnProperty(r)){s=!0;break}void 0!==i&&(this.push(t,i,s?{}:n),s&&this.push(n))}else this.push(n);t=i=void 0}else if("string"==typeof this.evq[o]||this.evq[o]instanceof String){void 0!==t&&void 0!==i&&(e||this.push(t,i,{}),t=i=void 0);var h=this.evq[o];void 0!==t||"set"!==h&&"consent"!==h&&"config"!==h&&"event"!==h?void 0!==t&&void 0===i?i=h:t=i=void 0:t=h,o!==this.evq.length-1||"event"!==t||e||void 0===i||this.push(t,i,{})}else t=i=void 0},this.invisibleDiv=null,this.invisibleFrame=null,this._SB=function(e){return(e+256).toString(16).substring(1,3)},this._SU=function(e,t){for(var i="",o=0;o<16;o++)t&&o>=4&&o<=10&&o%2==0&&(i+="-"),i+=this._SB(e[o]);return i},this.getRandomValues=window.crypto&&window.crypto.getRandomValues&&window.crypto.getRandomValues.bind(window.crypto)||window.msCrypto&&window.msCrypto.getRandomValues&&window.msCrypto.getRandomValues.bind(window.msCrypto)||function(e){for(var t=0;t<e.length;t++)e[t]=Math.floor(256*Math.random())},this.getUuidV1=function(e){try{var t=new Uint8Array(10);this.getRandomValues(t);var i=1e4*(Date.now()+122192928e5)+(t[8]+(t[9]<<8))%1e4,o=new Uint8Array(16);o[3]=255&i,o[2]=i>>8&255,o[1]=i>>16&255,o[0]=i>>24&255,i/=4294967296,o[5]=255&i,o[4]=i>>8&255,o[7]=i>>16&255,o[6]=i>>24&255;for(var n=0;n<8;n++)o[n+8]=t[n];return o[8]&=63,o[8]|=128,o[6]&=15,o[6]|=16,o[10]|=1,this._SU(o,e)}catch(s){return""}},this.getUuidV4=function(e){try{var t=new Uint8Array(16);return this.getRandomValues(t),t[8]&=63,t[8]|=128,t[6]&=15,t[6]|=64,this._SU(t,e)}catch(i){return""}},this.stringifyToRequest=function(e,t){var i="",o="";for(var n in t&&(o=t+"."),e)"object"==typeof e[n]?i+=this.stringifyToRequest(e[n],o+n):i+=o+n+"="+e[n]+"&";return i},this.createInvisibleElement=function(e,t){var i=document.createElement(t);return i.style.width="0px",i.style.height="0px",i.style.display="none",i.style.visibility="hidden",i.id="batBeacon"+Math.floor(1e12*Math.random()),e.appendChild(i),i},this.createInvisibleDiv=function(e){return this.invisibleDiv=this.createInvisibleElement(e,"div"),this.invisibleDiv.id},this.fireBeaconImg=function(e){if(!0!==this.uetConfig.msDns){var t=this.createInvisibleElement(this.invisibleDiv,"img");t.width=0,t.height=0;var i=Math.floor(1e6*Math.random()),o=e+"&rn="+i;return t.setAttribute("alt",""),this.uetConfig.imgAlt&&t.setAttribute("alt",this.uetConfig.imgAlt),t.setAttribute("src",o),i}},this.addLoadTime=function(e){var t,i;if(window.performance){var o=null==(t=window.performance.timing)?void 0:t.domContentLoadedEventEnd;if((null==(i=window.performance.timing)?void 0:i.loadEventEnd)&&(o=window.performance.timing.loadEventEnd),void 0!==o&&0!==o){var n=o-window.performance.timing.navigationStart;e.lt=n}if(this.uetConfig.navTimingApi&&null!=window.performance.timing){for(var s=["navigationStart","unloadEventStart","unloadEventEnd","redirectStart","redirectEnd","fetchStart","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd","domLoading","domInteractive","domContentLoadedEventStart","domContentLoadedEventEnd","domComplete","loadEventStart","loadEventEnd"],r=window.performance.timing[s[0]],h=r,d=1;d<s.length;d++){var u=window.performance.timing[s[d]];h+=",",h+=null==u||0===u?"":u-r}h.length<=150&&(e.pt=h),null!=window.performance.navigation&&(e.pn=window.performance.navigation.type+","+window.performance.navigation.redirectCount)}}return e},this.hashCode=function(e){var t=0;if(0===e.length)return t;for(var i=0;i<e.length;i++){t=(t<<5)-t+e.charCodeAt(i),t|=0}return t},this.addPluginData=function(e){for(var t=[],i=0;i<window.navigator.plugins.length;i++)t.push({name:window.navigator.plugins[i].name});for(var o=t.sort((function(e,t){return e.name>t.name?1:e.name<t.name?-1:0})),n="",s=0;s<o.length;s++)n+=o[s].name;return e.pi=this.hashCode(n),e},this.addFraudSignals=function(e){return screen&&(screen.width&&(e.sw=screen.width),screen.height&&(e.sh=screen.height),screen.colorDepth&&(e.sc=screen.colorDepth)),navigator&&!0===navigator.webdriver&&(e.nwd="1"),e},this.addUrlData=function(e,t){t=!0===t;var i=!1;if(window.window!=window.top)try{for(var o=0,n=window.window;o<=5&&null!=n&&n!=window.top;o++,n=n.parent)if(null!=n.document&&this.stringExists(n.document.referrer)&&"about:blank"!==n.document.referrer.toLowerCase()){var s=n.document.referrer;(!0===this.uetConfig.removeQueryFromUrls||t)&&(s=s.split("?")[0]),e.p=encodeURIComponent(s),e.r="",i=!0;break}}catch(u){}if(this.stringExists(this.plOverride)){s=this.plOverride;(!0===this.uetConfig.removeQueryFromUrls||t)&&(s=s.split("?")[0]),e.p=encodeURIComponent(s),e.r=""}else if(!i){var r=window.document.referrer;s=window.location.href;(!0===this.uetConfig.removeQueryFromUrls||t)&&(s=s.split("?")[0],r=r.split("?")[0]);var h=window.location.hash,d=s.indexOf(h)>=0?s:s+h;e.p=encodeURIComponent(d),e.r=encodeURIComponent(r)}return t&&delete e.r,e},this.extractMsClkId=function(e){if(!this.stringExists(this.msClkId)){var t=e.p;try{t=decodeURIComponent(t)}catch(i){}this.msClkId=this.getQueryParam(t,this.msClkIdParamName)}},this.addPageData=function(e,t){t=!0===t,e=this.addPluginData(e);var i=window.navigator.userLanguage||window.navigator.language;this.stringExists(i)&&(e.lg=i),e=this.addFraudSignals(e);var o=window.document.title;if(this.stringExists(o)&&!this.stringExists(e.tl)&&(e.tl=encodeURIComponent(o).replace(/%2C/gi,",")),window.document.head.getElementsByTagName("meta").keywords){var n=window.document.head.getElementsByTagName("meta").keywords.content;this.stringExists(n)&&(e.kw=encodeURIComponent(n).replace(/%2C/gi,","))}return t?this.stringExists(this.previousPage)&&!e.hasOwnProperty("r")&&(e.r=this.previousPage):(e=this.addUrlData(e),e=this.addLoadTime(e)),navigator.maxTouchPoints&&(e.mtp=navigator.maxTouchPoints),e},this.removeTrailingAmp=function(e){var t=e.charAt(e.length-1);return"&"!==t&&"?"!==t||(e=e.substring(0,e.length-1)),e},this.helperError=function(e){if("function"==typeof CustomEvent){var t={errMsg:e,tagId:this.beaconParams.ti},i=new CustomEvent("uetError",{detail:t});window.dispatchEvent(i)}},this.throwError=function(e){if(this.helperError(e),this.uetConfig.errorBeaconLevel>0){this.invisibleDiv||this.createInvisibleDiv(document.body);var t=this.combine(this.beaconParams,{errMsg:encodeURIComponent(e)}),i=this.stringifyToRequest(t),o=this.removeTrailingAmp(this.errPrefix+i);this.fireBeaconImg(o)}throw e},this.validateValue=function(e,t,i,o){var n=0,s=t,r=void 0===i||0===i;return-1!==t.toString().indexOf(",")&&(s=t.replace(/,/g,"")),n=parseFloat(s),(isNaN(s)||isNaN(n)||r&&-1!==n.toString().indexOf("."))&&this.throwError(e+" should be "+(r?"an integer":"a number")),n>o?this.throwError(e+" cannot be greater than "+o):n<0?this.throwError(e+" cannot be less than 0"):this.getDecimalPlaces(n)>i&&(n=parseFloat(n.toFixed(i))),n},this.validateRegex=function(e,t,i){var o=null==e?"":e.toString();return o.match(t)||this.throwError(i),o},this.encodeParameter=function(e){return(null==e?"":e.toString()).replace(/&/gi,"%26").replace(/#/gi,"%23")},this._validateProdId=function(e){return null==e&&this.throwError(this.invalidProdIdException),((e=e.toString()).length<1||e.length>50)&&this.throwError(this.invalidProdIdException),e},this.validateProdId=function(e){var t="";if(e instanceof Array){for(var i=0;i<e.length;i++)e[i]instanceof Array&&this.throwError(this.invalidProdIdException),null!==e[i]&&void 0!==e[i]&&(t+=""!==t?",":"",t+=encodeURIComponent(this._validateProdId(e[i])));""===t&&this.throwError(this.invalidProdIdException)}else t=encodeURIComponent(this._validateProdId(e));return t},this.validatePageType=function(e,t){null==e&&this.throwError(this.invalidPageTypeException+e);var i=e.toString().toLowerCase();return t[i]||this.throwError(this.invalidPageTypeException+i),i},this.getDecimalPlaces=function(e){var t=parseFloat(e);if(isNaN(e)||isNaN(t))return 0;var i=(""+e).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return i?Math.max(0,(i[1]?i[1].length:0)-(i[2]?+i[2]:0)):0},this.sha256=function a(e){function c(e,t){return e>>>t|e<<32-t}try{for(var t,i,o=Math.pow,n=o(2,32),s="length",r="push",h="",d=[],u=8*e[s],l=a.h=a.h||[],p=a.k=a.k||[],g=p[s],m={},f=2;64>g;f++)if(!m[f]){for(t=0;313>t;t+=f)m[t]=f;l[g]=o(f,.5)*n|0,p[g++]=o(f,1/3)*n|0}for(e+="";e[s]%64-56;)e+="\0";for(t=0;t<e[s];t++)d[t>>2]|=e.charCodeAt(t)<<(3-t)%4*8;for(d[r](u/n|0),d[r](0|u);d[s];){var v=d.splice(0,16),C=l;for(l=l.slice(0,8),t=0;64>t;t++){var y=v[t-15],w=v[t-2],b=l[0],_=l[4],P=l[7]+(c(_,6)^c(_,11)^c(_,25))+(_&l[5]^~_&l[6])+p[t]+(v[t]=16>t?v[t]:v[t-16]+(c(y,7)^c(y,18)^y>>>3)+v[t-7]+(c(w,17)^c(w,19)^w>>>10)|0);(l=[P+((c(b,2)^c(b,13)^c(b,22))+(b&l[1]^b&l[2]^l[1]&l[2]))|0].concat(l))[4]=l[4]+P|0}for(t=0;8>t;t++)l[t]=l[t]+C[t]|0}for(t=0;8>t;t++)for(i=24;i>=0;i-=8){var k=l[t]>>i&255;h+=(16>k?"0":"")+k.toString(16)}return h}catch(E){return""}},this.validatePid=function(e,t){if(""===(e=e.trim().toLowerCase()))return"";if(!e.match(/^[a-z0-9]{64}$/))switch(t){case"em":case"email":var i=e.split("@");if(2!=i.length){e="";break}i[0]=i[0].trim();var o=i[0].indexOf("+");-1!=o&&(i[0]=i[0].substring(0,o)),i[0]=i[0].replace(/\./g,""),i[1]=i[1].trim(),e=i[0]+"@"+i[1],e=this.sha256(e);break;case"ph":case"phone_number":if("+"!==(e=e.replace(/[-() \t]/g,"")).charAt(0)&&(e="+"+e),!e.match(/^\+\d{11,15}$/)){e="";break}e=this.sha256(e);break;default:e=""}return e},this.validateParameter=function(e,t,i){if(t.match(/^[a-z_]{2,32}$/)||this.throwError(e+" invalid parameter name"),null==i.type||null==this.paramValidations[i.type])return e.toString();var o=this.paramValidations[i.type];switch(o.type){case"regex":var n=o.error.replace("{p}",t);e=this.validateRegex(e,o.regex,n);break;case"num":e=this.validateValue(t,e,o.digits,o.max);break;case"enum":""!==(e=e.toString().toLowerCase())&&-1===i.values.indexOf(e)&&this.throwError(o.error.replace("{p}",t));break;case"pid":e=e.toString(),e=this.validatePid(e,t);break;case"array":e instanceof Array&&!(e.length<1)||this.throwError(o.error.replace("{p}",t)),e=this.validateParameterArray(e,t);break;case"object":"object"!=typeof e?(this.helperError(o.error.replace("{p}",t)),e=""):e=this.validateParameterObject(e,t);break;default:e=e.toString()}return e},this.validateParameterObject=function(e,t){return e=this.validateSubparams(e,t+"."),e=this.removeTrailingAmp(this.stringifyToRequest(e))},this.validateParameterArray=function(e,t){for(var i=0;i<e.length;i++)"object"==typeof e[i]&&(e[i]=this.validateParameterObject(e[i],t));return e.join(",")},this.validateSubparams=function(e,t){var i={};for(var o in e)if(this.knownParams.hasOwnProperty(t+o)&&null!=e[o]){var n=this.knownParams[t+o],s=this.validateParameter(e[o],o,n);if("string"==typeof s||s instanceof String){if(""===s)continue;s=encodeURIComponent(s)}i[n.hasOwnProperty("beacon")?n.beacon:o]=s}return i},this.eventBasicChecks=function(e,t){e||this.throwError(this.invalidEventException+"undefined event."),e!==this.pageLoadEvt&&e!==this.customEvt&&this.throwError(this.invalidEventException+e),t||this.throwError("undefined data object passed to validate")},this.validateDataObjectNew=function(e,t){this.eventBasicChecks(e,t),!t.hasOwnProperty("ecomm_prodid")||null==t.ecomm_prodid||"string"==typeof t.ecomm_prodid&&""===t.ecomm_prodid.toString()||(t.ecomm_prodid=this.validateProdId(t.ecomm_prodid));var i=t.event_action;if(null!=i&&this.knownEvents.hasOwnProperty(i)){var o=this.knownEvents[i];for(var n in o){var s=o[n];this.pageLevelParams.hasOwnProperty(s)&&!t.hasOwnProperty(s)&&(t[s]=this.pageLevelParams[s])}}var r=this.validateSubparams(t,"");return!r.hasOwnProperty("pagetype")&&r.hasOwnProperty("prodid")&&this.throwError(this.missingPageTypeException),r.hasOwnProperty("pagetype")&&"purchase"===r.pagetype&&r.hasOwnProperty("ea")&&"purchase"===r.ea&&r.hasOwnProperty("ecomm_totalvalue")&&!r.hasOwnProperty("gv")&&(r.gv=r.ecomm_totalvalue),(r.hasOwnProperty("hct_base_price")||r.hasOwnProperty("hct_booking_xref")||r.hasOwnProperty("hct_pagetype")||r.hasOwnProperty("hct_checkin_date")||r.hasOwnProperty("hct_checkout_date")||r.hasOwnProperty("hct_length_of_stay")||r.hasOwnProperty("hct_partner_hotel_id"))&&(r.hasOwnProperty("hct_total_price")&&r.hasOwnProperty("gc")||this.throwError(this.missingHotelParametersException)),r.hasOwnProperty("hct_total_price")&&r.hasOwnProperty("gv")&&this.throwError(this.hotelVariableRevenueException),r},this.validateDataObject=function(e,t){for(var i in this.eventBasicChecks(e,t),t)e===this.customEvt&&(this.legacyValidCustomEventKeyNames[i]||this.ambiguousValidCustomEventKeyNames[i])||e===this.pageLoadEvt&&this.legacyValidPageLoadKeyNames[i]||this.throwError(this.invalidKeyException+i);if(t.hasOwnProperty("pid")&&(t.pid=this.validateParameter(t.pid,"pid",this.knownParams.pid),""===t.pid?delete t.pid:t.pid=encodeURIComponent(t.pid)),t.hasOwnProperty("ev")>0&&(t.ev=this.validateValue("ev",t.ev,3,999999999999)),t.hasOwnProperty("gv")>0&&(t.gv=this.validateValue("gv",t.gv,3,999999999999)),t.hasOwnProperty("gc")>0&&(t.gc=this.validateRegex(t.gc,/^[a-zA-Z]{3}$/,this.goalCurrencyFormatException)),t.hasOwnProperty("ec")>0&&null!==t.ec&&void 0!==t.ec){var o=encodeURIComponent(t.ec);t.ec=this.encodeParameter(t.ec),t.ec!==o&&(t.ec2=o)}if(t.hasOwnProperty("ea")>0&&null!==t.ea&&void 0!==t.ea){var n=encodeURIComponent(t.ea);t.ea=this.encodeParameter(t.ea),t.ea!==n&&(t.ea2=n)}if(t.hasOwnProperty("el")>0&&null!==t.el&&void 0!==t.el){var s=encodeURIComponent(t.el);t.el=this.encodeParameter(t.el),t.el!==s&&(t.el2=s)}t.hasOwnProperty("ecomm_prodid")>0&&(t.prodid=t.ecomm_prodid,delete t.ecomm_prodid),t.hasOwnProperty("ecomm_pagetype")>0&&(t.pagetype=t.ecomm_pagetype,delete t.ecomm_pagetype),!t.hasOwnProperty("pagetype")||null!=t.pagetype&&""!==t.pagetype.toString()||delete t.pagetype,t.hasOwnProperty("prodid")&&(null==t.prodid||"string"==typeof t.prodid&&""===t.prodid)&&delete t.prodid,t.hasOwnProperty("pagetype")>0?(t.pagetype=this.validatePageType(t.pagetype,this.validRetailPageTypeValues),t.hasOwnProperty("prodid")>0&&(t.prodid=this.validateProdId(t.prodid))):t.hasOwnProperty("prodid")>0&&this.throwError(this.missingPageTypeException)},this.evt=function(e,t,i,o){if(o=!1!==o,i=i||{},!0===this.uetConfig.disableAutoPageView&&!1===this.evqDispatch&&this.dispatchq(!1),"object"==typeof i){if(!0===this.uetConfig.allRep?i.rep="1":i.hasOwnProperty("rep")&&(1===i.rep||"1"===i.rep||!0===i.rep?i.rep="1":delete i.rep),this.enforceConsent(),e===this.pageLoadEvt&&(!0===this.uetConfig.gtagPid&&"enhanced_conversion_data"in window&&"object"==typeof window.enhanced_conversion_data&&(this.pageLevelParams.pid={em:window.enhanced_conversion_data.email,ph:window.enhanced_conversion_data.phone_number}),o&&this.pageLevelParams.hasOwnProperty("pid")&&!i.hasOwnProperty("pid")&&(i.pid=this.pageLevelParams.pid),i.hasOwnProperty("page_location")))try{this.plOverride=new URL(i.page_location).toString()}catch(f){}if(this.pageLevelParams.hasOwnProperty("pid")&&(this.knownEvents[t]=this.knownEvents[t]||[],-1===this.knownEvents[t].indexOf("pid")&&this.knownEvents[t].push("pid")),o?this.validateDataObject(e,i):(i.event_action=t,e===this.customEvt&&i.hasOwnProperty("gtm_tag_source")&&(i=this.mapGtmParams(i)),i=this.validateDataObjectNew(e,i)),this.uetConfig.cdl&&(i.cdl=this.uetConfig.cdl),e===this.customEvt){var n=[];for(var s in i)n.push(s);if(0===n.length)return;o||(i.en="Y"),i=this.addUrlData(i,!0),i=this.addFraudSignals(i)}else if(e===this.pageLoadEvt){if(null!=i.ea&&this.knownEvents.hasOwnProperty(i.ea)){var r=this.knownEvents[i.ea];for(var h in i)-1===r.indexOf(h)&&delete i[h]}var d=!o&&i.hasOwnProperty("page_path");if(d){if(i.spa="Y",!1===this.pageLoadDispatch){var u={};u=this.addPageData(u,!1),this.stringExists(u.p)&&(this.previousPage=u.p),i.r=u.r,i.lt=u.lt,u.hasOwnProperty("pt")&&(i.pt=u.pt),u.hasOwnProperty("pn")&&(i.pn=u.pn)}else this.firePageHide(),this.midOverride||(this.beaconParams.mid=this.getUuidV4(!0),this.beaconParams.bo=0);if(i.hasOwnProperty("page_title")&&(i.tl=i.page_title,delete i.page_title),this.stringExists(this.previousPage)){var l=this.previousPage.toUpperCase(),p=l.indexOf("%3A%2F%2F");if(-1===p)return;if("%2F"===i.page_path.substring(0,3).toUpperCase()){p+=9;var g=l.indexOf("%2F",p);i.p=-1===g?this.previousPage+i.page_path:this.previousPage.substring(0,g)+i.page_path}else i.p=this.previousPage}}else{if(!0===this.pageLoadDispatch)return;if(!0===this.uetConfig.disableAutoPageView&&o)return;this.stringExists(this.uetConfig.gtmTagSource)&&(i.gtm_tag_source=this.uetConfig.gtmTagSource)}if(this.uetConfig.uach){var m=this.stringifyToRequest(this.uetConfig.uach);m=this.removeTrailingAmp(m),m=encodeURIComponent(m),i.uach=m}!1===this.pageLoadDispatch&&(this.pageLoadDispatch=!0),i=this.addPageData(i,d),this.stringExists(i.p)&&(this.previousPage=i.p)}this.invisibleDiv||this.createInvisibleDiv(document.body),i.evt=e,window.window!=window.top&&(i.ifm=1),this.addCookieIds(),e===this.pageLoadEvt&&(i.sv=this.subVersion),i=this.addConsentParams(i),!0===this.midOverride&&(i.et="up");try{i.cdb=this.buildConsentDetectionBlob()}catch(f){}this.stringExists(this.uetConfig.batDebug)&&(i.dbg=this.uetConfig.batDebug),!0!==this.uetConfig.msDns&&(this.beaconParams.bo=(this.beaconParams.bo||0)+1,this.fireBeacon(i),i.abf=!0),e===this.pageLoadEvt&&i.hasOwnProperty("pid")&&this.firePidEvent(i.pid),e===this.pageLoadEvt&&!1===this.evqDispatch&&this.dispatchq(!1)}},this.removeLocalStorageBackup=function(e){try{localStorage.removeItem(e+"_exp"),localStorage.removeItem(e)}catch(t){}},this.setLocalStorageBackup=function(e,t,i){try{var o=new Date;o.setTime(o.getTime()+1e3*i),localStorage.setItem(e,t),localStorage.setItem(e+"_exp",o.toUTCString())}catch(n){}},this.getLocalStorageBackup=function(e,t){try{var i=localStorage.getItem(e+"_exp");if(null==i)return null;if(new Date>new Date(i))return this.removeLocalStorageBackup(e),null;var o=localStorage.getItem(e);return null==o||o.length>t?null:o}catch(n){return null}},this.getCookie=function(e,t,i){if(!this.stringExists(e))return null;var o=document.cookie;if(0===o.length)return null;this.stringExists(t)||(t="");for(var n,s=0;s<o.length;){if((n=o.indexOf(e+"="+t,s))<0)return null;if(!(n>0&&" "!==o[n-1]&&";"!==o[n-1]))break;s=n+e.length+1}var r=o.indexOf(";",n);r=r>=0?r:o.length;var h=o.substring(n+e.length+1+t.length,r);return h.length>i?null:h},this._setCookie=function(e,t,i,o,n){return document.cookie=e+"="+t+(i?";expires="+i.toUTCString():"")+(o?";domain=."+o:"")+";path=/"+(this.stringExists(n)?";"+n:"")},this.getHostname=function(){return document.location&&document.location.hostname},this.setCookie=function(e,t,i,o,n,s,r){if(!this.stringExists(e))return null;if(this.stringExists(s)||(s=""),!this.stringExists(t)||t.length>r)return null;var h=null;i>0&&(h=new Date).setTime(h.getTime()+1e3*i);var d=new Date;if(d.setTime(0),n&&null!=h&&this.setLocalStorageBackup(e,t,i),null===this.domainName||o){var u=this.getHostname();if(u&&"string"==typeof u&&"localhost"!==u){var l=u.split("."),p=l.pop();for(3===l.length&&Number(p)>=0&&(l=[]);l.length>0;)if(p=l.pop()+"."+p,(""===this.uetConfig.cookieDomain||this.uetConfig.cookieDomain.toLowerCase()===p.toLowerCase())&&(o&&(this._setCookie(e,"",d,p,this.uetConfig.cookieFlags),o=!!this.getCookie(e,s,r)),!o&&(this._setCookie(e,s+t,h,p,this.uetConfig.cookieFlags),this.getCookie(e,s,r))))return void(this.domainName=p)}this.domainName=""}this._setCookie(e,s+t,h,this.domainName,this.uetConfig.cookieFlags)},this.getQueryParam=function(e,t){return this.stringExists(e)&&this.stringExists(t)&&!/[^\d\w]/.exec(t)?(new RegExp("[?&]"+t+"=([^&#]*)","i").exec(e)||[,null])[1]:null},this.addCookieIds=function(){if(this.addCookieId(this.beaconParams,"sid","",this.sessionCookieName,this.sessionExpirationTime),this.addCookieId(this.beaconParams,"uid","",this.uetConfig.uidCookie,0),this.pageLevelParams.hasOwnProperty("vid")){var e=this.pageLevelParams.vid;"string"==typeof e&&this.stringExists(e)&&(e=e.replace(/[-{}]/g,"").toLowerCase()).match(/^[0-9a-f]{32}$/)&&(this.beaconParams.vid=e,this.beaconParams.vids="3")}else this.uetConfig.disableUetVid||this.addCookieId(this.beaconParams,"vid","vids",this.uetConfig.vidCookie,this.uetConfig.disableUetVid?0:this.visitorExpirationTime);this.addMsClkId(this.beaconParams)},this.clearCookieIds=function(){delete this.beaconParams.sid,delete this.beaconParams.vid,delete this.beaconParams.vids,delete this.beaconParams.msclkid},this.handleCookieIds=function(){var e=!0;!0===this.uetConfig.consent.enabled&&!1===this.uetConfig.consent.adStorageAllowed&&(e=!1),!0===this.uetConfig.tcf.enabled&&!0===this.uetConfig.tcf.hasLoaded&&!0===this.uetConfig.tcf.gdprApplies&&!0!==this.uetConfig.tcf.adStorageAllowed&&(!1!==this.uetConfig.tcf.auto&&!0!==this.uetConfig.tcf.hasVendor||(e=!1)),this.uetConfig.enableAdStorage!=e&&(this.uetConfig.enableAdStorage=e,e?this.addCookieIds():this.clearCookieIds())},this.addCookieId=function(e,t,i,o,n){if(!this.isAdStorageAllowed())return e;var s="2",r=!0,h=this.getCookie(o,"",this.insightsCookieMaxLength);this.stringExists(h)||(r=!1,h=this.getLocalStorageBackup(o,this.insightsCookieMaxLength));var d=this.insightsTrimCookie(h,!0);if(h=this.insightsTrimCookie(h,!1),0===n)return this.stringExists(h)&&(e[t]=encodeURIComponent(h),this.stringExists(i)&&(e[i]=s)),e;this.stringExists(h)&&!h.match(/^[0-9a-f]{32}$/)&&(h=h.replace(/-/g,"")),this.stringExists(h)&&h.match(/^[0-9a-f]{32}$/)?s="0":(h=this.getUuidV1(!1),s="1");var u=null===d?h:h+"|"+d;return this.setCookie(o,u,n,r,!0,"",this.insightsCookieMaxLength),this.getCookie(o,"",this.insightsCookieMaxLength)!==u&&this.getLocalStorageBackup(o,this.insightsCookieMaxLength)!==u||(e[t]=encodeURIComponent(h),this.stringExists(i)&&(e[i]=s)),e},this.addMsClkId=function(e){if(!this.isAdStorageAllowed())return e;this.extractMsClkId(this.addUrlData({}));var t="0",i=this.getCookie(this.msClkIdCookieName,this.msClkIdCookieValuePrefix,this.lengthMsClkId);return this.stringExists(i)||(i=this.getLocalStorageBackup(this.msClkIdCookieName,this.lengthMsClkId)),this.stringExists(this.msClkId)?i!==this.msClkId&&(t="1"):this.msClkId=i,this.stringExists(this.msClkId)?(this.setCookie(this.msClkIdCookieName,this.msClkId,this.msClkIdExpirationTime,!0,!0,this.msClkIdCookieValuePrefix,this.lengthMsClkId),this.getCookie(this.msClkIdCookieName,this.msClkIdCookieValuePrefix,this.lengthMsClkId)!==this.msClkId&&(t+="N"),e.msclkid=encodeURIComponent(this.msClkId+"-"+t)):e.msclkid="N",e},this.clone=function(e,t){for(var i in void 0===t&&(t={}),e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t},this.combine=function(e,t){var i=this.clone(e);return i.alt&&delete i.alt,i=this.clone(t,i)},this.fireBeacon=function(e){for(var t=this.getClUrl(this.urlPrefix),i=this.combine(this.beaconParams,e),o=this.stringifyToRequest(i),n=this.removeTrailingAmp(t+o),s=["r","el2","ec2","ea2","page_location","page_path","kw","p","tl","items"],r=0;encodeURI(n).length>this.URLLENGTHLIMIT&&s.length>r;r++){var h=s[r];h in i&&(0==r?i[h]=i[h].split("?")[0]:r<=3?i[h]="":delete i[h],o=this.stringifyToRequest(i),n=this.removeTrailingAmp(t+o))}this.fireBeaconImg(n),this.snippetEventQueue.push(o),this.snippetEventQueue.length>20&&this.snippetEventQueue.shift();try{if("function"==typeof window.CustomEvent){var d=new CustomEvent("UetEvent",{bubbles:!0,detail:{uetEvent:o}});this.invisibleDiv.dispatchEvent(d)}}catch(u){}},this.firePageShow=function(e){!1===this.uetConfig.disableVisibilityEvents&&e&&e.persisted&&this.fireSendBeacon("pageShow")},this.firePageHide=function(e){!1===this.uetConfig.disableVisibilityEvents&&this.fireSendBeacon("pageHide")},this.firePidEvent=function(e){this.beaconParams.bo=(this.beaconParams.bo||0)+1;var t=this.combine(this.beaconParams,{evt:"pid",pid:e});t=this.addConsentParams(t);var i=this.removeTrailingAmp(this.getClUrl(this.urlPrefix)+this.stringifyToRequest(t));this.invisibleDiv||this.createInvisibleDiv(document.body),this.fireBeaconImg(i)},this.fireConsentPing=function(e){var t={};e&&(t.src=e),t.cdb=this.buildConsentDetectionBlob(),this.fireSendBeacon("consent",t)},this.fireSendBeacon=function(e,t){if(!0!==this.uetConfig.msDns){this.beaconParams.bo=(this.beaconParams.bo||0)+1;var i=this.combine(this.beaconParams,{evt:e});t&&(i=this.clone(t,i)),i=this.addConsentParams(i);var o=this.removeTrailingAmp(this.getClUrl(this.previewPrefix)+this.stringifyToRequest(i));try{navigator.sendBeacon?navigator.sendBeacon(o):this.fireBeaconImg(o)}catch(n){}}},this.getCmpData=function(){try{for(var e=["Optanon","CookieConsent","Usercentrics","osano","ketch","didomi","cookieyes","TrustArc","CookieScript","__cmp","iubenda","utag","CookieInformation","mineos","termly","privacytools","SeersConsentManagementPlatform","ethyca","PiwikPro","SecurePrivacy","borlabsCookie","SalesforceConsent","IBMVerify","adopt","illow","analytics","CommandersAct","Complianz","consentmanager","Sirdata"],t=0,i=0;i<e.length;i++)if(window.hasOwnProperty(e[i])){t=i+1;break}return t}catch(o){}return 0},this.preEnforce=function(){if(this.shouldEnforce()&&!1!==this.uetConfig.enableAutoConsent){var e=this.getGASC();void 0!==e.update?this.push("consent","update",{ad_storage:!0===e.update?"granted":"denied",source:"gtm_auto"}):void 0!==e.default&&this.push("consent","default",{ad_storage:!0===e.default?"granted":"denied",source:"gtm_auto",wait_for_update:500}),this.shouldEnforce()&&!1===this.uetConfig.tcf.enabled&&this.tcfSubscribe(!0)}},this.getGASC=function(){var e={};if(!window.hasOwnProperty("google_tag_data")||"object"!=typeof window.google_tag_data)return e;var t=window.google_tag_data.ics;return"object"!=typeof t||!0!==t.active||!0!==t.usedDefault&&!0!==t.usedUpdate||"object"!=typeof t.entries||"object"!=typeof t.entries.ad_storage||(!0===t.usedDefault&&(e.default=!0===t.entries.ad_storage.default),!0===t.usedUpdate&&(e.update=!0===t.entries.ad_storage.update)),e},this.buildConsentDetectionBlob=function(){var e=this.getCmpData(),t=this.getGASC(),i=0!=e|(void 0!==t.update?!0===t.update:!0===t.default)<<1|("function"==typeof window.__tcfapi)<<2|(!0===this.uetConfig.consent.enforced)<<3|(!0===this.uetConfig.cusig.hasLoaded)<<4;return btoa(String.fromCharCode(1,e,i)).replace("+","-").replace("/","_").replace(/={1,2}$/,"")},this.isTzEnforced=function(e){try{var t=e||Intl.DateTimeFormat().resolvedOptions().timeZone;return this.tzEnforced.includes(t)}catch(i){return!1}},this.shouldEnforce=function(){if(!0===this.uetConfig.consent.enabled&&!1===this.uetConfig.consent.enforced)return!1;if(!0===this.uetConfig.tcf.enabled&&!0===this.uetConfig.tcf.hasLoaded){if(!1===this.uetConfig.tcf.auto)return!1;if(!0===this.uetConfig.tcf.gdprApplies&&!0===this.uetConfig.tcf.hasVendor)return!1}return!0!==this.uetConfig.cusig.hasLoaded?this.isTzEnforced():!0===this.uetConfig.cusig.blob.bi||(!0===this.uetConfig.cusig.blob.dt&&!0===this.uetConfig.cusig.blob.ea||!0===this.uetConfig.cusig.blob.ec&&(!0!==this.uetConfig.cusig.blob.at&&(!0===this.uetConfig.cusig.blob.ah||!0!==this.uetConfig.cusig.blob.pt)))},this.enforceConsent=function(){var e=this.uetConfig.consent.enforced;this.shouldEnforce()?(this.uetConfig.consent.enabled=!0,this.uetConfig.consent.adStorageAllowed=!1,this.uetConfig.consent.enforced=!0,this.handleCookieIds(),!0!==e&&this.fireConsentPing("enforced")):!0===e&&(this.uetConfig.consent.enabled=!1,this.uetConfig.consent.adStorageAllowed=!0,this.uetConfig.consent.enforced=!1,this.handleCookieIds(),this.fireConsentPing("unenforced"))},this.addConsentParams=function(e){if(!0===this.uetConfig.consent.enabled&&(e.asc=this.uetConfig.consent.adStorageAllowed?"G":"D"),!0===this.uetConfig.tcf.enabled&&(!1===this.uetConfig.tcf.auto||!0===this.uetConfig.tcf.hasVendor)){var t={st:!0===this.uetConfig.tcf.hasLoaded?"L":"E"};!0===this.uetConfig.tcf.auto&&(t.al="1"),!0===this.uetConfig.tcf.gdprApplies&&(t.gdpr="Y",!0===this.uetConfig.tcf.adStorageAllowed&&(t.as="G"),!0===this.uetConfig.tcf.measurementAllowed&&(t.ms="G"),!0===this.uetConfig.tcf.personalizationAllowed&&(t.pz="G")),e.tcf=encodeURIComponent(this.removeTrailingAmp(this.stringifyToRequest(t)))}return e},this.clarityOnLoad=function(){void 0!==window.clarity&&window.clarity.start()},this.mapGtmUasProducts=function(e){if(e.hasOwnProperty("ecomm_totalvalue")){if(e.ecomm_pagetype="purchase",e.ecomm_prodid=[],e.items=[],e.hasOwnProperty("transactionProducts")&&e.transactionProducts instanceof Array)for(var t=0;t<e.transactionProducts.length;t++){var i=e.transactionProducts[t];if("object"==typeof i){var o={};i.hasOwnProperty("sku")&&(o.id=i.sku,e.ecomm_prodid.push(o.id)),i.hasOwnProperty("price")&&(o.price=i.price),i.hasOwnProperty("quantity")&&(o.quantity=i.quantity),e.items.push(o)}}delete e.transactionProducts}},this.mapGtmEcommercePurchase=function(e){if(e.hasOwnProperty("ecommerce")&&"object"==typeof e.ecommerce){var t;if(e.ecommerce.hasOwnProperty("ecommerce")&&"object"==typeof e.ecommerce.ecommerce&&(e.ecommerce=e.ecommerce.ecommerce),e.ecommerce.hasOwnProperty("purchase")&&"object"==typeof e.ecommerce.purchase)t=e.ecommerce.purchase,e.ecomm_pagetype="purchase";else if(e.ecommerce.hasOwnProperty("add")&&"object"==typeof e.ecommerce.add)t=e.ecommerce.add,e.ecomm_pagetype="cart";else if(e.ecommerce.hasOwnProperty("click")&&"object"==typeof e.ecommerce.click)t=e.ecommerce.click,e.ecomm_pagetype="product";else if(e.ecommerce.hasOwnProperty("detail")&&"object"==typeof e.ecommerce.detail)t=e.ecommerce.detail,e.ecomm_pagetype="product";else{if(!(e.ecommerce.hasOwnProperty("impressions")&&e.ecommerce.impressions instanceof Array))return void delete e.ecommerce;t={products:e.ecommerce.impressions},e.ecomm_pagetype="other"}if(e.ecommerce.hasOwnProperty("currencyCode")&&(e.currency=e.ecommerce.currencyCode),t.hasOwnProperty("actionField")&&"object"==typeof t.actionField&&(t.actionField.hasOwnProperty("id")&&(e.transaction_id=t.actionField.id),t.actionField.hasOwnProperty("revenue")&&(e.ecomm_totalvalue=t.actionField.revenue)),t.hasOwnProperty("products")&&t.products instanceof Array){e.ecomm_prodid=[],e.items=[];for(var i=0;i<t.products.length;i++){var o=t.products[i];if("object"==typeof o){var n={};o.hasOwnProperty("id")&&(n.id=o.id,e.ecomm_prodid.push(n.id)),o.hasOwnProperty("price")&&(n.price=o.price),o.hasOwnProperty("quantity")&&(n.quantity=o.quantity),e.items.push(n)}}}delete e.ecommerce}},this.mapGtmGa4Items=function(e){if(e.hasOwnProperty("event_action"))switch(e.event_action){case"purchase":e.ecomm_pagetype="purchase";break;case"add_to_cart":e.ecomm_pagetype="cart";break;case"view_item_list":e.ecomm_pagetype="category";break;case"view_item":case"select_item":e.ecomm_pagetype="product"}if(e.hasOwnProperty("ecomm_pagetype")&&e.hasOwnProperty("event_value")&&(e.ecomm_totalvalue=e.event_value),e.hasOwnProperty("items")&&e.items instanceof Array){e.ecomm_prodid=[];for(var t=[],i=0;i<e.items.length;i++){var o=e.items[i];if("object"==typeof o){var n={};(o.item_id||o.id)&&(n.id=o.item_id||o.id,e.ecomm_prodid.push(n.id)),o.hasOwnProperty("price")&&(n.price=o.price),o.hasOwnProperty("quantity")&&(n.quantity=o.quantity),t.push(n)}}e.items=t}},this.replaceGtmParam=function(e,t,i){e.hasOwnProperty(t)&&(e[i]=e[t],delete e[t])},this.mapGtmParams=function(e){switch(e.hasOwnProperty("event_value")&&""===e.event_value&&delete e.event_value,e.gtm_tag_source){case"ua_s":this.replaceGtmParam(e,"transactionId","transaction_id"),this.replaceGtmParam(e,"transactionTotal","ecomm_totalvalue"),this.mapGtmUasProducts(e);break;case"ua_e":this.mapGtmEcommercePurchase(e);break;case"ga4":this.replaceGtmParam(e,"value","event_value"),this.mapGtmGa4Items(e)}return e};var t=window.location.href;if(this.documentUrlChanged=function(e){var i=window.location.href;if(t!=i){t=i;var o=window.location.pathname+window.location.search+window.location.hash;this.push("event","page_view",{page_path:o})}},this.wrapHistoryMethod=function(e){if(window.history&&window.history[e]){var t=window.history[e],i=this.documentUrlChanged.bind(this,e);window.history[e]=function(){var e=t.apply(window.history,arguments);try{i()}catch(o){}return e}}},this.insightsTrimCookie=function(e,t){if(!this.stringExists(e))return null;var i=e.indexOf("|");return-1===i?t?null:e:t?e.substring(i+1):e.substring(0,i)},this.insightsGetCookie=function(e){if(!this.isAdStorageAllowed())return null;var t=0===e?this.sessionCookieName:this.uetConfig.vidCookie,i=this.getCookie(t,"",this.insightsCookieMaxLength);return this.stringExists(i)||(i=this.getLocalStorageBackup(t,this.insightsCookieMaxLength)),this.insightsTrimCookie(i,!0)},this.insightsSetCookie=function(e,t){if(this.isAdStorageAllowed()&&this.stringExists(e)&&!(e.length>this.insightsDataMaxLength)){var i=0===t?this.sessionCookieName:this.uetConfig.vidCookie,o=!0,n=this.getCookie(i,"",this.insightsCookieMaxLength);this.stringExists(n)||(o=!1,n=this.getLocalStorageBackup(i,this.insightsCookieMaxLength)),this.stringExists(n)||(n="");var s=this.insightsTrimCookie(n,!1)+"|"+e;this.setCookie(i,s,0===t?this.sessionExpirationTime:this.visitorExpirationTime,o,!0,"",this.insightsCookieMaxLength)}},this.setExternalMid=function(e){this.stringExists(e)&&e.match(/^[0-9a-fA-F]{8}-?([0-9a-fA-F]{4}-?){3}[0-9a-fA-F]{12}$/)&&(this.beaconParams.mid=e,this.beaconParams.bo=0,this.midOverride=!0)},this.setUserSignals=function(e){var t=!this.uetConfig.cusig.hasLoaded;t&&(this.uetConfig.cusig.hasLoaded=!0,clearTimeout(this.uetConfig.cusig.timeoutId),this.uetConfig.cusig.timeoutId=null),"object"==typeof e&&null!==e&&(this.uetConfig.cusig.blob=e,this.preEnforce(),!0===this.uetLoaded&&this.enforceConsent()),t&&!0!==this.uetLoaded&&this.checkuetHostdocumentload()},this.sanitizeTagId(e),this.isDuplicate(e)){this.uetInstance=e.q;var i=this.uetInstance,logDedup=function(){!0===i.uetLoaded?i.fireSendBeacon("dedup"):setTimeout(logDedup,100)};logDedup()}else this.loadConfig(),this.checkuetHostdocumentload()}globalThis.UET=UET,globalThis.UET_init=function UET_init(e,t){"object"==typeof window[e]&&"[object Array]"!==Object.prototype.toString.call(window[e])||(t.q=window[e],window[e]=new UET(t))},globalThis.UET_push=function UET_push(e){var t=Array.prototype.slice.call(arguments,1);window[e]=window[e]||[],window[e].push.apply(window[e],t)}}();