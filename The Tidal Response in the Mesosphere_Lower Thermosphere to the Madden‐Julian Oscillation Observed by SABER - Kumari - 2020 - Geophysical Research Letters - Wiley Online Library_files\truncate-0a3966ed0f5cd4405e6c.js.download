(window.webpackJsonp=window.webpackJsonp||[]).push([[51],{202:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var o=n(8),r=n.n(o),a=n(9),s=n.n(a),i=(n(203),function(){return s()((function TableOfContent(){r()(this,TableOfContent),this.classList={hidden:"hidden"},this.selectors={menuBookTab:".menuBook--tab",authorBio:"#authorBio",bookReviews:"#bookReviews",abstractPreviewZoom:".abstract-preview__zoom",abstractLastFocus:"[data-lastFocus]",readCubeSharing:".readCube-sharing",shareCtrl:".share__ctrl",shareAccessCtrl:".share-access__ctrl",dropBlockHolder:".dropBlock__holder",tocAbstractPreview:".table-of-content .abstract-preview"},this.elements={menuBookTab:null,abstractPreviewZoom:null,readCubeSharing:null,shareCtrl:null,tocAbstractPreview:null},this.checkMenuItem=function(e){if(!document.querySelector("".concat(e,"-pane"))){var t=UX.utils.convertToArray(document.querySelectorAll(e));t.length&&t.forEach((function(e){(null==e?void 0:e.parentElement).removeChild(e)}))}},this.setElements(),this.eventListeners(),this.onReady(),this.onLoad()}),[{key:"setElements",value:function setElements(){this.elements.menuBookTab=document.querySelector(this.selectors.menuBookTab),this.elements.abstractPreviewZoom=UX.utils.convertToArray(document.querySelectorAll(this.selectors.abstractPreviewZoom)),this.elements.readCubeSharing=document.querySelector(this.selectors.readCubeSharing),this.elements.shareCtrl=document.querySelector(this.selectors.shareCtrl),this.elements.tocAbstractPreview=UX.utils.convertToArray(document.querySelectorAll(this.selectors.tocAbstractPreview))}},{key:"eventListeners",value:function eventListeners(){UX.fancybox&&(UX.fancybox(this.elements.abstractPreviewZoom),document.addEventListener("keydown",UX.fancyboxClose))}},{key:"onReady",value:function onReady(){(this.checkMenuItem(this.selectors.authorBio),this.checkMenuItem(this.selectors.bookReviews),this.elements.menuBookTab)&&this.elements.menuBookTab.querySelector("li:first-child a").click();if(this.elements.readCubeSharing&&this.elements.shareCtrl){var e=this.elements.shareCtrl.parentElement,t=null==e?void 0:e.querySelector(this.selectors.shareAccessCtrl),n=null==e?void 0:e.querySelector(this.selectors.dropBlockHolder);this.elements.shareCtrl.classList.remove(this.classList.remove),null==t||t.classList.add(this.classList.remove),null==n||n.setAttribute("aria-labelledby","share__ctrl")}}},{key:"onLoad",value:function onLoad(){this.elements.tocAbstractPreview.length&&UX.utils.changeImageWidthOnLoad(this.selectors.tocAbstractPreview,1.75),UX.utils.fallbackImageViewer()}}])}());UX.tocJs.truncate=function(){try{UX.tocJs.$loa.not("[data-truncate='none']").truncate({lines:2,type:"list",seeMoreLink:!0,seeMoreText:"See all authors",seeLessText:"See fewer authors",lessLinkEllipsis:!0})}catch(e){return null}}},203:function(e,t){var n;n={$loa:null,vPort:"screen-xs",init:function init(){var e=$(".to-section");e.length?e.each((function(){var e=$(this).text(),t=$(this).attr("id");$('<li><a class="w-slide__hide" href="#'+t+'"><span>'+e+"</span></a></li>").appendTo(".sections__drop")})):$(".toc-go-section, .toc__section").remove(),n.$loa=$(".issue-item .loa, .search__item .meta__authors, .search__item .loa"),n.control(),n.additionalControl()},control:function control(){$(window).on("load orientationchange",(function(e){n.truncate()})),$(document).on(n.vPort+"-on",(function(){n.isMobile=!0,n.truncate()})),$(document).on(n.vPort+"-off",(function(){n.isMobile=!1,n.truncate()}))},additionalControl:function additionalControl(){},truncate:function truncate(){try{n.$loa.not("[data-truncate='none']").truncate({lines:2,type:"list",seeMoreLink:!0,seeMoreText:"See all authors",seeLessText:"See fewer authors"})}catch(e){console.error(e)}}},UX.tocJs=n},325:function(e,t,n){"use strict";n.r(t);n(326),n(327),n(202);t.default=function main(e){var t=Array.prototype.slice.call(document.getElementsByClassName("js--truncate")),n=Array.prototype.slice.call(document.querySelectorAll(".loa, .meta__authors"));try{t.length&&e.truncater.init(t)}catch(e){console.error(e)}try{n.length&&e.tocJs.init(n)}catch(e){console.error(e)}}},326:function(e,t){UX.truncateInit={init:function init(){$(".creative-work__title").not("[data-truncate='none']").truncate({lines:3,addClass:"min-height"}),$(".creative-work .loa").not("[data-truncate='none']").truncate({lines:2,type:"list",addClass:"loa-height"}),$(".featured .grid-item:first-child .creative-work__title").not("[data-truncate='none']").truncate({lines:2,addClass:"min-height"}),$(".card .creative-work__title").not("[data-truncate='none']").truncate({lines:2,addClass:"min-height"}),$(".card .creative-work .loa").not("[data-truncate='none']").truncate({lines:1,type:"list",addClass:"loa-height"}),$(window).on("resized orientationchange",(function(){$(".creative-work__title, .creative-work .loa").not("[data-truncate='none']").truncate()}))}}},327:function(e,t){var n;n={$target:$(".js--truncate"),init:function init(){n.truncate()},truncate:function truncate(){n.$target.each((function(){$(this).truncate()}))}},UX.truncater=n}}]);
//# sourceMappingURL=truncate-0a3966ed0f5cd4405e6c.js.map