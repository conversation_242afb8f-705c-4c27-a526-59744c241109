define([],function(){return window.utils={waitForElement:function(o){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:3e4;return new Promise(function(e,n){var t=new MutationObserver(function(){var n=document.querySelector(o);n&&null!==n.offsetParent&&(t.disconnect(),e(n))});t.observe(document.body,{childList:!0,subtree:!0}),setTimeout(function(){t.disconnect(),n(new Error('Element with selector "'.concat(o,'" was not found within ').concat(i,"ms.")))},i)})}},window.utils});
//# sourceMappingURL=utils.js.map