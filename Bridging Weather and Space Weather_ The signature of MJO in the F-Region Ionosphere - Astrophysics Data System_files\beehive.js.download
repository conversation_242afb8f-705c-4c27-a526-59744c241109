define(["backbone","underscore","js/components/generic_module","js/mixins/dependon","js/mixins/hardened","js/components/services_container"],function(e,n,t,i,c,r){var s=[],t=t.extend({initialize:function(e){n.extend(this,n.pick(e,s)),this.Services=new r,this.Objects=new r,this.debug=!1,this.active=!0},activate:function(){this.Services.activate.apply(this.Services,arguments),this.Objects.activate(this),this.active=!0},destroy:function(){this.Services.destroy(arguments),this.Objects.destroy(arguments),this.active=!1},getService:function(e){return this.Services.get(e)},hasService:function(e){return this.Services.has(e)},addService:function(e,t){return this.Services.add(e,t)},removeService:function(e){return this.Services.remove(e)},getObject:function(e){return this.Objects.get(e)},hasObject:function(e){return this.Objects.has(e)},addObject:function(e,t){return this.Objects.add(e,t)},removeObject:function(e){return this.Objects.remove(e)},getDebug:function(){return this.debug},getAllServices:function(){return this.Services.getAll()},getAllObjects:function(){return this.Objects.getAll()},hardenedInterface:{Services:"services container",Objects:"objects container",debug:"state of the app",active:"active or not",getHardenedInstance:"allow to create clone of the already hardened instance"}});return n.extend(t.prototype,c,{getHardenedInstance:function(e){(e=n.clone(e||this.hardenedInterface)).getService=function(e){return t.Services.get(e)},e.hasService=function(e){return t.Services.has(e)},e.getObject=function(e){return t.Objects.get(e)},e.hasObject=function(e){return t.Objects.has(e)};var t=this._getHardenedInstance(e,this);return t}}),t});
//# sourceMappingURL=beehive.js.map