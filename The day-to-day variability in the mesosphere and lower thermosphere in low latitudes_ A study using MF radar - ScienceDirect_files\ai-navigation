function Hp(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Jh={exports:{}},Ju={},Kh={exports:{}},Fu={exports:{}};Fu.exports;var P1;function GR(){return P1||(P1=1,function(i,c){/**
 * @license React
 * react.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){function s(f,E){Object.defineProperty(T.prototype,f,{get:function(){console.warn("%s(...) is deprecated in plain JavaScript React classes. %s",E[0],E[1])}})}function h(f){return f===null||typeof f!="object"?null:(f=et&&f[et]||f["@@iterator"],typeof f=="function"?f:null)}function b(f,E){f=(f=f.constructor)&&(f.displayName||f.name)||"ReactClass";var B=f+"."+E;Y[B]||(console.error("Can't call %s on a component that is not yet mounted. This is a no-op, but it might indicate a bug in your application. Instead, assign to `this.state` directly or define a `state = {};` class property with the desired state in the %s component.",E,f),Y[B]=!0)}function T(f,E,B){this.props=f,this.context=E,this.refs=ka,this.updater=B||Tt}function M(){}function H(f,E,B){this.props=f,this.context=E,this.refs=ka,this.updater=B||Tt}function R(f){return""+f}function Z(f){try{R(f);var E=!1}catch{E=!0}if(E){E=console;var B=E.error,V=typeof Symbol=="function"&&Symbol.toStringTag&&f[Symbol.toStringTag]||f.constructor.name||"Object";return B.call(E,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",V),R(f)}}function $(f){if(f==null)return null;if(typeof f=="function")return f.$$typeof===Ts?null:f.displayName||f.name||null;if(typeof f=="string")return f;switch(f){case g:return"Fragment";case N:return"Profiler";case U:return"StrictMode";case Q:return"Suspense";case me:return"SuspenseList";case ye:return"Activity"}if(typeof f=="object")switch(typeof f.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),f.$$typeof){case Se:return"Portal";case P:return(f.displayName||"Context")+".Provider";case K:return(f._context.displayName||"Context")+".Consumer";case se:var E=f.render;return f=f.displayName,f||(f=E.displayName||E.name||"",f=f!==""?"ForwardRef("+f+")":"ForwardRef"),f;case ae:return E=f.displayName||null,E!==null?E:$(f.type)||"Memo";case he:E=f._payload,f=f._init;try{return $(f(E))}catch{}}return null}function C(f){if(f===g)return"<>";if(typeof f=="object"&&f!==null&&f.$$typeof===he)return"<...>";try{var E=$(f);return E?"<"+E+">":"<...>"}catch{return"<...>"}}function S(){var f=Ue.A;return f===null?null:f.getOwner()}function j(){return Error("react-stack-top-frame")}function I(f){if(bl.call(f,"key")){var E=Object.getOwnPropertyDescriptor(f,"key").get;if(E&&E.isReactWarning)return!1}return f.key!==void 0}function ne(f,E){function B(){Ba||(Ba=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",E))}B.isReactWarning=!0,Object.defineProperty(f,"key",{get:B,configurable:!0})}function le(){var f=$(this.type);return Ei[f]||(Ei[f]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),f=this.props.ref,f!==void 0?f:null}function W(f,E,B,V,ie,Me,Oe,Ye){return B=Me.ref,f={$$typeof:ce,type:f,key:E,props:Me,_owner:ie},(B!==void 0?B:null)!==null?Object.defineProperty(f,"ref",{enumerable:!1,get:le}):Object.defineProperty(f,"ref",{enumerable:!1,value:null}),f._store={},Object.defineProperty(f._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(f,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(f,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:Oe}),Object.defineProperty(f,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:Ye}),Object.freeze&&(Object.freeze(f.props),Object.freeze(f)),f}function be(f,E){return E=W(f.type,E,void 0,void 0,f._owner,f.props,f._debugStack,f._debugTask),f._store&&(E._store.validated=f._store.validated),E}function fe(f){return typeof f=="object"&&f!==null&&f.$$typeof===ce}function oe(f){var E={"=":"=0",":":"=2"};return"$"+f.replace(/[=:]/g,function(B){return E[B]})}function pe(f,E){return typeof f=="object"&&f!==null&&f.key!=null?(Z(f.key),oe(""+f.key)):E.toString(36)}function F(){}function G(f){switch(f.status){case"fulfilled":return f.value;case"rejected":throw f.reason;default:switch(typeof f.status=="string"?f.then(F,F):(f.status="pending",f.then(function(E){f.status==="pending"&&(f.status="fulfilled",f.value=E)},function(E){f.status==="pending"&&(f.status="rejected",f.reason=E)})),f.status){case"fulfilled":return f.value;case"rejected":throw f.reason}}throw f}function $e(f,E,B,V,ie){var Me=typeof f;(Me==="undefined"||Me==="boolean")&&(f=null);var Oe=!1;if(f===null)Oe=!0;else switch(Me){case"bigint":case"string":case"number":Oe=!0;break;case"object":switch(f.$$typeof){case ce:case Se:Oe=!0;break;case he:return Oe=f._init,$e(Oe(f._payload),E,B,V,ie)}}if(Oe){Oe=f,ie=ie(Oe);var Ye=V===""?"."+pe(Oe,0):V;return La(ie)?(B="",Ye!=null&&(B=Ye.replace(Oi,"$&/")+"/"),$e(ie,E,B,"",function(Ot){return Ot})):ie!=null&&(fe(ie)&&(ie.key!=null&&(Oe&&Oe.key===ie.key||Z(ie.key)),B=be(ie,B+(ie.key==null||Oe&&Oe.key===ie.key?"":(""+ie.key).replace(Oi,"$&/")+"/")+Ye),V!==""&&Oe!=null&&fe(Oe)&&Oe.key==null&&Oe._store&&!Oe._store.validated&&(B._store.validated=2),ie=B),E.push(ie)),1}if(Oe=0,Ye=V===""?".":V+":",La(f))for(var Ce=0;Ce<f.length;Ce++)V=f[Ce],Me=Ye+pe(V,Ce),Oe+=$e(V,E,B,Me,ie);else if(Ce=h(f),typeof Ce=="function")for(Ce===f.entries&&(Ai||console.warn("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),Ai=!0),f=Ce.call(f),Ce=0;!(V=f.next()).done;)V=V.value,Me=Ye+pe(V,Ce++),Oe+=$e(V,E,B,Me,ie);else if(Me==="object"){if(typeof f.then=="function")return $e(G(f),E,B,V,ie);throw E=String(f),Error("Objects are not valid as a React child (found: "+(E==="[object Object]"?"object with keys {"+Object.keys(f).join(", ")+"}":E)+"). If you meant to render a collection of children, use an array instead.")}return Oe}function X(f,E,B){if(f==null)return f;var V=[],ie=0;return $e(f,V,"","",function(Me){return E.call(B,Me,ie++)}),V}function Ie(f){if(f._status===-1){var E=f._result;E=E(),E.then(function(B){(f._status===0||f._status===-1)&&(f._status=1,f._result=B)},function(B){(f._status===0||f._status===-1)&&(f._status=2,f._result=B)}),f._status===-1&&(f._status=0,f._result=E)}if(f._status===1)return E=f._result,E===void 0&&console.error(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))

Did you accidentally put curly braces around the import?`,E),"default"in E||console.error(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))`,E),E.default;throw f._result}function de(){var f=Ue.H;return f===null&&console.error(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem.`),f}function we(){}function Fe(f){if(ho===null)try{var E=("require"+Math.random()).slice(0,7);ho=(i&&i[E]).call(i,"timers").setImmediate}catch{ho=function(V){Ci===!1&&(Ci=!0,typeof MessageChannel>"u"&&console.error("This browser does not have a MessageChannel implementation, so enqueuing tasks via await act(async () => ...) will fail. Please file an issue at https://github.com/facebook/react/issues if you encounter this warning."));var ie=new MessageChannel;ie.port1.onmessage=V,ie.port2.postMessage(void 0)}}return ho(f)}function at(f){return 1<f.length&&typeof AggregateError=="function"?new AggregateError(f):f[0]}function ct(f,E){E!==Ya-1&&console.error("You seem to have overlapping act() calls, this is not supported. Be sure to await previous act() calls before making a new one. "),Ya=E}function L(f,E,B){var V=Ue.actQueue;if(V!==null)if(V.length!==0)try{ue(V),Fe(function(){return L(f,E,B)});return}catch(ie){Ue.thrownErrors.push(ie)}else Ue.actQueue=null;0<Ue.thrownErrors.length?(V=at(Ue.thrownErrors),Ue.thrownErrors.length=0,B(V)):E(f)}function ue(f){if(!Nn){Nn=!0;var E=0;try{for(;E<f.length;E++){var B=f[E];do{Ue.didUsePromise=!1;var V=B(!1);if(V!==null){if(Ue.didUsePromise){f[E]=B,f.splice(0,E);return}B=V}else break}while(!0)}f.length=0}catch(ie){f.splice(0,E+1),Ue.thrownErrors.push(ie)}finally{Nn=!1}}}typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var ce=Symbol.for("react.transitional.element"),Se=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),U=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),K=Symbol.for("react.consumer"),P=Symbol.for("react.context"),se=Symbol.for("react.forward_ref"),Q=Symbol.for("react.suspense"),me=Symbol.for("react.suspense_list"),ae=Symbol.for("react.memo"),he=Symbol.for("react.lazy"),ye=Symbol.for("react.activity"),et=Symbol.iterator,Y={},Tt={isMounted:function(){return!1},enqueueForceUpdate:function(f){b(f,"forceUpdate")},enqueueReplaceState:function(f){b(f,"replaceState")},enqueueSetState:function(f){b(f,"setState")}},sa=Object.assign,ka={};Object.freeze(ka),T.prototype.isReactComponent={},T.prototype.setState=function(f,E){if(typeof f!="object"&&typeof f!="function"&&f!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,f,E,"setState")},T.prototype.forceUpdate=function(f){this.updater.enqueueForceUpdate(this,f,"forceUpdate")};var yt={isMounted:["isMounted","Instead, make sure to clean up subscriptions and pending requests in componentWillUnmount to prevent memory leaks."],replaceState:["replaceState","Refactor your code to use setState instead (see https://github.com/facebook/react/issues/3236)."]},fa;for(fa in yt)yt.hasOwnProperty(fa)&&s(fa,yt[fa]);M.prototype=T.prototype,yt=H.prototype=new M,yt.constructor=H,sa(yt,T.prototype),yt.isPureReactComponent=!0;var La=Array.isArray,Ts=Symbol.for("react.client.reference"),Ue={H:null,A:null,T:null,S:null,V:null,actQueue:null,isBatchingLegacy:!1,didScheduleLegacyUpdate:!1,didUsePromise:!1,thrownErrors:[],getCurrentStack:null,recentlyCreatedOwnerStacks:0},bl=Object.prototype.hasOwnProperty,so=console.createTask?console.createTask:function(){return null};yt={react_stack_bottom_frame:function(f){return f()}};var Ba,lr,Ei={},or=yt.react_stack_bottom_frame.bind(yt,j)(),Ri=so(C(j)),Ai=!1,Oi=/\/+/g,fo=typeof reportError=="function"?reportError:function(f){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var E=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof f=="object"&&f!==null&&typeof f.message=="string"?String(f.message):String(f),error:f});if(!window.dispatchEvent(E))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",f);return}console.error(f)},Ci=!1,ho=null,Ya=0,jn=!1,Nn=!1,qa=typeof queueMicrotask=="function"?function(f){queueMicrotask(function(){return queueMicrotask(f)})}:Fe;yt=Object.freeze({__proto__:null,c:function(f){return de().useMemoCache(f)}}),c.Children={map:X,forEach:function(f,E,B){X(f,function(){E.apply(this,arguments)},B)},count:function(f){var E=0;return X(f,function(){E++}),E},toArray:function(f){return X(f,function(E){return E})||[]},only:function(f){if(!fe(f))throw Error("React.Children.only expected to receive a single React element child.");return f}},c.Component=T,c.Fragment=g,c.Profiler=N,c.PureComponent=H,c.StrictMode=U,c.Suspense=Q,c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Ue,c.__COMPILER_RUNTIME=yt,c.act=function(f){var E=Ue.actQueue,B=Ya;Ya++;var V=Ue.actQueue=E!==null?E:[],ie=!1;try{var Me=f()}catch(Ce){Ue.thrownErrors.push(Ce)}if(0<Ue.thrownErrors.length)throw ct(E,B),f=at(Ue.thrownErrors),Ue.thrownErrors.length=0,f;if(Me!==null&&typeof Me=="object"&&typeof Me.then=="function"){var Oe=Me;return qa(function(){ie||jn||(jn=!0,console.error("You called act(async () => ...) without await. This could lead to unexpected testing behaviour, interleaving multiple act calls and mixing their scopes. You should - await act(async () => ...);"))}),{then:function(Ce,Ot){ie=!0,Oe.then(function(da){if(ct(E,B),B===0){try{ue(V),Fe(function(){return L(da,Ce,Ot)})}catch(Rs){Ue.thrownErrors.push(Rs)}if(0<Ue.thrownErrors.length){var Es=at(Ue.thrownErrors);Ue.thrownErrors.length=0,Ot(Es)}}else Ce(da)},function(da){ct(E,B),0<Ue.thrownErrors.length&&(da=at(Ue.thrownErrors),Ue.thrownErrors.length=0),Ot(da)})}}}var Ye=Me;if(ct(E,B),B===0&&(ue(V),V.length!==0&&qa(function(){ie||jn||(jn=!0,console.error("A component suspended inside an `act` scope, but the `act` call was not awaited. When testing React components that depend on asynchronous data, you must await the result:\n\nawait act(() => ...)"))}),Ue.actQueue=null),0<Ue.thrownErrors.length)throw f=at(Ue.thrownErrors),Ue.thrownErrors.length=0,f;return{then:function(Ce,Ot){ie=!0,B===0?(Ue.actQueue=V,Fe(function(){return L(Ye,Ce,Ot)})):Ce(Ye)}}},c.cache=function(f){return function(){return f.apply(null,arguments)}},c.captureOwnerStack=function(){var f=Ue.getCurrentStack;return f===null?null:f()},c.cloneElement=function(f,E,B){if(f==null)throw Error("The argument must be a React element, but you passed "+f+".");var V=sa({},f.props),ie=f.key,Me=f._owner;if(E!=null){var Oe;e:{if(bl.call(E,"ref")&&(Oe=Object.getOwnPropertyDescriptor(E,"ref").get)&&Oe.isReactWarning){Oe=!1;break e}Oe=E.ref!==void 0}Oe&&(Me=S()),I(E)&&(Z(E.key),ie=""+E.key);for(Ye in E)!bl.call(E,Ye)||Ye==="key"||Ye==="__self"||Ye==="__source"||Ye==="ref"&&E.ref===void 0||(V[Ye]=E[Ye])}var Ye=arguments.length-2;if(Ye===1)V.children=B;else if(1<Ye){Oe=Array(Ye);for(var Ce=0;Ce<Ye;Ce++)Oe[Ce]=arguments[Ce+2];V.children=Oe}for(V=W(f.type,ie,void 0,void 0,Me,V,f._debugStack,f._debugTask),ie=2;ie<arguments.length;ie++)Me=arguments[ie],fe(Me)&&Me._store&&(Me._store.validated=1);return V},c.createContext=function(f){return f={$$typeof:P,_currentValue:f,_currentValue2:f,_threadCount:0,Provider:null,Consumer:null},f.Provider=f,f.Consumer={$$typeof:K,_context:f},f._currentRenderer=null,f._currentRenderer2=null,f},c.createElement=function(f,E,B){for(var V=2;V<arguments.length;V++){var ie=arguments[V];fe(ie)&&ie._store&&(ie._store.validated=1)}if(V={},ie=null,E!=null)for(Ce in lr||!("__self"in E)||"key"in E||(lr=!0,console.warn("Your app (or one of its dependencies) is using an outdated JSX transform. Update to the modern JSX transform for faster performance: https://react.dev/link/new-jsx-transform")),I(E)&&(Z(E.key),ie=""+E.key),E)bl.call(E,Ce)&&Ce!=="key"&&Ce!=="__self"&&Ce!=="__source"&&(V[Ce]=E[Ce]);var Me=arguments.length-2;if(Me===1)V.children=B;else if(1<Me){for(var Oe=Array(Me),Ye=0;Ye<Me;Ye++)Oe[Ye]=arguments[Ye+2];Object.freeze&&Object.freeze(Oe),V.children=Oe}if(f&&f.defaultProps)for(Ce in Me=f.defaultProps,Me)V[Ce]===void 0&&(V[Ce]=Me[Ce]);ie&&ne(V,typeof f=="function"?f.displayName||f.name||"Unknown":f);var Ce=1e4>Ue.recentlyCreatedOwnerStacks++;return W(f,ie,void 0,void 0,S(),V,Ce?Error("react-stack-top-frame"):or,Ce?so(C(f)):Ri)},c.createRef=function(){var f={current:null};return Object.seal(f),f},c.forwardRef=function(f){f!=null&&f.$$typeof===ae?console.error("forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)), use memo(forwardRef(...))."):typeof f!="function"?console.error("forwardRef requires a render function but was given %s.",f===null?"null":typeof f):f.length!==0&&f.length!==2&&console.error("forwardRef render functions accept exactly two parameters: props and ref. %s",f.length===1?"Did you forget to use the ref parameter?":"Any additional parameter will be undefined."),f!=null&&f.defaultProps!=null&&console.error("forwardRef render functions do not support defaultProps. Did you accidentally pass a React component?");var E={$$typeof:se,render:f},B;return Object.defineProperty(E,"displayName",{enumerable:!1,configurable:!0,get:function(){return B},set:function(V){B=V,f.name||f.displayName||(Object.defineProperty(f,"name",{value:V}),f.displayName=V)}}),E},c.isValidElement=fe,c.lazy=function(f){return{$$typeof:he,_payload:{_status:-1,_result:f},_init:Ie}},c.memo=function(f,E){f==null&&console.error("memo: The first argument must be a component. Instead received: %s",f===null?"null":typeof f),E={$$typeof:ae,type:f,compare:E===void 0?null:E};var B;return Object.defineProperty(E,"displayName",{enumerable:!1,configurable:!0,get:function(){return B},set:function(V){B=V,f.name||f.displayName||(Object.defineProperty(f,"name",{value:V}),f.displayName=V)}}),E},c.startTransition=function(f){var E=Ue.T,B={};Ue.T=B,B._updatedFibers=new Set;try{var V=f(),ie=Ue.S;ie!==null&&ie(B,V),typeof V=="object"&&V!==null&&typeof V.then=="function"&&V.then(we,fo)}catch(Me){fo(Me)}finally{E===null&&B._updatedFibers&&(f=B._updatedFibers.size,B._updatedFibers.clear(),10<f&&console.warn("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table.")),Ue.T=E}},c.unstable_useCacheRefresh=function(){return de().useCacheRefresh()},c.use=function(f){return de().use(f)},c.useActionState=function(f,E,B){return de().useActionState(f,E,B)},c.useCallback=function(f,E){return de().useCallback(f,E)},c.useContext=function(f){var E=de();return f.$$typeof===K&&console.error("Calling useContext(Context.Consumer) is not supported and will cause bugs. Did you mean to call useContext(Context) instead?"),E.useContext(f)},c.useDebugValue=function(f,E){return de().useDebugValue(f,E)},c.useDeferredValue=function(f,E){return de().useDeferredValue(f,E)},c.useEffect=function(f,E,B){f==null&&console.warn("React Hook useEffect requires an effect callback. Did you forget to pass a callback to the hook?");var V=de();if(typeof B=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return V.useEffect(f,E)},c.useId=function(){return de().useId()},c.useImperativeHandle=function(f,E,B){return de().useImperativeHandle(f,E,B)},c.useInsertionEffect=function(f,E){return f==null&&console.warn("React Hook useInsertionEffect requires an effect callback. Did you forget to pass a callback to the hook?"),de().useInsertionEffect(f,E)},c.useLayoutEffect=function(f,E){return f==null&&console.warn("React Hook useLayoutEffect requires an effect callback. Did you forget to pass a callback to the hook?"),de().useLayoutEffect(f,E)},c.useMemo=function(f,E){return de().useMemo(f,E)},c.useOptimistic=function(f,E){return de().useOptimistic(f,E)},c.useReducer=function(f,E,B){return de().useReducer(f,E,B)},c.useRef=function(f){return de().useRef(f)},c.useState=function(f){return de().useState(f)},c.useSyncExternalStore=function(f,E,B){return de().useSyncExternalStore(f,E,B)},c.useTransition=function(){return de().useTransition()},c.version="19.1.1",typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())})()}(Fu,Fu.exports)),Fu.exports}var W1;function hs(){return W1||(W1=1,Kh.exports=GR()),Kh.exports}var F1;function XR(){if(F1)return Ju;F1=1;/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */return function(){function i(g){if(g==null)return null;if(typeof g=="function")return g.$$typeof===Ie?null:g.displayName||g.name||null;if(typeof g=="string")return g;switch(g){case ne:return"Fragment";case W:return"Profiler";case le:return"StrictMode";case pe:return"Suspense";case F:return"SuspenseList";case X:return"Activity"}if(typeof g=="object")switch(typeof g.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),g.$$typeof){case I:return"Portal";case fe:return(g.displayName||"Context")+".Provider";case be:return(g._context.displayName||"Context")+".Consumer";case oe:var U=g.render;return g=g.displayName,g||(g=U.displayName||U.name||"",g=g!==""?"ForwardRef("+g+")":"ForwardRef"),g;case G:return U=g.displayName||null,U!==null?U:i(g.type)||"Memo";case $e:U=g._payload,g=g._init;try{return i(g(U))}catch{}}return null}function c(g){return""+g}function s(g){try{c(g);var U=!1}catch{U=!0}if(U){U=console;var N=U.error,K=typeof Symbol=="function"&&Symbol.toStringTag&&g[Symbol.toStringTag]||g.constructor.name||"Object";return N.call(U,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",K),c(g)}}function h(g){if(g===ne)return"<>";if(typeof g=="object"&&g!==null&&g.$$typeof===$e)return"<...>";try{var U=i(g);return U?"<"+U+">":"<...>"}catch{return"<...>"}}function b(){var g=de.A;return g===null?null:g.getOwner()}function T(){return Error("react-stack-top-frame")}function M(g){if(we.call(g,"key")){var U=Object.getOwnPropertyDescriptor(g,"key").get;if(U&&U.isReactWarning)return!1}return g.key!==void 0}function H(g,U){function N(){ct||(ct=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",U))}N.isReactWarning=!0,Object.defineProperty(g,"key",{get:N,configurable:!0})}function R(){var g=i(this.type);return L[g]||(L[g]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),g=this.props.ref,g!==void 0?g:null}function Z(g,U,N,K,P,se,Q,me){return N=se.ref,g={$$typeof:j,type:g,key:U,props:se,_owner:P},(N!==void 0?N:null)!==null?Object.defineProperty(g,"ref",{enumerable:!1,get:R}):Object.defineProperty(g,"ref",{enumerable:!1,value:null}),g._store={},Object.defineProperty(g._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(g,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(g,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:Q}),Object.defineProperty(g,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:me}),Object.freeze&&(Object.freeze(g.props),Object.freeze(g)),g}function $(g,U,N,K,P,se,Q,me){var ae=U.children;if(ae!==void 0)if(K)if(Fe(ae)){for(K=0;K<ae.length;K++)C(ae[K]);Object.freeze&&Object.freeze(ae)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else C(ae);if(we.call(U,"key")){ae=i(g);var he=Object.keys(U).filter(function(et){return et!=="key"});K=0<he.length?"{key: someKey, "+he.join(": ..., ")+": ...}":"{key: someKey}",Se[ae+K]||(he=0<he.length?"{"+he.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,K,ae,he,ae),Se[ae+K]=!0)}if(ae=null,N!==void 0&&(s(N),ae=""+N),M(U)&&(s(U.key),ae=""+U.key),"key"in U){N={};for(var ye in U)ye!=="key"&&(N[ye]=U[ye])}else N=U;return ae&&H(N,typeof g=="function"?g.displayName||g.name||"Unknown":g),Z(g,ae,se,P,b(),N,Q,me)}function C(g){typeof g=="object"&&g!==null&&g.$$typeof===j&&g._store&&(g._store.validated=1)}var S=hs(),j=Symbol.for("react.transitional.element"),I=Symbol.for("react.portal"),ne=Symbol.for("react.fragment"),le=Symbol.for("react.strict_mode"),W=Symbol.for("react.profiler"),be=Symbol.for("react.consumer"),fe=Symbol.for("react.context"),oe=Symbol.for("react.forward_ref"),pe=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),G=Symbol.for("react.memo"),$e=Symbol.for("react.lazy"),X=Symbol.for("react.activity"),Ie=Symbol.for("react.client.reference"),de=S.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,we=Object.prototype.hasOwnProperty,Fe=Array.isArray,at=console.createTask?console.createTask:function(){return null};S={react_stack_bottom_frame:function(g){return g()}};var ct,L={},ue=S.react_stack_bottom_frame.bind(S,T)(),ce=at(h(T)),Se={};Ju.Fragment=ne,Ju.jsx=function(g,U,N,K,P){var se=1e4>de.recentlyCreatedOwnerStacks++;return $(g,U,N,!1,K,P,se?Error("react-stack-top-frame"):ue,se?at(h(g)):ce)},Ju.jsxs=function(g,U,N,K,P){var se=1e4>de.recentlyCreatedOwnerStacks++;return $(g,U,N,!0,K,P,se?Error("react-stack-top-frame"):ue,se?at(h(g)):ce)}}(),Ju}var eb;function QR(){return eb||(eb=1,Jh.exports=XR()),Jh.exports}var ve=QR(),nn=hs();const De=Hp(nn);var an=function(){return an=Object.assign||function(c){for(var s,h=1,b=arguments.length;h<b;h++){s=arguments[h];for(var T in s)Object.prototype.hasOwnProperty.call(s,T)&&(c[T]=s[T])}return c},an.apply(this,arguments)};function mi(i,c,s){if(s||arguments.length===2)for(var h=0,b=c.length,T;h<b;h++)(T||!(h in c))&&(T||(T=Array.prototype.slice.call(c,0,h)),T[h]=c[h]);return i.concat(T||Array.prototype.slice.call(c))}var it="-ms-",tr="-moz-",Ze="-webkit-",Nb="comm",ps="rule",jp="decl",ZR="@import",kb="@keyframes",IR="@layer",Lb=Math.abs,Np=String.fromCharCode,mp=Object.assign;function JR(i,c){return Dt(i,0)^45?(((c<<2^Dt(i,0))<<2^Dt(i,1))<<2^Dt(i,2))<<2^Dt(i,3):0}function Bb(i){return i.trim()}function Na(i,c){return(i=c.exec(i))?i[0]:i}function ze(i,c,s){return i.replace(c,s)}function ls(i,c,s){return i.indexOf(c,s)}function Dt(i,c){return i.charCodeAt(c)|0}function yi(i,c,s){return i.slice(c,s)}function ca(i){return i.length}function Yb(i){return i.length}function er(i,c){return c.push(i),i}function KR(i,c){return i.map(c).join("")}function tb(i,c){return i.filter(function(s){return!Na(s,c)})}var ms=1,gi=1,qb=0,Un=0,St=0,Si="";function ys(i,c,s,h,b,T,M,H){return{value:i,root:c,parent:s,type:h,props:b,children:T,line:ms,column:gi,length:M,return:"",siblings:H}}function gl(i,c){return mp(ys("",null,null,"",null,null,0,i.siblings),i,{length:-i.length},c)}function pi(i){for(;i.root;)i=gl(i.root,{children:[i]});er(i,i.siblings)}function PR(){return St}function WR(){return St=Un>0?Dt(Si,--Un):0,gi--,St===10&&(gi=1,ms--),St}function Xn(){return St=Un<qb?Dt(Si,Un++):0,gi++,St===10&&(gi=1,ms++),St}function oo(){return Dt(Si,Un)}function os(){return Un}function gs(i,c){return yi(Si,i,c)}function yp(i){switch(i){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function FR(i){return ms=gi=1,qb=ca(Si=i),Un=0,[]}function eA(i){return Si="",i}function Ph(i){return Bb(gs(Un-1,gp(i===91?i+2:i===40?i+1:i)))}function tA(i){for(;(St=oo())&&St<33;)Xn();return yp(i)>2||yp(St)>3?"":" "}function nA(i,c){for(;--c&&Xn()&&!(St<48||St>102||St>57&&St<65||St>70&&St<97););return gs(i,os()+(c<6&&oo()==32&&Xn()==32))}function gp(i){for(;Xn();)switch(St){case i:return Un;case 34:case 39:i!==34&&i!==39&&gp(St);break;case 40:i===41&&gp(i);break;case 92:Xn();break}return Un}function aA(i,c){for(;Xn()&&i+St!==57;)if(i+St===84&&oo()===47)break;return"/*"+gs(c,Un-1)+"*"+Np(i===47?i:Xn())}function lA(i){for(;!yp(oo());)Xn();return gs(i,Un)}function oA(i){return eA(is("",null,null,null,[""],i=FR(i),0,[0],i))}function is(i,c,s,h,b,T,M,H,R){for(var Z=0,$=0,C=M,S=0,j=0,I=0,ne=1,le=1,W=1,be=0,fe="",oe=b,pe=T,F=h,G=fe;le;)switch(I=be,be=Xn()){case 40:if(I!=108&&Dt(G,C-1)==58){ls(G+=ze(Ph(be),"&","&\f"),"&\f",Lb(Z?H[Z-1]:0))!=-1&&(W=-1);break}case 34:case 39:case 91:G+=Ph(be);break;case 9:case 10:case 13:case 32:G+=tA(I);break;case 92:G+=nA(os()-1,7);continue;case 47:switch(oo()){case 42:case 47:er(iA(aA(Xn(),os()),c,s,R),R);break;default:G+="/"}break;case 123*ne:H[Z++]=ca(G)*W;case 125*ne:case 59:case 0:switch(be){case 0:case 125:le=0;case 59+$:W==-1&&(G=ze(G,/\f/g,"")),j>0&&ca(G)-C&&er(j>32?ab(G+";",h,s,C-1,R):ab(ze(G," ","")+";",h,s,C-2,R),R);break;case 59:G+=";";default:if(er(F=nb(G,c,s,Z,$,b,H,fe,oe=[],pe=[],C,T),T),be===123)if($===0)is(G,c,F,F,oe,T,C,H,pe);else switch(S===99&&Dt(G,3)===110?100:S){case 100:case 108:case 109:case 115:is(i,F,F,h&&er(nb(i,F,F,0,0,b,H,fe,b,oe=[],C,pe),pe),b,pe,C,H,h?oe:pe);break;default:is(G,F,F,F,[""],pe,0,H,pe)}}Z=$=j=0,ne=W=1,fe=G="",C=M;break;case 58:C=1+ca(G),j=I;default:if(ne<1){if(be==123)--ne;else if(be==125&&ne++==0&&WR()==125)continue}switch(G+=Np(be),be*ne){case 38:W=$>0?1:(G+="\f",-1);break;case 44:H[Z++]=(ca(G)-1)*W,W=1;break;case 64:oo()===45&&(G+=Ph(Xn())),S=oo(),$=C=ca(fe=G+=lA(os())),be++;break;case 45:I===45&&ca(G)==2&&(ne=0)}}return T}function nb(i,c,s,h,b,T,M,H,R,Z,$,C){for(var S=b-1,j=b===0?T:[""],I=Yb(j),ne=0,le=0,W=0;ne<h;++ne)for(var be=0,fe=yi(i,S+1,S=Lb(le=M[ne])),oe=i;be<I;++be)(oe=Bb(le>0?j[be]+" "+fe:ze(fe,/&\f/g,j[be])))&&(R[W++]=oe);return ys(i,c,s,b===0?ps:H,R,Z,$,C)}function iA(i,c,s,h){return ys(i,c,s,Nb,Np(PR()),yi(i,2,-2),0,h)}function ab(i,c,s,h,b){return ys(i,c,s,jp,yi(i,0,h),yi(i,h+1,-1),h,b)}function $b(i,c,s){switch(JR(i,c)){case 5103:return Ze+"print-"+i+i;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Ze+i+i;case 4789:return tr+i+i;case 5349:case 4246:case 4810:case 6968:case 2756:return Ze+i+tr+i+it+i+i;case 5936:switch(Dt(i,c+11)){case 114:return Ze+i+it+ze(i,/[svh]\w+-[tblr]{2}/,"tb")+i;case 108:return Ze+i+it+ze(i,/[svh]\w+-[tblr]{2}/,"tb-rl")+i;case 45:return Ze+i+it+ze(i,/[svh]\w+-[tblr]{2}/,"lr")+i}case 6828:case 4268:case 2903:return Ze+i+it+i+i;case 6165:return Ze+i+it+"flex-"+i+i;case 5187:return Ze+i+ze(i,/(\w+).+(:[^]+)/,Ze+"box-$1$2"+it+"flex-$1$2")+i;case 5443:return Ze+i+it+"flex-item-"+ze(i,/flex-|-self/g,"")+(Na(i,/flex-|baseline/)?"":it+"grid-row-"+ze(i,/flex-|-self/g,""))+i;case 4675:return Ze+i+it+"flex-line-pack"+ze(i,/align-content|flex-|-self/g,"")+i;case 5548:return Ze+i+it+ze(i,"shrink","negative")+i;case 5292:return Ze+i+it+ze(i,"basis","preferred-size")+i;case 6060:return Ze+"box-"+ze(i,"-grow","")+Ze+i+it+ze(i,"grow","positive")+i;case 4554:return Ze+ze(i,/([^-])(transform)/g,"$1"+Ze+"$2")+i;case 6187:return ze(ze(ze(i,/(zoom-|grab)/,Ze+"$1"),/(image-set)/,Ze+"$1"),i,"")+i;case 5495:case 3959:return ze(i,/(image-set\([^]*)/,Ze+"$1$`$1");case 4968:return ze(ze(i,/(.+:)(flex-)?(.*)/,Ze+"box-pack:$3"+it+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Ze+i+i;case 4200:if(!Na(i,/flex-|baseline/))return it+"grid-column-align"+yi(i,c)+i;break;case 2592:case 3360:return it+ze(i,"template-","")+i;case 4384:case 3616:return s&&s.some(function(h,b){return c=b,Na(h.props,/grid-\w+-end/)})?~ls(i+(s=s[c].value),"span",0)?i:it+ze(i,"-start","")+i+it+"grid-row-span:"+(~ls(s,"span",0)?Na(s,/\d+/):+Na(s,/\d+/)-+Na(i,/\d+/))+";":it+ze(i,"-start","")+i;case 4896:case 4128:return s&&s.some(function(h){return Na(h.props,/grid-\w+-start/)})?i:it+ze(ze(i,"-end","-span"),"span ","")+i;case 4095:case 3583:case 4068:case 2532:return ze(i,/(.+)-inline(.+)/,Ze+"$1$2")+i;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(ca(i)-1-c>6)switch(Dt(i,c+1)){case 109:if(Dt(i,c+4)!==45)break;case 102:return ze(i,/(.+:)(.+)-([^]+)/,"$1"+Ze+"$2-$3$1"+tr+(Dt(i,c+3)==108?"$3":"$2-$3"))+i;case 115:return~ls(i,"stretch",0)?$b(ze(i,"stretch","fill-available"),c,s)+i:i}break;case 5152:case 5920:return ze(i,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(h,b,T,M,H,R,Z){return it+b+":"+T+Z+(M?it+b+"-span:"+(H?R:+R-+T)+Z:"")+i});case 4949:if(Dt(i,c+6)===121)return ze(i,":",":"+Ze)+i;break;case 6444:switch(Dt(i,Dt(i,14)===45?18:11)){case 120:return ze(i,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+Ze+(Dt(i,14)===45?"inline-":"")+"box$3$1"+Ze+"$2$3$1"+it+"$2box$3")+i;case 100:return ze(i,":",":"+it)+i}break;case 5719:case 2647:case 2135:case 3927:case 2391:return ze(i,"scroll-","scroll-snap-")+i}return i}function ss(i,c){for(var s="",h=0;h<i.length;h++)s+=c(i[h],h,i,c)||"";return s}function uA(i,c,s,h){switch(i.type){case IR:if(i.children.length)break;case ZR:case jp:return i.return=i.return||i.value;case Nb:return"";case kb:return i.return=i.value+"{"+ss(i.children,h)+"}";case ps:if(!ca(i.value=i.props.join(",")))return""}return ca(s=ss(i.children,h))?i.return=i.value+"{"+s+"}":""}function rA(i){var c=Yb(i);return function(s,h,b,T){for(var M="",H=0;H<c;H++)M+=i[H](s,h,b,T)||"";return M}}function cA(i){return function(c){c.root||(c=c.return)&&i(c)}}function sA(i,c,s,h){if(i.length>-1&&!i.return)switch(i.type){case jp:i.return=$b(i.value,i.length,s);return;case kb:return ss([gl(i,{value:ze(i.value,"@","@"+Ze)})],h);case ps:if(i.length)return KR(s=i.props,function(b){switch(Na(b,h=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":pi(gl(i,{props:[ze(b,/:(read-\w+)/,":"+tr+"$1")]})),pi(gl(i,{props:[b]})),mp(i,{props:tb(s,h)});break;case"::placeholder":pi(gl(i,{props:[ze(b,/:(plac\w+)/,":"+Ze+"input-$1")]})),pi(gl(i,{props:[ze(b,/:(plac\w+)/,":"+tr+"$1")]})),pi(gl(i,{props:[ze(b,/:(plac\w+)/,it+"input-$1")]})),pi(gl(i,{props:[b]})),mp(i,{props:tb(s,h)});break}return""})}}var fA={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},mn={},ro=typeof process<"u"&&mn!==void 0&&(mn.REACT_APP_SC_ATTR||mn.SC_ATTR)||"data-styled",Vb="active",Gb="data-styled-version",vs="6.1.15",kp=`/*!sc*/
`,fs=typeof window<"u"&&"HTMLElement"in window,dA=!!(typeof SC_DISABLE_SPEEDY=="boolean"?SC_DISABLE_SPEEDY:typeof process<"u"&&mn!==void 0&&mn.REACT_APP_SC_DISABLE_SPEEDY!==void 0&&mn.REACT_APP_SC_DISABLE_SPEEDY!==""?mn.REACT_APP_SC_DISABLE_SPEEDY!=="false"&&mn.REACT_APP_SC_DISABLE_SPEEDY:!(typeof process<"u"&&mn!==void 0&&mn.SC_DISABLE_SPEEDY!==void 0&&mn.SC_DISABLE_SPEEDY!=="")||mn.SC_DISABLE_SPEEDY!=="false"&&mn.SC_DISABLE_SPEEDY),lb=/invalid hook call/i,Fc=new Set,hA=function(i,c){{var s=c?' with the id of "'.concat(c,'"'):"",h="The component ".concat(i).concat(s,` has been created dynamically.
`)+`You may see this warning because you've called styled inside another component.
To resolve this only create new StyledComponents outside of any render method and function component.`,b=console.error;try{var T=!0;console.error=function(M){for(var H=[],R=1;R<arguments.length;R++)H[R-1]=arguments[R];lb.test(M)?(T=!1,Fc.delete(h)):b.apply(void 0,mi([M],H,!1))},nn.useRef(),T&&!Fc.has(h)&&(console.warn(h),Fc.add(h))}catch(M){lb.test(M.message)&&Fc.delete(h)}finally{console.error=b}}},bs=Object.freeze([]),vi=Object.freeze({});function pA(i,c,s){return s===void 0&&(s=vi),i.theme!==s.theme&&i.theme||c||s.theme}var Xb=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),mA=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,yA=/(^-|-$)/g;function ob(i){return i.replace(mA,"-").replace(yA,"")}var gA=/(a)(d)/gi,es=52,ib=function(i){return String.fromCharCode(i+(i>25?39:97))};function vp(i){var c,s="";for(c=Math.abs(i);c>es;c=c/es|0)s=ib(c%es)+s;return(ib(c%es)+s).replace(gA,"$1-$2")}var Wh,Qb=5381,ao=function(i,c){for(var s=c.length;s;)i=33*i^c.charCodeAt(--s);return i},Zb=function(i){return ao(Qb,i)};function vA(i){return vp(Zb(i)>>>0)}function Ib(i){return typeof i=="string"&&i||i.displayName||i.name||"Component"}function Fh(i){return typeof i=="string"&&i.charAt(0)===i.charAt(0).toLowerCase()}var Jb=typeof Symbol=="function"&&Symbol.for,Kb=Jb?Symbol.for("react.memo"):60115,bA=Jb?Symbol.for("react.forward_ref"):60112,SA={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},TA={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Pb={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},EA=((Wh={})[bA]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Wh[Kb]=Pb,Wh);function ub(i){return("type"in(c=i)&&c.type.$$typeof)===Kb?Pb:"$$typeof"in i?EA[i.$$typeof]:SA;var c}var RA=Object.defineProperty,AA=Object.getOwnPropertyNames,rb=Object.getOwnPropertySymbols,OA=Object.getOwnPropertyDescriptor,CA=Object.getPrototypeOf,cb=Object.prototype;function Wb(i,c,s){if(typeof c!="string"){if(cb){var h=CA(c);h&&h!==cb&&Wb(i,h,s)}var b=AA(c);rb&&(b=b.concat(rb(c)));for(var T=ub(i),M=ub(c),H=0;H<b.length;++H){var R=b[H];if(!(R in TA||s&&s[R]||M&&R in M||T&&R in T)){var Z=OA(c,R);try{RA(i,R,Z)}catch{}}}}return i}function nr(i){return typeof i=="function"}function Fb(i){return typeof i=="object"&&"styledComponentId"in i}function lo(i,c){return i&&c?"".concat(i," ").concat(c):i||c||""}function sb(i,c){if(i.length===0)return"";for(var s=i[0],h=1;h<i.length;h++)s+=i[h];return s}function bi(i){return i!==null&&typeof i=="object"&&i.constructor.name===Object.name&&!("props"in i&&i.$$typeof)}function bp(i,c,s){if(s===void 0&&(s=!1),!s&&!bi(i)&&!Array.isArray(i))return c;if(Array.isArray(c))for(var h=0;h<c.length;h++)i[h]=bp(i[h],c[h]);else if(bi(c))for(var h in c)i[h]=bp(i[h],c[h]);return i}function Lp(i,c){Object.defineProperty(i,"toString",{value:c})}var wA={1:`Cannot create styled-component for component: %s.

`,2:`Can't collect styles once you've consumed a \`ServerStyleSheet\`'s styles! \`ServerStyleSheet\` is a one off instance for each server-side render cycle.

- Are you trying to reuse it across renders?
- Are you accidentally calling collectStyles twice?

`,3:`Streaming SSR is only supported in a Node.js environment; Please do not try to call this method in the browser.

`,4:`The \`StyleSheetManager\` expects a valid target or sheet prop!

- Does this error occur on the client and is your target falsy?
- Does this error occur on the server and is the sheet falsy?

`,5:`The clone method cannot be used on the client!

- Are you running in a client-like environment on the server?
- Are you trying to run SSR on the client?

`,6:`Trying to insert a new style tag, but the given Node is unmounted!

- Are you using a custom target that isn't mounted?
- Does your document not have a valid head element?
- Have you accidentally removed a style tag manually?

`,7:'ThemeProvider: Please return an object from your "theme" prop function, e.g.\n\n```js\ntheme={() => ({})}\n```\n\n',8:`ThemeProvider: Please make your "theme" prop an object.

`,9:"Missing document `<head>`\n\n",10:`Cannot find a StyleSheet instance. Usually this happens if there are multiple copies of styled-components loaded at once. Check out this issue for how to troubleshoot and fix the common cases where this situation can happen: https://github.com/styled-components/styled-components/issues/1941#issuecomment-417862021

`,11:`_This error was replaced with a dev-time warning, it will be deleted for v4 final._ [createGlobalStyle] received children which will not be rendered. Please use the component without passing children elements.

`,12:"It seems you are interpolating a keyframe declaration (%s) into an untagged string. This was supported in styled-components v3, but is not longer supported in v4 as keyframes are now injected on-demand. Please wrap your string in the css\\`\\` helper which ensures the styles are injected correctly. See https://www.styled-components.com/docs/api#css\n\n",13:`%s is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.

`,14:`ThemeProvider: "theme" prop is required.

`,15:"A stylis plugin has been supplied that is not named. We need a name for each plugin to be able to prevent styling collisions between different stylis configurations within the same app. Before you pass your plugin to `<StyleSheetManager stylisPlugins={[]}>`, please make sure each plugin is uniquely-named, e.g.\n\n```js\nObject.defineProperty(importedPlugin, 'name', { value: 'some-unique-name' });\n```\n\n",16:`Reached the limit of how many styled components may be created at group %s.
You may only create up to 1,073,741,824 components. If you're creating components dynamically,
as for instance in your render method then you may be running into this limitation.

`,17:`CSSStyleSheet could not be found on HTMLStyleElement.
Has styled-components' style tag been unmounted or altered by another script?
`,18:"ThemeProvider: Please make sure your useTheme hook is within a `<ThemeProvider>`"};function xA(){for(var i=[],c=0;c<arguments.length;c++)i[c]=arguments[c];for(var s=i[0],h=[],b=1,T=i.length;b<T;b+=1)h.push(i[b]);return h.forEach(function(M){s=s.replace(/%[a-z]/,M)}),s}function Ti(i){for(var c=[],s=1;s<arguments.length;s++)c[s-1]=arguments[s];return new Error(xA.apply(void 0,mi([wA[i]],c,!1)).trim())}var DA=function(){function i(c){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=c}return i.prototype.indexOfGroup=function(c){for(var s=0,h=0;h<c;h++)s+=this.groupSizes[h];return s},i.prototype.insertRules=function(c,s){if(c>=this.groupSizes.length){for(var h=this.groupSizes,b=h.length,T=b;c>=T;)if((T<<=1)<0)throw Ti(16,"".concat(c));this.groupSizes=new Uint32Array(T),this.groupSizes.set(h),this.length=T;for(var M=b;M<T;M++)this.groupSizes[M]=0}for(var H=this.indexOfGroup(c+1),R=(M=0,s.length);M<R;M++)this.tag.insertRule(H,s[M])&&(this.groupSizes[c]++,H++)},i.prototype.clearGroup=function(c){if(c<this.length){var s=this.groupSizes[c],h=this.indexOfGroup(c),b=h+s;this.groupSizes[c]=0;for(var T=h;T<b;T++)this.tag.deleteRule(h)}},i.prototype.getGroup=function(c){var s="";if(c>=this.length||this.groupSizes[c]===0)return s;for(var h=this.groupSizes[c],b=this.indexOfGroup(c),T=b+h,M=b;M<T;M++)s+="".concat(this.tag.getRule(M)).concat(kp);return s},i}(),MA=1<<30,us=new Map,ds=new Map,rs=1,ts=function(i){if(us.has(i))return us.get(i);for(;ds.has(rs);)rs++;var c=rs++;if((0|c)<0||c>MA)throw Ti(16,"".concat(c));return us.set(i,c),ds.set(c,i),c},_A=function(i,c){rs=c+1,us.set(i,c),ds.set(c,i)},zA="style[".concat(ro,"][").concat(Gb,'="').concat(vs,'"]'),UA=new RegExp("^".concat(ro,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),HA=function(i,c,s){for(var h,b=s.split(","),T=0,M=b.length;T<M;T++)(h=b[T])&&i.registerName(c,h)},jA=function(i,c){for(var s,h=((s=c.textContent)!==null&&s!==void 0?s:"").split(kp),b=[],T=0,M=h.length;T<M;T++){var H=h[T].trim();if(H){var R=H.match(UA);if(R){var Z=0|parseInt(R[1],10),$=R[2];Z!==0&&(_A($,Z),HA(i,$,R[3]),i.getTag().insertRules(Z,b)),b.length=0}else b.push(H)}}},fb=function(i){for(var c=document.querySelectorAll(zA),s=0,h=c.length;s<h;s++){var b=c[s];b&&b.getAttribute(ro)!==Vb&&(jA(i,b),b.parentNode&&b.parentNode.removeChild(b))}};function NA(){return typeof __webpack_nonce__<"u"?__webpack_nonce__:null}var eS=function(i){var c=document.head,s=i||c,h=document.createElement("style"),b=function(H){var R=Array.from(H.querySelectorAll("style[".concat(ro,"]")));return R[R.length-1]}(s),T=b!==void 0?b.nextSibling:null;h.setAttribute(ro,Vb),h.setAttribute(Gb,vs);var M=NA();return M&&h.setAttribute("nonce",M),s.insertBefore(h,T),h},kA=function(){function i(c){this.element=eS(c),this.element.appendChild(document.createTextNode("")),this.sheet=function(s){if(s.sheet)return s.sheet;for(var h=document.styleSheets,b=0,T=h.length;b<T;b++){var M=h[b];if(M.ownerNode===s)return M}throw Ti(17)}(this.element),this.length=0}return i.prototype.insertRule=function(c,s){try{return this.sheet.insertRule(s,c),this.length++,!0}catch{return!1}},i.prototype.deleteRule=function(c){this.sheet.deleteRule(c),this.length--},i.prototype.getRule=function(c){var s=this.sheet.cssRules[c];return s&&s.cssText?s.cssText:""},i}(),LA=function(){function i(c){this.element=eS(c),this.nodes=this.element.childNodes,this.length=0}return i.prototype.insertRule=function(c,s){if(c<=this.length&&c>=0){var h=document.createTextNode(s);return this.element.insertBefore(h,this.nodes[c]||null),this.length++,!0}return!1},i.prototype.deleteRule=function(c){this.element.removeChild(this.nodes[c]),this.length--},i.prototype.getRule=function(c){return c<this.length?this.nodes[c].textContent:""},i}(),BA=function(){function i(c){this.rules=[],this.length=0}return i.prototype.insertRule=function(c,s){return c<=this.length&&(this.rules.splice(c,0,s),this.length++,!0)},i.prototype.deleteRule=function(c){this.rules.splice(c,1),this.length--},i.prototype.getRule=function(c){return c<this.length?this.rules[c]:""},i}(),db=fs,YA={isServer:!fs,useCSSOMInjection:!dA},tS=function(){function i(c,s,h){c===void 0&&(c=vi),s===void 0&&(s={});var b=this;this.options=an(an({},YA),c),this.gs=s,this.names=new Map(h),this.server=!!c.isServer,!this.server&&fs&&db&&(db=!1,fb(this)),Lp(this,function(){return function(T){for(var M=T.getTag(),H=M.length,R="",Z=function(C){var S=function(W){return ds.get(W)}(C);if(S===void 0)return"continue";var j=T.names.get(S),I=M.getGroup(C);if(j===void 0||!j.size||I.length===0)return"continue";var ne="".concat(ro,".g").concat(C,'[id="').concat(S,'"]'),le="";j!==void 0&&j.forEach(function(W){W.length>0&&(le+="".concat(W,","))}),R+="".concat(I).concat(ne,'{content:"').concat(le,'"}').concat(kp)},$=0;$<H;$++)Z($);return R}(b)})}return i.registerId=function(c){return ts(c)},i.prototype.rehydrate=function(){!this.server&&fs&&fb(this)},i.prototype.reconstructWithOptions=function(c,s){return s===void 0&&(s=!0),new i(an(an({},this.options),c),this.gs,s&&this.names||void 0)},i.prototype.allocateGSInstance=function(c){return this.gs[c]=(this.gs[c]||0)+1},i.prototype.getTag=function(){return this.tag||(this.tag=(c=function(s){var h=s.useCSSOMInjection,b=s.target;return s.isServer?new BA(b):h?new kA(b):new LA(b)}(this.options),new DA(c)));var c},i.prototype.hasNameForId=function(c,s){return this.names.has(c)&&this.names.get(c).has(s)},i.prototype.registerName=function(c,s){if(ts(c),this.names.has(c))this.names.get(c).add(s);else{var h=new Set;h.add(s),this.names.set(c,h)}},i.prototype.insertRules=function(c,s,h){this.registerName(c,s),this.getTag().insertRules(ts(c),h)},i.prototype.clearNames=function(c){this.names.has(c)&&this.names.get(c).clear()},i.prototype.clearRules=function(c){this.getTag().clearGroup(ts(c)),this.clearNames(c)},i.prototype.clearTag=function(){this.tag=void 0},i}(),qA=/&/g,$A=/^\s*\/\/.*$/gm;function nS(i,c){return i.map(function(s){return s.type==="rule"&&(s.value="".concat(c," ").concat(s.value),s.value=s.value.replaceAll(",",",".concat(c," ")),s.props=s.props.map(function(h){return"".concat(c," ").concat(h)})),Array.isArray(s.children)&&s.type!=="@keyframes"&&(s.children=nS(s.children,c)),s})}function VA(i){var c,s,h,b=vi,T=b.options,M=T===void 0?vi:T,H=b.plugins,R=H===void 0?bs:H,Z=function(S,j,I){return I.startsWith(s)&&I.endsWith(s)&&I.replaceAll(s,"").length>0?".".concat(c):S},$=R.slice();$.push(function(S){S.type===ps&&S.value.includes("&")&&(S.props[0]=S.props[0].replace(qA,s).replace(h,Z))}),M.prefix&&$.push(sA),$.push(uA);var C=function(S,j,I,ne){j===void 0&&(j=""),I===void 0&&(I=""),ne===void 0&&(ne="&"),c=ne,s=j,h=new RegExp("\\".concat(s,"\\b"),"g");var le=S.replace($A,""),W=oA(I||j?"".concat(I," ").concat(j," { ").concat(le," }"):le);M.namespace&&(W=nS(W,M.namespace));var be=[];return ss(W,rA($.concat(cA(function(fe){return be.push(fe)})))),be};return C.hash=R.length?R.reduce(function(S,j){return j.name||Ti(15),ao(S,j.name)},Qb).toString():"",C}var GA=new tS,Sp=VA(),aS=De.createContext({shouldForwardProp:void 0,styleSheet:GA,stylis:Sp});aS.Consumer;De.createContext(void 0);function hb(){return nn.useContext(aS)}var pb=function(){function i(c,s){var h=this;this.inject=function(b,T){T===void 0&&(T=Sp);var M=h.name+T.hash;b.hasNameForId(h.id,M)||b.insertRules(h.id,M,T(h.rules,M,"@keyframes"))},this.name=c,this.id="sc-keyframes-".concat(c),this.rules=s,Lp(this,function(){throw Ti(12,String(h.name))})}return i.prototype.getName=function(c){return c===void 0&&(c=Sp),this.name+c.hash},i}(),XA=function(i){return i>="A"&&i<="Z"};function mb(i){for(var c="",s=0;s<i.length;s++){var h=i[s];if(s===1&&h==="-"&&i[0]==="-")return i;XA(h)?c+="-"+h.toLowerCase():c+=h}return c.startsWith("ms-")?"-"+c:c}var lS=function(i){return i==null||i===!1||i===""},oS=function(i){var c,s,h=[];for(var b in i){var T=i[b];i.hasOwnProperty(b)&&!lS(T)&&(Array.isArray(T)&&T.isCss||nr(T)?h.push("".concat(mb(b),":"),T,";"):bi(T)?h.push.apply(h,mi(mi(["".concat(b," {")],oS(T),!1),["}"],!1)):h.push("".concat(mb(b),": ").concat((c=b,(s=T)==null||typeof s=="boolean"||s===""?"":typeof s!="number"||s===0||c in fA||c.startsWith("--")?String(s).trim():"".concat(s,"px")),";")))}return h};function io(i,c,s,h){if(lS(i))return[];if(Fb(i))return[".".concat(i.styledComponentId)];if(nr(i)){if(!nr(T=i)||T.prototype&&T.prototype.isReactComponent||!c)return[i];var b=i(c);return typeof b!="object"||Array.isArray(b)||b instanceof pb||bi(b)||b===null||console.error("".concat(Ib(i)," is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.")),io(b,c,s,h)}var T;return i instanceof pb?s?(i.inject(s,h),[i.getName(h)]):[i]:bi(i)?oS(i):Array.isArray(i)?Array.prototype.concat.apply(bs,i.map(function(M){return io(M,c,s,h)})):[i.toString()]}var QA=Zb(vs),ZA=function(){function i(c,s,h){this.rules=c,this.staticRulesId="",this.isStatic=!1,this.componentId=s,this.baseHash=ao(QA,s),this.baseStyle=h,tS.registerId(s)}return i.prototype.generateAndInjectStyles=function(c,s,h){var b=this.baseStyle?this.baseStyle.generateAndInjectStyles(c,s,h):"";if(this.isStatic&&!h.hash)if(this.staticRulesId&&s.hasNameForId(this.componentId,this.staticRulesId))b=lo(b,this.staticRulesId);else{var T=sb(io(this.rules,c,s,h)),M=vp(ao(this.baseHash,T)>>>0);if(!s.hasNameForId(this.componentId,M)){var H=h(T,".".concat(M),void 0,this.componentId);s.insertRules(this.componentId,M,H)}b=lo(b,M),this.staticRulesId=M}else{for(var R=ao(this.baseHash,h.hash),Z="",$=0;$<this.rules.length;$++){var C=this.rules[$];if(typeof C=="string")Z+=C,R=ao(R,C);else if(C){var S=sb(io(C,c,s,h));R=ao(R,S+$),Z+=S}}if(Z){var j=vp(R>>>0);s.hasNameForId(this.componentId,j)||s.insertRules(this.componentId,j,h(Z,".".concat(j),void 0,this.componentId)),b=lo(b,j)}}return b},i}(),iS=De.createContext(void 0);iS.Consumer;var ep={};function IA(i,c,s){var h=Fb(i),b=i,T=!Fh(i),M=c.attrs,H=M===void 0?bs:M,R=c.componentId,Z=R===void 0?function(oe,pe){var F=typeof oe!="string"?"sc":ob(oe);ep[F]=(ep[F]||0)+1;var G="".concat(F,"-").concat(vA(vs+F+ep[F]));return pe?"".concat(pe,"-").concat(G):G}(c.displayName,c.parentComponentId):R,$=c.displayName,C=$===void 0?function(oe){return Fh(oe)?"styled.".concat(oe):"Styled(".concat(Ib(oe),")")}(i):$,S=c.displayName&&c.componentId?"".concat(ob(c.displayName),"-").concat(c.componentId):c.componentId||Z,j=h&&b.attrs?b.attrs.concat(H).filter(Boolean):H,I=c.shouldForwardProp;if(h&&b.shouldForwardProp){var ne=b.shouldForwardProp;if(c.shouldForwardProp){var le=c.shouldForwardProp;I=function(oe,pe){return ne(oe,pe)&&le(oe,pe)}}else I=ne}var W=new ZA(s,S,h?b.componentStyle:void 0);function be(oe,pe){return function(F,G,$e){var X=F.attrs,Ie=F.componentStyle,de=F.defaultProps,we=F.foldedComponentIds,Fe=F.styledComponentId,at=F.target,ct=De.useContext(iS),L=hb(),ue=F.shouldForwardProp||L.shouldForwardProp;nn.useDebugValue(Fe);var ce=pA(G,ct,de)||vi,Se=function(se,Q,me){for(var ae,he=an(an({},Q),{className:void 0,theme:me}),ye=0;ye<se.length;ye+=1){var et=nr(ae=se[ye])?ae(he):ae;for(var Y in et)he[Y]=Y==="className"?lo(he[Y],et[Y]):Y==="style"?an(an({},he[Y]),et[Y]):et[Y]}return Q.className&&(he.className=lo(he.className,Q.className)),he}(X,G,ce),g=Se.as||at,U={};for(var N in Se)Se[N]===void 0||N[0]==="$"||N==="as"||N==="theme"&&Se.theme===ce||(N==="forwardedAs"?U.as=Se.forwardedAs:ue&&!ue(N,g)||(U[N]=Se[N]));var K=function(se,Q){var me=hb(),ae=se.generateAndInjectStyles(Q,me.styleSheet,me.stylis);return nn.useDebugValue(ae),ae}(Ie,Se);F.warnTooManyClasses&&F.warnTooManyClasses(K);var P=lo(we,Fe);return K&&(P+=" "+K),Se.className&&(P+=" "+Se.className),U[Fh(g)&&!Xb.has(g)?"class":"className"]=P,$e&&(U.ref=$e),nn.createElement(g,U)}(fe,oe,pe)}be.displayName=C;var fe=De.forwardRef(be);return fe.attrs=j,fe.componentStyle=W,fe.displayName=C,fe.shouldForwardProp=I,fe.foldedComponentIds=h?lo(b.foldedComponentIds,b.styledComponentId):"",fe.styledComponentId=S,fe.target=h?b.target:i,Object.defineProperty(fe,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(oe){this._foldedDefaultProps=h?function(pe){for(var F=[],G=1;G<arguments.length;G++)F[G-1]=arguments[G];for(var $e=0,X=F;$e<X.length;$e++)bp(pe,X[$e],!0);return pe}({},b.defaultProps,oe):oe}}),hA(C,S),fe.warnTooManyClasses=function(oe,pe){var F={},G=!1;return function($e){if(!G&&(F[$e]=!0,Object.keys(F).length>=200)){var X=pe?' with the id of "'.concat(pe,'"'):"";console.warn("Over ".concat(200," classes were generated for component ").concat(oe).concat(X,`.
`)+`Consider using the attrs method, together with a style object for frequently changed styles.
Example:
  const Component = styled.div.attrs(props => ({
    style: {
      background: props.background,
    },
  }))\`width: 100%;\`

  <Component />`),G=!0,F={}}}}(C,S),Lp(fe,function(){return".".concat(fe.styledComponentId)}),T&&Wb(fe,i,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),fe}function yb(i,c){for(var s=[i[0]],h=0,b=c.length;h<b;h+=1)s.push(c[h],i[h+1]);return s}var gb=function(i){return Object.assign(i,{isCss:!0})};function ar(i){for(var c=[],s=1;s<arguments.length;s++)c[s-1]=arguments[s];if(nr(i)||bi(i))return gb(io(yb(bs,mi([i],c,!0))));var h=i;return c.length===0&&h.length===1&&typeof h[0]=="string"?io(h):gb(io(yb(h,c)))}function Tp(i,c,s){if(s===void 0&&(s=vi),!c)throw Ti(1,c);var h=function(b){for(var T=[],M=1;M<arguments.length;M++)T[M-1]=arguments[M];return i(c,s,ar.apply(void 0,mi([b],T,!1)))};return h.attrs=function(b){return Tp(i,c,an(an({},s),{attrs:Array.prototype.concat(s.attrs,b).filter(Boolean)}))},h.withConfig=function(b){return Tp(i,c,an(an({},s),b))},h}var uS=function(i){return Tp(IA,i)},Hn=uS;Xb.forEach(function(i){Hn[i]=uS(i)});typeof navigator<"u"&&navigator.product==="ReactNative"&&console.warn(`It looks like you've imported 'styled-components' on React Native.
Perhaps you're looking to import 'styled-components/native'?
Read more about this at https://www.styled-components.com/docs/basics#react-native`);var ns="__sc-".concat(ro,"__");typeof window<"u"&&(window[ns]||(window[ns]=0),window[ns]===1&&console.warn(`It looks like there are several instances of 'styled-components' initialized in this application. This may cause dynamic styles to not render properly, errors during the rehydration process, a missing theme prop, and makes your application bigger without good reason.

See https://s-c.sh/2BAXzed for more info.`),window[ns]+=1);const je={white:"#F9FAFB",white2:"#D6DAE0",blue:"#2E354D",blue2:"#171D27E5",grey:"#455368",lightGrey:"#BEC7D6",focusVisible:"#EB6500"},Ss=ar`
    box-shadow: 0 4px 32px 0 ${je.blue}80;
    backdrop-filter: blur(8px);
`,rS=ar`
    transition: unset;
    background-color: unset;
    border: unset;
    padding: unset;
`,Bp=ar`
    &:hover {
        height: 36px;
        width: 36px;
        place-items: center;
        place-content: center;
        border-radius: 4px;
        background-color: ${je.grey};
        outline: 2px solid ${je.lightGrey};

        .icon {
            fill: ${je.white} !important;
        }
    }
`;Hn.div`
    width: 349px;
    background: ${je.blue2};
    border-radius: 8px;
    padding: 16px 24px;
    color: ${je.white};
    line-height: 24px;
    gap: 12px;

    .sd-ai-navigation-pendo-float-header {
        display: flex;
        width: fit-content;
        font-size: 24px;
        padding-bottom: 16px;
        fill: white;
        gap: 12px;
    }

    .sd-ai-navigation-pendo-float-body {
        padding-bottom: 8px;
    }

    .sd-ai-navigation-pendo-float-footer {
        font-size: 16px;
        color: ${je.blue2};
        height: 48px;
        width: 301px;
        background: white;
        display: block;
        align-content: center;
        text-align: center;
        border-radius: 10px;
        margin-top: 16px;
        justify-self: center;
        text-decoration: none;
        
        .anchor-text {
            border: none;
        }
        
        &:hover {
            outline: 2px solid ${je.lightGrey};
            background-color: ${je.grey};
            color: ${je.white};
            text-decoration: none;
        }
        
        &:active {
            background-color: ${je.blue};
        }

        &:focus-visible {
            outline: 2px solid ${je.focusVisible};
        }
    }

    @media (max-width: 768px) {
        width: 288px;

        .sd-ai-navigation-pendo-float-footer {
            max-width: 230px;
        }
    }
`;const JA=Hn.div`
    ${Ss};
    display: grid;
    grid-template-columns: auto auto;
    background: #2e354d;
    border-radius: 8px;
    width: fit-content;

    &.sd-ai-navigation-pendo-float-visible {
        display: contents;
    }
    
    .button {
        ${rS}
        ${Bp}
    }
    
    > div{
        align-content: flex-end;
    }
`,Ep=ar`
    display: flex;
    place-content: center;
    place-items: center;
    width: 36px;
    height: 36px;
    border-radius: 4px !important;

    .icon {
        fill: ${je.blue};
    }

    &:hover {
        background-color: ${je.grey};
    }
`,KA=Hn.div`
    position: fixed;
    bottom: 32px;
    left: 32px;
    display: flex;
    gap: 8px;
    z-index: 10;
    flex-direction: column-reverse;
    
    &.sd-ai-navigation-widget-hidden {
        display: grid;
        grid-auto-flow: column;
    }
`,PA=Hn.div`
    display: grid;
    grid-template-columns: auto auto;
    grid-gap: 8px;
`,WA=Hn.div`
    display: flex;
    flex-direction: column;
    width: max-content;
    height: max-content;
    border-radius: 8px;
    ${Ss};
    background-color: ${je.blue};
    place-content: center;
    align-self: flex-end;
    padding: 12px;
    z-index: 9;
    
    .anchor, .button {
        transition: unset;
        color: white;
        padding: 6px;
        border-radius: 4px;

        span {
            border: unset;
        }

        &:hover {
            outline: 2px solid ${je.lightGrey} !important;
            border-radius: 3px;
            background-color: ${je.grey};
        }

        &:active {
            background-color: unset;
        }
    }
`,vb=Hn.div`
    .button {
        ${rS}
        ${Bp}
    }

    height: 48px;
    width: 48px;
    place-content: center;
    display: flex;
    box-shadow: 0 4px 32px 0 ${je.blue}80;
    backdrop-filter: blur(8px);
    border-radius: 8px;
    overflow: hidden;
    align-items: center;
    justify-content: center;
`,Ku=Hn.div`
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: ${je.blue};
    height: 48px;
    width: 48px;
    
    .icon {
        display: flex;
        height: 24px;
        width: 24px;
        fill: ${je.white} !important;
    }
    
    .icon-sparkle-speech {
        height: 26px !important;
        width: 26px !important;
    }

    .button-icon-only, .anchor-with-icon {
        display: flex;
        justify-content: center;
    
        &:focus-visible {
            place-content: center;
            place-items: center;
        }
    }
    
    .anchor-with-icon {
        &:hover {
            background-color: ${je.grey};
            outline: 2px solid ${je.lightGrey};
            height: 36px;
            width: 36px;
            border-radius: 4px;
            display: grid;
            place-content: center;
            place-items: center;

            &:active {
                background-color: unset;
            }
        }
        &:focus-visible {
            height: 48px;
            width: 48px;
            outline-offset: -4px !important;
            border-radius: 10px;
        }
    }

    .button-icon-only {
        ${Bp};
        &:focus-visible {
            height: 40px;
            width: 40px;
            outline-offset: 0 !important;
            border-radius: 6px;
        }
    }
    &.sd-ai-navigation-no-cursor-event {
        pointer-events: none;
    }
    
    &:after {
        content: '';
        width: 10px;
        height: 32px;
        position: absolute;
        right: -8px;
    }
`,tp=Hn.div`
    .button {
        &:hover {
            height: 36px;
            width: 36px;
            place-items: center;
            place-content: center;
            outline: 2px solid ${je.lightGrey};
            border-radius: 4px;
            background-color: ${je.grey};

            .icon {
                fill: ${je.white} !important;
            }
        }
    }
    
    &.sd-ai-navigation-more-options-expanded {
        ${Ep};
        background-color: ${je.white2} !important;

        .icon {
            fill: ${je.blue} !important;
        }

        &:hover {
            background-color: unset !important;

            .icon {
                fill: ${je.white} !important;
            }
        }
    }

    &.sd-ai-navigation-active-page {
        ${Ep};
        background-color: ${je.white};

        .icon {
            fill: ${je.blue} !important;
        }

        &:hover {
            border-radius: 6px;
            background-color: ${je.grey};
            outline: 2px solid ${je.lightGrey};

            .icon {
                fill: ${je.white} !important;
            }
        }
    }
`,FA=Hn.div`
    &.sd-ai-navigation-active-view-more-options {
        ${Ep};
        background-color: ${je.white};
        .icon {
            fill: ${je.blue} !important;
        }
        svg {
            transform: rotate(90deg);
        }
    }
`,eO=Hn.div`
    display: grid;
    width: 48px;
    border-radius: 8px;
    place-content: center;
    place-items: center;
    ${Ss};
    background-color: ${je.blue};
    z-index: 10;
`;var np={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var bb;function tO(){return bb||(bb=1,function(i){(function(){var c={}.hasOwnProperty;function s(){for(var T="",M=0;M<arguments.length;M++){var H=arguments[M];H&&(T=b(T,h(H)))}return T}function h(T){if(typeof T=="string"||typeof T=="number")return T;if(typeof T!="object")return"";if(Array.isArray(T))return s.apply(null,T);if(T.toString!==Object.prototype.toString&&!T.toString.toString().includes("[native code]"))return T.toString();var M="";for(var H in T)c.call(T,H)&&T[H]&&(M=b(M,H));return M}function b(T,M){return M?T?T+" "+M:T+M:T}i.exports?(s.default=s,i.exports=s):window.classNames=s})()}(np)),np.exports}var nO=tO();const co=Hp(nO);var aO=["desc","className","iconClassName","viewBox","children","height"];function Rp(){return Rp=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},Rp.apply(null,arguments)}function lO(i,c){if(i==null)return{};var s,h,b=oO(i,c);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(i);for(h=0;h<T.length;h++)s=T[h],c.includes(s)||{}.propertyIsEnumerable.call(i,s)&&(b[s]=i[s])}return b}function oO(i,c){if(i==null)return{};var s={};for(var h in i)if({}.hasOwnProperty.call(i,h)){if(c.includes(h))continue;s[h]=i[h]}return s}var vl=i=>{var{desc:c,className:s,iconClassName:h,viewBox:b,children:T,height:M=20}=i,H=lO(i,aO),R=co("icon",h,s);return De.createElement("svg",Rp({focusable:"false",viewBox:b,height:M},H,{className:R}),c&&De.createElement("desc",null,c),T)};vl.__docgenInfo={description:"",methods:[],displayName:"Icon",props:{height:{type:{name:"union",value:[{name:"number"},{name:"string"}]},required:!1,description:""},width:{type:{name:"union",value:[{name:"number"},{name:"string"}]},required:!1,description:""},viewBox:{type:{name:"string"},required:!1,description:""},children:{type:{name:"node"},required:!1,description:""},className:{type:{name:"string"},required:!1,description:""},iconClassName:{type:{name:"string"},required:!1,description:""},desc:{type:{name:"string"},required:!1,description:""}}};function Ap(){return Ap=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},Ap.apply(null,arguments)}var cS=i=>De.createElement(vl,Ap({viewBox:"0 0 24 24",iconClassName:"icon-sparkle"},i),De.createElement("path",{d:"M9.59085 4.94259C10.542 9.37206 10.5953 11.09 11.6326 11.9784C12.5848 12.7938 14.366 12.9104 18.4317 13.7835C14.1842 14.6956 12.43 14.782 11.5097 15.7023C10.5894 16.6226 10.503 18.3767 9.59085 22.6243C8.67876 18.3767 8.59233 16.6226 7.67205 15.7023C6.75173 14.782 4.99758 14.6956 0.75 13.7835C4.7002 12.9352 6.4939 12.8011 7.46591 12.0462C8.58428 11.1777 8.61489 9.48755 9.59085 4.94259ZM18.2328 1.40625C18.7505 3.81686 18.7994 4.81233 19.3217 5.33457C19.8439 5.8568 20.8394 5.9058 23.25 6.42344C20.9086 6.92623 19.9023 6.98688 19.3679 7.46846C18.8019 7.97851 18.7653 8.96076 18.2328 11.4406C17.7152 9.03001 17.6662 8.03454 17.1439 7.51231C16.6217 6.99008 15.6262 6.94108 13.2156 6.42344C15.5654 5.91885 16.5706 5.85956 17.1035 5.37322C17.664 4.86168 17.7021 3.87772 18.2328 1.40625Z"}));cS.__docgenInfo={description:"",methods:[],displayName:"Sparkle",composes:["./Icon"]};function Op(){return Op=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},Op.apply(null,arguments)}var sS=i=>De.createElement(vl,Op({viewBox:"0 0 30 30",iconClassName:"icon-sparkle-cloud"},i),De.createElement("path",{d:"M27.3056 15.0054C27.3383 14.9649 27.3705 14.9248 27.4018 14.8843C28.623 13.314 29.2597 11.4302 29.2431 9.4365C29.2304 7.9287 28.8285 6.4453 28.081 5.1469C26.5053 2.4086 23.5639 0.768497 20.4091 0.749897C19.0404 0.761597 17.7314 1.08 16.519 1.6962C15.9897 1.9652 15.4843 2.2958 15.0083 2.6845C13.4604 1.4423 11.5239 0.749397 9.53124 0.749397C9.50784 0.749397 9.48434 0.749897 9.46044 0.749897C8.09224 0.761597 6.78324 1.08 5.56984 1.6967C4.13864 2.4243 2.93504 3.5298 2.08784 4.894C1.21484 6.2998 0.760745 7.9213 0.774345 9.5844C0.783145 10.5861 0.957544 11.5577 1.28464 12.4701C2.18114 12.2816 2.96624 12.1281 3.64314 11.9966C3.68814 11.9879 3.73324 11.9791 3.77844 11.9703C3.45654 11.2201 3.28174 10.4071 3.27434 9.5634C3.26454 8.3744 3.58884 7.2162 4.21184 6.2128C4.81784 5.2367 5.67914 4.4457 6.70254 3.9252C7.56824 3.4853 8.50334 3.2582 9.48134 3.2499C9.49944 3.2499 9.51694 3.2494 9.53454 3.2494C11.2767 3.2494 12.8968 3.9564 14.0999 5.2431L15.012 6.2182L15.9251 5.2436C16.4495 4.684 17.0301 4.2407 17.6512 3.9252C18.5164 3.4853 19.4515 3.2582 20.43 3.2499C22.7308 3.2333 24.7874 4.4359 25.9139 6.3939C26.4476 7.3202 26.7342 8.3793 26.743 9.4569C26.7547 10.8822 26.3001 12.2284 25.4281 13.3495C25.2484 13.5809 25.0228 13.8222 24.7386 14.0863L23.7557 15.0009L24.7381 15.9159C25.2215 16.3661 25.617 16.8563 25.9139 17.3734C26.4476 18.2997 26.7342 19.3588 26.743 20.4364C26.7547 21.8617 26.3001 23.2079 25.4281 24.329C24.2445 25.851 22.4613 26.7333 20.536 26.7494C19.1102 26.7606 17.7909 26.3158 16.6756 25.4594C16.4368 25.2763 16.1981 25.0551 15.9241 24.7631L15.0121 23.79L14.1005 24.7636C12.9145 26.0297 11.3119 26.7348 9.58735 26.7494C8.17375 26.7606 6.84274 26.3158 5.72694 25.4594C4.18494 24.2758 3.29094 22.4833 3.27434 20.5429C3.26724 19.672 3.44304 18.8189 3.78044 18.03C3.73464 18.0211 3.68884 18.0122 3.64314 18.0033C2.97234 17.8731 2.19514 17.7212 1.30874 17.5349C0.961645 18.502 0.765845 19.5213 0.774345 20.564C0.797745 23.2798 2.04774 25.7871 4.20504 27.4429C5.74654 28.626 7.58635 29.25 9.52975 29.25C9.55564 29.25 9.58245 29.2495 9.60884 29.2495C11.6025 29.2324 13.4789 28.5576 15.0063 27.3266C15.0551 27.3662 15.1044 27.4052 15.1538 27.4428C16.6953 28.6259 18.5351 29.2499 20.4785 29.2499C20.5044 29.2499 20.5312 29.2494 20.5571 29.2494C23.2509 29.2269 25.7456 27.9931 27.4018 25.8637C28.623 24.2934 29.2597 22.4096 29.2431 20.4159C29.2304 18.9081 28.8285 17.4247 28.0815 16.1273C27.8583 15.7393 27.599 15.3643 27.3056 15.0054Z"}),De.createElement("path",{d:"M9.99996 23.5964C10.949 19.1771 10.9788 17.5337 12.0662 16.6892C13.0113 15.9552 14.7554 15.8248 18.5964 15C14.4663 14.1131 12.7606 14.0291 11.8657 13.1342C10.9708 12.2393 10.8869 10.5337 9.99996 6.4035C9.11306 10.5337 9.02906 12.2394 8.13416 13.1342C7.23926 14.029 5.53366 14.1131 1.40356 15C5.35686 15.8489 7.08886 15.9622 8.01466 16.7551C9.02326 17.619 9.07506 19.2894 9.99996 23.5964Z"}));sS.__docgenInfo={description:"",methods:[],displayName:"SparkleCloud",composes:["./Icon"]};function Cp(){return Cp=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},Cp.apply(null,arguments)}var fS=i=>De.createElement(vl,Cp({viewBox:"0 0 6 22",iconClassName:"icon-dots"},i),De.createElement("path",{d:"M3 21.875C1.5525 21.875 0.375 20.6975 0.375 19.25C0.375 17.8025 1.5525 16.625 3 16.625C4.4475 16.625 5.625 17.8025 5.625 19.25C5.625 20.6975 4.4475 21.875 3 21.875ZM0.375 11C0.375 12.4475 1.5525 13.625 3 13.625C4.4475 13.625 5.625 12.4475 5.625 11C5.625 9.5525 4.4475 8.375 3 8.375C1.5525 8.375 0.375 9.5525 0.375 11ZM0.375 2.75C0.375 4.1975 1.5525 5.375 3 5.375C4.4475 5.375 5.625 4.1975 5.625 2.75C5.625 1.3025 4.4475 0.125 3 0.125C1.5525 0.125 0.375 1.3025 0.375 2.75Z"}));fS.__docgenInfo={description:"",methods:[],displayName:"Dots",composes:["./Icon"]};function wp(){return wp=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},wp.apply(null,arguments)}var dS=i=>De.createElement(vl,wp({viewBox:"0 0 16 16",iconClassName:"icon-library"},i),De.createElement("path",{d:"M2.42004 2H3.82004V14H2.42004V2Z"}),De.createElement("path",{d:"M5.12004 2H6.52004V14H5.12004V2Z"}),De.createElement("path",{d:"M9.37548 2.15421L8.06462 2.64579L12.2646 13.8458L13.5755 13.3542L9.37548 2.15421Z"}));dS.__docgenInfo={description:"",methods:[],displayName:"Library",composes:["./Icon"]};var iO=["isExternalLink"];function xp(){return xp=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},xp.apply(null,arguments)}function uO(i,c){if(i==null)return{};var s,h,b=rO(i,c);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(i);for(h=0;h<T.length;h++)s=T[h],c.includes(s)||{}.propertyIsEnumerable.call(i,s)&&(b[s]=i[s])}return b}function rO(i,c){if(i==null)return{};var s={};for(var h in i)if({}.hasOwnProperty.call(i,h)){if(c.includes(h))continue;s[h]=i[h]}return s}var hS=i=>{var{isExternalLink:c}=i,s=uO(i,iO);return De.createElement(vl,xp({viewBox:"0 0 78 128",iconClassName:co("icon-arrow-up-right",{"arrow-external-link":c})},s),De.createElement("path",{d:"M4 36h57.07L1.57 95.5l7.07 7.08L68 43.22V100h10V26H4z"}))};hS.__docgenInfo={description:"",methods:[],displayName:"ArrowUpRight",props:{isExternalLink:{type:{name:"bool"},required:!1,description:""}},composes:["./Icon"]};var cO=["isExternalLink"];function Dp(){return Dp=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},Dp.apply(null,arguments)}function sO(i,c){if(i==null)return{};var s,h,b=fO(i,c);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(i);for(h=0;h<T.length;h++)s=T[h],c.includes(s)||{}.propertyIsEnumerable.call(i,s)&&(b[s]=i[s])}return b}function fO(i,c){if(i==null)return{};var s={};for(var h in i)if({}.hasOwnProperty.call(i,h)){if(c.includes(h))continue;s[h]=i[h]}return s}function pS(i){var{isExternalLink:c}=i,s=sO(i,cO);return De.createElement(vl,Dp({viewBox:"0 0 8 8",iconClassName:co("icon-arrow-up-right-tiny",{"arrow-external-link":c})},s),De.createElement("path",{d:"M1.12949 2.1072V1H7V6.85795H5.89111V2.90281L0.784057 8L0 7.21635L5.11902 2.1072H1.12949Z"}))}pS.__docgenInfo={description:"",methods:[],displayName:"ArrowUpRightTiny",props:{isExternalLink:{type:{name:"bool"},required:!1,description:""}},composes:["./Icon"]};function Mp(){return Mp=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},Mp.apply(null,arguments)}function mS(i){var{children:c,icon:s,iconPlacement:h,iconLeft:b,iconOnly:T,iconRight:M,isExternalLink:H,isTinyExternalLinkIcon:R,externalLinkProps:Z={}}=i,$=b,C=T,S=M;s&&(c?h==="left"?$=s:S=s:C=s);var j=R?pS:hS,I=H?De.createElement(j,Mp({"aria-label":"Opens in new window",key:"icon-external-link",isExternalLink:!0},Z)):void 0;return{_iconLeft:$,_iconOnly:C,_iconRight:S,_iconExternalLink:I}}var ap={exports:{}},lp={exports:{}},Qe={},Sb;function dO(){if(Sb)return Qe;Sb=1;/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */return function(){var i=typeof Symbol=="function"&&Symbol.for,c=i?Symbol.for("react.element"):60103,s=i?Symbol.for("react.portal"):60106,h=i?Symbol.for("react.fragment"):60107,b=i?Symbol.for("react.strict_mode"):60108,T=i?Symbol.for("react.profiler"):60114,M=i?Symbol.for("react.provider"):60109,H=i?Symbol.for("react.context"):60110,R=i?Symbol.for("react.async_mode"):60111,Z=i?Symbol.for("react.concurrent_mode"):60111,$=i?Symbol.for("react.forward_ref"):60112,C=i?Symbol.for("react.suspense"):60113,S=i?Symbol.for("react.suspense_list"):60120,j=i?Symbol.for("react.memo"):60115,I=i?Symbol.for("react.lazy"):60116,ne=i?Symbol.for("react.block"):60121,le=i?Symbol.for("react.fundamental"):60117,W=i?Symbol.for("react.responder"):60118,be=i?Symbol.for("react.scope"):60119;function fe(Y){return typeof Y=="string"||typeof Y=="function"||Y===h||Y===Z||Y===T||Y===b||Y===C||Y===S||typeof Y=="object"&&Y!==null&&(Y.$$typeof===I||Y.$$typeof===j||Y.$$typeof===M||Y.$$typeof===H||Y.$$typeof===$||Y.$$typeof===le||Y.$$typeof===W||Y.$$typeof===be||Y.$$typeof===ne)}function oe(Y){if(typeof Y=="object"&&Y!==null){var Tt=Y.$$typeof;switch(Tt){case c:var sa=Y.type;switch(sa){case R:case Z:case h:case T:case b:case C:return sa;default:var ka=sa&&sa.$$typeof;switch(ka){case H:case $:case I:case j:case M:return ka;default:return Tt}}case s:return Tt}}}var pe=R,F=Z,G=H,$e=M,X=c,Ie=$,de=h,we=I,Fe=j,at=s,ct=T,L=b,ue=C,ce=!1;function Se(Y){return ce||(ce=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),g(Y)||oe(Y)===R}function g(Y){return oe(Y)===Z}function U(Y){return oe(Y)===H}function N(Y){return oe(Y)===M}function K(Y){return typeof Y=="object"&&Y!==null&&Y.$$typeof===c}function P(Y){return oe(Y)===$}function se(Y){return oe(Y)===h}function Q(Y){return oe(Y)===I}function me(Y){return oe(Y)===j}function ae(Y){return oe(Y)===s}function he(Y){return oe(Y)===T}function ye(Y){return oe(Y)===b}function et(Y){return oe(Y)===C}Qe.AsyncMode=pe,Qe.ConcurrentMode=F,Qe.ContextConsumer=G,Qe.ContextProvider=$e,Qe.Element=X,Qe.ForwardRef=Ie,Qe.Fragment=de,Qe.Lazy=we,Qe.Memo=Fe,Qe.Portal=at,Qe.Profiler=ct,Qe.StrictMode=L,Qe.Suspense=ue,Qe.isAsyncMode=Se,Qe.isConcurrentMode=g,Qe.isContextConsumer=U,Qe.isContextProvider=N,Qe.isElement=K,Qe.isForwardRef=P,Qe.isFragment=se,Qe.isLazy=Q,Qe.isMemo=me,Qe.isPortal=ae,Qe.isProfiler=he,Qe.isStrictMode=ye,Qe.isSuspense=et,Qe.isValidElementType=fe,Qe.typeOf=oe}(),Qe}var Tb;function yS(){return Tb||(Tb=1,lp.exports=dO()),lp.exports}/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var op,Eb;function hO(){if(Eb)return op;Eb=1;var i=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable;function h(T){if(T==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(T)}function b(){try{if(!Object.assign)return!1;var T=new String("abc");if(T[5]="de",Object.getOwnPropertyNames(T)[0]==="5")return!1;for(var M={},H=0;H<10;H++)M["_"+String.fromCharCode(H)]=H;var R=Object.getOwnPropertyNames(M).map(function($){return M[$]});if(R.join("")!=="**********")return!1;var Z={};return"abcdefghijklmnopqrst".split("").forEach(function($){Z[$]=$}),Object.keys(Object.assign({},Z)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}return op=b()?Object.assign:function(T,M){for(var H,R=h(T),Z,$=1;$<arguments.length;$++){H=Object(arguments[$]);for(var C in H)c.call(H,C)&&(R[C]=H[C]);if(i){Z=i(H);for(var S=0;S<Z.length;S++)s.call(H,Z[S])&&(R[Z[S]]=H[Z[S]])}}return R},op}var ip,Rb;function gS(){if(Rb)return ip;Rb=1;var i="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return ip=i,ip}var up,Ab;function vS(){return Ab||(Ab=1,up=Function.call.bind(Object.prototype.hasOwnProperty)),up}var rp,Ob;function pO(){if(Ob)return rp;Ob=1;var i=function(){};{var c=gS(),s={},h=vS();i=function(T){var M="Warning: "+T;typeof console<"u"&&console.error(M);try{throw new Error(M)}catch{}}}function b(T,M,H,R,Z){for(var $ in T)if(h(T,$)){var C;try{if(typeof T[$]!="function"){var S=Error((R||"React class")+": "+H+" type `"+$+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof T[$]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw S.name="Invariant Violation",S}C=T[$](M,$,R,H,null,c)}catch(I){C=I}if(C&&!(C instanceof Error)&&i((R||"React class")+": type specification of "+H+" `"+$+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof C+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),C instanceof Error&&!(C.message in s)){s[C.message]=!0;var j=Z?Z():"";i("Failed "+H+" type: "+C.message+(j??""))}}}return b.resetWarningCache=function(){s={}},rp=b,rp}var cp,Cb;function mO(){if(Cb)return cp;Cb=1;var i=yS(),c=hO(),s=gS(),h=vS(),b=pO(),T=function(){};T=function(H){var R="Warning: "+H;typeof console<"u"&&console.error(R);try{throw new Error(R)}catch{}};function M(){return null}return cp=function(H,R){var Z=typeof Symbol=="function"&&Symbol.iterator,$="@@iterator";function C(g){var U=g&&(Z&&g[Z]||g[$]);if(typeof U=="function")return U}var S="<<anonymous>>",j={array:W("array"),bigint:W("bigint"),bool:W("boolean"),func:W("function"),number:W("number"),object:W("object"),string:W("string"),symbol:W("symbol"),any:be(),arrayOf:fe,element:oe(),elementType:pe(),instanceOf:F,node:Ie(),objectOf:$e,oneOf:G,oneOfType:X,shape:we,exact:Fe};function I(g,U){return g===U?g!==0||1/g===1/U:g!==g&&U!==U}function ne(g,U){this.message=g,this.data=U&&typeof U=="object"?U:{},this.stack=""}ne.prototype=Error.prototype;function le(g){var U={},N=0;function K(se,Q,me,ae,he,ye,et){if(ae=ae||S,ye=ye||me,et!==s){if(R){var Y=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw Y.name="Invariant Violation",Y}else if(typeof console<"u"){var Tt=ae+":"+me;!U[Tt]&&N<3&&(T("You are manually calling a React.PropTypes validation function for the `"+ye+"` prop on `"+ae+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),U[Tt]=!0,N++)}}return Q[me]==null?se?Q[me]===null?new ne("The "+he+" `"+ye+"` is marked as required "+("in `"+ae+"`, but its value is `null`.")):new ne("The "+he+" `"+ye+"` is marked as required in "+("`"+ae+"`, but its value is `undefined`.")):null:g(Q,me,ae,he,ye)}var P=K.bind(null,!1);return P.isRequired=K.bind(null,!0),P}function W(g){function U(N,K,P,se,Q,me){var ae=N[K],he=L(ae);if(he!==g){var ye=ue(ae);return new ne("Invalid "+se+" `"+Q+"` of type "+("`"+ye+"` supplied to `"+P+"`, expected ")+("`"+g+"`."),{expectedType:g})}return null}return le(U)}function be(){return le(M)}function fe(g){function U(N,K,P,se,Q){if(typeof g!="function")return new ne("Property `"+Q+"` of component `"+P+"` has invalid PropType notation inside arrayOf.");var me=N[K];if(!Array.isArray(me)){var ae=L(me);return new ne("Invalid "+se+" `"+Q+"` of type "+("`"+ae+"` supplied to `"+P+"`, expected an array."))}for(var he=0;he<me.length;he++){var ye=g(me,he,P,se,Q+"["+he+"]",s);if(ye instanceof Error)return ye}return null}return le(U)}function oe(){function g(U,N,K,P,se){var Q=U[N];if(!H(Q)){var me=L(Q);return new ne("Invalid "+P+" `"+se+"` of type "+("`"+me+"` supplied to `"+K+"`, expected a single ReactElement."))}return null}return le(g)}function pe(){function g(U,N,K,P,se){var Q=U[N];if(!i.isValidElementType(Q)){var me=L(Q);return new ne("Invalid "+P+" `"+se+"` of type "+("`"+me+"` supplied to `"+K+"`, expected a single ReactElement type."))}return null}return le(g)}function F(g){function U(N,K,P,se,Q){if(!(N[K]instanceof g)){var me=g.name||S,ae=Se(N[K]);return new ne("Invalid "+se+" `"+Q+"` of type "+("`"+ae+"` supplied to `"+P+"`, expected ")+("instance of `"+me+"`."))}return null}return le(U)}function G(g){if(!Array.isArray(g))return arguments.length>1?T("Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."):T("Invalid argument supplied to oneOf, expected an array."),M;function U(N,K,P,se,Q){for(var me=N[K],ae=0;ae<g.length;ae++)if(I(me,g[ae]))return null;var he=JSON.stringify(g,function(et,Y){var Tt=ue(Y);return Tt==="symbol"?String(Y):Y});return new ne("Invalid "+se+" `"+Q+"` of value `"+String(me)+"` "+("supplied to `"+P+"`, expected one of "+he+"."))}return le(U)}function $e(g){function U(N,K,P,se,Q){if(typeof g!="function")return new ne("Property `"+Q+"` of component `"+P+"` has invalid PropType notation inside objectOf.");var me=N[K],ae=L(me);if(ae!=="object")return new ne("Invalid "+se+" `"+Q+"` of type "+("`"+ae+"` supplied to `"+P+"`, expected an object."));for(var he in me)if(h(me,he)){var ye=g(me,he,P,se,Q+"."+he,s);if(ye instanceof Error)return ye}return null}return le(U)}function X(g){if(!Array.isArray(g))return T("Invalid argument supplied to oneOfType, expected an instance of array."),M;for(var U=0;U<g.length;U++){var N=g[U];if(typeof N!="function")return T("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+ce(N)+" at index "+U+"."),M}function K(P,se,Q,me,ae){for(var he=[],ye=0;ye<g.length;ye++){var et=g[ye],Y=et(P,se,Q,me,ae,s);if(Y==null)return null;Y.data&&h(Y.data,"expectedType")&&he.push(Y.data.expectedType)}var Tt=he.length>0?", expected one of type ["+he.join(", ")+"]":"";return new ne("Invalid "+me+" `"+ae+"` supplied to "+("`"+Q+"`"+Tt+"."))}return le(K)}function Ie(){function g(U,N,K,P,se){return at(U[N])?null:new ne("Invalid "+P+" `"+se+"` supplied to "+("`"+K+"`, expected a ReactNode."))}return le(g)}function de(g,U,N,K,P){return new ne((g||"React class")+": "+U+" type `"+N+"."+K+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+P+"`.")}function we(g){function U(N,K,P,se,Q){var me=N[K],ae=L(me);if(ae!=="object")return new ne("Invalid "+se+" `"+Q+"` of type `"+ae+"` "+("supplied to `"+P+"`, expected `object`."));for(var he in g){var ye=g[he];if(typeof ye!="function")return de(P,se,Q,he,ue(ye));var et=ye(me,he,P,se,Q+"."+he,s);if(et)return et}return null}return le(U)}function Fe(g){function U(N,K,P,se,Q){var me=N[K],ae=L(me);if(ae!=="object")return new ne("Invalid "+se+" `"+Q+"` of type `"+ae+"` "+("supplied to `"+P+"`, expected `object`."));var he=c({},N[K],g);for(var ye in he){var et=g[ye];if(h(g,ye)&&typeof et!="function")return de(P,se,Q,ye,ue(et));if(!et)return new ne("Invalid "+se+" `"+Q+"` key `"+ye+"` supplied to `"+P+"`.\nBad object: "+JSON.stringify(N[K],null,"  ")+`
Valid keys: `+JSON.stringify(Object.keys(g),null,"  "));var Y=et(me,ye,P,se,Q+"."+ye,s);if(Y)return Y}return null}return le(U)}function at(g){switch(typeof g){case"number":case"string":case"undefined":return!0;case"boolean":return!g;case"object":if(Array.isArray(g))return g.every(at);if(g===null||H(g))return!0;var U=C(g);if(U){var N=U.call(g),K;if(U!==g.entries){for(;!(K=N.next()).done;)if(!at(K.value))return!1}else for(;!(K=N.next()).done;){var P=K.value;if(P&&!at(P[1]))return!1}}else return!1;return!0;default:return!1}}function ct(g,U){return g==="symbol"?!0:U?U["@@toStringTag"]==="Symbol"||typeof Symbol=="function"&&U instanceof Symbol:!1}function L(g){var U=typeof g;return Array.isArray(g)?"array":g instanceof RegExp?"object":ct(U,g)?"symbol":U}function ue(g){if(typeof g>"u"||g===null)return""+g;var U=L(g);if(U==="object"){if(g instanceof Date)return"date";if(g instanceof RegExp)return"regexp"}return U}function ce(g){var U=ue(g);switch(U){case"array":case"object":return"an "+U;case"boolean":case"date":case"regexp":return"a "+U;default:return U}}function Se(g){return!g.constructor||!g.constructor.name?S:g.constructor.name}return j.checkPropTypes=b,j.resetWarningCache=b.resetWarningCache,j.PropTypes=j,j},cp}var wb;function yO(){if(wb)return ap.exports;wb=1;{var i=yS(),c=!0;ap.exports=mO()(i.isElement,c)}return ap.exports}var gO=yO();const sp=Hp(gO);function vO(i){var c;return typeof i=="function"&&!!(i!=null&&(c=i.prototype)!==null&&c!==void 0&&c.isReactComponent)}function bO(i){return typeof i=="function"}function SO(i){return vO(i)||bO(i)}function uo(i){var{icon:c}=i;return c&&SO(c)?De.createElement(c,null):c}sp.oneOfType([sp.func,sp.node]);uo.__docgenInfo={description:"",methods:[],displayName:"IconGroup",props:{icon:{type:{name:"custom",raw:"IconPropType"},required:!1,description:""}}};var TO=["children","href","className","icon","iconPlacement","iconLeft","iconOnly","iconRight","hasBackgroundColor","hasColoredIcon","hasInheritColor","hasUnderline","isExternalLink","externalLinkProps","linkType","onDarkBackground","size"];function _p(){return _p=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},_p.apply(null,arguments)}function EO(i,c){if(i==null)return{};var s,h,b=RO(i,c);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(i);for(h=0;h<T.length;h++)s=T[h],c.includes(s)||{}.propertyIsEnumerable.call(i,s)&&(b[s]=i[s])}return b}function RO(i,c){if(i==null)return{};var s={};for(var h in i)if({}.hasOwnProperty.call(i,h)){if(c.includes(h))continue;s[h]=i[h]}return s}var bS="anchor-primary",AO={[bS]:!0,"anchor-secondary":!0},OO=["navigation","alternative"],as=i=>{var c,{children:s,href:h,className:b,icon:T,iconPlacement:M,iconLeft:H,iconOnly:R,iconRight:Z,hasBackgroundColor:$,hasColoredIcon:C,hasInheritColor:S,hasUnderline:j,isExternalLink:I,externalLinkProps:ne={},linkType:le="default",onDarkBackground:W,size:be}=i,fe=EO(i,TO),{_iconLeft:oe,_iconOnly:pe,_iconRight:F,_iconExternalLink:G}=mS({children:s,icon:T,iconPlacement:M,iconLeft:H,iconOnly:R,iconRight:Z,isExternalLink:I,isTinyExternalLinkIcon:!0,externalLinkProps:ne}),$e=!(b!=null&&(c=b.split(" "))!==null&&c!==void 0&&c.some(we=>AO[we])),X=!OO.includes(le),Ie=co("anchor",b,{[bS]:$e&&X,"anchor-secondary":le==="alternative"||le==="navigation",["anchor-".concat(be)]:!!be,"anchor-has-background-color":$,"anchor-has-colored-icon":C,"anchor-has-inherit-color":S,"anchor-icon-left":!!oe,"anchor-icon-only":!!pe,"anchor-icon-right":!!F,"on-dark-background":W,"anchor-underline":j||le==="alternative","anchor-with-icon":oe||pe||F}),de=I?"_blank":void 0;return De.createElement("a",_p({className:Ie,href:h,target:de},fe),oe?De.createElement(uo,{icon:oe}):null,pe?De.createElement(uo,{icon:pe}):null,s?De.createElement("span",{className:"anchor-text-container"},De.createElement("span",{className:"anchor-text"},s),G):null,F?De.createElement(uo,{icon:F}):null)},CO=["width","children","className","icon","iconPlacement","iconLeft","iconOnly","iconRight","id","onMouseLeave","disabled","onDarkBackground","size","disableMinWidth"];function zp(){return zp=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},zp.apply(null,arguments)}function wO(i,c){if(i==null)return{};var s,h,b=xO(i,c);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(i);for(h=0;h<T.length;h++)s=T[h],c.includes(s)||{}.propertyIsEnumerable.call(i,s)&&(b[s]=i[s])}return b}function xO(i,c){if(i==null)return{};var s={};for(var h in i)if({}.hasOwnProperty.call(i,h)){if(c.includes(h))continue;s[h]=i[h]}return s}var xb="button-primary",DO={"button-primary":!0,"button-secondary":!0,"button-tertiary":!0},cs=nn.forwardRef((i,c)=>{var{width:s,children:h,className:b=xb,icon:T,iconPlacement:M,iconLeft:H,iconOnly:R,iconRight:Z,id:$,onMouseLeave:C=()=>{},disabled:S,onDarkBackground:j,size:I,disableMinWidth:ne}=i,le=wO(i,CO),{_iconLeft:W,_iconOnly:be,_iconRight:fe}=mS({children:h,icon:T,iconPlacement:M,iconLeft:H,iconOnly:R,iconRight:Z}),oe=co("button",b,{[xb]:!b.split(" ").some(F=>DO[F]),[I]:!!I,"disable-min-width":ne,"on-dark-background":j,"button-icon-left":!!W,"button-icon-only":!!be,"button-icon-right":!!fe}),pe=S?"true":"false";return De.createElement("button",zp({type:"button",id:$,style:s&&{width:s},className:oe,onMouseLeave:C,disabled:S,"aria-disabled":pe,ref:c},le),W?De.createElement(uo,{icon:W}):null,be?De.createElement(uo,{icon:be}):null,h?De.createElement("span",{className:"button-text-container"},De.createElement("span",{className:"button-text"},h)):null,fe?De.createElement(uo,{icon:fe}):null)});cs.displayName="Button";const MO=Hn.div`
    .sd-ai-navigation-tooltip {
        #sd-ai-navigation-sparkle-tooltip, 
        #sd-ai-navigation-view-more-options-tooltip,
        #sd-ai-navigation-reading-assistant-tooltip {
            bottom: 9px;
        }

        #sd-ai-navigation-my-library-tooltip {
            bottom: 57px;
        }

        #sd-ai-navigation-ai-core-tooltip {
            bottom: 106px !important;
        }

        #sd-ai-navigation-reading-assistant-tooltip {
            left: 100px;
        }

        &-content {
            border: 1px solid ${je.grey};
            background-color: ${je.grey}F2;
            opacity: 0;
            visibility: hidden;
            display: none;
            
            &-visible {
                position: absolute;
                left: 50px;
                display: flex;
                visibility: visible;
                opacity: 1;
                width: max-content;
                padding: 6px;
                ${Ss};
                background-color: ${je.grey}F2;
                border-radius: 4px;
                color: white;
                line-height: normal;
                align-items: center;
            }
            
            &-adjustX {
                left: 100px;
            }
        }
    }
`,Pu=({children:i,id:c,content:s,label:h,adjust:b})=>{const[T,M]=nn.useState(!1),H=()=>{M(!0)},R=()=>{M(!1)};return ve.jsx(MO,{children:ve.jsxs("div",{onMouseLeave:R,className:"sd-ai-navigation-tooltip-wrapper",children:[ve.jsx("div",{onFocus:H,onBlur:R,onMouseEnter:H,children:i}),s?ve.jsx("div",{"aria-label":h,className:"sd-ai-navigation-tooltip",role:"tooltip",children:ve.jsx("div",{id:c,className:co("sd-ai-navigation-tooltip-content",{"sd-ai-navigation-tooltip-content-visible":T,"sd-ai-navigation-tooltip-content-adjustX":b}),children:s})}):null]})})};function Up(){return Up=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var h in s)({}).hasOwnProperty.call(s,h)&&(i[h]=s[h])}return i},Up.apply(null,arguments)}var SS=i=>De.createElement(vl,Up({viewBox:"0 0 30 30",iconClassName:"icon-sparkle-speech"},i),De.createElement("path",{d:"M15 0.75C7.1426 0.75 0.75 7.1426 0.75 15V26.646C0.75 28.082 1.918 29.25 3.354 29.25H15C22.8574 29.25 29.25 22.8574 29.25 15C29.25 7.1426 22.8574 0.75 15 0.75ZM15 26.75H3.354C3.2969 26.75 3.25 26.7031 3.25 26.646V15C3.25 8.521 8.521 3.25 15 3.25C21.479 3.25 26.75 8.521 26.75 15C26.75 21.479 21.479 26.75 15 26.75Z"}),De.createElement("path",{d:"M20.75 17.25C21.7165 17.25 22.5 16.4665 22.5 15.5C22.5 14.5335 21.7165 13.75 20.75 13.75C19.7835 13.75 19 14.5335 19 15.5C19 16.4665 19.7835 17.25 20.75 17.25Z"}),De.createElement("path",{d:"M9.25 17.25C10.2165 17.25 11 16.4665 11 15.5C11 14.5335 10.2165 13.75 9.25 13.75C8.2835 13.75 7.5 14.5335 7.5 15.5C7.5 16.4665 8.2835 17.25 9.25 17.25Z"}),De.createElement("path",{d:"M15 17.25C15.9665 17.25 16.75 16.4665 16.75 15.5C16.75 14.5335 15.9665 13.75 15 13.75C14.0335 13.75 13.25 14.5335 13.25 15.5C13.25 16.4665 14.0335 17.25 15 17.25Z"}));SS.__docgenInfo={description:"",methods:[],displayName:"Speech",composes:["./Icon"]};const Db=["ULP","SAP"],_O=({page:i,entitlements:c})=>{var pe;const{SDAI:s,RA:h,raState:b}=c,[T,M]=nn.useState(!1),[H,R]=nn.useState(!1),[Z,$]=nn.useState(b.openState),[C,S]=nn.useState(void 0),j=i===Db[0],I=i===Db[1],ne=!1,le=co({"sd-ai-navigation-active-page":T,"sd-ai-navigation-more-options-expanded":H}),W=H?{}:{"aria-describedby":"sd-ai-navigation-view-more-options-tooltip"},be={},fe=F=>{F.key==="Escape"&&(M(!1),R(!1))},oe=F=>{F.target.closest("#sd-ai-navigation-container")||(M(!1),R(!1))};return nn.useEffect(()=>{var G,$e;if(!h)return;(G=window.aiComponents)==null||G.registerNavLoaded();const F=($e=window.aiComponents)==null?void 0:$e.getEventEmitter();F==null||F.on("stateChange",X=>{$(X==null?void 0:X.openState),S((X==null?void 0:X.openState)==="CLOSED"&&h)}),S(Z==="CLOSED"&&h)},[h]),nn.useEffect(()=>(document.addEventListener("keydown",fe),document.addEventListener("mousedown",oe),()=>{document.removeEventListener("keydown",fe),document.removeEventListener("mousedown",oe)}),[]),s&&ve.jsxs(KA,{role:"navigation",id:"sd-ai-navigation",className:s?"":"sd-ai-navigation-widget-hidden",children:[ve.jsxs(JA,{className:`sd-ai-navigation-main-wrapper ${s?"":"sd-ai-navigation-pendo-float-visible"}`,children:[ve.jsx(Pu,{id:"sd-ai-navigation-sparkle-tooltip",adjust:C,label:T?"Hide AI menu ToolTip":"Show AI menu ToolTip",content:ve.jsxs("div",{children:[ve.jsx("b",{children:T?"Hide":"Show"})," AI menu"]}),children:ve.jsx(Ku,{"aria-describedby":"sd-ai-navigation-sparkle-tooltip",children:ve.jsx(cs,{...be,"aria-label":T?"Hide AI menu":"Show AI menu","aria-expanded":T,onClick:()=>{M(!T),R(!1)},iconOnly:ve.jsx(tp,{className:le,children:ve.jsx(cS,{"aria-label":"ScienceDirect AI"})})})})}),C?ve.jsx(Pu,{id:"sd-ai-navigation-reading-assistant-tooltip",label:"Open Reading Assistant ToolTip",content:ve.jsxs("div",{children:[ve.jsx("b",{children:"Open"})," Reading Assistant"]}),children:ve.jsx(Ku,{"aria-describedby":"sd-ai-navigation-reading-assistant-tooltip",children:ve.jsx(vb,{children:ve.jsx(cs,{"aria-label":"Open Reading Assistant",onClick:(pe=window.aiComponents)==null?void 0:pe.openReadingAssistant,iconOnly:ve.jsx(SS,{"aria-label":"Reading Assistant"})})})})}):null,ne]}),s&&T&&ve.jsxs(PA,{children:[ve.jsxs(eO,{children:[ve.jsx(Pu,{id:"sd-ai-navigation-ai-core-tooltip",label:"Ask ScienceDirect AI",content:ve.jsxs("div",{children:[ve.jsx("b",{children:"Ask"})," ScienceDirect AI"]}),children:ve.jsx(Ku,{className:I?"sd-ai-navigation-no-cursor-event":"",children:ve.jsx(as,{"aria-labelledby":"sd-ai-navigation-ai-core-tooltip",tabIndex:I?-1:0,href:I?"javascript:void(0)":"/ai",iconOnly:ve.jsx(tp,{className:I?"sd-ai-navigation-active-page":"",children:ve.jsx(sS,{"aria-hidden":!0})})})})}),ve.jsx(Pu,{id:"sd-ai-navigation-my-library-tooltip",label:"Open Library",content:ve.jsxs("div",{children:[ve.jsx("b",{children:"Open"})," Library"]}),children:ve.jsx(Ku,{className:j?"sd-ai-navigation-no-cursor-event":"",children:ve.jsx(as,{"aria-labelledby":"sd-ai-navigation-my-library-tooltip",tabIndex:j?-1:0,href:j?"javascript:void(0)":"/user/library",iconOnly:ve.jsx(tp,{className:j?"sd-ai-navigation-active-page":"",children:ve.jsx(dS,{"aria-hidden":!0})})})})}),ve.jsx(Pu,{id:"sd-ai-navigation-view-more-options-tooltip",label:H?"Hide more options ToolTip":"View more options ToolTip",content:H?null:ve.jsxs("div",{children:[ve.jsx("b",{children:"View more"})," options"]}),children:ve.jsx(Ku,{...W,children:ve.jsx(vb,{children:ve.jsx(cs,{"aria-expanded":H,"aria-label":"view more options",onClick:()=>R(!H),iconOnly:ve.jsx(FA,{className:H?"sd-ai-navigation-active-view-more-options":"",children:ve.jsx(fS,{"aria-hidden":!0})})})})})})]}),H&&ve.jsxs(WA,{className:"sd-ai-navigation-expanded-stack",children:[ve.jsx(as,{isExternalLink:!0,href:"https://service.elsevier.com/app/answers/detail/a_id/38158/supporthub/sciencedirect/",children:"About ScienceDirect AI"}),ve.jsx(as,{isExternalLink:!I,href:"/ai?pendo=iaCcLNDf6Duhs0ih9DbmxVea9S8&step=7HHB3RSq3uwvR9fbcqJkARhd-_0",children:"Getting started"})]})]})]})};var fp={exports:{}},Wu={},dp={exports:{}},hp={},Mb;function zO(){return Mb||(Mb=1,function(i){/**
 * @license React
 * scheduler.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){function c(){if(pe=!1,X){var L=i.unstable_now();we=L;var ue=!0;try{e:{fe=!1,oe&&(oe=!1,G(Ie),Ie=-1),be=!0;var ce=W;try{t:{for(M(L),le=h(j);le!==null&&!(le.expirationTime>L&&R());){var Se=le.callback;if(typeof Se=="function"){le.callback=null,W=le.priorityLevel;var g=Se(le.expirationTime<=L);if(L=i.unstable_now(),typeof g=="function"){le.callback=g,M(L),ue=!0;break t}le===h(j)&&b(j),M(L)}else b(j);le=h(j)}if(le!==null)ue=!0;else{var U=h(I);U!==null&&Z(H,U.startTime-L),ue=!1}}break e}finally{le=null,W=ce,be=!1}ue=void 0}}finally{ue?Fe():X=!1}}}function s(L,ue){var ce=L.length;L.push(ue);e:for(;0<ce;){var Se=ce-1>>>1,g=L[Se];if(0<T(g,ue))L[Se]=ue,L[ce]=g,ce=Se;else break e}}function h(L){return L.length===0?null:L[0]}function b(L){if(L.length===0)return null;var ue=L[0],ce=L.pop();if(ce!==ue){L[0]=ce;e:for(var Se=0,g=L.length,U=g>>>1;Se<U;){var N=2*(Se+1)-1,K=L[N],P=N+1,se=L[P];if(0>T(K,ce))P<g&&0>T(se,K)?(L[Se]=se,L[P]=ce,Se=P):(L[Se]=K,L[N]=ce,Se=N);else if(P<g&&0>T(se,ce))L[Se]=se,L[P]=ce,Se=P;else break e}}return ue}function T(L,ue){var ce=L.sortIndex-ue.sortIndex;return ce!==0?ce:L.id-ue.id}function M(L){for(var ue=h(I);ue!==null;){if(ue.callback===null)b(I);else if(ue.startTime<=L)b(I),ue.sortIndex=ue.expirationTime,s(j,ue);else break;ue=h(I)}}function H(L){if(oe=!1,M(L),!fe)if(h(j)!==null)fe=!0,X||(X=!0,Fe());else{var ue=h(I);ue!==null&&Z(H,ue.startTime-L)}}function R(){return pe?!0:!(i.unstable_now()-we<de)}function Z(L,ue){Ie=F(function(){L(i.unstable_now())},ue)}if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error()),i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var $=performance;i.unstable_now=function(){return $.now()}}else{var C=Date,S=C.now();i.unstable_now=function(){return C.now()-S}}var j=[],I=[],ne=1,le=null,W=3,be=!1,fe=!1,oe=!1,pe=!1,F=typeof setTimeout=="function"?setTimeout:null,G=typeof clearTimeout=="function"?clearTimeout:null,$e=typeof setImmediate<"u"?setImmediate:null,X=!1,Ie=-1,de=5,we=-1;if(typeof $e=="function")var Fe=function(){$e(c)};else if(typeof MessageChannel<"u"){var at=new MessageChannel,ct=at.port2;at.port1.onmessage=c,Fe=function(){ct.postMessage(null)}}else Fe=function(){F(c,0)};i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(L){L.callback=null},i.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):de=0<L?Math.floor(1e3/L):5},i.unstable_getCurrentPriorityLevel=function(){return W},i.unstable_next=function(L){switch(W){case 1:case 2:case 3:var ue=3;break;default:ue=W}var ce=W;W=ue;try{return L()}finally{W=ce}},i.unstable_requestPaint=function(){pe=!0},i.unstable_runWithPriority=function(L,ue){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var ce=W;W=L;try{return ue()}finally{W=ce}},i.unstable_scheduleCallback=function(L,ue,ce){var Se=i.unstable_now();switch(typeof ce=="object"&&ce!==null?(ce=ce.delay,ce=typeof ce=="number"&&0<ce?Se+ce:Se):ce=Se,L){case 1:var g=-1;break;case 2:g=250;break;case 5:g=1073741823;break;case 4:g=1e4;break;default:g=5e3}return g=ce+g,L={id:ne++,callback:ue,priorityLevel:L,startTime:ce,expirationTime:g,sortIndex:-1},ce>Se?(L.sortIndex=ce,s(I,L),h(j)===null&&L===h(I)&&(oe?(G(Ie),Ie=-1):oe=!0,Z(H,ce-Se))):(L.sortIndex=g,s(j,L),fe||be||(fe=!0,X||(X=!0,Fe()))),L},i.unstable_shouldYield=R,i.unstable_wrapCallback=function(L){var ue=W;return function(){var ce=W;W=ue;try{return L.apply(this,arguments)}finally{W=ce}}},typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())})()}(hp)),hp}var _b;function UO(){return _b||(_b=1,dp.exports=zO()),dp.exports}var pp={exports:{}},Yt={},zb;function HO(){if(zb)return Yt;zb=1;/**
 * @license React
 * react-dom.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */return function(){function i(){}function c(C){return""+C}function s(C,S,j){var I=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;try{c(I);var ne=!1}catch{ne=!0}return ne&&(console.error("The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",typeof Symbol=="function"&&Symbol.toStringTag&&I[Symbol.toStringTag]||I.constructor.name||"Object"),c(I)),{$$typeof:Z,key:I==null?null:""+I,children:C,containerInfo:S,implementation:j}}function h(C,S){if(C==="font")return"";if(typeof S=="string")return S==="use-credentials"?S:""}function b(C){return C===null?"`null`":C===void 0?"`undefined`":C===""?"an empty string":'something with type "'+typeof C+'"'}function T(C){return C===null?"`null`":C===void 0?"`undefined`":C===""?"an empty string":typeof C=="string"?JSON.stringify(C):typeof C=="number"?"`"+C+"`":'something with type "'+typeof C+'"'}function M(){var C=$.H;return C===null&&console.error(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem.`),C}typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var H=hs(),R={d:{f:i,r:function(){throw Error("Invalid form element. requestFormReset must be passed a form that was rendered by React.")},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},Z=Symbol.for("react.portal"),$=H.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;typeof Map=="function"&&Map.prototype!=null&&typeof Map.prototype.forEach=="function"&&typeof Set=="function"&&Set.prototype!=null&&typeof Set.prototype.clear=="function"&&typeof Set.prototype.forEach=="function"||console.error("React depends on Map and Set built-in types. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills"),Yt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=R,Yt.createPortal=function(C,S){var j=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!S||S.nodeType!==1&&S.nodeType!==9&&S.nodeType!==11)throw Error("Target container is not a DOM element.");return s(C,S,null,j)},Yt.flushSync=function(C){var S=$.T,j=R.p;try{if($.T=null,R.p=2,C)return C()}finally{$.T=S,R.p=j,R.d.f()&&console.error("flushSync was called from inside a lifecycle method. React cannot flush when React is already rendering. Consider moving this call to a scheduler task or micro task.")}},Yt.preconnect=function(C,S){typeof C=="string"&&C?S!=null&&typeof S!="object"?console.error("ReactDOM.preconnect(): Expected the `options` argument (second) to be an object but encountered %s instead. The only supported option at this time is `crossOrigin` which accepts a string.",T(S)):S!=null&&typeof S.crossOrigin!="string"&&console.error("ReactDOM.preconnect(): Expected the `crossOrigin` option (second argument) to be a string but encountered %s instead. Try removing this option or passing a string value instead.",b(S.crossOrigin)):console.error("ReactDOM.preconnect(): Expected the `href` argument (first) to be a non-empty string but encountered %s instead.",b(C)),typeof C=="string"&&(S?(S=S.crossOrigin,S=typeof S=="string"?S==="use-credentials"?S:"":void 0):S=null,R.d.C(C,S))},Yt.prefetchDNS=function(C){if(typeof C!="string"||!C)console.error("ReactDOM.prefetchDNS(): Expected the `href` argument (first) to be a non-empty string but encountered %s instead.",b(C));else if(1<arguments.length){var S=arguments[1];typeof S=="object"&&S.hasOwnProperty("crossOrigin")?console.error("ReactDOM.prefetchDNS(): Expected only one argument, `href`, but encountered %s as a second argument instead. This argument is reserved for future options and is currently disallowed. It looks like the you are attempting to set a crossOrigin property for this DNS lookup hint. Browsers do not perform DNS queries using CORS and setting this attribute on the resource hint has no effect. Try calling ReactDOM.prefetchDNS() with just a single string argument, `href`.",T(S)):console.error("ReactDOM.prefetchDNS(): Expected only one argument, `href`, but encountered %s as a second argument instead. This argument is reserved for future options and is currently disallowed. Try calling ReactDOM.prefetchDNS() with just a single string argument, `href`.",T(S))}typeof C=="string"&&R.d.D(C)},Yt.preinit=function(C,S){if(typeof C=="string"&&C?S==null||typeof S!="object"?console.error("ReactDOM.preinit(): Expected the `options` argument (second) to be an object with an `as` property describing the type of resource to be preinitialized but encountered %s instead.",T(S)):S.as!=="style"&&S.as!=="script"&&console.error('ReactDOM.preinit(): Expected the `as` property in the `options` argument (second) to contain a valid value describing the type of resource to be preinitialized but encountered %s instead. Valid values for `as` are "style" and "script".',T(S.as)):console.error("ReactDOM.preinit(): Expected the `href` argument (first) to be a non-empty string but encountered %s instead.",b(C)),typeof C=="string"&&S&&typeof S.as=="string"){var j=S.as,I=h(j,S.crossOrigin),ne=typeof S.integrity=="string"?S.integrity:void 0,le=typeof S.fetchPriority=="string"?S.fetchPriority:void 0;j==="style"?R.d.S(C,typeof S.precedence=="string"?S.precedence:void 0,{crossOrigin:I,integrity:ne,fetchPriority:le}):j==="script"&&R.d.X(C,{crossOrigin:I,integrity:ne,fetchPriority:le,nonce:typeof S.nonce=="string"?S.nonce:void 0})}},Yt.preinitModule=function(C,S){var j="";if(typeof C=="string"&&C||(j+=" The `href` argument encountered was "+b(C)+"."),S!==void 0&&typeof S!="object"?j+=" The `options` argument encountered was "+b(S)+".":S&&"as"in S&&S.as!=="script"&&(j+=" The `as` option encountered was "+T(S.as)+"."),j)console.error("ReactDOM.preinitModule(): Expected up to two arguments, a non-empty `href` string and, optionally, an `options` object with a valid `as` property.%s",j);else switch(j=S&&typeof S.as=="string"?S.as:"script",j){case"script":break;default:j=T(j),console.error('ReactDOM.preinitModule(): Currently the only supported "as" type for this function is "script" but received "%s" instead. This warning was generated for `href` "%s". In the future other module types will be supported, aligning with the import-attributes proposal. Learn more here: (https://github.com/tc39/proposal-import-attributes)',j,C)}typeof C=="string"&&(typeof S=="object"&&S!==null?(S.as==null||S.as==="script")&&(j=h(S.as,S.crossOrigin),R.d.M(C,{crossOrigin:j,integrity:typeof S.integrity=="string"?S.integrity:void 0,nonce:typeof S.nonce=="string"?S.nonce:void 0})):S==null&&R.d.M(C))},Yt.preload=function(C,S){var j="";if(typeof C=="string"&&C||(j+=" The `href` argument encountered was "+b(C)+"."),S==null||typeof S!="object"?j+=" The `options` argument encountered was "+b(S)+".":typeof S.as=="string"&&S.as||(j+=" The `as` option encountered was "+b(S.as)+"."),j&&console.error('ReactDOM.preload(): Expected two arguments, a non-empty `href` string and an `options` object with an `as` property valid for a `<link rel="preload" as="..." />` tag.%s',j),typeof C=="string"&&typeof S=="object"&&S!==null&&typeof S.as=="string"){j=S.as;var I=h(j,S.crossOrigin);R.d.L(C,j,{crossOrigin:I,integrity:typeof S.integrity=="string"?S.integrity:void 0,nonce:typeof S.nonce=="string"?S.nonce:void 0,type:typeof S.type=="string"?S.type:void 0,fetchPriority:typeof S.fetchPriority=="string"?S.fetchPriority:void 0,referrerPolicy:typeof S.referrerPolicy=="string"?S.referrerPolicy:void 0,imageSrcSet:typeof S.imageSrcSet=="string"?S.imageSrcSet:void 0,imageSizes:typeof S.imageSizes=="string"?S.imageSizes:void 0,media:typeof S.media=="string"?S.media:void 0})}},Yt.preloadModule=function(C,S){var j="";typeof C=="string"&&C||(j+=" The `href` argument encountered was "+b(C)+"."),S!==void 0&&typeof S!="object"?j+=" The `options` argument encountered was "+b(S)+".":S&&"as"in S&&typeof S.as!="string"&&(j+=" The `as` option encountered was "+b(S.as)+"."),j&&console.error('ReactDOM.preloadModule(): Expected two arguments, a non-empty `href` string and, optionally, an `options` object with an `as` property valid for a `<link rel="modulepreload" as="..." />` tag.%s',j),typeof C=="string"&&(S?(j=h(S.as,S.crossOrigin),R.d.m(C,{as:typeof S.as=="string"&&S.as!=="script"?S.as:void 0,crossOrigin:j,integrity:typeof S.integrity=="string"?S.integrity:void 0})):R.d.m(C))},Yt.requestFormReset=function(C){R.d.r(C)},Yt.unstable_batchedUpdates=function(C,S){return C(S)},Yt.useFormState=function(C,S,j){return M().useFormState(C,S,j)},Yt.useFormStatus=function(){return M().useHostTransitionStatus()},Yt.version="19.1.1",typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())}(),Yt}var Ub;function jO(){return Ub||(Ub=1,pp.exports=HO()),pp.exports}var Hb;function NO(){if(Hb)return Wu;Hb=1;/**
 * @license React
 * react-dom-client.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */return function(){function i(e,t){for(e=e.memoizedState;e!==null&&0<t;)e=e.next,t--;return e}function c(e,t,n,a){if(n>=t.length)return a;var l=t[n],o=Ut(e)?e.slice():_e({},e);return o[l]=c(e[l],t,n+1,a),o}function s(e,t,n){if(t.length!==n.length)console.warn("copyWithRename() expects paths of the same length");else{for(var a=0;a<n.length-1;a++)if(t[a]!==n[a]){console.warn("copyWithRename() expects paths to be the same except for the deepest key");return}return h(e,t,n,0)}}function h(e,t,n,a){var l=t[a],o=Ut(e)?e.slice():_e({},e);return a+1===t.length?(o[n[a]]=o[l],Ut(o)?o.splice(l,1):delete o[l]):o[l]=h(e[l],t,n,a+1),o}function b(e,t,n){var a=t[n],l=Ut(e)?e.slice():_e({},e);return n+1===t.length?(Ut(l)?l.splice(a,1):delete l[a],l):(l[a]=b(e[a],t,n+1),l)}function T(){return!1}function M(){return null}function H(){}function R(){console.error("Do not call Hooks inside useEffect(...), useMemo(...), or other built-in Hooks. You can only call Hooks at the top level of your React function. For more information, see https://react.dev/link/rules-of-hooks")}function Z(){console.error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().")}function $(){}function C(e){var t=[];return e.forEach(function(n){t.push(n)}),t.sort().join(", ")}function S(e,t,n,a){return new $S(e,t,n,a)}function j(e,t){e.context===al&&(Sd(e.current,2,t,e,null,null),Ao())}function I(e,t){if(Rn!==null){var n=t.staleFamilies;t=t.updatedFamilies,Wi(),Ls(e.current,t,n),Ao()}}function ne(e){Rn=e}function le(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function W(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function be(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function fe(e){if(W(e)!==e)throw Error("Unable to find node on an unmounted component.")}function oe(e){var t=e.alternate;if(!t){if(t=W(e),t===null)throw Error("Unable to find node on an unmounted component.");return t!==e?null:e}for(var n=e,a=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(a=l.return,a!==null){n=a;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return fe(l),e;if(o===a)return fe(l),t;o=o.sibling}throw Error("Unable to find node on an unmounted component.")}if(n.return!==a.return)n=l,a=o;else{for(var u=!1,r=l.child;r;){if(r===n){u=!0,n=l,a=o;break}if(r===a){u=!0,a=l,n=o;break}r=r.sibling}if(!u){for(r=o.child;r;){if(r===n){u=!0,n=o,a=l;break}if(r===a){u=!0,a=o,n=l;break}r=r.sibling}if(!u)throw Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.")}}if(n.alternate!==a)throw Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.")}if(n.tag!==3)throw Error("Unable to find node on an unmounted component.");return n.stateNode.current===n?e:t}function pe(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=pe(e),t!==null)return t;e=e.sibling}return null}function F(e){return e===null||typeof e!="object"?null:(e=bv&&e[bv]||e["@@iterator"],typeof e=="function"?e:null)}function G(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===lE?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _o:return"Fragment";case wd:return"Profiler";case ic:return"StrictMode";case Dd:return"Suspense";case Md:return"SuspenseList";case _d:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case Mo:return"Portal";case Fn:return(e.displayName||"Context")+".Provider";case xd:return(e._context.displayName||"Context")+".Consumer";case uu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case uc:return t=e.displayName||null,t!==null?t:G(e.type)||"Memo";case sn:t=e._payload,e=e._init;try{return G(e(t))}catch{}}return null}function $e(e){return typeof e.tag=="number"?X(e):typeof e.name=="string"?e.name:null}function X(e){var t=e.type;switch(e.tag){case 31:return"Activity";case 24:return"Cache";case 9:return(t._context.displayName||"Context")+".Consumer";case 10:return(t.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 26:case 27:case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return G(t);case 8:return t===ic?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;break;case 29:if(t=e._debugInfo,t!=null){for(var n=t.length-1;0<=n;n--)if(typeof t[n].name=="string")return t[n].name}if(e.return!==null)return X(e.return)}return null}function Ie(e){return{current:e}}function de(e,t){0>Ea?console.error("Unexpected pop."):(t!==Ud[Ea]&&console.error("Unexpected Fiber popped."),e.current=zd[Ea],zd[Ea]=null,Ud[Ea]=null,Ea--)}function we(e,t,n){Ea++,zd[Ea]=e.current,Ud[Ea]=n,e.current=t}function Fe(e){return e===null&&console.error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue."),e}function at(e,t){we(Fa,t,e),we(ru,e,e),we(Wa,null,e);var n=t.nodeType;switch(n){case 9:case 11:n=n===9?"#document":"#fragment",t=(t=t.documentElement)&&(t=t.namespaceURI)?Zg(t):Ua;break;default:if(n=t.tagName,t=t.namespaceURI)t=Zg(t),t=Ig(t,n);else switch(n){case"svg":t=di;break;case"math":t=Qc;break;default:t=Ua}}n=n.toLowerCase(),n=am(null,n),n={context:t,ancestorInfo:n},de(Wa,e),we(Wa,n,e)}function ct(e){de(Wa,e),de(ru,e),de(Fa,e)}function L(){return Fe(Wa.current)}function ue(e){e.memoizedState!==null&&we(rc,e,e);var t=Fe(Wa.current),n=e.type,a=Ig(t.context,n);n=am(t.ancestorInfo,n),a={context:a,ancestorInfo:n},t!==a&&(we(ru,e,e),we(Wa,a,e))}function ce(e){ru.current===e&&(de(Wa,e),de(ru,e)),rc.current===e&&(de(rc,e),Qu._currentValue=no)}function Se(e){return typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object"}function g(e){try{return U(e),!1}catch{return!0}}function U(e){return""+e}function N(e,t){if(g(e))return console.error("The provided `%s` attribute is an unsupported type %s. This value must be coerced to a string before using it here.",t,Se(e)),U(e)}function K(e,t){if(g(e))return console.error("The provided `%s` CSS property is an unsupported type %s. This value must be coerced to a string before using it here.",t,Se(e)),U(e)}function P(e){if(g(e))return console.error("Form field values (value, checked, defaultValue, or defaultChecked props) must be strings, not %s. This value must be coerced to a string before using it here.",Se(e)),U(e)}function se(e){if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u")return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled)return!0;if(!t.supportsFiber)return console.error("The installed version of React DevTools is too old and will not work with the current version of React. Please update React DevTools. https://react.dev/link/react-devtools"),!0;try{Uo=t.inject(e),kt=t}catch(n){console.error("React instrumentation encountered an error: %s.",n)}return!!t.checkDCE}function Q(e){if(typeof fE=="function"&&dE(e),kt&&typeof kt.setStrictMode=="function")try{kt.setStrictMode(Uo,e)}catch(t){ta||(ta=!0,console.error("React instrumentation encountered an error: %s",t))}}function me(e){J=e}function ae(){J!==null&&typeof J.markCommitStopped=="function"&&J.markCommitStopped()}function he(e){J!==null&&typeof J.markComponentRenderStarted=="function"&&J.markComponentRenderStarted(e)}function ye(){J!==null&&typeof J.markComponentRenderStopped=="function"&&J.markComponentRenderStopped()}function et(e){J!==null&&typeof J.markRenderStarted=="function"&&J.markRenderStarted(e)}function Y(){J!==null&&typeof J.markRenderStopped=="function"&&J.markRenderStopped()}function Tt(e,t){J!==null&&typeof J.markStateUpdateScheduled=="function"&&J.markStateUpdateScheduled(e,t)}function sa(e){return e>>>=0,e===0?32:31-(hE(e)/pE|0)|0}function ka(e){if(e&1)return"SyncHydrationLane";if(e&2)return"Sync";if(e&4)return"InputContinuousHydration";if(e&8)return"InputContinuous";if(e&16)return"DefaultHydration";if(e&32)return"Default";if(e&128)return"TransitionHydration";if(e&4194048)return"Transition";if(e&62914560)return"Retry";if(e&67108864)return"SelectiveHydration";if(e&134217728)return"IdleHydration";if(e&268435456)return"Idle";if(e&536870912)return"Offscreen";if(e&1073741824)return"Deferred"}function yt(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return console.error("Should have found matching lanes. This is a bug in React."),e}}function fa(e,t,n){var a=e.pendingLanes;if(a===0)return 0;var l=0,o=e.suspendedLanes,u=e.pingedLanes;e=e.warmLanes;var r=a&134217727;return r!==0?(a=r&~o,a!==0?l=yt(a):(u&=r,u!==0?l=yt(u):n||(n=r&~e,n!==0&&(l=yt(n))))):(r=a&~o,r!==0?l=yt(r):u!==0?l=yt(u):n||(n=a&~e,n!==0&&(l=yt(n)))),l===0?0:t!==0&&t!==l&&(t&o)===0&&(o=l&-l,n=t&-t,o>=n||o===32&&(n&4194048)!==0)?t:l}function La(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Ts(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return console.error("Should have found matching lanes. This is a bug in React."),-1}}function Ue(){var e=cc;return cc<<=1,(cc&4194048)===0&&(cc=256),e}function bl(){var e=sc;return sc<<=1,(sc&62914560)===0&&(sc=4194304),e}function so(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ba(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function lr(e,t,n,a,l,o){var u=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var r=e.entanglements,d=e.expirationTimes,p=e.hiddenUpdates;for(n=u&~n;0<n;){var O=31-qt(n),D=1<<O;r[O]=0,d[O]=-1;var A=p[O];if(A!==null)for(p[O]=null,O=0;O<A.length;O++){var _=A[O];_!==null&&(_.lane&=-536870913)}n&=~D}a!==0&&Ei(e,a,0),o!==0&&l===0&&e.tag!==0&&(e.suspendedLanes|=o&~(u&~t))}function Ei(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-qt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|n&4194090}function or(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-qt(n),l=1<<a;l&t|e[a]&t&&(e[a]|=t),n&=~l}}function Ri(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ai(e,t,n){if(Bn)for(e=e.pendingUpdatersLaneMap;0<n;){var a=31-qt(n),l=1<<a;e[a].add(t),n&=~l}}function Oi(e,t){if(Bn)for(var n=e.pendingUpdatersLaneMap,a=e.memoizedUpdaters;0<t;){var l=31-qt(t);e=1<<l,l=n[l],0<l.size&&(l.forEach(function(o){var u=o.alternate;u!==null&&a.has(u)||a.add(o)}),l.clear()),t&=~e}}function fo(e){return e&=-e,Tn<e?na<e?(e&134217727)!==0?Aa:fc:na:Tn}function Ci(){var e=Ve.p;return e!==0?e:(e=window.event,e===void 0?Aa:hv(e.type))}function ho(e,t){var n=Ve.p;try{return Ve.p=e,t()}finally{Ve.p=n}}function Ya(e){delete e[Lt],delete e[Wt],delete e[Ld],delete e[mE],delete e[yE]}function jn(e){var t=e[Lt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[tl]||n[Lt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ev(e);e!==null;){if(n=e[Lt])return n;e=ev(e)}return t}e=n,n=e.parentNode}return null}function Nn(e){if(e=e[Lt]||e[tl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function qa(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error("getNodeFromInstance: Invalid argument.")}function f(e){var t=e[Sv];return t||(t=e[Sv]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function E(e){e[cu]=!0}function B(e,t){V(e,t),V(e+"Capture",t)}function V(e,t){Nl[e]&&console.error("EventRegistry: More than one plugin attempted to publish the same registration name, `%s`.",e),Nl[e]=t;var n=e.toLowerCase();for(Bd[n]=e,e==="onDoubleClick"&&(Bd.ondblclick=e),e=0;e<t.length;e++)Tv.add(t[e])}function ie(e,t){gE[t.type]||t.onChange||t.onInput||t.readOnly||t.disabled||t.value==null||console.error(e==="select"?"You provided a `value` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultValue`. Otherwise, set `onChange`.":"You provided a `value` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultValue`. Otherwise, set either `onChange` or `readOnly`."),t.onChange||t.readOnly||t.disabled||t.checked==null||console.error("You provided a `checked` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultChecked`. Otherwise, set either `onChange` or `readOnly`.")}function Me(e){return Ra.call(Rv,e)?!0:Ra.call(Ev,e)?!1:vE.test(e)?Rv[e]=!0:(Ev[e]=!0,console.error("Invalid attribute name: `%s`",e),!1)}function Oe(e,t,n){if(Me(t)){if(!e.hasAttribute(t)){switch(typeof n){case"symbol":case"object":return n;case"function":return n;case"boolean":if(n===!1)return n}return n===void 0?void 0:null}return e=e.getAttribute(t),e===""&&n===!0?!0:(N(n,t),e===""+n?n:e)}}function Ye(e,t,n){if(Me(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}N(n,t),e.setAttribute(t,""+n)}}function Ce(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}N(n,t),e.setAttribute(t,""+n)}}function Ot(e,t,n,a){if(a===null)e.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}N(a,n),e.setAttributeNS(t,n,""+a)}}function da(){}function Es(){if(su===0){Av=console.log,Ov=console.info,Cv=console.warn,wv=console.error,xv=console.group,Dv=console.groupCollapsed,Mv=console.groupEnd;var e={configurable:!0,enumerable:!0,value:da,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}su++}function Rs(){if(su--,su===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:_e({},e,{value:Av}),info:_e({},e,{value:Ov}),warn:_e({},e,{value:Cv}),error:_e({},e,{value:wv}),group:_e({},e,{value:xv}),groupCollapsed:_e({},e,{value:Dv}),groupEnd:_e({},e,{value:Mv})})}0>su&&console.error("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}function yn(e){if(Yd===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Yd=t&&t[1]||"",_v=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Yd+e+_v}function As(e,t){if(!e||qd)return"";var n=$d.get(e);if(n!==void 0)return n;qd=!0,n=Error.prepareStackTrace,Error.prepareStackTrace=void 0;var a=null;a=x.H,x.H=null,Es();try{var l={DetermineComponentFrameRoot:function(){try{if(t){var A=function(){throw Error()};if(Object.defineProperty(A.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(A,[])}catch(ee){var _=ee}Reflect.construct(e,[],A)}else{try{A.call()}catch(ee){_=ee}e.call(A.prototype)}}else{try{throw Error()}catch(ee){_=ee}(A=e())&&typeof A.catch=="function"&&A.catch(function(){})}}catch(ee){if(ee&&_&&typeof ee.stack=="string")return[ee.stack,_.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),r=u[0],d=u[1];if(r&&d){var p=r.split(`
`),O=d.split(`
`);for(u=o=0;o<p.length&&!p[o].includes("DetermineComponentFrameRoot");)o++;for(;u<O.length&&!O[u].includes("DetermineComponentFrameRoot");)u++;if(o===p.length||u===O.length)for(o=p.length-1,u=O.length-1;1<=o&&0<=u&&p[o]!==O[u];)u--;for(;1<=o&&0<=u;o--,u--)if(p[o]!==O[u]){if(o!==1||u!==1)do if(o--,u--,0>u||p[o]!==O[u]){var D=`
`+p[o].replace(" at new "," at ");return e.displayName&&D.includes("<anonymous>")&&(D=D.replace("<anonymous>",e.displayName)),typeof e=="function"&&$d.set(e,D),D}while(1<=o&&0<=u);break}}}finally{qd=!1,x.H=a,Rs(),Error.prepareStackTrace=n}return p=(p=e?e.displayName||e.name:"")?yn(p):"",typeof e=="function"&&$d.set(e,p),p}function Yp(e){var t=Error.prepareStackTrace;if(Error.prepareStackTrace=void 0,e=e.stack,Error.prepareStackTrace=t,e.startsWith(`Error: react-stack-top-frame
`)&&(e=e.slice(29)),t=e.indexOf(`
`),t!==-1&&(e=e.slice(t+1)),t=e.indexOf("react_stack_bottom_frame"),t!==-1&&(t=e.lastIndexOf(`
`,t)),t!==-1)e=e.slice(0,t);else return"";return e}function TS(e){switch(e.tag){case 26:case 27:case 5:return yn(e.type);case 16:return yn("Lazy");case 13:return yn("Suspense");case 19:return yn("SuspenseList");case 0:case 15:return As(e.type,!1);case 11:return As(e.type.render,!1);case 1:return As(e.type,!0);case 31:return yn("Activity");default:return""}}function qp(e){try{var t="";do{t+=TS(e);var n=e._debugInfo;if(n)for(var a=n.length-1;0<=a;a--){var l=n[a];if(typeof l.name=="string"){var o=t,u=l.env,r=yn(l.name+(u?" ["+u+"]":""));t=o+r}}e=e.return}while(e);return t}catch(d){return`
Error generating stack: `+d.message+`
`+d.stack}}function $p(e){return(e=e?e.displayName||e.name:"")?yn(e):""}function ir(){if(fn===null)return null;var e=fn._debugOwner;return e!=null?$e(e):null}function ES(){if(fn===null)return"";var e=fn;try{var t="";switch(e.tag===6&&(e=e.return),e.tag){case 26:case 27:case 5:t+=yn(e.type);break;case 13:t+=yn("Suspense");break;case 19:t+=yn("SuspenseList");break;case 31:t+=yn("Activity");break;case 30:case 0:case 15:case 1:e._debugOwner||t!==""||(t+=$p(e.type));break;case 11:e._debugOwner||t!==""||(t+=$p(e.type.render))}for(;e;)if(typeof e.tag=="number"){var n=e;e=n._debugOwner;var a=n._debugStack;e&&a&&(typeof a!="string"&&(n._debugStack=a=Yp(a)),a!==""&&(t+=`
`+a))}else if(e.debugStack!=null){var l=e.debugStack;(e=e.owner)&&l&&(t+=`
`+Yp(l))}else break;var o=t}catch(u){o=`
Error generating stack: `+u.message+`
`+u.stack}return o}function re(e,t,n,a,l,o,u){var r=fn;Os(e);try{return e!==null&&e._debugTask?e._debugTask.run(t.bind(null,n,a,l,o,u)):t(n,a,l,o,u)}finally{Os(r)}throw Error("runWithFiberInDEV should never be called in production. This is a bug in React.")}function Os(e){x.getCurrentStack=e===null?null:ES,aa=!1,fn=e}function gn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return P(e),e;default:return""}}function Vp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function RS(e){var t=Vp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t);P(e[t]);var a=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(u){P(u),a=""+u,o.call(this,u)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(u){P(u),a=""+u},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ur(e){e._valueTracker||(e._valueTracker=RS(e))}function Gp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=Vp(e)?e.checked?"true":"false":e.value),e=a,e!==n?(t.setValue(e),!0):!1}function rr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function vn(e){return e.replace(bE,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Xp(e,t){t.checked===void 0||t.defaultChecked===void 0||Uv||(console.error("%s contains an input of type %s with both checked and defaultChecked props. Input elements must be either controlled or uncontrolled (specify either the checked prop, or the defaultChecked prop, but not both). Decide between using a controlled or uncontrolled input element and remove one of these props. More info: https://react.dev/link/controlled-components",ir()||"A component",t.type),Uv=!0),t.value===void 0||t.defaultValue===void 0||zv||(console.error("%s contains an input of type %s with both value and defaultValue props. Input elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled input element and remove one of these props. More info: https://react.dev/link/controlled-components",ir()||"A component",t.type),zv=!0)}function Cs(e,t,n,a,l,o,u,r){e.name="",u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"?(N(u,"type"),e.type=u):e.removeAttribute("type"),t!=null?u==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+gn(t)):e.value!==""+gn(t)&&(e.value=""+gn(t)):u!=="submit"&&u!=="reset"||e.removeAttribute("value"),t!=null?ws(e,u,gn(t)):n!=null?ws(e,u,gn(n)):a!=null&&e.removeAttribute("value"),l==null&&o!=null&&(e.defaultChecked=!!o),l!=null&&(e.checked=l&&typeof l!="function"&&typeof l!="symbol"),r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?(N(r,"name"),e.name=""+gn(r)):e.removeAttribute("name")}function Qp(e,t,n,a,l,o,u,r){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(N(o,"type"),e.type=o),t!=null||n!=null){if(!(o!=="submit"&&o!=="reset"||t!=null))return;n=n!=null?""+gn(n):"",t=t!=null?""+gn(t):n,r||t===e.value||(e.value=t),e.defaultValue=t}a=a??l,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=r?e.checked:!!a,e.defaultChecked=!!a,u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(N(u,"name"),e.name=u)}function ws(e,t,n){t==="number"&&rr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Zp(e,t){t.value==null&&(typeof t.children=="object"&&t.children!==null?Cd.Children.forEach(t.children,function(n){n==null||typeof n=="string"||typeof n=="number"||typeof n=="bigint"||jv||(jv=!0,console.error("Cannot infer the option value of complex children. Pass a `value` prop or use a plain string as children to <option>."))}):t.dangerouslySetInnerHTML==null||Nv||(Nv=!0,console.error("Pass a `value` prop if you set dangerouslyInnerHTML so React knows which value should be selected."))),t.selected==null||Hv||(console.error("Use the `defaultValue` or `value` props on <select> instead of setting `selected` on <option>."),Hv=!0)}function Ip(){var e=ir();return e?`

Check the render method of \``+e+"`.":""}function po(e,t,n,a){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&a&&(e[n].defaultSelected=!0)}else{for(n=""+gn(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,a&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Jp(e,t){for(e=0;e<Lv.length;e++){var n=Lv[e];if(t[n]!=null){var a=Ut(t[n]);t.multiple&&!a?console.error("The `%s` prop supplied to <select> must be an array if `multiple` is true.%s",n,Ip()):!t.multiple&&a&&console.error("The `%s` prop supplied to <select> must be a scalar value if `multiple` is false.%s",n,Ip())}}t.value===void 0||t.defaultValue===void 0||kv||(console.error("Select elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled select element and remove one of these props. More info: https://react.dev/link/controlled-components"),kv=!0)}function Kp(e,t){t.value===void 0||t.defaultValue===void 0||Bv||(console.error("%s contains a textarea with both value and defaultValue props. Textarea elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled textarea and remove one of these props. More info: https://react.dev/link/controlled-components",ir()||"A component"),Bv=!0),t.children!=null&&t.value==null&&console.error("Use the `defaultValue` or `value` props instead of setting children on <textarea>.")}function Pp(e,t,n){if(t!=null&&(t=""+gn(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+gn(n):""}function Wp(e,t,n,a){if(t==null){if(a!=null){if(n!=null)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ut(a)){if(1<a.length)throw Error("<textarea> can only have at most one child.");a=a[0]}n=a}n==null&&(n=""),t=n}n=gn(t),e.defaultValue=n,a=e.textContent,a===n&&a!==""&&a!==null&&(e.value=a)}function Fp(e,t){return e.serverProps===void 0&&e.serverTail.length===0&&e.children.length===1&&3<e.distanceFromLeaf&&e.distanceFromLeaf>15-t?Fp(e.children[0],t):e}function ln(e){return"  "+"  ".repeat(e)}function mo(e){return"+ "+"  ".repeat(e)}function Sl(e){return"- "+"  ".repeat(e)}function em(e){switch(e.tag){case 26:case 27:case 5:return e.type;case 16:return"Lazy";case 13:return"Suspense";case 19:return"SuspenseList";case 0:case 15:return e=e.type,e.displayName||e.name||null;case 11:return e=e.type.render,e.displayName||e.name||null;case 1:return e=e.type,e.displayName||e.name||null;default:return null}}function wi(e,t){return Yv.test(e)?(e=JSON.stringify(e),e.length>t-2?8>t?'{"..."}':"{"+e.slice(0,t-7)+'..."}':"{"+e+"}"):e.length>t?5>t?'{"..."}':e.slice(0,t-3)+"...":e}function cr(e,t,n){var a=120-2*n;if(t===null)return mo(n)+wi(e,a)+`
`;if(typeof t=="string"){for(var l=0;l<t.length&&l<e.length&&t.charCodeAt(l)===e.charCodeAt(l);l++);return l>a-8&&10<l&&(e="..."+e.slice(l-8),t="..."+t.slice(l-8)),mo(n)+wi(e,a)+`
`+Sl(n)+wi(t,a)+`
`}return ln(n)+wi(e,a)+`
`}function xs(e){return Object.prototype.toString.call(e).replace(/^\[object (.*)\]$/,function(t,n){return n})}function xi(e,t){switch(typeof e){case"string":return e=JSON.stringify(e),e.length>t?5>t?'"..."':e.slice(0,t-4)+'..."':e;case"object":if(e===null)return"null";if(Ut(e))return"[...]";if(e.$$typeof===Pa)return(t=G(e.type))?"<"+t+">":"<...>";var n=xs(e);if(n==="Object"){n="",t-=2;for(var a in e)if(e.hasOwnProperty(a)){var l=JSON.stringify(a);if(l!=='"'+a+'"'&&(a=l),t-=a.length-2,l=xi(e[a],15>t?t:15),t-=l.length,0>t){n+=n===""?"...":", ...";break}n+=(n===""?"":",")+a+":"+l}return"{"+n+"}"}return n;case"function":return(t=e.displayName||e.name)?"function "+t:"function";default:return String(e)}}function yo(e,t){return typeof e!="string"||Yv.test(e)?"{"+xi(e,t-2)+"}":e.length>t-2?5>t?'"..."':'"'+e.slice(0,t-5)+'..."':'"'+e+'"'}function Ds(e,t,n){var a=120-n.length-e.length,l=[],o;for(o in t)if(t.hasOwnProperty(o)&&o!=="children"){var u=yo(t[o],120-n.length-o.length-1);a-=o.length+u.length+2,l.push(o+"="+u)}return l.length===0?n+"<"+e+`>
`:0<a?n+"<"+e+" "+l.join(" ")+`>
`:n+"<"+e+`
`+n+"  "+l.join(`
`+n+"  ")+`
`+n+`>
`}function AS(e,t,n){var a="",l=_e({},t),o;for(o in e)if(e.hasOwnProperty(o)){delete l[o];var u=120-2*n-o.length-2,r=xi(e[o],u);t.hasOwnProperty(o)?(u=xi(t[o],u),a+=mo(n)+o+": "+r+`
`,a+=Sl(n)+o+": "+u+`
`):a+=mo(n)+o+": "+r+`
`}for(var d in l)l.hasOwnProperty(d)&&(e=xi(l[d],120-2*n-d.length-2),a+=Sl(n)+d+": "+e+`
`);return a}function OS(e,t,n,a){var l="",o=new Map;for(p in n)n.hasOwnProperty(p)&&o.set(p.toLowerCase(),p);if(o.size===1&&o.has("children"))l+=Ds(e,t,ln(a));else{for(var u in t)if(t.hasOwnProperty(u)&&u!=="children"){var r=120-2*(a+1)-u.length-1,d=o.get(u.toLowerCase());if(d!==void 0){o.delete(u.toLowerCase());var p=t[u];d=n[d];var O=yo(p,r);r=yo(d,r),typeof p=="object"&&p!==null&&typeof d=="object"&&d!==null&&xs(p)==="Object"&&xs(d)==="Object"&&(2<Object.keys(p).length||2<Object.keys(d).length||-1<O.indexOf("...")||-1<r.indexOf("..."))?l+=ln(a+1)+u+`={{
`+AS(p,d,a+2)+ln(a+1)+`}}
`:(l+=mo(a+1)+u+"="+O+`
`,l+=Sl(a+1)+u+"="+r+`
`)}else l+=ln(a+1)+u+"="+yo(t[u],r)+`
`}o.forEach(function(D){if(D!=="children"){var A=120-2*(a+1)-D.length-1;l+=Sl(a+1)+D+"="+yo(n[D],A)+`
`}}),l=l===""?ln(a)+"<"+e+`>
`:ln(a)+"<"+e+`
`+l+ln(a)+`>
`}return e=n.children,t=t.children,typeof e=="string"||typeof e=="number"||typeof e=="bigint"?(o="",(typeof t=="string"||typeof t=="number"||typeof t=="bigint")&&(o=""+t),l+=cr(o,""+e,a+1)):(typeof t=="string"||typeof t=="number"||typeof t=="bigint")&&(l=e==null?l+cr(""+t,null,a+1):l+cr(""+t,void 0,a+1)),l}function tm(e,t){var n=em(e);if(n===null){for(n="",e=e.child;e;)n+=tm(e,t),e=e.sibling;return n}return ln(t)+"<"+n+`>
`}function Ms(e,t){var n=Fp(e,t);if(n!==e&&(e.children.length!==1||e.children[0]!==n))return ln(t)+`...
`+Ms(n,t+1);n="";var a=e.fiber._debugInfo;if(a)for(var l=0;l<a.length;l++){var o=a[l].name;typeof o=="string"&&(n+=ln(t)+"<"+o+`>
`,t++)}if(a="",l=e.fiber.pendingProps,e.fiber.tag===6)a=cr(l,e.serverProps,t),t++;else if(o=em(e.fiber),o!==null)if(e.serverProps===void 0){a=t;var u=120-2*a-o.length-2,r="";for(p in l)if(l.hasOwnProperty(p)&&p!=="children"){var d=yo(l[p],15);if(u-=p.length+d.length+2,0>u){r+=" ...";break}r+=" "+p+"="+d}a=ln(a)+"<"+o+r+`>
`,t++}else e.serverProps===null?(a=Ds(o,l,mo(t)),t++):typeof e.serverProps=="string"?console.error("Should not have matched a non HostText fiber to a Text node. This is a bug in React."):(a=OS(o,l,e.serverProps,t),t++);var p="";for(l=e.fiber.child,o=0;l&&o<e.children.length;)u=e.children[o],u.fiber===l?(p+=Ms(u,t),o++):p+=tm(l,t),l=l.sibling;for(l&&0<e.children.length&&(p+=ln(t)+`...
`),l=e.serverTail,e.serverProps===null&&t--,e=0;e<l.length;e++)o=l[e],p=typeof o=="string"?p+(Sl(t)+wi(o,120-2*t)+`
`):p+Ds(o.type,o.props,Sl(t));return n+a+p}function _s(e){try{return`

`+Ms(e,0)}catch{return""}}function nm(e,t,n){for(var a=t,l=null,o=0;a;)a===e&&(o=0),l={fiber:a,children:l!==null?[l]:[],serverProps:a===t?n:a===e?null:void 0,serverTail:[],distanceFromLeaf:o},o++,a=a.return;return l!==null?_s(l).replaceAll(/^[+-]/gm,">"):""}function am(e,t){var n=_e({},e||$v),a={tag:t};return qv.indexOf(t)!==-1&&(n.aTagInScope=null,n.buttonTagInScope=null,n.nobrTagInScope=null),TE.indexOf(t)!==-1&&(n.pTagInButtonScope=null),SE.indexOf(t)!==-1&&t!=="address"&&t!=="div"&&t!=="p"&&(n.listItemTagAutoclosing=null,n.dlItemTagAutoclosing=null),n.current=a,t==="form"&&(n.formTag=a),t==="a"&&(n.aTagInScope=a),t==="button"&&(n.buttonTagInScope=a),t==="nobr"&&(n.nobrTagInScope=a),t==="p"&&(n.pTagInButtonScope=a),t==="li"&&(n.listItemTagAutoclosing=a),(t==="dd"||t==="dt")&&(n.dlItemTagAutoclosing=a),t==="#document"||t==="html"?n.containerTagInScope=null:n.containerTagInScope||(n.containerTagInScope=a),e!==null||t!=="#document"&&t!=="html"&&t!=="body"?n.implicitRootScope===!0&&(n.implicitRootScope=!1):n.implicitRootScope=!0,n}function lm(e,t,n){switch(t){case"select":return e==="hr"||e==="option"||e==="optgroup"||e==="script"||e==="template"||e==="#text";case"optgroup":return e==="option"||e==="#text";case"option":return e==="#text";case"tr":return e==="th"||e==="td"||e==="style"||e==="script"||e==="template";case"tbody":case"thead":case"tfoot":return e==="tr"||e==="style"||e==="script"||e==="template";case"colgroup":return e==="col"||e==="template";case"table":return e==="caption"||e==="colgroup"||e==="tbody"||e==="tfoot"||e==="thead"||e==="style"||e==="script"||e==="template";case"head":return e==="base"||e==="basefont"||e==="bgsound"||e==="link"||e==="meta"||e==="title"||e==="noscript"||e==="noframes"||e==="style"||e==="script"||e==="template";case"html":if(n)break;return e==="head"||e==="body"||e==="frameset";case"frameset":return e==="frame";case"#document":if(!n)return e==="html"}switch(e){case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":return t!=="h1"&&t!=="h2"&&t!=="h3"&&t!=="h4"&&t!=="h5"&&t!=="h6";case"rp":case"rt":return EE.indexOf(t)===-1;case"caption":case"col":case"colgroup":case"frameset":case"frame":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return t==null;case"head":return n||t===null;case"html":return n&&t==="#document"||t===null;case"body":return n&&(t==="#document"||t==="html")||t===null}return!0}function CS(e,t){switch(e){case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"menu":case"nav":case"ol":case"p":case"section":case"summary":case"ul":case"pre":case"listing":case"table":case"hr":case"xmp":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":return t.pTagInButtonScope;case"form":return t.formTag||t.pTagInButtonScope;case"li":return t.listItemTagAutoclosing;case"dd":case"dt":return t.dlItemTagAutoclosing;case"button":return t.buttonTagInScope;case"a":return t.aTagInScope;case"nobr":return t.nobrTagInScope}return null}function om(e,t){for(;e;){switch(e.tag){case 5:case 26:case 27:if(e.type===t)return e}e=e.return}return null}function zs(e,t){t=t||$v;var n=t.current;if(t=(n=lm(e,n&&n.tag,t.implicitRootScope)?null:n)?null:CS(e,t),t=n||t,!t)return!0;var a=t.tag;if(t=String(!!n)+"|"+e+"|"+a,dc[t])return!1;dc[t]=!0;var l=(t=fn)?om(t.return,a):null,o=t!==null&&l!==null?nm(l,t,null):"",u="<"+e+">";return n?(n="",a==="table"&&e==="tr"&&(n+=" Add a <tbody>, <thead> or <tfoot> to your code to match the DOM tree generated by the browser."),console.error(`In HTML, %s cannot be a child of <%s>.%s
This will cause a hydration error.%s`,u,a,n,o)):console.error(`In HTML, %s cannot be a descendant of <%s>.
This will cause a hydration error.%s`,u,a,o),t&&(e=t.return,l===null||e===null||l===e&&e._debugOwner===t._debugOwner||re(l,function(){console.error(`<%s> cannot contain a nested %s.
See this log for the ancestor stack trace.`,a,u)})),!1}function sr(e,t,n){if(n||lm("#text",t,!1))return!0;if(n="#text|"+t,dc[n])return!1;dc[n]=!0;var a=(n=fn)?om(n,t):null;return n=n!==null&&a!==null?nm(a,n,n.tag!==6?{children:null}:null):"",/\S/.test(e)?console.error(`In HTML, text nodes cannot be a child of <%s>.
This will cause a hydration error.%s`,t,n):console.error(`In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.
This will cause a hydration error.%s`,t,n),!1}function Di(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}function wS(e){return e.replace(OE,function(t,n){return n.toUpperCase()})}function im(e,t,n){var a=t.indexOf("--")===0;a||(-1<t.indexOf("-")?Ho.hasOwnProperty(t)&&Ho[t]||(Ho[t]=!0,console.error("Unsupported style property %s. Did you mean %s?",t,wS(t.replace(AE,"ms-")))):RE.test(t)?Ho.hasOwnProperty(t)&&Ho[t]||(Ho[t]=!0,console.error("Unsupported vendor-prefixed style property %s. Did you mean %s?",t,t.charAt(0).toUpperCase()+t.slice(1))):!Xv.test(n)||Gd.hasOwnProperty(n)&&Gd[n]||(Gd[n]=!0,console.error(`Style property values shouldn't contain a semicolon. Try "%s: %s" instead.`,t,n.replace(Xv,""))),typeof n=="number"&&(isNaN(n)?Qv||(Qv=!0,console.error("`NaN` is an invalid value for the `%s` css style property.",t)):isFinite(n)||Zv||(Zv=!0,console.error("`Infinity` is an invalid value for the `%s` css style property.",t)))),n==null||typeof n=="boolean"||n===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,n):typeof n!="number"||n===0||Iv.has(t)?t==="float"?e.cssFloat=n:(K(n,t),e[t]=(""+n).trim()):e[t]=n+"px"}function um(e,t,n){if(t!=null&&typeof t!="object")throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");if(t&&Object.freeze(t),e=e.style,n!=null){if(t){var a={};if(n){for(var l in n)if(n.hasOwnProperty(l)&&!t.hasOwnProperty(l))for(var o=Vd[l]||[l],u=0;u<o.length;u++)a[o[u]]=l}for(var r in t)if(t.hasOwnProperty(r)&&(!n||n[r]!==t[r]))for(l=Vd[r]||[r],o=0;o<l.length;o++)a[l[o]]=r;r={};for(var d in t)for(l=Vd[d]||[d],o=0;o<l.length;o++)r[l[o]]=d;d={};for(var p in a)if(l=a[p],(o=r[p])&&l!==o&&(u=l+","+o,!d[u])){d[u]=!0,u=console;var O=t[l];u.error.call(u,"%s a style property during rerender (%s) when a conflicting property is set (%s) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.",O==null||typeof O=="boolean"||O===""?"Removing":"Updating",l,o)}}for(var D in n)!n.hasOwnProperty(D)||t!=null&&t.hasOwnProperty(D)||(D.indexOf("--")===0?e.setProperty(D,""):D==="float"?e.cssFloat="":e[D]="");for(var A in t)p=t[A],t.hasOwnProperty(A)&&n[A]!==p&&im(e,A,p)}else for(a in t)t.hasOwnProperty(a)&&im(e,a,t[a])}function Mi(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}function rm(e){return CE.get(e)||e}function xS(e,t){if(Ra.call(No,t)&&No[t])return!0;if(xE.test(t)){if(e="aria-"+t.slice(4).toLowerCase(),e=Jv.hasOwnProperty(e)?e:null,e==null)return console.error("Invalid ARIA attribute `%s`. ARIA attributes follow the pattern aria-* and must be lowercase.",t),No[t]=!0;if(t!==e)return console.error("Invalid ARIA attribute `%s`. Did you mean `%s`?",t,e),No[t]=!0}if(wE.test(t)){if(e=t.toLowerCase(),e=Jv.hasOwnProperty(e)?e:null,e==null)return No[t]=!0,!1;t!==e&&(console.error("Unknown ARIA attribute `%s`. Did you mean `%s`?",t,e),No[t]=!0)}return!0}function DS(e,t){var n=[],a;for(a in t)xS(e,a)||n.push(a);t=n.map(function(l){return"`"+l+"`"}).join(", "),n.length===1?console.error("Invalid aria prop %s on <%s> tag. For details, see https://react.dev/link/invalid-aria-props",t,e):1<n.length&&console.error("Invalid aria props %s on <%s> tag. For details, see https://react.dev/link/invalid-aria-props",t,e)}function MS(e,t,n,a){if(Ra.call($t,t)&&$t[t])return!0;var l=t.toLowerCase();if(l==="onfocusin"||l==="onfocusout")return console.error("React uses onFocus and onBlur instead of onFocusIn and onFocusOut. All React events are normalized to bubble, so onFocusIn and onFocusOut are not needed/supported by React."),$t[t]=!0;if(typeof n=="function"&&(e==="form"&&t==="action"||e==="input"&&t==="formAction"||e==="button"&&t==="formAction"))return!0;if(a!=null){if(e=a.possibleRegistrationNames,a.registrationNameDependencies.hasOwnProperty(t))return!0;if(a=e.hasOwnProperty(l)?e[l]:null,a!=null)return console.error("Invalid event handler property `%s`. Did you mean `%s`?",t,a),$t[t]=!0;if(Pv.test(t))return console.error("Unknown event handler property `%s`. It will be ignored.",t),$t[t]=!0}else if(Pv.test(t))return DE.test(t)&&console.error("Invalid event handler property `%s`. React events use the camelCase naming convention, for example `onClick`.",t),$t[t]=!0;if(ME.test(t)||_E.test(t))return!0;if(l==="innerhtml")return console.error("Directly setting property `innerHTML` is not permitted. For more information, lookup documentation on `dangerouslySetInnerHTML`."),$t[t]=!0;if(l==="aria")return console.error("The `aria` attribute is reserved for future use in React. Pass individual `aria-` attributes instead."),$t[t]=!0;if(l==="is"&&n!==null&&n!==void 0&&typeof n!="string")return console.error("Received a `%s` for a string attribute `is`. If this is expected, cast the value to a string.",typeof n),$t[t]=!0;if(typeof n=="number"&&isNaN(n))return console.error("Received NaN for the `%s` attribute. If this is expected, cast the value to a string.",t),$t[t]=!0;if(pc.hasOwnProperty(l)){if(l=pc[l],l!==t)return console.error("Invalid DOM property `%s`. Did you mean `%s`?",t,l),$t[t]=!0}else if(t!==l)return console.error("React does not recognize the `%s` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `%s` instead. If you accidentally passed it from a parent component, remove it from the DOM element.",t,l),$t[t]=!0;switch(t){case"dangerouslySetInnerHTML":case"children":case"style":case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":return!0;case"innerText":case"textContent":return!0}switch(typeof n){case"boolean":switch(t){case"autoFocus":case"checked":case"multiple":case"muted":case"selected":case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":case"capture":case"download":case"inert":return!0;default:return l=t.toLowerCase().slice(0,5),l==="data-"||l==="aria-"?!0:(n?console.error('Received `%s` for a non-boolean attribute `%s`.\n\nIf you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}.',n,t,t,n,t):console.error('Received `%s` for a non-boolean attribute `%s`.\n\nIf you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}.\n\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.',n,t,t,n,t,t,t),$t[t]=!0)}case"function":case"symbol":return $t[t]=!0,!1;case"string":if(n==="false"||n==="true"){switch(t){case"checked":case"selected":case"multiple":case"muted":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":case"inert":break;default:return!0}console.error("Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}?",n,t,n==="false"?"The browser will interpret it as a truthy value.":'Although this works, it will not work as expected if you pass the string "false".',t,n),$t[t]=!0}}return!0}function _S(e,t,n){var a=[],l;for(l in t)MS(e,l,t[l],n)||a.push(l);t=a.map(function(o){return"`"+o+"`"}).join(", "),a.length===1?console.error("Invalid value for prop %s on <%s> tag. Either remove it from the element, or pass a string or number value to keep it in the DOM. For details, see https://react.dev/link/attribute-behavior ",t,e):1<a.length&&console.error("Invalid values for props %s on <%s> tag. Either remove them from the element, or pass a string or number value to keep them in the DOM. For details, see https://react.dev/link/attribute-behavior ",t,e)}function _i(e){return zE.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}function Us(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}function cm(e){var t=Nn(e);if(t&&(e=t.stateNode)){var n=e[Wt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Cs(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(N(t,"name"),n=n.querySelectorAll('input[name="'+vn(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var l=a[Wt]||null;if(!l)throw Error("ReactDOMInput: Mixing React and non-React radio inputs with the same `name` is not supported.");Cs(a,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(t=0;t<n.length;t++)a=n[t],a.form===e.form&&Gp(a)}break e;case"textarea":Pp(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&po(e,!!n.multiple,t,!1)}}}function sm(e,t,n){if(Xd)return e(t,n);Xd=!0;try{var a=e(t);return a}finally{if(Xd=!1,(ko!==null||Lo!==null)&&(Ao(),ko&&(t=ko,e=Lo,Lo=ko=null,cm(t),e)))for(t=0;t<e.length;t++)cm(e[t])}}function zi(e,t){var n=e.stateNode;if(n===null)return null;var a=n[Wt]||null;if(a===null)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error("Expected `"+t+"` listener to be a function, instead got a value of `"+typeof n+"` type.");return n}function fm(){if(mc)return mc;var e,t=Zd,n=t.length,a,l="value"in nl?nl.value:nl.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var u=n-e;for(a=1;a<=u&&t[n-a]===l[o-a];a++);return mc=l.slice(e,1<a?1-a:void 0)}function fr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function dr(){return!0}function dm(){return!1}function It(e){function t(n,a,l,o,u){this._reactName=n,this._targetInst=l,this.type=a,this.nativeEvent=o,this.target=u,this.currentTarget=null;for(var r in e)e.hasOwnProperty(r)&&(n=e[r],this[r]=n?n(o):o[r]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?dr:dm,this.isPropagationStopped=dm,this}return _e(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=dr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=dr)},persist:function(){},isPersistent:dr}),t}function zS(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=XE[e])?!!t[e]:!1}function Hs(){return zS}function hm(e,t){switch(e){case"keyup":return aR.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==t0;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function pm(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}function US(e,t){switch(e){case"compositionend":return pm(t);case"keypress":return t.which!==a0?null:(o0=!0,l0);case"textInput":return e=t.data,e===l0&&o0?null:e;default:return null}}function HS(e,t){if(Bo)return e==="compositionend"||!Pd&&hm(e,t)?(e=fm(),mc=Zd=nl=null,Bo=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return n0&&t.locale!=="ko"?null:t.data;default:return null}}function mm(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!oR[e.type]:t==="textarea"}function jS(e){if(!la)return!1;e="on"+e;var t=e in document;return t||(t=document.createElement("div"),t.setAttribute(e,"return;"),t=typeof t[e]=="function"),t}function ym(e,t,n,a){ko?Lo?Lo.push(a):Lo=[a]:ko=a,t=Kr(t,"onChange"),0<t.length&&(n=new yc("onChange","change",null,n,a),e.push({event:n,listeners:t}))}function NS(e){kg(e,0)}function hr(e){var t=qa(e);if(Gp(t))return e}function gm(e,t){if(e==="change")return t}function vm(){yu&&(yu.detachEvent("onpropertychange",bm),gu=yu=null)}function bm(e){if(e.propertyName==="value"&&hr(gu)){var t=[];ym(t,gu,e,Us(e)),sm(NS,t)}}function kS(e,t,n){e==="focusin"?(vm(),yu=t,gu=n,yu.attachEvent("onpropertychange",bm)):e==="focusout"&&vm()}function LS(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return hr(gu)}function BS(e,t){if(e==="click")return hr(t)}function YS(e,t){if(e==="input"||e==="change")return hr(t)}function qS(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}function Ui(e,t){if(Vt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var l=n[a];if(!Ra.call(t,l)||!Vt(e[l],t[l]))return!1}return!0}function Sm(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Tm(e,t){var n=Sm(e);e=0;for(var a;n;){if(n.nodeType===3){if(a=e+n.textContent.length,e<=t&&a>=t)return{node:n,offset:t-e};e=a}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Sm(n)}}function Em(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Em(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Rm(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=rr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=rr(e.document)}return t}function js(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Am(e,t,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Fd||Yo==null||Yo!==rr(a)||(a=Yo,"selectionStart"in a&&js(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),vu&&Ui(vu,a)||(vu=a,a=Kr(Wd,"onSelect"),0<a.length&&(t=new yc("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=Yo)))}function Tl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}function El(e){if(eh[e])return eh[e];if(!qo[e])return e;var t=qo[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in u0)return eh[e]=t[n];return e}function kn(e,t){d0.set(e,t),B(t,[e])}function on(e,t){if(typeof e=="object"&&e!==null){var n=nh.get(e);return n!==void 0?n:(t={value:e,source:t,stack:qp(t)},nh.set(e,t),t)}return{value:e,source:t,stack:qp(t)}}function pr(){for(var e=$o,t=lh=$o=0;t<e;){var n=En[t];En[t++]=null;var a=En[t];En[t++]=null;var l=En[t];En[t++]=null;var o=En[t];if(En[t++]=null,a!==null&&l!==null){var u=a.pending;u===null?l.next=l:(l.next=u.next,u.next=l),a.pending=l}o!==0&&Om(n,l,o)}}function mr(e,t,n,a){En[$o++]=e,En[$o++]=t,En[$o++]=n,En[$o++]=a,lh|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Ns(e,t,n,a){return mr(e,t,n,a),yr(e)}function Jt(e,t){return mr(e,null,null,t),yr(e)}function Om(e,t,n){e.lanes|=n;var a=e.alternate;a!==null&&(a.lanes|=n);for(var l=!1,o=e.return;o!==null;)o.childLanes|=n,a=o.alternate,a!==null&&(a.childLanes|=n),o.tag===22&&(e=o.stateNode,e===null||e._visibility&ah||(l=!0)),e=o,o=o.return;return e.tag===3?(o=e.stateNode,l&&t!==null&&(l=31-qt(n),e=o.hiddenUpdates,a=e[l],a===null?e[l]=[t]:a.push(t),t.lane=n|536870912),o):null}function yr(e){if(Yu>xR)throw Pl=Yu=0,qu=Hh=null,Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");Pl>DR&&(Pl=0,qu=null,console.error("Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.")),e.alternate===null&&(e.flags&4098)!==0&&Mg(e);for(var t=e,n=t.return;n!==null;)t.alternate===null&&(t.flags&4098)!==0&&Mg(e),t=n,n=t.return;return t.tag===3?t.stateNode:null}function Rl(e){if(Rn===null)return e;var t=Rn(e);return t===void 0?e:t.current}function ks(e){if(Rn===null)return e;var t=Rn(e);return t===void 0?e!=null&&typeof e.render=="function"&&(t=Rl(e.render),e.render!==t)?(t={$$typeof:uu,render:t},e.displayName!==void 0&&(t.displayName=e.displayName),t):e:t.current}function Cm(e,t){if(Rn===null)return!1;var n=e.elementType;t=t.type;var a=!1,l=typeof t=="object"&&t!==null?t.$$typeof:null;switch(e.tag){case 1:typeof t=="function"&&(a=!0);break;case 0:(typeof t=="function"||l===sn)&&(a=!0);break;case 11:(l===uu||l===sn)&&(a=!0);break;case 14:case 15:(l===uc||l===sn)&&(a=!0);break;default:return!1}return!!(a&&(e=Rn(n),e!==void 0&&e===Rn(t)))}function wm(e){Rn!==null&&typeof WeakSet=="function"&&(Vo===null&&(Vo=new WeakSet),Vo.add(e))}function Ls(e,t,n){var a=e.alternate,l=e.child,o=e.sibling,u=e.tag,r=e.type,d=null;switch(u){case 0:case 15:case 1:d=r;break;case 11:d=r.render}if(Rn===null)throw Error("Expected resolveFamily to be set during hot reload.");var p=!1;r=!1,d!==null&&(d=Rn(d),d!==void 0&&(n.has(d)?r=!0:t.has(d)&&(u===1?r=!0:p=!0))),Vo!==null&&(Vo.has(e)||a!==null&&Vo.has(a))&&(r=!0),r&&(e._debugNeedsRemount=!0),(r||p)&&(a=Jt(e,2),a!==null&&ht(a,e,2)),l===null||r||Ls(l,t,n),o!==null&&Ls(o,t,n)}function $S(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null,this.actualDuration=-0,this.actualStartTime=-1.1,this.treeBaseDuration=this.selfBaseDuration=-0,this._debugTask=this._debugStack=this._debugOwner=this._debugInfo=null,this._debugNeedsRemount=!1,this._debugHookTypes=null,p0||typeof Object.preventExtensions!="function"||Object.preventExtensions(this)}function Bs(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ha(e,t){var n=e.alternate;switch(n===null?(n=S(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n._debugOwner=e._debugOwner,n._debugStack=e._debugStack,n._debugTask=e._debugTask,n._debugHookTypes=e._debugHookTypes,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null,n.actualDuration=-0,n.actualStartTime=-1.1),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext,_debugThenableState:t._debugThenableState},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n.selfBaseDuration=e.selfBaseDuration,n.treeBaseDuration=e.treeBaseDuration,n._debugInfo=e._debugInfo,n._debugNeedsRemount=e._debugNeedsRemount,n.tag){case 0:case 15:n.type=Rl(e.type);break;case 1:n.type=Rl(e.type);break;case 11:n.type=ks(e.type)}return n}function xm(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null,e.selfBaseDuration=0,e.treeBaseDuration=0):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext,_debugThenableState:t._debugThenableState},e.selfBaseDuration=n.selfBaseDuration,e.treeBaseDuration=n.treeBaseDuration),e}function Ys(e,t,n,a,l,o){var u=0,r=e;if(typeof e=="function")Bs(e)&&(u=1),r=Rl(r);else if(typeof e=="string")u=L(),u=$T(e,n,u)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case _d:return t=S(31,n,t,l),t.elementType=_d,t.lanes=o,t;case _o:return Al(n.children,l,o,t);case ic:u=8,l|=Bt,l|=Yn;break;case wd:return e=n,a=l,typeof e.id!="string"&&console.error('Profiler must specify an "id" of type `string` as a prop. Received the type `%s` instead.',typeof e.id),t=S(12,e,t,a|Ht),t.elementType=wd,t.lanes=o,t.stateNode={effectDuration:0,passiveEffectDuration:0},t;case Dd:return t=S(13,n,t,l),t.elementType=Dd,t.lanes=o,t;case Md:return t=S(19,n,t,l),t.elementType=Md,t.lanes=o,t;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case nE:case Fn:u=10;break e;case xd:u=9;break e;case uu:u=11,r=ks(r);break e;case uc:u=14;break e;case sn:u=16,r=null;break e}r="",(e===void 0||typeof e=="object"&&e!==null&&Object.keys(e).length===0)&&(r+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports."),e===null?n="null":Ut(e)?n="array":e!==void 0&&e.$$typeof===Pa?(n="<"+(G(e.type)||"Unknown")+" />",r=" Did you accidentally export a JSX literal instead of a component?"):n=typeof e,(u=a?$e(a):null)&&(r+=`

Check the render method of \``+u+"`."),u=29,n=Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+(n+"."+r)),r=null}return t=S(u,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t._debugOwner=a,t}function gr(e,t,n){return t=Ys(e.type,e.key,e.props,e._owner,t,n),t._debugOwner=e._owner,t._debugStack=e._debugStack,t._debugTask=e._debugTask,t}function Al(e,t,n,a){return e=S(7,e,a,t),e.lanes=n,e}function qs(e,t,n){return e=S(6,e,null,t),e.lanes=n,e}function $s(e,t,n){return t=S(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ol(e,t){Cl(),Go[Xo++]=bc,Go[Xo++]=vc,vc=e,bc=t}function Dm(e,t,n){Cl(),An[On++]=Oa,An[On++]=Ca,An[On++]=Bl,Bl=e;var a=Oa;e=Ca;var l=32-qt(a)-1;a&=~(1<<l),n+=1;var o=32-qt(t)+l;if(30<o){var u=l-l%5;o=(a&(1<<u)-1).toString(32),a>>=u,l-=u,Oa=1<<32-qt(t)+l|n<<l|a,Ca=o+e}else Oa=1<<o|n<<l|a,Ca=e}function Vs(e){Cl(),e.return!==null&&(Ol(e,1),Dm(e,1,0))}function Gs(e){for(;e===vc;)vc=Go[--Xo],Go[Xo]=null,bc=Go[--Xo],Go[Xo]=null;for(;e===Bl;)Bl=An[--On],An[On]=null,Ca=An[--On],An[On]=null,Oa=An[--On],An[On]=null}function Cl(){qe||console.error("Expected to be hydrating. This is a bug in React. Please file an issue.")}function wl(e,t){if(e.return===null){if(Cn===null)Cn={fiber:e,children:[],serverProps:void 0,serverTail:[],distanceFromLeaf:t};else{if(Cn.fiber!==e)throw Error("Saw multiple hydration diff roots in a pass. This is a bug in React.");Cn.distanceFromLeaf>t&&(Cn.distanceFromLeaf=t)}return Cn}var n=wl(e.return,t+1).children;return 0<n.length&&n[n.length-1].fiber===e?(n=n[n.length-1],n.distanceFromLeaf>t&&(n.distanceFromLeaf=t),n):(t={fiber:e,children:[],serverProps:void 0,serverTail:[],distanceFromLeaf:t},n.push(t),t)}function Xs(e,t){wa||(e=wl(e,0),e.serverProps=null,t!==null&&(t=Pg(t),e.serverTail.push(t)))}function xl(e){var t="",n=Cn;throw n!==null&&(Cn=null,t=_s(n)),Ni(on(Error(`Hydration failed because the server rendered HTML didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:

- A server/client branch \`if (typeof window !== 'undefined')\`.
- Variable input such as \`Date.now()\` or \`Math.random()\` which changes each time it's called.
- Date formatting in a user's locale which doesn't match the server.
- External changing data without sending a snapshot of it along with the HTML.
- Invalid HTML tag nesting.

It can also happen if the client has a browser extension installed which messes with the HTML before React loaded.

https://react.dev/link/hydration-mismatch`+t),e)),oh}function Mm(e){var t=e.stateNode,n=e.type,a=e.memoizedProps;switch(t[Lt]=e,t[Wt]=a,cd(n,a),n){case"dialog":Le("cancel",t),Le("close",t);break;case"iframe":case"object":case"embed":Le("load",t);break;case"video":case"audio":for(n=0;n<$u.length;n++)Le($u[n],t);break;case"source":Le("error",t);break;case"img":case"image":case"link":Le("error",t),Le("load",t);break;case"details":Le("toggle",t);break;case"input":ie("input",a),Le("invalid",t),Xp(t,a),Qp(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),ur(t);break;case"option":Zp(t,a);break;case"select":ie("select",a),Le("invalid",t),Jp(t,a);break;case"textarea":ie("textarea",a),Le("invalid",t),Kp(t,a),Wp(t,a.value,a.defaultValue,a.children),ur(t)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||a.suppressHydrationWarning===!0||qg(t.textContent,n)?(a.popover!=null&&(Le("beforetoggle",t),Le("toggle",t)),a.onScroll!=null&&Le("scroll",t),a.onScrollEnd!=null&&Le("scrollend",t),a.onClick!=null&&(t.onclick=Pr),t=!0):t=!1,t||xl(e)}function _m(e){for(Gt=e.return;Gt;)switch(Gt.tag){case 5:case 13:oa=!1;return;case 27:case 3:oa=!0;return;default:Gt=Gt.return}}function Hi(e){if(e!==Gt)return!1;if(!qe)return _m(e),qe=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||pd(e.type,e.memoizedProps)),n=!n),n&&st){for(n=st;n;){var a=wl(e,0),l=Pg(n);a.serverTail.push(l),n=l.type==="Suspense"?Fg(n):Sn(n.nextSibling)}xl(e)}if(_m(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");st=Fg(e)}else t===27?(t=st,Ka(e.type)?(e=Qh,Qh=null,st=e):st=t):st=Gt?Sn(e.stateNode.nextSibling):null;return!0}function ji(){st=Gt=null,wa=qe=!1}function zm(){var e=Yl;return e!==null&&(Zt===null?Zt=e:Zt.push.apply(Zt,e),Yl=null),e}function Ni(e){Yl===null?Yl=[e]:Yl.push(e)}function Um(){var e=Cn;if(e!==null){Cn=null;for(var t=_s(e);0<e.children.length;)e=e.children[0];re(e.fiber,function(){console.error(`A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:

- A server/client branch \`if (typeof window !== 'undefined')\`.
- Variable input such as \`Date.now()\` or \`Math.random()\` which changes each time it's called.
- Date formatting in a user's locale which doesn't match the server.
- External changing data without sending a snapshot of it along with the HTML.
- Invalid HTML tag nesting.

It can also happen if the client has a browser extension installed which messes with the HTML before React loaded.

%s%s`,"https://react.dev/link/hydration-mismatch",t)})}}function vr(){Qo=Sc=null,Zo=!1}function $a(e,t,n){we(ih,t._currentValue,e),t._currentValue=n,we(uh,t._currentRenderer,e),t._currentRenderer!==void 0&&t._currentRenderer!==null&&t._currentRenderer!==v0&&console.error("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."),t._currentRenderer=v0}function pa(e,t){e._currentValue=ih.current;var n=uh.current;de(uh,t),e._currentRenderer=n,de(ih,t)}function Qs(e,t,n){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}e!==n&&console.error("Expected to find the propagation root when scheduling context work. This error is likely caused by a bug in React. Please file an issue.")}function Zs(e,t,n,a){var l=e.child;for(l!==null&&(l.return=e);l!==null;){var o=l.dependencies;if(o!==null){var u=l.child;o=o.firstContext;e:for(;o!==null;){var r=o;o=l;for(var d=0;d<t.length;d++)if(r.context===t[d]){o.lanes|=n,r=o.alternate,r!==null&&(r.lanes|=n),Qs(o.return,n,e),a||(u=null);break e}o=r.next}}else if(l.tag===18){if(u=l.return,u===null)throw Error("We just came from a parent so we must have had a parent. This is a bug in React.");u.lanes|=n,o=u.alternate,o!==null&&(o.lanes|=n),Qs(u,n,e),u=null}else u=l.child;if(u!==null)u.return=l;else for(u=l;u!==null;){if(u===e){u=null;break}if(l=u.sibling,l!==null){l.return=u.return,u=l;break}u=u.return}l=u}}function ki(e,t,n,a){e=null;for(var l=t,o=!1;l!==null;){if(!o){if((l.flags&524288)!==0)o=!0;else if((l.flags&262144)!==0)break}if(l.tag===10){var u=l.alternate;if(u===null)throw Error("Should have a current fiber. This is a bug in React.");if(u=u.memoizedProps,u!==null){var r=l.type;Vt(l.pendingProps.value,u.value)||(e!==null?e.push(r):e=[r])}}else if(l===rc.current){if(u=l.alternate,u===null)throw Error("Should have a current fiber. This is a bug in React.");u.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(e!==null?e.push(Qu):e=[Qu])}l=l.return}e!==null&&Zs(t,e,n,a),t.flags|=262144}function br(e){for(e=e.firstContext;e!==null;){if(!Vt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Dl(e){Sc=e,Qo=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ut(e){return Zo&&console.error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo()."),Hm(Sc,e)}function Sr(e,t){return Sc===null&&Dl(e),Hm(e,t)}function Hm(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},Qo===null){if(e===null)throw Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");Qo=t,e.dependencies={lanes:0,firstContext:t,_debugThenableState:null},e.flags|=524288}else Qo=Qo.next=t;return n}function Is(){return{controller:new hR,data:new Map,refCount:0}}function Ml(e){e.controller.signal.aborted&&console.warn("A cache instance was retained after it was already freed. This likely indicates a bug in React."),e.refCount++}function Li(e){e.refCount--,0>e.refCount&&console.warn("A cache instance was released after it was already freed. This likely indicates a bug in React."),e.refCount===0&&pR(mR,function(){e.controller.abort()})}function ma(){var e=ql;return ql=0,e}function Tr(e){var t=ql;return ql=e,t}function Bi(e){var t=ql;return ql+=e,t}function Js(e){Ft=Io(),0>e.actualStartTime&&(e.actualStartTime=Ft)}function Ks(e){if(0<=Ft){var t=Io()-Ft;e.actualDuration+=t,e.selfBaseDuration=t,Ft=-1}}function jm(e){if(0<=Ft){var t=Io()-Ft;e.actualDuration+=t,Ft=-1}}function Qn(){if(0<=Ft){var e=Io()-Ft;Ft=-1,ql+=e}}function Zn(){Ft=Io()}function Er(e){for(var t=e.child;t;)e.actualDuration+=t.actualDuration,t=t.sibling}function VS(e,t){if(bu===null){var n=bu=[];rh=0,$l=od(),Jo={status:"pending",value:void 0,then:function(a){n.push(a)}}}return rh++,t.then(Nm,Nm),t}function Nm(){if(--rh===0&&bu!==null){Jo!==null&&(Jo.status="fulfilled");var e=bu;bu=null,$l=0,Jo=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function GS(e,t){var n=[],a={status:"pending",value:null,reason:null,then:function(l){n.push(l)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var l=0;l<n.length;l++)(0,n[l])(t)},function(l){for(a.status="rejected",a.reason=l,l=0;l<n.length;l++)(0,n[l])(void 0)}),a}function Ps(){var e=Vl.current;return e!==null?e:tt.pooledCache}function Rr(e,t){t===null?we(Vl,Vl.current,e):we(Vl,t.pool,e)}function km(){var e=Ps();return e===null?null:{parent:Et._currentValue,pool:e}}function Lm(){return{didWarnAboutUncachedPromise:!1,thenables:[]}}function Bm(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Ar(){}function Ym(e,t,n){x.actQueue!==null&&(x.didUsePromise=!0);var a=e.thenables;switch(n=a[n],n===void 0?a.push(t):n!==t&&(e.didWarnAboutUncachedPromise||(e.didWarnAboutUncachedPromise=!0,console.error("A component was suspended by an uncached promise. Creating promises inside a Client Component or hook is not yet supported, except via a Suspense-compatible library or framework.")),t.then(Ar,Ar),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,$m(e),e;default:if(typeof t.status=="string")t.then(Ar,Ar);else{if(e=tt,e!==null&&100<e.shellSuspendCounter)throw Error("An unknown Component is an async Client Component. Only Server Components can be async at the moment. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server.");e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var o=t;o.status="fulfilled",o.value=l}},function(l){if(t.status==="pending"){var o=t;o.status="rejected",o.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,$m(e),e}throw wu=t,Cc=!0,Cu}}function qm(){if(wu===null)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var e=wu;return wu=null,Cc=!1,e}function $m(e){if(e===Cu||e===Oc)throw Error("Hooks are not supported inside an async component. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server.")}function Ws(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Fs(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Va(e){return{lane:e,tag:R0,payload:null,callback:null,next:null}}function Ga(e,t,n){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,fh===a&&!C0){var l=X(e);console.error(`An update (setState, replaceState, or forceUpdate) was scheduled from inside an update function. Update functions should be pure, with zero side-effects. Consider using componentDidUpdate or a callback.

Please update the following component: %s`,l),C0=!0}return(Ge&Qt)!==dn?(l=a.pending,l===null?t.next=t:(t.next=l.next,l.next=t),a.pending=t,t=yr(e),Om(e,null,n),t):(mr(e,a,t,n),yr(e))}function Yi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,or(e,n)}}function Or(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var u={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};o===null?l=o=u:o=o.next=u,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:a.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:a.shared,callbacks:a.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function qi(){if(dh){var e=Jo;if(e!==null)throw e}}function $i(e,t,n,a){dh=!1;var l=e.updateQueue;ll=!1,fh=l.shared;var o=l.firstBaseUpdate,u=l.lastBaseUpdate,r=l.shared.pending;if(r!==null){l.shared.pending=null;var d=r,p=d.next;d.next=null,u===null?o=p:u.next=p,u=d;var O=e.alternate;O!==null&&(O=O.updateQueue,r=O.lastBaseUpdate,r!==u&&(r===null?O.firstBaseUpdate=p:r.next=p,O.lastBaseUpdate=d))}if(o!==null){var D=l.baseState;u=0,O=p=d=null,r=o;do{var A=r.lane&-536870913,_=A!==r.lane;if(_?(ke&A)===A:(a&A)===A){A!==0&&A===$l&&(dh=!0),O!==null&&(O=O.next={lane:0,tag:r.tag,payload:r.payload,callback:null,next:null});e:{A=e;var ee=r,ge=t,nt=n;switch(ee.tag){case A0:if(ee=ee.payload,typeof ee=="function"){Zo=!0;var Be=ee.call(nt,D,ge);if(A.mode&Bt){Q(!0);try{ee.call(nt,D,ge)}finally{Q(!1)}}Zo=!1,D=Be;break e}D=ee;break e;case sh:A.flags=A.flags&-65537|128;case R0:if(Be=ee.payload,typeof Be=="function"){if(Zo=!0,ee=Be.call(nt,D,ge),A.mode&Bt){Q(!0);try{Be.call(nt,D,ge)}finally{Q(!1)}}Zo=!1}else ee=Be;if(ee==null)break e;D=_e({},D,ee);break e;case O0:ll=!0}}A=r.callback,A!==null&&(e.flags|=64,_&&(e.flags|=8192),_=l.callbacks,_===null?l.callbacks=[A]:_.push(A))}else _={lane:A,tag:r.tag,payload:r.payload,callback:r.callback,next:null},O===null?(p=O=_,d=D):O=O.next=_,u|=A;if(r=r.next,r===null){if(r=l.shared.pending,r===null)break;_=r,r=_.next,_.next=null,l.lastBaseUpdate=_,l.shared.pending=null}}while(!0);O===null&&(d=D),l.baseState=d,l.firstBaseUpdate=p,l.lastBaseUpdate=O,o===null&&(l.shared.lanes=0),rl|=u,e.lanes=u,e.memoizedState=D}fh=null}function Vm(e,t){if(typeof e!="function")throw Error("Invalid argument passed as callback. Expected a function. Instead received: "+e);e.call(t)}function XS(e,t){var n=e.shared.hiddenCallbacks;if(n!==null)for(e.shared.hiddenCallbacks=null,e=0;e<n.length;e++)Vm(n[e],t)}function Gm(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Vm(n[e],t)}function Xm(e,t){var n=ra;we(wc,n,e),we(Ko,t,e),ra=n|t.baseLanes}function ef(e){we(wc,ra,e),we(Ko,Ko.current,e)}function tf(e){ra=wc.current,de(Ko,e),de(wc,e)}function Ne(){var e=w;Dn===null?Dn=[e]:Dn.push(e)}function k(){var e=w;if(Dn!==null&&(Da++,Dn[Da]!==e)){var t=X(Te);if(!w0.has(t)&&(w0.add(t),Dn!==null)){for(var n="",a=0;a<=Da;a++){var l=Dn[a],o=a===Da?e:l;for(l=a+1+". "+l;30>l.length;)l+=" ";l+=o+`
`,n+=l}console.error(`React has detected a change in the order of Hooks called by %s. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://react.dev/link/rules-of-hooks

   Previous render            Next render
   ------------------------------------------------------
%s   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
`,t,n)}}}function go(e){e==null||Ut(e)||console.error("%s received a final argument that is not an array (instead, received `%s`). When specified, the final argument must be an array.",w,typeof e)}function Cr(){var e=X(Te);D0.has(e)||(D0.add(e),console.error("ReactDOM.useFormState has been renamed to React.useActionState. Please update %s to use React.useActionState.",e))}function dt(){throw Error(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem.`)}function nf(e,t){if(Du)return!1;if(t===null)return console.error("%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.",w),!1;e.length!==t.length&&console.error(`The final argument passed to %s changed size between renders. The order and size of this array must remain constant.

Previous: %s
Incoming: %s`,w,"["+t.join(", ")+"]","["+e.join(", ")+"]");for(var n=0;n<t.length&&n<e.length;n++)if(!Vt(e[n],t[n]))return!1;return!0}function af(e,t,n,a,l,o){ol=o,Te=t,Dn=e!==null?e._debugHookTypes:null,Da=-1,Du=e!==null&&e.type!==t.type,(Object.prototype.toString.call(n)==="[object AsyncFunction]"||Object.prototype.toString.call(n)==="[object AsyncGeneratorFunction]")&&(o=X(Te),hh.has(o)||(hh.add(o),console.error("%s is an async Client Component. Only Server Components can be async at the moment. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server.",o===null?"An unknown Component":"<"+o+">"))),t.memoizedState=null,t.updateQueue=null,t.lanes=0,x.H=e!==null&&e.memoizedState!==null?mh:Dn!==null?M0:ph,Xl=o=(t.mode&Bt)!==lt;var u=yh(n,a,l);if(Xl=!1,Wo&&(u=lf(t,n,a,l)),o){Q(!0);try{u=lf(t,n,a,l)}finally{Q(!1)}}return Qm(e,t),u}function Qm(e,t){t._debugHookTypes=Dn,t.dependencies===null?xa!==null&&(t.dependencies={lanes:0,firstContext:null,_debugThenableState:xa}):t.dependencies._debugThenableState=xa,x.H=Mc;var n=We!==null&&We.next!==null;if(ol=0,Dn=w=vt=We=Te=null,Da=-1,e!==null&&(e.flags&65011712)!==(t.flags&65011712)&&console.error("Internal React error: Expected static flag was missing. Please notify the React team."),xc=!1,xu=0,xa=null,n)throw Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");e===null||wt||(e=e.dependencies,e!==null&&br(e)&&(wt=!0)),Cc?(Cc=!1,e=!0):e=!1,e&&(t=X(t)||"Unknown",x0.has(t)||hh.has(t)||(x0.add(t),console.error("`use` was called from inside a try/catch block. This is not allowed and can lead to unexpected behavior. To handle errors triggered by `use`, wrap your component in a error boundary.")))}function lf(e,t,n,a){Te=e;var l=0;do{if(Wo&&(xa=null),xu=0,Wo=!1,l>=gR)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(l+=1,Du=!1,vt=We=null,e.updateQueue!=null){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}Da=-1,x.H=_0,o=yh(t,n,a)}while(Wo);return o}function QS(){var e=x.H,t=e.useState()[0];return t=typeof t.then=="function"?Vi(t):t,e=e.useState()[0],(We!==null?We.memoizedState:null)!==e&&(Te.flags|=1024),t}function of(){var e=Dc!==0;return Dc=0,e}function uf(e,t,n){t.updateQueue=e.updateQueue,t.flags=(t.mode&Yn)!==lt?t.flags&-402655237:t.flags&-2053,e.lanes&=~n}function rf(e){if(xc){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}xc=!1}ol=0,Dn=vt=We=Te=null,Da=-1,w=null,Wo=!1,xu=Dc=0,xa=null}function Kt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return vt===null?Te.memoizedState=vt=e:vt=vt.next=e,vt}function Je(){if(We===null){var e=Te.alternate;e=e!==null?e.memoizedState:null}else e=We.next;var t=vt===null?Te.memoizedState:vt.next;if(t!==null)vt=t,We=e;else{if(e===null)throw Te.alternate===null?Error("Update hook called on initial render. This is likely a bug in React. Please file an issue."):Error("Rendered more hooks than during the previous render.");We=e,e={memoizedState:We.memoizedState,baseState:We.baseState,baseQueue:We.baseQueue,queue:We.queue,next:null},vt===null?Te.memoizedState=vt=e:vt=vt.next=e}return vt}function cf(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Vi(e){var t=xu;return xu+=1,xa===null&&(xa=Lm()),e=Ym(xa,e,t),t=Te,(vt===null?t.memoizedState:vt.next)===null&&(t=t.alternate,x.H=t!==null&&t.memoizedState!==null?mh:ph),e}function Xa(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Vi(e);if(e.$$typeof===Fn)return ut(e)}throw Error("An unsupported type was passed to use(): "+String(e))}function _l(e){var t=null,n=Te.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var a=Te.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(l){return l.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=cf(),Te.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0||Du)for(n=t.data[t.index]=Array(e),a=0;a<e;a++)n[a]=aE;else n.length!==e&&console.error("Expected a constant size argument for each invocation of useMemoCache. The previous cache was allocated with size %s but size %s was requested.",n.length,e);return t.index++,n}function Ln(e,t){return typeof t=="function"?t(e):t}function sf(e,t,n){var a=Kt();if(n!==void 0){var l=n(t);if(Xl){Q(!0);try{n(t)}finally{Q(!1)}}}else l=t;return a.memoizedState=a.baseState=l,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:l},a.queue=e,e=e.dispatch=KS.bind(null,Te,e),[a.memoizedState,e]}function vo(e){var t=Je();return ff(t,We,e)}function ff(e,t,n){var a=e.queue;if(a===null)throw Error("Should have a queue. You are likely calling Hooks conditionally, which is not allowed. (https://react.dev/link/invalid-hook-call)");a.lastRenderedReducer=n;var l=e.baseQueue,o=a.pending;if(o!==null){if(l!==null){var u=l.next;l.next=o.next,o.next=u}t.baseQueue!==l&&console.error("Internal error: Expected work-in-progress queue to be a clone. This is a bug in React."),t.baseQueue=l=o,a.pending=null}if(o=e.baseState,l===null)e.memoizedState=o;else{t=l.next;var r=u=null,d=null,p=t,O=!1;do{var D=p.lane&-536870913;if(D!==p.lane?(ke&D)===D:(ol&D)===D){var A=p.revertLane;if(A===0)d!==null&&(d=d.next={lane:0,revertLane:0,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null}),D===$l&&(O=!0);else if((ol&A)===A){p=p.next,A===$l&&(O=!0);continue}else D={lane:0,revertLane:p.revertLane,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null},d===null?(r=d=D,u=o):d=d.next=D,Te.lanes|=A,rl|=A;D=p.action,Xl&&n(o,D),o=p.hasEagerState?p.eagerState:n(o,D)}else A={lane:D,revertLane:p.revertLane,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null},d===null?(r=d=A,u=o):d=d.next=A,Te.lanes|=D,rl|=D;p=p.next}while(p!==null&&p!==t);if(d===null?u=o:d.next=r,!Vt(o,e.memoizedState)&&(wt=!0,O&&(n=Jo,n!==null)))throw n;e.memoizedState=o,e.baseState=u,e.baseQueue=d,a.lastRenderedState=o}return l===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Gi(e){var t=Je(),n=t.queue;if(n===null)throw Error("Should have a queue. You are likely calling Hooks conditionally, which is not allowed. (https://react.dev/link/invalid-hook-call)");n.lastRenderedReducer=e;var a=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var u=l=l.next;do o=e(o,u.action),u=u.next;while(u!==l);Vt(o,t.memoizedState)||(wt=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,a]}function df(e,t,n){var a=Te,l=Kt();if(qe){if(n===void 0)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");var o=n();Po||o===n()||(console.error("The result of getServerSnapshot should be cached to avoid an infinite loop"),Po=!0)}else{if(o=t(),Po||(n=t(),Vt(o,n)||(console.error("The result of getSnapshot should be cached to avoid an infinite loop"),Po=!0)),tt===null)throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");(ke&124)!==0||Zm(a,t,o)}return l.memoizedState=o,n={value:o,getSnapshot:t},l.queue=n,_r(Jm.bind(null,a,n,e),[e]),a.flags|=2048,So(xn|Rt,Mr(),Im.bind(null,a,n,o,t),null),o}function wr(e,t,n){var a=Te,l=Je(),o=qe;if(o){if(n===void 0)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");n=n()}else if(n=t(),!Po){var u=t();Vt(n,u)||(console.error("The result of getSnapshot should be cached to avoid an infinite loop"),Po=!0)}(u=!Vt((We||l).memoizedState,n))&&(l.memoizedState=n,wt=!0),l=l.queue;var r=Jm.bind(null,a,l,e);if(Pt(2048,Rt,r,[e]),l.getSnapshot!==t||u||vt!==null&&vt.memoizedState.tag&xn){if(a.flags|=2048,So(xn|Rt,Mr(),Im.bind(null,a,l,n,t),null),tt===null)throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");o||(ol&124)!==0||Zm(a,t,n)}return n}function Zm(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Te.updateQueue,t===null?(t=cf(),Te.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Im(e,t,n,a){t.value=n,t.getSnapshot=a,Km(t)&&Pm(e)}function Jm(e,t,n){return n(function(){Km(t)&&Pm(e)})}function Km(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Vt(e,n)}catch{return!0}}function Pm(e){var t=Jt(e,2);t!==null&&ht(t,e,2)}function hf(e){var t=Kt();if(typeof e=="function"){var n=e;if(e=n(),Xl){Q(!0);try{n()}finally{Q(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ln,lastRenderedState:e},t}function pf(e){e=hf(e);var t=e.queue,n=yy.bind(null,Te,t);return t.dispatch=n,[e.memoizedState,n]}function mf(e){var t=Kt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=xf.bind(null,Te,!0,n),n.dispatch=t,[e,t]}function Wm(e,t){var n=Je();return Fm(n,We,e,t)}function Fm(e,t,n,a){return e.baseState=n,ff(e,We,typeof a=="function"?a:Ln)}function ey(e,t){var n=Je();return We!==null?Fm(n,We,e,t):(n.baseState=e,[e,n.queue.dispatch])}function ZS(e,t,n,a,l){if(jr(e))throw Error("Cannot update form state while rendering.");if(e=t.action,e!==null){var o={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(u){o.listeners.push(u)}};x.T!==null?n(!0):o.isTransition=!1,a(o),n=t.pending,n===null?(o.next=t.pending=o,ty(t,o)):(o.next=n.next,t.pending=n.next=o)}}function ty(e,t){var n=t.action,a=t.payload,l=e.state;if(t.isTransition){var o=x.T,u={};x.T=u,x.T._updatedFibers=new Set;try{var r=n(l,a),d=x.S;d!==null&&d(u,r),ny(e,t,r)}catch(p){yf(e,t,p)}finally{x.T=o,o===null&&u._updatedFibers&&(e=u._updatedFibers.size,u._updatedFibers.clear(),10<e&&console.warn("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."))}}else try{u=n(l,a),ny(e,t,u)}catch(p){yf(e,t,p)}}function ny(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?(n.then(function(a){ay(e,t,a)},function(a){return yf(e,t,a)}),t.isTransition||console.error("An async function with useActionState was called outside of a transition. This is likely not what you intended (for example, isPending will not update correctly). Either call the returned function inside startTransition, or pass it to an `action` or `formAction` prop.")):ay(e,t,n)}function ay(e,t,n){t.status="fulfilled",t.value=n,ly(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,ty(e,n)))}function yf(e,t,n){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=n,ly(t),t=t.next;while(t!==a)}e.action=null}function ly(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function oy(e,t){return t}function bo(e,t){if(qe){var n=tt.formState;if(n!==null){e:{var a=Te;if(qe){if(st){t:{for(var l=st,o=oa;l.nodeType!==8;){if(!o){l=null;break t}if(l=Sn(l.nextSibling),l===null){l=null;break t}}o=l.data,l=o===$h||o===D1?l:null}if(l){st=Sn(l.nextSibling),a=l.data===$h;break e}}xl(a)}a=!1}a&&(t=n[0])}}return n=Kt(),n.memoizedState=n.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:oy,lastRenderedState:t},n.queue=a,n=yy.bind(null,Te,a),a.dispatch=n,a=hf(!1),o=xf.bind(null,Te,!1,a.queue),a=Kt(),l={state:t,dispatch:null,action:e,pending:null},a.queue=l,n=ZS.bind(null,Te,l,o,n),l.dispatch=n,a.memoizedState=e,[t,n,!1]}function xr(e){var t=Je();return iy(t,We,e)}function iy(e,t,n){if(t=ff(e,t,oy)[0],e=vo(Ln)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=Vi(t)}catch(u){throw u===Cu?Oc:u}else a=t;t=Je();var l=t.queue,o=l.dispatch;return n!==t.memoizedState&&(Te.flags|=2048,So(xn|Rt,Mr(),IS.bind(null,l,n),null)),[a,o,e]}function IS(e,t){e.action=t}function Dr(e){var t=Je(),n=We;if(n!==null)return iy(t,n,e);Je(),t=t.memoizedState,n=Je();var a=n.queue.dispatch;return n.memoizedState=e,[t,a,!1]}function So(e,t,n,a){return e={tag:e,create:n,deps:a,inst:t,next:null},t=Te.updateQueue,t===null&&(t=cf(),Te.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function Mr(){return{destroy:void 0,resource:void 0}}function gf(e){var t=Kt();return e={current:e},t.memoizedState=e}function zl(e,t,n,a){var l=Kt();a=a===void 0?null:a,Te.flags|=e,l.memoizedState=So(xn|t,Mr(),n,a)}function Pt(e,t,n,a){var l=Je();a=a===void 0?null:a;var o=l.memoizedState.inst;We!==null&&a!==null&&nf(a,We.memoizedState.deps)?l.memoizedState=So(t,o,n,a):(Te.flags|=e,l.memoizedState=So(xn|t,o,n,a))}function _r(e,t){(Te.mode&Yn)!==lt&&(Te.mode&h0)===lt?zl(276826112,Rt,e,t):zl(8390656,Rt,e,t)}function vf(e,t){var n=4194308;return(Te.mode&Yn)!==lt&&(n|=134217728),zl(n,jt,e,t)}function uy(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return t.hasOwnProperty("current")||console.error("Expected useImperativeHandle() first argument to either be a ref callback or React.createRef() object. Instead received: %s.","an object with keys {"+Object.keys(t).join(", ")+"}"),e=e(),t.current=e,function(){t.current=null}}function bf(e,t,n){typeof t!="function"&&console.error("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null"),n=n!=null?n.concat([e]):null;var a=4194308;(Te.mode&Yn)!==lt&&(a|=134217728),zl(a,jt,uy.bind(null,t,e),n)}function zr(e,t,n){typeof t!="function"&&console.error("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null"),n=n!=null?n.concat([e]):null,Pt(4,jt,uy.bind(null,t,e),n)}function Sf(e,t){return Kt().memoizedState=[e,t===void 0?null:t],e}function Ur(e,t){var n=Je();t=t===void 0?null:t;var a=n.memoizedState;return t!==null&&nf(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function Tf(e,t){var n=Kt();t=t===void 0?null:t;var a=e();if(Xl){Q(!0);try{e()}finally{Q(!1)}}return n.memoizedState=[a,t],a}function Hr(e,t){var n=Je();t=t===void 0?null:t;var a=n.memoizedState;if(t!==null&&nf(t,a[1]))return a[0];if(a=e(),Xl){Q(!0);try{e()}finally{Q(!1)}}return n.memoizedState=[a,t],a}function Ef(e,t){var n=Kt();return Rf(n,e,t)}function ry(e,t){var n=Je();return sy(n,We.memoizedState,e,t)}function cy(e,t){var n=Je();return We===null?Rf(n,e,t):sy(n,We.memoizedState,e,t)}function Rf(e,t,n){return n===void 0||(ol&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=fg(),Te.lanes|=e,rl|=e,n)}function sy(e,t,n,a){return Vt(n,t)?n:Ko.current!==null?(e=Rf(e,n,a),Vt(e,t)||(wt=!0),e):(ol&42)===0?(wt=!0,e.memoizedState=n):(e=fg(),Te.lanes|=e,rl|=e,t)}function fy(e,t,n,a,l){var o=Ve.p;Ve.p=o!==0&&o<na?o:na;var u=x.T,r={};x.T=r,xf(e,!1,t,n),r._updatedFibers=new Set;try{var d=l(),p=x.S;if(p!==null&&p(r,d),d!==null&&typeof d=="object"&&typeof d.then=="function"){var O=GS(d,a);Xi(e,t,O,cn(e))}else Xi(e,t,a,cn(e))}catch(D){Xi(e,t,{then:function(){},status:"rejected",reason:D},cn(e))}finally{Ve.p=o,x.T=u,u===null&&r._updatedFibers&&(e=r._updatedFibers.size,r._updatedFibers.clear(),10<e&&console.warn("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."))}}function Af(e,t,n,a){if(e.tag!==5)throw Error("Expected the form instance to be a HostComponent. This is a bug in React.");var l=dy(e).queue;fy(e,l,t,no,n===null?$:function(){return hy(e),n(a)})}function dy(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:no,baseState:no,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ln,lastRenderedState:no},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ln,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function hy(e){x.T===null&&console.error("requestFormReset was called outside a transition or action. To fix, move to an action, or wrap with startTransition.");var t=dy(e).next.queue;Xi(e,t,{},cn(e))}function Of(){var e=hf(!1);return e=fy.bind(null,Te,e.queue,!0,!1),Kt().memoizedState=e,[!1,e]}function py(){var e=vo(Ln)[0],t=Je().memoizedState;return[typeof e=="boolean"?e:Vi(e),t]}function my(){var e=Gi(Ln)[0],t=Je().memoizedState;return[typeof e=="boolean"?e:Vi(e),t]}function Ul(){return ut(Qu)}function Cf(){var e=Kt(),t=tt.identifierPrefix;if(qe){var n=Ca,a=Oa;n=(a&~(1<<32-qt(a)-1)).toString(32)+n,t="«"+t+"R"+n,n=Dc++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=yR++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t}function wf(){return Kt().memoizedState=JS.bind(null,Te)}function JS(e,t){for(var n=e.return;n!==null;){switch(n.tag){case 24:case 3:var a=cn(n);e=Va(a);var l=Ga(n,e,a);l!==null&&(ht(l,n,a),Yi(l,n,a)),n=Is(),t!=null&&l!==null&&console.error("The seed argument is not enabled outside experimental channels."),e.payload={cache:n};return}n=n.return}}function KS(e,t,n){var a=arguments;typeof a[3]=="function"&&console.error("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect()."),a=cn(e);var l={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};jr(e)?gy(t,l):(l=Ns(e,t,l,a),l!==null&&(ht(l,e,a),vy(l,t,a))),Tt(e,a)}function yy(e,t,n){var a=arguments;typeof a[3]=="function"&&console.error("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect()."),a=cn(e),Xi(e,t,n,a),Tt(e,a)}function Xi(e,t,n,a){var l={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(jr(e))gy(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null)){var u=x.H;x.H=$n;try{var r=t.lastRenderedState,d=o(r,n);if(l.hasEagerState=!0,l.eagerState=d,Vt(d,r))return mr(e,t,l,0),tt===null&&pr(),!1}catch{}finally{x.H=u}}if(n=Ns(e,t,l,a),n!==null)return ht(n,e,a),vy(n,t,a),!0}return!1}function xf(e,t,n,a){if(x.T===null&&$l===0&&console.error("An optimistic state update occurred outside a transition or action. To fix, move the update to an action, or wrap with startTransition."),a={lane:2,revertLane:od(),action:a,hasEagerState:!1,eagerState:null,next:null},jr(e)){if(t)throw Error("Cannot update optimistic state while rendering.");console.error("Cannot call startTransition while rendering.")}else t=Ns(e,n,a,2),t!==null&&ht(t,e,2);Tt(e,2)}function jr(e){var t=e.alternate;return e===Te||t!==null&&t===Te}function gy(e,t){Wo=xc=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function vy(e,t,n){if((n&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,or(e,n)}}function Mt(e){var t=xe;return e!=null&&(xe=t===null?e:t.concat(e)),t}function Nr(e,t,n){for(var a=Object.keys(e.props),l=0;l<a.length;l++){var o=a[l];if(o!=="children"&&o!=="key"){t===null&&(t=gr(e,n.mode,0),t._debugInfo=xe,t.return=n),re(t,function(u){console.error("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",u)},o);break}}}function kr(e){var t=Mu;return Mu+=1,Fo===null&&(Fo=Lm()),Ym(Fo,e,t)}function Qi(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Lr(e,t){throw t.$$typeof===tE?Error(`A React Element from an older version of React was rendered. This is not supported. It can happen if:
- Multiple copies of the "react" package is used.
- A library pre-bundled an old copy of "react" or "react/jsx-runtime".
- A compiler tries to "inline" JSX instead of using the runtime.`):(e=Object.prototype.toString.call(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead."))}function Br(e,t){var n=X(e)||"Component";Q0[n]||(Q0[n]=!0,t=t.displayName||t.name||"Component",e.tag===3?console.error(`Functions are not valid as a React child. This may happen if you return %s instead of <%s /> from render. Or maybe you meant to call this function rather than return it.
  root.render(%s)`,t,t,t):console.error(`Functions are not valid as a React child. This may happen if you return %s instead of <%s /> from render. Or maybe you meant to call this function rather than return it.
  <%s>{%s}</%s>`,t,t,n,t,n))}function Yr(e,t){var n=X(e)||"Component";Z0[n]||(Z0[n]=!0,t=String(t),e.tag===3?console.error(`Symbols are not valid as a React child.
  root.render(%s)`,t):console.error(`Symbols are not valid as a React child.
  <%s>%s</%s>`,n,t,n))}function by(e){function t(m,y){if(e){var v=m.deletions;v===null?(m.deletions=[y],m.flags|=16):v.push(y)}}function n(m,y){if(!e)return null;for(;y!==null;)t(m,y),y=y.sibling;return null}function a(m){for(var y=new Map;m!==null;)m.key!==null?y.set(m.key,m):y.set(m.index,m),m=m.sibling;return y}function l(m,y){return m=ha(m,y),m.index=0,m.sibling=null,m}function o(m,y,v){return m.index=v,e?(v=m.alternate,v!==null?(v=v.index,v<y?(m.flags|=67108866,y):v):(m.flags|=67108866,y)):(m.flags|=1048576,y)}function u(m){return e&&m.alternate===null&&(m.flags|=67108866),m}function r(m,y,v,z){return y===null||y.tag!==6?(y=qs(v,m.mode,z),y.return=m,y._debugOwner=m,y._debugTask=m._debugTask,y._debugInfo=xe,y):(y=l(y,v),y.return=m,y._debugInfo=xe,y)}function d(m,y,v,z){var q=v.type;return q===_o?(y=O(m,y,v.props.children,z,v.key),Nr(v,y,m),y):y!==null&&(y.elementType===q||Cm(y,v)||typeof q=="object"&&q!==null&&q.$$typeof===sn&&il(q)===y.type)?(y=l(y,v.props),Qi(y,v),y.return=m,y._debugOwner=v._owner,y._debugInfo=xe,y):(y=gr(v,m.mode,z),Qi(y,v),y.return=m,y._debugInfo=xe,y)}function p(m,y,v,z){return y===null||y.tag!==4||y.stateNode.containerInfo!==v.containerInfo||y.stateNode.implementation!==v.implementation?(y=$s(v,m.mode,z),y.return=m,y._debugInfo=xe,y):(y=l(y,v.children||[]),y.return=m,y._debugInfo=xe,y)}function O(m,y,v,z,q){return y===null||y.tag!==7?(y=Al(v,m.mode,z,q),y.return=m,y._debugOwner=m,y._debugTask=m._debugTask,y._debugInfo=xe,y):(y=l(y,v),y.return=m,y._debugInfo=xe,y)}function D(m,y,v){if(typeof y=="string"&&y!==""||typeof y=="number"||typeof y=="bigint")return y=qs(""+y,m.mode,v),y.return=m,y._debugOwner=m,y._debugTask=m._debugTask,y._debugInfo=xe,y;if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Pa:return v=gr(y,m.mode,v),Qi(v,y),v.return=m,m=Mt(y._debugInfo),v._debugInfo=xe,xe=m,v;case Mo:return y=$s(y,m.mode,v),y.return=m,y._debugInfo=xe,y;case sn:var z=Mt(y._debugInfo);return y=il(y),m=D(m,y,v),xe=z,m}if(Ut(y)||F(y))return v=Al(y,m.mode,v,null),v.return=m,v._debugOwner=m,v._debugTask=m._debugTask,m=Mt(y._debugInfo),v._debugInfo=xe,xe=m,v;if(typeof y.then=="function")return z=Mt(y._debugInfo),m=D(m,kr(y),v),xe=z,m;if(y.$$typeof===Fn)return D(m,Sr(m,y),v);Lr(m,y)}return typeof y=="function"&&Br(m,y),typeof y=="symbol"&&Yr(m,y),null}function A(m,y,v,z){var q=y!==null?y.key:null;if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return q!==null?null:r(m,y,""+v,z);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Pa:return v.key===q?(q=Mt(v._debugInfo),m=d(m,y,v,z),xe=q,m):null;case Mo:return v.key===q?p(m,y,v,z):null;case sn:return q=Mt(v._debugInfo),v=il(v),m=A(m,y,v,z),xe=q,m}if(Ut(v)||F(v))return q!==null?null:(q=Mt(v._debugInfo),m=O(m,y,v,z,null),xe=q,m);if(typeof v.then=="function")return q=Mt(v._debugInfo),m=A(m,y,kr(v),z),xe=q,m;if(v.$$typeof===Fn)return A(m,y,Sr(m,v),z);Lr(m,v)}return typeof v=="function"&&Br(m,v),typeof v=="symbol"&&Yr(m,v),null}function _(m,y,v,z,q){if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return m=m.get(v)||null,r(y,m,""+z,q);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case Pa:return v=m.get(z.key===null?v:z.key)||null,m=Mt(z._debugInfo),y=d(y,v,z,q),xe=m,y;case Mo:return m=m.get(z.key===null?v:z.key)||null,p(y,m,z,q);case sn:var Ee=Mt(z._debugInfo);return z=il(z),y=_(m,y,v,z,q),xe=Ee,y}if(Ut(z)||F(z))return v=m.get(v)||null,m=Mt(z._debugInfo),y=O(y,v,z,q,null),xe=m,y;if(typeof z.then=="function")return Ee=Mt(z._debugInfo),y=_(m,y,v,kr(z),q),xe=Ee,y;if(z.$$typeof===Fn)return _(m,y,v,Sr(y,z),q);Lr(y,z)}return typeof z=="function"&&Br(y,z),typeof z=="symbol"&&Yr(y,z),null}function ee(m,y,v,z){if(typeof v!="object"||v===null)return z;switch(v.$$typeof){case Pa:case Mo:H(m,y,v);var q=v.key;if(typeof q!="string")break;if(z===null){z=new Set,z.add(q);break}if(!z.has(q)){z.add(q);break}re(y,function(){console.error("Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version.",q)});break;case sn:v=il(v),ee(m,y,v,z)}return z}function ge(m,y,v,z){for(var q=null,Ee=null,te=null,Re=y,Ae=y=0,ot=null;Re!==null&&Ae<v.length;Ae++){Re.index>Ae?(ot=Re,Re=null):ot=Re.sibling;var mt=A(m,Re,v[Ae],z);if(mt===null){Re===null&&(Re=ot);break}q=ee(m,mt,v[Ae],q),e&&Re&&mt.alternate===null&&t(m,Re),y=o(mt,y,Ae),te===null?Ee=mt:te.sibling=mt,te=mt,Re=ot}if(Ae===v.length)return n(m,Re),qe&&Ol(m,Ae),Ee;if(Re===null){for(;Ae<v.length;Ae++)Re=D(m,v[Ae],z),Re!==null&&(q=ee(m,Re,v[Ae],q),y=o(Re,y,Ae),te===null?Ee=Re:te.sibling=Re,te=Re);return qe&&Ol(m,Ae),Ee}for(Re=a(Re);Ae<v.length;Ae++)ot=_(Re,m,Ae,v[Ae],z),ot!==null&&(q=ee(m,ot,v[Ae],q),e&&ot.alternate!==null&&Re.delete(ot.key===null?Ae:ot.key),y=o(ot,y,Ae),te===null?Ee=ot:te.sibling=ot,te=ot);return e&&Re.forEach(function(ja){return t(m,ja)}),qe&&Ol(m,Ae),Ee}function nt(m,y,v,z){if(v==null)throw Error("An iterable object provided no iterator.");for(var q=null,Ee=null,te=y,Re=y=0,Ae=null,ot=null,mt=v.next();te!==null&&!mt.done;Re++,mt=v.next()){te.index>Re?(Ae=te,te=null):Ae=te.sibling;var ja=A(m,te,mt.value,z);if(ja===null){te===null&&(te=Ae);break}ot=ee(m,ja,mt.value,ot),e&&te&&ja.alternate===null&&t(m,te),y=o(ja,y,Re),Ee===null?q=ja:Ee.sibling=ja,Ee=ja,te=Ae}if(mt.done)return n(m,te),qe&&Ol(m,Re),q;if(te===null){for(;!mt.done;Re++,mt=v.next())te=D(m,mt.value,z),te!==null&&(ot=ee(m,te,mt.value,ot),y=o(te,y,Re),Ee===null?q=te:Ee.sibling=te,Ee=te);return qe&&Ol(m,Re),q}for(te=a(te);!mt.done;Re++,mt=v.next())Ae=_(te,m,Re,mt.value,z),Ae!==null&&(ot=ee(m,Ae,mt.value,ot),e&&Ae.alternate!==null&&te.delete(Ae.key===null?Re:Ae.key),y=o(Ae,y,Re),Ee===null?q=Ae:Ee.sibling=Ae,Ee=Ae);return e&&te.forEach(function(VR){return t(m,VR)}),qe&&Ol(m,Re),q}function Be(m,y,v,z){if(typeof v=="object"&&v!==null&&v.type===_o&&v.key===null&&(Nr(v,null,m),v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Pa:var q=Mt(v._debugInfo);e:{for(var Ee=v.key;y!==null;){if(y.key===Ee){if(Ee=v.type,Ee===_o){if(y.tag===7){n(m,y.sibling),z=l(y,v.props.children),z.return=m,z._debugOwner=v._owner,z._debugInfo=xe,Nr(v,z,m),m=z;break e}}else if(y.elementType===Ee||Cm(y,v)||typeof Ee=="object"&&Ee!==null&&Ee.$$typeof===sn&&il(Ee)===y.type){n(m,y.sibling),z=l(y,v.props),Qi(z,v),z.return=m,z._debugOwner=v._owner,z._debugInfo=xe,m=z;break e}n(m,y);break}else t(m,y);y=y.sibling}v.type===_o?(z=Al(v.props.children,m.mode,z,v.key),z.return=m,z._debugOwner=m,z._debugTask=m._debugTask,z._debugInfo=xe,Nr(v,z,m),m=z):(z=gr(v,m.mode,z),Qi(z,v),z.return=m,z._debugInfo=xe,m=z)}return m=u(m),xe=q,m;case Mo:e:{for(q=v,v=q.key;y!==null;){if(y.key===v)if(y.tag===4&&y.stateNode.containerInfo===q.containerInfo&&y.stateNode.implementation===q.implementation){n(m,y.sibling),z=l(y,q.children||[]),z.return=m,m=z;break e}else{n(m,y);break}else t(m,y);y=y.sibling}z=$s(q,m.mode,z),z.return=m,m=z}return u(m);case sn:return q=Mt(v._debugInfo),v=il(v),m=Be(m,y,v,z),xe=q,m}if(Ut(v))return q=Mt(v._debugInfo),m=ge(m,y,v,z),xe=q,m;if(F(v)){if(q=Mt(v._debugInfo),Ee=F(v),typeof Ee!="function")throw Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");var te=Ee.call(v);return te===v?(m.tag!==0||Object.prototype.toString.call(m.type)!=="[object GeneratorFunction]"||Object.prototype.toString.call(te)!=="[object Generator]")&&(G0||console.error("Using Iterators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()` or the `[...spread]` operator before rendering. You can also use an Iterable that can iterate multiple times over the same items."),G0=!0):v.entries!==Ee||vh||(console.error("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),vh=!0),m=nt(m,y,te,z),xe=q,m}if(typeof v.then=="function")return q=Mt(v._debugInfo),m=Be(m,y,kr(v),z),xe=q,m;if(v.$$typeof===Fn)return Be(m,y,Sr(m,v),z);Lr(m,v)}return typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint"?(q=""+v,y!==null&&y.tag===6?(n(m,y.sibling),z=l(y,q),z.return=m,m=z):(n(m,y),z=qs(q,m.mode,z),z.return=m,z._debugOwner=m,z._debugTask=m._debugTask,z._debugInfo=xe,m=z),u(m)):(typeof v=="function"&&Br(m,v),typeof v=="symbol"&&Yr(m,v),n(m,y))}return function(m,y,v,z){var q=xe;xe=null;try{Mu=0;var Ee=Be(m,y,v,z);return Fo=null,Ee}catch(ot){if(ot===Cu||ot===Oc)throw ot;var te=S(29,ot,null,m.mode);te.lanes=z,te.return=m;var Re=te._debugInfo=xe;if(te._debugOwner=m._debugOwner,te._debugTask=m._debugTask,Re!=null){for(var Ae=Re.length-1;0<=Ae;Ae--)if(typeof Re[Ae].stack=="string"){te._debugOwner=Re[Ae],te._debugTask=Re[Ae].debugTask;break}}return te}finally{xe=q}}}function Qa(e){var t=e.alternate;we(At,At.current&ti,e),we(Mn,e,e),ua===null&&(t===null||Ko.current!==null||t.memoizedState!==null)&&(ua=e)}function Sy(e){if(e.tag===22){if(we(At,At.current,e),we(Mn,e,e),ua===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(ua=e)}}else Za(e)}function Za(e){we(At,At.current,e),we(Mn,Mn.current,e)}function ya(e){de(Mn,e),ua===e&&(ua=null),de(At,e)}function qr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data===za||yd(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Df(e){if(e!==null&&typeof e!="function"){var t=String(e);o1.has(t)||(o1.add(t),console.error("Expected the last optional `callback` argument to be a function. Instead received: %s.",e))}}function Mf(e,t,n,a){var l=e.memoizedState,o=n(a,l);if(e.mode&Bt){Q(!0);try{o=n(a,l)}finally{Q(!1)}}o===void 0&&(t=G(t)||"Component",t1.has(t)||(t1.add(t),console.error("%s.getDerivedStateFromProps(): A valid state object (or null) must be returned. You have returned undefined.",t))),l=o==null?l:_e({},l,o),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}function Ty(e,t,n,a,l,o,u){var r=e.stateNode;if(typeof r.shouldComponentUpdate=="function"){if(n=r.shouldComponentUpdate(a,o,u),e.mode&Bt){Q(!0);try{n=r.shouldComponentUpdate(a,o,u)}finally{Q(!1)}}return n===void 0&&console.error("%s.shouldComponentUpdate(): Returned undefined instead of a boolean value. Make sure to return true or false.",G(t)||"Component"),n}return t.prototype&&t.prototype.isPureReactComponent?!Ui(n,a)||!Ui(l,o):!0}function Ey(e,t,n,a){var l=t.state;typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==l&&(e=X(e)||"Component",K0.has(e)||(K0.add(e),console.error("%s.componentWillReceiveProps(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",e)),bh.enqueueReplaceState(t,t.state,null))}function Hl(e,t){var n=t;if("ref"in t){n={};for(var a in t)a!=="ref"&&(n[a]=t[a])}if(e=e.defaultProps){n===t&&(n=_e({},n));for(var l in e)n[l]===void 0&&(n[l]=e[l])}return n}function Ry(e){Sh(e),console.warn(`%s

%s
`,ni?"An error occurred in the <"+ni+"> component.":"An error occurred in one of your React components.",`Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries.`)}function Ay(e){var t=ni?"The above error occurred in the <"+ni+"> component.":"The above error occurred in one of your React components.",n="React will try to recreate this component tree from scratch using the error boundary you provided, "+((Th||"Anonymous")+".");if(typeof e=="object"&&e!==null&&typeof e.environmentName=="string"){var a=e.environmentName;e=[`%o

%s

%s
`,e,t,n].slice(0),typeof e[0]=="string"?e.splice(0,1,k1+e[0],L1,Jc+a+Jc,B1):e.splice(0,0,k1,L1,Jc+a+Jc,B1),e.unshift(console),a=qR.apply(console.error,e),a()}else console.error(`%o

%s

%s
`,e,t,n)}function Oy(e){Sh(e)}function $r(e,t){try{ni=t.source?X(t.source):null,Th=null;var n=t.value;if(x.actQueue!==null)x.thrownErrors.push(n);else{var a=e.onUncaughtError;a(n,{componentStack:t.stack})}}catch(l){setTimeout(function(){throw l})}}function Cy(e,t,n){try{ni=n.source?X(n.source):null,Th=X(t);var a=e.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(l){setTimeout(function(){throw l})}}function _f(e,t,n){return n=Va(n),n.tag=sh,n.payload={element:null},n.callback=function(){re(t.source,$r,e,t)},n}function zf(e){return e=Va(e),e.tag=sh,e}function Uf(e,t,n,a){var l=n.type.getDerivedStateFromError;if(typeof l=="function"){var o=a.value;e.payload=function(){return l(o)},e.callback=function(){wm(n),re(a.source,Cy,t,n,a)}}var u=n.stateNode;u!==null&&typeof u.componentDidCatch=="function"&&(e.callback=function(){wm(n),re(a.source,Cy,t,n,a),typeof l!="function"&&(sl===null?sl=new Set([this]):sl.add(this)),vR(this,a),typeof l=="function"||(n.lanes&2)===0&&console.error("%s: Error boundaries should implement getDerivedStateFromError(). In that method, return a state update to display an error message or fallback UI.",X(n)||"Unknown")})}function PS(e,t,n,a,l){if(n.flags|=32768,Bn&&Fi(e,l),a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=n.alternate,t!==null&&ki(t,n,l,!0),qe&&(wa=!0),n=Mn.current,n!==null){switch(n.tag){case 13:return ua===null?Wf():n.alternate===null&&ft===_a&&(ft=Oh),n.flags&=-257,n.flags|=65536,n.lanes=l,a===ch?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([a]):t.add(a),td(e,a,l)),!1;case 22:return n.flags|=65536,a===ch?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([a]):n.add(a)),td(e,a,l)),!1}throw Error("Unexpected Suspense handler tag ("+n.tag+"). This is a bug in React.")}return td(e,a,l),Wf(),!1}if(qe)return wa=!0,t=Mn.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=l,a!==oh&&Ni(on(Error("There was an error while hydrating but React was able to recover by instead client rendering from the nearest Suspense boundary.",{cause:a}),n))):(a!==oh&&Ni(on(Error("There was an error while hydrating but React was able to recover by instead client rendering the entire root.",{cause:a}),n)),e=e.current.alternate,e.flags|=65536,l&=-l,e.lanes|=l,a=on(a,n),l=_f(e.stateNode,a,l),Or(e,l),ft!==Ql&&(ft=ii)),!1;var o=on(Error("There was an error during concurrent rendering but React was able to recover by instead synchronously rendering the entire root.",{cause:a}),n);if(Lu===null?Lu=[o]:Lu.push(o),ft!==Ql&&(ft=ii),t===null)return!0;a=on(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=l&-l,n.lanes|=e,e=_f(n.stateNode,a,e),Or(n,e),!1;case 1:if(t=n.type,o=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(sl===null||!sl.has(o))))return n.flags|=65536,l&=-l,n.lanes|=l,l=zf(l),Uf(l,e,n,a),Or(n,l),!1}n=n.return}while(n!==null);return!1}function _t(e,t,n,a){t.child=e===null?I0(t,null,n,a):ei(t,e.child,n,a)}function wy(e,t,n,a,l){n=n.render;var o=t.ref;if("ref"in a){var u={};for(var r in a)r!=="ref"&&(u[r]=a[r])}else u=a;return Dl(t),he(t),a=af(e,t,n,u,o,l),r=of(),ye(),e!==null&&!wt?(uf(e,t,l),ga(e,t,l)):(qe&&r&&Vs(t),t.flags|=1,_t(e,t,a,l),t.child)}function xy(e,t,n,a,l){if(e===null){var o=n.type;return typeof o=="function"&&!Bs(o)&&o.defaultProps===void 0&&n.compare===null?(n=Rl(o),t.tag=15,t.type=n,jf(t,o),Dy(e,t,n,a,l)):(e=Ys(n.type,null,a,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!qf(e,l)){var u=o.memoizedProps;if(n=n.compare,n=n!==null?n:Ui,n(u,a)&&e.ref===t.ref)return ga(e,t,l)}return t.flags|=1,e=ha(o,a),e.ref=t.ref,e.return=t,t.child=e}function Dy(e,t,n,a,l){if(e!==null){var o=e.memoizedProps;if(Ui(o,a)&&e.ref===t.ref&&t.type===e.type)if(wt=!1,t.pendingProps=a=o,qf(e,l))(e.flags&131072)!==0&&(wt=!0);else return t.lanes=e.lanes,ga(e,t,l)}return Hf(e,t,n,a,l)}function My(e,t,n){var a=t.pendingProps,l=a.children,o=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=o!==null?o.baseLanes|n:n,e!==null){for(l=t.child=e.child,o=0;l!==null;)o=o|l.lanes|l.childLanes,l=l.sibling;t.childLanes=o&~a}else t.childLanes=0,t.child=null;return _y(e,t,a,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Rr(t,o!==null?o.cachePool:null),o!==null?Xm(t,o):ef(t),Sy(t);else return t.lanes=t.childLanes=536870912,_y(e,t,o!==null?o.baseLanes|n:n,n)}else o!==null?(Rr(t,o.cachePool),Xm(t,o),Za(t),t.memoizedState=null):(e!==null&&Rr(t,null),ef(t),Za(t));return _t(e,t,l,n),t.child}function _y(e,t,n,a){var l=Ps();return l=l===null?null:{parent:Et._currentValue,pool:l},t.memoizedState={baseLanes:n,cachePool:l},e!==null&&Rr(t,null),ef(t),Sy(t),e!==null&&ki(e,t,a,!0),null}function Vr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error("Expected ref to be a function, an object returned by React.createRef(), or undefined/null.");(e===null||e.ref!==n)&&(t.flags|=4194816)}}function Hf(e,t,n,a,l){if(n.prototype&&typeof n.prototype.render=="function"){var o=G(n)||"Unknown";u1[o]||(console.error("The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.",o,o),u1[o]=!0)}return t.mode&Bt&&qn.recordLegacyContextWarning(t,null),e===null&&(jf(t,t.type),n.contextTypes&&(o=G(n)||"Unknown",c1[o]||(c1[o]=!0,console.error("%s uses the legacy contextTypes API which was removed in React 19. Use React.createContext() with React.useContext() instead. (https://react.dev/link/legacy-context)",o)))),Dl(t),he(t),n=af(e,t,n,a,void 0,l),a=of(),ye(),e!==null&&!wt?(uf(e,t,l),ga(e,t,l)):(qe&&a&&Vs(t),t.flags|=1,_t(e,t,n,l),t.child)}function zy(e,t,n,a,l,o){return Dl(t),he(t),Da=-1,Du=e!==null&&e.type!==t.type,t.updateQueue=null,n=lf(t,a,n,l),Qm(e,t),a=of(),ye(),e!==null&&!wt?(uf(e,t,o),ga(e,t,o)):(qe&&a&&Vs(t),t.flags|=1,_t(e,t,n,o),t.child)}function Uy(e,t,n,a,l){switch(M(t)){case!1:var o=t.stateNode,u=new t.type(t.memoizedProps,o.context).state;o.updater.enqueueSetState(o,u,null);break;case!0:t.flags|=128,t.flags|=65536,o=Error("Simulated error coming from DevTools");var r=l&-l;if(t.lanes|=r,u=tt,u===null)throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");r=zf(r),Uf(r,u,t,on(o,t)),Or(t,r)}if(Dl(t),t.stateNode===null){if(u=al,o=n.contextType,"contextType"in n&&o!==null&&(o===void 0||o.$$typeof!==Fn)&&!l1.has(n)&&(l1.add(n),r=o===void 0?" However, it is set to undefined. This can be caused by a typo or by mixing up named and default imports. This can also happen due to a circular dependency, so try moving the createContext() call to a separate file.":typeof o!="object"?" However, it is set to a "+typeof o+".":o.$$typeof===xd?" Did you accidentally pass the Context.Consumer instead?":" However, it is set to an object with keys {"+Object.keys(o).join(", ")+"}.",console.error("%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext().%s",G(n)||"Component",r)),typeof o=="object"&&o!==null&&(u=ut(o)),o=new n(a,u),t.mode&Bt){Q(!0);try{o=new n(a,u)}finally{Q(!1)}}if(u=t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=bh,t.stateNode=o,o._reactInternals=t,o._reactInternalInstance=J0,typeof n.getDerivedStateFromProps=="function"&&u===null&&(u=G(n)||"Component",P0.has(u)||(P0.add(u),console.error("`%s` uses `getDerivedStateFromProps` but its initial state is %s. This is not recommended. Instead, define the initial state by assigning an object to `this.state` in the constructor of `%s`. This ensures that `getDerivedStateFromProps` arguments have a consistent shape.",u,o.state===null?"null":"undefined",u))),typeof n.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"){var d=r=u=null;if(typeof o.componentWillMount=="function"&&o.componentWillMount.__suppressDeprecationWarning!==!0?u="componentWillMount":typeof o.UNSAFE_componentWillMount=="function"&&(u="UNSAFE_componentWillMount"),typeof o.componentWillReceiveProps=="function"&&o.componentWillReceiveProps.__suppressDeprecationWarning!==!0?r="componentWillReceiveProps":typeof o.UNSAFE_componentWillReceiveProps=="function"&&(r="UNSAFE_componentWillReceiveProps"),typeof o.componentWillUpdate=="function"&&o.componentWillUpdate.__suppressDeprecationWarning!==!0?d="componentWillUpdate":typeof o.UNSAFE_componentWillUpdate=="function"&&(d="UNSAFE_componentWillUpdate"),u!==null||r!==null||d!==null){o=G(n)||"Component";var p=typeof n.getDerivedStateFromProps=="function"?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";F0.has(o)||(F0.add(o),console.error(`Unsafe legacy lifecycles will not be called for components using new component APIs.

%s uses %s but also contains the following legacy lifecycles:%s%s%s

The above lifecycles should be removed. Learn more about this warning here:
https://react.dev/link/unsafe-component-lifecycles`,o,p,u!==null?`
  `+u:"",r!==null?`
  `+r:"",d!==null?`
  `+d:""))}}o=t.stateNode,u=G(n)||"Component",o.render||(n.prototype&&typeof n.prototype.render=="function"?console.error("No `render` method found on the %s instance: did you accidentally return an object from the constructor?",u):console.error("No `render` method found on the %s instance: you may have forgotten to define `render`.",u)),!o.getInitialState||o.getInitialState.isReactClassApproved||o.state||console.error("getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?",u),o.getDefaultProps&&!o.getDefaultProps.isReactClassApproved&&console.error("getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.",u),o.contextType&&console.error("contextType was defined as an instance property on %s. Use a static property to define contextType instead.",u),n.childContextTypes&&!a1.has(n)&&(a1.add(n),console.error("%s uses the legacy childContextTypes API which was removed in React 19. Use React.createContext() instead. (https://react.dev/link/legacy-context)",u)),n.contextTypes&&!n1.has(n)&&(n1.add(n),console.error("%s uses the legacy contextTypes API which was removed in React 19. Use React.createContext() with static contextType instead. (https://react.dev/link/legacy-context)",u)),typeof o.componentShouldUpdate=="function"&&console.error("%s has a method called componentShouldUpdate(). Did you mean shouldComponentUpdate()? The name is phrased as a question because the function is expected to return a value.",u),n.prototype&&n.prototype.isPureReactComponent&&typeof o.shouldComponentUpdate<"u"&&console.error("%s has a method called shouldComponentUpdate(). shouldComponentUpdate should not be used when extending React.PureComponent. Please extend React.Component if shouldComponentUpdate is used.",G(n)||"A pure component"),typeof o.componentDidUnmount=="function"&&console.error("%s has a method called componentDidUnmount(). But there is no such lifecycle method. Did you mean componentWillUnmount()?",u),typeof o.componentDidReceiveProps=="function"&&console.error("%s has a method called componentDidReceiveProps(). But there is no such lifecycle method. If you meant to update the state in response to changing props, use componentWillReceiveProps(). If you meant to fetch data or run side-effects or mutations after React has updated the UI, use componentDidUpdate().",u),typeof o.componentWillRecieveProps=="function"&&console.error("%s has a method called componentWillRecieveProps(). Did you mean componentWillReceiveProps()?",u),typeof o.UNSAFE_componentWillRecieveProps=="function"&&console.error("%s has a method called UNSAFE_componentWillRecieveProps(). Did you mean UNSAFE_componentWillReceiveProps()?",u),r=o.props!==a,o.props!==void 0&&r&&console.error("When calling super() in `%s`, make sure to pass up the same props that your component's constructor was passed.",u),o.defaultProps&&console.error("Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.",u,u),typeof o.getSnapshotBeforeUpdate!="function"||typeof o.componentDidUpdate=="function"||W0.has(n)||(W0.add(n),console.error("%s: getSnapshotBeforeUpdate() should be used with componentDidUpdate(). This component defines getSnapshotBeforeUpdate() only.",G(n))),typeof o.getDerivedStateFromProps=="function"&&console.error("%s: getDerivedStateFromProps() is defined as an instance method and will be ignored. Instead, declare it as a static method.",u),typeof o.getDerivedStateFromError=="function"&&console.error("%s: getDerivedStateFromError() is defined as an instance method and will be ignored. Instead, declare it as a static method.",u),typeof n.getSnapshotBeforeUpdate=="function"&&console.error("%s: getSnapshotBeforeUpdate() is defined as a static method and will be ignored. Instead, declare it as an instance method.",u),(r=o.state)&&(typeof r!="object"||Ut(r))&&console.error("%s.state: must be set to an object or null",u),typeof o.getChildContext=="function"&&typeof n.childContextTypes!="object"&&console.error("%s.getChildContext(): childContextTypes must be defined in order to use getChildContext().",u),o=t.stateNode,o.props=a,o.state=t.memoizedState,o.refs={},Ws(t),u=n.contextType,o.context=typeof u=="object"&&u!==null?ut(u):al,o.state===a&&(u=G(n)||"Component",e1.has(u)||(e1.add(u),console.error("%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.",u))),t.mode&Bt&&qn.recordLegacyContextWarning(t,o),qn.recordUnsafeLifecycleWarnings(t,o),o.state=t.memoizedState,u=n.getDerivedStateFromProps,typeof u=="function"&&(Mf(t,n,u,a),o.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(u=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),u!==o.state&&(console.error("%s.componentWillMount(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",X(t)||"Component"),bh.enqueueReplaceState(o,o.state,null)),$i(t,a,o,l),qi(),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308),(t.mode&Yn)!==lt&&(t.flags|=134217728),o=!0}else if(e===null){o=t.stateNode;var O=t.memoizedProps;r=Hl(n,O),o.props=r;var D=o.context;d=n.contextType,u=al,typeof d=="object"&&d!==null&&(u=ut(d)),p=n.getDerivedStateFromProps,d=typeof p=="function"||typeof o.getSnapshotBeforeUpdate=="function",O=t.pendingProps!==O,d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(O||D!==u)&&Ey(t,o,a,u),ll=!1;var A=t.memoizedState;o.state=A,$i(t,a,o,l),qi(),D=t.memoizedState,O||A!==D||ll?(typeof p=="function"&&(Mf(t,n,p,a),D=t.memoizedState),(r=ll||Ty(t,n,r,a,A,D,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308),(t.mode&Yn)!==lt&&(t.flags|=134217728)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),(t.mode&Yn)!==lt&&(t.flags|=134217728),t.memoizedProps=a,t.memoizedState=D),o.props=a,o.state=D,o.context=u,o=r):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),(t.mode&Yn)!==lt&&(t.flags|=134217728),o=!1)}else{o=t.stateNode,Fs(e,t),u=t.memoizedProps,d=Hl(n,u),o.props=d,p=t.pendingProps,A=o.context,D=n.contextType,r=al,typeof D=="object"&&D!==null&&(r=ut(D)),O=n.getDerivedStateFromProps,(D=typeof O=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(u!==p||A!==r)&&Ey(t,o,a,r),ll=!1,A=t.memoizedState,o.state=A,$i(t,a,o,l),qi();var _=t.memoizedState;u!==p||A!==_||ll||e!==null&&e.dependencies!==null&&br(e.dependencies)?(typeof O=="function"&&(Mf(t,n,O,a),_=t.memoizedState),(d=ll||Ty(t,n,d,a,A,_,r)||e!==null&&e.dependencies!==null&&br(e.dependencies))?(D||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(a,_,r),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(a,_,r)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||u===e.memoizedProps&&A===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&A===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=_),o.props=a,o.state=_,o.context=r,o=d):(typeof o.componentDidUpdate!="function"||u===e.memoizedProps&&A===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&A===e.memoizedState||(t.flags|=1024),o=!1)}if(r=o,Vr(e,t),u=(t.flags&128)!==0,r||u){if(r=t.stateNode,Os(t),u&&typeof n.getDerivedStateFromError!="function")n=null,Ft=-1;else{if(he(t),n=H0(r),t.mode&Bt){Q(!0);try{H0(r)}finally{Q(!1)}}ye()}t.flags|=1,e!==null&&u?(t.child=ei(t,e.child,null,l),t.child=ei(t,null,n,l)):_t(e,t,n,l),t.memoizedState=r.state,e=t.child}else e=ga(e,t,l);return l=t.stateNode,o&&l.props!==a&&(ai||console.error("It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.",X(t)||"a component"),ai=!0),e}function Hy(e,t,n,a){return ji(),t.flags|=256,_t(e,t,n,a),t.child}function jf(e,t){t&&t.childContextTypes&&console.error(`childContextTypes cannot be defined on a function component.
  %s.childContextTypes = ...`,t.displayName||t.name||"Component"),typeof t.getDerivedStateFromProps=="function"&&(e=G(t)||"Unknown",s1[e]||(console.error("%s: Function components do not support getDerivedStateFromProps.",e),s1[e]=!0)),typeof t.contextType=="object"&&t.contextType!==null&&(t=G(t)||"Unknown",r1[t]||(console.error("%s: Function components do not support contextType.",t),r1[t]=!0))}function Nf(e){return{baseLanes:e,cachePool:km()}}function kf(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=pn),e}function jy(e,t,n){var a,l=t.pendingProps;T(t)&&(t.flags|=128);var o=!1,u=(t.flags&128)!==0;if((a=u)||(a=e!==null&&e.memoizedState===null?!1:(At.current&_u)!==0),a&&(o=!0,t.flags&=-129),a=(t.flags&32)!==0,t.flags&=-33,e===null){if(qe){if(o?Qa(t):Za(t),qe){var r=st,d;if(!(d=!r)){e:{var p=r;for(d=oa;p.nodeType!==8;){if(!d){d=null;break e}if(p=Sn(p.nextSibling),p===null){d=null;break e}}d=p}d!==null?(Cl(),t.memoizedState={dehydrated:d,treeContext:Bl!==null?{id:Oa,overflow:Ca}:null,retryLane:536870912,hydrationErrors:null},p=S(18,null,null,lt),p.stateNode=d,p.return=t,t.child=p,Gt=t,st=null,d=!0):d=!1,d=!d}d&&(Xs(t,r),xl(t))}if(r=t.memoizedState,r!==null&&(r=r.dehydrated,r!==null))return yd(r)?t.lanes=32:t.lanes=536870912,null;ya(t)}return r=l.children,l=l.fallback,o?(Za(t),o=t.mode,r=Gr({mode:"hidden",children:r},o),l=Al(l,o,n,null),r.return=t,l.return=t,r.sibling=l,t.child=r,o=t.child,o.memoizedState=Nf(n),o.childLanes=kf(e,a,n),t.memoizedState=Rh,l):(Qa(t),Lf(t,r))}var O=e.memoizedState;if(O!==null&&(r=O.dehydrated,r!==null)){if(u)t.flags&256?(Qa(t),t.flags&=-257,t=Bf(e,t,n)):t.memoizedState!==null?(Za(t),t.child=e.child,t.flags|=128,t=null):(Za(t),o=l.fallback,r=t.mode,l=Gr({mode:"visible",children:l.children},r),o=Al(o,r,n,null),o.flags|=2,l.return=t,o.return=t,l.sibling=o,t.child=l,ei(t,e.child,null,n),l=t.child,l.memoizedState=Nf(n),l.childLanes=kf(e,a,n),t.memoizedState=Rh,t=o);else if(Qa(t),qe&&console.error("We should not be hydrating here. This is a bug in React. Please file a bug."),yd(r)){if(a=r.nextSibling&&r.nextSibling.dataset,a){d=a.dgst;var D=a.msg;p=a.stck;var A=a.cstck}r=D,a=d,l=p,d=o=A,o=Error(r||"The server could not finish this Suspense boundary, likely due to an error during server rendering. Switched to client rendering."),o.stack=l||"",o.digest=a,a=d===void 0?null:d,l={value:o,source:null,stack:a},typeof a=="string"&&nh.set(o,l),Ni(l),t=Bf(e,t,n)}else if(wt||ki(e,t,n,!1),a=(n&e.childLanes)!==0,wt||a){if(a=tt,a!==null&&(l=n&-n,l=(l&42)!==0?1:Ri(l),l=(l&(a.suspendedLanes|n))!==0?0:l,l!==0&&l!==O.retryLane))throw O.retryLane=l,Jt(e,l),ht(a,e,l),i1;r.data===za||Wf(),t=Bf(e,t,n)}else r.data===za?(t.flags|=192,t.child=e.child,t=null):(e=O.treeContext,st=Sn(r.nextSibling),Gt=t,qe=!0,Yl=null,wa=!1,Cn=null,oa=!1,e!==null&&(Cl(),An[On++]=Oa,An[On++]=Ca,An[On++]=Bl,Oa=e.id,Ca=e.overflow,Bl=t),t=Lf(t,l.children),t.flags|=4096);return t}return o?(Za(t),o=l.fallback,r=t.mode,d=e.child,p=d.sibling,l=ha(d,{mode:"hidden",children:l.children}),l.subtreeFlags=d.subtreeFlags&65011712,p!==null?o=ha(p,o):(o=Al(o,r,n,null),o.flags|=2),o.return=t,l.return=t,l.sibling=o,t.child=l,l=o,o=t.child,r=e.child.memoizedState,r===null?r=Nf(n):(d=r.cachePool,d!==null?(p=Et._currentValue,d=d.parent!==p?{parent:p,pool:p}:d):d=km(),r={baseLanes:r.baseLanes|n,cachePool:d}),o.memoizedState=r,o.childLanes=kf(e,a,n),t.memoizedState=Rh,l):(Qa(t),n=e.child,e=n.sibling,n=ha(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(a=t.deletions,a===null?(t.deletions=[e],t.flags|=16):a.push(e)),t.child=n,t.memoizedState=null,n)}function Lf(e,t){return t=Gr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Gr(e,t){return e=S(22,e,null,t),e.lanes=0,e.stateNode={_visibility:ah,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Bf(e,t,n){return ei(t,e.child,null,n),e=Lf(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ny(e,t,n){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Qs(e.return,t,n)}function ky(e,t){var n=Ut(e);return e=!n&&typeof F(e)=="function",n||e?(n=n?"array":"iterable",console.error("A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>",n,t,n),!1):!0}function Yf(e,t,n,a,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=a,o.tail=n,o.tailMode=l)}function Ly(e,t,n){var a=t.pendingProps,l=a.revealOrder,o=a.tail;if(a=a.children,l!==void 0&&l!=="forwards"&&l!=="backwards"&&l!=="together"&&!f1[l])if(f1[l]=!0,typeof l=="string")switch(l.toLowerCase()){case"together":case"forwards":case"backwards":console.error('"%s" is not a valid value for revealOrder on <SuspenseList />. Use lowercase "%s" instead.',l,l.toLowerCase());break;case"forward":case"backward":console.error('"%s" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use "%ss" instead.',l,l.toLowerCase());break;default:console.error('"%s" is not a supported revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',l)}else console.error('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',l);o===void 0||Eh[o]||(o!=="collapsed"&&o!=="hidden"?(Eh[o]=!0,console.error('"%s" is not a supported value for tail on <SuspenseList />. Did you mean "collapsed" or "hidden"?',o)):l!=="forwards"&&l!=="backwards"&&(Eh[o]=!0,console.error('<SuspenseList tail="%s" /> is only valid if revealOrder is "forwards" or "backwards". Did you mean to specify revealOrder="forwards"?',o)));e:if((l==="forwards"||l==="backwards")&&a!==void 0&&a!==null&&a!==!1)if(Ut(a)){for(var u=0;u<a.length;u++)if(!ky(a[u],u))break e}else if(u=F(a),typeof u=="function"){if(u=u.call(a))for(var r=u.next(),d=0;!r.done;r=u.next()){if(!ky(r.value,d))break e;d++}}else console.error('A single row was passed to a <SuspenseList revealOrder="%s" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?',l);if(_t(e,t,a,n),a=At.current,(a&_u)!==0)a=a&ti|_u,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ny(e,n,t);else if(e.tag===19)Ny(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=ti}switch(we(At,a,t),l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&qr(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),Yf(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&qr(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}Yf(t,!0,n,null,o);break;case"together":Yf(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ga(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ft=-1,rl|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(ki(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error("Resuming work not yet implemented.");if(t.child!==null){for(e=t.child,n=ha(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=ha(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function qf(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&br(e)))}function WS(e,t,n){switch(t.tag){case 3:at(t,t.stateNode.containerInfo),$a(t,Et,e.memoizedState.cache),ji();break;case 27:case 5:ue(t);break;case 4:at(t,t.stateNode.containerInfo);break;case 10:$a(t,t.type,t.memoizedProps.value);break;case 12:(n&t.childLanes)!==0&&(t.flags|=4),t.flags|=2048;var a=t.stateNode;a.effectDuration=-0,a.passiveEffectDuration=-0;break;case 13:if(a=t.memoizedState,a!==null)return a.dehydrated!==null?(Qa(t),t.flags|=128,null):(n&t.child.childLanes)!==0?jy(e,t,n):(Qa(t),e=ga(e,t,n),e!==null?e.sibling:null);Qa(t);break;case 19:var l=(e.flags&128)!==0;if(a=(n&t.childLanes)!==0,a||(ki(e,t,n,!1),a=(n&t.childLanes)!==0),l){if(a)return Ly(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),we(At,At.current,t),a)break;return null;case 22:case 23:return t.lanes=0,My(e,t,n);case 24:$a(t,Et,e.memoizedState.cache)}return ga(e,t,n)}function $f(e,t,n){if(t._debugNeedsRemount&&e!==null){n=Ys(t.type,t.key,t.pendingProps,t._debugOwner||null,t.mode,t.lanes),n._debugStack=t._debugStack,n._debugTask=t._debugTask;var a=t.return;if(a===null)throw Error("Cannot swap the root fiber.");if(e.alternate=null,t.alternate=null,n.index=t.index,n.sibling=t.sibling,n.return=t.return,n.ref=t.ref,n._debugInfo=t._debugInfo,t===a.child)a.child=n;else{var l=a.child;if(l===null)throw Error("Expected parent to have a child.");for(;l.sibling!==t;)if(l=l.sibling,l===null)throw Error("Expected to find the previous sibling.");l.sibling=n}return t=a.deletions,t===null?(a.deletions=[e],a.flags|=16):t.push(e),n.flags|=2,n}if(e!==null)if(e.memoizedProps!==t.pendingProps||t.type!==e.type)wt=!0;else{if(!qf(e,n)&&(t.flags&128)===0)return wt=!1,WS(e,t,n);wt=(e.flags&131072)!==0}else wt=!1,(a=qe)&&(Cl(),a=(t.flags&1048576)!==0),a&&(a=t.index,Cl(),Dm(t,bc,a));switch(t.lanes=0,t.tag){case 16:e:if(a=t.pendingProps,e=il(t.elementType),t.type=e,typeof e=="function")Bs(e)?(a=Hl(e,a),t.tag=1,t.type=e=Rl(e),t=Uy(null,t,e,a,n)):(t.tag=0,jf(t,e),t.type=e=Rl(e),t=Hf(null,t,e,a,n));else{if(e!=null){if(l=e.$$typeof,l===uu){t.tag=11,t.type=e=ks(e),t=wy(null,t,e,a,n);break e}else if(l===uc){t.tag=14,t=xy(null,t,e,a,n);break e}}throw t="",e!==null&&typeof e=="object"&&e.$$typeof===sn&&(t=" Did you wrap a component in React.lazy() more than once?"),e=G(e)||e,Error("Element type is invalid. Received a promise that resolves to: "+e+". Lazy element type must resolve to a class or function."+t)}return t;case 0:return Hf(e,t,t.type,t.pendingProps,n);case 1:return a=t.type,l=Hl(a,t.pendingProps),Uy(e,t,a,l,n);case 3:e:{if(at(t,t.stateNode.containerInfo),e===null)throw Error("Should have a current fiber. This is a bug in React.");a=t.pendingProps;var o=t.memoizedState;l=o.element,Fs(e,t),$i(t,a,null,n);var u=t.memoizedState;if(a=u.cache,$a(t,Et,a),a!==o.cache&&Zs(t,[Et],n,!0),qi(),a=u.element,o.isDehydrated)if(o={element:a,isDehydrated:!1,cache:u.cache},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){t=Hy(e,t,a,n);break e}else if(a!==l){l=on(Error("This root received an early update, before anything was able hydrate. Switched the entire root to client rendering."),t),Ni(l),t=Hy(e,t,a,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(st=Sn(e.firstChild),Gt=t,qe=!0,Yl=null,wa=!1,Cn=null,oa=!0,e=I0(t,null,a,n),t.child=e;e;)e.flags=e.flags&-3|4096,e=e.sibling}else{if(ji(),a===l){t=ga(e,t,n);break e}_t(e,t,a,n)}t=t.child}return t;case 26:return Vr(e,t),e===null?(e=av(t.type,null,t.pendingProps,null))?t.memoizedState=e:qe||(e=t.type,n=t.pendingProps,a=Fe(Fa.current),a=Wr(a).createElement(e),a[Lt]=t,a[Wt]=n,zt(a,e,n),E(a),t.stateNode=a):t.memoizedState=av(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ue(t),e===null&&qe&&(a=Fe(Fa.current),l=L(),a=t.stateNode=tv(t.type,t.pendingProps,a,l,!1),wa||(l=Qg(a,t.type,t.pendingProps,l),l!==null&&(wl(t,0).serverProps=l)),Gt=t,oa=!0,l=st,Ka(t.type)?(Qh=l,st=Sn(a.firstChild)):st=l),_t(e,t,t.pendingProps.children,n),Vr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&qe&&(o=L(),a=zs(t.type,o.ancestorInfo),l=st,(u=!l)||(u=jT(l,t.type,t.pendingProps,oa),u!==null?(t.stateNode=u,wa||(o=Qg(u,t.type,t.pendingProps,o),o!==null&&(wl(t,0).serverProps=o)),Gt=t,st=Sn(u.firstChild),oa=!1,o=!0):o=!1,u=!o),u&&(a&&Xs(t,l),xl(t))),ue(t),l=t.type,o=t.pendingProps,u=e!==null?e.memoizedProps:null,a=o.children,pd(l,o)?a=null:u!==null&&pd(l,u)&&(t.flags|=32),t.memoizedState!==null&&(l=af(e,t,QS,null,null,n),Qu._currentValue=l),Vr(e,t),_t(e,t,a,n),t.child;case 6:return e===null&&qe&&(e=t.pendingProps,n=L(),a=n.ancestorInfo.current,e=a!=null?sr(e,a.tag,n.ancestorInfo.implicitRootScope):!0,n=st,(a=!n)||(a=NT(n,t.pendingProps,oa),a!==null?(t.stateNode=a,Gt=t,st=null,a=!0):a=!1,a=!a),a&&(e&&Xs(t,n),xl(t))),null;case 13:return jy(e,t,n);case 4:return at(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=ei(t,null,a,n):_t(e,t,a,n),t.child;case 11:return wy(e,t,t.type,t.pendingProps,n);case 7:return _t(e,t,t.pendingProps,n),t.child;case 8:return _t(e,t,t.pendingProps.children,n),t.child;case 12:return t.flags|=4,t.flags|=2048,a=t.stateNode,a.effectDuration=-0,a.passiveEffectDuration=-0,_t(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.type,l=t.pendingProps,o=l.value,"value"in l||d1||(d1=!0,console.error("The `value` prop is required for the `<Context.Provider>`. Did you misspell it or forget to pass it?")),$a(t,a,o),_t(e,t,l.children,n),t.child;case 9:return l=t.type._context,a=t.pendingProps.children,typeof a!="function"&&console.error("A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it."),Dl(t),l=ut(l),he(t),a=yh(a,l,void 0),ye(),t.flags|=1,_t(e,t,a,n),t.child;case 14:return xy(e,t,t.type,t.pendingProps,n);case 15:return Dy(e,t,t.type,t.pendingProps,n);case 19:return Ly(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},e===null?(e=Gr(a,n),e.ref=t.ref,t.child=e,e.return=t,t=e):(e=ha(e.child,a),e.ref=t.ref,t.child=e,e.return=t,t=e),t;case 22:return My(e,t,n);case 24:return Dl(t),a=ut(Et),e===null?(l=Ps(),l===null&&(l=tt,o=Is(),l.pooledCache=o,Ml(o),o!==null&&(l.pooledCacheLanes|=n),l=o),t.memoizedState={parent:a,cache:l},Ws(t),$a(t,Et,l)):((e.lanes&n)!==0&&(Fs(e,t),$i(t,null,null,n),qi()),l=e.memoizedState,o=t.memoizedState,l.parent!==a?(l={parent:a,cache:a},t.memoizedState=l,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=l),$a(t,Et,a)):(a=o.cache,$a(t,Et,a),a!==l.cache&&Zs(t,[Et],n,!0))),_t(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function va(e){e.flags|=4}function By(e,t){if(t.type!=="stylesheet"||(t.state.loading&_n)!==to)e.flags&=-16777217;else if(e.flags|=16777216,!rv(t)){if(t=Mn.current,t!==null&&((ke&4194048)===ke?ua!==null:(ke&62914560)!==ke&&(ke&536870912)===0||t!==ua))throw wu=ch,E0;e.flags|=8192}}function Xr(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?bl():536870912,e.lanes|=t,Jl|=t)}function Zi(e,t){if(!qe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function rt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,a=0;if(t)if((e.mode&Ht)!==lt){for(var l=e.selfBaseDuration,o=e.child;o!==null;)n|=o.lanes|o.childLanes,a|=o.subtreeFlags&65011712,a|=o.flags&65011712,l+=o.treeBaseDuration,o=o.sibling;e.treeBaseDuration=l}else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,a|=l.subtreeFlags&65011712,a|=l.flags&65011712,l.return=e,l=l.sibling;else if((e.mode&Ht)!==lt){l=e.actualDuration,o=e.selfBaseDuration;for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,a|=u.subtreeFlags,a|=u.flags,l+=u.actualDuration,o+=u.treeBaseDuration,u=u.sibling;e.actualDuration=l,e.treeBaseDuration=o}else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,a|=l.subtreeFlags,a|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function FS(e,t,n){var a=t.pendingProps;switch(Gs(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return rt(t),null;case 1:return rt(t),null;case 3:return n=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),pa(Et,t),ct(t),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Hi(t)?(Um(),va(t)):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,zm())),rt(t),null;case 26:return n=t.memoizedState,e===null?(va(t),n!==null?(rt(t),By(t,n)):(rt(t),t.flags&=-16777217)):n?n!==e.memoizedState?(va(t),rt(t),By(t,n)):(rt(t),t.flags&=-16777217):(e.memoizedProps!==a&&va(t),rt(t),t.flags&=-16777217),null;case 27:ce(t),n=Fe(Fa.current);var l=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&va(t);else{if(!a){if(t.stateNode===null)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");return rt(t),null}e=L(),Hi(t)?Mm(t):(e=tv(l,a,n,e,!0),t.stateNode=e,va(t))}return rt(t),null;case 5:if(ce(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&va(t);else{if(!a){if(t.stateNode===null)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");return rt(t),null}if(l=L(),Hi(t))Mm(t);else{switch(e=Fe(Fa.current),zs(n,l.ancestorInfo),l=l.context,e=Wr(e),l){case di:e=e.createElementNS(jo,n);break;case Qc:e=e.createElementNS(hc,n);break;default:switch(n){case"svg":e=e.createElementNS(jo,n);break;case"math":e=e.createElementNS(hc,n);break;case"script":e=e.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?e.createElement("select",{is:a.is}):e.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?e.createElement(n,{is:a.is}):e.createElement(n),n.indexOf("-")===-1&&(n!==n.toLowerCase()&&console.error("<%s /> is using incorrect casing. Use PascalCase for React components, or lowercase for HTML elements.",n),Object.prototype.toString.call(e)!=="[object HTMLUnknownElement]"||Ra.call(_1,n)||(_1[n]=!0,console.error("The tag <%s> is unrecognized in this browser. If you meant to render a React component, start its name with an uppercase letter.",n)))}}e[Lt]=t,e[Wt]=a;e:for(l=t.child;l!==null;){if(l.tag===5||l.tag===6)e.appendChild(l.stateNode);else if(l.tag!==4&&l.tag!==27&&l.child!==null){l.child.return=l,l=l.child;continue}if(l===t)break e;for(;l.sibling===null;){if(l.return===null||l.return===t)break e;l=l.return}l.sibling.return=l.return,l=l.sibling}t.stateNode=e;e:switch(zt(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&va(t)}}return rt(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&va(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");if(e=Fe(Fa.current),n=L(),Hi(t)){e=t.stateNode,n=t.memoizedProps,l=!wa,a=null;var o=Gt;if(o!==null)switch(o.tag){case 3:l&&(l=Wg(e,n,a),l!==null&&(wl(t,0).serverProps=l));break;case 27:case 5:a=o.memoizedProps,l&&(l=Wg(e,n,a),l!==null&&(wl(t,0).serverProps=l))}e[Lt]=t,e=!!(e.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||qg(e.nodeValue,n)),e||xl(t)}else l=n.ancestorInfo.current,l!=null&&sr(a,l.tag,n.ancestorInfo.implicitRootScope),e=Wr(e).createTextNode(a),e[Lt]=t,t.stateNode=e}return rt(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(l=Hi(t),a!==null&&a.dehydrated!==null){if(e===null){if(!l)throw Error("A dehydrated suspense component was completed without a hydrated node. This is probably a bug in React.");if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");l[Lt]=t,rt(t),(t.mode&Ht)!==lt&&a!==null&&(l=t.child,l!==null&&(t.treeBaseDuration-=l.treeBaseDuration))}else Um(),ji(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4,rt(t),(t.mode&Ht)!==lt&&a!==null&&(l=t.child,l!==null&&(t.treeBaseDuration-=l.treeBaseDuration));l=!1}else l=zm(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=l),l=!0;if(!l)return t.flags&256?(ya(t),t):(ya(t),null)}return ya(t),(t.flags&128)!==0?(t.lanes=n,(t.mode&Ht)!==lt&&Er(t),t):(n=a!==null,e=e!==null&&e.memoizedState!==null,n&&(a=t.child,l=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(l=a.alternate.memoizedState.cachePool.pool),o=null,a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(o=a.memoizedState.cachePool.pool),o!==l&&(a.flags|=2048)),n!==e&&n&&(t.child.flags|=8192),Xr(t,t.updateQueue),rt(t),(t.mode&Ht)!==lt&&n&&(e=t.child,e!==null&&(t.treeBaseDuration-=e.treeBaseDuration)),null);case 4:return ct(t),e===null&&ud(t.stateNode.containerInfo),rt(t),null;case 10:return pa(t.type,t),rt(t),null;case 19:if(de(At,t),l=t.memoizedState,l===null)return rt(t),null;if(a=(t.flags&128)!==0,o=l.rendering,o===null)if(a)Zi(l,!1);else{if(ft!==_a||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(o=qr(e),o!==null){for(t.flags|=128,Zi(l,!1),e=o.updateQueue,t.updateQueue=e,Xr(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)xm(n,e),n=n.sibling;return we(At,At.current&ti|_u,t),t.child}e=e.sibling}l.tail!==null&&ea()>Hc&&(t.flags|=128,a=!0,Zi(l,!1),t.lanes=4194304)}else{if(!a)if(e=qr(o),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Xr(t,e),Zi(l,!0),l.tail===null&&l.tailMode==="hidden"&&!o.alternate&&!qe)return rt(t),null}else 2*ea()-l.renderingStartTime>Hc&&n!==536870912&&(t.flags|=128,a=!0,Zi(l,!1),t.lanes=4194304);l.isBackwards?(o.sibling=t.child,t.child=o):(e=l.last,e!==null?e.sibling=o:t.child=o,l.last=o)}return l.tail!==null?(e=l.tail,l.rendering=e,l.tail=e.sibling,l.renderingStartTime=ea(),e.sibling=null,n=At.current,n=a?n&ti|_u:n&ti,we(At,n,t),e):(rt(t),null);case 22:case 23:return ya(t),tf(t),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(n&536870912)!==0&&(t.flags&128)===0&&(rt(t),t.subtreeFlags&6&&(t.flags|=8192)):rt(t),n=t.updateQueue,n!==null&&Xr(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),e!==null&&de(Vl,t),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),pa(Et,t),rt(t),null;case 25:return null;case 30:return null}throw Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function eT(e,t){switch(Gs(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,(t.mode&Ht)!==lt&&Er(t),t):null;case 3:return pa(Et,t),ct(t),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return ce(t),null;case 13:if(ya(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error("Threw in newly mounted dehydrated component. This is likely a bug in React. Please file an issue.");ji()}return e=t.flags,e&65536?(t.flags=e&-65537|128,(t.mode&Ht)!==lt&&Er(t),t):null;case 19:return de(At,t),null;case 4:return ct(t),null;case 10:return pa(t.type,t),null;case 22:case 23:return ya(t),tf(t),e!==null&&de(Vl,t),e=t.flags,e&65536?(t.flags=e&-65537|128,(t.mode&Ht)!==lt&&Er(t),t):null;case 24:return pa(Et,t),null;case 25:return null;default:return null}}function Yy(e,t){switch(Gs(t),t.tag){case 3:pa(Et,t),ct(t);break;case 26:case 27:case 5:ce(t);break;case 4:ct(t);break;case 13:ya(t);break;case 19:de(At,t);break;case 10:pa(t.type,t);break;case 22:case 23:ya(t),tf(t),e!==null&&de(Vl,t);break;case 24:pa(Et,t)}}function In(e){return(e.mode&Ht)!==lt}function qy(e,t){In(e)?(Zn(),Ii(t,e),Qn()):Ii(t,e)}function Vf(e,t,n){In(e)?(Zn(),To(n,e,t),Qn()):To(n,e,t)}function Ii(e,t){try{var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var l=a.next;n=l;do{if((n.tag&e)===e&&((e&Rt)!==wn?J!==null&&typeof J.markComponentPassiveEffectMountStarted=="function"&&J.markComponentPassiveEffectMountStarted(t):(e&jt)!==wn&&J!==null&&typeof J.markComponentLayoutEffectMountStarted=="function"&&J.markComponentLayoutEffectMountStarted(t),a=void 0,(e&Xt)!==wn&&(si=!0),a=re(t,bR,n),(e&Xt)!==wn&&(si=!1),(e&Rt)!==wn?J!==null&&typeof J.markComponentPassiveEffectMountStopped=="function"&&J.markComponentPassiveEffectMountStopped():(e&jt)!==wn&&J!==null&&typeof J.markComponentLayoutEffectMountStopped=="function"&&J.markComponentLayoutEffectMountStopped(),a!==void 0&&typeof a!="function")){var o=void 0;o=(n.tag&jt)!==0?"useLayoutEffect":(n.tag&Xt)!==0?"useInsertionEffect":"useEffect";var u=void 0;u=a===null?" You returned null. If your effect does not require clean up, return undefined (or nothing).":typeof a.then=="function"?`

It looks like you wrote `+o+`(async () => ...) or returned a Promise. Instead, write the async function inside your effect and call it immediately:

`+o+`(() => {
  async function fetchData() {
    // You can await here
    const response = await MyAPI.getData(someId);
    // ...
  }
  fetchData();
}, [someId]); // Or [] if effect doesn't need props or state

Learn more about data fetching with Hooks: https://react.dev/link/hooks-data-fetching`:" You returned: "+a,re(t,function(r,d){console.error("%s must not return anything besides a function, which is used for clean-up.%s",r,d)},o,u)}n=n.next}while(n!==l)}}catch(r){Ke(t,t.return,r)}}function To(e,t,n){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var o=l.next;a=o;do{if((a.tag&e)===e){var u=a.inst,r=u.destroy;r!==void 0&&(u.destroy=void 0,(e&Rt)!==wn?J!==null&&typeof J.markComponentPassiveEffectUnmountStarted=="function"&&J.markComponentPassiveEffectUnmountStarted(t):(e&jt)!==wn&&J!==null&&typeof J.markComponentLayoutEffectUnmountStarted=="function"&&J.markComponentLayoutEffectUnmountStarted(t),(e&Xt)!==wn&&(si=!0),l=t,re(l,SR,l,n,r),(e&Xt)!==wn&&(si=!1),(e&Rt)!==wn?J!==null&&typeof J.markComponentPassiveEffectUnmountStopped=="function"&&J.markComponentPassiveEffectUnmountStopped():(e&jt)!==wn&&J!==null&&typeof J.markComponentLayoutEffectUnmountStopped=="function"&&J.markComponentLayoutEffectUnmountStopped())}a=a.next}while(a!==o)}}catch(d){Ke(t,t.return,d)}}function $y(e,t){In(e)?(Zn(),Ii(t,e),Qn()):Ii(t,e)}function Gf(e,t,n){In(e)?(Zn(),To(n,e,t),Qn()):To(n,e,t)}function Vy(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;e.type.defaultProps||"ref"in e.memoizedProps||ai||(n.props!==e.memoizedProps&&console.error("Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",X(e)||"instance"),n.state!==e.memoizedState&&console.error("Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",X(e)||"instance"));try{re(e,Gm,t,n)}catch(a){Ke(e,e.return,a)}}}function tT(e,t,n){return e.getSnapshotBeforeUpdate(t,n)}function nT(e,t){var n=t.memoizedProps,a=t.memoizedState;t=e.stateNode,e.type.defaultProps||"ref"in e.memoizedProps||ai||(t.props!==e.memoizedProps&&console.error("Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",X(e)||"instance"),t.state!==e.memoizedState&&console.error("Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",X(e)||"instance"));try{var l=Hl(e.type,n,e.elementType===e.type),o=re(e,tT,t,l,a);n=h1,o!==void 0||n.has(e.type)||(n.add(e.type),re(e,function(){console.error("%s.getSnapshotBeforeUpdate(): A snapshot value (or null) must be returned. You have returned undefined.",X(e))})),t.__reactInternalSnapshotBeforeUpdate=o}catch(u){Ke(e,e.return,u)}}function Gy(e,t,n){n.props=Hl(e.type,e.memoizedProps),n.state=e.memoizedState,In(e)?(Zn(),re(e,Y0,e,t,n),Qn()):re(e,Y0,e,t,n)}function aT(e){var t=e.ref;if(t!==null){switch(e.tag){case 26:case 27:case 5:var n=e.stateNode;break;case 30:n=e.stateNode;break;default:n=e.stateNode}if(typeof t=="function")if(In(e))try{Zn(),e.refCleanup=t(n)}finally{Qn()}else e.refCleanup=t(n);else typeof t=="string"?console.error("String refs are no longer supported."):t.hasOwnProperty("current")||console.error("Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef().",X(e)),t.current=n}}function Ji(e,t){try{re(e,aT,e)}catch(n){Ke(e,t,n)}}function Jn(e,t){var n=e.ref,a=e.refCleanup;if(n!==null)if(typeof a=="function")try{if(In(e))try{Zn(),re(e,a)}finally{Qn(e)}else re(e,a)}catch(l){Ke(e,t,l)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{if(In(e))try{Zn(),re(e,n,null)}finally{Qn(e)}else re(e,n,null)}catch(l){Ke(e,t,l)}else n.current=null}function Xy(e,t,n,a){var l=e.memoizedProps,o=l.id,u=l.onCommit;l=l.onRender,t=t===null?"mount":"update",Ec&&(t="nested-update"),typeof l=="function"&&l(o,t,e.actualDuration,e.treeBaseDuration,e.actualStartTime,n),typeof u=="function"&&u(e.memoizedProps.id,t,a,n)}function lT(e,t,n,a){var l=e.memoizedProps;e=l.id,l=l.onPostCommit,t=t===null?"mount":"update",Ec&&(t="nested-update"),typeof l=="function"&&l(e,t,a,n)}function Qy(e){var t=e.type,n=e.memoizedProps,a=e.stateNode;try{re(e,CT,a,t,n,e)}catch(l){Ke(e,e.return,l)}}function Xf(e,t,n){try{re(e,wT,e.stateNode,e.type,n,t,e)}catch(a){Ke(e,e.return,a)}}function Zy(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Ka(e.type)||e.tag===4}function Qf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Zy(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Ka(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Zf(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Pr));else if(a!==4&&(a===27&&Ka(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Zf(e,t,n),e=e.sibling;e!==null;)Zf(e,t,n),e=e.sibling}function Qr(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(a!==4&&(a===27&&Ka(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Qr(e,t,n),e=e.sibling;e!==null;)Qr(e,t,n),e=e.sibling}function oT(e){for(var t,n=e.return;n!==null;){if(Zy(n)){t=n;break}n=n.return}if(t==null)throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");switch(t.tag){case 27:t=t.stateNode,n=Qf(e),Qr(e,n,t);break;case 5:n=t.stateNode,t.flags&32&&(Jg(n),t.flags&=-33),t=Qf(e),Qr(e,t,n);break;case 3:case 4:t=t.stateNode.containerInfo,n=Qf(e),Zf(e,n,t);break;default:throw Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.")}}function Iy(e){var t=e.stateNode,n=e.memoizedProps;try{re(e,YT,e.type,n,t,e)}catch(a){Ke(e,e.return,a)}}function iT(e,t){if(e=e.containerInfo,Vh=Kc,e=Rm(e),js(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var l=a.anchorOffset,o=a.focusNode;a=a.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var u=0,r=-1,d=-1,p=0,O=0,D=e,A=null;t:for(;;){for(var _;D!==n||l!==0&&D.nodeType!==3||(r=u+l),D!==o||a!==0&&D.nodeType!==3||(d=u+a),D.nodeType===3&&(u+=D.nodeValue.length),(_=D.firstChild)!==null;)A=D,D=_;for(;;){if(D===e)break t;if(A===n&&++p===l&&(r=u),A===o&&++O===a&&(d=u),(_=D.nextSibling)!==null)break;D=A,A=D.parentNode}D=_}n=r===-1||d===-1?null:{start:r,end:d}}else n=null}n=n||{start:0,end:0}}else n=null;for(Gh={focusedElem:e,selectionRange:n},Kc=!1,xt=t;xt!==null;)if(t=xt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,xt=e;else for(;xt!==null;){switch(e=t=xt,n=e.alternate,l=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:(l&1024)!==0&&n!==null&&nT(e,n);break;case 3:if((l&1024)!==0){if(e=e.stateNode.containerInfo,n=e.nodeType,n===9)md(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((l&1024)!==0)throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}if(e=t.sibling,e!==null){e.return=t.return,xt=e;break}xt=t.return}}function Jy(e,t,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:Sa(e,n),a&4&&qy(n,jt|xn);break;case 1:if(Sa(e,n),a&4)if(e=n.stateNode,t===null)n.type.defaultProps||"ref"in n.memoizedProps||ai||(e.props!==n.memoizedProps&&console.error("Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",X(n)||"instance"),e.state!==n.memoizedState&&console.error("Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",X(n)||"instance")),In(n)?(Zn(),re(n,gh,n,e),Qn()):re(n,gh,n,e);else{var l=Hl(n.type,t.memoizedProps);t=t.memoizedState,n.type.defaultProps||"ref"in n.memoizedProps||ai||(e.props!==n.memoizedProps&&console.error("Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",X(n)||"instance"),e.state!==n.memoizedState&&console.error("Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",X(n)||"instance")),In(n)?(Zn(),re(n,k0,n,e,l,t,e.__reactInternalSnapshotBeforeUpdate),Qn()):re(n,k0,n,e,l,t,e.__reactInternalSnapshotBeforeUpdate)}a&64&&Vy(n),a&512&&Ji(n,n.return);break;case 3:if(t=ma(),Sa(e,n),a&64&&(a=n.updateQueue,a!==null)){if(l=null,n.child!==null)switch(n.child.tag){case 27:case 5:l=n.child.stateNode;break;case 1:l=n.child.stateNode}try{re(n,Gm,a,l)}catch(u){Ke(n,n.return,u)}}e.effectDuration+=Tr(t);break;case 27:t===null&&a&4&&Iy(n);case 26:case 5:Sa(e,n),t===null&&a&4&&Qy(n),a&512&&Ji(n,n.return);break;case 12:if(a&4){a=ma(),Sa(e,n),e=n.stateNode,e.effectDuration+=Bi(a);try{re(n,Xy,n,t,Tc,e.effectDuration)}catch(u){Ke(n,n.return,u)}}else Sa(e,n);break;case 13:Sa(e,n),a&4&&Wy(e,n),a&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=mT.bind(null,n),kT(e,n))));break;case 22:if(a=n.memoizedState!==null||Ma,!a){t=t!==null&&t.memoizedState!==null||pt,l=Ma;var o=pt;Ma=a,(pt=t)&&!o?Ta(e,n,(n.subtreeFlags&8772)!==0):Sa(e,n),Ma=l,pt=o}break;case 30:break;default:Sa(e,n)}}function Ky(e){var t=e.alternate;t!==null&&(e.alternate=null,Ky(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Ya(t)),e.stateNode=null,e._debugOwner=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ba(e,t,n){for(n=n.child;n!==null;)Py(e,t,n),n=n.sibling}function Py(e,t,n){if(kt&&typeof kt.onCommitFiberUnmount=="function")try{kt.onCommitFiberUnmount(Uo,n)}catch(o){ta||(ta=!0,console.error("React instrumentation encountered an error: %s",o))}switch(n.tag){case 26:pt||Jn(n,t),ba(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:pt||Jn(n,t);var a=bt,l=en;Ka(n.type)&&(bt=n.stateNode,en=!1),ba(e,t,n),re(n,nu,n.stateNode),bt=a,en=l;break;case 5:pt||Jn(n,t);case 6:if(a=bt,l=en,bt=null,ba(e,t,n),bt=a,en=l,bt!==null)if(en)try{re(n,MT,bt,n.stateNode)}catch(o){Ke(n,t,o)}else try{re(n,DT,bt,n.stateNode)}catch(o){Ke(n,t,o)}break;case 18:bt!==null&&(en?(e=bt,Kg(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),iu(e)):Kg(bt,n.stateNode));break;case 4:a=bt,l=en,bt=n.stateNode.containerInfo,en=!0,ba(e,t,n),bt=a,en=l;break;case 0:case 11:case 14:case 15:pt||To(Xt,n,t),pt||Vf(n,t,jt),ba(e,t,n);break;case 1:pt||(Jn(n,t),a=n.stateNode,typeof a.componentWillUnmount=="function"&&Gy(n,t,a)),ba(e,t,n);break;case 21:ba(e,t,n);break;case 22:pt=(a=pt)||n.memoizedState!==null,ba(e,t,n),pt=a;break;default:ba(e,t,n)}}function Wy(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{re(t,BT,e)}catch(n){Ke(t,t.return,n)}}function uT(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new p1),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new p1),t;default:throw Error("Unexpected Suspense handler tag ("+e.tag+"). This is a bug in React.")}}function If(e,t){var n=uT(e);t.forEach(function(a){var l=yT.bind(null,e,a);if(!n.has(a)){if(n.add(a),Bn)if(li!==null&&oi!==null)Fi(oi,li);else throw Error("Expected finished root and lanes to be set. This is a bug in React.");a.then(l,l)}})}function un(e,t){var n=t.deletions;if(n!==null)for(var a=0;a<n.length;a++){var l=e,o=t,u=n[a],r=o;e:for(;r!==null;){switch(r.tag){case 27:if(Ka(r.type)){bt=r.stateNode,en=!1;break e}break;case 5:bt=r.stateNode,en=!1;break e;case 3:case 4:bt=r.stateNode.containerInfo,en=!0;break e}r=r.return}if(bt===null)throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");Py(l,o,u),bt=null,en=!1,l=u,o=l.alternate,o!==null&&(o.return=null),l.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Fy(t,e),t=t.sibling}function Fy(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:un(t,e),rn(e),a&4&&(To(Xt|xn,e,e.return),Ii(Xt|xn,e),Vf(e,e.return,jt|xn));break;case 1:un(t,e),rn(e),a&512&&(pt||n===null||Jn(n,n.return)),a&64&&Ma&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var l=Vn;if(un(t,e),rn(e),a&512&&(pt||n===null||Jn(n,n.return)),a&4)if(t=n!==null?n.memoizedState:null,a=e.memoizedState,n===null)if(a===null)if(e.stateNode===null){e:{a=e.type,n=e.memoizedProps,t=l.ownerDocument||l;t:switch(a){case"title":l=t.getElementsByTagName("title")[0],(!l||l[cu]||l[Lt]||l.namespaceURI===jo||l.hasAttribute("itemprop"))&&(l=t.createElement(a),t.head.insertBefore(l,t.querySelector("head > title"))),zt(l,a,n),l[Lt]=e,E(l),a=l;break e;case"link":var o=iv("link","href",t).get(a+(n.href||""));if(o){for(var u=0;u<o.length;u++)if(l=o[u],l.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&l.getAttribute("rel")===(n.rel==null?null:n.rel)&&l.getAttribute("title")===(n.title==null?null:n.title)&&l.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){o.splice(u,1);break t}}l=t.createElement(a),zt(l,a,n),t.head.appendChild(l);break;case"meta":if(o=iv("meta","content",t).get(a+(n.content||""))){for(u=0;u<o.length;u++)if(l=o[u],N(n.content,"content"),l.getAttribute("content")===(n.content==null?null:""+n.content)&&l.getAttribute("name")===(n.name==null?null:n.name)&&l.getAttribute("property")===(n.property==null?null:n.property)&&l.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&l.getAttribute("charset")===(n.charSet==null?null:n.charSet)){o.splice(u,1);break t}}l=t.createElement(a),zt(l,a,n),t.head.appendChild(l);break;default:throw Error('getNodesForType encountered a type it did not expect: "'+a+'". This is a bug in React.')}l[Lt]=e,E(l),a=l}e.stateNode=a}else uv(l,e.type,e.stateNode);else e.stateNode=ov(l,a,e.memoizedProps);else t!==a?(t===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):t.count--,a===null?uv(l,e.type,e.stateNode):ov(l,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Xf(e,e.memoizedProps,n.memoizedProps);break;case 27:un(t,e),rn(e),a&512&&(pt||n===null||Jn(n,n.return)),n!==null&&a&4&&Xf(e,e.memoizedProps,n.memoizedProps);break;case 5:if(un(t,e),rn(e),a&512&&(pt||n===null||Jn(n,n.return)),e.flags&32){t=e.stateNode;try{re(e,Jg,t)}catch(O){Ke(e,e.return,O)}}a&4&&e.stateNode!=null&&(t=e.memoizedProps,Xf(e,t,n!==null?n.memoizedProps:t)),a&1024&&(Ah=!0,e.type!=="form"&&console.error("Unexpected host component type. Expected a form. This is a bug in React."));break;case 6:if(un(t,e),rn(e),a&4){if(e.stateNode===null)throw Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");a=e.memoizedProps,n=n!==null?n.memoizedProps:a,t=e.stateNode;try{re(e,xT,t,n,a)}catch(O){Ke(e,e.return,O)}}break;case 3:if(l=ma(),Zc=null,o=Vn,Vn=Fr(t.containerInfo),un(t,e),Vn=o,rn(e),a&4&&n!==null&&n.memoizedState.isDehydrated)try{re(e,LT,t.containerInfo)}catch(O){Ke(e,e.return,O)}Ah&&(Ah=!1,eg(e)),t.effectDuration+=Tr(l);break;case 4:a=Vn,Vn=Fr(e.stateNode.containerInfo),un(t,e),rn(e),Vn=a;break;case 12:a=ma(),un(t,e),rn(e),e.stateNode.effectDuration+=Bi(a);break;case 13:un(t,e),rn(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Mh=ea()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,If(e,a)));break;case 22:l=e.memoizedState!==null;var r=n!==null&&n.memoizedState!==null,d=Ma,p=pt;if(Ma=d||l,pt=p||r,un(t,e),pt=p,Ma=d,rn(e),a&8192)e:for(t=e.stateNode,t._visibility=l?t._visibility&-2:t._visibility|ah,l&&(n===null||r||Ma||pt||jl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){r=n=t;try{o=r.stateNode,l?re(r,_T,o):re(r,UT,r.stateNode,r.memoizedProps)}catch(O){Ke(r,r.return,O)}}}else if(t.tag===6){if(n===null){r=t;try{u=r.stateNode,l?re(r,zT,u):re(r,HT,u,r.memoizedProps)}catch(O){Ke(r,r.return,O)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,If(e,n))));break;case 19:un(t,e),rn(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,If(e,a)));break;case 30:break;case 21:break;default:un(t,e),rn(e)}}function rn(e){var t=e.flags;if(t&2){try{re(e,oT,e)}catch(n){Ke(e,e.return,n)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function eg(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;eg(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Sa(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Jy(e,t.alternate,t),t=t.sibling}function tg(e){switch(e.tag){case 0:case 11:case 14:case 15:Vf(e,e.return,jt),jl(e);break;case 1:Jn(e,e.return);var t=e.stateNode;typeof t.componentWillUnmount=="function"&&Gy(e,e.return,t),jl(e);break;case 27:re(e,nu,e.stateNode);case 26:case 5:Jn(e,e.return),jl(e);break;case 22:e.memoizedState===null&&jl(e);break;case 30:jl(e);break;default:jl(e)}}function jl(e){for(e=e.child;e!==null;)tg(e),e=e.sibling}function ng(e,t,n,a){var l=n.flags;switch(n.tag){case 0:case 11:case 15:Ta(e,n,a),qy(n,jt);break;case 1:if(Ta(e,n,a),t=n.stateNode,typeof t.componentDidMount=="function"&&re(n,gh,n,t),t=n.updateQueue,t!==null){e=n.stateNode;try{re(n,XS,t,e)}catch(o){Ke(n,n.return,o)}}a&&l&64&&Vy(n),Ji(n,n.return);break;case 27:Iy(n);case 26:case 5:Ta(e,n,a),a&&t===null&&l&4&&Qy(n),Ji(n,n.return);break;case 12:if(a&&l&4){l=ma(),Ta(e,n,a),a=n.stateNode,a.effectDuration+=Bi(l);try{re(n,Xy,n,t,Tc,a.effectDuration)}catch(o){Ke(n,n.return,o)}}else Ta(e,n,a);break;case 13:Ta(e,n,a),a&&l&4&&Wy(e,n);break;case 22:n.memoizedState===null&&Ta(e,n,a),Ji(n,n.return);break;case 30:break;default:Ta(e,n,a)}}function Ta(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;)ng(e,t.alternate,t,n),t=t.sibling}function Jf(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&Ml(e),n!=null&&Li(n))}function Kf(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(Ml(t),e!=null&&Li(e))}function Kn(e,t,n,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ag(e,t,n,a),t=t.sibling}function ag(e,t,n,a){var l=t.flags;switch(t.tag){case 0:case 11:case 15:Kn(e,t,n,a),l&2048&&$y(t,Rt|xn);break;case 1:Kn(e,t,n,a);break;case 3:var o=ma();Kn(e,t,n,a),l&2048&&(n=null,t.alternate!==null&&(n=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==n&&(Ml(t),n!=null&&Li(n))),e.passiveEffectDuration+=Tr(o);break;case 12:if(l&2048){l=ma(),Kn(e,t,n,a),e=t.stateNode,e.passiveEffectDuration+=Bi(l);try{re(t,lT,t,t.alternate,Tc,e.passiveEffectDuration)}catch(r){Ke(t,t.return,r)}}else Kn(e,t,n,a);break;case 13:Kn(e,t,n,a);break;case 23:break;case 22:o=t.stateNode;var u=t.alternate;t.memoizedState!==null?o._visibility&Ll?Kn(e,t,n,a):Ki(e,t):o._visibility&Ll?Kn(e,t,n,a):(o._visibility|=Ll,Eo(e,t,n,a,(t.subtreeFlags&10256)!==0)),l&2048&&Jf(u,t);break;case 24:Kn(e,t,n,a),l&2048&&Kf(t.alternate,t);break;default:Kn(e,t,n,a)}}function Eo(e,t,n,a,l){for(l=l&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;)lg(e,t,n,a,l),t=t.sibling}function lg(e,t,n,a,l){var o=t.flags;switch(t.tag){case 0:case 11:case 15:Eo(e,t,n,a,l),$y(t,Rt);break;case 23:break;case 22:var u=t.stateNode;t.memoizedState!==null?u._visibility&Ll?Eo(e,t,n,a,l):Ki(e,t):(u._visibility|=Ll,Eo(e,t,n,a,l)),l&&o&2048&&Jf(t.alternate,t);break;case 24:Eo(e,t,n,a,l),l&&o&2048&&Kf(t.alternate,t);break;default:Eo(e,t,n,a,l)}}function Ki(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,a=t,l=a.flags;switch(a.tag){case 22:Ki(n,a),l&2048&&Jf(a.alternate,a);break;case 24:Ki(n,a),l&2048&&Kf(a.alternate,a);break;default:Ki(n,a)}t=t.sibling}}function Ro(e){if(e.subtreeFlags&zu)for(e=e.child;e!==null;)og(e),e=e.sibling}function og(e){switch(e.tag){case 26:Ro(e),e.flags&zu&&e.memoizedState!==null&&GT(Vn,e.memoizedState,e.memoizedProps);break;case 5:Ro(e);break;case 3:case 4:var t=Vn;Vn=Fr(e.stateNode.containerInfo),Ro(e),Vn=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=zu,zu=16777216,Ro(e),zu=t):Ro(e));break;default:Ro(e)}}function ig(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Pi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];xt=a,cg(a,e)}ig(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ug(e),e=e.sibling}function ug(e){switch(e.tag){case 0:case 11:case 15:Pi(e),e.flags&2048&&Gf(e,e.return,Rt|xn);break;case 3:var t=ma();Pi(e),e.stateNode.passiveEffectDuration+=Tr(t);break;case 12:t=ma(),Pi(e),e.stateNode.passiveEffectDuration+=Bi(t);break;case 22:t=e.stateNode,e.memoizedState!==null&&t._visibility&Ll&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Zr(e)):Pi(e);break;default:Pi(e)}}function Zr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];xt=a,cg(a,e)}ig(e)}for(e=e.child;e!==null;)rg(e),e=e.sibling}function rg(e){switch(e.tag){case 0:case 11:case 15:Gf(e,e.return,Rt),Zr(e);break;case 22:var t=e.stateNode;t._visibility&Ll&&(t._visibility&=-3,Zr(e));break;default:Zr(e)}}function cg(e,t){for(;xt!==null;){var n=xt,a=n;switch(a.tag){case 0:case 11:case 15:Gf(a,t,Rt);break;case 23:case 22:a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(a=a.memoizedState.cachePool.pool,a!=null&&Ml(a));break;case 24:Li(a.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,xt=a;else e:for(n=e;xt!==null;){a=xt;var l=a.sibling,o=a.return;if(Ky(a),a===n){xt=null;break e}if(l!==null){l.return=o,xt=l;break e}xt=o}}}function rT(){ER.forEach(function(e){return e()})}function sg(){var e=typeof IS_REACT_ACT_ENVIRONMENT<"u"?IS_REACT_ACT_ENVIRONMENT:void 0;return e||x.actQueue===null||console.error("The current testing environment is not configured to support act(...)"),e}function cn(e){if((Ge&Qt)!==dn&&ke!==0)return ke&-ke;var t=x.T;return t!==null?(t._updatedFibers||(t._updatedFibers=new Set),t._updatedFibers.add(e),e=$l,e!==0?e:od()):Ci()}function fg(){pn===0&&(pn=(ke&536870912)===0||qe?Ue():536870912);var e=Mn.current;return e!==null&&(e.flags|=32),pn}function ht(e,t,n){if(si&&console.error("useInsertionEffect must not schedule updates."),jh&&(jc=!0),(e===tt&&(Xe===Zl||Xe===Il)||e.cancelPendingCommit!==null)&&(Oo(e,0),Ia(e,ke,pn,!1)),Ba(e,n),(Ge&Qt)!==0&&e===tt){if(aa)switch(t.tag){case 0:case 11:case 15:e=He&&X(He)||"Unknown",A1.has(e)||(A1.add(e),t=X(t)||"Unknown",console.error("Cannot update a component (`%s`) while rendering a different component (`%s`). To locate the bad setState() call inside `%s`, follow the stack trace as described in https://react.dev/link/setstate-in-render",t,e,e));break;case 1:R1||(console.error("Cannot update during an existing state transition (such as within `render`). Render methods should be a pure function of props and state."),R1=!0)}}else Bn&&Ai(e,t,n),vT(t),e===tt&&((Ge&Qt)===dn&&(cl|=n),ft===Ql&&Ia(e,ke,pn,!1)),Pn(e)}function dg(e,t,n){if((Ge&(Qt|Gn))!==dn)throw Error("Should not already be working.");var a=!n&&(t&124)===0&&(t&e.expiredLanes)===0||La(e,t),l=a?sT(e,t):Ff(e,t,!0),o=a;do{if(l===_a){ri&&!a&&Ia(e,t,0,!1);break}else{if(n=e.current.alternate,o&&!cT(n)){l=Ff(e,t,!1),o=!1;continue}if(l===ii){if(o=t,e.errorRecoveryDisabledLanes&o)var u=0;else u=e.pendingLanes&-536870913,u=u!==0?u:u&536870912?536870912:0;if(u!==0){t=u;e:{l=e;var r=u;u=Lu;var d=l.current.memoizedState.isDehydrated;if(d&&(Oo(l,r).flags|=256),r=Ff(l,r,!1),r!==ii){if(xh&&!d){l.errorRecoveryDisabledLanes|=o,cl|=o,l=Ql;break e}l=Zt,Zt=u,l!==null&&(Zt===null?Zt=l:Zt.push.apply(Zt,l))}l=r}if(o=!1,l!==ii)continue}}if(l===Hu){Oo(e,0),Ia(e,t,0,!0);break}e:{switch(a=e,l){case _a:case Hu:throw Error("Root did not complete. This is a bug in React.");case Ql:if((t&4194048)!==t)break;case zc:Ia(a,t,pn,!ul);break e;case ii:Zt=null;break;case Oh:case m1:break;default:throw Error("Unknown root exit status.")}if(x.actQueue!==null)ed(a,n,t,Zt,Bu,Uc,pn,cl,Jl);else{if((t&62914560)===t&&(o=Mh+g1-ea(),10<o)){if(Ia(a,t,pn,!ul),fa(a,0,!0)!==0)break e;a.timeoutHandle=z1(hg.bind(null,a,n,Zt,Bu,Uc,t,pn,cl,Jl,ul,l,CR,b0,0),o);break e}hg(a,n,Zt,Bu,Uc,t,pn,cl,Jl,ul,l,AR,b0,0)}}}break}while(!0);Pn(e)}function hg(e,t,n,a,l,o,u,r,d,p,O,D,A,_){if(e.timeoutHandle=eo,D=t.subtreeFlags,(D&8192||(D&16785408)===16785408)&&(Xu={stylesheets:null,count:0,unsuspend:VT},og(t),D=XT(),D!==null)){e.cancelPendingCommit=D(ed.bind(null,e,t,o,n,a,l,u,r,d,O,OR,A,_)),Ia(e,o,u,!p);return}ed(e,t,o,n,a,l,u,r,d)}function cT(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var l=n[a],o=l.getSnapshot;l=l.value;try{if(!Vt(o(),l))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ia(e,t,n,a){t&=~Dh,t&=~cl,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var l=t;0<l;){var o=31-qt(l),u=1<<o;a[o]=-1,l&=~u}n!==0&&Ei(e,n,t)}function Ao(){return(Ge&(Qt|Gn))===dn?(eu(0),!1):!0}function Pf(){if(He!==null){if(Xe===tn)var e=He.return;else e=He,vr(),rf(e),Fo=null,Mu=0,e=He;for(;e!==null;)Yy(e.alternate,e),e=e.return;He=null}}function Oo(e,t){var n=e.timeoutHandle;n!==eo&&(e.timeoutHandle=eo,BR(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Pf(),tt=e,He=n=ha(e.current,null),ke=t,Xe=tn,hn=null,ul=!1,ri=La(e,t),xh=!1,ft=_a,Jl=pn=Dh=cl=rl=0,Zt=Lu=null,Uc=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var l=31-qt(a),o=1<<l;t|=e[l],a&=~o}return ra=t,pr(),t=g0(),1e3<t-y0&&(x.recentlyCreatedOwnerStacks=0,y0=t),qn.discardPendingWarnings(),n}function pg(e,t){Te=null,x.H=Mc,x.getCurrentStack=null,aa=!1,fn=null,t===Cu||t===Oc?(t=qm(),Xe=Nu):t===E0?(t=qm(),Xe=y1):Xe=t===i1?wh:t!==null&&typeof t=="object"&&typeof t.then=="function"?ui:ju,hn=t;var n=He;if(n===null)ft=Hu,$r(e,on(t,e.current));else switch(n.mode&Ht&&Ks(n),ye(),Xe){case ju:J!==null&&typeof J.markComponentErrored=="function"&&J.markComponentErrored(n,t,ke);break;case Zl:case Il:case Nu:case ui:case ku:J!==null&&typeof J.markComponentSuspended=="function"&&J.markComponentSuspended(n,t,ke)}}function mg(){var e=x.H;return x.H=Mc,e===null?Mc:e}function yg(){var e=x.A;return x.A=TR,e}function Wf(){ft=Ql,ul||(ke&4194048)!==ke&&Mn.current!==null||(ri=!0),(rl&134217727)===0&&(cl&134217727)===0||tt===null||Ia(tt,ke,pn,!1)}function Ff(e,t,n){var a=Ge;Ge|=Qt;var l=mg(),o=yg();if(tt!==e||ke!==t){if(Bn){var u=e.memoizedUpdaters;0<u.size&&(Fi(e,ke),u.clear()),Oi(e,t)}Bu=null,Oo(e,t)}et(t),t=!1,u=ft;e:do try{if(Xe!==tn&&He!==null){var r=He,d=hn;switch(Xe){case wh:Pf(),u=zc;break e;case Nu:case Zl:case Il:case ui:Mn.current===null&&(t=!0);var p=Xe;if(Xe=tn,hn=null,Co(e,r,d,p),n&&ri){u=_a;break e}break;default:p=Xe,Xe=tn,hn=null,Co(e,r,d,p)}}gg(),u=ft;break}catch(O){pg(e,O)}while(!0);return t&&e.shellSuspendCounter++,vr(),Ge=a,x.H=l,x.A=o,Y(),He===null&&(tt=null,ke=0,pr()),u}function gg(){for(;He!==null;)vg(He)}function sT(e,t){var n=Ge;Ge|=Qt;var a=mg(),l=yg();if(tt!==e||ke!==t){if(Bn){var o=e.memoizedUpdaters;0<o.size&&(Fi(e,ke),o.clear()),Oi(e,t)}Bu=null,Hc=ea()+v1,Oo(e,t)}else ri=La(e,t);et(t);e:do try{if(Xe!==tn&&He!==null)t:switch(t=He,o=hn,Xe){case ju:Xe=tn,hn=null,Co(e,t,o,ju);break;case Zl:case Il:if(Bm(o)){Xe=tn,hn=null,bg(t);break}t=function(){Xe!==Zl&&Xe!==Il||tt!==e||(Xe=ku),Pn(e)},o.then(t,t);break e;case Nu:Xe=ku;break e;case y1:Xe=Ch;break e;case ku:Bm(o)?(Xe=tn,hn=null,bg(t)):(Xe=tn,hn=null,Co(e,t,o,ku));break;case Ch:var u=null;switch(He.tag){case 26:u=He.memoizedState;case 5:case 27:var r=He;if(!u||rv(u)){Xe=tn,hn=null;var d=r.sibling;if(d!==null)He=d;else{var p=r.return;p!==null?(He=p,Ir(p)):He=null}break t}break;default:console.error("Unexpected type of fiber triggered a suspensey commit. This is a bug in React.")}Xe=tn,hn=null,Co(e,t,o,Ch);break;case ui:Xe=tn,hn=null,Co(e,t,o,ui);break;case wh:Pf(),ft=zc;break e;default:throw Error("Unexpected SuspendedReason. This is a bug in React.")}x.actQueue!==null?gg():fT();break}catch(O){pg(e,O)}while(!0);return vr(),x.H=a,x.A=l,Ge=n,He!==null?(J!==null&&typeof J.markRenderYielded=="function"&&J.markRenderYielded(),_a):(Y(),tt=null,ke=0,pr(),ft)}function fT(){for(;He!==null&&!uE();)vg(He)}function vg(e){var t=e.alternate;(e.mode&Ht)!==lt?(Js(e),t=re(e,$f,t,e,ra),Ks(e)):t=re(e,$f,t,e,ra),e.memoizedProps=e.pendingProps,t===null?Ir(e):He=t}function bg(e){var t=re(e,dT,e);e.memoizedProps=e.pendingProps,t===null?Ir(e):He=t}function dT(e){var t=e.alternate,n=(e.mode&Ht)!==lt;switch(n&&Js(e),e.tag){case 15:case 0:t=zy(t,e,e.pendingProps,e.type,void 0,ke);break;case 11:t=zy(t,e,e.pendingProps,e.type.render,e.ref,ke);break;case 5:rf(e);default:Yy(t,e),e=He=xm(e,ra),t=$f(t,e,ra)}return n&&Ks(e),t}function Co(e,t,n,a){vr(),rf(t),Fo=null,Mu=0;var l=t.return;try{if(PS(e,l,t,n,ke)){ft=Hu,$r(e,on(n,e.current)),He=null;return}}catch(o){if(l!==null)throw He=l,o;ft=Hu,$r(e,on(n,e.current)),He=null;return}t.flags&32768?(qe||a===ju?e=!0:ri||(ke&536870912)!==0?e=!1:(ul=e=!0,(a===Zl||a===Il||a===Nu||a===ui)&&(a=Mn.current,a!==null&&a.tag===13&&(a.flags|=16384))),Sg(t,e)):Ir(t)}function Ir(e){var t=e;do{if((t.flags&32768)!==0){Sg(t,ul);return}var n=t.alternate;if(e=t.return,Js(t),n=re(t,FS,n,t,ra),(t.mode&Ht)!==lt&&jm(t),n!==null){He=n;return}if(t=t.sibling,t!==null){He=t;return}He=t=e}while(t!==null);ft===_a&&(ft=m1)}function Sg(e,t){do{var n=eT(e.alternate,e);if(n!==null){n.flags&=32767,He=n;return}if((e.mode&Ht)!==lt){jm(e),n=e.actualDuration;for(var a=e.child;a!==null;)n+=a.actualDuration,a=a.sibling;e.actualDuration=n}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){He=e;return}He=e=n}while(e!==null);ft=zc,He=null}function ed(e,t,n,a,l,o,u,r,d){e.cancelPendingCommit=null;do Wi();while(Nt!==Kl);if(qn.flushLegacyContextWarning(),qn.flushPendingUnsafeLifecycleWarnings(),(Ge&(Qt|Gn))!==dn)throw Error("Should not already be working.");if(J!==null&&typeof J.markCommitStarted=="function"&&J.markCommitStarted(n),t===null)ae();else{if(n===0&&console.error("finishedLanes should not be empty during a commit. This is a bug in React."),t===e.current)throw Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");if(o=t.lanes|t.childLanes,o|=lh,lr(e,n,o,u,r,d),e===tt&&(He=tt=null,ke=0),ci=t,fl=e,dl=n,zh=o,Uh=l,E1=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,gT(zo,function(){return Og(),null})):(e.callbackNode=null,e.callbackPriority=0),Tc=Io(),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=x.T,x.T=null,l=Ve.p,Ve.p=Tn,u=Ge,Ge|=Gn;try{iT(e,t,n)}finally{Ge=u,Ve.p=l,x.T=a}}Nt=b1,Tg(),Eg(),Rg()}}function Tg(){if(Nt===b1){Nt=Kl;var e=fl,t=ci,n=dl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=x.T,x.T=null;var l=Ve.p;Ve.p=Tn;var o=Ge;Ge|=Gn;try{li=n,oi=e,Fy(t,e),oi=li=null,n=Gh;var u=Rm(e.containerInfo),r=n.focusedElem,d=n.selectionRange;if(u!==r&&r&&r.ownerDocument&&Em(r.ownerDocument.documentElement,r)){if(d!==null&&js(r)){var p=d.start,O=d.end;if(O===void 0&&(O=p),"selectionStart"in r)r.selectionStart=p,r.selectionEnd=Math.min(O,r.value.length);else{var D=r.ownerDocument||document,A=D&&D.defaultView||window;if(A.getSelection){var _=A.getSelection(),ee=r.textContent.length,ge=Math.min(d.start,ee),nt=d.end===void 0?ge:Math.min(d.end,ee);!_.extend&&ge>nt&&(u=nt,nt=ge,ge=u);var Be=Tm(r,ge),m=Tm(r,nt);if(Be&&m&&(_.rangeCount!==1||_.anchorNode!==Be.node||_.anchorOffset!==Be.offset||_.focusNode!==m.node||_.focusOffset!==m.offset)){var y=D.createRange();y.setStart(Be.node,Be.offset),_.removeAllRanges(),ge>nt?(_.addRange(y),_.extend(m.node,m.offset)):(y.setEnd(m.node,m.offset),_.addRange(y))}}}}for(D=[],_=r;_=_.parentNode;)_.nodeType===1&&D.push({element:_,left:_.scrollLeft,top:_.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<D.length;r++){var v=D[r];v.element.scrollLeft=v.left,v.element.scrollTop=v.top}}Kc=!!Vh,Gh=Vh=null}finally{Ge=o,Ve.p=l,x.T=a}}e.current=t,Nt=S1}}function Eg(){if(Nt===S1){Nt=Kl;var e=fl,t=ci,n=dl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=x.T,x.T=null;var l=Ve.p;Ve.p=Tn;var o=Ge;Ge|=Gn;try{J!==null&&typeof J.markLayoutEffectsStarted=="function"&&J.markLayoutEffectsStarted(n),li=n,oi=e,Jy(e,t.alternate,t),oi=li=null,J!==null&&typeof J.markLayoutEffectsStopped=="function"&&J.markLayoutEffectsStopped()}finally{Ge=o,Ve.p=l,x.T=a}}Nt=T1}}function Rg(){if(Nt===wR||Nt===T1){Nt=Kl,rE();var e=fl,t=ci,n=dl,a=E1,l=(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0;l?Nt=_h:(Nt=Kl,ci=fl=null,Ag(e,e.pendingLanes),Pl=0,qu=null);var o=e.pendingLanes;if(o===0&&(sl=null),l||Dg(e),l=fo(n),t=t.stateNode,kt&&typeof kt.onCommitFiberRoot=="function")try{var u=(t.current.flags&128)===128;switch(l){case Tn:var r=jd;break;case na:r=Nd;break;case Aa:r=zo;break;case fc:r=kd;break;default:r=zo}kt.onCommitFiberRoot(Uo,t,r,u)}catch(D){ta||(ta=!0,console.error("React instrumentation encountered an error: %s",D))}if(Bn&&e.memoizedUpdaters.clear(),rT(),a!==null){u=x.T,r=Ve.p,Ve.p=Tn,x.T=null;try{var d=e.onRecoverableError;for(t=0;t<a.length;t++){var p=a[t],O=hT(p.stack);re(p.source,d,p.value,O)}}finally{x.T=u,Ve.p=r}}(dl&3)!==0&&Wi(),Pn(e),o=e.pendingLanes,(n&4194090)!==0&&(o&42)!==0?(Rc=!0,e===Hh?Yu++:(Yu=0,Hh=e)):Yu=0,eu(0),ae()}}function hT(e){return e={componentStack:e},Object.defineProperty(e,"digest",{get:function(){console.error('You are accessing "digest" from the errorInfo object passed to onRecoverableError. This property is no longer provided as part of errorInfo but can be accessed as a property of the Error instance itself.')}}),e}function Ag(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Li(t)))}function Wi(e){return Tg(),Eg(),Rg(),Og()}function Og(){if(Nt!==_h)return!1;var e=fl,t=zh;zh=0;var n=fo(dl),a=Aa>n?Aa:n;n=x.T;var l=Ve.p;try{Ve.p=a,x.T=null,a=Uh,Uh=null;var o=fl,u=dl;if(Nt=Kl,ci=fl=null,dl=0,(Ge&(Qt|Gn))!==dn)throw Error("Cannot flush passive effects while already rendering.");jh=!0,jc=!1,J!==null&&typeof J.markPassiveEffectsStarted=="function"&&J.markPassiveEffectsStarted(u);var r=Ge;if(Ge|=Gn,ug(o.current),ag(o,o.current,u,a),J!==null&&typeof J.markPassiveEffectsStopped=="function"&&J.markPassiveEffectsStopped(),Dg(o),Ge=r,eu(0,!1),jc?o===qu?Pl++:(Pl=0,qu=o):Pl=0,jc=jh=!1,kt&&typeof kt.onPostCommitFiberRoot=="function")try{kt.onPostCommitFiberRoot(Uo,o)}catch(p){ta||(ta=!0,console.error("React instrumentation encountered an error: %s",p))}var d=o.current.stateNode;return d.effectDuration=0,d.passiveEffectDuration=0,!0}finally{Ve.p=l,x.T=n,Ag(e,t)}}function Cg(e,t,n){t=on(n,t),t=_f(e.stateNode,t,2),e=Ga(e,t,2),e!==null&&(Ba(e,2),Pn(e))}function Ke(e,t,n){if(si=!1,e.tag===3)Cg(e,e,n);else{for(;t!==null;){if(t.tag===3){Cg(t,e,n);return}if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(sl===null||!sl.has(a))){e=on(n,e),n=zf(2),a=Ga(t,n,2),a!==null&&(Uf(n,a,t,e),Ba(a,2),Pn(a));return}}t=t.return}console.error(`Internal React error: Attempted to capture a commit phase error inside a detached tree. This indicates a bug in React. Potential causes include deleting the same fiber more than once, committing an already-finished tree, or an inconsistent return pointer.

Error message:

%s`,n)}}function td(e,t,n){var a=e.pingCache;if(a===null){a=e.pingCache=new RR;var l=new Set;a.set(t,l)}else l=a.get(t),l===void 0&&(l=new Set,a.set(t,l));l.has(n)||(xh=!0,l.add(n),a=pT.bind(null,e,t,n),Bn&&Fi(e,n),t.then(a,a))}function pT(e,t,n){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,sg()&&x.actQueue===null&&console.error(`A suspended resource finished loading inside a test, but the event was not wrapped in act(...).

When testing, code that resolves suspended data should be wrapped into act(...):

act(() => {
  /* finish loading suspended data */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act`),tt===e&&(ke&n)===n&&(ft===Ql||ft===Oh&&(ke&62914560)===ke&&ea()-Mh<g1?(Ge&Qt)===dn&&Oo(e,0):Dh|=n,Jl===ke&&(Jl=0)),Pn(e)}function wg(e,t){t===0&&(t=bl()),e=Jt(e,t),e!==null&&(Ba(e,t),Pn(e))}function mT(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),wg(e,n)}function yT(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error("Pinged unknown suspense boundary type. This is probably a bug in React.")}a!==null&&a.delete(t),wg(e,n)}function nd(e,t,n){if((t.subtreeFlags&67117056)!==0)for(t=t.child;t!==null;){var a=e,l=t,o=l.type===ic;o=n||o,l.tag!==22?l.flags&67108864?o&&re(l,xg,a,l,(l.mode&h0)===lt):nd(a,l,o):l.memoizedState===null&&(o&&l.flags&8192?re(l,xg,a,l):l.subtreeFlags&67108864&&re(l,nd,a,l,o)),t=t.sibling}}function xg(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:!0;Q(!0);try{tg(t),n&&rg(t),ng(e,t.alternate,t,!1),n&&lg(e,t,0,null,!1,0)}finally{Q(!1)}}function Dg(e){var t=!0;e.current.mode&(Bt|Yn)||(t=!1),nd(e,e.current,t)}function Mg(e){if((Ge&Qt)===dn){var t=e.tag;if(t===3||t===1||t===0||t===11||t===14||t===15){if(t=X(e)||"ReactComponent",Nc!==null){if(Nc.has(t))return;Nc.add(t)}else Nc=new Set([t]);re(e,function(){console.error("Can't perform a React state update on a component that hasn't mounted yet. This indicates that you have a side-effect in your render function that asynchronously later calls tries to update the component. Move this work to useEffect instead.")})}}}function Fi(e,t){Bn&&e.memoizedUpdaters.forEach(function(n){Ai(e,n,t)})}function gT(e,t){var n=x.actQueue;return n!==null?(n.push(t),MR):Hd(e,t)}function vT(e){sg()&&x.actQueue===null&&re(e,function(){console.error(`An update to %s inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act`,X(e))})}function Pn(e){e!==fi&&e.next===null&&(fi===null?kc=fi=e:fi=fi.next=e),Lc=!0,x.actQueue!==null?kh||(kh=!0,Hg()):Nh||(Nh=!0,Hg())}function eu(e,t){if(!Lh&&Lc){Lh=!0;do for(var n=!1,a=kc;a!==null;){if(e!==0){var l=a.pendingLanes;if(l===0)var o=0;else{var u=a.suspendedLanes,r=a.pingedLanes;o=(1<<31-qt(42|e)+1)-1,o&=l&~(u&~r),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(n=!0,Ug(a,o))}else o=ke,o=fa(a,a===tt?o:0,a.cancelPendingCommit!==null||a.timeoutHandle!==eo),(o&3)===0||La(a,o)||(n=!0,Ug(a,o));a=a.next}while(n);Lh=!1}}function bT(){ad()}function ad(){Lc=kh=Nh=!1;var e=0;Wl!==0&&(AT()&&(e=Wl),Wl=0);for(var t=ea(),n=null,a=kc;a!==null;){var l=a.next,o=_g(a,t);o===0?(a.next=null,n===null?kc=l:n.next=l,l===null&&(fi=n)):(n=a,(e!==0||(o&3)!==0)&&(Lc=!0)),a=l}eu(e)}function _g(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes&-62914561;0<o;){var u=31-qt(o),r=1<<u,d=l[u];d===-1?((r&n)===0||(r&a)!==0)&&(l[u]=Ts(r,t)):d<=t&&(e.expiredLanes|=r),o&=~r}if(t=tt,n=ke,n=fa(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==eo),a=e.callbackNode,n===0||e===t&&(Xe===Zl||Xe===Il)||e.cancelPendingCommit!==null)return a!==null&&ld(a),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||La(e,n)){if(t=n&-n,t!==e.callbackPriority||x.actQueue!==null&&a!==Bh)ld(a);else return t;switch(fo(n)){case Tn:case na:n=Nd;break;case Aa:n=zo;break;case fc:n=kd;break;default:n=zo}return a=zg.bind(null,e),x.actQueue!==null?(x.actQueue.push(a),n=Bh):n=Hd(n,a),e.callbackPriority=t,e.callbackNode=n,t}return a!==null&&ld(a),e.callbackPriority=2,e.callbackNode=null,2}function zg(e,t){if(Rc=Ec=!1,Nt!==Kl&&Nt!==_h)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Wi()&&e.callbackNode!==n)return null;var a=ke;return a=fa(e,e===tt?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==eo),a===0?null:(dg(e,a,t),_g(e,ea()),e.callbackNode!=null&&e.callbackNode===n?zg.bind(null,e):null)}function Ug(e,t){if(Wi())return null;Ec=Rc,Rc=!1,dg(e,t,!0)}function ld(e){e!==Bh&&e!==null&&iE(e)}function Hg(){x.actQueue!==null&&x.actQueue.push(function(){return ad(),null}),YR(function(){(Ge&(Qt|Gn))!==dn?Hd(jd,bT):ad()})}function od(){return Wl===0&&(Wl=Ue()),Wl}function jg(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:(N(e,"action"),_i(""+e))}function Ng(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function ST(e,t,n,a,l){if(t==="submit"&&n&&n.stateNode===l){var o=jg((l[Wt]||null).action),u=a.submitter;u&&(t=(t=u[Wt]||null)?jg(t.formAction):u.getAttribute("formAction"),t!==null&&(o=t,u=null));var r=new yc("action","action",null,a,l);e.push({event:r,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Wl!==0){var d=u?Ng(l,u):new FormData(l),p={pending:!0,data:d,method:l.method,action:o};Object.freeze(p),Af(n,p,null,d)}}else typeof o=="function"&&(r.preventDefault(),d=u?Ng(l,u):new FormData(l),p={pending:!0,data:d,method:l.method,action:o},Object.freeze(p),Af(n,p,o,d))},currentTarget:l}]})}}function Jr(e,t,n){e.currentTarget=n;try{t(e)}catch(a){Sh(a)}e.currentTarget=null}function kg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var a=e[n];e:{var l=void 0,o=a.event;if(a=a.listeners,t)for(var u=a.length-1;0<=u;u--){var r=a[u],d=r.instance,p=r.currentTarget;if(r=r.listener,d!==l&&o.isPropagationStopped())break e;d!==null?re(d,Jr,o,r,p):Jr(o,r,p),l=d}else for(u=0;u<a.length;u++){if(r=a[u],d=r.instance,p=r.currentTarget,r=r.listener,d!==l&&o.isPropagationStopped())break e;d!==null?re(d,Jr,o,r,p):Jr(o,r,p),l=d}}}}function Le(e,t){Yh.has(e)||console.error('Did not expect a listenToNonDelegatedEvent() call for "%s". This is a bug in React. Please file an issue.',e);var n=t[Ld];n===void 0&&(n=t[Ld]=new Set);var a=e+"__bubble";n.has(a)||(Lg(t,e,2,!1),n.add(a))}function id(e,t,n){Yh.has(e)&&!t&&console.error('Did not expect a listenToNativeEvent() call for "%s" in the bubble phase. This is a bug in React. Please file an issue.',e);var a=0;t&&(a|=4),Lg(n,e,a,t)}function ud(e){if(!e[Bc]){e[Bc]=!0,Tv.forEach(function(n){n!=="selectionchange"&&(Yh.has(n)||id(n,!1,e),id(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Bc]||(t[Bc]=!0,id("selectionchange",!1,t))}}function Lg(e,t,n,a){switch(hv(t)){case Tn:var l=KT;break;case na:l=PT;break;default:l=Ed}n=l.bind(null,t,n,e),l=void 0,!Qd||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),a?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function rd(e,t,n,a,l){var o=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var u=a.tag;if(u===3||u===4){var r=a.stateNode.containerInfo;if(r===l)break;if(u===4)for(u=a.return;u!==null;){var d=u.tag;if((d===3||d===4)&&u.stateNode.containerInfo===l)return;u=u.return}for(;r!==null;){if(u=jn(r),u===null)return;if(d=u.tag,d===5||d===6||d===26||d===27){a=o=u;continue e}r=r.parentNode}}a=a.return}sm(function(){var p=o,O=Us(n),D=[];e:{var A=d0.get(e);if(A!==void 0){var _=yc,ee=e;switch(e){case"keypress":if(fr(n)===0)break e;case"keydown":case"keyup":_=ZE;break;case"focusin":ee="focus",_=Kd;break;case"focusout":ee="blur",_=Kd;break;case"beforeblur":case"afterblur":_=Kd;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":_=Wv;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":_=jE;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":_=KE;break;case r0:case c0:case s0:_=LE;break;case f0:_=WE;break;case"scroll":case"scrollend":_=UE;break;case"wheel":_=eR;break;case"copy":case"cut":case"paste":_=YE;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":_=e0;break;case"toggle":case"beforetoggle":_=nR}var ge=(t&4)!==0,nt=!ge&&(e==="scroll"||e==="scrollend"),Be=ge?A!==null?A+"Capture":null:A;ge=[];for(var m=p,y;m!==null;){var v=m;if(y=v.stateNode,v=v.tag,v!==5&&v!==26&&v!==27||y===null||Be===null||(v=zi(m,Be),v!=null&&ge.push(tu(m,v,y))),nt)break;m=m.return}0<ge.length&&(A=new _(A,ee,null,n,O),D.push({event:A,listeners:ge}))}}if((t&7)===0){e:{if(A=e==="mouseover"||e==="pointerover",_=e==="mouseout"||e==="pointerout",A&&n!==fu&&(ee=n.relatedTarget||n.fromElement)&&(jn(ee)||ee[tl]))break e;if((_||A)&&(A=O.window===O?O:(A=O.ownerDocument)?A.defaultView||A.parentWindow:window,_?(ee=n.relatedTarget||n.toElement,_=p,ee=ee?jn(ee):null,ee!==null&&(nt=W(ee),ge=ee.tag,ee!==nt||ge!==5&&ge!==27&&ge!==6)&&(ee=null)):(_=null,ee=p),_!==ee)){if(ge=Wv,v="onMouseLeave",Be="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(ge=e0,v="onPointerLeave",Be="onPointerEnter",m="pointer"),nt=_==null?A:qa(_),y=ee==null?A:qa(ee),A=new ge(v,m+"leave",_,n,O),A.target=nt,A.relatedTarget=y,v=null,jn(O)===p&&(ge=new ge(Be,m+"enter",ee,n,O),ge.target=y,ge.relatedTarget=nt,v=ge),nt=v,_&&ee)t:{for(ge=_,Be=ee,m=0,y=ge;y;y=wo(y))m++;for(y=0,v=Be;v;v=wo(v))y++;for(;0<m-y;)ge=wo(ge),m--;for(;0<y-m;)Be=wo(Be),y--;for(;m--;){if(ge===Be||Be!==null&&ge===Be.alternate)break t;ge=wo(ge),Be=wo(Be)}ge=null}else ge=null;_!==null&&Bg(D,A,_,ge,!1),ee!==null&&nt!==null&&Bg(D,nt,ee,ge,!0)}}e:{if(A=p?qa(p):window,_=A.nodeName&&A.nodeName.toLowerCase(),_==="select"||_==="input"&&A.type==="file")var z=gm;else if(mm(A))if(i0)z=YS;else{z=LS;var q=kS}else _=A.nodeName,!_||_.toLowerCase()!=="input"||A.type!=="checkbox"&&A.type!=="radio"?p&&Mi(p.elementType)&&(z=gm):z=BS;if(z&&(z=z(e,p))){ym(D,z,n,O);break e}q&&q(e,A,p),e==="focusout"&&p&&A.type==="number"&&p.memoizedProps.value!=null&&ws(A,"number",A.value)}switch(q=p?qa(p):window,e){case"focusin":(mm(q)||q.contentEditable==="true")&&(Yo=q,Wd=p,vu=null);break;case"focusout":vu=Wd=Yo=null;break;case"mousedown":Fd=!0;break;case"contextmenu":case"mouseup":case"dragend":Fd=!1,Am(D,n,O);break;case"selectionchange":if(iR)break;case"keydown":case"keyup":Am(D,n,O)}var Ee;if(Pd)e:{switch(e){case"compositionstart":var te="onCompositionStart";break e;case"compositionend":te="onCompositionEnd";break e;case"compositionupdate":te="onCompositionUpdate";break e}te=void 0}else Bo?hm(e,n)&&(te="onCompositionEnd"):e==="keydown"&&n.keyCode===t0&&(te="onCompositionStart");te&&(n0&&n.locale!=="ko"&&(Bo||te!=="onCompositionStart"?te==="onCompositionEnd"&&Bo&&(Ee=fm()):(nl=O,Zd="value"in nl?nl.value:nl.textContent,Bo=!0)),q=Kr(p,te),0<q.length&&(te=new Fv(te,e,null,n,O),D.push({event:te,listeners:q}),Ee?te.data=Ee:(Ee=pm(n),Ee!==null&&(te.data=Ee)))),(Ee=lR?US(e,n):HS(e,n))&&(te=Kr(p,"onBeforeInput"),0<te.length&&(q=new $E("onBeforeInput","beforeinput",null,n,O),D.push({event:q,listeners:te}),q.data=Ee)),ST(D,e,p,n,O)}kg(D,t)})}function tu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Kr(e,t){for(var n=t+"Capture",a=[];e!==null;){var l=e,o=l.stateNode;if(l=l.tag,l!==5&&l!==26&&l!==27||o===null||(l=zi(e,n),l!=null&&a.unshift(tu(e,l,o)),l=zi(e,t),l!=null&&a.push(tu(e,l,o))),e.tag===3)return a;e=e.return}return[]}function wo(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Bg(e,t,n,a,l){for(var o=t._reactName,u=[];n!==null&&n!==a;){var r=n,d=r.alternate,p=r.stateNode;if(r=r.tag,d!==null&&d===a)break;r!==5&&r!==26&&r!==27||p===null||(d=p,l?(p=zi(n,o),p!=null&&u.unshift(tu(n,p,d))):l||(p=zi(n,o),p!=null&&u.push(tu(n,p,d)))),n=n.return}u.length!==0&&e.push({event:t,listeners:u})}function cd(e,t){DS(e,t),e!=="input"&&e!=="textarea"&&e!=="select"||t==null||t.value!==null||Kv||(Kv=!0,e==="select"&&t.multiple?console.error("`value` prop on `%s` should not be null. Consider using an empty array when `multiple` is set to `true` to clear the component or `undefined` for uncontrolled components.",e):console.error("`value` prop on `%s` should not be null. Consider using an empty string to clear the component or `undefined` for uncontrolled components.",e));var n={registrationNameDependencies:Nl,possibleRegistrationNames:Bd};Mi(e)||typeof t.is=="string"||_S(e,t,n),t.contentEditable&&!t.suppressContentEditableWarning&&t.children!=null&&console.error("A component is `contentEditable` and contains `children` managed by React. It is now your responsibility to guarantee that none of those nodes are unexpectedly modified or duplicated. This is probably not intentional.")}function Ct(e,t,n,a){t!==n&&(n=Ja(n),Ja(t)!==n&&(a[e]=t))}function TT(e,t,n){t.forEach(function(a){n[$g(a)]=a==="style"?fd(e):e.getAttribute(a)})}function Wn(e,t){t===!1?console.error("Expected `%s` listener to be a function, instead got `false`.\n\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.",e,e,e):console.error("Expected `%s` listener to be a function, instead got a value of `%s` type.",e,typeof t)}function Yg(e,t){return e=e.namespaceURI===hc||e.namespaceURI===jo?e.ownerDocument.createElementNS(e.namespaceURI,e.tagName):e.ownerDocument.createElement(e.tagName),e.innerHTML=t,e.innerHTML}function Ja(e){return g(e)&&(console.error("The provided HTML markup uses a value of unsupported type %s. This value must be coerced to a string before using it here.",Se(e)),U(e)),(typeof e=="string"?e:""+e).replace(_R,`
`).replace(zR,"")}function qg(e,t){return t=Ja(t),Ja(e)===t}function Pr(){}function Pe(e,t,n,a,l,o){switch(n){case"children":typeof a=="string"?(sr(a,t,!1),t==="body"||t==="textarea"&&a===""||Di(e,a)):(typeof a=="number"||typeof a=="bigint")&&(sr(""+a,t,!1),t!=="body"&&Di(e,""+a));break;case"className":Ce(e,"class",a);break;case"tabIndex":Ce(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Ce(e,n,a);break;case"style":um(e,a,o);break;case"data":if(t!=="object"){Ce(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||n!=="href")){console.error(n==="src"?'An empty string ("") was passed to the %s attribute. This may cause the browser to download the whole page again over the network. To fix this, either do not render the element at all or pass null to %s instead of an empty string.':'An empty string ("") was passed to the %s attribute. To fix this, either do not render the element at all or pass null to %s instead of an empty string.',n,n),e.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}N(a,n),a=_i(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if(a!=null&&(t==="form"?n==="formAction"?console.error("You can only pass the formAction prop to <input> or <button>. Use the action prop on <form>."):typeof a=="function"&&(l.encType==null&&l.method==null||$c||($c=!0,console.error("Cannot specify a encType or method for a form that specifies a function as the action. React provides those automatically. They will get overridden.")),l.target==null||qc||(qc=!0,console.error("Cannot specify a target for a form that specifies a function as the action. The function will always be executed in the same window."))):t==="input"||t==="button"?n==="action"?console.error("You can only pass the action prop to <form>. Use the formAction prop on <input> or <button>."):t!=="input"||l.type==="submit"||l.type==="image"||Yc?t!=="button"||l.type==null||l.type==="submit"||Yc?typeof a=="function"&&(l.name==null||w1||(w1=!0,console.error('Cannot specify a "name" prop for a button that specifies a function as a formAction. React needs it to encode which action should be invoked. It will get overridden.')),l.formEncType==null&&l.formMethod==null||$c||($c=!0,console.error("Cannot specify a formEncType or formMethod for a button that specifies a function as a formAction. React provides those automatically. They will get overridden.")),l.formTarget==null||qc||(qc=!0,console.error("Cannot specify a formTarget for a button that specifies a function as a formAction. The function will always be executed in the same window."))):(Yc=!0,console.error('A button can only specify a formAction along with type="submit" or no type.')):(Yc=!0,console.error('An input can only specify a formAction along with type="submit" or type="image".')):console.error(n==="action"?"You can only pass the action prop to <form>.":"You can only pass the formAction prop to <input> or <button>.")),typeof a=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(n==="formAction"?(t!=="input"&&Pe(e,t,"name",l.name,l,null),Pe(e,t,"formEncType",l.formEncType,l,null),Pe(e,t,"formMethod",l.formMethod,l,null),Pe(e,t,"formTarget",l.formTarget,l,null)):(Pe(e,t,"encType",l.encType,l,null),Pe(e,t,"method",l.method,l,null),Pe(e,t,"target",l.target,l,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}N(a,n),a=_i(""+a),e.setAttribute(n,a);break;case"onClick":a!=null&&(typeof a!="function"&&Wn(n,a),e.onclick=Pr);break;case"onScroll":a!=null&&(typeof a!="function"&&Wn(n,a),Le("scroll",e));break;case"onScrollEnd":a!=null&&(typeof a!="function"&&Wn(n,a),Le("scrollend",e));break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://react.dev/link/dangerously-set-inner-html for more information.");if(n=a.__html,n!=null){if(l.children!=null)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");e.innerHTML=n}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}N(a,n),n=_i(""+a),e.setAttributeNS(Fl,"xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?(N(a,n),e.setAttribute(n,""+a)):e.removeAttribute(n);break;case"inert":a!==""||Vc[n]||(Vc[n]=!0,console.error("Received an empty string for a boolean attribute `%s`. This will treat the attribute as if it were false. Either pass `false` to silence this warning, or pass `true` if you used an empty string in earlier versions of React to indicate this attribute is true.",n));case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":a===!0?e.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?(N(a,n),e.setAttribute(n,a)):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?(N(a,n),e.setAttribute(n,a)):e.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(n):(N(a,n),e.setAttribute(n,a));break;case"popover":Le("beforetoggle",e),Le("toggle",e),Ye(e,"popover",a);break;case"xlinkActuate":Ot(e,Fl,"xlink:actuate",a);break;case"xlinkArcrole":Ot(e,Fl,"xlink:arcrole",a);break;case"xlinkRole":Ot(e,Fl,"xlink:role",a);break;case"xlinkShow":Ot(e,Fl,"xlink:show",a);break;case"xlinkTitle":Ot(e,Fl,"xlink:title",a);break;case"xlinkType":Ot(e,Fl,"xlink:type",a);break;case"xmlBase":Ot(e,qh,"xml:base",a);break;case"xmlLang":Ot(e,qh,"xml:lang",a);break;case"xmlSpace":Ot(e,qh,"xml:space",a);break;case"is":o!=null&&console.error('Cannot update the "is" prop after it has been initialized.'),Ye(e,"is",a);break;case"innerText":case"textContent":break;case"popoverTarget":x1||a==null||typeof a!="object"||(x1=!0,console.error("The `popoverTarget` prop expects the ID of an Element as a string. Received %s instead.",a));default:!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N"?(n=rm(n),Ye(e,n,a)):Nl.hasOwnProperty(n)&&a!=null&&typeof a!="function"&&Wn(n,a)}}function sd(e,t,n,a,l,o){switch(n){case"style":um(e,a,o);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://react.dev/link/dangerously-set-inner-html for more information.");if(n=a.__html,n!=null){if(l.children!=null)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");e.innerHTML=n}}break;case"children":typeof a=="string"?Di(e,a):(typeof a=="number"||typeof a=="bigint")&&Di(e,""+a);break;case"onScroll":a!=null&&(typeof a!="function"&&Wn(n,a),Le("scroll",e));break;case"onScrollEnd":a!=null&&(typeof a!="function"&&Wn(n,a),Le("scrollend",e));break;case"onClick":a!=null&&(typeof a!="function"&&Wn(n,a),e.onclick=Pr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(Nl.hasOwnProperty(n))a!=null&&typeof a!="function"&&Wn(n,a);else e:{if(n[0]==="o"&&n[1]==="n"&&(l=n.endsWith("Capture"),t=n.slice(2,l?n.length-7:void 0),o=e[Wt]||null,o=o!=null?o[n]:null,typeof o=="function"&&e.removeEventListener(t,o,l),typeof a=="function")){typeof o!="function"&&o!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,l);break e}n in e?e[n]=a:a===!0?e.setAttribute(n,""):Ye(e,n,a)}}}function zt(e,t,n){switch(cd(t,n),t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Le("error",e),Le("load",e);var a=!1,l=!1,o;for(o in n)if(n.hasOwnProperty(o)){var u=n[o];if(u!=null)switch(o){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(t+" is a void element tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:Pe(e,t,o,u,n,null)}}l&&Pe(e,t,"srcSet",n.srcSet,n,null),a&&Pe(e,t,"src",n.src,n,null);return;case"input":ie("input",n),Le("invalid",e);var r=o=u=l=null,d=null,p=null;for(a in n)if(n.hasOwnProperty(a)){var O=n[a];if(O!=null)switch(a){case"name":l=O;break;case"type":u=O;break;case"checked":d=O;break;case"defaultChecked":p=O;break;case"value":o=O;break;case"defaultValue":r=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(t+" is a void element tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");break;default:Pe(e,t,a,O,n,null)}}Xp(e,n),Qp(e,o,r,d,p,u,l,!1),ur(e);return;case"select":ie("select",n),Le("invalid",e),a=u=o=null;for(l in n)if(n.hasOwnProperty(l)&&(r=n[l],r!=null))switch(l){case"value":o=r;break;case"defaultValue":u=r;break;case"multiple":a=r;default:Pe(e,t,l,r,n,null)}Jp(e,n),t=o,n=u,e.multiple=!!a,t!=null?po(e,!!a,t,!1):n!=null&&po(e,!!a,n,!0);return;case"textarea":ie("textarea",n),Le("invalid",e),o=l=a=null;for(u in n)if(n.hasOwnProperty(u)&&(r=n[u],r!=null))switch(u){case"value":a=r;break;case"defaultValue":l=r;break;case"children":o=r;break;case"dangerouslySetInnerHTML":if(r!=null)throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");break;default:Pe(e,t,u,r,n,null)}Kp(e,n),Wp(e,a,l,o),ur(e);return;case"option":Zp(e,n);for(d in n)if(n.hasOwnProperty(d)&&(a=n[d],a!=null))switch(d){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Pe(e,t,d,a,n,null)}return;case"dialog":Le("beforetoggle",e),Le("toggle",e),Le("cancel",e),Le("close",e);break;case"iframe":case"object":Le("load",e);break;case"video":case"audio":for(a=0;a<$u.length;a++)Le($u[a],e);break;case"image":Le("error",e),Le("load",e);break;case"details":Le("toggle",e);break;case"embed":case"source":case"link":Le("error",e),Le("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(p in n)if(n.hasOwnProperty(p)&&(a=n[p],a!=null))switch(p){case"children":case"dangerouslySetInnerHTML":throw Error(t+" is a void element tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:Pe(e,t,p,a,n,null)}return;default:if(Mi(t)){for(O in n)n.hasOwnProperty(O)&&(a=n[O],a!==void 0&&sd(e,t,O,a,n,void 0));return}}for(r in n)n.hasOwnProperty(r)&&(a=n[r],a!=null&&Pe(e,t,r,a,n,null))}function ET(e,t,n,a){switch(cd(t,a),t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,o=null,u=null,r=null,d=null,p=null,O=null;for(_ in n){var D=n[_];if(n.hasOwnProperty(_)&&D!=null)switch(_){case"checked":break;case"value":break;case"defaultValue":d=D;default:a.hasOwnProperty(_)||Pe(e,t,_,null,a,D)}}for(var A in a){var _=a[A];if(D=n[A],a.hasOwnProperty(A)&&(_!=null||D!=null))switch(A){case"type":o=_;break;case"name":l=_;break;case"checked":p=_;break;case"defaultChecked":O=_;break;case"value":u=_;break;case"defaultValue":r=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(t+" is a void element tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");break;default:_!==D&&Pe(e,t,A,_,a,D)}}t=n.type==="checkbox"||n.type==="radio"?n.checked!=null:n.value!=null,a=a.type==="checkbox"||a.type==="radio"?a.checked!=null:a.value!=null,t||!a||C1||(console.error("A component is changing an uncontrolled input to be controlled. This is likely caused by the value changing from undefined to a defined value, which should not happen. Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://react.dev/link/controlled-components"),C1=!0),!t||a||O1||(console.error("A component is changing a controlled input to be uncontrolled. This is likely caused by the value changing from a defined to undefined, which should not happen. Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://react.dev/link/controlled-components"),O1=!0),Cs(e,u,r,d,p,O,o,l);return;case"select":_=u=r=A=null;for(o in n)if(d=n[o],n.hasOwnProperty(o)&&d!=null)switch(o){case"value":break;case"multiple":_=d;default:a.hasOwnProperty(o)||Pe(e,t,o,null,a,d)}for(l in a)if(o=a[l],d=n[l],a.hasOwnProperty(l)&&(o!=null||d!=null))switch(l){case"value":A=o;break;case"defaultValue":r=o;break;case"multiple":u=o;default:o!==d&&Pe(e,t,l,o,a,d)}a=r,t=u,n=_,A!=null?po(e,!!t,A,!1):!!n!=!!t&&(a!=null?po(e,!!t,a,!0):po(e,!!t,t?[]:"",!1));return;case"textarea":_=A=null;for(r in n)if(l=n[r],n.hasOwnProperty(r)&&l!=null&&!a.hasOwnProperty(r))switch(r){case"value":break;case"children":break;default:Pe(e,t,r,null,a,l)}for(u in a)if(l=a[u],o=n[u],a.hasOwnProperty(u)&&(l!=null||o!=null))switch(u){case"value":A=l;break;case"defaultValue":_=l;break;case"children":break;case"dangerouslySetInnerHTML":if(l!=null)throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");break;default:l!==o&&Pe(e,t,u,l,a,o)}Pp(e,A,_);return;case"option":for(var ee in n)if(A=n[ee],n.hasOwnProperty(ee)&&A!=null&&!a.hasOwnProperty(ee))switch(ee){case"selected":e.selected=!1;break;default:Pe(e,t,ee,null,a,A)}for(d in a)if(A=a[d],_=n[d],a.hasOwnProperty(d)&&A!==_&&(A!=null||_!=null))switch(d){case"selected":e.selected=A&&typeof A!="function"&&typeof A!="symbol";break;default:Pe(e,t,d,A,a,_)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ge in n)A=n[ge],n.hasOwnProperty(ge)&&A!=null&&!a.hasOwnProperty(ge)&&Pe(e,t,ge,null,a,A);for(p in a)if(A=a[p],_=n[p],a.hasOwnProperty(p)&&A!==_&&(A!=null||_!=null))switch(p){case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(t+" is a void element tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");break;default:Pe(e,t,p,A,a,_)}return;default:if(Mi(t)){for(var nt in n)A=n[nt],n.hasOwnProperty(nt)&&A!==void 0&&!a.hasOwnProperty(nt)&&sd(e,t,nt,void 0,a,A);for(O in a)A=a[O],_=n[O],!a.hasOwnProperty(O)||A===_||A===void 0&&_===void 0||sd(e,t,O,A,a,_);return}}for(var Be in n)A=n[Be],n.hasOwnProperty(Be)&&A!=null&&!a.hasOwnProperty(Be)&&Pe(e,t,Be,null,a,A);for(D in a)A=a[D],_=n[D],!a.hasOwnProperty(D)||A===_||A==null&&_==null||Pe(e,t,D,A,a,_)}function $g(e){switch(e){case"class":return"className";case"for":return"htmlFor";default:return e}}function fd(e){var t={};e=e.style;for(var n=0;n<e.length;n++){var a=e[n];t[a]=e.getPropertyValue(a)}return t}function Vg(e,t,n){if(t!=null&&typeof t!="object")console.error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");else{var a,l=a="",o;for(o in t)if(t.hasOwnProperty(o)){var u=t[o];u!=null&&typeof u!="boolean"&&u!==""&&(o.indexOf("--")===0?(K(u,o),a+=l+o+":"+(""+u).trim()):typeof u!="number"||u===0||Iv.has(o)?(K(u,o),a+=l+o.replace(Vv,"-$1").toLowerCase().replace(Gv,"-ms-")+":"+(""+u).trim()):a+=l+o.replace(Vv,"-$1").toLowerCase().replace(Gv,"-ms-")+":"+u+"px",l=";")}a=a||null,t=e.getAttribute("style"),t!==a&&(a=Ja(a),Ja(t)!==a&&(n.style=fd(e)))}}function bn(e,t,n,a,l,o){if(l.delete(n),e=e.getAttribute(n),e===null)switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":return}else if(a!=null)switch(typeof a){case"function":case"symbol":case"boolean":break;default:if(N(a,t),e===""+a)return}Ct(t,e,a,o)}function Gg(e,t,n,a,l,o){if(l.delete(n),e=e.getAttribute(n),e===null){switch(typeof a){case"function":case"symbol":return}if(!a)return}else switch(typeof a){case"function":case"symbol":break;default:if(a)return}Ct(t,e,a,o)}function dd(e,t,n,a,l,o){if(l.delete(n),e=e.getAttribute(n),e===null)switch(typeof a){case"undefined":case"function":case"symbol":return}else if(a!=null)switch(typeof a){case"function":case"symbol":break;default:if(N(a,n),e===""+a)return}Ct(t,e,a,o)}function Xg(e,t,n,a,l,o){if(l.delete(n),e=e.getAttribute(n),e===null)switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":return;default:if(isNaN(a))return}else if(a!=null)switch(typeof a){case"function":case"symbol":case"boolean":break;default:if(!isNaN(a)&&(N(a,t),e===""+a))return}Ct(t,e,a,o)}function hd(e,t,n,a,l,o){if(l.delete(n),e=e.getAttribute(n),e===null)switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":return}else if(a!=null)switch(typeof a){case"function":case"symbol":case"boolean":break;default:if(N(a,t),n=_i(""+a),e===n)return}Ct(t,e,a,o)}function Qg(e,t,n,a){for(var l={},o=new Set,u=e.attributes,r=0;r<u.length;r++)switch(u[r].name.toLowerCase()){case"value":break;case"checked":break;case"selected":break;default:o.add(u[r].name)}if(Mi(t)){for(var d in n)if(n.hasOwnProperty(d)){var p=n[d];if(p!=null){if(Nl.hasOwnProperty(d))typeof p!="function"&&Wn(d,p);else if(n.suppressHydrationWarning!==!0)switch(d){case"children":typeof p!="string"&&typeof p!="number"||Ct("children",e.textContent,p,l);continue;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":continue;case"dangerouslySetInnerHTML":u=e.innerHTML,p=p?p.__html:void 0,p!=null&&(p=Yg(e,p),Ct(d,u,p,l));continue;case"style":o.delete(d),Vg(e,p,l);continue;case"offsetParent":case"offsetTop":case"offsetLeft":case"offsetWidth":case"offsetHeight":case"isContentEditable":case"outerText":case"outerHTML":o.delete(d.toLowerCase()),console.error("Assignment to read-only property will result in a no-op: `%s`",d);continue;case"className":o.delete("class"),u=Oe(e,"class",p),Ct("className",u,p,l);continue;default:a.context===Ua&&t!=="svg"&&t!=="math"?o.delete(d.toLowerCase()):o.delete(d),u=Oe(e,d,p),Ct(d,u,p,l)}}}}else for(p in n)if(n.hasOwnProperty(p)&&(d=n[p],d!=null)){if(Nl.hasOwnProperty(p))typeof d!="function"&&Wn(p,d);else if(n.suppressHydrationWarning!==!0)switch(p){case"children":typeof d!="string"&&typeof d!="number"||Ct("children",e.textContent,d,l);continue;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"value":case"checked":case"selected":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":continue;case"dangerouslySetInnerHTML":u=e.innerHTML,d=d?d.__html:void 0,d!=null&&(d=Yg(e,d),u!==d&&(l[p]={__html:u}));continue;case"className":bn(e,p,"class",d,o,l);continue;case"tabIndex":bn(e,p,"tabindex",d,o,l);continue;case"style":o.delete(p),Vg(e,d,l);continue;case"multiple":o.delete(p),Ct(p,e.multiple,d,l);continue;case"muted":o.delete(p),Ct(p,e.muted,d,l);continue;case"autoFocus":o.delete("autofocus"),Ct(p,e.autofocus,d,l);continue;case"data":if(t!=="object"){o.delete(p),u=e.getAttribute("data"),Ct(p,u,d,l);continue}case"src":case"href":if(!(d!==""||t==="a"&&p==="href"||t==="object"&&p==="data")){console.error(p==="src"?'An empty string ("") was passed to the %s attribute. This may cause the browser to download the whole page again over the network. To fix this, either do not render the element at all or pass null to %s instead of an empty string.':'An empty string ("") was passed to the %s attribute. To fix this, either do not render the element at all or pass null to %s instead of an empty string.',p,p);continue}hd(e,p,p,d,o,l);continue;case"action":case"formAction":if(u=e.getAttribute(p),typeof d=="function"){o.delete(p.toLowerCase()),p==="formAction"?(o.delete("name"),o.delete("formenctype"),o.delete("formmethod"),o.delete("formtarget")):(o.delete("enctype"),o.delete("method"),o.delete("target"));continue}else if(u===UR){o.delete(p.toLowerCase()),Ct(p,"function",d,l);continue}hd(e,p,p.toLowerCase(),d,o,l);continue;case"xlinkHref":hd(e,p,"xlink:href",d,o,l);continue;case"contentEditable":dd(e,p,"contenteditable",d,o,l);continue;case"spellCheck":dd(e,p,"spellcheck",d,o,l);continue;case"draggable":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":dd(e,p,p,d,o,l);continue;case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":Gg(e,p,p.toLowerCase(),d,o,l);continue;case"capture":case"download":e:{r=e;var O=u=p,D=l;if(o.delete(O),r=r.getAttribute(O),r===null)switch(typeof d){case"undefined":case"function":case"symbol":break e;default:if(d===!1)break e}else if(d!=null)switch(typeof d){case"function":case"symbol":break;case"boolean":if(d===!0&&r==="")break e;break;default:if(N(d,u),r===""+d)break e}Ct(u,r,d,D)}continue;case"cols":case"rows":case"size":case"span":e:{if(r=e,O=u=p,D=l,o.delete(O),r=r.getAttribute(O),r===null)switch(typeof d){case"undefined":case"function":case"symbol":case"boolean":break e;default:if(isNaN(d)||1>d)break e}else if(d!=null)switch(typeof d){case"function":case"symbol":case"boolean":break;default:if(!(isNaN(d)||1>d)&&(N(d,u),r===""+d))break e}Ct(u,r,d,D)}continue;case"rowSpan":Xg(e,p,"rowspan",d,o,l);continue;case"start":Xg(e,p,p,d,o,l);continue;case"xHeight":bn(e,p,"x-height",d,o,l);continue;case"xlinkActuate":bn(e,p,"xlink:actuate",d,o,l);continue;case"xlinkArcrole":bn(e,p,"xlink:arcrole",d,o,l);continue;case"xlinkRole":bn(e,p,"xlink:role",d,o,l);continue;case"xlinkShow":bn(e,p,"xlink:show",d,o,l);continue;case"xlinkTitle":bn(e,p,"xlink:title",d,o,l);continue;case"xlinkType":bn(e,p,"xlink:type",d,o,l);continue;case"xmlBase":bn(e,p,"xml:base",d,o,l);continue;case"xmlLang":bn(e,p,"xml:lang",d,o,l);continue;case"xmlSpace":bn(e,p,"xml:space",d,o,l);continue;case"inert":d!==""||Vc[p]||(Vc[p]=!0,console.error("Received an empty string for a boolean attribute `%s`. This will treat the attribute as if it were false. Either pass `false` to silence this warning, or pass `true` if you used an empty string in earlier versions of React to indicate this attribute is true.",p)),Gg(e,p,p,d,o,l);continue;default:if(!(2<p.length)||p[0]!=="o"&&p[0]!=="O"||p[1]!=="n"&&p[1]!=="N"){r=rm(p),u=!1,a.context===Ua&&t!=="svg"&&t!=="math"?o.delete(r.toLowerCase()):(O=p.toLowerCase(),O=pc.hasOwnProperty(O)&&pc[O]||null,O!==null&&O!==p&&(u=!0,o.delete(O)),o.delete(r));e:if(O=e,D=r,r=d,Me(D))if(O.hasAttribute(D))O=O.getAttribute(D),N(r,D),r=O===""+r?r:O;else{switch(typeof r){case"function":case"symbol":break e;case"boolean":if(O=D.toLowerCase().slice(0,5),O!=="data-"&&O!=="aria-")break e}r=r===void 0?void 0:null}else r=void 0;u||Ct(p,r,d,l)}}}return 0<o.size&&n.suppressHydrationWarning!==!0&&TT(e,o,l),Object.keys(l).length===0?null:l}function RT(e,t){switch(e.length){case 0:return"";case 1:return e[0];case 2:return e[0]+" "+t+" "+e[1];default:return e.slice(0,-1).join(", ")+", "+t+" "+e[e.length-1]}}function Wr(e){return e.nodeType===9?e:e.ownerDocument}function Zg(e){switch(e){case jo:return di;case hc:return Qc;default:return Ua}}function Ig(e,t){if(e===Ua)switch(t){case"svg":return di;case"math":return Qc;default:return Ua}return e===di&&t==="foreignObject"?Ua:e}function pd(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}function AT(){var e=window.event;return e&&e.type==="popstate"?e===Xh?!1:(Xh=e,!0):(Xh=null,!1)}function OT(e){setTimeout(function(){throw e})}function CT(e,t,n){switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&e.focus();break;case"img":n.src?e.src=n.src:n.srcSet&&(e.srcset=n.srcSet)}}function wT(e,t,n,a){ET(e,t,n,a),e[Wt]=a}function Jg(e){Di(e,"")}function xT(e,t,n){e.nodeValue=n}function Ka(e){return e==="head"}function DT(e,t){e.removeChild(t)}function MT(e,t){(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e).removeChild(t)}function Kg(e,t){var n=t,a=0,l=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n===Xc){if(0<a&&8>a){n=a;var u=e.ownerDocument;if(n&jR&&nu(u.documentElement),n&NR&&nu(u.body),n&kR)for(n=u.head,nu(n),u=n.firstChild;u;){var r=u.nextSibling,d=u.nodeName;u[cu]||d==="SCRIPT"||d==="STYLE"||d==="LINK"&&u.rel.toLowerCase()==="stylesheet"||n.removeChild(u),u=r}}if(l===0){e.removeChild(o),iu(t);return}l--}else n===Gc||n===za||n===Vu?l++:a=n.charCodeAt(0)-48;else a=0;n=o}while(n);iu(t)}function _T(e){e=e.style,typeof e.setProperty=="function"?e.setProperty("display","none","important"):e.display="none"}function zT(e){e.nodeValue=""}function UT(e,t){t=t[LR],t=t!=null&&t.hasOwnProperty("display")?t.display:null,e.style.display=t==null||typeof t=="boolean"?"":(""+t).trim()}function HT(e,t){e.nodeValue=t}function md(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":md(n),Ya(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function jT(e,t,n,a){for(;e.nodeType===1;){var l=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[cu])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(o=e.getAttribute("rel"),o==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(o!==l.rel||e.getAttribute("href")!==(l.href==null||l.href===""?null:l.href)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin)||e.getAttribute("title")!==(l.title==null?null:l.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(o=e.getAttribute("src"),(o!==(l.src==null?null:l.src)||e.getAttribute("type")!==(l.type==null?null:l.type)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){N(l.name,"name");var o=l.name==null?null:""+l.name;if(l.type==="hidden"&&e.getAttribute("name")===o)return e}else return e;if(e=Sn(e.nextSibling),e===null)break}return null}function NT(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Sn(e.nextSibling),e===null))return null;return e}function yd(e){return e.data===Vu||e.data===za&&e.ownerDocument.readyState===M1}function kT(e,t){var n=e.ownerDocument;if(e.data!==za||n.readyState===M1)t();else{var a=function(){t(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Sn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t===Gc||t===Vu||t===za||t===$h||t===D1)break;if(t===Xc)return null}}return e}function Pg(e){if(e.nodeType===1){for(var t=e.nodeName.toLowerCase(),n={},a=e.attributes,l=0;l<a.length;l++){var o=a[l];n[$g(o.name)]=o.name.toLowerCase()==="style"?fd(e):o.value}return{type:t,props:n}}return e.nodeType===8?{type:"Suspense",props:{}}:e.nodeValue}function Wg(e,t,n){return n===null||n[HR]!==!0?(e.nodeValue===t?e=null:(t=Ja(t),e=Ja(e.nodeValue)===t?null:e.nodeValue),e):null}function Fg(e){e=e.nextSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n===Xc){if(t===0)return Sn(e.nextSibling);t--}else n!==Gc&&n!==Vu&&n!==za||t++}e=e.nextSibling}return null}function ev(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n===Gc||n===Vu||n===za){if(t===0)return e;t--}else n===Xc&&t++}e=e.previousSibling}return null}function LT(e){iu(e)}function BT(e){iu(e)}function tv(e,t,n,a,l){switch(l&&zs(e,a.ancestorInfo),t=Wr(n),e){case"html":if(e=t.documentElement,!e)throw Error("React expected an <html> element (document.documentElement) to exist in the Document but one was not found. React never removes the documentElement for any Document it renders into so the cause is likely in some other script running on this page.");return e;case"head":if(e=t.head,!e)throw Error("React expected a <head> element (document.head) to exist in the Document but one was not found. React never removes the head for any Document it renders into so the cause is likely in some other script running on this page.");return e;case"body":if(e=t.body,!e)throw Error("React expected a <body> element (document.body) to exist in the Document but one was not found. React never removes the body for any Document it renders into so the cause is likely in some other script running on this page.");return e;default:throw Error("resolveSingletonInstance was called with an element type that is not supported. This is a bug in React.")}}function YT(e,t,n,a){if(!n[tl]&&Nn(n)){var l=n.tagName.toLowerCase();console.error("You are mounting a new %s component when a previous one has not first unmounted. It is an error to render more than one %s component at a time and attributes and children of these components will likely fail in unpredictable ways. Please only render a single instance of <%s> and if you need to mount a new one, ensure any previous ones have unmounted first.",l,l,l)}switch(e){case"html":case"head":case"body":break;default:console.error("acquireSingletonInstance was called with an element type that is not supported. This is a bug in React.")}for(l=n.attributes;l.length;)n.removeAttributeNode(l[0]);zt(n,e,t),n[Lt]=a,n[Wt]=t}function nu(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ya(e)}function Fr(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}function nv(e,t,n){var a=hi;if(a&&typeof t=="string"&&t){var l=vn(t);l='link[rel="'+e+'"][href="'+l+'"]',typeof n=="string"&&(l+='[crossorigin="'+n+'"]'),N1.has(l)||(N1.add(l),e={rel:e,crossOrigin:n,href:t},a.querySelector(l)===null&&(t=a.createElement("link"),zt(t,"link",e),E(t),a.head.appendChild(t)))}}function av(e,t,n,a){var l=(l=Fa.current)?Fr(l):null;if(!l)throw Error('"resourceRoot" was expected to exist. This is a bug in React.');switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(n=xo(n.href),t=f(l).hoistableStyles,a=t.get(n),a||(a={type:"style",instance:null,count:0,state:null},t.set(n,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=xo(n.href);var o=f(l).hoistableStyles,u=o.get(e);if(!u&&(l=l.ownerDocument||l,u={type:"stylesheet",instance:null,count:0,state:{loading:to,preload:null}},o.set(e,u),(o=l.querySelector(au(e)))&&!o._p&&(u.instance=o,u.state.loading=Gu|_n),!zn.has(e))){var r={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy};zn.set(e,r),o||qT(l,e,r,u.state)}if(t&&a===null)throw n=`

  - `+ec(t)+`
  + `+ec(n),Error("Expected <link> not to update to be updated to a stylesheet with precedence. Check the `rel`, `href`, and `precedence` props of this component. Alternatively, check whether two different <link> components render in the same slot or share the same key."+n);return u}if(t&&a!==null)throw n=`

  - `+ec(t)+`
  + `+ec(n),Error("Expected stylesheet with precedence to not be updated to a different kind of <link>. Check the `rel`, `href`, and `precedence` props of this component. Alternatively, check whether two different <link> components render in the same slot or share the same key."+n);return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(n=Do(n),t=f(l).hoistableScripts,a=t.get(n),a||(a={type:"script",instance:null,count:0,state:null},t.set(n,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error('getResource encountered a type it did not expect: "'+e+'". this is a bug in React.')}}function ec(e){var t=0,n="<link";return typeof e.rel=="string"?(t++,n+=' rel="'+e.rel+'"'):Ra.call(e,"rel")&&(t++,n+=' rel="'+(e.rel===null?"null":"invalid type "+typeof e.rel)+'"'),typeof e.href=="string"?(t++,n+=' href="'+e.href+'"'):Ra.call(e,"href")&&(t++,n+=' href="'+(e.href===null?"null":"invalid type "+typeof e.href)+'"'),typeof e.precedence=="string"?(t++,n+=' precedence="'+e.precedence+'"'):Ra.call(e,"precedence")&&(t++,n+=" precedence={"+(e.precedence===null?"null":"invalid type "+typeof e.precedence)+"}"),Object.getOwnPropertyNames(e).length>t&&(n+=" ..."),n+" />"}function xo(e){return'href="'+vn(e)+'"'}function au(e){return'link[rel="stylesheet"]['+e+"]"}function lv(e){return _e({},e,{"data-precedence":e.precedence,precedence:null})}function qT(e,t,n,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=Gu:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=Gu}),t.addEventListener("error",function(){return a.loading|=H1}),zt(t,"link",n),E(t),e.head.appendChild(t))}function Do(e){return'[src="'+vn(e)+'"]'}function lu(e){return"script[async]"+e}function ov(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+vn(n.href)+'"]');if(a)return t.instance=a,E(a),a;var l=_e({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),E(a),zt(a,"style",l),tc(a,n.precedence,e),t.instance=a;case"stylesheet":l=xo(n.href);var o=e.querySelector(au(l));if(o)return t.state.loading|=_n,t.instance=o,E(o),o;a=lv(n),(l=zn.get(l))&&gd(a,l),o=(e.ownerDocument||e).createElement("link"),E(o);var u=o;return u._p=new Promise(function(r,d){u.onload=r,u.onerror=d}),zt(o,"link",a),t.state.loading|=_n,tc(o,n.precedence,e),t.instance=o;case"script":return o=Do(n.src),(l=e.querySelector(lu(o)))?(t.instance=l,E(l),l):(a=n,(l=zn.get(o))&&(a=_e({},n),vd(a,l)),e=e.ownerDocument||e,l=e.createElement("script"),E(l),zt(l,"link",a),e.head.appendChild(l),t.instance=l);case"void":return null;default:throw Error('acquireResource encountered a resource type it did not expect: "'+t.type+'". this is a bug in React.')}else t.type==="stylesheet"&&(t.state.loading&_n)===to&&(a=t.instance,t.state.loading|=_n,tc(a,n.precedence,e));return t.instance}function tc(e,t,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),l=a.length?a[a.length-1]:null,o=l,u=0;u<a.length;u++){var r=a[u];if(r.dataset.precedence===t)o=r;else if(o!==l)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function gd(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function vd(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}function iv(e,t,n){if(Zc===null){var a=new Map,l=Zc=new Map;l.set(n,a)}else l=Zc,a=l.get(n),a||(a=new Map,l.set(n,a));if(a.has(e))return a;for(a.set(e,null),n=n.getElementsByTagName(e),l=0;l<n.length;l++){var o=n[l];if(!(o[cu]||o[Lt]||e==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!==jo){var u=o.getAttribute(t)||"";u=e+u;var r=a.get(u);r?r.push(o):a.set(u,[o])}}return a}function uv(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function $T(e,t,n){var a=!n.ancestorInfo.containerTagInScope;if(n.context===di||t.itemProp!=null)return!a||t.itemProp==null||e!=="meta"&&e!=="title"&&e!=="style"&&e!=="link"&&e!=="script"||console.error("Cannot render a <%s> outside the main document if it has an `itemProp` prop. `itemProp` suggests the tag belongs to an `itemScope` which can appear anywhere in the DOM. If you were intending for React to hoist this <%s> remove the `itemProp` prop. Otherwise, try moving this tag into the <head> or <body> of the Document.",e,e),!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href===""){a&&console.error('Cannot render a <style> outside the main document without knowing its precedence and a unique href key. React can hoist and deduplicate <style> tags if you provide a `precedence` prop along with an `href` prop that does not conflict with the `href` values used in any other hoisted <style> or <link rel="stylesheet" ...> tags.  Note that hoisting <style> tags is considered an advanced feature that most will not use directly. Consider moving the <style> tag to the <head> or consider adding a `precedence="default"` and `href="some unique resource identifier"`.');break}return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError){if(t.rel==="stylesheet"&&typeof t.precedence=="string"){e=t.href;var l=t.onError,o=t.disabled;n=[],t.onLoad&&n.push("`onLoad`"),l&&n.push("`onError`"),o!=null&&n.push("`disabled`"),l=RT(n,"and"),l+=n.length===1?" prop":" props",o=n.length===1?"an "+l:"the "+l,n.length&&console.error('React encountered a <link rel="stylesheet" href="%s" ... /> with a `precedence` prop that also included %s. The presence of loading and error handlers indicates an intent to manage the stylesheet loading state from your from your Component code and React will not hoist or deduplicate this stylesheet. If your intent was to have React hoist and deduplciate this stylesheet using the `precedence` prop remove the %s, otherwise remove the `precedence` prop.',e,o,l)}a&&(typeof t.rel!="string"||typeof t.href!="string"||t.href===""?console.error("Cannot render a <link> outside the main document without a `rel` and `href` prop. Try adding a `rel` and/or `href` prop to this <link> or moving the link into the <head> tag"):(t.onError||t.onLoad)&&console.error("Cannot render a <link> with onLoad or onError listeners outside the main document. Try removing onLoad={...} and onError={...} or moving it into the root <head> tag or somewhere in the <body>."));break}switch(t.rel){case"stylesheet":return e=t.precedence,t=t.disabled,typeof e!="string"&&a&&console.error('Cannot render a <link rel="stylesheet" /> outside the main document without knowing its precedence. Consider adding precedence="default" or moving it into the root <head> tag.'),typeof e=="string"&&t==null;default:return!0}case"script":if(e=t.async&&typeof t.async!="function"&&typeof t.async!="symbol",!e||t.onLoad||t.onError||!t.src||typeof t.src!="string"){a&&(e?t.onLoad||t.onError?console.error("Cannot render a <script> with onLoad or onError listeners outside the main document. Try removing onLoad={...} and onError={...} or moving it into the root <head> tag or somewhere in the <body>."):console.error("Cannot render a <script> outside the main document without `async={true}` and a non-empty `src` prop. Ensure there is a valid `src` and either make the script async or move it into the root <head> tag or somewhere in the <body>."):console.error('Cannot render a sync or defer <script> outside the main document without knowing its order. Try adding async="" or moving it into the root <head> tag.'));break}return!0;case"noscript":case"template":a&&console.error("Cannot render <%s> outside the main document. Try moving it into the root <head> tag.",e)}return!1}function rv(e){return!(e.type==="stylesheet"&&(e.state.loading&j1)===to)}function VT(){}function GT(e,t,n){if(Xu===null)throw Error("Internal React Error: suspendedState null when it was expected to exists. Please report this as a React bug.");var a=Xu;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&_n)===to){if(t.instance===null){var l=xo(n.href),o=e.querySelector(au(l));if(o){e=o._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=nc.bind(a),e.then(a,a)),t.state.loading|=_n,t.instance=o,E(o);return}o=e.ownerDocument||e,n=lv(n),(l=zn.get(l))&&gd(n,l),o=o.createElement("link"),E(o);var u=o;u._p=new Promise(function(r,d){u.onload=r,u.onerror=d}),zt(o,"link",n),t.instance=o}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&j1)===to&&(a.count++,t=nc.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function XT(){if(Xu===null)throw Error("Internal React Error: suspendedState null when it was expected to exists. Please report this as a React bug.");var e=Xu;return e.stylesheets&&e.count===0&&bd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&bd(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function nc(){if(this.count--,this.count===0){if(this.stylesheets)bd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}function bd(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ic=new Map,t.forEach(QT,e),Ic=null,nc.call(e))}function QT(e,t){if(!(t.state.loading&_n)){var n=Ic.get(e);if(n)var a=n.get(Zh);else{n=new Map,Ic.set(e,n);for(var l=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<l.length;o++){var u=l[o];(u.nodeName==="LINK"||u.getAttribute("media")!=="not all")&&(n.set(u.dataset.precedence,u),a=u)}a&&n.set(Zh,a)}l=t.instance,u=l.getAttribute("data-precedence"),o=n.get(u)||a,o===a&&n.set(Zh,l),n.set(u,l),this.count++,a=nc.bind(this),l.addEventListener("load",a),l.addEventListener("error",a),o?o.parentNode.insertBefore(l,o.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(l,e.firstChild)),t.state.loading|=_n}}function ZT(e,t,n,a,l,o,u,r){for(this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=eo,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=so(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=so(0),this.hiddenUpdates=so(null),this.identifierPrefix=a,this.onUncaughtError=l,this.onCaughtError=o,this.onRecoverableError=u,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=r,this.incompleteTransitions=new Map,this.passiveEffectDuration=this.effectDuration=-0,this.memoizedUpdaters=new Set,e=this.pendingUpdatersLaneMap=[],t=0;31>t;t++)e.push(new Set);this._debugRootType=n?"hydrateRoot()":"createRoot()"}function cv(e,t,n,a,l,o,u,r,d,p,O,D){return e=new ZT(e,t,n,u,r,d,p,D),t=sR,o===!0&&(t|=Bt|Yn),Bn&&(t|=Ht),o=S(3,null,null,t),e.current=o,o.stateNode=e,t=Is(),Ml(t),e.pooledCache=t,Ml(t),o.memoizedState={element:a,isDehydrated:n,cache:t},Ws(o),e}function sv(e){return e?(e=al,e):al}function Sd(e,t,n,a,l,o){if(kt&&typeof kt.onScheduleFiberRoot=="function")try{kt.onScheduleFiberRoot(Uo,a,n)}catch(u){ta||(ta=!0,console.error("React instrumentation encountered an error: %s",u))}J!==null&&typeof J.markRenderScheduled=="function"&&J.markRenderScheduled(t),l=sv(l),a.context===null?a.context=l:a.pendingContext=l,aa&&fn!==null&&!Y1&&(Y1=!0,console.error(`Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.

Check the render method of %s.`,X(fn)||"Unknown")),a=Va(t),a.payload={element:n},o=o===void 0?null:o,o!==null&&(typeof o!="function"&&console.error("Expected the last optional `callback` argument to be a function. Instead received: %s.",o),a.callback=o),n=Ga(e,a,t),n!==null&&(ht(n,e,t),Yi(n,e,t))}function fv(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Td(e,t){fv(e,t),(e=e.alternate)&&fv(e,t)}function dv(e){if(e.tag===13){var t=Jt(e,67108864);t!==null&&ht(t,e,67108864),Td(e,67108864)}}function IT(){return fn}function JT(){for(var e=new Map,t=1,n=0;31>n;n++){var a=ka(t);e.set(t,a),t*=2}return e}function KT(e,t,n,a){var l=x.T;x.T=null;var o=Ve.p;try{Ve.p=Tn,Ed(e,t,n,a)}finally{Ve.p=o,x.T=l}}function PT(e,t,n,a){var l=x.T;x.T=null;var o=Ve.p;try{Ve.p=na,Ed(e,t,n,a)}finally{Ve.p=o,x.T=l}}function Ed(e,t,n,a){if(Kc){var l=Rd(a);if(l===null)rd(e,t,a,Pc,n),pv(e,a);else if(WT(l,e,t,n,a))a.stopPropagation();else if(pv(e,a),t&4&&-1<$R.indexOf(e)){for(;l!==null;){var o=Nn(l);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var u=yt(o.pendingLanes);if(u!==0){var r=o;for(r.pendingLanes|=2,r.entangledLanes|=2;u;){var d=1<<31-qt(u);r.entanglements[1]|=d,u&=~d}Pn(o),(Ge&(Qt|Gn))===dn&&(Hc=ea()+v1,eu(0))}}break;case 13:r=Jt(o,2),r!==null&&ht(r,o,2),Ao(),Td(o,2)}if(o=Rd(a),o===null&&rd(e,t,a,Pc,n),o===l)break;l=o}l!==null&&a.stopPropagation()}else rd(e,t,a,null,n)}}function Rd(e){return e=Us(e),Ad(e)}function Ad(e){if(Pc=null,e=jn(e),e!==null){var t=W(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=be(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Pc=e,null}function hv(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return Tn;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return na;case"message":switch(cE()){case jd:return Tn;case Nd:return na;case zo:case sE:return Aa;case kd:return fc;default:return Aa}default:return Aa}}function pv(e,t){switch(e){case"focusin":case"focusout":hl=null;break;case"dragenter":case"dragleave":pl=null;break;case"mouseover":case"mouseout":ml=null;break;case"pointerover":case"pointerout":Zu.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Iu.delete(t.pointerId)}}function ou(e,t,n,a,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:o,targetContainers:[l]},t!==null&&(t=Nn(t),t!==null&&dv(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function WT(e,t,n,a,l){switch(t){case"focusin":return hl=ou(hl,e,t,n,a,l),!0;case"dragenter":return pl=ou(pl,e,t,n,a,l),!0;case"mouseover":return ml=ou(ml,e,t,n,a,l),!0;case"pointerover":var o=l.pointerId;return Zu.set(o,ou(Zu.get(o)||null,e,t,n,a,l)),!0;case"gotpointercapture":return o=l.pointerId,Iu.set(o,ou(Iu.get(o)||null,e,t,n,a,l)),!0}return!1}function mv(e){var t=jn(e.target);if(t!==null){var n=W(t);if(n!==null){if(t=n.tag,t===13){if(t=be(n),t!==null){e.blockedOn=t,ho(e.priority,function(){if(n.tag===13){var a=cn(n);a=Ri(a);var l=Jt(n,a);l!==null&&ht(l,n,a),Td(n,a)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ac(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Rd(e.nativeEvent);if(n===null){n=e.nativeEvent;var a=new n.constructor(n.type,n),l=a;fu!==null&&console.error("Expected currently replaying event to be null. This error is likely caused by a bug in React. Please file an issue."),fu=l,n.target.dispatchEvent(a),fu===null&&console.error("Expected currently replaying event to not be null. This error is likely caused by a bug in React. Please file an issue."),fu=null}else return t=Nn(n),t!==null&&dv(t),e.blockedOn=n,!1;t.shift()}return!0}function yv(e,t,n){ac(e)&&n.delete(t)}function FT(){Ih=!1,hl!==null&&ac(hl)&&(hl=null),pl!==null&&ac(pl)&&(pl=null),ml!==null&&ac(ml)&&(ml=null),Zu.forEach(yv),Iu.forEach(yv)}function lc(e,t){e.blockedOn===t&&(e.blockedOn=null,Ih||(Ih=!0,gt.unstable_scheduleCallback(gt.unstable_NormalPriority,FT)))}function gv(e){Wc!==e&&(Wc=e,gt.unstable_scheduleCallback(gt.unstable_NormalPriority,function(){Wc===e&&(Wc=null);for(var t=0;t<e.length;t+=3){var n=e[t],a=e[t+1],l=e[t+2];if(typeof a!="function"){if(Ad(a||n)===null)continue;break}var o=Nn(n);o!==null&&(e.splice(t,3),t-=3,n={pending:!0,data:l,method:n.method,action:a},Object.freeze(n),Af(o,n,a,l))}}))}function iu(e){function t(d){return lc(d,e)}hl!==null&&lc(hl,e),pl!==null&&lc(pl,e),ml!==null&&lc(ml,e),Zu.forEach(t),Iu.forEach(t);for(var n=0;n<yl.length;n++){var a=yl[n];a.blockedOn===e&&(a.blockedOn=null)}for(;0<yl.length&&(n=yl[0],n.blockedOn===null);)mv(n),n.blockedOn===null&&yl.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var l=n[a],o=n[a+1],u=l[Wt]||null;if(typeof o=="function")u||gv(n);else if(u){var r=null;if(o&&o.hasAttribute("formAction")){if(l=o,u=o[Wt]||null)r=u.formAction;else if(Ad(l)!==null)continue}else r=u.action;typeof r=="function"?n[a+1]=r:(n.splice(a,3),a-=3),gv(n)}}}function Od(e){this._internalRoot=e}function oc(e){this._internalRoot=e}function vv(e){e[tl]&&(e._reactRootContainer?console.error("You are calling ReactDOMClient.createRoot() on a container that was previously passed to ReactDOM.render(). This is not supported."):console.error("You are calling ReactDOMClient.createRoot() on a container that has already been passed to createRoot() before. Instead, call root.render() on the existing root instead if you want to update it."))}typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var gt=UO(),Cd=hs(),eE=jO(),_e=Object.assign,tE=Symbol.for("react.element"),Pa=Symbol.for("react.transitional.element"),Mo=Symbol.for("react.portal"),_o=Symbol.for("react.fragment"),ic=Symbol.for("react.strict_mode"),wd=Symbol.for("react.profiler"),nE=Symbol.for("react.provider"),xd=Symbol.for("react.consumer"),Fn=Symbol.for("react.context"),uu=Symbol.for("react.forward_ref"),Dd=Symbol.for("react.suspense"),Md=Symbol.for("react.suspense_list"),uc=Symbol.for("react.memo"),sn=Symbol.for("react.lazy"),_d=Symbol.for("react.activity"),aE=Symbol.for("react.memo_cache_sentinel"),bv=Symbol.iterator,lE=Symbol.for("react.client.reference"),Ut=Array.isArray,x=Cd.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Ve=eE.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,oE=Object.freeze({pending:!1,data:null,method:null,action:null}),zd=[],Ud=[],Ea=-1,Wa=Ie(null),ru=Ie(null),Fa=Ie(null),rc=Ie(null),Ra=Object.prototype.hasOwnProperty,Hd=gt.unstable_scheduleCallback,iE=gt.unstable_cancelCallback,uE=gt.unstable_shouldYield,rE=gt.unstable_requestPaint,ea=gt.unstable_now,cE=gt.unstable_getCurrentPriorityLevel,jd=gt.unstable_ImmediatePriority,Nd=gt.unstable_UserBlockingPriority,zo=gt.unstable_NormalPriority,sE=gt.unstable_LowPriority,kd=gt.unstable_IdlePriority,fE=gt.log,dE=gt.unstable_setDisableYieldValue,Uo=null,kt=null,J=null,ta=!1,Bn=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u",qt=Math.clz32?Math.clz32:sa,hE=Math.log,pE=Math.LN2,cc=256,sc=4194304,Tn=2,na=8,Aa=32,fc=268435456,el=Math.random().toString(36).slice(2),Lt="__reactFiber$"+el,Wt="__reactProps$"+el,tl="__reactContainer$"+el,Ld="__reactEvents$"+el,mE="__reactListeners$"+el,yE="__reactHandles$"+el,Sv="__reactResources$"+el,cu="__reactMarker$"+el,Tv=new Set,Nl={},Bd={},gE={button:!0,checkbox:!0,image:!0,hidden:!0,radio:!0,reset:!0,submit:!0},vE=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ev={},Rv={},su=0,Av,Ov,Cv,wv,xv,Dv,Mv;da.__reactDisabledLog=!0;var Yd,_v,qd=!1,$d=new(typeof WeakMap=="function"?WeakMap:Map),fn=null,aa=!1,bE=/[\n"\\]/g,zv=!1,Uv=!1,Hv=!1,jv=!1,Nv=!1,kv=!1,Lv=["value","defaultValue"],Bv=!1,Yv=/["'&<>\n\t]|^\s|\s$/,SE="address applet area article aside base basefont bgsound blockquote body br button caption center col colgroup dd details dir div dl dt embed fieldset figcaption figure footer form frame frameset h1 h2 h3 h4 h5 h6 head header hgroup hr html iframe img input isindex li link listing main marquee menu menuitem meta nav noembed noframes noscript object ol p param plaintext pre script section select source style summary table tbody td template textarea tfoot th thead title tr track ul wbr xmp".split(" "),qv="applet caption html table td th marquee object template foreignObject desc title".split(" "),TE=qv.concat(["button"]),EE="dd dt li option optgroup p rp rt".split(" "),$v={current:null,formTag:null,aTagInScope:null,buttonTagInScope:null,nobrTagInScope:null,pTagInButtonScope:null,listItemTagAutoclosing:null,dlItemTagAutoclosing:null,containerTagInScope:null,implicitRootScope:!1},dc={},Vd={animation:"animationDelay animationDirection animationDuration animationFillMode animationIterationCount animationName animationPlayState animationTimingFunction".split(" "),background:"backgroundAttachment backgroundClip backgroundColor backgroundImage backgroundOrigin backgroundPositionX backgroundPositionY backgroundRepeat backgroundSize".split(" "),backgroundPosition:["backgroundPositionX","backgroundPositionY"],border:"borderBottomColor borderBottomStyle borderBottomWidth borderImageOutset borderImageRepeat borderImageSlice borderImageSource borderImageWidth borderLeftColor borderLeftStyle borderLeftWidth borderRightColor borderRightStyle borderRightWidth borderTopColor borderTopStyle borderTopWidth".split(" "),borderBlockEnd:["borderBlockEndColor","borderBlockEndStyle","borderBlockEndWidth"],borderBlockStart:["borderBlockStartColor","borderBlockStartStyle","borderBlockStartWidth"],borderBottom:["borderBottomColor","borderBottomStyle","borderBottomWidth"],borderColor:["borderBottomColor","borderLeftColor","borderRightColor","borderTopColor"],borderImage:["borderImageOutset","borderImageRepeat","borderImageSlice","borderImageSource","borderImageWidth"],borderInlineEnd:["borderInlineEndColor","borderInlineEndStyle","borderInlineEndWidth"],borderInlineStart:["borderInlineStartColor","borderInlineStartStyle","borderInlineStartWidth"],borderLeft:["borderLeftColor","borderLeftStyle","borderLeftWidth"],borderRadius:["borderBottomLeftRadius","borderBottomRightRadius","borderTopLeftRadius","borderTopRightRadius"],borderRight:["borderRightColor","borderRightStyle","borderRightWidth"],borderStyle:["borderBottomStyle","borderLeftStyle","borderRightStyle","borderTopStyle"],borderTop:["borderTopColor","borderTopStyle","borderTopWidth"],borderWidth:["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth"],columnRule:["columnRuleColor","columnRuleStyle","columnRuleWidth"],columns:["columnCount","columnWidth"],flex:["flexBasis","flexGrow","flexShrink"],flexFlow:["flexDirection","flexWrap"],font:"fontFamily fontFeatureSettings fontKerning fontLanguageOverride fontSize fontSizeAdjust fontStretch fontStyle fontVariant fontVariantAlternates fontVariantCaps fontVariantEastAsian fontVariantLigatures fontVariantNumeric fontVariantPosition fontWeight lineHeight".split(" "),fontVariant:"fontVariantAlternates fontVariantCaps fontVariantEastAsian fontVariantLigatures fontVariantNumeric fontVariantPosition".split(" "),gap:["columnGap","rowGap"],grid:"gridAutoColumns gridAutoFlow gridAutoRows gridTemplateAreas gridTemplateColumns gridTemplateRows".split(" "),gridArea:["gridColumnEnd","gridColumnStart","gridRowEnd","gridRowStart"],gridColumn:["gridColumnEnd","gridColumnStart"],gridColumnGap:["columnGap"],gridGap:["columnGap","rowGap"],gridRow:["gridRowEnd","gridRowStart"],gridRowGap:["rowGap"],gridTemplate:["gridTemplateAreas","gridTemplateColumns","gridTemplateRows"],listStyle:["listStyleImage","listStylePosition","listStyleType"],margin:["marginBottom","marginLeft","marginRight","marginTop"],marker:["markerEnd","markerMid","markerStart"],mask:"maskClip maskComposite maskImage maskMode maskOrigin maskPositionX maskPositionY maskRepeat maskSize".split(" "),maskPosition:["maskPositionX","maskPositionY"],outline:["outlineColor","outlineStyle","outlineWidth"],overflow:["overflowX","overflowY"],padding:["paddingBottom","paddingLeft","paddingRight","paddingTop"],placeContent:["alignContent","justifyContent"],placeItems:["alignItems","justifyItems"],placeSelf:["alignSelf","justifySelf"],textDecoration:["textDecorationColor","textDecorationLine","textDecorationStyle"],textEmphasis:["textEmphasisColor","textEmphasisStyle"],transition:["transitionDelay","transitionDuration","transitionProperty","transitionTimingFunction"],wordWrap:["overflowWrap"]},Vv=/([A-Z])/g,Gv=/^ms-/,RE=/^(?:webkit|moz|o)[A-Z]/,AE=/^-ms-/,OE=/-(.)/g,Xv=/;\s*$/,Ho={},Gd={},Qv=!1,Zv=!1,Iv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),hc="http://www.w3.org/1998/Math/MathML",jo="http://www.w3.org/2000/svg",CE=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),pc={accept:"accept",acceptcharset:"acceptCharset","accept-charset":"acceptCharset",accesskey:"accessKey",action:"action",allowfullscreen:"allowFullScreen",alt:"alt",as:"as",async:"async",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autosave:"autoSave",capture:"capture",cellpadding:"cellPadding",cellspacing:"cellSpacing",challenge:"challenge",charset:"charSet",checked:"checked",children:"children",cite:"cite",class:"className",classid:"classID",classname:"className",cols:"cols",colspan:"colSpan",content:"content",contenteditable:"contentEditable",contextmenu:"contextMenu",controls:"controls",controlslist:"controlsList",coords:"coords",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",data:"data",datetime:"dateTime",default:"default",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",defer:"defer",dir:"dir",disabled:"disabled",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback",download:"download",draggable:"draggable",enctype:"encType",enterkeyhint:"enterKeyHint",fetchpriority:"fetchPriority",for:"htmlFor",form:"form",formmethod:"formMethod",formaction:"formAction",formenctype:"formEncType",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder",headers:"headers",height:"height",hidden:"hidden",high:"high",href:"href",hreflang:"hrefLang",htmlfor:"htmlFor",httpequiv:"httpEquiv","http-equiv":"httpEquiv",icon:"icon",id:"id",imagesizes:"imageSizes",imagesrcset:"imageSrcSet",inert:"inert",innerhtml:"innerHTML",inputmode:"inputMode",integrity:"integrity",is:"is",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",keyparams:"keyParams",keytype:"keyType",kind:"kind",label:"label",lang:"lang",list:"list",loop:"loop",low:"low",manifest:"manifest",marginwidth:"marginWidth",marginheight:"marginHeight",max:"max",maxlength:"maxLength",media:"media",mediagroup:"mediaGroup",method:"method",min:"min",minlength:"minLength",multiple:"multiple",muted:"muted",name:"name",nomodule:"noModule",nonce:"nonce",novalidate:"noValidate",open:"open",optimum:"optimum",pattern:"pattern",placeholder:"placeholder",playsinline:"playsInline",poster:"poster",preload:"preload",profile:"profile",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",rel:"rel",required:"required",reversed:"reversed",role:"role",rows:"rows",rowspan:"rowSpan",sandbox:"sandbox",scope:"scope",scoped:"scoped",scrolling:"scrolling",seamless:"seamless",selected:"selected",shape:"shape",size:"size",sizes:"sizes",span:"span",spellcheck:"spellCheck",src:"src",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",start:"start",step:"step",style:"style",summary:"summary",tabindex:"tabIndex",target:"target",title:"title",type:"type",usemap:"useMap",value:"value",width:"width",wmode:"wmode",wrap:"wrap",about:"about",accentheight:"accentHeight","accent-height":"accentHeight",accumulate:"accumulate",additive:"additive",alignmentbaseline:"alignmentBaseline","alignment-baseline":"alignmentBaseline",allowreorder:"allowReorder",alphabetic:"alphabetic",amplitude:"amplitude",arabicform:"arabicForm","arabic-form":"arabicForm",ascent:"ascent",attributename:"attributeName",attributetype:"attributeType",autoreverse:"autoReverse",azimuth:"azimuth",basefrequency:"baseFrequency",baselineshift:"baselineShift","baseline-shift":"baselineShift",baseprofile:"baseProfile",bbox:"bbox",begin:"begin",bias:"bias",by:"by",calcmode:"calcMode",capheight:"capHeight","cap-height":"capHeight",clip:"clip",clippath:"clipPath","clip-path":"clipPath",clippathunits:"clipPathUnits",cliprule:"clipRule","clip-rule":"clipRule",color:"color",colorinterpolation:"colorInterpolation","color-interpolation":"colorInterpolation",colorinterpolationfilters:"colorInterpolationFilters","color-interpolation-filters":"colorInterpolationFilters",colorprofile:"colorProfile","color-profile":"colorProfile",colorrendering:"colorRendering","color-rendering":"colorRendering",contentscripttype:"contentScriptType",contentstyletype:"contentStyleType",cursor:"cursor",cx:"cx",cy:"cy",d:"d",datatype:"datatype",decelerate:"decelerate",descent:"descent",diffuseconstant:"diffuseConstant",direction:"direction",display:"display",divisor:"divisor",dominantbaseline:"dominantBaseline","dominant-baseline":"dominantBaseline",dur:"dur",dx:"dx",dy:"dy",edgemode:"edgeMode",elevation:"elevation",enablebackground:"enableBackground","enable-background":"enableBackground",end:"end",exponent:"exponent",externalresourcesrequired:"externalResourcesRequired",fill:"fill",fillopacity:"fillOpacity","fill-opacity":"fillOpacity",fillrule:"fillRule","fill-rule":"fillRule",filter:"filter",filterres:"filterRes",filterunits:"filterUnits",floodopacity:"floodOpacity","flood-opacity":"floodOpacity",floodcolor:"floodColor","flood-color":"floodColor",focusable:"focusable",fontfamily:"fontFamily","font-family":"fontFamily",fontsize:"fontSize","font-size":"fontSize",fontsizeadjust:"fontSizeAdjust","font-size-adjust":"fontSizeAdjust",fontstretch:"fontStretch","font-stretch":"fontStretch",fontstyle:"fontStyle","font-style":"fontStyle",fontvariant:"fontVariant","font-variant":"fontVariant",fontweight:"fontWeight","font-weight":"fontWeight",format:"format",from:"from",fx:"fx",fy:"fy",g1:"g1",g2:"g2",glyphname:"glyphName","glyph-name":"glyphName",glyphorientationhorizontal:"glyphOrientationHorizontal","glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphorientationvertical:"glyphOrientationVertical","glyph-orientation-vertical":"glyphOrientationVertical",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",hanging:"hanging",horizadvx:"horizAdvX","horiz-adv-x":"horizAdvX",horizoriginx:"horizOriginX","horiz-origin-x":"horizOriginX",ideographic:"ideographic",imagerendering:"imageRendering","image-rendering":"imageRendering",in2:"in2",in:"in",inlist:"inlist",intercept:"intercept",k1:"k1",k2:"k2",k3:"k3",k4:"k4",k:"k",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",kerning:"kerning",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",letterspacing:"letterSpacing","letter-spacing":"letterSpacing",lightingcolor:"lightingColor","lighting-color":"lightingColor",limitingconeangle:"limitingConeAngle",local:"local",markerend:"markerEnd","marker-end":"markerEnd",markerheight:"markerHeight",markermid:"markerMid","marker-mid":"markerMid",markerstart:"markerStart","marker-start":"markerStart",markerunits:"markerUnits",markerwidth:"markerWidth",mask:"mask",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",mathematical:"mathematical",mode:"mode",numoctaves:"numOctaves",offset:"offset",opacity:"opacity",operator:"operator",order:"order",orient:"orient",orientation:"orientation",origin:"origin",overflow:"overflow",overlineposition:"overlinePosition","overline-position":"overlinePosition",overlinethickness:"overlineThickness","overline-thickness":"overlineThickness",paintorder:"paintOrder","paint-order":"paintOrder",panose1:"panose1","panose-1":"panose1",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointerevents:"pointerEvents","pointer-events":"pointerEvents",points:"points",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",popover:"popover",popovertarget:"popoverTarget",popovertargetaction:"popoverTargetAction",prefix:"prefix",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",property:"property",r:"r",radius:"radius",refx:"refX",refy:"refY",renderingintent:"renderingIntent","rendering-intent":"renderingIntent",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",resource:"resource",restart:"restart",result:"result",results:"results",rotate:"rotate",rx:"rx",ry:"ry",scale:"scale",security:"security",seed:"seed",shaperendering:"shapeRendering","shape-rendering":"shapeRendering",slope:"slope",spacing:"spacing",specularconstant:"specularConstant",specularexponent:"specularExponent",speed:"speed",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stemh:"stemh",stemv:"stemv",stitchtiles:"stitchTiles",stopcolor:"stopColor","stop-color":"stopColor",stopopacity:"stopOpacity","stop-opacity":"stopOpacity",strikethroughposition:"strikethroughPosition","strikethrough-position":"strikethroughPosition",strikethroughthickness:"strikethroughThickness","strikethrough-thickness":"strikethroughThickness",string:"string",stroke:"stroke",strokedasharray:"strokeDasharray","stroke-dasharray":"strokeDasharray",strokedashoffset:"strokeDashoffset","stroke-dashoffset":"strokeDashoffset",strokelinecap:"strokeLinecap","stroke-linecap":"strokeLinecap",strokelinejoin:"strokeLinejoin","stroke-linejoin":"strokeLinejoin",strokemiterlimit:"strokeMiterlimit","stroke-miterlimit":"strokeMiterlimit",strokewidth:"strokeWidth","stroke-width":"strokeWidth",strokeopacity:"strokeOpacity","stroke-opacity":"strokeOpacity",suppresscontenteditablewarning:"suppressContentEditableWarning",suppresshydrationwarning:"suppressHydrationWarning",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textanchor:"textAnchor","text-anchor":"textAnchor",textdecoration:"textDecoration","text-decoration":"textDecoration",textlength:"textLength",textrendering:"textRendering","text-rendering":"textRendering",to:"to",transform:"transform",transformorigin:"transformOrigin","transform-origin":"transformOrigin",typeof:"typeof",u1:"u1",u2:"u2",underlineposition:"underlinePosition","underline-position":"underlinePosition",underlinethickness:"underlineThickness","underline-thickness":"underlineThickness",unicode:"unicode",unicodebidi:"unicodeBidi","unicode-bidi":"unicodeBidi",unicoderange:"unicodeRange","unicode-range":"unicodeRange",unitsperem:"unitsPerEm","units-per-em":"unitsPerEm",unselectable:"unselectable",valphabetic:"vAlphabetic","v-alphabetic":"vAlphabetic",values:"values",vectoreffect:"vectorEffect","vector-effect":"vectorEffect",version:"version",vertadvy:"vertAdvY","vert-adv-y":"vertAdvY",vertoriginx:"vertOriginX","vert-origin-x":"vertOriginX",vertoriginy:"vertOriginY","vert-origin-y":"vertOriginY",vhanging:"vHanging","v-hanging":"vHanging",videographic:"vIdeographic","v-ideographic":"vIdeographic",viewbox:"viewBox",viewtarget:"viewTarget",visibility:"visibility",vmathematical:"vMathematical","v-mathematical":"vMathematical",vocab:"vocab",widths:"widths",wordspacing:"wordSpacing","word-spacing":"wordSpacing",writingmode:"writingMode","writing-mode":"writingMode",x1:"x1",x2:"x2",x:"x",xchannelselector:"xChannelSelector",xheight:"xHeight","x-height":"xHeight",xlinkactuate:"xlinkActuate","xlink:actuate":"xlinkActuate",xlinkarcrole:"xlinkArcrole","xlink:arcrole":"xlinkArcrole",xlinkhref:"xlinkHref","xlink:href":"xlinkHref",xlinkrole:"xlinkRole","xlink:role":"xlinkRole",xlinkshow:"xlinkShow","xlink:show":"xlinkShow",xlinktitle:"xlinkTitle","xlink:title":"xlinkTitle",xlinktype:"xlinkType","xlink:type":"xlinkType",xmlbase:"xmlBase","xml:base":"xmlBase",xmllang:"xmlLang","xml:lang":"xmlLang",xmlns:"xmlns","xml:space":"xmlSpace",xmlnsxlink:"xmlnsXlink","xmlns:xlink":"xmlnsXlink",xmlspace:"xmlSpace",y1:"y1",y2:"y2",y:"y",ychannelselector:"yChannelSelector",z:"z",zoomandpan:"zoomAndPan"},Jv={"aria-current":0,"aria-description":0,"aria-details":0,"aria-disabled":0,"aria-hidden":0,"aria-invalid":0,"aria-keyshortcuts":0,"aria-label":0,"aria-roledescription":0,"aria-autocomplete":0,"aria-checked":0,"aria-expanded":0,"aria-haspopup":0,"aria-level":0,"aria-modal":0,"aria-multiline":0,"aria-multiselectable":0,"aria-orientation":0,"aria-placeholder":0,"aria-pressed":0,"aria-readonly":0,"aria-required":0,"aria-selected":0,"aria-sort":0,"aria-valuemax":0,"aria-valuemin":0,"aria-valuenow":0,"aria-valuetext":0,"aria-atomic":0,"aria-busy":0,"aria-live":0,"aria-relevant":0,"aria-dropeffect":0,"aria-grabbed":0,"aria-activedescendant":0,"aria-colcount":0,"aria-colindex":0,"aria-colspan":0,"aria-controls":0,"aria-describedby":0,"aria-errormessage":0,"aria-flowto":0,"aria-labelledby":0,"aria-owns":0,"aria-posinset":0,"aria-rowcount":0,"aria-rowindex":0,"aria-rowspan":0,"aria-setsize":0},No={},wE=RegExp("^(aria)-[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),xE=RegExp("^(aria)[A-Z][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Kv=!1,$t={},Pv=/^on./,DE=/^on[^A-Z]/,ME=RegExp("^(aria)-[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),_E=RegExp("^(aria)[A-Z][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),zE=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i,fu=null,ko=null,Lo=null,Xd=!1,la=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Qd=!1;if(la)try{var du={};Object.defineProperty(du,"passive",{get:function(){Qd=!0}}),window.addEventListener("test",du,du),window.removeEventListener("test",du,du)}catch{Qd=!1}var nl=null,Zd=null,mc=null,kl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},yc=It(kl),hu=_e({},kl,{view:0,detail:0}),UE=It(hu),Id,Jd,pu,gc=_e({},hu,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==pu&&(pu&&e.type==="mousemove"?(Id=e.screenX-pu.screenX,Jd=e.screenY-pu.screenY):Jd=Id=0,pu=e),Id)},movementY:function(e){return"movementY"in e?e.movementY:Jd}}),Wv=It(gc),HE=_e({},gc,{dataTransfer:0}),jE=It(HE),NE=_e({},hu,{relatedTarget:0}),Kd=It(NE),kE=_e({},kl,{animationName:0,elapsedTime:0,pseudoElement:0}),LE=It(kE),BE=_e({},kl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),YE=It(BE),qE=_e({},kl,{data:0}),Fv=It(qE),$E=Fv,VE={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},GE={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},XE={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"},QE=_e({},hu,{key:function(e){if(e.key){var t=VE[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=fr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?GE[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hs,charCode:function(e){return e.type==="keypress"?fr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?fr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ZE=It(QE),IE=_e({},gc,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),e0=It(IE),JE=_e({},hu,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hs}),KE=It(JE),PE=_e({},kl,{propertyName:0,elapsedTime:0,pseudoElement:0}),WE=It(PE),FE=_e({},gc,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),eR=It(FE),tR=_e({},kl,{newState:0,oldState:0}),nR=It(tR),aR=[9,13,27,32],t0=229,Pd=la&&"CompositionEvent"in window,mu=null;la&&"documentMode"in document&&(mu=document.documentMode);var lR=la&&"TextEvent"in window&&!mu,n0=la&&(!Pd||mu&&8<mu&&11>=mu),a0=32,l0=String.fromCharCode(a0),o0=!1,Bo=!1,oR={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0},yu=null,gu=null,i0=!1;la&&(i0=jS("input")&&(!document.documentMode||9<document.documentMode));var Vt=typeof Object.is=="function"?Object.is:qS,iR=la&&"documentMode"in document&&11>=document.documentMode,Yo=null,Wd=null,vu=null,Fd=!1,qo={animationend:Tl("Animation","AnimationEnd"),animationiteration:Tl("Animation","AnimationIteration"),animationstart:Tl("Animation","AnimationStart"),transitionrun:Tl("Transition","TransitionRun"),transitionstart:Tl("Transition","TransitionStart"),transitioncancel:Tl("Transition","TransitionCancel"),transitionend:Tl("Transition","TransitionEnd")},eh={},u0={};la&&(u0=document.createElement("div").style,"AnimationEvent"in window||(delete qo.animationend.animation,delete qo.animationiteration.animation,delete qo.animationstart.animation),"TransitionEvent"in window||delete qo.transitionend.transition);var r0=El("animationend"),c0=El("animationiteration"),s0=El("animationstart"),uR=El("transitionrun"),rR=El("transitionstart"),cR=El("transitioncancel"),f0=El("transitionend"),d0=new Map,th="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");th.push("scrollEnd");var nh=new WeakMap,ah=1,Ll=2,En=[],$o=0,lh=0,al={};Object.freeze(al);var Rn=null,Vo=null,lt=0,sR=1,Ht=2,Bt=8,Yn=16,h0=64,p0=!1;try{var m0=Object.preventExtensions({})}catch{p0=!0}var Go=[],Xo=0,vc=null,bc=0,An=[],On=0,Bl=null,Oa=1,Ca="",Gt=null,st=null,qe=!1,wa=!1,Cn=null,Yl=null,oa=!1,oh=Error("Hydration Mismatch Exception: This is not a real error, and should not leak into userspace. If you're seeing this, it's likely a bug in React."),y0=0;if(typeof performance=="object"&&typeof performance.now=="function")var fR=performance,g0=function(){return fR.now()};else{var dR=Date;g0=function(){return dR.now()}}var ih=Ie(null),uh=Ie(null),v0={},Sc=null,Qo=null,Zo=!1,hR=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},pR=gt.unstable_scheduleCallback,mR=gt.unstable_NormalPriority,Et={$$typeof:Fn,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0,_currentRenderer:null,_currentRenderer2:null},Io=gt.unstable_now,b0=-0,Tc=-0,Ft=-1.1,ql=-0,Ec=!1,Rc=!1,bu=null,rh=0,$l=0,Jo=null,S0=x.S;x.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&VS(e,t),S0!==null&&S0(e,t)};var Vl=Ie(null),qn={recordUnsafeLifecycleWarnings:function(){},flushPendingUnsafeLifecycleWarnings:function(){},recordLegacyContextWarning:function(){},flushLegacyContextWarning:function(){},discardPendingWarnings:function(){}},Su=[],Tu=[],Eu=[],Ru=[],Au=[],Ou=[],Gl=new Set;qn.recordUnsafeLifecycleWarnings=function(e,t){Gl.has(e.type)||(typeof t.componentWillMount=="function"&&t.componentWillMount.__suppressDeprecationWarning!==!0&&Su.push(e),e.mode&Bt&&typeof t.UNSAFE_componentWillMount=="function"&&Tu.push(e),typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps.__suppressDeprecationWarning!==!0&&Eu.push(e),e.mode&Bt&&typeof t.UNSAFE_componentWillReceiveProps=="function"&&Ru.push(e),typeof t.componentWillUpdate=="function"&&t.componentWillUpdate.__suppressDeprecationWarning!==!0&&Au.push(e),e.mode&Bt&&typeof t.UNSAFE_componentWillUpdate=="function"&&Ou.push(e))},qn.flushPendingUnsafeLifecycleWarnings=function(){var e=new Set;0<Su.length&&(Su.forEach(function(r){e.add(X(r)||"Component"),Gl.add(r.type)}),Su=[]);var t=new Set;0<Tu.length&&(Tu.forEach(function(r){t.add(X(r)||"Component"),Gl.add(r.type)}),Tu=[]);var n=new Set;0<Eu.length&&(Eu.forEach(function(r){n.add(X(r)||"Component"),Gl.add(r.type)}),Eu=[]);var a=new Set;0<Ru.length&&(Ru.forEach(function(r){a.add(X(r)||"Component"),Gl.add(r.type)}),Ru=[]);var l=new Set;0<Au.length&&(Au.forEach(function(r){l.add(X(r)||"Component"),Gl.add(r.type)}),Au=[]);var o=new Set;if(0<Ou.length&&(Ou.forEach(function(r){o.add(X(r)||"Component"),Gl.add(r.type)}),Ou=[]),0<t.size){var u=C(t);console.error(`Using UNSAFE_componentWillMount in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.

Please update the following components: %s`,u)}0<a.size&&(u=C(a),console.error(`Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state

Please update the following components: %s`,u)),0<o.size&&(u=C(o),console.error(`Using UNSAFE_componentWillUpdate in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.

Please update the following components: %s`,u)),0<e.size&&(u=C(e),console.warn(`componentWillMount has been renamed, and is not recommended for use. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.
* Rename componentWillMount to UNSAFE_componentWillMount to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,u)),0<n.size&&(u=C(n),console.warn(`componentWillReceiveProps has been renamed, and is not recommended for use. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state
* Rename componentWillReceiveProps to UNSAFE_componentWillReceiveProps to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,u)),0<l.size&&(u=C(l),console.warn(`componentWillUpdate has been renamed, and is not recommended for use. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* Rename componentWillUpdate to UNSAFE_componentWillUpdate to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,u))};var Ac=new Map,T0=new Set;qn.recordLegacyContextWarning=function(e,t){for(var n=null,a=e;a!==null;)a.mode&Bt&&(n=a),a=a.return;n===null?console.error("Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue."):!T0.has(e.type)&&(a=Ac.get(n),e.type.contextTypes!=null||e.type.childContextTypes!=null||t!==null&&typeof t.getChildContext=="function")&&(a===void 0&&(a=[],Ac.set(n,a)),a.push(e))},qn.flushLegacyContextWarning=function(){Ac.forEach(function(e){if(e.length!==0){var t=e[0],n=new Set;e.forEach(function(l){n.add(X(l)||"Component"),T0.add(l.type)});var a=C(n);re(t,function(){console.error(`Legacy context API has been detected within a strict-mode tree.

The old API will be supported in all 16.x releases, but applications using it should migrate to the new version.

Please update the following components: %s

Learn more about this warning here: https://react.dev/link/legacy-context`,a)})}})},qn.discardPendingWarnings=function(){Su=[],Tu=[],Eu=[],Ru=[],Au=[],Ou=[],Ac=new Map};var Cu=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`."),E0=Error("Suspense Exception: This is not a real error, and should not leak into userspace. If you're seeing this, it's likely a bug in React."),Oc=Error("Suspense Exception: This is not a real error! It's an implementation detail of `useActionState` to interrupt the current render. You must either rethrow it immediately, or move the `useActionState` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary."),ch={then:function(){console.error('Internal React error: A listener was unexpectedly attached to a "noop" thenable. This is a bug in React. Please file an issue.')}},wu=null,Cc=!1,wn=0,xn=1,Xt=2,jt=4,Rt=8,R0=0,A0=1,O0=2,sh=3,ll=!1,C0=!1,fh=null,dh=!1,Ko=Ie(null),wc=Ie(0),Po,w0=new Set,x0=new Set,hh=new Set,D0=new Set,ol=0,Te=null,We=null,vt=null,xc=!1,Wo=!1,Xl=!1,Dc=0,xu=0,xa=null,yR=0,gR=25,w=null,Dn=null,Da=-1,Du=!1,Mc={readContext:ut,use:Xa,useCallback:dt,useContext:dt,useEffect:dt,useImperativeHandle:dt,useLayoutEffect:dt,useInsertionEffect:dt,useMemo:dt,useReducer:dt,useRef:dt,useState:dt,useDebugValue:dt,useDeferredValue:dt,useTransition:dt,useSyncExternalStore:dt,useId:dt,useHostTransitionStatus:dt,useFormState:dt,useActionState:dt,useOptimistic:dt,useMemoCache:dt,useCacheRefresh:dt},ph=null,M0=null,mh=null,_0=null,ia=null,$n=null,_c=null;ph={readContext:function(e){return ut(e)},use:Xa,useCallback:function(e,t){return w="useCallback",Ne(),go(t),Sf(e,t)},useContext:function(e){return w="useContext",Ne(),ut(e)},useEffect:function(e,t){return w="useEffect",Ne(),go(t),_r(e,t)},useImperativeHandle:function(e,t,n){return w="useImperativeHandle",Ne(),go(n),bf(e,t,n)},useInsertionEffect:function(e,t){w="useInsertionEffect",Ne(),go(t),zl(4,Xt,e,t)},useLayoutEffect:function(e,t){return w="useLayoutEffect",Ne(),go(t),vf(e,t)},useMemo:function(e,t){w="useMemo",Ne(),go(t);var n=x.H;x.H=ia;try{return Tf(e,t)}finally{x.H=n}},useReducer:function(e,t,n){w="useReducer",Ne();var a=x.H;x.H=ia;try{return sf(e,t,n)}finally{x.H=a}},useRef:function(e){return w="useRef",Ne(),gf(e)},useState:function(e){w="useState",Ne();var t=x.H;x.H=ia;try{return pf(e)}finally{x.H=t}},useDebugValue:function(){w="useDebugValue",Ne()},useDeferredValue:function(e,t){return w="useDeferredValue",Ne(),Ef(e,t)},useTransition:function(){return w="useTransition",Ne(),Of()},useSyncExternalStore:function(e,t,n){return w="useSyncExternalStore",Ne(),df(e,t,n)},useId:function(){return w="useId",Ne(),Cf()},useFormState:function(e,t){return w="useFormState",Ne(),Cr(),bo(e,t)},useActionState:function(e,t){return w="useActionState",Ne(),bo(e,t)},useOptimistic:function(e){return w="useOptimistic",Ne(),mf(e)},useHostTransitionStatus:Ul,useMemoCache:_l,useCacheRefresh:function(){return w="useCacheRefresh",Ne(),wf()}},M0={readContext:function(e){return ut(e)},use:Xa,useCallback:function(e,t){return w="useCallback",k(),Sf(e,t)},useContext:function(e){return w="useContext",k(),ut(e)},useEffect:function(e,t){return w="useEffect",k(),_r(e,t)},useImperativeHandle:function(e,t,n){return w="useImperativeHandle",k(),bf(e,t,n)},useInsertionEffect:function(e,t){w="useInsertionEffect",k(),zl(4,Xt,e,t)},useLayoutEffect:function(e,t){return w="useLayoutEffect",k(),vf(e,t)},useMemo:function(e,t){w="useMemo",k();var n=x.H;x.H=ia;try{return Tf(e,t)}finally{x.H=n}},useReducer:function(e,t,n){w="useReducer",k();var a=x.H;x.H=ia;try{return sf(e,t,n)}finally{x.H=a}},useRef:function(e){return w="useRef",k(),gf(e)},useState:function(e){w="useState",k();var t=x.H;x.H=ia;try{return pf(e)}finally{x.H=t}},useDebugValue:function(){w="useDebugValue",k()},useDeferredValue:function(e,t){return w="useDeferredValue",k(),Ef(e,t)},useTransition:function(){return w="useTransition",k(),Of()},useSyncExternalStore:function(e,t,n){return w="useSyncExternalStore",k(),df(e,t,n)},useId:function(){return w="useId",k(),Cf()},useActionState:function(e,t){return w="useActionState",k(),bo(e,t)},useFormState:function(e,t){return w="useFormState",k(),Cr(),bo(e,t)},useOptimistic:function(e){return w="useOptimistic",k(),mf(e)},useHostTransitionStatus:Ul,useMemoCache:_l,useCacheRefresh:function(){return w="useCacheRefresh",k(),wf()}},mh={readContext:function(e){return ut(e)},use:Xa,useCallback:function(e,t){return w="useCallback",k(),Ur(e,t)},useContext:function(e){return w="useContext",k(),ut(e)},useEffect:function(e,t){w="useEffect",k(),Pt(2048,Rt,e,t)},useImperativeHandle:function(e,t,n){return w="useImperativeHandle",k(),zr(e,t,n)},useInsertionEffect:function(e,t){return w="useInsertionEffect",k(),Pt(4,Xt,e,t)},useLayoutEffect:function(e,t){return w="useLayoutEffect",k(),Pt(4,jt,e,t)},useMemo:function(e,t){w="useMemo",k();var n=x.H;x.H=$n;try{return Hr(e,t)}finally{x.H=n}},useReducer:function(e,t,n){w="useReducer",k();var a=x.H;x.H=$n;try{return vo(e,t,n)}finally{x.H=a}},useRef:function(){return w="useRef",k(),Je().memoizedState},useState:function(){w="useState",k();var e=x.H;x.H=$n;try{return vo(Ln)}finally{x.H=e}},useDebugValue:function(){w="useDebugValue",k()},useDeferredValue:function(e,t){return w="useDeferredValue",k(),ry(e,t)},useTransition:function(){return w="useTransition",k(),py()},useSyncExternalStore:function(e,t,n){return w="useSyncExternalStore",k(),wr(e,t,n)},useId:function(){return w="useId",k(),Je().memoizedState},useFormState:function(e){return w="useFormState",k(),Cr(),xr(e)},useActionState:function(e){return w="useActionState",k(),xr(e)},useOptimistic:function(e,t){return w="useOptimistic",k(),Wm(e,t)},useHostTransitionStatus:Ul,useMemoCache:_l,useCacheRefresh:function(){return w="useCacheRefresh",k(),Je().memoizedState}},_0={readContext:function(e){return ut(e)},use:Xa,useCallback:function(e,t){return w="useCallback",k(),Ur(e,t)},useContext:function(e){return w="useContext",k(),ut(e)},useEffect:function(e,t){w="useEffect",k(),Pt(2048,Rt,e,t)},useImperativeHandle:function(e,t,n){return w="useImperativeHandle",k(),zr(e,t,n)},useInsertionEffect:function(e,t){return w="useInsertionEffect",k(),Pt(4,Xt,e,t)},useLayoutEffect:function(e,t){return w="useLayoutEffect",k(),Pt(4,jt,e,t)},useMemo:function(e,t){w="useMemo",k();var n=x.H;x.H=_c;try{return Hr(e,t)}finally{x.H=n}},useReducer:function(e,t,n){w="useReducer",k();var a=x.H;x.H=_c;try{return Gi(e,t,n)}finally{x.H=a}},useRef:function(){return w="useRef",k(),Je().memoizedState},useState:function(){w="useState",k();var e=x.H;x.H=_c;try{return Gi(Ln)}finally{x.H=e}},useDebugValue:function(){w="useDebugValue",k()},useDeferredValue:function(e,t){return w="useDeferredValue",k(),cy(e,t)},useTransition:function(){return w="useTransition",k(),my()},useSyncExternalStore:function(e,t,n){return w="useSyncExternalStore",k(),wr(e,t,n)},useId:function(){return w="useId",k(),Je().memoizedState},useFormState:function(e){return w="useFormState",k(),Cr(),Dr(e)},useActionState:function(e){return w="useActionState",k(),Dr(e)},useOptimistic:function(e,t){return w="useOptimistic",k(),ey(e,t)},useHostTransitionStatus:Ul,useMemoCache:_l,useCacheRefresh:function(){return w="useCacheRefresh",k(),Je().memoizedState}},ia={readContext:function(e){return Z(),ut(e)},use:function(e){return R(),Xa(e)},useCallback:function(e,t){return w="useCallback",R(),Ne(),Sf(e,t)},useContext:function(e){return w="useContext",R(),Ne(),ut(e)},useEffect:function(e,t){return w="useEffect",R(),Ne(),_r(e,t)},useImperativeHandle:function(e,t,n){return w="useImperativeHandle",R(),Ne(),bf(e,t,n)},useInsertionEffect:function(e,t){w="useInsertionEffect",R(),Ne(),zl(4,Xt,e,t)},useLayoutEffect:function(e,t){return w="useLayoutEffect",R(),Ne(),vf(e,t)},useMemo:function(e,t){w="useMemo",R(),Ne();var n=x.H;x.H=ia;try{return Tf(e,t)}finally{x.H=n}},useReducer:function(e,t,n){w="useReducer",R(),Ne();var a=x.H;x.H=ia;try{return sf(e,t,n)}finally{x.H=a}},useRef:function(e){return w="useRef",R(),Ne(),gf(e)},useState:function(e){w="useState",R(),Ne();var t=x.H;x.H=ia;try{return pf(e)}finally{x.H=t}},useDebugValue:function(){w="useDebugValue",R(),Ne()},useDeferredValue:function(e,t){return w="useDeferredValue",R(),Ne(),Ef(e,t)},useTransition:function(){return w="useTransition",R(),Ne(),Of()},useSyncExternalStore:function(e,t,n){return w="useSyncExternalStore",R(),Ne(),df(e,t,n)},useId:function(){return w="useId",R(),Ne(),Cf()},useFormState:function(e,t){return w="useFormState",R(),Ne(),bo(e,t)},useActionState:function(e,t){return w="useActionState",R(),Ne(),bo(e,t)},useOptimistic:function(e){return w="useOptimistic",R(),Ne(),mf(e)},useMemoCache:function(e){return R(),_l(e)},useHostTransitionStatus:Ul,useCacheRefresh:function(){return w="useCacheRefresh",Ne(),wf()}},$n={readContext:function(e){return Z(),ut(e)},use:function(e){return R(),Xa(e)},useCallback:function(e,t){return w="useCallback",R(),k(),Ur(e,t)},useContext:function(e){return w="useContext",R(),k(),ut(e)},useEffect:function(e,t){w="useEffect",R(),k(),Pt(2048,Rt,e,t)},useImperativeHandle:function(e,t,n){return w="useImperativeHandle",R(),k(),zr(e,t,n)},useInsertionEffect:function(e,t){return w="useInsertionEffect",R(),k(),Pt(4,Xt,e,t)},useLayoutEffect:function(e,t){return w="useLayoutEffect",R(),k(),Pt(4,jt,e,t)},useMemo:function(e,t){w="useMemo",R(),k();var n=x.H;x.H=$n;try{return Hr(e,t)}finally{x.H=n}},useReducer:function(e,t,n){w="useReducer",R(),k();var a=x.H;x.H=$n;try{return vo(e,t,n)}finally{x.H=a}},useRef:function(){return w="useRef",R(),k(),Je().memoizedState},useState:function(){w="useState",R(),k();var e=x.H;x.H=$n;try{return vo(Ln)}finally{x.H=e}},useDebugValue:function(){w="useDebugValue",R(),k()},useDeferredValue:function(e,t){return w="useDeferredValue",R(),k(),ry(e,t)},useTransition:function(){return w="useTransition",R(),k(),py()},useSyncExternalStore:function(e,t,n){return w="useSyncExternalStore",R(),k(),wr(e,t,n)},useId:function(){return w="useId",R(),k(),Je().memoizedState},useFormState:function(e){return w="useFormState",R(),k(),xr(e)},useActionState:function(e){return w="useActionState",R(),k(),xr(e)},useOptimistic:function(e,t){return w="useOptimistic",R(),k(),Wm(e,t)},useMemoCache:function(e){return R(),_l(e)},useHostTransitionStatus:Ul,useCacheRefresh:function(){return w="useCacheRefresh",k(),Je().memoizedState}},_c={readContext:function(e){return Z(),ut(e)},use:function(e){return R(),Xa(e)},useCallback:function(e,t){return w="useCallback",R(),k(),Ur(e,t)},useContext:function(e){return w="useContext",R(),k(),ut(e)},useEffect:function(e,t){w="useEffect",R(),k(),Pt(2048,Rt,e,t)},useImperativeHandle:function(e,t,n){return w="useImperativeHandle",R(),k(),zr(e,t,n)},useInsertionEffect:function(e,t){return w="useInsertionEffect",R(),k(),Pt(4,Xt,e,t)},useLayoutEffect:function(e,t){return w="useLayoutEffect",R(),k(),Pt(4,jt,e,t)},useMemo:function(e,t){w="useMemo",R(),k();var n=x.H;x.H=$n;try{return Hr(e,t)}finally{x.H=n}},useReducer:function(e,t,n){w="useReducer",R(),k();var a=x.H;x.H=$n;try{return Gi(e,t,n)}finally{x.H=a}},useRef:function(){return w="useRef",R(),k(),Je().memoizedState},useState:function(){w="useState",R(),k();var e=x.H;x.H=$n;try{return Gi(Ln)}finally{x.H=e}},useDebugValue:function(){w="useDebugValue",R(),k()},useDeferredValue:function(e,t){return w="useDeferredValue",R(),k(),cy(e,t)},useTransition:function(){return w="useTransition",R(),k(),my()},useSyncExternalStore:function(e,t,n){return w="useSyncExternalStore",R(),k(),wr(e,t,n)},useId:function(){return w="useId",R(),k(),Je().memoizedState},useFormState:function(e){return w="useFormState",R(),k(),Dr(e)},useActionState:function(e){return w="useActionState",R(),k(),Dr(e)},useOptimistic:function(e,t){return w="useOptimistic",R(),k(),ey(e,t)},useMemoCache:function(e){return R(),_l(e)},useHostTransitionStatus:Ul,useCacheRefresh:function(){return w="useCacheRefresh",k(),Je().memoizedState}};var z0={react_stack_bottom_frame:function(e,t,n){var a=aa;aa=!0;try{return e(t,n)}finally{aa=a}}},yh=z0.react_stack_bottom_frame.bind(z0),U0={react_stack_bottom_frame:function(e){var t=aa;aa=!0;try{return e.render()}finally{aa=t}}},H0=U0.react_stack_bottom_frame.bind(U0),j0={react_stack_bottom_frame:function(e,t){try{t.componentDidMount()}catch(n){Ke(e,e.return,n)}}},gh=j0.react_stack_bottom_frame.bind(j0),N0={react_stack_bottom_frame:function(e,t,n,a,l){try{t.componentDidUpdate(n,a,l)}catch(o){Ke(e,e.return,o)}}},k0=N0.react_stack_bottom_frame.bind(N0),L0={react_stack_bottom_frame:function(e,t){var n=t.stack;e.componentDidCatch(t.value,{componentStack:n!==null?n:""})}},vR=L0.react_stack_bottom_frame.bind(L0),B0={react_stack_bottom_frame:function(e,t,n){try{n.componentWillUnmount()}catch(a){Ke(e,t,a)}}},Y0=B0.react_stack_bottom_frame.bind(B0),q0={react_stack_bottom_frame:function(e){e.resourceKind!=null&&console.error("Expected only SimpleEffects when enableUseEffectCRUDOverload is disabled, got %s",e.resourceKind);var t=e.create;return e=e.inst,t=t(),e.destroy=t}},bR=q0.react_stack_bottom_frame.bind(q0),$0={react_stack_bottom_frame:function(e,t,n){try{n()}catch(a){Ke(e,t,a)}}},SR=$0.react_stack_bottom_frame.bind($0),V0={react_stack_bottom_frame:function(e){var t=e._init;return t(e._payload)}},il=V0.react_stack_bottom_frame.bind(V0),Fo=null,Mu=0,xe=null,vh,G0=vh=!1,X0={},Q0={},Z0={};H=function(e,t,n){if(n!==null&&typeof n=="object"&&n._store&&(!n._store.validated&&n.key==null||n._store.validated===2)){if(typeof n._store!="object")throw Error("React Component in warnForMissingKey should have a _store. This error is likely caused by a bug in React. Please file an issue.");n._store.validated=1;var a=X(e),l=a||"null";if(!X0[l]){X0[l]=!0,n=n._owner,e=e._debugOwner;var o="";e&&typeof e.tag=="number"&&(l=X(e))&&(o=`

Check the render method of \``+l+"`."),o||a&&(o=`

Check the top-level render call using <`+a+">.");var u="";n!=null&&e!==n&&(a=null,typeof n.tag=="number"?a=X(n):typeof n.name=="string"&&(a=n.name),a&&(u=" It was passed a child from "+a+".")),re(t,function(){console.error('Each child in a list should have a unique "key" prop.%s%s See https://react.dev/link/warning-keys for more information.',o,u)})}}};var ei=by(!0),I0=by(!1),Mn=Ie(null),ua=null,ti=1,_u=2,At=Ie(0),J0={},K0=new Set,P0=new Set,W0=new Set,F0=new Set,e1=new Set,t1=new Set,n1=new Set,a1=new Set,l1=new Set,o1=new Set;Object.freeze(J0);var bh={enqueueSetState:function(e,t,n){e=e._reactInternals;var a=cn(e),l=Va(a);l.payload=t,n!=null&&(Df(n),l.callback=n),t=Ga(e,l,a),t!==null&&(ht(t,e,a),Yi(t,e,a)),Tt(e,a)},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=cn(e),l=Va(a);l.tag=A0,l.payload=t,n!=null&&(Df(n),l.callback=n),t=Ga(e,l,a),t!==null&&(ht(t,e,a),Yi(t,e,a)),Tt(e,a)},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=cn(e),a=Va(n);a.tag=O0,t!=null&&(Df(t),a.callback=t),t=Ga(e,a,n),t!==null&&(ht(t,e,n),Yi(t,e,n)),J!==null&&typeof J.markForceUpdateScheduled=="function"&&J.markForceUpdateScheduled(e,n)}},Sh=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)},ni=null,Th=null,i1=Error("This is not a real error. It's an implementation detail of React's selective hydration feature. If this leaks into userspace, it's a bug in React. Please file an issue."),wt=!1,u1={},r1={},c1={},s1={},ai=!1,f1={},Eh={},Rh={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null},d1=!1,h1=null;h1=new Set;var Ma=!1,pt=!1,Ah=!1,p1=typeof WeakSet=="function"?WeakSet:Set,xt=null,li=null,oi=null,bt=null,en=!1,Vn=null,zu=8192,TR={getCacheForType:function(e){var t=ut(Et),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n},getOwner:function(){return fn}};if(typeof Symbol=="function"&&Symbol.for){var Uu=Symbol.for;Uu("selector.component"),Uu("selector.has_pseudo_class"),Uu("selector.role"),Uu("selector.test_id"),Uu("selector.text")}var ER=[],RR=typeof WeakMap=="function"?WeakMap:Map,dn=0,Qt=2,Gn=4,_a=0,Hu=1,ii=2,Oh=3,Ql=4,zc=6,m1=5,Ge=dn,tt=null,He=null,ke=0,tn=0,ju=1,Zl=2,Nu=3,y1=4,Ch=5,ui=6,ku=7,wh=8,Il=9,Xe=tn,hn=null,ul=!1,ri=!1,xh=!1,ra=0,ft=_a,rl=0,cl=0,Dh=0,pn=0,Jl=0,Lu=null,Zt=null,Uc=!1,Mh=0,g1=300,Hc=1/0,v1=500,Bu=null,sl=null,AR=0,OR=1,CR=2,Kl=0,b1=1,S1=2,T1=3,wR=4,_h=5,Nt=0,fl=null,ci=null,dl=0,zh=0,Uh=null,E1=null,xR=50,Yu=0,Hh=null,jh=!1,jc=!1,DR=50,Pl=0,qu=null,si=!1,Nc=null,R1=!1,A1=new Set,MR={},kc=null,fi=null,Nh=!1,kh=!1,Lc=!1,Lh=!1,Wl=0,Bh={};(function(){for(var e=0;e<th.length;e++){var t=th[e],n=t.toLowerCase();t=t[0].toUpperCase()+t.slice(1),kn(n,"on"+t)}kn(r0,"onAnimationEnd"),kn(c0,"onAnimationIteration"),kn(s0,"onAnimationStart"),kn("dblclick","onDoubleClick"),kn("focusin","onFocus"),kn("focusout","onBlur"),kn(uR,"onTransitionRun"),kn(rR,"onTransitionStart"),kn(cR,"onTransitionCancel"),kn(f0,"onTransitionEnd")})(),V("onMouseEnter",["mouseout","mouseover"]),V("onMouseLeave",["mouseout","mouseover"]),V("onPointerEnter",["pointerout","pointerover"]),V("onPointerLeave",["pointerout","pointerover"]),B("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),B("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),B("onBeforeInput",["compositionend","keypress","textInput","paste"]),B("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),B("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),B("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var $u="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Yh=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat($u)),Bc="_reactListening"+Math.random().toString(36).slice(2),O1=!1,C1=!1,Yc=!1,w1=!1,qc=!1,$c=!1,x1=!1,Vc={},_R=/\r\n?/g,zR=/\u0000|\uFFFD/g,Fl="http://www.w3.org/1999/xlink",qh="http://www.w3.org/XML/1998/namespace",UR="javascript:throw new Error('React form unexpectedly submitted.')",HR="suppressHydrationWarning",Gc="$",Xc="/$",za="$?",Vu="$!",jR=1,NR=2,kR=4,$h="F!",D1="F",M1="complete",LR="style",Ua=0,di=1,Qc=2,Vh=null,Gh=null,_1={dialog:!0,webview:!0},Xh=null,z1=typeof setTimeout=="function"?setTimeout:void 0,BR=typeof clearTimeout=="function"?clearTimeout:void 0,eo=-1,U1=typeof Promise=="function"?Promise:void 0,YR=typeof queueMicrotask=="function"?queueMicrotask:typeof U1<"u"?function(e){return U1.resolve(null).then(e).catch(OT)}:z1,Qh=null,to=0,Gu=1,H1=2,j1=3,_n=4,zn=new Map,N1=new Set,Ha=Ve.d;Ve.d={f:function(){var e=Ha.f(),t=Ao();return e||t},r:function(e){var t=Nn(e);t!==null&&t.tag===5&&t.type==="form"?hy(t):Ha.r(e)},D:function(e){Ha.D(e),nv("dns-prefetch",e,null)},C:function(e,t){Ha.C(e,t),nv("preconnect",e,t)},L:function(e,t,n){Ha.L(e,t,n);var a=hi;if(a&&e&&t){var l='link[rel="preload"][as="'+vn(t)+'"]';t==="image"&&n&&n.imageSrcSet?(l+='[imagesrcset="'+vn(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(l+='[imagesizes="'+vn(n.imageSizes)+'"]')):l+='[href="'+vn(e)+'"]';var o=l;switch(t){case"style":o=xo(e);break;case"script":o=Do(e)}zn.has(o)||(e=_e({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),zn.set(o,e),a.querySelector(l)!==null||t==="style"&&a.querySelector(au(o))||t==="script"&&a.querySelector(lu(o))||(t=a.createElement("link"),zt(t,"link",e),E(t),a.head.appendChild(t)))}},m:function(e,t){Ha.m(e,t);var n=hi;if(n&&e){var a=t&&typeof t.as=="string"?t.as:"script",l='link[rel="modulepreload"][as="'+vn(a)+'"][href="'+vn(e)+'"]',o=l;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=Do(e)}if(!zn.has(o)&&(e=_e({rel:"modulepreload",href:e},t),zn.set(o,e),n.querySelector(l)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(lu(o)))return}a=n.createElement("link"),zt(a,"link",e),E(a),n.head.appendChild(a)}}},X:function(e,t){Ha.X(e,t);var n=hi;if(n&&e){var a=f(n).hoistableScripts,l=Do(e),o=a.get(l);o||(o=n.querySelector(lu(l)),o||(e=_e({src:e,async:!0},t),(t=zn.get(l))&&vd(e,t),o=n.createElement("script"),E(o),zt(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},a.set(l,o))}},S:function(e,t,n){Ha.S(e,t,n);var a=hi;if(a&&e){var l=f(a).hoistableStyles,o=xo(e);t=t||"default";var u=l.get(o);if(!u){var r={loading:to,preload:null};if(u=a.querySelector(au(o)))r.loading=Gu|_n;else{e=_e({rel:"stylesheet",href:e,"data-precedence":t},n),(n=zn.get(o))&&gd(e,n);var d=u=a.createElement("link");E(d),zt(d,"link",e),d._p=new Promise(function(p,O){d.onload=p,d.onerror=O}),d.addEventListener("load",function(){r.loading|=Gu}),d.addEventListener("error",function(){r.loading|=H1}),r.loading|=_n,tc(u,t,a)}u={type:"stylesheet",instance:u,count:1,state:r},l.set(o,u)}}},M:function(e,t){Ha.M(e,t);var n=hi;if(n&&e){var a=f(n).hoistableScripts,l=Do(e),o=a.get(l);o||(o=n.querySelector(lu(l)),o||(e=_e({src:e,async:!0,type:"module"},t),(t=zn.get(l))&&vd(e,t),o=n.createElement("script"),E(o),zt(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},a.set(l,o))}}};var hi=typeof document>"u"?null:document,Zc=null,Xu=null,Zh=null,Ic=null,no=oE,Qu={$$typeof:Fn,Provider:null,Consumer:null,_currentValue:no,_currentValue2:no,_threadCount:0},k1="%c%s%c ",L1="background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px",B1="",Jc=" ",qR=Function.prototype.bind,Y1=!1,q1=null,$1=null,V1=null,G1=null,X1=null,Q1=null,Z1=null,I1=null,J1=null;q1=function(e,t,n,a){t=i(e,t),t!==null&&(n=c(t.memoizedState,n,0,a),t.memoizedState=n,t.baseState=n,e.memoizedProps=_e({},e.memoizedProps),n=Jt(e,2),n!==null&&ht(n,e,2))},$1=function(e,t,n){t=i(e,t),t!==null&&(n=b(t.memoizedState,n,0),t.memoizedState=n,t.baseState=n,e.memoizedProps=_e({},e.memoizedProps),n=Jt(e,2),n!==null&&ht(n,e,2))},V1=function(e,t,n,a){t=i(e,t),t!==null&&(n=s(t.memoizedState,n,a),t.memoizedState=n,t.baseState=n,e.memoizedProps=_e({},e.memoizedProps),n=Jt(e,2),n!==null&&ht(n,e,2))},G1=function(e,t,n){e.pendingProps=c(e.memoizedProps,t,0,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps),t=Jt(e,2),t!==null&&ht(t,e,2)},X1=function(e,t){e.pendingProps=b(e.memoizedProps,t,0),e.alternate&&(e.alternate.pendingProps=e.pendingProps),t=Jt(e,2),t!==null&&ht(t,e,2)},Q1=function(e,t,n){e.pendingProps=s(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps),t=Jt(e,2),t!==null&&ht(t,e,2)},Z1=function(e){var t=Jt(e,2);t!==null&&ht(t,e,2)},I1=function(e){M=e},J1=function(e){T=e};var Kc=!0,Pc=null,Ih=!1,hl=null,pl=null,ml=null,Zu=new Map,Iu=new Map,yl=[],$R="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" "),Wc=null;if(oc.prototype.render=Od.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error("Cannot update an unmounted root.");var n=arguments;typeof n[1]=="function"?console.error("does not support the second callback argument. To execute a side effect after rendering, declare it in a component body with useEffect()."):le(n[1])?console.error("You passed a container to the second argument of root.render(...). You don't need to pass it again since you already passed it to create the root."):typeof n[1]<"u"&&console.error("You passed a second argument to root.render(...) but it only accepts one argument."),n=e;var a=t.current,l=cn(a);Sd(a,l,n,t,null,null)},oc.prototype.unmount=Od.prototype.unmount=function(){var e=arguments;if(typeof e[0]=="function"&&console.error("does not support a callback argument. To execute a side effect after rendering, declare it in a component body with useEffect()."),e=this._internalRoot,e!==null){this._internalRoot=null;var t=e.containerInfo;(Ge&(Qt|Gn))!==dn&&console.error("Attempted to synchronously unmount a root while React was already rendering. React cannot finish unmounting the root until the current render has completed, which may lead to a race condition."),Sd(e.current,2,null,e,null,null),Ao(),t[tl]=null}},oc.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ci();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yl.length&&t!==0&&t<yl[n].priority;n++);yl.splice(n,0,e),n===0&&mv(e)}},function(){var e=Cd.version;if(e!=="19.1.1")throw Error(`Incompatible React versions: The "react" and "react-dom" packages must have the exact same version. Instead got:
  - react:      `+(e+`
  - react-dom:  19.1.1
Learn more: https://react.dev/warnings/version-mismatch`))}(),typeof Map=="function"&&Map.prototype!=null&&typeof Map.prototype.forEach=="function"&&typeof Set=="function"&&Set.prototype!=null&&typeof Set.prototype.clear=="function"&&typeof Set.prototype.forEach=="function"||console.error("React depends on Map and Set built-in types. Make sure that you load a polyfill in older browsers. https://react.dev/link/react-polyfills"),Ve.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error("Unable to find node on an unmounted component."):(e=Object.keys(e).join(","),Error("Argument appears to not be a ReactComponent. Keys: "+e));return e=oe(t),e=e!==null?pe(e):null,e=e===null?null:e.stateNode,e},!function(){var e={bundleType:1,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:x,reconcilerVersion:"19.1.1"};return e.overrideHookState=q1,e.overrideHookStateDeletePath=$1,e.overrideHookStateRenamePath=V1,e.overrideProps=G1,e.overridePropsDeletePath=X1,e.overridePropsRenamePath=Q1,e.scheduleUpdate=Z1,e.setErrorHandler=I1,e.setSuspenseHandler=J1,e.scheduleRefresh=I,e.scheduleRoot=j,e.setRefreshHandler=ne,e.getCurrentFiber=IT,e.getLaneLabelMap=JT,e.injectProfilingHooks=me,se(e)}()&&la&&window.top===window.self&&(-1<navigator.userAgent.indexOf("Chrome")&&navigator.userAgent.indexOf("Edge")===-1||-1<navigator.userAgent.indexOf("Firefox"))){var K1=window.location.protocol;/^(https?|file):$/.test(K1)&&console.info("%cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools"+(K1==="file:"?`
You might need to use a local HTTP server (instead of file://): https://react.dev/link/react-devtools-faq`:""),"font-weight:bold")}Wu.createRoot=function(e,t){if(!le(e))throw Error("Target container is not a DOM element.");vv(e);var n=!1,a="",l=Ry,o=Ay,u=Oy,r=null;return t!=null&&(t.hydrate?console.warn("hydrate through createRoot is deprecated. Use ReactDOMClient.hydrateRoot(container, <App />) instead."):typeof t=="object"&&t!==null&&t.$$typeof===Pa&&console.error(`You passed a JSX element to createRoot. You probably meant to call root.render instead. Example usage:

  let root = createRoot(domContainer);
  root.render(<App />);`),t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(l=t.onUncaughtError),t.onCaughtError!==void 0&&(o=t.onCaughtError),t.onRecoverableError!==void 0&&(u=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(r=t.unstable_transitionCallbacks)),t=cv(e,1,!1,null,null,n,a,l,o,u,r,null),e[tl]=t.current,ud(e),new Od(t)},Wu.hydrateRoot=function(e,t,n){if(!le(e))throw Error("Target container is not a DOM element.");vv(e),t===void 0&&console.error("Must provide initial children as second argument to hydrateRoot. Example usage: hydrateRoot(domContainer, <App />)");var a=!1,l="",o=Ry,u=Ay,r=Oy,d=null,p=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onUncaughtError!==void 0&&(o=n.onUncaughtError),n.onCaughtError!==void 0&&(u=n.onCaughtError),n.onRecoverableError!==void 0&&(r=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(d=n.unstable_transitionCallbacks),n.formState!==void 0&&(p=n.formState)),t=cv(e,1,!0,t,n??null,a,l,o,u,r,d,p),t.context=sv(null),n=t.current,a=cn(n),a=Ri(a),l=Va(a),l.callback=null,Ga(n,l,a),n=a,t.current.lanes=n,Ba(t,n),Pn(t),e[tl]=t.current,ud(e),new oc(t)},Wu.version="19.1.1",typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())}(),Wu}var jb;function kO(){return jb||(jb=1,fp.exports=NO()),fp.exports}var LO=kO();const BO=1e4,YO=1e3,qO=()=>{const{page:i=""}=window.aiNavConfig||{},c=window.rhfEntitlements||{};LO.createRoot(document.getElementById("sd-ai-navigation-container")).render(ve.jsx(_O,{page:i,entitlements:c}))},$O=()=>new Promise(i=>{const c=()=>{document.removeEventListener("DOMContentLoaded",c),i()};document.addEventListener("DOMContentLoaded",c),["interactive","complete"].includes(document.readyState)&&c()}),VO=()=>new Promise((i,c)=>{const s=setInterval(()=>{window.rhfEntitlements&&window.rhfEntitlements.SDAI!==null&&window.rhfEntitlements.SDAI!==void 0&&(clearInterval(s),clearTimeout(b),window.removeEventListener("featureEntitlementsSet",h),i(window.rhfEntitlements))},YO),h=T=>{clearInterval(s),clearTimeout(b),window.rhfEntitlements.SDAI!==null&&window.rhfEntitlements.SDAI!==void 0&&(window.removeEventListener("featureEntitlementsSet",h),i(T.detail))};window.addEventListener("featureEntitlementsSet",h);const b=setTimeout(()=>{clearInterval(s),window.removeEventListener("featureEntitlementsSet",h),c(new Error("Timeout waiting for entitlements"))},BO)}),GO=async()=>{try{await Promise.all([$O(),VO()]),qO()}catch(i){console.log(i)}};GO();
