function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define(["underscore","jquery","cache","js/components/generic_module","js/mixins/dependon","js/components/transition","js/components/transition_catalog","analytics"],function(a,c,e,t,n,r,i,o){var s="Astrophysics Data System",t=t.extend({initialize:function(e){this.router=(e=e||{}).router,this.catalog=new i},activate:function(e){this.setBeeHive(e),this.storage=e.getObject("AppStorage");e=this.getPubSub();e.subscribe(e.NAVIGATE,a.bind(this.navigate,this)),e.subscribe(e.CUSTOM_EVENT,a.bind(this._onCustomEvent,this)),e.subscribe(e.USER_ANNOUNCEMENT,a.bind(this._onUserAnnouncement,this))},_onUserAnnouncement:function(e,t){"user_signed_in"===e&&"string"==typeof t&&(e=t,((t=window.crypto||window.msCrypto)?(e=(new TextEncoder).encode(e),t.subtle.digest("SHA-256",e).then(function(e){return Array.from(new Uint8Array(e)).map(function(e){return e.toString(16).padStart(2,"0")}).join("")})):Promise.reject(new Error("Crypto not available"))).then(function(e){o("send","user_update",{user_id:e})}))},_debouncedAnalyticsCall:a.debounce(function(){o.push.apply(o,arguments)},500),_onCustomEvent:function(e,t){switch(console.log("Custom Event",e,t),e){case"timing:results-loaded":window.getSentry(function(e){var t=e.getActiveSpan().getSpanJSON(),t=(new Date).getTime()-1e3*t.start_timestamp;e.setMeasurement("timing.results.shown",t,"millisecond")});break;case"update-document-title":this._updateDocumentTitle(t);break;case"latest-abstract-data":Array.isArray(t.database)?(o.set("items",void 0),t.database.forEach(function(e){o.push({event:"view_item",items:[{item_id:t.bibcode,item_name:t.title,item_category:e,index:t.resultsIndex,refereed:t.property.includes("REFEREED")}]})})):this._debouncedAnalyticsCall({event:"view_item",items:[{item_id:t.bibcode,item_name:t.title,item_category:"(no collection)",index:t.resultsIndex,refereed:t.property.includes("REFEREED")}]});break;case"search-page-results":this._debouncedAnalyticsCall({event:"view_item_list",item_list_id:"search_results",item_list_name:"Search Results",items:t.docs.map(function(e){return _objectSpread(_objectSpread({item_id:e.identifier,item_name:e.title[0]},e.database.slice(1).reduce(function(e,t,n){return _objectSpread(_objectSpread({},e),{},_defineProperty({},"item_category".concat(n+1),t))},{item_category:e.database[0]})),{},{item_list_id:"search_results",item_list_name:"Search Results",item_variant:"search_result_item",index:e.resultsIndex,refereed:e.property.includes("REFEREED")})})})}},_cleanRoute:function(e){var t=e.match(/[#\/]?([^\/]*)\//);return t&&1<t.length?"/"+t[1]:e},_setPageAndEmitEvent:a.debounce(function(e,t){o.reset(),o("send","virtual_page_view",{page_name:t,clean_route:this._cleanRoute(e)}),getSentry(function(e){e.setTag("page.name",t)})},300),navigate:function(n,r,e){var i=c.Deferred();if(this.router&&this.router instanceof Backbone.Router){var o=this.catalog.get(n);if(o){if(!o.execute)return i.resolve().promise();var t=a.bind(function(){var e,t=!!(o.replace||r&&r.replace);""!==o.route&&!o.route||(e=""===o.route?"/":o.route,this._setPageAndEmitEvent(e,n),this.router.navigate(e,{trigger:!1,replace:t})),c("head").find("meta[data-highwire]").remove(),this._updateDocumentTitle(o.title),i.resolve()},this);try{(s=o.execute.apply(o,arguments))&&a.isFunction(s.then)?s.then(t):t()}catch(e){this.handleTransitionError(o,e,arguments);var s=new Error("Error transitioning to route; going to 404");return i.reject(s).promise()}}else this.handleMissingTransition(arguments),i.reject(new Error("Missing route; going to 404"))}else i.reject(new Error("Navigator must be given 'router' instance"));return i.promise()},_updateDocumentTitle:function(e){var t,n;a.isUndefined(e)||!1===e||(t=this.storage.getDocumentTitle(),n=a.bind(function(e){document.title=""===e?s:e+" - "+s,this.storage.setDocumentTitle(e)},this),e!==t&&n(e))},handleMissingTransition:function(e){console.error("Cannot handle 'navigate' event: "+JSON.stringify(arguments));var t=this.getPubSub();t.publish(t.BIG_FIRE,"navigation-error",arguments),this.catalog.get("404")&&t.publish(t.NAVIGATE,"404")},handleTransitionError:function(e,t,n){console.error("Error while executing transition",e,n),console.error(t.stack);e=this.getPubSub();e.publish(e.CITY_BURNING,"navigation-error",arguments),this.catalog.get("404")&&e.publish(e.NAVIGATE,"404")},set:function(){if(1==arguments.length){if(arguments[1]instanceof r)return this.catalog.add(arguments[1]);throw new Error("You must be kiddin' sir!")}var e;if(2==arguments.length)return e=arguments[0],a.isFunction(arguments[1])?this.catalog.add(new r(e,{execute:arguments[1]})):a.isObject(arguments[1])&&arguments[1].execute?this.catalog.add(new r(e,arguments[1])):void 0;throw new Error("Himmm, I dont know how to create a catalog rule with this input:",arguments)},get:function(e){return this.catalog.get(e)}});return a.extend(t.prototype,n.BeeHive),t});
//# sourceMappingURL=navigator.js.map