define(["underscore","backbone","react","react-dom","react-redux","analytics","js/components/api_query","js/components/api_request","js/components/api_targets","js/widgets/base/base_widget","./redux/configure-store","./redux/modules/api","./redux/modules/ui","./containers/app"],function(s,e,t,r,i,n,o,a,c,d,u,p,b,h){var l=e.View.extend({initialize:function(e){s.assign(this,e)},render:function(){return r.render(t.createElement(i.Provider,{store:this.store},t.createElement(h,null)),this.el),this},destroy:function(){r.unmountComponentAtNode(this.el)}});return d.extend({initialize:function(){this.store=u(this),this.view=new l({store:this.store}),window.__BUMBLEBEE_TESTING_MODE__||(this.processAbstractData=s.debounce(s.bind(this.processAbstractData,this),300))},defaultQueryArguments:{},activate:function(e){var r=this,i=this.store.dispatch,e=(this.setBeeHive(e),this.activateWidget(),this.attachGeneralHandler(this.onApiFeedback),this.getPubSub());e.subscribe(e.CUSTOM_EVENT,function(e,t){var s;"latest-abstract-data"===e&&(e=r.store.getState().api.bibcode,s=t.bibcode||t.identifier,s=Array.isArray(s)?s[0]:s)&&e!==s&&(i(b.reset()),i(p.setBibcode(s)),r.processAbstractData(t))}),e.subscribe(e.DELIVERING_RESPONSE,function(e){e&&s.isFunction(e.toJSON)?i(p.processResponse(e.toJSON())):i(b.setError("did not receive response from server"))})},processAbstractData:function(e){var t=this.store.dispatch;e&&e.property&&e.property.includes("ASSOCIATED")?this.dispatchRequest(new o):t(b.setError("no associated property"))},composeRequest:function(){var e=this.store.getState().api.bibcode;return new a({target:"".concat(c.RESOLVER,"/").concat(e,"/associated"),query:new o})},emitAnalytics:function(e){n("send","event","interaction","associated-link-followed",{target:"associated",url:e.rawUrl})},onApiFeedback:function(e){var t=this.store.dispatch;s.isPlainObject(e.error)&&(t(b.setError(e.error)),t(p.fallbackOnError()))}})});
//# sourceMappingURL=widget.jsx.js.map