// For license information, see `https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RCdb7b83b6d08a410e9ccdc9fc1f6d1cbe-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RCdb7b83b6d08a410e9ccdc9fc1f6d1cbe-source.min.js', "var brand=adobeDataLayer.getState(\"page\").brand,relatedTabLoadObj={xdm:{eventType:\"related content -  tab loaded\",identityMap:_satellite.getVar(\"Web SDK | IdentityMap\"),web:{webPageDetails:{server:window.document.domain},webInteraction:{linkClicks:{value:1},name:brand+\":related content -  tab loaded\",URL:this.href,type:\"other\"}},_experience:{analytics:{event101to200:{event122:{value:1}},customDimensions:{eVars:{eVar1:_satellite.getVar(\"Analytics Variable|Page|Page_Url\"),eVar21:_satellite.getVar(\"Computed Page Name\"),eVar155:_satellite.getVar(\"Analytics Variable|Page|Query_Param\")},props:{prop20:_satellite.getVar(\"Analytics Variable|Page|Query_Param\"),prop29:_satellite.getVar(\"Analytics Variable|Page|Hash_Url\")}}}}}};alloy(\"sendEvent\",relatedTabLoadObj);");