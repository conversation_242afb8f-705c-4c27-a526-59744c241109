define(["jquery","underscore","js/components/api_query","js/components/api_request","js/components/api_response","js/mixins/dependon","js/components/generic_module","sprintf","js/components/api_targets"],function(f,p,s,i,e,t,n,o,r){n=n.extend({activate:function(e,t){this.setApp(t),this.setBeeHive(e)},getFirstDoc:function(e,t){var e={query:{q:e,fl:"title,abstract,bibcode,id,author"},target:r.SEARCH},n=(t&&p.extend(e,t),f.Deferred());return this.apiRequest(e).done(function(e){n.resolve(e.response.docs[0])}),n.promise()},apiRequest:function(e,t){var n=this.getApp().getService("Api");if(!(e=e||{}).query)throw Error('You must pass in "query"');e.query instanceof s||(e.query=new s(e.query));e=new i({target:e.target||r.SEARCH,query:e.query});return n.request(e,t)},ajax:function(e){var t=this.getApp().getService("Api");return e.headers=e.header||{},e.headers.Authorization=t.access_token,f.ajax(e)},jsonp:function(e,s){return e.dataType="jsonp",e.beforeSend=function(e,t){var n=t.url.split("&");p.each(n,function(e){e.startsWith("callback=")&&(t.url+="&"+(s||"json.wrf")+"="+e.replace("callback=",""))})},this.ajax(e)},printComparison:function(e){var t,n=[],s="%40s %40s %40s %10s";for(t in n.push(o.sprintf(s,"field",e.first,e.second,"diff")),n.push("------------------------------------------------------------------------------------------------------------------------------------"),e.fields){var i=e.fields[t];n.push(o.sprintf(s,t,i.first,i.second,i.diff))}return console.log(n.join("\n")),n},compareTwoSearchInstances:function(d,a,e,t){var n=this,l=f.Deferred();return e&&t?(e=this.countDocsInFields(d,e),t=this.countDocsInFields(a,t),f.when(e,t).done(function(e,t){var n=p.keys(e.fields),s=p.keys(t.fields),i=p.unique(n.concat(s)).sort(),o={};for(r in i){var r=i[r],u=e.fields[r],c=t.fields[r];o[r]={first:u?u.numFound:"--",second:c?c.numFound:"--",diff:u&&c?c.numFound-u.numFound:"--"}}l.resolve({first:d,second:a,fields:o})})):(e=this.getListOfFields(d),t=this.getListOfFields(a),f.when(e,t).done(function(e,t){n.compareTwoSearchInstances(d,a,e,t).done(function(e){l.resolve(e)})})),l},getListOfFields:function(e){var s=f.Deferred();return this.jsonp({url:e+"/admin/luke?numTerms=0&wt=json&indent=true",timeout:6e4}).done(function(e){var t,n=[];for(t in e.fields)t.startsWith("_")||n.push(t);s.resolve(n)}),s},countDocsInFields:function(i,e){var t,o=f.Deferred(),n=[],s={},r={fields:{}};for(t in e){var u=e[t],c=(f.Deferred(),"{!lucene}"+u+":*"),c=(console.log("Getting num docs for: "+c),this.jsonp({url:i+"/query?q="+c+"&fl=id&wt=json&indent=true",context:{field:u,finalResult:r,cycleR:s},timeout:3e5}).done(function(e){this.finalResult.fields[this.field]={numFound:e.response.numFound};var t,n=0,s=[];for(t in this.cycleR)"pending"==this.cycleR[t].state()&&(n+=1,s.push(t));console.log(i+" Got response for: "+this.field+" numFound: "+e.response.numFound+" requests to go: "+n+" "+(s.length<5?s.join(", "):"")),0==n&&o.resolve(this.finalResult)}));s[u]=c,n.push(c)}return o},request:function(e){return f.ajax(e)},testOrcidLogin:function(){var e=this.getApp().getService("OrcidApi");e.signOut(),window.location=e.config.loginUrl},testOrcidSendingData:function(){var t=this.getApp();this.getFirstDoc("bibcode:1978yCat.1072....0C").done(function(e){t.getService("OrcidApi").addWorks([e]).done(function(e){console.log("result:",e,"expected: {}")})})},printPubSubSubscribers:function(e){var s=this.getApp().getService("PubSub"),t=this.getApp(),i={},o=(t.triggerMethodOnAll(function(e,t){var n;this===s?i[s.pubSubKey.getId()]=e:this.hasPubSub&&this.hasPubSub()?(n=this.getPubSub()).getCurrentPubSubKey&&(i[n.getCurrentPubSubKey().getId()]&&console.warn("Redefining key that already exists: "+n.getCurrentPubSubKey().getId()+" "+i[n.getCurrentPubSubKey().getId()]),i[n.getCurrentPubSubKey().getId()]=e):console.warn("Instance without active PubSub: "+e,this)}),e&&(console.log("Printing list of all issued keys to PubSub"),p.each(p.keys(s._issuedKeys),function(e){var t;(t=s._issuedKeys[e].getId&&i[s._issuedKeys[e].getId()]?i[s._issuedKeys[e].getId()]:t)?console.log(e+"(parent:"+t+") -> "+i[e]):console.log(e+" -> "+i[e])})),{});console.log("Printing active subscribers to PubSub"),p.each(s._events,function(e,n,t){console.log(n+" ("+e.length+")"),p.each(e,function(e){var t=e.ctx.getId?i[e.ctx.getId()]:"unknown object";console.log("\t"+t),o[t]=o[t]?o[t]+1:1,null==t&&(o["undefined keys"]||(o["undefined keys"]=[]),o["undefined keys"].push(n+(e.ctx.getId?e.ctx.getId():"X")))})}),console.log(JSON.stringify(o,null," "))}});return p.extend(n.prototype,t.App,t.BeeHive),n});
//# sourceMappingURL=diagnostics.js.map