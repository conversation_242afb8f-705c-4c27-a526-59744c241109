
// Copyright 2012 Google Inc. All rights reserved.
 
 (function(w,g){w[g]=w[g]||{};
 w[g].e=function(s){return eval(s);};})(window,'google_tag_manager');
 
(function(){

var data = {
"resource": {
  "version":"21",
  
  "macros":[{"function":"__e"},{"function":"__jsm","vtp_javascript":["template","(function(){if(bbb){var a=bbb.getService(\"Api\");if(a\u0026\u0026typeof a.access_token===\"string\"\u0026\u0026a.access_token.length\u003E0)try{return a.access_token.split(\" \")[1]}catch(b){}}})();"]},{"function":"__v","vtp_name":"gtm.scrollThreshold","vtp_dataLayerVersion":1},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value1"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.timing_value"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.timing_label"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value1.page_name"},{"function":"__jsm","convert_undefined_to":"{}","vtp_javascript":["template","(function(){if(bbb){var a=bbb.getObject(\"AppStorage\");if(a\u0026\u0026a.getCurrentQuery\u0026\u0026(a=a.getCurrentQuery())\u0026\u0026a.toJSON)return a=a.toJSON()}return{}})();"]},{"function":"__cvt_12596837_81","vtp_path":"sort[0]","vtp_obj":["macro",7],"vtp_stringify":false},{"function":"__cvt_12596837_81","vtp_path":"fq[0]","vtp_obj":["macro",7],"vtp_stringify":true},{"function":"__cvt_12596837_81","vtp_path":"q[0]","vtp_obj":["macro",7],"vtp_stringify":false},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value1.clean_route"},{"function":"__jsm","convert_undefined_to":"0","vtp_javascript":["template","(function(){if(window.performance\u0026\u0026window.performance.getEntriesByType){var a=window.performance.getEntriesByType(\"navigation\");if(0\u003Ca.length)return a=a[0],a=a.loadEventEnd-a.startTime,Math.round(100*(a+Number.EPSILON))\/100}})();"]},{"function":"__c","vtp_value":"G-X07MS2584V"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value1.target"},{"function":"__v","convert_case_to":1,"vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value1"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value1"},{"function":"__v","convert_undefined_to":"{}","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value1"},{"function":"__cvt_12596837_81","vtp_path":"name","vtp_obj":["macro",17],"vtp_stringify":false},{"function":"__cvt_12596837_81","vtp_path":"logic","vtp_obj":["macro",17],"vtp_stringify":false},{"function":"__cvt_12596837_81","vtp_path":"link_followed","vtp_obj":["macro",17],"vtp_stringify":false},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"webVitalsMeasurement.value"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"webVitalsMeasurement.valueRounded"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"webVitalsMeasurement.id"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"webVitalsMeasurement.name"},{"function":"__jsm","vtp_javascript":["template","(function(){if(!(Backbone\u0026\u0026bbb\u0026\u0026bbb.router\u0026\u0026bbb.router))return{};try{var a=Backbone.history.getFragment(),f=bbb.router.routes,d;if(d=Object.keys(f).find(function(b){c=b instanceof RegExp?b:bbb.router._routeToRegExp(b);return c.test(a)})){var e=bbb.router._extractParameters(c,a);var c=d}return{route:c,fragment:a,params:e,cleanRoute:\"\/\"+a.split(\"\/\"+e[0]).join(\"\")}}catch(b){return{}}})();"]},{"function":"__cvt_12596837_81","vtp_path":"cleanRoute","vtp_obj":["macro",25],"vtp_stringify":false},{"function":"__u","vtp_component":"PATH","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__cvt_12596837_81","vtp_path":"timingVar","vtp_obj":["macro",17],"vtp_stringify":false},{"function":"__cvt_12596837_81","vtp_path":"timingValue","vtp_obj":["macro",17],"vtp_stringify":false},{"function":"__cvt_12596837_81","vtp_path":"search_result_meta_state","vtp_obj":["macro",17],"vtp_stringify":false},{"function":"__cvt_12596837_81","vtp_path":"search_result_meta_name","vtp_obj":["macro",17],"vtp_stringify":false},{"function":"__v","vtp_name":"gtm.element","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.triggers","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":""},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"gtm.element.dataset.ftl"},{"function":"__jsm","vtp_javascript":["template","(function(){var b=\/\\s\\(\\d*\\)$\/;return function(a){try{return a.replace(b,\"\")}catch(c){return a}}})();"]},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"gtm.element.dataset.quicklink"},{"function":"__jsm","vtp_javascript":["template","(function(){return ",["escape",["macro",35],8,16],"(",["escape",["macro",36],8,16],")})();"]},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"gtm.element.dataset.datalink"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"item_list_id"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"item_list_name"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"items"},{"function":"__jsm","vtp_javascript":["template","(function(){return bbb.getObject(\"AppStorage\").get(\"currentQuery\").toJSON()})();"]},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.search_param_q"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.search_param_sort"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.search_applied_facets"},{"function":"__jsm","vtp_javascript":["template","(function(){var a=",["escape",["macro",27],8,16],".split(\"\/\");if(0\u003Ca.length\u0026\u0026\"abs\"===a[1]\u0026\u0026\"string\"===typeof a[2]\u0026\u002619===a[2].length)return a=decodeURIComponent(a[2]),a.slice(0,4)})();"]},{"function":"__jsm","vtp_javascript":["template","(function(){var a=",["escape",["macro",27],8,16],".split(\"\/\");if(0\u003Ca.length\u0026\u0026\"abs\"===a[1]\u0026\u0026\"string\"===typeof a[2]\u0026\u002619===a[2].length)return a=decodeURIComponent(a[2]),a.slice(4,9)})();"]},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.content_id"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.details_publication"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":"false","vtp_name":"eventModel.details_refereed"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"page_name"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"page_route"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"focused_identifier"},{"function":"__jsm","vtp_javascript":["template","(function(){return location.pathname.split(\"\/\")[3]})();"]},{"function":"__v","convert_undefined_to":"{}","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value2"},{"function":"__jsm","vtp_javascript":["template","(function(){return JSON.parse(",["escape",["macro",17],8,16],")})();"]},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value1"},{"function":"__jsm","vtp_javascript":["template","(function(){var a=document.head.querySelector('[name\\x3d\"citation_publication_date\"][content]').content;if(a)return a.slice(0,4)})();"]},{"function":"__jsm","convert_undefined_to":"false","vtp_javascript":["template","(function(){try{return bbb\u0026\u0026bbb.hasObject(\"User\")?bbb.getObject(\"User\").isLoggedIn():!1}catch(a){return!1}})();"]},{"function":"__jsm","vtp_javascript":["template","(function(){if(crypto\u0026\u0026TextEncoder\u0026\u0026Uint8Array){var e=function(c){return new Promise(function(d,g){return(new Promise(function(a,b){b=(new TextEncoder).encode(c);a(crypto.subtle.digest(\"SHA-256\",b))})).then(function(a){a=Array.from(new Uint8Array(a));a=a.map(function(b){return b.toString(16).padStart(2,\"0\")}).join(\"\");d(a)})})};try{if(bbb\u0026\u0026bbb.hasObject(\"User\")){var f=bbb.getObject(\"User\").getUserName();return e(f)}}catch(c){}}})();"]},{"function":"__cvt_12596837_81","vtp_path":"user_id","vtp_obj":["macro",17],"vtp_stringify":false},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"items"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"value1.clean_route"},{"function":"__u","vtp_component":"URL","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","vtp_component":"HOST","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__f","vtp_component":"URL"},{"function":"__e"},{"function":"__v","vtp_name":"gtm.elementUrl","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.visibleRatio","vtp_dataLayerVersion":1}],
  "tags":[{"function":"__googtag","metadata":["map"],"once_per_event":true,"vtp_tagId":"G-X07MS2584V","vtp_eventSettingsTable":["list",["map","parameter","user_id","parameterValue",["macro",1]]],"tag_id":8},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","percent_scrolled","parameterValue",["macro",2]]],"vtp_eventName":"scroll","vtp_measurementIdOverride":"G-X07MS2584V","vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":9},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","ui_theme","parameterValue",["macro",3]]],"vtp_enhancedUserId":false,"vtp_eventName":"change_theme","vtp_measurementIdOverride":"G-X07MS2584V","vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":10},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","value","parameterValue",["macro",4]],["map","parameter","name","parameterValue",["macro",5]]],"vtp_eventName":"timing_complete","vtp_measurementIdOverride":"G-X07MS2584V","vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":15},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","search_param_sort","parameterValue",["macro",8]],["map","parameter","search_applied_facets","parameterValue",["macro",9]],["map","parameter","search_term","parameterValue",["macro",10]]],"vtp_enhancedUserId":false,"vtp_eventName":"view_search_results","vtp_measurementIdOverride":"G-X07MS2584V","vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":21},{"function":"__paused","vtp_originalTagType":"gaawe","tag_id":29},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","loading_time_ms","parameterValue",["macro",12]]],"vtp_eventName":"page_load_time","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":44},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","abstract_navigation_target","parameterValue",["macro",14]]],"vtp_eventName":"abstract_page_navigation","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":51},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"search_example_used","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":53},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","value","parameterValue",["macro",15]]],"vtp_eventName":"search_graph_tab_changed","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":56},{"function":"__paused","vtp_originalTagType":"gaawe","tag_id":58},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","sort_value","parameterValue",["macro",16]]],"vtp_eventName":"search_sort_applied","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":61},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","search_facet_applied_field","parameterValue",["macro",18]],["map","parameter","search_facet_applied_logic","parameterValue",["macro",19]]],"vtp_eventName":"search_facet_applied","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":63},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","full_text_link_value","parameterValue",["macro",20]]],"vtp_eventName":"full_text_link_followed","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":69},{"function":"__cvt_12596837_70","metadata":["map"],"once_per_load":true,"vtp_allMetrics":true,"vtp_namespace":false,"tag_id":71},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","web_vitals_value","parameterValue",["macro",21]],["map","parameter","web_vitals_value_rounded","parameterValue",["macro",22]],["map","parameter","web_vitals_id","parameterValue",["macro",23]]],"vtp_eventName":["macro",24],"vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":76},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","search_term","parameterValue",["macro",17]]],"vtp_eventName":"search","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":79},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","cleaned_route_value","parameterValue",["macro",26]]],"vtp_eventName":"clean_page_route","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":87},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","inserted_quick_field","parameterValue",["macro",17]]],"vtp_eventName":"quick_field_inserted","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":96},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"login","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":98},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","second_order_operator","parameterValue",["macro",17]]],"vtp_eventName":"second_order_operator_used","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":101},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","timing_var","parameterValue",["macro",28]],["map","parameter","timing_value_ms","parameterValue",["macro",29]]],"vtp_eventName":"timing","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":107},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","search_result_meta_toggled_value","parameterValue",["macro",30]]],"vtp_eventName":["template","search_result_meta_toggled_",["macro",31]],"vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":110},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","full_text_link_value","parameterValue",["macro",34]]],"vtp_eventName":"quick_full_text_link_followed","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":114},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","quick_link_value","parameterValue",["macro",37]]],"vtp_eventName":"quick_link_followed","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":115},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","data_product_link_value","parameterValue",["macro",20]]],"vtp_eventName":"data_product_link_followed","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":118},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","data_product_link_value","parameterValue",["macro",38]]],"vtp_eventName":"data_product_quick_link_followed","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":122},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","item_list_id","parameterValue",["macro",39]],["map","parameter","item_list_name","parameterValue",["macro",40]],["map","parameter","items","parameterValue",["macro",41]]],"vtp_eventName":"view_item_list","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":129},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","items","parameterValue",["macro",41]]],"vtp_eventName":"view_item","vtp_measurementIdOverride":["macro",13],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":133},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_waitForTagsTimeout":"2000","vtp_uniqueTriggerId":"12596837_113","tag_id":134},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_waitForTagsTimeout":"2000","vtp_uniqueTriggerId":"12596837_121","tag_id":135},{"function":"__cl","tag_id":136}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"user_update"},{"function":"_eq","arg0":["macro",0],"arg1":"uitheme_appSetting"},{"function":"_eq","arg0":["macro",0],"arg1":"timing_complete"},{"function":"_eq","arg0":["macro",6],"arg1":"search-page"},{"function":"_eq","arg0":["macro",0],"arg1":"virtual_page_view"},{"function":"_sw","arg0":["macro",11],"arg1":"\/abs"},{"function":"_gt","arg0":["macro",12],"arg1":"0"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.load"},{"function":"_eq","arg0":["macro",14],"arg1":"searchwidget"},{"function":"_eq","arg0":["macro",14],"arg1":"userpreferences"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_toc-link-followed"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_suggestion-used"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_graph-tab-active"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_abstracts-toggled-on"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_sort-applied"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_facet-applied"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_full-text-link-followed"},{"function":"_eq","arg0":["macro",0],"arg1":"coreWebVitals"},{"function":"_re","arg0":["macro",0],"arg1":"interaction_search-bar-(fielded|unfielded)-query-submitted"},{"function":"_eq","arg0":["macro",0],"arg1":"page_view"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_field-insert-dropdown-selected"},{"function":"_eq","arg0":["macro",27],"arg1":"\/user\/account\/login"},{"function":"_eq","arg0":["macro",0],"arg1":"user_login"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_second-order-operation"},{"function":"_eq","arg0":["macro",0],"arg1":"timing"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_search_result_meta_toggled"},{"function":"_css","arg0":["macro",32],"arg1":"[data-ftl]"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.linkClick"},{"function":"_re","arg0":["macro",33],"arg1":"(^$|((^|,)12596837_113($|,)))"},{"function":"_css","arg0":["macro",32],"arg1":"[data-quicklink]"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.click"},{"function":"_eq","arg0":["macro",0],"arg1":"interaction_data-link-followed"},{"function":"_css","arg0":["macro",32],"arg1":"[data-datalink]"},{"function":"_re","arg0":["macro",33],"arg1":"(^$|((^|,)12596837_121($|,)))"},{"function":"_eq","arg0":["macro",0],"arg1":"view_item_list"},{"function":"_ew","arg0":["macro",27],"arg1":"\/abstract"},{"function":"_eq","arg0":["macro",0],"arg1":"view_item"}],
  "rules":[[["if",0],["add",0,1,14,29,30,31]],[["if",1],["add",0]],[["if",2],["add",2]],[["if",3],["add",3]],[["if",4,5],["add",4]],[["if",5,6],["add",5]],[["if",7,8],["add",6]],[["if",11],["unless",9,10],["add",7]],[["if",12],["add",8]],[["if",13],["add",9]],[["if",14],["add",10]],[["if",15],["add",11]],[["if",16],["add",12]],[["if",17],["add",13]],[["if",18],["add",15]],[["if",19],["add",16]],[["if",20],["add",17]],[["if",21],["add",18]],[["if",22,23],["add",19]],[["if",24],["add",20]],[["if",25],["add",21]],[["if",26],["add",22]],[["if",27,28,29],["add",23]],[["if",30,31],["add",24]],[["if",32],["add",25]],[["if",28,33,34],["add",26]],[["if",35],["add",27]],[["if",36,37],["add",28]]]
},
"runtime":[ [50,"__cvt_12596837_70",[46,"a"],[52,"b",["require","copyFromWindow"]],[52,"c",["require","createQueue"]],[52,"d",["require","injectScript"]],[52,"e",["require","logToConsole"]],[52,"f",["require","Math"]],[52,"g",["c","dataLayer"]],[52,"h",[51,"",[7,"m"],["e",[15,"m"]],[2,[15,"a"],"gtmOnFailure",[7]]]],[52,"i",[51,"",[7,"m"],[22,[28,[17,[15,"m"],"attribution"]],[46,[36,[44]]]],[38,[17,[15,"m"],"name"],[46,"CLS","INP","LCP"],[46,[5,[46,[36,[17,[17,[15,"m"],"attribution"],"largestShiftTarget"]]]],[5,[46,[36,[17,[17,[15,"m"],"attribution"],"interactionTarget"]]]],[5,[46,[36,[17,[17,[15,"m"],"attribution"],"element"]]]]]]]],[52,"j",[51,"",[7,"m"],[52,"n",[8,"event","coreWebVitals","webVitalsMeasurement",[8]]],[52,"o",[8,"name",[17,[15,"m"],"name"],"id",[17,[15,"m"],"id"],"value",[17,[15,"m"],"value"],"delta",[17,[15,"m"],"delta"],"valueRounded",[2,[15,"f"],"round",[7,[39,[20,[17,[15,"m"],"name"],"CLS"],[26,[17,[15,"m"],"value"],1000],[17,[15,"m"],"value"]]]],"deltaRounded",[2,[15,"f"],"round",[7,[39,[20,[17,[15,"m"],"name"],"CLS"],[26,[17,[15,"m"],"delta"],1000],[17,[15,"m"],"delta"]]]],"rating",[17,[15,"m"],"rating"],"navigationType",[17,[15,"m"],"navigationType"]]],[22,[17,[15,"a"],"attribution"],[46,[53,[52,"p",["i",[15,"m"]]],[22,[21,[40,[15,"p"]],"undefined"],[46,[43,[15,"o"],"debugTarget",[15,"p"]]]],[22,[17,[17,[15,"m"],"attribution"],"loadState"],[46,[43,[15,"o"],"loadState",[17,[17,[15,"m"],"attribution"],"loadState"]]]]]]],[22,[17,[15,"a"],"namespace"],[46,[43,[17,[15,"n"],"webVitalsMeasurement"],[17,[15,"m"],"name"],[15,"o"]]],[46,[43,[15,"n"],"webVitalsMeasurement",[15,"o"]]]],["g",[15,"n"]]]],[52,"k",[51,"",[7],[52,"m",["b","webVitals"]],[22,[28,[15,"m"]],[46,[36,["h","[GTM / Core Web Vitals]: window.webVitals failed to load."]]]],[2,[15,"m"],"onINP",[7,[15,"j"]]],[2,[15,"m"],"onFID",[7,[15,"j"]]],[2,[15,"m"],"onCLS",[7,[15,"j"]]],[2,[15,"m"],"onLCP",[7,[15,"j"]]],[22,[17,[15,"a"],"allMetrics"],[46,[2,[15,"m"],"onFCP",[7,[15,"j"]]],[2,[15,"m"],"onTTFB",[7,[15,"j"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]],[52,"l",[39,[17,[15,"a"],"attribution"],".attribution.iife.js",".iife.js"]],["d",[0,"https://unpkg.com/web-vitals/dist/web-vitals",[15,"l"]],[15,"k"],[17,[15,"a"],"gtmOnFailure"],"web-vitals"]]
 ,[50,"__cvt_12596837_81",[46,"a"],[41,"c","d","e"],[52,"b",["require","JSON"]],[3,"c",[30,[17,[15,"a"],"path"],""]],[3,"d",[30,[17,[15,"a"],"obj"],[8]]],[22,[20,[40,[15,"d"]],"string"],[46,[3,"d",[2,[15,"b"],"parse",[7,[15,"d"]]]]]],[3,"e",[2,[2,[15,"c"],"split",[7,"."]],"reduce",[7,[51,"",[7,"f","g"],[41,"h","i","j","k"],[3,"h",[2,[15,"g"],"indexOf",[7,"["]]],[3,"i",[2,[15,"g"],"indexOf",[7,"]"]]],[22,[1,[19,[15,"h"],0],[18,[15,"i"],[15,"h"]]],[46,[3,"j",[15,"g"]],[3,"g",[2,[15,"j"],"substring",[7,0,[15,"h"]]]],[3,"k",[2,[15,"j"],"substring",[7,[0,[15,"h"],1],[15,"i"]]]],[22,[29,[15,"g"],""],[46,[36,[1,[15,"f"],[16,[16,[15,"f"],[15,"g"]],[15,"k"]]]]],[46,[36,[1,[15,"f"],[16,[15,"f"],[15,"k"]]]]]]],[46,[36,[1,[15,"f"],[16,[15,"f"],[15,"g"]]]]]]],[15,"d"]]]],[22,[1,[20,[40,[15,"e"]],"object"],[17,[15,"a"],"stringify"]],[46,[3,"e",[2,[15,"b"],"stringify",[7,[15,"e"]]]]]],[36,[15,"e"]]]
 ,[50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__cl",[46,"a"],[52,"b",["require","internal.enableAutoEventOnClick"]],["b"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__f",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","getReferrerUrl"]],[52,"d",["require","makeString"]],[52,"e",["require","parseUrl"]],[52,"f",[15,"__module_legacyUrls"]],[52,"g",[30,["b","gtm.referrer",1],["c"]]],[22,[28,[15,"g"]],[46,[36,["d",[15,"g"]]]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"f"],"B",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"C",[7,[15,"g"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"f"],"D",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"E",[7,[15,"g"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[22,[17,[15,"a"],"queryKey"],[46,[53,[36,[2,[15,"f"],"H",[7,[15,"g"],[17,[15,"a"],"queryKey"]]]]]]],[52,"h",["e",[15,"g"]]],[36,[2,[17,[15,"h"],"search"],"replace",[7,"?",""]]]]],[5,[46,[36,[2,[15,"f"],"G",[7,[15,"g"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"f"],"A",[7,["d",[15,"g"]]]]]]]]]]
 ,[50,"__googtag",[46,"a"],[50,"m",[46,"v","w"],[66,"x",[2,[15,"b"],"keys",[7,[15,"w"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],[15,"x"]]]]]]],[50,"n",[46],[36,[7,[17,[15,"f"],"HR"],[17,[15,"f"],"II"]]]],[50,"o",[46,"v"],[52,"w",["n"]],[65,"x",[15,"w"],[46,[53,[52,"y",[16,[15,"v"],[15,"x"]]],[22,[15,"y"],[46,[36,[15,"y"]]]]]]],[36,[44]]],[52,"b",["require","Object"]],[52,"c",["require","createArgumentsQueue"]],[52,"d",[15,"__module_gtag"]],[52,"e",["require","internal.gtagConfig"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g",["require","getType"]],[52,"h",["require","internal.loadGoogleTag"]],[52,"i",["require","logToConsole"]],[52,"j",["require","makeNumber"]],[52,"k",["require","makeString"]],[52,"l",["require","makeTableMap"]],[52,"p",[30,[17,[15,"a"],"tagId"],""]],[22,[30,[21,["g",[15,"p"]],"string"],[24,[2,[15,"p"],"indexOf",[7,"-"]],0]],[46,[53,["i",[0,"Invalid Measurement ID for the GA4 Configuration tag: ",[15,"p"]]],[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[52,"q",[30,[17,[15,"a"],"configSettingsVariable"],[8]]],[52,"r",[30,["l",[30,[17,[15,"a"],"configSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["m",[15,"q"],[15,"r"]],[52,"s",[30,[17,[15,"a"],"eventSettingsVariable"],[8]]],[52,"t",[30,["l",[30,[17,[15,"a"],"eventSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["m",[15,"s"],[15,"t"]],[52,"u",[15,"q"]],["m",[15,"u"],[15,"s"]],[22,[30,[2,[15,"u"],"hasOwnProperty",[7,[17,[15,"f"],"JE"]]],[17,[15,"a"],"userProperties"]],[46,[53,[52,"v",[30,[16,[15,"u"],[17,[15,"f"],"JE"]],[8]]],["m",[15,"v"],[30,["l",[30,[17,[15,"a"],"userProperties"],[7]],"name","value"],[8]]],[43,[15,"u"],[17,[15,"f"],"JE"],[15,"v"]]]]],[2,[15,"d"],"E",[7,[15,"u"],[17,[15,"d"],"B"],[51,"",[7,"v"],[36,[39,[20,"false",[2,["k",[15,"v"]],"toLowerCase",[7]]],false,[28,[28,[15,"v"]]]]]]]],[2,[15,"d"],"E",[7,[15,"u"],[17,[15,"d"],"D"],[51,"",[7,"v"],[36,["j",[15,"v"]]]]]],["h",[15,"p"],[8,"firstPartyUrl",["o",[15,"u"]]]],["e",[15,"p"],[15,"u"],[8,"noTargetGroup",true]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__jsm",[46,"a"],[52,"b",["require","internal.executeJavascriptString"]],[22,[20,[17,[15,"a"],"javascript"],[44]],[46,[36]]],[36,["b",[17,[15,"a"],"javascript"]]]]
 ,[50,"__lcl",[46,"a"],[52,"b",["require","makeInteger"]],[52,"c",["require","makeString"]],[52,"d",["require","internal.enableAutoEventOnLinkClick"]],[52,"e",[15,"__module_featureFlags"]],[52,"f",["require","queryPermission"]],[52,"g",["require","internal.isFeatureEnabled"]],[52,"h",["require","internal.isOgt"]],[52,"i",[8]],[22,[17,[15,"a"],"waitForTags"],[46,[53,[43,[15,"i"],"waitForTags",true],[43,[15,"i"],"waitForTagsTimeout",["b",[17,[15,"a"],"waitForTagsTimeout"]]]]]],[22,[17,[15,"a"],"checkValidation"],[46,[53,[43,[15,"i"],"checkValidation",true]]]],[52,"j",[30,[17,[15,"a"],"uniqueTriggerId"],"0"]],[22,[1,[1,["g",[17,[15,"e"],"DU"]],["h"]],[28,["f","detect_link_click_events",[15,"i"]]]],[46,[53,[43,[15,"i"],"waitForTags",false]]]],["d",[15,"i"],[15,"j"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__paused",[46,"a"],[2,[15,"a"],"gtmOnFailure",[7]]]
 ,[50,"__u",[46,"a"],[50,"k",[46,"l","m"],[52,"n",[17,[15,"m"],"multiQueryKeys"]],[52,"o",[30,[17,[15,"m"],"queryKey"],""]],[52,"p",[17,[15,"m"],"ignoreEmptyQueryParam"]],[22,[20,[15,"o"],""],[46,[53,[52,"r",[2,[17,["i",[15,"l"]],"search"],"replace",[7,"?",""]]],[36,[39,[1,[28,[15,"r"]],[15,"p"]],[44],[15,"r"]]]]]],[41,"q"],[22,[15,"n"],[46,[53,[22,[20,["e",[15,"o"]],"array"],[46,[53,[3,"q",[15,"o"]]]],[46,[53,[52,"r",["c","\\s+","g"]],[3,"q",[2,[2,["f",[15,"o"]],"replace",[7,[15,"r"],""]],"split",[7,","]]]]]]]],[46,[53,[3,"q",[7,["f",[15,"o"]]]]]]],[65,"r",[15,"q"],[46,[53,[52,"s",[2,[15,"h"],"H",[7,[15,"l"],[15,"r"]]]],[22,[29,[15,"s"],[44]],[46,[53,[22,[1,[15,"p"],[20,[15,"s"],""]],[46,[53,[6]]]],[36,[15,"s"]]]]]]]],[36,[44]]],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getUrl"]],[52,"e",["require","getType"]],[52,"f",["require","makeString"]],[52,"g",["require","parseUrl"]],[52,"h",[15,"__module_legacyUrls"]],[52,"i",["require","internal.legacyParseUrl"]],[41,"j"],[22,[17,[15,"a"],"customUrlSource"],[46,[53,[3,"j",[17,[15,"a"],"customUrlSource"]]]],[46,[53,[3,"j",["b","gtm.url",1]]]]],[3,"j",[30,[15,"j"],["d"]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"h"],"B",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"C",[7,[15,"j"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"h"],"D",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"E",[7,[15,"j"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"h"],"F",[7,[15,"j"]]]]]],[5,[46,[36,["k",[15,"j"],[15,"a"]]]]],[5,[46,[36,[2,[15,"h"],"G",[7,[15,"j"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"h"],"A",[7,["f",[15,"j"]]]]]]]]]]
 ,[50,"__v",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getType"]],[52,"e",[17,[15,"a"],"name"]],[22,[30,[28,[15,"e"]],[21,["d",[15,"e"]],"string"]],[46,[36,false]]],[52,"f",[2,[15,"e"],"replace",[7,["c","\\\\.","g"],"."]]],[52,"g",["b",[15,"f"],[30,[17,[15,"a"],"dataLayerVersion"],1]]],[36,[39,[21,[15,"g"],[44]],[15,"g"],[17,[15,"a"],"defaultValue"]]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_custom_scripts"],[52,"v","allow_direct_google_requests"],[52,"w","allow_enhanced_conversions"],[52,"x","allow_google_signals"],[52,"y","auid"],[52,"z","aw_remarketing_only"],[52,"aA","discount"],[52,"aB","aw_feed_country"],[52,"aC","aw_feed_language"],[52,"aD","items"],[52,"aE","aw_merchant_id"],[52,"aF","aw_basket_type"],[52,"aG","client_id"],[52,"aH","conversion_cookie_prefix"],[52,"aI","conversion_id"],[52,"aJ","conversion_linker"],[52,"aK","conversion_api"],[52,"aL","cookie_deprecation"],[52,"aM","cookie_expires"],[52,"aN","cookie_prefix"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","developer_id"],[52,"aX","shipping"],[52,"aY","engagement_time_msec"],[52,"aZ","estimated_delivery_date"],[52,"bA","event_developer_id_string"],[52,"bB","event"],[52,"bC","event_timeout"],[52,"bD","first_party_collection"],[52,"bE","match_id"],[52,"bF","gdpr_applies"],[52,"bG","google_analysis_params"],[52,"bH","_google_ng"],[52,"bI","gpp_sid"],[52,"bJ","gpp_string"],[52,"bK","gsa_experiment_id"],[52,"bL","gtag_event_feature_usage"],[52,"bM","iframe_state"],[52,"bN","ignore_referrer"],[52,"bO","is_passthrough"],[52,"bP","_lps"],[52,"bQ","language"],[52,"bR","merchant_feed_label"],[52,"bS","merchant_feed_language"],[52,"bT","merchant_id"],[52,"bU","new_customer"],[52,"bV","page_hostname"],[52,"bW","page_path"],[52,"bX","page_referrer"],[52,"bY","page_title"],[52,"bZ","_platinum_request_status"],[52,"cA","quantity"],[52,"cB","restricted_data_processing"],[52,"cC","screen_resolution"],[52,"cD","send_page_view"],[52,"cE","server_container_url"],[52,"cF","session_duration"],[52,"cG","session_engaged_time"],[52,"cH","session_id"],[52,"cI","_shared_user_id"],[52,"cJ","delivery_postal_code"],[52,"cK","topmost_url"],[52,"cL","transaction_id"],[52,"cM","transport_url"],[52,"cN","update"],[52,"cO","_user_agent_architecture"],[52,"cP","_user_agent_bitness"],[52,"cQ","_user_agent_full_version_list"],[52,"cR","_user_agent_mobile"],[52,"cS","_user_agent_model"],[52,"cT","_user_agent_platform"],[52,"cU","_user_agent_platform_version"],[52,"cV","_user_agent_wow64"],[52,"cW","user_data"],[52,"cX","user_data_auto_latency"],[52,"cY","user_data_auto_meta"],[52,"cZ","user_data_auto_multi"],[52,"dA","user_data_auto_selectors"],[52,"dB","user_data_auto_status"],[52,"dC","user_data_mode"],[52,"dD","user_id"],[52,"dE","user_properties"],[52,"dF","us_privacy_string"],[52,"dG","value"],[52,"dH","_fpm_parameters"],[52,"dI","_host_name"],[52,"dJ","_in_page_command"],[52,"dK","non_personalized_ads"],[52,"dL","conversion_label"],[52,"dM","page_location"],[52,"dN","global_developer_id_string"],[52,"dO","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"F",[15,"e"],"H",[15,"f"],"I",[15,"g"],"J",[15,"h"],"K",[15,"i"],"L",[15,"j"],"X",[15,"k"],"AC",[15,"l"],"AD",[15,"m"],"AE",[15,"n"],"AG",[15,"o"],"AH",[15,"p"],"AJ",[15,"q"],"AN",[15,"r"],"AX",[15,"s"],"BE",[15,"t"],"BF",[15,"u"],"BG",[15,"v"],"BI",[15,"w"],"BJ",[15,"x"],"BP",[15,"y"],"BS",[15,"z"],"BT",[15,"aA"],"BU",[15,"aB"],"BV",[15,"aC"],"BW",[15,"aD"],"BX",[15,"aE"],"BY",[15,"aF"],"CG",[15,"aG"],"CL",[15,"aH"],"CM",[15,"aI"],"JS",[15,"dL"],"CN",[15,"aJ"],"CP",[15,"aK"],"CQ",[15,"aL"],"CS",[15,"aM"],"CW",[15,"aN"],"CX",[15,"aO"],"CY",[15,"aP"],"CZ",[15,"aQ"],"DA",[15,"aR"],"DB",[15,"aS"],"DC",[15,"aT"],"DD",[15,"aU"],"DH",[15,"aV"],"DI",[15,"aW"],"DU",[15,"aX"],"DW",[15,"aY"],"EA",[15,"aZ"],"ED",[15,"bA"],"EF",[15,"bB"],"EH",[15,"bC"],"EM",[15,"bD"],"EV",[15,"bE"],"FF",[15,"bF"],"JU",[15,"dN"],"FJ",[15,"bG"],"FK",[15,"bH"],"FN",[15,"bI"],"FO",[15,"bJ"],"FQ",[15,"bK"],"FR",[15,"bL"],"FT",[15,"bM"],"FU",[15,"bN"],"FZ",[15,"bO"],"GA",[15,"bP"],"GB",[15,"bQ"],"GI",[15,"bR"],"GJ",[15,"bS"],"GK",[15,"bT"],"GO",[15,"bU"],"GR",[15,"bV"],"JT",[15,"dM"],"GS",[15,"bW"],"GT",[15,"bX"],"GU",[15,"bY"],"HC",[15,"bZ"],"HE",[15,"cA"],"HI",[15,"cB"],"HM",[15,"cC"],"HP",[15,"cD"],"HR",[15,"cE"],"HT",[15,"cF"],"HV",[15,"cG"],"HW",[15,"cH"],"HY",[15,"cI"],"HZ",[15,"cJ"],"JV",[15,"dO"],"IE",[15,"cK"],"IH",[15,"cL"],"II",[15,"cM"],"IK",[15,"cN"],"IN",[15,"cO"],"IO",[15,"cP"],"IP",[15,"cQ"],"IQ",[15,"cR"],"IR",[15,"cS"],"IS",[15,"cT"],"IT",[15,"cU"],"IU",[15,"cV"],"IV",[15,"cW"],"IW",[15,"cX"],"IX",[15,"cY"],"IY",[15,"cZ"],"IZ",[15,"dA"],"JA",[15,"dB"],"JB",[15,"dC"],"JD",[15,"dD"],"JE",[15,"dE"],"JG",[15,"dF"],"JH",[15,"dG"],"JJ",[15,"dH"],"JK",[15,"dI"],"JL",[15,"dJ"],"JP",[15,"dK"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","allow_ad_personalization"],[52,"e","consent_state"],[52,"f","consent_updated"],[52,"g","conversion_linker_enabled"],[52,"h","cookie_options"],[52,"i","em_event"],[52,"j","event_start_timestamp_ms"],[52,"k","event_usage"],[52,"l","ga4_collection_subdomain"],[52,"m","handle_internally"],[52,"n","hit_type"],[52,"o","hit_type_override"],[52,"p","is_conversion"],[52,"q","is_external_event"],[52,"r","is_first_visit"],[52,"s","is_first_visit_conversion"],[52,"t","is_fpm_encryption"],[52,"u","is_fpm_split"],[52,"v","is_gcp_conversion"],[52,"w","is_google_signals_allowed"],[52,"x","is_server_side_destination"],[52,"y","is_session_start"],[52,"z","is_session_start_conversion"],[52,"aA","is_sgtm_ga_ads_conversion_study_control_group"],[52,"aB","is_sgtm_prehit"],[52,"aC","is_split_conversion"],[52,"aD","is_syn"],[52,"aE","prehit_for_retry"],[52,"aF","redact_ads_data"],[52,"aG","redact_click_ids"],[52,"aH","send_ccm_parallel_ping"],[52,"aI","send_user_data_hit"],[52,"aJ","speculative"],[52,"aK","syn_or_mod"],[52,"aL","transient_ecsid"],[52,"aM","transmission_type"],[52,"aN","user_data"],[52,"aO","user_data_from_automatic"],[52,"aP","user_data_from_automatic_getter"],[52,"aQ","user_data_from_code"],[52,"aR","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"D",[15,"d"],"I",[15,"e"],"J",[15,"f"],"K",[15,"g"],"L",[15,"h"],"R",[15,"i"],"X",[15,"j"],"Y",[15,"k"],"AG",[15,"l"],"AI",[15,"m"],"AJ",[15,"n"],"AK",[15,"o"],"AO",[15,"p"],"AQ",[15,"q"],"AS",[15,"r"],"AT",[15,"s"],"AV",[15,"t"],"AW",[15,"u"],"AX",[15,"v"],"AY",[15,"w"],"BC",[15,"x"],"BD",[15,"y"],"BE",[15,"z"],"BF",[15,"aA"],"BG",[15,"aB"],"BI",[15,"aC"],"BJ",[15,"aD"],"BO",[15,"aE"],"BR",[15,"aF"],"BS",[15,"aG"],"BU",[15,"aH"],"BY",[15,"aI"],"CA",[15,"aJ"],"CD",[15,"aK"],"CE",[15,"aL"],"CF",[15,"aM"],"CG",[15,"aN"],"CH",[15,"aO"],"CI",[15,"aP"],"CJ",[15,"aQ"],"CK",[15,"aR"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",42],[52,"f",43],[52,"g",44],[52,"h",45],[52,"i",46],[52,"j",47],[52,"k",113],[52,"l",129],[52,"m",142],[52,"n",156],[52,"o",168],[52,"p",174],[52,"q",178],[52,"r",212],[52,"s",240],[52,"t",241],[52,"u",243],[52,"v",252],[52,"w",253],[52,"x",254],[36,[8,"EP",[15,"v"],"DC",[15,"o"],"V",[15,"b"],"W",[15,"c"],"X",[15,"d"],"AC",[15,"e"],"AD",[15,"f"],"AE",[15,"g"],"AF",[15,"h"],"AG",[15,"i"],"AH",[15,"j"],"DG",[15,"p"],"DJ",[15,"q"],"EI",[15,"s"],"BN",[15,"k"],"EK",[15,"u"],"BZ",[15,"l"],"EJ",[15,"t"],"ER",[15,"x"],"EQ",[15,"w"],"CM",[15,"m"],"DU",[15,"r"],"CV",[15,"n"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_legacyUrls",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"p"],[52,"q",[2,[15,"p"],"indexOf",[7,"#"]]],[36,[39,[23,[15,"q"],0],[15,"p"],[2,[15,"p"],"substring",[7,0,[15,"q"]]]]]],[50,"i",[46,"p"],[52,"q",[17,["e",[15,"p"]],"protocol"]],[36,[39,[15,"q"],[2,[15,"q"],"replace",[7,":",""]],""]]],[50,"j",[46,"p","q"],[41,"r"],[3,"r",[17,["e",[15,"p"]],"hostname"]],[22,[28,[15,"r"]],[46,[36,""]]],[52,"s",["b",":[0-9]+"]],[3,"r",[2,[15,"r"],"replace",[7,[15,"s"],""]]],[22,[15,"q"],[46,[53,[52,"t",["b","^www\\d*\\."]],[52,"u",[2,[15,"r"],"match",[7,[15,"t"]]]],[22,[1,[15,"u"],[16,[15,"u"],0]],[46,[3,"r",[2,[15,"r"],"substring",[7,[17,[16,[15,"u"],0],"length"]]]]]]]]],[36,[15,"r"]]],[50,"k",[46,"p"],[52,"q",["e",[15,"p"]]],[41,"r"],[3,"r",["f",[17,[15,"q"],"port"]]],[22,[28,[15,"r"]],[46,[53,[22,[20,[17,[15,"q"],"protocol"],"http:"],[46,[53,[3,"r",80]]],[46,[22,[20,[17,[15,"q"],"protocol"],"https:"],[46,[53,[3,"r",443]]],[46,[53,[3,"r",""]]]]]]]]],[36,["g",[15,"r"]]]],[50,"l",[46,"p","q"],[52,"r",["e",[15,"p"]]],[41,"s"],[3,"s",[39,[20,[2,[17,[15,"r"],"pathname"],"indexOf",[7,"/"]],0],[17,[15,"r"],"pathname"],[0,"/",[17,[15,"r"],"pathName"]]]],[22,[20,["d",[15,"q"]],"array"],[46,[53,[52,"t",[2,[15,"s"],"split",[7,"/"]]],[22,[19,[2,[15,"q"],"indexOf",[7,[16,[15,"t"],[37,[17,[15,"t"],"length"],1]]]],0],[46,[53,[43,[15,"t"],[37,[17,[15,"t"],"length"],1],""],[3,"s",[2,[15,"t"],"join",[7,"/"]]]]]]]]],[36,[15,"s"]]],[50,"m",[46,"p"],[52,"q",[17,["e",[15,"p"]],"pathname"]],[52,"r",[2,[15,"q"],"split",[7,"."]]],[41,"s"],[3,"s",[39,[18,[17,[15,"r"],"length"],1],[16,[15,"r"],[37,[17,[15,"r"],"length"],1]],""]],[36,[16,[2,[15,"s"],"split",[7,"/"]],0]]],[50,"n",[46,"p"],[52,"q",[17,["e",[15,"p"]],"hash"]],[36,[2,[15,"q"],"replace",[7,"#",""]]]],[50,"o",[46,"p","q"],[50,"s",[46,"t"],[36,["c",[2,[15,"t"],"replace",[7,["b","\\+","g"]," "]]]]],[52,"r",[2,[17,["e",[15,"p"]],"search"],"replace",[7,"?",""]]],[65,"t",[2,[15,"r"],"split",[7,"&"]],[46,[53,[52,"u",[2,[15,"t"],"split",[7,"="]]],[22,[21,["s",[16,[15,"u"],0]],[15,"q"]],[46,[6]]],[36,["s",[2,[2,[15,"u"],"slice",[7,1]],"join",[7,"="]]]]]]],[36]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","getType"]],[52,"e",["require","internal.legacyParseUrl"]],[52,"f",["require","makeNumber"]],[52,"g",["require","makeString"]],[36,[8,"F",[15,"m"],"H",[15,"o"],"G",[15,"n"],"C",[15,"j"],"E",[15,"l"],"D",[15,"k"],"B",[15,"i"],"A",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtag",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"n",[46,"r","s","t"],[65,"u",[15,"s"],[46,[53,[22,[2,[15,"r"],"hasOwnProperty",[7,[15,"u"]]],[46,[53,[43,[15,"r"],[15,"u"],["t",[16,[15,"r"],[15,"u"]]]]]]]]]]],[50,"o",[46,"r","s"],["n",[15,"r"],[15,"s"],[51,"",[7,"t"],[36,[39,[20,"false",[2,["e",[15,"t"]],"toLowerCase",[7]]],false,[28,[28,[15,"t"]]]]]]]],[50,"p",[46,"r","s"],["n",[15,"r"],[15,"s"],[15,"d"]]],[50,"q",[46,"r","s"],[22,["f",[17,[15,"g"],"EI"]],[46,[53,[52,"t",["h"]],[22,[1,[15,"t"],[18,[2,[15,"t"],"indexOf",[7,[15,"s"]]],[27,1]]],[46,[53,[43,[15,"r"],[17,[15,"i"],"AI"],true]]]]]]]],[52,"b",["require","Object"]],[52,"c",[15,"__module_gtagSchema"]],[52,"d",["require","makeNumber"]],[52,"e",["require","makeString"]],[52,"f",["require","internal.isFeatureEnabled"]],[52,"g",[15,"__module_featureFlags"]],[52,"h",["require","internal.getDestinationIds"]],[52,"i",[15,"__module_metadataSchema"]],[52,"j",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"BE"],[17,[15,"c"],"BG"],[17,[15,"c"],"BJ"],[17,[15,"c"],"CX"],[17,[15,"c"],"FU"],[17,[15,"c"],"IK"],[17,[15,"c"],"EM"],[17,[15,"c"],"HP"]]]]],[52,"k",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"BE"],[17,[15,"c"],"BG"],[17,[15,"c"],"BJ"],[17,[15,"c"],"CX"],[17,[15,"c"],"FU"],[17,[15,"c"],"IK"],[17,[15,"c"],"EM"],[17,[15,"c"],"HP"]]]]],[52,"l",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"CS"],[17,[15,"c"],"EH"],[17,[15,"c"],"HT"],[17,[15,"c"],"HV"],[17,[15,"c"],"DW"]]]]],[52,"m",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"CS"],[17,[15,"c"],"EH"],[17,[15,"c"],"HT"],[17,[15,"c"],"HV"],[17,[15,"c"],"DW"]]]]],[36,[8,"B",[15,"k"],"D",[15,"m"],"A",[15,"j"],"C",[15,"l"],"F",[15,"o"],"G",[15,"p"],"E",[15,"n"],"H",[15,"q"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__f":{"2":true,"5":true}
,
"__googtag":{"1":10,"5":true}
,
"__lcl":{"5":true}
,
"__paused":{"5":true}
,
"__u":{"2":true,"5":true}
,
"__v":{"2":true,"5":true}


}
,"blob":{"1":"21","10":"GTM-NT2453N","14":"59n2","15":"0","16":"NjQ2NjY1MzM2MzM5NzkzMDg1Mw==","19":"dataLayer","20":"","21":"www.googletagmanager.com","22":"eyIwIjoiQlIiLCIxIjoiQlItU1AiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20uYnIiLCI0IjoiIiwiNSI6ZmFsc2UsIjYiOmZhbHNlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"BR","31":"BR-SP","32":false,"36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BK/Gst5Na4jJDshjzt6iEksRJHn/s0fL4YjkZgCIJlcEumkh8nwnkQVOv81SLLo9n2mWHAmWa9+Fgkw2JYYX1Og=\",\"version\":0},\"id\":\"af2af632-dd0c-4115-a1ab-3bffd272e8f5\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BARwwR9ESQd3wyBqu0hZgXJz1dKsSRURrfbbNJk2WE4VCB5kioriMXiHrlFk9GZy+szwpQfB43VLUWzuZ74Vfag=\",\"version\":0},\"id\":\"93a87232-e071-42f8-bc6d-8675fb895d17\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BMJDyfBzKr35XBMQHIbgARQjg/xvBbXc9yJfFqNzG3lKoPPF1NSnhkyGbx5+pAuNMw9CjkZIv4GC48oWolJbTwo=\",\"version\":0},\"id\":\"438c7757-876e-4254-a1b8-d31448aa1eca\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BC0chllHJGqTZZR68Aql8cYoJx3fBo0+5omHYf9LtzCUcHcznPl1+bRG4eMozdb4sLCMpkBSoUcEYqSSDXzX6yQ=\",\"version\":0},\"id\":\"3be49a24-aa14-4b62-b81e-2eafc70b23df\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BDvRDTIxwn+wyzMGa8QrmckYpZ4WGahd85zQTVXNMYoJCGFmqwpjuE60hHKOWD1Cgml/DOON8gtiNvOa7PSx/Xk=\",\"version\":0},\"id\":\"2088bb5e-ce20-48b6-8428-aa7c832d792e\"}]}","44":"101509157~103116026~103200004~103233427~104684208~104684211","46":{"1":"1000","10":"5940","11":"59f0","14":"1000","16":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","17":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","2":"9","20":"5000","21":"5000","22":"3.2.0","23":"0.0.0","25":"1","26":"4000","27":"100","3":"5","4":"ad_storage|analytics_storage|ad_user_data|ad_personalization","44":"15000","48":"30000","5":"ad_storage|analytics_storage|ad_user_data","6":"1","60":"0","7":"10"},"48":true,"5":"GTM-NT2453N","55":["GTM-NT2453N"],"56":[],"6":"12596837","8":"res_ts:1731016827162440,srv_cl:810487051,ds:live,cv:21","9":"GTM-NT2453N"}
,"permissions":{
"__cvt_12596837_70":{"logging":{"environments":"debug"},"access_globals":{"keys":[{"key":"webVitals","read":true,"write":false,"execute":false},{"key":"dataLayer","read":true,"write":true,"execute":false}]},"inject_script":{"urls":["https:\/\/unpkg.com\/web-vitals\/dist\/web-vitals.iife.js","https:\/\/unpkg.com\/web-vitals\/dist\/web-vitals.attribution.iife.js"]}}
,
"__cvt_12596837_81":{}
,
"__c":{}
,
"__cl":{"detect_click_events":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__f":{"read_data_layer":{"keyPatterns":["gtm.referrer"]},"get_referrer":{"urlParts":"any"}}
,
"__googtag":{"logging":{"environments":"debug"},"access_globals":{"keys":[{"key":"gtag","read":true,"write":true,"execute":true},{"key":"dataLayer","read":true,"write":true,"execute":false}]},"configure_google_tags":{"allowedTagIds":"any"},"load_google_tags":{"allowedTagIds":"any","allowFirstPartyUrls":true,"allowedFirstPartyUrls":"any"}}
,
"__jsm":{"unsafe_run_arbitrary_javascript":{}}
,
"__lcl":{"detect_link_click_events":{"allowWaitForTags":true}}
,
"__paused":{}
,
"__u":{"read_data_layer":{"keyPatterns":["gtm.url"]},"get_url":{"urlParts":"any"}}
,
"__v":{"read_data_layer":{"allowedKeys":"any"}}


}

,"sandboxed_scripts":[
"__cvt_12596837_70"
,"__cvt_12596837_81"

]

,"security_groups":{
"customScripts":[
"__jsm"

]
,
"google":[
"__c"
,
"__cl"
,
"__e"
,
"__f"
,
"__googtag"
,
"__u"
,
"__v"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=da(this),ia=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ma={},na={},oa=function(a,b,c){if(!c||a!=null){var d=na[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},pa=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ma?g=ma:g=fa;for(var h=0;h<d.length-1;h++){var l=d[h];if(!(l in g))break a;g=g[l]}var n=d[d.length-1],p=ia&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ba(ma,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(na[n]===void 0){var r=
Math.random()*1E9>>>0;na[n]=ia?fa.Symbol(n):"$jscp$"+r+"$"+n}ba(g,na[n],{configurable:!0,writable:!0,value:q})}}};pa("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var qa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ra;if(ia&&typeof Object.setPrototypeOf=="function")ra=Object.setPrototypeOf;else{var sa;a:{var ua={a:!0},va={};try{va.__proto__=ua;sa=va.a;break a}catch(a){}sa=!1}ra=sa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var wa=ra,za=function(a,b){a.prototype=qa(b.prototype);a.prototype.constructor=a;if(wa)wa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.qr=b.prototype},m=function(a){var b=typeof ma.Symbol!="undefined"&&ma.Symbol.iterator&&a[ma.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},Aa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},Ba=function(a){return a instanceof Array?a:Aa(m(a))},Da=function(a){return Ca(a,a)},Ca=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Ea=ia&&typeof oa(Object,"assign")=="function"?oa(Object,"assign"):function(a,b){if(a==null)throw new TypeError("No nullish arg");a=Object(a);for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};
pa("Object.assign",function(a){return a||Ea},"es6");var Fa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Ga=this||self,Ha=function(a,b){function c(){}c.prototype=b.prototype;a.qr=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.ys=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ia=function(a,b){this.type=a;this.data=b};var Ja=function(){this.map={};this.C={}};Ja.prototype.get=function(a){return this.map["dust."+a]};Ja.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ja.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ja.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ka=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ja.prototype.Ba=function(){return Ka(this,1)};Ja.prototype.sc=function(){return Ka(this,2)};Ja.prototype.Zb=function(){return Ka(this,3)};var La=function(){};La.prototype.reset=function(){};var Ma=function(a,b){this.R=a;this.parent=b;this.M=this.C=void 0;this.Ab=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ja};Ma.prototype.add=function(a,b){Na(this,a,b,!1)};Ma.prototype.ph=function(a,b){Na(this,a,b,!0)};var Na=function(a,b,c,d){if(!a.Ab)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=Ma.prototype;k.set=function(a,b){this.Ab||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.lb=function(){var a=new Ma(this.R,this);this.C&&a.Mb(this.C);a.Uc(this.H);a.Od(this.M);return a};k.Hd=function(){return this.R};k.Mb=function(a){this.C=a};k.Tm=function(){return this.C};k.Uc=function(a){this.H=a};k.ij=function(){return this.H};k.Sa=function(){this.Ab=!0};k.Od=function(a){this.M=a};k.nb=function(){return this.M};var Oa=function(){this.value={};this.prefix="gtm."};Oa.prototype.set=function(a,b){this.value[this.prefix+String(a)]=b};Oa.prototype.get=function(a){return this.value[this.prefix+String(a)]};Oa.prototype.has=function(a){return this.value.hasOwnProperty(this.prefix+String(a))};function Pa(){try{if(Map)return new Map}catch(a){}return new Oa};var Qa=function(){this.values=[]};Qa.prototype.add=function(a){this.values.indexOf(a)===-1&&this.values.push(a)};Qa.prototype.has=function(a){return this.values.indexOf(a)>-1};var Ra=function(a,b){this.ja=a;this.parent=b;this.R=this.H=void 0;this.Ab=!1;this.M=function(d,e,f){return d.apply(e,f)};this.C=Pa();var c;a:{try{if(Set){c=new Set;break a}}catch(d){}c=new Qa}this.U=c};Ra.prototype.add=function(a,b){Sa(this,a,b,!1)};Ra.prototype.ph=function(a,b){Sa(this,a,b,!0)};var Sa=function(a,b,c,d){a.Ab||a.U.has(b)||(d&&a.U.add(b),a.C.set(b,c))};k=Ra.prototype;
k.set=function(a,b){this.Ab||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.U.has(a)||this.C.set(a,b))};k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.lb=function(){var a=new Ra(this.ja,this);this.H&&a.Mb(this.H);a.Uc(this.M);a.Od(this.R);return a};k.Hd=function(){return this.ja};k.Mb=function(a){this.H=a};k.Tm=function(){return this.H};
k.Uc=function(a){this.M=a};k.ij=function(){return this.M};k.Sa=function(){this.Ab=!0};k.Od=function(a){this.R=a};k.nb=function(){return this.R};var Ta=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.fn=a;this.Lm=c===void 0?!1:c;this.debugInfo=[];this.C=b};za(Ta,Error);var Ua=function(a){return a instanceof Ta?a:new Ta(a,void 0,!0)};var Wa=[];function Xa(a){return Wa[a]===void 0?!1:Wa[a]};var Ya=Pa();function Za(a,b){for(var c,d=m(b),e=d.next();!e.done&&!(c=ab(a,e.value),c instanceof Ia);e=d.next());return c}
function ab(a,b){try{if(Xa(17)){var c=b[0],d=b.slice(1),e=String(c),f=Ya.has(e)?Ya.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Ua(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=m(b),h=g.next().value,l=Aa(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Ua(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(Ba(l)))}catch(q){var p=a.Tm();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var bb=function(){this.H=new La;this.C=Xa(17)?new Ra(this.H):new Ma(this.H)};k=bb.prototype;k.Hd=function(){return this.H};k.Mb=function(a){this.C.Mb(a)};k.Uc=function(a){this.C.Uc(a)};k.execute=function(a){return this.Gj([a].concat(Ba(Fa.apply(1,arguments))))};k.Gj=function(){for(var a,b=m(Fa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=ab(this.C,c.value);return a};
k.Yo=function(a){var b=Fa.apply(1,arguments),c=this.C.lb();c.Od(a);for(var d,e=m(b),f=e.next();!f.done;f=e.next())d=ab(c,f.value);return d};k.Sa=function(){this.C.Sa()};var cb=function(){this.Fa=!1;this.da=new Ja};k=cb.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.Ba=function(){return this.da.Ba()};k.sc=function(){return this.da.sc()};k.Zb=function(){return this.da.Zb()};k.Sa=function(){this.Fa=!0};k.Ab=function(){return this.Fa};function db(){for(var a=eb,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function fb(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var eb,gb;function hb(a){eb=eb||fb();gb=gb||db();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,l=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(eb[l],eb[n],eb[p],eb[q])}return b.join("")}
function ib(a){function b(l){for(;d<a.length;){var n=a.charAt(d++),p=gb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return l}eb=eb||fb();gb=gb||db();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var jb={};function kb(a,b){jb[a]=jb[a]||[];jb[a][b]=!0}function lb(){delete jb.GA4_EVENT}function mb(){jb.GTAG_EVENT_FEATURE_CHANNEL=nb}function ob(a){var b=jb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return hb(c.join("")).replace(/\.+$/,"")};function pb(){}function qb(a){return typeof a==="function"}function rb(a){return typeof a==="string"}function sb(a){return typeof a==="number"&&!isNaN(a)}function tb(a){return Array.isArray(a)?a:[a]}function ub(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function vb(a,b){if(!sb(a)||!sb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function wb(a,b){for(var c=new xb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function yb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function zb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function Ab(a){return Math.round(Number(a))||0}function Bb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function Cb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function Db(a){return a?a.replace(/^\s+|\s+$/g,""):""}function Eb(){return new Date(Date.now())}function Fb(){return Eb().getTime()}var xb=function(){this.prefix="gtm.";this.values={}};xb.prototype.set=function(a,b){this.values[this.prefix+a]=b};xb.prototype.get=function(a){return this.values[this.prefix+a]};xb.prototype.contains=function(a){return this.get(a)!==void 0};
function Gb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Hb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Ib(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Jb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Kb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Lb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Nb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Ob=/^\w{1,9}$/;function Pb(a,b){a=a||{};b=b||",";var c=[];yb(a,function(d,e){Ob.test(d)&&e&&c.push(d)});return c.join(b)}function Qb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Rb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Sb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var l=""+f+g+h;l[l.length-1]==="/"&&(l=l.substring(0,l.length-1));return l}
function Tb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Ub(){var a=w,b;a:{var c=a.crypto||a.msCrypto;if(c&&c.getRandomValues)try{var d=new Uint8Array(25);c.getRandomValues(d);b=btoa(String.fromCharCode.apply(String,Ba(d))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");break a}catch(e){}b=void 0}return b};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Vb=globalThis.trustedTypes,Wb;function Xb(){var a=null;if(!Vb)return a;try{var b=function(c){return c};a=Vb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Yb(){Wb===void 0&&(Wb=Xb());return Wb};var Zb=function(a){this.C=a};Zb.prototype.toString=function(){return this.C+""};function $b(a){var b=a,c=Yb(),d=c?c.createScriptURL(b):b;return new Zb(d)}function ac(a){if(a instanceof Zb)return a.C;throw Error("");};var dc=Da([""]),ec=Ca(["\x00"],["\\0"]),fc=Ca(["\n"],["\\n"]),hc=Ca(["\x00"],["\\u0000"]);function ic(a){return a.toString().indexOf("`")===-1}ic(function(a){return a(dc)})||ic(function(a){return a(ec)})||ic(function(a){return a(fc)})||ic(function(a){return a(hc)});var jc=function(a){this.C=a};jc.prototype.toString=function(){return this.C};var kc=function(a){this.Eq=a};function lc(a){return new kc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var mc=[lc("data"),lc("http"),lc("https"),lc("mailto"),lc("ftp"),new kc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function nc(a){var b;b=b===void 0?mc:b;if(a instanceof jc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof kc&&d.Eq(a))return new jc(a)}}var oc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function pc(a){var b;if(a instanceof jc)if(a instanceof jc)b=a.C;else throw Error("");else b=oc.test(a)?a:void 0;return b};function qc(a,b){var c=pc(b);c!==void 0&&(a.action=c)};function rc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var sc=function(a){this.C=a};sc.prototype.toString=function(){return this.C+""};var uc=function(){this.C=tc[0].toLowerCase()};uc.prototype.toString=function(){return this.C};function vc(a,b){var c=[new uc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof uc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var wc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function xc(a){return a===null?"null":a===void 0?"undefined":a};var w=window,yc=window.history,z=document,zc=navigator;function Ac(){var a;try{a=zc.serviceWorker}catch(b){return}return a}var Bc=z.currentScript,Cc=Bc&&Bc.src;function Dc(a,b){var c=w,d=c[a];c[a]=d===void 0?b:d;return c[a]}function Ec(a){return(zc.userAgent||"").indexOf(a)!==-1}function Fc(){return Ec("Firefox")||Ec("FxiOS")}function Gc(){return(Ec("GSA")||Ec("GoogleApp"))&&(Ec("iPhone")||Ec("iPad"))}function Hc(){return Ec("Edg/")||Ec("EdgA/")||Ec("EdgiOS/")}
var Ic={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Jc={height:1,onload:1,src:1,style:1,width:1};function Kc(a,b,c){b&&yb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Lc(a,b,c,d,e){var f=z.createElement("script");Kc(f,d,Ic);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=$b(xc(a));f.src=ac(g);var h,l=f.ownerDocument;l=l===void 0?document:l;var n,p,q=(p=(n=l).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Mc(){if(Cc){var a=Cc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Nc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Kc(g,c,Jc);d&&yb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var l=z.body&&z.body.lastChild||z.body||z.head;l.parentNode.insertBefore(g,l)}b&&(g.onload=b);return g}
function Oc(a,b,c,d){return Pc(a,b,c,d)}function Qc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Rc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Sc(a){w.setTimeout(a,0)}function Tc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Uc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Vc(a){var b=z.createElement("div"),c=b,d,e=xc("A<div>"+a+"</div>"),f=Yb(),g=f?f.createHTML(e):e;d=new sc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof sc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var l=[];b&&b.firstChild;)l.push(b.removeChild(b.firstChild));return l}
function Wc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Xc(a,b,c){var d;try{d=zc.sendBeacon&&zc.sendBeacon(a)}catch(e){kb("TAGGING",15)}d?b==null||b():Pc(a,b,c)}function Yc(a,b){try{return zc.sendBeacon(a,b)}catch(c){kb("TAGGING",15)}return!1}var Zc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function $c(a,b,c,d,e){if(cd()){var f=oa(Object,"assign").call(Object,{},Zc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=w.fetch(a,f);if(g)return g.then(function(l){l&&(l.ok||l.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(l){}}if(c&&c.ng)return e==null||e(),
!1;if(b){var h=Yc(a,b);h?d==null||d():e==null||e();return h}dd(a,d,e);return!0}function cd(){return typeof w.fetch==="function"}function ed(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function fd(){var a=w.performance;if(a&&qb(a.now))return a.now()}
function gd(){var a,b=w.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function hd(){return w.performance||void 0}function id(){var a=w.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Pc=function(a,b,c,d){var e=new Image(1,1);Kc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},dd=Xc;function jd(a,b){return this.evaluate(a)&&this.evaluate(b)}function kd(a,b){return this.evaluate(a)===this.evaluate(b)}function ld(a,b){return this.evaluate(a)||this.evaluate(b)}function md(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function nd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function od(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=w.location.href;d instanceof cb&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var pd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,qd=function(a){if(a==null)return String(a);var b=pd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},rd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},sd=function(a){if(!a||qd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!rd(a,"constructor")&&!rd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
rd(a,b)},td=function(a,b){var c=b||(qd(a)=="array"?[]:{}),d;for(d in a)if(rd(a,d)){var e=a[d];qd(e)=="array"?(qd(c[d])!="array"&&(c[d]=[]),c[d]=td(e,c[d])):sd(e)?(sd(c[d])||(c[d]={}),c[d]=td(e,c[d])):c[d]=e}return c};function ud(a){if(a==void 0||Array.isArray(a)||sd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function vd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var wd=function(a){a=a===void 0?[]:a;this.da=new Ja;this.values=[];this.Fa=!1;for(var b in a)a.hasOwnProperty(b)&&(vd(b)?this.values[Number(b)]=a[Number(b)]:this.da.set(b,a[b]))};k=wd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof wd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Fa)if(a==="length"){if(!vd(b))throw Ua(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else vd(a)?this.values[Number(a)]=b:this.da.set(a,b)};k.get=function(a){return a==="length"?this.length():vd(a)?this.values[Number(a)]:this.da.get(a)};k.length=function(){return this.values.length};k.Ba=function(){for(var a=this.da.Ba(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.sc=function(){for(var a=this.da.sc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Zb=function(){for(var a=this.da.Zb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){vd(a)?delete this.values[Number(a)]:this.Fa||this.da.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,Ba(Fa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=Fa.apply(2,arguments);return b===void 0&&c.length===0?new wd(this.values.splice(a)):new wd(this.values.splice.apply(this.values,[a,b||0].concat(Ba(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,Ba(Fa.apply(0,arguments)))};k.has=function(a){return vd(a)&&this.values.hasOwnProperty(a)||this.da.has(a)};k.Sa=function(){this.Fa=!0;Object.freeze(this.values)};k.Ab=function(){return this.Fa};
function xd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var yd=function(a,b){this.functionName=a;this.Gd=b;this.da=new Ja;this.Fa=!1};k=yd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new wd(this.Ba())};k.invoke=function(a){return this.Gd.call.apply(this.Gd,[new zd(this,a)].concat(Ba(Fa.apply(1,arguments))))};k.apply=function(a,b){return this.Gd.apply(new zd(this,a),b)};k.Kb=function(a){var b=Fa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(Ba(b)))}catch(c){}};
k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.Ba=function(){return this.da.Ba()};k.sc=function(){return this.da.sc()};k.Zb=function(){return this.da.Zb()};k.Sa=function(){this.Fa=!0};k.Ab=function(){return this.Fa};var Ad=function(a,b){yd.call(this,a,b)};za(Ad,yd);var Bd=function(a,b){yd.call(this,a,b)};za(Bd,yd);var zd=function(a,b){this.Gd=a;this.J=b};
zd.prototype.evaluate=function(a){var b=this.J;return Array.isArray(a)?ab(b,a):a};zd.prototype.getName=function(){return this.Gd.getName()};zd.prototype.Hd=function(){return this.J.Hd()};var Cd=function(){this.map=new Map};Cd.prototype.set=function(a,b){this.map.set(a,b)};Cd.prototype.get=function(a){return this.map.get(a)};var Dd=function(){this.keys=[];this.values=[]};Dd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};Dd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function Ed(){try{return Map?new Cd:new Dd}catch(a){return new Dd}};var Fd=function(a){if(a instanceof Fd)return a;if(ud(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};Fd.prototype.getValue=function(){return this.value};Fd.prototype.toString=function(){return String(this.value)};var Hd=function(a){this.promise=a;this.Fa=!1;this.da=new Ja;this.da.set("then",Gd(this));this.da.set("catch",Gd(this,!0));this.da.set("finally",Gd(this,!1,!0))};k=Hd.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.Ba=function(){return this.da.Ba()};k.sc=function(){return this.da.sc()};k.Zb=function(){return this.da.Zb()};
var Gd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new Ad("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof Ad||(d=void 0);e instanceof Ad||(e=void 0);var f=this.J.lb(),g=function(l){return function(n){try{return c?(l.invoke(f),a.promise):l.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new Fd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Hd(h)})};Hd.prototype.Sa=function(){this.Fa=!0};Hd.prototype.Ab=function(){return this.Fa};function B(a,b,c){var d=Ed(),e=function(g,h){for(var l=g.Ba(),n=0;n<l.length;n++)h[l[n]]=f(g.get(l[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof wd){var l=[];d.set(g,l);for(var n=g.Ba(),p=0;p<n.length;p++)l[n[p]]=f(g.get(n[p]));return l}if(g instanceof Hd)return g.promise.then(function(t){return B(t,b,1)},function(t){return Promise.reject(B(t,b,1))});if(g instanceof cb){var q={};d.set(g,q);e(g,q);return q}if(g instanceof Ad){var r=function(){for(var t=
[],v=0;v<arguments.length;v++)t[v]=Id(arguments[v],b,c);var x=new Ma(b?b.Hd():new La);b&&x.Od(b.nb());return f(Xa(17)?g.apply(x,t):g.invoke.apply(g,[x].concat(Ba(t))))};d.set(g,r);e(g,r);return r}var u=!1;switch(c){case 1:u=!0;break;case 2:u=!1;break;case 3:u=!1;break;default:}if(g instanceof Fd&&u)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Id(a,b,c){var d=Ed(),e=function(g,h){for(var l in g)g.hasOwnProperty(l)&&h.set(l,f(g[l]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||zb(g)){var l=new wd;d.set(g,l);for(var n in g)g.hasOwnProperty(n)&&l.set(n,f(g[n]));return l}if(sd(g)){var p=new cb;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new Ad("",function(){for(var t=Fa.apply(0,arguments),v=[],x=0;x<t.length;x++)v[x]=B(this.evaluate(t[x]),b,c);return f(this.J.ij()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var u=!1;switch(c){case 1:u=!0;break;case 2:u=!1;break;default:}if(g!==void 0&&u)return new Fd(g)};return f(a)};var Jd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof wd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new wd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new wd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new wd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
Ba(Fa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ua(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ua(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ua(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ua(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=xd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new wd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=xd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(Ba(Fa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,Ba(Fa.apply(1,arguments)))}};var Kd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Ld=new Ia("break"),Md=new Ia("continue");function Nd(a,b){return this.evaluate(a)+this.evaluate(b)}function Od(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Pd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof wd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ua(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=B(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ua(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Kd.hasOwnProperty(e)){var l=2;l=1;var n=B(f,void 0,l);return Id(d[e].apply(d,n),this.J)}throw Ua(Error("TypeError: "+e+" is not a function"));}if(d instanceof wd){if(d.has(e)){var p=d.get(String(e));if(p instanceof Ad){var q=xd(f);return Xa(17)?p.apply(this.J,q):p.invoke.apply(p,[this.J].concat(Ba(q)))}throw Ua(Error("TypeError: "+e+" is not a function"));
}if(Jd.supportedMethods.indexOf(e)>=0){var r=xd(f);return Jd[e].call.apply(Jd[e],[d,this.J].concat(Ba(r)))}}if(d instanceof Ad||d instanceof cb||d instanceof Hd){if(d.has(e)){var u=d.get(e);if(u instanceof Ad){var t=xd(f);return Xa(17)?u.apply(this.J,t):u.invoke.apply(u,[this.J].concat(Ba(t)))}throw Ua(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof Ad?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof Fd&&e==="toString")return d.toString();
throw Ua(Error("TypeError: Object has no '"+e+"' property."));}function Qd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.J;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Rd(){var a=Fa.apply(0,arguments),b=this.J.lb(),c=Za(b,a);if(c instanceof Ia)return c}function Sd(){return Ld}
function Td(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ia)return d}}function Ud(){for(var a=this.J,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.ph(c,d)}}}function Vd(){return Md}function Wd(a,b){return new Ia(a,this.evaluate(b))}
function Xd(a,b){var c=Fa.apply(2,arguments),d;d=new wd;for(var e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(Ba(c));this.J.add(a,this.evaluate(g))}function Yd(a,b){return this.evaluate(a)/this.evaluate(b)}function Zd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof Fd,f=d instanceof Fd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function $d(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}
function ae(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Za(f,d);if(g instanceof Ia){if(g.type==="break")break;if(g.type==="return")return g}}}function be(a,b,c){if(typeof b==="string")return ae(a,function(){return b.length},function(f){return f},c);if(b instanceof cb||b instanceof Hd||b instanceof wd||b instanceof Ad){var d=b.Ba(),e=d.length;return ae(a,function(){return e},function(f){return d[f]},c)}}
function ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return be(function(h){g.set(d,h);return g},e,f)}function de(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return be(function(h){var l=g.lb();l.ph(d,h);return l},e,f)}function ee(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return be(function(h){var l=g.lb();l.add(d,h);return l},e,f)}
function fe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ge(function(h){g.set(d,h);return g},e,f)}function he(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ge(function(h){var l=g.lb();l.ph(d,h);return l},e,f)}function ie(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ge(function(h){var l=g.lb();l.add(d,h);return l},e,f)}
function ge(a,b,c){if(typeof b==="string")return ae(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof wd)return ae(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ua(Error("The value is not iterable."));}
function je(a,b,c,d){function e(q,r){for(var u=0;u<f.length();u++){var t=f.get(u);r.add(t,q.get(t))}}var f=this.evaluate(a);if(!(f instanceof wd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.J,h=this.evaluate(d),l=g.lb();for(e(g,l);ab(l,b);){var n=Za(l,h);if(n instanceof Ia){if(n.type==="break")break;if(n.type==="return")return n}var p=g.lb();e(l,p);ab(p,c);l=p}}
function ke(a,b){var c=Fa.apply(2,arguments),d=this.J,e=this.evaluate(b);if(!(e instanceof wd))throw Error("Error: non-List value given for Fn argument names.");return new Ad(a,function(){return function(){var f=Fa.apply(0,arguments),g=d.lb();g.nb()===void 0&&g.Od(this.J.nb());for(var h=[],l=0;l<f.length;l++){var n=this.evaluate(f[l]);h[l]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new wd(h));var r=Za(g,c);if(r instanceof Ia)return r.type===
"return"?r.data:r}}())}function le(a){var b=this.evaluate(a),c=this.J;if(me&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ne(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ua(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof cb||d instanceof Hd||d instanceof wd||d instanceof Ad)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:vd(e)&&(c=d[e]);else if(d instanceof Fd)return;return c}function oe(a,b){return this.evaluate(a)>this.evaluate(b)}function pe(a,b){return this.evaluate(a)>=this.evaluate(b)}
function qe(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof Fd&&(c=c.getValue());d instanceof Fd&&(d=d.getValue());return c===d}function re(a,b){return!qe.call(this,a,b)}function se(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Za(this.J,d);if(e instanceof Ia)return e}var me=!1;
function te(a,b){return this.evaluate(a)<this.evaluate(b)}function ue(a,b){return this.evaluate(a)<=this.evaluate(b)}function ve(){for(var a=new wd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function we(){for(var a=new cb,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function xe(a,b){return this.evaluate(a)%this.evaluate(b)}
function ye(a,b){return this.evaluate(a)*this.evaluate(b)}function ze(a){return-this.evaluate(a)}function Ae(a){return!this.evaluate(a)}function Be(a,b){return!Zd.call(this,a,b)}function Ce(){return null}function De(a,b){return this.evaluate(a)||this.evaluate(b)}function Ee(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Fe(a){return this.evaluate(a)}function Ge(){return Fa.apply(0,arguments)}function He(a){return new Ia("return",this.evaluate(a))}
function Ie(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ua(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof Ad||d instanceof wd||d instanceof cb)&&d.set(String(e),f);return f}function Je(a,b){return this.evaluate(a)-this.evaluate(b)}
function Ke(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,l=0;l<e.length;l++)if(h||d===this.evaluate(e[l]))if(g=this.evaluate(f[l]),g instanceof Ia){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ia&&(g.type==="return"||g.type==="continue")))return g}
function Le(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Me(a){var b=this.evaluate(a);return b instanceof Ad?"function":typeof b}function Ne(){for(var a=this.J,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Oe(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Za(this.J,e);if(f instanceof Ia){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Za(this.J,e);if(g instanceof Ia){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Pe(a){return~Number(this.evaluate(a))}function Qe(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Re(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Se(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Te(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ue(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ve(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function We(){}
function Xe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ia)return d}catch(h){if(!(h instanceof Ta&&h.Lm))throw h;var e=this.J.lb();a!==""&&(h instanceof Ta&&(h=h.fn),e.add(a,new Fd(h)));var f=this.evaluate(c),g=Za(e,f);if(g instanceof Ia)return g}}function Ye(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ta&&f.Lm))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ia)return e;if(c)throw c;if(d instanceof Ia)return d};var $e=function(){this.C=new bb;Ze(this)};$e.prototype.execute=function(a){return this.C.Gj(a)};var Ze=function(a){var b=function(c,d){var e=new Bd(String(c),d);e.Sa();var f=String(c);a.C.C.set(f,e);Ya.set(f,e)};b("map",we);b("and",jd);b("contains",md);b("equals",kd);b("or",ld);b("startsWith",nd);b("variable",od)};$e.prototype.Mb=function(a){this.C.Mb(a)};var bf=function(){this.H=!1;this.C=new bb;af(this);this.H=!0};bf.prototype.execute=function(a){return cf(this.C.Gj(a))};var df=function(a,b,c){return cf(a.C.Yo(b,c))};bf.prototype.Sa=function(){this.C.Sa()};
var af=function(a){var b=function(c,d){var e=String(c),f=new Bd(e,d);f.Sa();a.C.C.set(e,f);Ya.set(e,f)};b(0,Nd);b(1,Od);b(2,Pd);b(3,Qd);b(56,Te);b(57,Qe);b(58,Pe);b(59,Ve);b(60,Re);b(61,Se);b(62,Ue);b(53,Rd);b(4,Sd);b(5,Td);b(68,Xe);b(52,Ud);b(6,Vd);b(49,Wd);b(7,ve);b(8,we);b(9,Td);b(50,Xd);b(10,Yd);b(12,Zd);b(13,$d);b(67,Ye);b(51,ke);b(47,ce);b(54,de);b(55,ee);b(63,je);b(64,fe);b(65,he);b(66,ie);b(15,le);b(16,ne);b(17,ne);b(18,oe);b(19,pe);b(20,qe);b(21,re);b(22,se);b(23,te);b(24,ue);b(25,xe);b(26,
ye);b(27,ze);b(28,Ae);b(29,Be);b(45,Ce);b(30,De);b(32,Ee);b(33,Ee);b(34,Fe);b(35,Fe);b(46,Ge);b(36,He);b(43,Ie);b(37,Je);b(38,Ke);b(39,Le);b(40,Me);b(44,We);b(41,Ne);b(42,Oe)};bf.prototype.Hd=function(){return this.C.Hd()};bf.prototype.Mb=function(a){this.C.Mb(a)};bf.prototype.Uc=function(a){this.C.Uc(a)};
function cf(a){if(a instanceof Ia||a instanceof Ad||a instanceof wd||a instanceof cb||a instanceof Hd||a instanceof Fd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var ef=function(a){this.message=a};function ff(a){a.Es=!0;return a};var gf=ff(function(a){return typeof a==="string"});function hf(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new ef("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function jf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var kf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function lf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+hf(e)+c}a<<=2;d||(a|=32);return c=""+hf(a|b)+c}
function mf(a,b){var c;var d=a.Gh,e=a.Wm;d===void 0?c="":(e||(e=0),c=""+lf(1,1)+hf(d<<2|e));var f=a.Ep,g="4"+c+(f?""+lf(2,1)+hf(f):""),h,l=a.qn;h=l&&kf.test(l)?""+lf(3,2)+l:"";var n,p=a.nn;n=p?""+lf(4,1)+hf(p):"";var q;var r=a.ctid;if(r&&b){var u=lf(5,3),t=r.split("-"),v=t[0].toUpperCase();if(v!=="GTM"&&v!=="OPT")q="";else{var x=t[1];q=""+u+hf(1+x.length)+(a.Fq||0)+x}}else q="";var y=a.mr,A=a.canonicalId,D=a.Oa,E=a.Is,J=g+h+n+q+(y?""+lf(6,1)+hf(y):"")+(A?""+lf(7,3)+hf(A.length)+A:"")+(D?""+lf(8,3)+
hf(D.length)+D:"")+(E?""+lf(9,3)+hf(E.length)+E:""),F;var M=a.Kp;M=M===void 0?{}:M;for(var T=[],ha=m(Object.keys(M)),S=ha.next();!S.done;S=ha.next()){var ca=S.value;T[Number(ca)]=M[ca]}if(T.length){var ta=lf(10,3),ka;if(T.length===0)ka=hf(0);else{for(var ea=[],X=0,la=!1,ya=0;ya<T.length;ya++){la=!0;var xa=ya%6;T[ya]&&(X|=1<<xa);xa===5&&(ea.push(hf(X)),X=0,la=!1)}la&&ea.push(hf(X));ka=ea.join("")}var Va=ka;F=""+ta+hf(Va.length)+Va}else F="";var $a=a.Lq,bc=a.Zq,cc=a.nr;return J+F+($a?""+lf(11,3)+hf($a.length)+
$a:"")+(bc?""+lf(13,3)+hf(bc.length)+bc:"")+(cc?""+lf(14,1)+hf(cc):"")};var nf=function(){function a(b){return{toString:function(){return b}}}return{Hn:a("consent"),fk:a("convert_case_to"),gk:a("convert_false_to"),hk:a("convert_null_to"),ik:a("convert_true_to"),jk:a("convert_undefined_to"),Fr:a("debug_mode_metadata"),Qa:a("function"),eh:a("instance_name"),cp:a("live_only"),ep:a("malware_disabled"),METADATA:a("metadata"),jp:a("original_activity_id"),Zr:a("original_vendor_template_id"),Yr:a("once_on_load"),hp:a("once_per_event"),hm:a("once_per_load"),ds:a("priority_override"),
ks:a("respected_consent_types"),rm:a("setup_tags"),oh:a("tag_id"),Cm:a("teardown_tags")}}();
var pf=function(a){return of[a]},rf=function(a){return qf[a]},tf=function(a){return sf[a]},uf=[],sf={"\x00":"&#0;",'"':"&quot;","&":"&amp;","'":"&#39;","<":"&lt;",">":"&gt;","\t":"&#9;","\n":"&#10;","\v":"&#11;","\f":"&#12;","\r":"&#13;"," ":"&#32;","-":"&#45;","/":"&#47;","=":"&#61;","`":"&#96;","\u0085":"&#133;","\u00a0":"&#160;","\u2028":"&#8232;","\u2029":"&#8233;"},vf=/[\x00\x22\x26\x27\x3c\x3e]/g;
var zf=/[\x00\x08-\x0d\x22\x26\x27\/\x3c-\x3e\\\x85\u2028\u2029]/g,qf={"\x00":"\\x00",
"\b":"\\x08","\t":"\\t","\n":"\\n","\v":"\\x0b","\f":"\\f","\r":"\\r",'"':"\\x22","&":"\\x26","'":"\\x27","/":"\\/","<":"\\x3c","=":"\\x3d",">":"\\x3e","\\":"\\\\","\u0085":"\\x85","\u2028":"\\u2028","\u2029":"\\u2029",$:"\\x24","(":"\\x28",")":"\\x29","*":"\\x2a","+":"\\x2b",",":"\\x2c","-":"\\x2d",".":"\\x2e",":":"\\x3a","?":"\\x3f","[":"\\x5b","]":"\\x5d","^":"\\x5e","{":"\\x7b","|":"\\x7c","}":"\\x7d"};
uf[8]=function(a){if(a==null)return" null ";switch(typeof a){case "boolean":case "number":return" "+a+" ";default:return"'"+String(String(a)).replace(zf,rf)+"'"}};var Hf=/[\x00- \x22\x27-\x29\x3c\x3e\\\x7b\x7d\x7f\x85\xa0\u2028\u2029\uff01\uff03\uff04\uff06-\uff0c\uff0f\uff1a\uff1b\uff1d\uff1f\uff20\uff3b\uff3d]/g,of={"\x00":"%00","\u0001":"%01","\u0002":"%02","\u0003":"%03","\u0004":"%04","\u0005":"%05","\u0006":"%06","\u0007":"%07","\b":"%08","\t":"%09","\n":"%0A","\v":"%0B","\f":"%0C","\r":"%0D","\u000e":"%0E","\u000f":"%0F","\u0010":"%10",
"\u0011":"%11","\u0012":"%12","\u0013":"%13","\u0014":"%14","\u0015":"%15","\u0016":"%16","\u0017":"%17","\u0018":"%18","\u0019":"%19","\u001a":"%1A","\u001b":"%1B","\u001c":"%1C","\u001d":"%1D","\u001e":"%1E","\u001f":"%1F"," ":"%20",'"':"%22","'":"%27","(":"%28",")":"%29","<":"%3C",">":"%3E","\\":"%5C","{":"%7B","}":"%7D","\u007f":"%7F","\u0085":"%C2%85","\u00a0":"%C2%A0","\u2028":"%E2%80%A8","\u2029":"%E2%80%A9","\uff01":"%EF%BC%81","\uff03":"%EF%BC%83","\uff04":"%EF%BC%84","\uff06":"%EF%BC%86",
"\uff07":"%EF%BC%87","\uff08":"%EF%BC%88","\uff09":"%EF%BC%89","\uff0a":"%EF%BC%8A","\uff0b":"%EF%BC%8B","\uff0c":"%EF%BC%8C","\uff0f":"%EF%BC%8F","\uff1a":"%EF%BC%9A","\uff1b":"%EF%BC%9B","\uff1d":"%EF%BC%9D","\uff1f":"%EF%BC%9F","\uff20":"%EF%BC%A0","\uff3b":"%EF%BC%BB","\uff3d":"%EF%BC%BD"};uf[16]=function(a){return a};var Jf;var Kf=[],Lf=[],Mf=[],Nf=[],Of=[],Pf,Qf,Rf;function Sf(a){Rf=Rf||a}
function Tf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Kf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Nf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Mf.push(f[g]);for(var h=a.rules||[],l=0;l<h.length;l++){for(var n=h[l],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Uf(p[r])}Lf.push(p)}}
function Uf(a){}var Vf,Wf=[],Xf=[];function Yf(a,b){var c={};c[nf.Qa]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Zf(a,b,c){try{return Qf($f(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var $f=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=ag(a[e],b,c));return d},ag=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(ag(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Kf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[nf.eh]);try{var l=$f(g,b,c);l.vtp_gtmEventId=b.id;b.priorityId&&(l.vtp_gtmPriorityId=b.priorityId);d=bg(l,{event:b,index:f,type:2,
name:h});Vf&&(d=Vf.Lp(d,l))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[ag(a[n],b,c)]=ag(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=ag(a[q],b,c);Rf&&(p=p||Rf.Bq(r));d.push(r)}return Rf&&p?Rf.Qp(d):d.join("");case "escape":d=ag(a[1],b,c);if(Rf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Rf.Cq(a))return Rf.Rq(d);d=String(d);for(var u=2;u<a.length;u++)uf[a[u]]&&(d=uf[a[u]](d));return d;
case "tag":var t=a[1];if(!Nf[t])throw Error("Unable to resolve tag reference "+t+".");return{Qm:a[2],index:t};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[nf.Qa]=a[1];var x=Zf(v,b,c),y=!!a[4];return y||x!==2?y!==(x===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},bg=function(a,b){var c=a[nf.Qa],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Pf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Wf.indexOf(c)!==-1,g={},h={},l;for(l in a)a.hasOwnProperty(l)&&Kb(l,"vtp_")&&(e&&(g[l]=a[l]),!e||f)&&(h[l.substring(4)]=a[l]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Kf[q];break;case 1:r=Nf[q];break;default:n="";break a}var u=r&&r[nf.eh];n=u?String(u):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var t,v,x;if(f&&Xf.indexOf(c)===-1){Xf.push(c);
var y=Fb();t=e(g);var A=Fb()-y,D=Fb();v=Jf(c,h,b);x=A-(Fb()-D)}else if(e&&(t=e(g)),!e||f)v=Jf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),ud(t)?(Array.isArray(t)?Array.isArray(v):sd(t)?sd(v):typeof t==="function"?typeof v==="function":t===v)||d.reportMacroDiscrepancy(d.id,c):t!==v&&d.reportMacroDiscrepancy(d.id,c),x!==void 0&&d.reportMacroDiscrepancy(d.id,c,x));return e?t:v};var cg=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};za(cg,Error);cg.prototype.getMessage=function(){return this.message};function dg(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)dg(a[c],b[c])}};function eg(){return function(a,b){var c;var d=fg;a instanceof Ta?(a.C=d,c=a):c=new Ta(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function fg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)sb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function gg(a){function b(r){for(var u=0;u<r.length;u++)d[r[u]]=!0}for(var c=[],d=[],e=hg(a),f=0;f<Lf.length;f++){var g=Lf[f],h=ig(g,e);if(h){for(var l=g.add||[],n=0;n<l.length;n++)c[l[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Nf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function ig(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function hg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Zf(Mf[c],a));return b[c]}};function jg(a,b){b[nf.fk]&&typeof a==="string"&&(a=b[nf.fk]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(nf.hk)&&a===null&&(a=b[nf.hk]);b.hasOwnProperty(nf.jk)&&a===void 0&&(a=b[nf.jk]);b.hasOwnProperty(nf.ik)&&a===!0&&(a=b[nf.ik]);b.hasOwnProperty(nf.gk)&&a===!1&&(a=b[nf.gk]);return a};var kg=function(){this.C={}},mg=function(a,b){var c=lg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,Ba(Fa.apply(0,arguments)))})};function ng(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new cg(c,d,g);}}
function og(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(Ba(Fa.apply(1,arguments))));ng(e,b,d,g);ng(f,b,d,g)}}}};var rg=function(a,b){var c=this;this.H={};this.C=new kg;var d={},e={},f=og(this.C,a,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(Ba(Fa.apply(1,arguments)))):{}});yb(b,function(g,h){function l(p){var q=Fa.apply(1,arguments);if(!n[p])throw pg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(Ba(q)))}var n={};yb(h,function(p,q){var r=qg(p,q);n[p]=r.assert;d[p]||(d[p]=r.V);r.Jm&&!e[p]&&(e[p]=r.Jm)});c.H[g]=function(p,q){var r=n[p];if(!r)throw pg(p,
{},"The requested permission "+p+" is not configured.");var u=Array.prototype.slice.call(arguments,0);r.apply(void 0,u);f.apply(void 0,u);var t=e[p];t&&t.apply(null,[l].concat(Ba(u.slice(1))))}})},sg=function(a){return lg.H[a]||function(){}};function qg(a,b){var c=Yf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=pg;try{return bg(c)}catch(d){return{assert:function(e){throw new cg(e,{},"Permission "+e+" is unknown.");},V:function(){throw new cg(a,{},"Permission "+a+" is unknown.");}}}}
function pg(a,b,c){return new cg(a,b,c)};var tg=!1;var ug={};ug.Ar=Bb('');ug.Cs=Bb('');var zg=[],Ag;function Bg(a,b,c){var d=Cg(a,c===void 0?!1:c);return d!==b?(Ag?Ag(a):zg.push(a),b):d}function Cg(a,b){b=b===void 0?!1:b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?!!data.blob[a]:b}function C(a){var b;b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}function Dg(){var a=Eg.M,b=Fg(54);return b===a||isNaN(b)&&isNaN(a)?b:(Ag?Ag(54):zg.push(54),a)}
function Fg(a){var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(a))?Number(data.blob[a]):0}function Gg(a,b){b=b===void 0?"":b;var c=Hg(46);return c&&(c==null?0:c.hasOwnProperty(a))?String(c[a]):b}function Hg(a){var b,c;return(b=data)==null?void 0:(c=b.blob)==null?void 0:c[a]}function Ig(){var a=Jg;Ag=a;for(var b=m(zg),c=b.next();!c.done;c=b.next())a(c.value);zg.length=0};function Kg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var Lg=[];function Mg(a){switch(a){case 1:return 0;case 235:return 18;case 38:return 13;case 256:return 11;case 257:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 17;case 75:return 3;case 103:return 14;case 197:return 15;case 109:return 19;case 269:return 21;case 116:return 4;case 135:return 8;case 136:return 5;case 261:return 20}}function Ng(a,b){Lg[a]=b;var c=Mg(a);c!==void 0&&(Wa[c]=b)}function G(a){Ng(a,!0)}G(39);
G(145);G(153);G(144);G(120);G(5);G(111);G(139);
G(87);G(92);G(159);
G(132);G(20);G(72);
G(113);G(154);G(116);Ng(23,!1),G(24);Gg(6);Gg(7);Gg(35);G(29);Og(26,25);
G(37);G(9);G(91);G(123);G(158);G(71);
G(136);
G(127);G(27);
G(69);G(135);G(95);
G(38);G(103);G(112);G(101);
G(122);G(121);G(21);G(134);G(22);
G(141);G(90);
G(59);G(175);G(177);
G(185);
G(197);G(200);G(206);G(231);G(232);G(241);

function H(a){return!!Lg[a]}
function Og(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?G(b):G(a)};var I={P:{Pn:1,Rn:2,Dm:3,jm:4,qk:5,rk:6,Vo:7,Sn:8,Uo:9,On:10,Nn:11,wm:12,sm:13,Xj:14,An:15,Cn:16,fm:17,sk:18,dm:19,Qn:20,fp:21,Gn:22,Bn:23,Dn:24,pk:25,Vj:26,pp:27,Ll:28,Ul:29,Tl:30,Sl:31,Ol:32,Ml:33,Nl:34,Kl:35,Jl:36}};I.P[I.P.Pn]="CREATE_EVENT_SOURCE";I.P[I.P.Rn]="EDIT_EVENT";I.P[I.P.Dm]="TRAFFIC_TYPE";I.P[I.P.jm]="REFERRAL_EXCLUSION";I.P[I.P.qk]="ECOMMERCE_FROM_GTM_TAG";I.P[I.P.rk]="ECOMMERCE_FROM_GTM_UA_SCHEMA";I.P[I.P.Vo]="GA_SEND";I.P[I.P.Sn]="EM_FORM";I.P[I.P.Uo]="GA_GAM_LINK";I.P[I.P.On]="CREATE_EVENT_AUTO_PAGE_PATH";
I.P[I.P.Nn]="CREATED_EVENT";I.P[I.P.wm]="SIDELOADED";I.P[I.P.sm]="SGTM_LEGACY_CONFIGURATION";I.P[I.P.Xj]="CCD_EM_EVENT";I.P[I.P.An]="AUTO_REDACT_EMAIL";I.P[I.P.Cn]="AUTO_REDACT_QUERY_PARAM";I.P[I.P.fm]="MULTIPLE_PAGEVIEW_FROM_CONFIG";I.P[I.P.sk]="EM_EVENT_SENT_BEFORE_CONFIG";I.P[I.P.dm]="LOADED_VIA_CST_OR_SIDELOADING";I.P[I.P.Qn]="DECODED_PARAM_MATCH";I.P[I.P.fp]="NON_DECODED_PARAM_MATCH";I.P[I.P.Gn]="CCD_EVENT_SGTM";I.P[I.P.Bn]="AUTO_REDACT_EMAIL_SGTM";I.P[I.P.Dn]="AUTO_REDACT_QUERY_PARAM_SGTM";
I.P[I.P.pk]="DAILY_LIMIT_REACHED";I.P[I.P.Vj]="BURST_LIMIT_REACHED";I.P[I.P.pp]="SHARED_USER_ID_SET_AFTER_REQUEST";I.P[I.P.Ll]="GA4_MULTIPLE_SESSION_COOKIES";I.P[I.P.Ul]="INVALID_GA4_SESSION_COUNT";I.P[I.P.Tl]="INVALID_GA4_LAST_EVENT_TIMESTAMP";I.P[I.P.Sl]="INVALID_GA4_JOIN_TIMER";I.P[I.P.Ol]="GA4_STALE_SESSION_COOKIE_SELECTED";I.P[I.P.Ml]="GA4_SESSION_COOKIE_GS1_READ";I.P[I.P.Nl]="GA4_SESSION_COOKIE_GS2_READ";I.P[I.P.Kl]="GA4_DL_PARAM_RECOVERY_AVAILABLE";I.P[I.P.Jl]="GA4_DL_PARAM_RECOVERY_APPLIED";var Qg={},Rg=(Qg.uaa=!0,Qg.uab=!0,Qg.uafvl=!0,Qg.uamb=!0,Qg.uam=!0,Qg.uap=!0,Qg.uapv=!0,Qg.uaw=!0,Qg);
var Zg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Xg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,l;a:if(d.length===0)l=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Yg.exec(n[p])){l=!1;break a}l=!0}if(!l||h.length>d.length||!g&&d.length!==e.length?0:g?Kb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Yg=/^[a-z$_][\w-$]*$/i,Xg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var $g=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function ah(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function bh(a,b){return String(a).split(",").indexOf(String(b))>=0}var ch=new xb;function dh(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=ch.get(e);f||(f=new RegExp(b,d),ch.set(e,f));return f.test(a)}catch(g){return!1}}function eh(a,b){return String(a).indexOf(String(b))>=0}
function fh(a,b){return String(a)===String(b)}function gh(a,b){return Number(a)>=Number(b)}function hh(a,b){return Number(a)<=Number(b)}function ih(a,b){return Number(a)>Number(b)}function jh(a,b){return Number(a)<Number(b)}function kh(a,b){return Kb(String(a),String(b))};
var lh=function(a,b){return a.length&&b.length&&a.lastIndexOf(b)===a.length-b.length},mh=function(a,b){var c=b.charAt(b.length-1)==="*"||b==="/"||b==="/*";lh(b,"/*")&&(b=b.slice(0,-2));lh(b,"?")&&(b=b.slice(0,-1));var d=b.split("*");if(!c&&d.length===1)return a===d[0];for(var e=-1,f=0;f<d.length;f++){var g=d[f];if(g){e=a.indexOf(g,e);if(e===-1||f===0&&e!==0)return!1;e+=g.length}}if(c||e===a.length)return!0;var h=d[d.length-1];return a.lastIndexOf(h)===a.length-h.length},nh=function(a){return a.protocol===
"https:"&&(!a.port||a.port==="443")},qh=function(a,b){var c;if(!(c=!nh(a))){var d;a:{var e=a.hostname.split(".");if(e.length<2)d=!1;else{for(var f=0;f<e.length;f++)if(!oh.exec(e[f])){d=!1;break a}d=!0}}c=!d}if(c)return!1;for(var g=0;g<b.length;g++){var h;var l=a,n=b[g];if(!ph.exec(n))throw Error("Invalid Wildcard");var p=n.slice(8),q=p.slice(0,p.indexOf("/")),r;var u=l.hostname,t=q;if(t.indexOf("*.")!==0)r=u.toLowerCase()===t.toLowerCase();else{t=t.slice(2);var v=u.toLowerCase().indexOf(t.toLowerCase());
r=v===-1?!1:u.length===t.length?!0:u.length!==t.length+v?!1:u[v-1]==="."}if(r){var x=p.slice(p.indexOf("/"));h=mh(l.pathname+l.search,x)?!0:!1}else h=!1;if(h)return!0}return!1},oh=/^[a-z0-9-]+$/i,ph=/^https:\/\/(\*\.|)((?:[a-z0-9-]+\.)+[a-z0-9-]+)\/(.*)$/i;var rh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,sh={Fn:"function",PixieMap:"Object",List:"Array"};
function th(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=rh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],l=b[d];if(l==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof l;l instanceof Ad?n="Fn":l instanceof wd?n="List":l instanceof cb?n="PixieMap":l instanceof Hd?n="PixiePromise":l instanceof Fd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((sh[n]||n)+", which does not match required type ")+
((sh[h]||h)+"."));}}}function K(a,b,c){for(var d=[],e=m(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof Ad?d.push("function"):g instanceof wd?d.push("Array"):g instanceof cb?d.push("Object"):g instanceof Hd?d.push("Promise"):g instanceof Fd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function uh(a){return a instanceof cb}function vh(a){return uh(a)||a===null||wh(a)}
function xh(a){return a instanceof Ad}function yh(a){return xh(a)||a===null||wh(a)}function zh(a){return a instanceof wd}function Ah(a){return a instanceof Fd}function Bh(a){return typeof a==="string"}function Ch(a){return Bh(a)||a===null||wh(a)}function Dh(a){return typeof a==="boolean"}function Eh(a){return Dh(a)||wh(a)}function Fh(a){return Dh(a)||a===null||wh(a)}function Gh(a){return typeof a==="number"}function wh(a){return a===void 0};function Hh(a){return""+a}
function Ih(a,b){var c=[];return c};function Jh(a,b){var c=new Ad(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ua(g);}});c.Sa();return c}
function Kh(a,b){var c=new cb,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];qb(e)?c.set(d,Jh(a+"_"+d,e)):sd(e)?c.set(d,Kh(a+"_"+d,e)):(sb(e)||rb(e)||typeof e==="boolean")&&c.set(d,e)}c.Sa();return c};function Lh(a,b){if(!Bh(a))throw K(this.getName(),["string"],arguments);if(!Ch(b))throw K(this.getName(),["string","undefined"],arguments);var c={},d=new cb;return d=Kh("AssertApiSubject",
c)};function Mh(a,b){if(!Ch(b))throw K(this.getName(),["string","undefined"],arguments);if(a instanceof Hd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new cb;return d=Kh("AssertThatSubject",c)};function Nh(a){return function(){for(var b=Fa.apply(0,arguments),c=[],d=this.J,e=0;e<b.length;++e)c.push(B(b[e],d));return Id(a.apply(null,c))}}function Oh(){for(var a=Math,b=Qh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=Nh(a[e].bind(a)))}return c};function Rh(a){return a!=null&&Kb(a,"__cvt_")};function Sh(a){var b;return b};function Th(a){var b;if(!Bh(a))throw K(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Uh(a){try{return encodeURI(a)}catch(b){}};function Vh(a){try{return encodeURIComponent(String(a))}catch(b){}};function $h(a){if(!Ch(a))throw K(this.getName(),["string|undefined"],arguments);};function ai(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};function bi(a){var b=B(a);return ai(b?""+b:"")};function ci(a,b){if(!Gh(a)||!Gh(b))throw K(this.getName(),["number","number"],arguments);return vb(a,b)};function di(){return(new Date).getTime()};function ei(a){if(a===null)return"null";if(a instanceof wd)return"array";if(a instanceof Ad)return"function";if(a instanceof Fd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function fi(a){function b(c){return function(d){try{return c(d)}catch(e){(tg||ug.Ar)&&a.call(this,e.message)}}}return{parse:b(function(c){return Id(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(B(c))}),publicName:"JSON"}};function gi(a){return Ab(B(a,this.J))};function hi(a){return Number(B(a,this.J))};function ii(a){return a===null?"null":a===void 0?"undefined":a.toString()};function ji(a,b,c){var d=null,e=!1;if(!zh(a)||!Bh(b)||!Bh(c))throw K(this.getName(),["Array","string","string"],arguments);d=new cb;for(var f=0;f<a.length();f++){var g=a.get(f);g instanceof cb&&g.has(b)&&g.has(c)&&(d.set(g.get(b),g.get(c)),e=!0)}return e?d:null};var Qh="floor ceil round max min abs pow sqrt".split(" ");function ki(){var a={};return{gq:function(b){return a.hasOwnProperty(b)?a[b]:void 0},tn:function(b,c){a[b]=c},reset:function(){a={}}}}function li(a,b){return function(){return Ad.prototype.invoke.apply(a,[b].concat(Ba(Fa.apply(0,arguments))))}}
function mi(a,b){if(!Bh(a))throw K(this.getName(),["string","any"],arguments);}
function ni(a,b){if(!Bh(a)||!uh(b))throw K(this.getName(),["string","PixieMap"],arguments);};var oi={};var pi=function(a){var b=new cb;if(a instanceof wd)for(var c=a.Ba(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof Ad)for(var f=a.Ba(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var l=0;l<a.length;l++)b.set(l,a[l]);return b};
oi.keys=function(a){th(this.getName(),arguments);if(a instanceof wd||a instanceof Ad||typeof a==="string")a=pi(a);if(a instanceof cb||a instanceof Hd)return new wd(a.Ba());return new wd};
oi.values=function(a){th(this.getName(),arguments);if(a instanceof wd||a instanceof Ad||typeof a==="string")a=pi(a);if(a instanceof cb||a instanceof Hd)return new wd(a.sc());return new wd};
oi.entries=function(a){th(this.getName(),arguments);if(a instanceof wd||a instanceof Ad||typeof a==="string")a=pi(a);if(a instanceof cb||a instanceof Hd)return new wd(a.Zb().map(function(b){return new wd(b)}));return new wd};
oi.freeze=function(a){(a instanceof cb||a instanceof Hd||a instanceof wd||a instanceof Ad)&&a.Sa();return a};oi.delete=function(a,b){if(a instanceof cb&&!a.Ab())return a.remove(b),!0;return!1};function L(a,b){var c=Fa.apply(2,arguments),d=a.J.nb();if(!d)throw Error("Missing program state.");if(d.Xq){try{d.Km.apply(null,[b].concat(Ba(c)))}catch(e){throw kb("TAGGING",21),e;}return}d.Km.apply(null,[b].concat(Ba(c)))};var qi=function(){this.H={};this.C={};this.M=!0;};qi.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};qi.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
qi.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:qb(b)?Jh(a,b):Kh(a,b)};function ri(a,b){var c=void 0;return c};function si(){var a={};
return a};var N={m:{La:"ad_personalization",aa:"ad_storage",W:"ad_user_data",ka:"analytics_storage",hc:"region",ia:"consent_updated",wg:"wait_for_update",Tn:"app_remove",Un:"app_store_refund",Vn:"app_store_subscription_cancel",Wn:"app_store_subscription_convert",Xn:"app_store_subscription_renew",Yn:"consent_update",uk:"add_payment_info",vk:"add_shipping_info",Ud:"add_to_cart",Vd:"remove_from_cart",wk:"view_cart",Yc:"begin_checkout",Wd:"select_item",jc:"view_item_list",yc:"select_promotion",kc:"view_promotion",
qb:"purchase",Xd:"refund",mc:"view_item",xk:"add_to_wishlist",Zn:"exception",ao:"first_open",bo:"first_visit",ma:"gtag.config",nc:"gtag.get",co:"in_app_purchase",Zc:"page_view",eo:"screen_view",fo:"session_start",ho:"source_update",io:"timing_complete",jo:"track_social",Yd:"user_engagement",ko:"user_id_update",Oe:"gclid_link_decoration_source",Pe:"gclid_storage_source",oc:"gclgb",rb:"gclid",yk:"gclid_len",Zd:"gclgs",ae:"gcllp",be:"gclst",Ha:"ads_data_redaction",Qe:"gad_source",Re:"gad_source_src",
bd:"gclid_url",zk:"gclsrc",Se:"gbraid",ce:"wbraid",Ob:"allow_ad_personalization_signals",Cg:"allow_custom_scripts",Te:"allow_direct_google_requests",Dg:"allow_display_features",Qh:"allow_enhanced_conversions",Pb:"allow_google_signals",Rh:"allow_interest_groups",lo:"app_id",mo:"app_installer_id",no:"app_name",oo:"app_version",dd:"auid",Ir:"auto_detection_enabled",Ak:"aw_remarketing",Sh:"aw_remarketing_only",Eg:"discount",Fg:"aw_feed_country",Gg:"aw_feed_language",ya:"items",Hg:"aw_merchant_id",Bk:"aw_basket_type",
Ue:"campaign_content",Ve:"campaign_id",We:"campaign_medium",Xe:"campaign_name",Ye:"campaign",Ze:"campaign_source",af:"campaign_term",Qb:"client_id",Ck:"rnd",Th:"consent_update_type",po:"content_group",qo:"content_type",sb:"conversion_cookie_prefix",Uh:"conversion_id",jb:"conversion_linker",Vh:"conversion_linker_disabled",ed:"conversion_api",Ig:"cookie_deprecation",tb:"cookie_domain",ub:"cookie_expires",Bb:"cookie_flags",fd:"cookie_name",Rb:"cookie_path",Va:"cookie_prefix",zc:"cookie_update",gd:"country",
kb:"currency",Wh:"customer_buyer_stage",bf:"customer_lifetime_value",Xh:"customer_loyalty",Yh:"customer_ltv_bucket",cf:"custom_map",Jg:"gcldc",hd:"dclid",Dk:"debug_mode",Ea:"developer_id",ro:"disable_merchant_reported_purchases",jd:"dc_custom_params",so:"dc_natural_search",Ek:"dynamic_event_settings",Fk:"affiliation",Kg:"checkout_option",Zh:"checkout_step",Gk:"coupon",df:"item_list_name",ai:"list_name",uo:"promotions",ee:"shipping",Hk:"tax",Lg:"engagement_time_msec",Mg:"enhanced_client_id",vo:"enhanced_conversions",
Jr:"enhanced_conversions_automatic_settings",ef:"estimated_delivery_date",ff:"event_callback",wo:"event_category",Ac:"event_developer_id_string",xo:"event_label",Bc:"event",Ng:"event_settings",Og:"event_timeout",yo:"description",zo:"fatal",Ao:"experiments",bi:"firebase_id",fe:"first_party_collection",Pg:"_x_20",qc:"_x_19",Bo:"flight_error_code",Co:"flight_error_message",Ik:"fl_activity_category",Jk:"fl_activity_group",di:"fl_advertiser_id",Kk:"fl_ar_dedupe",hf:"match_id",Lk:"fl_random_number",Mk:"tran",
Nk:"u",Qg:"gac_gclid",he:"gac_wbraid",Ok:"gac_wbraid_multiple_conversions",Pk:"ga_restrict_domain",Qk:"ga_temp_client_id",Do:"ga_temp_ecid",ie:"gdpr_applies",Rk:"geo_granularity",jf:"value_callback",kf:"value_key",Dc:"google_analysis_params",je:"_google_ng",ke:"google_signals",Sk:"google_tld",lf:"gpp_sid",nf:"gpp_string",Rg:"groups",Tk:"gsa_experiment_id",pf:"gtag_event_feature_usage",Uk:"gtm_up",Ec:"iframe_state",qf:"ignore_referrer",ei:"internal_traffic_results",Vk:"_is_fpm",Fc:"is_legacy_converted",
Gc:"is_legacy_loaded",fi:"is_passthrough",kd:"_lps",wb:"language",Sg:"legacy_developer_id_string",Wa:"linker",rf:"accept_incoming",Hc:"decorate_forms",na:"domains",ld:"url_position",md:"merchant_feed_label",nd:"merchant_feed_language",od:"merchant_id",Wk:"method",Eo:"name",Xk:"navigation_type",tf:"new_customer",Tg:"non_interaction",Fo:"optimize_id",Yk:"page_hostname",uf:"page_path",Xa:"page_referrer",Cb:"page_title",Zk:"passengers",al:"phone_conversion_callback",Go:"phone_conversion_country_code",
bl:"phone_conversion_css_class",Ho:"phone_conversion_ids",fl:"phone_conversion_number",il:"phone_conversion_options",Io:"_platinum_request_status",Jo:"_protected_audience_enabled",me:"quantity",Ug:"redact_device_info",gi:"referral_exclusion_definition",Kr:"_request_start_time",Sb:"restricted_data_processing",Ko:"retoken",Lo:"sample_rate",hi:"screen_name",Ic:"screen_resolution",jl:"_script_source",Mo:"search_term",pd:"send_page_view",rd:"send_to",sd:"server_container_url",No:"session_attributes_encoded",
vf:"session_duration",Vg:"session_engaged",ii:"session_engaged_time",Tb:"session_id",Wg:"session_number",wf:"_shared_user_id",ne:"delivery_postal_code",Lr:"_tag_firing_delay",Mr:"_tag_firing_time",Nr:"temporary_client_id",ji:"_timezone",ki:"topmost_url",Xg:"tracking_id",li:"traffic_type",Pa:"transaction_id",rc:"transport_url",kl:"trip_type",ud:"update",Db:"url_passthrough",ml:"uptgs",xf:"_user_agent_architecture",yf:"_user_agent_bitness",zf:"_user_agent_full_version_list",Af:"_user_agent_mobile",
Bf:"_user_agent_model",Cf:"_user_agent_platform",Df:"_user_agent_platform_version",Ef:"_user_agent_wow64",xb:"user_data",nl:"user_data_auto_latency",ol:"user_data_auto_meta",pl:"user_data_auto_multi",ql:"user_data_auto_selectors",rl:"user_data_auto_status",Eb:"user_data_mode",sl:"user_data_settings",Ma:"user_id",Ub:"user_properties",tl:"_user_region",Ff:"us_privacy_string",Ia:"value",vl:"wbraid_multiple_conversions",Jc:"_fpm_parameters",ui:"_host_name",Vl:"_in_page_command",xi:"_ip_override",Zl:"_is_passthrough_cid",
Fi:"_measurement_type",Dd:"non_personalized_ads",Ni:"_sst_parameters",op:"sgtm_geo_user_country",de:"conversion_label",za:"page_location",Cc:"global_developer_id_string",oe:"tc_privacy_string"}};var ti={},ui=(ti[N.m.ia]="gcu",ti[N.m.oc]="gclgb",ti[N.m.rb]="gclaw",ti[N.m.yk]="gclid_len",ti[N.m.Zd]="gclgs",ti[N.m.ae]="gcllp",ti[N.m.be]="gclst",ti[N.m.dd]="auid",ti[N.m.Eg]="dscnt",ti[N.m.Fg]="fcntr",ti[N.m.Gg]="flng",ti[N.m.Hg]="mid",ti[N.m.Bk]="bttype",ti[N.m.Qb]="gacid",ti[N.m.de]="label",ti[N.m.ed]="capi",ti[N.m.Ig]="pscdl",ti[N.m.kb]="currency_code",ti[N.m.Wh]="clobs",ti[N.m.bf]="vdltv",ti[N.m.Xh]="clolo",ti[N.m.Yh]="clolb",ti[N.m.Dk]="_dbg",ti[N.m.ef]="oedeld",ti[N.m.Ac]="edid",ti[N.m.Qg]=
"gac",ti[N.m.he]="gacgb",ti[N.m.Ok]="gacmcov",ti[N.m.ie]="gdpr",ti[N.m.Cc]="gdid",ti[N.m.je]="_ng",ti[N.m.lf]="gpp_sid",ti[N.m.nf]="gpp",ti[N.m.Tk]="gsaexp",ti[N.m.pf]="_tu",ti[N.m.Ec]="frm",ti[N.m.fi]="gtm_up",ti[N.m.kd]="lps",ti[N.m.Sg]="did",ti[N.m.md]="fcntr",ti[N.m.nd]="flng",ti[N.m.od]="mid",ti[N.m.tf]=void 0,ti[N.m.Cb]="tiba",ti[N.m.Sb]="rdp",ti[N.m.Tb]="ecsid",ti[N.m.wf]="ga_uid",ti[N.m.ne]="delopc",ti[N.m.oe]="gdpr_consent",ti[N.m.Pa]="oid",ti[N.m.ml]="uptgs",ti[N.m.xf]="uaa",ti[N.m.yf]=
"uab",ti[N.m.zf]="uafvl",ti[N.m.Af]="uamb",ti[N.m.Bf]="uam",ti[N.m.Cf]="uap",ti[N.m.Df]="uapv",ti[N.m.Ef]="uaw",ti[N.m.nl]="ec_lat",ti[N.m.ol]="ec_meta",ti[N.m.pl]="ec_m",ti[N.m.ql]="ec_sel",ti[N.m.rl]="ec_s",ti[N.m.Eb]="ec_mode",ti[N.m.Ma]="userId",ti[N.m.Ff]="us_privacy",ti[N.m.Ia]="value",ti[N.m.vl]="mcov",ti[N.m.ui]="hn",ti[N.m.Vl]="gtm_ee",ti[N.m.xi]="uip",ti[N.m.Fi]="mt",ti[N.m.Dd]="npa",ti[N.m.op]="sg_uc",ti[N.m.Uh]=null,ti[N.m.Ic]=null,ti[N.m.wb]=null,ti[N.m.ya]=null,ti[N.m.za]=null,ti[N.m.Xa]=
null,ti[N.m.ki]=null,ti[N.m.Jc]=null,ti[N.m.Oe]=null,ti[N.m.Pe]=null,ti[N.m.Dc]=null,ti);function vi(a,b){if(a){var c=a.split("x");c.length===2&&(wi(b,"u_w",c[0]),wi(b,"u_h",c[1]))}}
function xi(a){var b=yi;b=b===void 0?zi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var l=c;if(l){for(var n=[],p=0;p<l.length;p++){var q=l[p],r=[];q&&(r.push(Ai(q.value)),r.push(Ai(q.quantity)),r.push(Ai(q.item_id)),r.push(Ai(q.start_date)),r.push(Ai(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function zi(a){return Bi(a.item_id,a.id,a.item_name)}function Bi(){for(var a=m(Fa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function Ci(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function wi(a,b,c){c===void 0||c===null||c===""&&!Rg[b]||(a[b]=c)}function Ai(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var Di={},Ei=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=vb(0,1)===0,b=vb(0,1)===0,c++,c>30)return;return a},Gi={er:Fi};function Fi(a,b){var c=Di[b];if(!(vb(0,9999)<c.probability*(c.controlId2?4:2)*1E4))return a;var d=c.studyId,e=c.experimentId,f=c.controlId,g=c.controlId2;if(!((a.exp||{})[e]||(a.exp||{})[f]||g&&(a.exp||{})[g])){var h=Ei()?0:1;g&&(h|=(Ei()?0:1)<<1);h===0?Hi(a,e,d):h===1?Hi(a,f,d):h===2&&Hi(a,g,d)}return a}
function Ii(a,b){return Di[b]?!!Di[b].active||Di[b].probability>.5||!!(a.exp||{})[Di[b].experimentId]:!1}function Ji(a,b){for(var c=a.exp||{},d=m(Object.keys(c).map(Number)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c[f]===b)return f}}function Hi(a,b,c){var d=a.exp||{};d[b]=c;a.exp=d};var O={N:{Wj:"call_conversion",Rd:"ccm_conversion",xa:"conversion",Oo:"floodlight",Hf:"ga_conversion",wd:"gcp_remarketing",Di:"landing_page",Na:"page_view",ve:"fpm_test_hit",Hb:"remarketing",Vb:"user_data_lead",zb:"user_data_web"}};var Ni=function(){this.C=new Set;this.H=new Set},Oi=function(a){var b=Eg.U;a=a===void 0?[]:a;var c=[].concat(Ba(b.C)).concat([].concat(Ba(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Pi=function(){var a=[].concat(Ba(Eg.U.C));a.sort(function(b,c){return b-c});return a},Qi=function(){var a=Eg.U,b=C(44);a.C=new Set;if(b!=="")for(var c=m(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Ri={},Si={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Ti={__paused:1,__tg:1},Ui;for(Ui in Si)Si.hasOwnProperty(Ui)&&(Ti[Ui]=1);var Vi=!1;function Wi(){var a=!1;return a}var Xi=H(218)?Bg(45,Wi()):Wi(),Yi,Zi=!1;Yi=Zi;var $i=null,aj=null,bj={},cj={},dj="";Ri.Oi=dj;var Eg=new function(){this.U=new Ni;this.H=this.C=!1;this.M=0;this.ja=this.R=!1};function ej(){var a=C(18),b=a.length;return a[b-1]==="/"?a.substring(0,b-1):a}function fj(){return Eg.H?H(84)?Eg.M===0:Eg.M!==1:!1}function gj(a){for(var b={},c=m(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var hj=/:[0-9]+$/,ij=/^\d+\.fls\.doubleclick\.net$/;function jj(a,b,c,d){var e=kj(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function kj(a,b,c){for(var d={},e=m(a.split("&")),f=e.next();!f.done;f=e.next()){var g=m(f.value.split("=")),h=g.next().value,l=Aa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=l.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function lj(a){try{return decodeURIComponent(a)}catch(b){}}function mj(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=nj(a.protocol)||nj(w.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:w.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||w.location.hostname).replace(hj,"").toLowerCase());return oj(a,b,c,d,e)}
function oj(a,b,c,d,e){var f,g=nj(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=pj(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(hj,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||kb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var l=f.split("/");(d||[]).indexOf(l[l.length-
1])>=0&&(l[l.length-1]="");f=l.join("/");break;case "query":f=a.search.replace("?","");e&&(f=jj(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function nj(a){return a?a.replace(":","").toLowerCase():""}function pj(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var qj={},rj=0;
function sj(a){var b=qj[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||kb("TAGGING",1),d="/"+d);var e=c.hostname.replace(hj,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};rj<5&&(qj[a]=b,rj++)}return b}function tj(a,b,c){var d=sj(a);return Sb(b,d,c)}
function uj(a){var b=sj(w.location.href),c=mj(b,"host",!1);if(c&&c.match(ij)){var d=mj(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var vj=/gtag[.\/]js/,wj=/gtm[.\/]js/,xj=!1;function yj(a){if(xj)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(vj.test(c))return"3";if(wj.test(c))return"2"}return"0"};function P(a){kb("GTM",a)};function zj(a){var b=Aj().destinationArray[a],c=Aj().destination[a];return b&&b.length>0?b[0]:c}function Bj(a,b){var c=Aj();c.pending||(c.pending=[]);ub(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Cj(){var a=w.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=m(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Dj=function(){this.container={};this.destination={};this.destinationArray={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Cj()};
function Aj(){var a=Dc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Dj,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.destinationArray||(c.destinationArray={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Cj());return c};function Ej(){return Cg(7)&&Fj().some(function(a){return a===C(5)})}function Gj(){return C(6)||"_"+C(5)}function Hj(){var a=C(10);return a?a.split("|"):[C(5)]}function Fj(){var a=C(9);return a?a.split("|").filter(function(b){return b.indexOf("GTM-")!==0}):[]}function Ij(){var a=Jj(Kj()),b=a&&a.parent;if(b)return Jj(b)}function Lj(){var a=Jj(Kj());if(a){for(;a.parent;){var b=Jj(a.parent);if(!b)break;a=b}return a}}function Jj(a){var b=Aj();return a.isDestination?zj(a.ctid):b.container[a.ctid]}
function Mj(){var a=Aj();if(a.pending){for(var b,c=[],d=!1,e=Hj(),f=Fj(),g={},h=0;h<a.pending.length;g={og:void 0},h++)g.og=a.pending[h],ub(g.og.target.isDestination?f:e,function(l){return function(n){return n===l.og.target.ctid}}(g))?d||(b=g.og.onLoad,d=!0):c.push(g.og);a.pending=c;if(b)try{b(Gj())}catch(l){}}}
function Nj(){for(var a=C(5),b=Hj(),c=Fj(),d=function(n,p){var q={canonicalContainerId:C(6),scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};Bc&&(q.scriptElement=Bc);Cc&&(q.scriptSource=Cc);if(Ij()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var u;b:{var t,v=(t=q.scriptElement)==null?void 0:t.src;if(v){for(var x=Eg.H,y=sj(v),A=x?y.pathname:""+y.hostname+y.pathname,D=z.scripts,E="",J=0;J<D.length;++J){var F=D[J];if(!(F.innerHTML.length===0||!x&&F.innerHTML.indexOf(q.scriptContainerId||
"SHOULD_NOT_BE_SET")<0||F.innerHTML.indexOf(A)<0)){if(F.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){u=String(J);break b}E=String(J)}}if(E){u=E;break b}}u=void 0}var M=u;if(M){xj=!0;r=M;break a}}var T=[].slice.call(z.scripts);r=q.scriptElement?String(T.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=yj(q)}var ha=p?e.destination:e.container,S=ha[n];S?Array.isArray(S)||(p&&S.state===0&&P(93),oa(Object,"assign").call(Object,S,q)):ha[n]=q},e=Aj(),f=m(b),g=f.next();!g.done;g=f.next())d(g.value,
!1);for(var h=m(c),l=h.next();!l.done;l=h.next())d(l.value,!0);e.canonical[Gj()]={};Mj()}function Oj(){var a=Gj();return!!Aj().canonical[a]}function Pj(a){return!!Aj().container[a]}function Qj(a){var b=zj(a);return b?b.state!==0:!1}function Kj(){return{ctid:C(5),isDestination:Cg(7)}}function Rj(a,b,c){var d=Kj(),e=Aj().container[a];e&&e.state!==3||(Aj().container[a]={state:1,context:b,parent:d},Bj({ctid:a,isDestination:!1},c))}
function Sj(){var a=Aj().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function Tj(){var a={};yb(Aj().destination,function(b,c){(c==null?void 0:c.state)===0&&(a[b]=c)});yb(Aj().destinationArray,function(b,c){var d=c[0];(d==null?void 0:d.state)===0&&(a[b]=d)});return a}function Uj(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}
function Vj(){for(var a=Aj(),b=m(Hj()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};function Wj(a){a=a===void 0?[]:a;return Oi(a).join("~")}function Xj(){if(!H(118))return"";var a,b;return(((a=Jj(Kj()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};var Yj={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Zj=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function ak(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return sj(""+c+b).href}}function bk(a,b){if(fj()||Eg.C)return ak(a,b)}
function ck(){return!!Ri.Oi&&Ri.Oi.split("@@").join("")!=="SGTM_TOKEN"}function dk(a){for(var b=m([N.m.sd,N.m.rc]),c=b.next();!c.done;c=b.next()){var d=Q(a,c.value);if(d)return d}}function ek(a,b,c){c=c===void 0?"":c;if(!fj())return a;var d=b?Yj[a]||"":"";d==="/gs"&&(c="");return""+ej()+d+c}function fk(a){if(!fj())return a;for(var b=m(Zj),c=b.next();!c.done;c=b.next()){var d=c.value;if(Kb(a,""+ej()+d))return a+"&_uip="+encodeURIComponent("::")}return a};function gk(a){var b=String(a[nf.Qa]||"").replace(/_/g,"");return Kb(b,"cvt")?"cvt":b}var hk=w.location.search.indexOf("?gtm_latency=")>=0||w.location.search.indexOf("&gtm_latency=")>=0;var ik=Math.random(),jk,kk=Fg(27);jk=hk||ik<kk;var lk,mk=Fg(42);lk=hk||ik>=1-mk;function nk(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var ok=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};var pk,qk;a:{for(var rk=["CLOSURE_FLAGS"],sk=Ga,tk=0;tk<rk.length;tk++)if(sk=sk[rk[tk]],sk==null){qk=null;break a}qk=sk}var uk=qk&&qk[610401301];pk=uk!=null?uk:!1;function vk(){var a=Ga.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var wk,xk=Ga.navigator;wk=xk?xk.userAgentData||null:null;function yk(a){if(!pk||!wk)return!1;for(var b=0;b<wk.brands.length;b++){var c=wk.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function zk(a){return vk().indexOf(a)!=-1};function Ak(){return pk?!!wk&&wk.brands.length>0:!1}function Bk(){return Ak()?!1:zk("Opera")}function Ck(){return zk("Firefox")||zk("FxiOS")}function Dk(){return Ak()?yk("Chromium"):(zk("Chrome")||zk("CriOS"))&&!(Ak()?0:zk("Edge"))||zk("Silk")};function Ek(){return pk?!!wk&&!!wk.platform:!1}function Fk(){return zk("iPhone")&&!zk("iPod")&&!zk("iPad")}function Gk(){Fk()||zk("iPad")||zk("iPod")};var Hk=function(a){Hk[" "](a);return a};Hk[" "]=function(){};Bk();Ak()||zk("Trident")||zk("MSIE");zk("Edge");!zk("Gecko")||vk().toLowerCase().indexOf("webkit")!=-1&&!zk("Edge")||zk("Trident")||zk("MSIE")||zk("Edge");vk().toLowerCase().indexOf("webkit")!=-1&&!zk("Edge")&&zk("Mobile");Ek()||zk("Macintosh");Ek()||zk("Windows");(Ek()?wk.platform==="Linux":zk("Linux"))||Ek()||zk("CrOS");Ek()||zk("Android");Fk();zk("iPad");zk("iPod");Gk();vk().toLowerCase().indexOf("kaios");Ck();Fk()||zk("iPod");zk("iPad");!zk("Android")||Dk()||Ck()||Bk()||zk("Silk");Dk();!zk("Safari")||Dk()||(Ak()?0:zk("Coast"))||Bk()||(Ak()?0:zk("Edge"))||(Ak()?yk("Microsoft Edge"):zk("Edg/"))||(Ak()?yk("Opera"):zk("OPR"))||Ck()||zk("Silk")||zk("Android")||Gk();var Ik={},Jk=null,Kk=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Jk){Jk={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],l=0;l<5;l++){var n=g.concat(h[l].split(""));Ik[l]=n;for(var p=0;p<n.length;p++){var q=n[p];Jk[q]===void 0&&(Jk[q]=p)}}}for(var r=Ik[f],u=Array(Math.floor(b.length/3)),t=r[64]||"",v=0,x=0;v<b.length-2;v+=3){var y=b[v],
A=b[v+1],D=b[v+2],E=r[y>>2],J=r[(y&3)<<4|A>>4],F=r[(A&15)<<2|D>>6],M=r[D&63];u[x++]=""+E+J+F+M}var T=0,ha=t;switch(b.length-v){case 2:T=b[v+1],ha=r[(T&15)<<2]||t;case 1:var S=b[v];u[x]=""+r[S>>2]+r[(S&3)<<4|T>>4]+ha+t}return u.join("")};var Lk=function(a){return decodeURIComponent(a.replace(/\+/g," "))};var Mk=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Nk=/#|$/,Ok=function(a,b){var c=a.search(Nk),d=Mk(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Lk(a.slice(d,e!==-1?e:0))},Pk=/[?&]($|#)/,Qk=function(a,b,c){for(var d,e=a.search(Nk),f=0,g,h=[];(g=Mk(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Pk,"$1");var l,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var u=d.indexOf("?"),t;u<0||u>r?(u=r,t=""):t=d.substring(u+1,r);q=[d.slice(0,u),t,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;l=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else l=d;return l};function Rk(a,b,c,d,e,f,g){var h=Ok(c,"fmt");if(d){var l=Ok(c,"random"),n=Ok(c,"label")||"";if(!l)return!1;var p=Kk(Lk(n)+":"+Lk(l));if(!nk(a,p,d))return!1}h&&Number(h)!==4&&(c=Qk(c,"rfmt",h));var q=Qk(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||Sk(g);Lc(q,function(){g==null||Tk(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||Tk(g);e==null||e()},f,r||void 0);return!0};var Uk={},Vk=(Uk[1]={},Uk[2]={},Uk[3]={},Uk[4]={},Uk);function Wk(a,b,c){var d=Xk(b,c);if(d){var e=Vk[b][d];e||(e=Vk[b][d]=[]);e.push(oa(Object,"assign").call(Object,{},a))}}function Yk(a,b){var c=Xk(a,b);if(c){var d=Vk[a][c];d&&(Vk[a][c]=d.filter(function(e){return!e.on}))}}function Zk(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function Xk(a,b){var c=b;if(b[0]==="/"){var d;c=((d=w.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function $k(a){var b=Fa.apply(1,arguments);lk&&(Wk(a,2,b[0]),Wk(a,3,b[0]));Xc.apply(null,Ba(b))}function al(a){var b=Fa.apply(1,arguments);lk&&Wk(a,2,b[0]);return Yc.apply(null,Ba(b))}function bl(a){var b=Fa.apply(1,arguments);lk&&Wk(a,3,b[0]);Oc.apply(null,Ba(b))}
function cl(a){var b=Fa.apply(1,arguments),c=b[0];lk&&(Wk(a,2,c),Wk(a,3,c));return $c.apply(null,Ba(b))}function dl(a){var b=Fa.apply(1,arguments);lk&&Wk(a,1,b[0]);Lc.apply(null,Ba(b))}function el(a){var b=Fa.apply(1,arguments);b[0]&&lk&&Wk(a,4,b[0]);Nc.apply(null,Ba(b))}function fl(a){var b=Fa.apply(1,arguments);lk&&Wk(a,1,b[2]);return Rk.apply(null,Ba(b))};var gl={Ja:{qe:0,ue:1,Hi:2}};gl.Ja[gl.Ja.qe]="FULL_TRANSMISSION";gl.Ja[gl.Ja.ue]="LIMITED_TRANSMISSION";gl.Ja[gl.Ja.Hi]="NO_TRANSMISSION";var hl={Z:{Gb:0,Ga:1,xc:2,Kc:3}};hl.Z[hl.Z.Gb]="NO_QUEUE";hl.Z[hl.Z.Ga]="ADS";hl.Z[hl.Z.xc]="ANALYTICS";hl.Z[hl.Z.Kc]="MONITORING";function il(){var a=Dc("google_tag_data",{});return a.ics=a.ics||new jl}var jl=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
jl.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;kb("TAGGING",19);b==null?kb("TAGGING",18):kl(this,a,b==="granted",c,d,e,f,g)};jl.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)kl(this,a[d],void 0,void 0,"","",b,c)};
var kl=function(a,b,c,d,e,f,g,h){var l=a.entries,n=l[b]||{},p=n.region,q=d&&rb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),u={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)l[b]=u;r&&w.setTimeout(function(){l[b]===u&&u.quiet&&(kb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=jl.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var l=m(d),n=l.next();!n.done;n=l.next())ll(this,n.value)}else if(b!==void 0&&h!==b)for(var p=m(d),q=p.next();!q.done;q=p.next())ll(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,l=c&&rb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||l===e||(l===d?h!==e:!l&&!h)){var n={region:g.region,declare_region:l,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var l=b.containerScopedDefaults[g];if(l===3)return 1;if(l===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Gd:b})};var ll=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.jn=!0)}};jl.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.jn){d.jn=!1;try{d.Gd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var ml=!1,nl=!1,ol={},pl={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(ol.ad_storage=1,ol.analytics_storage=1,ol.ad_user_data=1,ol.ad_personalization=1,ol),usedContainerScopedDefaults:!1};function ql(a){var b=il();b.accessedAny=!0;return(rb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,pl)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function rl(a){var b=il();b.accessedAny=!0;return b.getConsentState(a,pl)}function sl(a){var b=il();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function tl(){if(!Xa(7))return!1;var a=il();a.accessedAny=!0;if(a.active)return!0;if(!pl.usedContainerScopedDefaults)return!1;for(var b=m(Object.keys(pl.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(pl.containerScopedDefaults[c.value]!==1)return!0;return!1}function ul(a,b){il().addListener(a,b)}
function vl(a,b){il().notifyListeners(a,b)}function wl(a,b){function c(){for(var e=0;e<b.length;e++)if(!sl(b[e]))return!0;return!1}if(c()){var d=!1;ul(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function xl(a,b){function c(){for(var h=[],l=0;l<e.length;l++){var n=e[l];ql(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var l=0;l<h.length;l++)f[h[l]]=!0}var e=rb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),ul(e,function(h){function l(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?l(n):w.setTimeout(function(){l(c())},500)}}))};var yl={},zl=(yl[hl.Z.Gb]=gl.Ja.qe,yl[hl.Z.Ga]=gl.Ja.qe,yl[hl.Z.xc]=gl.Ja.qe,yl[hl.Z.Kc]=gl.Ja.qe,yl),Al=function(a,b){this.C=a;this.consentTypes=b};Al.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return ql(a)});case 1:return this.consentTypes.some(function(a){return ql(a)});default:rc(this.C,"consentsRequired had an unknown type")}};
var Bl={},Cl=(Bl[hl.Z.Gb]=new Al(0,[]),Bl[hl.Z.Ga]=new Al(0,["ad_storage"]),Bl[hl.Z.xc]=new Al(0,["analytics_storage"]),Bl[hl.Z.Kc]=new Al(1,["ad_storage","analytics_storage"]),Bl);var El=function(a){var b=this;this.type=a;this.C=[];ul(Cl[a].consentTypes,function(){Dl(b)||b.flush()})};El.prototype.flush=function(){for(var a=m(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var Dl=function(a){return zl[a.type]===gl.Ja.Hi&&!Cl[a.type].isConsentGranted()},Fl=function(a,b){Dl(a)?a.C.push(b):b()},Gl=new Map;function Hl(a){Gl.has(a)||Gl.set(a,new El(a));return Gl.get(a)};var Il={X:{zn:"aw_user_data_cache",Mh:"cookie_deprecation_label",Bg:"diagnostics_page_id",mi:"eab",Po:"fl_user_data_cache",To:"ga4_user_data_cache",se:"ip_geo_data_cache",wi:"ip_geo_fetch_in_progress",Ei:"local_cookie_cache_map",gm:"nb_data",Ii:"page_experiment_ids",we:"pt_data",im:"pt_listener_set",qm:"service_worker_endpoint",tm:"shared_user_id",vm:"shared_user_id_requested",nh:"shared_user_id_source"}};var Jl=function(a){return ff(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(Il.X);
function Kl(a,b){b=b===void 0?!1:b;if(Jl(a)){var c,d,e=(d=(c=Dc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},l={set:function(n){f=n;l.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=m(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=l}}}
function Ll(a,b){var c=Kl(a,!0);c&&c.set(b)}function Ml(a){var b;return(b=Kl(a))==null?void 0:b.get()}function Nl(a,b){var c=Kl(a);if(!c){c=Kl(a,!0);if(!c)return;c.set(b)}return c.get()}function Ol(a,b){if(typeof b==="function"){var c;return(c=Kl(a,!0))==null?void 0:c.subscribe(b)}}function Pl(a,b){var c=Kl(a);return c?c.unsubscribe(b):!1};var Ql={},Rl=(Ql.tdp=1,Ql.exp=1,Ql.pid=1,Ql.dl=1,Ql.seq=1,Ql.t=1,Ql.v=1,Ql),Sl=["mcc"],Tl={},Ul={},Vl=!1;function Wl(a,b,c){Ul[a]=b;(c===void 0||c)&&Xl(a)}function Xl(a,b){Tl[a]!==void 0&&(b===void 0||!b)||Kb(C(5),"GTM-")&&a==="mcc"||(Tl[a]=!0)}
function Yl(a){a=a===void 0?!1:a;var b=Object.keys(Tl).filter(function(f){return Tl[f]===!0&&Ul[f]!==void 0&&(a||!Sl.includes(f))});Zl(b);var c=b.map(function(f){var g=Ul[f];typeof g==="function"&&(g=g());return g?"&"+f+"="+g:""}).join(""),d="https://"+C(21),e="/td?id="+C(5);return""+ek(d)+e+(""+c+"&z=0")}function Zl(a){a.forEach(function(b){Rl[b]||(Tl[b]=!1)})}
function $l(a){a=a===void 0?!1:a;if(Eg.ja&&lk&&C(5)){var b=Hl(hl.Z.Kc);if(Dl(b))Vl||(Vl=!0,Fl(b,$l));else{var c=Yl(a),d={destinationId:C(5),endpoint:61};a?cl(d,c,void 0,{ng:!0},void 0,function(){bl(d,c+"&img=1")}):bl(d,c);Vl=!1}}}function am(){Object.keys(Tl).filter(function(a){return Tl[a]&&!Rl[a]}).length>0&&$l(!0)}var bm;function cm(){if(Ml(Il.X.Bg)===void 0){var a=function(){Ll(Il.X.Bg,vb());bm=0};a();w.setInterval(a,864E5)}else Ol(Il.X.Bg,function(){bm=0});bm=0}
function dm(){cm();Wl("v","3");Wl("t","t");Wl("pid",function(){return String(Ml(Il.X.Bg))});Wl("seq",function(){return String(++bm)});Wl("exp",Wj());Qc(w,"pagehide",am)};var em=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],fm=[N.m.sd,N.m.rc,N.m.fe,N.m.Qb,N.m.Tb,N.m.Ma,N.m.Wa,N.m.Va,N.m.tb,N.m.Rb],gm=!1,hm=!1,im={},jm={};function km(){!hm&&gm&&(em.some(function(a){return pl.containerScopedDefaults[a]!==1})||lm("mbc"));hm=!0}function lm(a){lk&&(Wl(a,"1"),$l())}function mm(a,b){if(!im[b]&&(im[b]=!0,jm[b]))for(var c=m(fm),d=c.next();!d.done;d=c.next())if(Q(a,d.value)){lm("erc");break}};function nm(a){kb("HEALTH",a)};var om={},pm=!1;function qm(){function a(){c!==void 0&&Pl(Il.X.se,c);try{var e=Ml(Il.X.se);om=JSON.parse(e)}catch(f){P(123),nm(2),om={}}pm=!0;b()}var b=rm,c=void 0,d=Ml(Il.X.se);d?a(d):(c=Ol(Il.X.se,a),sm())}
function sm(){function a(b){Ll(Il.X.se,b||"{}");Ll(Il.X.wi,!1)}if(!Ml(Il.X.wi)){Ll(Il.X.wi,!0);try{w.fetch("https://www.google.com/ccm/geo",{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(b){b.ok?b.text().then(function(c){a(c)},function(){a()}):a()},function(){a()})}catch(b){a()}}}function tm(){var a=C(22);try{return JSON.parse(ib(a))}catch(b){return P(123),nm(2),{}}}function um(){return om["0"]||""}function vm(){return om["1"]||""}
function wm(){var a=!1;return a}function xm(){return om["6"]!==!1}function ym(){var a="";return a}function zm(){var a="";return a};var Am={},Bm=Object.freeze((Am[N.m.Ob]=1,Am[N.m.Dg]=1,Am[N.m.Qh]=1,Am[N.m.Pb]=1,Am[N.m.ya]=1,Am[N.m.tb]=1,Am[N.m.ub]=1,Am[N.m.Bb]=1,Am[N.m.fd]=1,Am[N.m.Rb]=1,Am[N.m.Va]=1,Am[N.m.zc]=1,Am[N.m.cf]=1,Am[N.m.Ea]=1,Am[N.m.Ek]=1,Am[N.m.ff]=1,Am[N.m.Ng]=1,Am[N.m.Og]=1,Am[N.m.fe]=1,Am[N.m.Pk]=1,Am[N.m.Dc]=1,Am[N.m.ke]=1,Am[N.m.Sk]=1,Am[N.m.Rg]=1,Am[N.m.ei]=1,Am[N.m.Fc]=1,Am[N.m.Gc]=1,Am[N.m.Wa]=1,Am[N.m.gi]=1,Am[N.m.Sb]=1,Am[N.m.pd]=1,Am[N.m.rd]=1,Am[N.m.sd]=1,Am[N.m.vf]=1,Am[N.m.ii]=1,Am[N.m.ne]=1,Am[N.m.rc]=
1,Am[N.m.ud]=1,Am[N.m.sl]=1,Am[N.m.Ub]=1,Am[N.m.Jc]=1,Am[N.m.Ni]=1,Am));Object.freeze([N.m.za,N.m.Xa,N.m.Cb,N.m.wb,N.m.hi,N.m.Ma,N.m.bi,N.m.po]);
var Cm={},Dm=Object.freeze((Cm[N.m.Tn]=1,Cm[N.m.Un]=1,Cm[N.m.Vn]=1,Cm[N.m.Wn]=1,Cm[N.m.Xn]=1,Cm[N.m.ao]=1,Cm[N.m.bo]=1,Cm[N.m.co]=1,Cm[N.m.fo]=1,Cm[N.m.Yd]=1,Cm)),Em={},Fm=Object.freeze((Em[N.m.uk]=1,Em[N.m.vk]=1,Em[N.m.Ud]=1,Em[N.m.Vd]=1,Em[N.m.wk]=1,Em[N.m.Yc]=1,Em[N.m.Wd]=1,Em[N.m.jc]=1,Em[N.m.yc]=1,Em[N.m.kc]=1,Em[N.m.qb]=1,Em[N.m.Xd]=1,Em[N.m.mc]=1,Em[N.m.xk]=1,Em)),Gm=Object.freeze([N.m.Ob,N.m.Te,N.m.Pb,N.m.zc,N.m.fe,N.m.qf,N.m.pd,N.m.ud]),Hm=Object.freeze([].concat(Ba(Gm))),Im=Object.freeze([N.m.ub,
N.m.Og,N.m.vf,N.m.ii,N.m.Lg]),Jm=Object.freeze([].concat(Ba(Im))),Km={},Lm=(Km[N.m.aa]="1",Km[N.m.ka]="2",Km[N.m.W]="3",Km[N.m.La]="4",Km),Mm={},Nm=Object.freeze((Mm.search="s",Mm.youtube="y",Mm.playstore="p",Mm.shopping="h",Mm.ads="a",Mm.maps="m",Mm));function Om(a){return typeof a!=="object"||a===null?{}:a}function Pm(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Qm(a){if(a!==void 0&&a!==null)return Pm(a)}function Rm(a){return typeof a==="number"?a:Qm(a)};function Sm(a){return a&&a.indexOf("pending:")===0?Tm(a.substr(8)):!1}function Tm(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Fb();return b<c+3E5&&b>c-9E5};var Um=!1,Vm=!1,Wm=!1,Xm=0,Ym=!1,Zm=[];function $m(a){if(Xm===0)Ym&&Zm&&(Zm.length>=100&&Zm.shift(),Zm.push(a));else if(an()){var b=C(41),c=Dc(b,[]);c.length>=50&&c.shift();c.push(a)}}function bn(){cn();Rc(z,"TAProdDebugSignal",bn)}function cn(){if(!Vm){Vm=!0;dn();var a=Zm;Zm=void 0;a==null||a.forEach(function(b){$m(b)})}}
function dn(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Tm(a)?Xm=1:!Sm(a)||Um||Wm?Xm=2:(Wm=!0,Qc(z,"TAProdDebugSignal",bn,!1),w.setTimeout(function(){cn();Um=!0},200))}function an(){if(!Ym)return!1;switch(Xm){case 1:case 0:return!0;case 2:return!1;default:return!1}};var en=!1;function fn(a,b){var c=Hj(),d=Fj();C(26);if(an()){var e=gn("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;$m(e)}}
function hn(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.hb;e=a.isBatched;var f;if(f=an()){var g;a:switch(c.endpoint){case 19:case 47:g=!0;break a;default:g=!1}f=!g}if(f){var h=gn("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);$m(h)}}function jn(a){an()&&hn(a())}
function gn(a,b){b=b===void 0?{}:b;b.groupId=kn;var c,d=b,e=ln,f={publicId:mn};d.eventId!=null&&(f.eventId=d.eventId);d.priorityId!=null&&(f.priorityId=d.priorityId);d.eventName&&(f.eventName=d.eventName);d.groupId&&(f.groupId=d.groupId);d.tagName&&(f.tagName=d.tagName);c={containerProduct:"GTM",key:f,version:e,messageType:a};c.containerProduct=en?"OGT":"GTM";c.key.targetRef=nn;return c}var mn="",ln="",nn={ctid:"",isDestination:!1},kn;
function on(a){var b=C(5),c=Ej(),d=C(6),e=C(1);C(23);Xm=0;Ym=!0;dn();kn=a;mn=b;ln=e;en=Xi;nn={ctid:b,isDestination:c,canonicalId:d}};var pn=[N.m.aa,N.m.ka,N.m.W,N.m.La],qn,rn;function sn(a){var b=a[N.m.hc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)yb(a,function(d){return function(e,f){if(e!==N.m.hc){var g=Pm(f),h=b[d.cg],l=um(),n=vm();nl=!0;ml&&kb("TAGGING",20);il().declare(e,g,h,l,n)}}}(c))}
function tn(a){km();!rn&&qn&&lm("crc");rn=!0;var b=a[N.m.wg];b&&P(41);var c=a[N.m.hc];c?P(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)yb(a,function(e){return function(f,g){if(f!==N.m.hc&&f!==N.m.wg){var h=Qm(g),l=c[e.dg],n=Number(b),p=um(),q=vm();n=n===void 0?0:n;ml=!0;nl&&kb("TAGGING",20);il().default(f,h,l,p,q,n,pl)}}}(d))}
function un(a){pl.usedContainerScopedDefaults=!0;var b=a[N.m.hc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(vm())&&!c.includes(um()))return}yb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}pl.usedContainerScopedDefaults=!0;pl.containerScopedDefaults[d]=e==="granted"?3:2})}
function vn(a,b){km();qn=!0;yb(a,function(c,d){var e=Pm(d);ml=!0;nl&&kb("TAGGING",20);il().update(c,e,pl)});vl(b.eventId,b.priorityId)}function wn(a){a.hasOwnProperty("all")&&(pl.selectedAllCorePlatformServices=!0,yb(Nm,function(b){pl.corePlatformServices[b]=a.all==="granted";pl.usedCorePlatformServices=!0}));yb(a,function(b,c){b!=="all"&&(pl.corePlatformServices[b]=c==="granted",pl.usedCorePlatformServices=!0)})}function xn(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return ql(b)})}
function yn(a,b){ul(a,b)}function zn(a,b){xl(a,b)}function An(a,b){wl(a,b)}function Mn(){var a=[N.m.aa,N.m.La,N.m.W];il().waitForUpdate(a,500,pl)}function Nn(a){for(var b=m(a),c=b.next();!c.done;c=b.next()){var d=c.value;il().clearTimeout(d,void 0,pl)}vl()}function On(){if(!Yi)for(var a=xm()?gj(Gg(5)):gj(Gg(4)),b=0;b<pn.length;b++){var c=pn[b],d=c,e=a[c]?"granted":"denied";il().implicit(d,e)}};var Pn=!1;H(218)&&(Pn=Bg(49,Pn));var Qn=!1,Rn=[];function Sn(){if(!Qn){Qn=!0;for(var a=Rn.length-1;a>=0;a--)Rn[a]();Rn=[]}};var Tn=w.google_tag_manager=w.google_tag_manager||{};function Un(a,b){return Tn[a]=Tn[a]||b()}function Vn(){var a=C(5),b=Wn;Tn[a]=Tn[a]||b}function Xn(){var a=C(19);return Tn[a]=Tn[a]||{}}function Yn(){var a=C(19);return Tn[a]}function Zn(){var a=Tn.sequence||1;Tn.sequence=a+1;return a}w.google_tag_data=w.google_tag_data||{};function $n(){if(Tn.pscdl!==void 0)Ml(Il.X.Mh)===void 0&&Ll(Il.X.Mh,Tn.pscdl);else{var a=function(c){Tn.pscdl=c;Ll(Il.X.Mh,c)},b=function(){a("error")};try{zc.cookieDeprecationLabel?(a("pending"),zc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var ao=0;function bo(a){lk&&a===void 0&&ao===0&&(Wl("mcc","1"),ao=1)};function co(){var a=[],b=Number('')||0,c=Number('')||0;c||(c=b/100);var d=function(){var ja=!1;ja=!0;return ja}();a.push({oa:21,studyId:21,experimentId:105102050,controlId:105102051,controlId2:105102052,probability:c,active:d,la:0});var e=
Number('')||0,f=Number('0.10')||0;f||(f=e/100);var g=function(){var ja=!1;return ja}();a.push({oa:265,studyId:265,experimentId:115691063,controlId:115691064,controlId2:115691065,
probability:f,active:g,la:0});var h=Number('')||0,l=Number('')||0;l||(l=h/100);var n=function(){var ja=!1;return ja}();a.push({oa:228,studyId:228,experimentId:105177154,controlId:105177155,controlId2:105255245,probability:l,active:n,la:0});var p=Number('')||
0,q=Number('')||0;q||(q=p/100);var r=function(){var ja=!1;return ja}();a.push({oa:256,studyId:256,experimentId:115495938,controlId:115495939,controlId2:115495940,probability:q,active:r,la:0});var u=Number('')||
0,t=Number('')||0;t||(t=u/100);var v=function(){var ja=!1;return ja}();a.push({oa:257,studyId:257,experimentId:115495941,controlId:115495942,controlId2:115495943,probability:t,
active:v,la:0});var x=Number('')||0,y=Number('')||0;y||(y=x/100);var A=function(){var ja=!1;ja=!0;return ja}();a.push({oa:219,studyId:219,experimentId:104948811,
controlId:104948812,controlId2:0,probability:y,active:A,la:0});var D=Number('')||0,E=Number('1')||0;E||(E=D/100);var J=function(){var ja=!1;
return ja}();a.push({oa:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:E,active:J,la:0});var F=Number('')||0,M=Number('0.001')||0;M||(M=F/100);var T=function(){var ja=!1;
return ja}();a.push({oa:255,studyId:255,experimentId:105391252,controlId:105391253,controlId2:105446120,probability:M,active:T,la:0});var ha=Number('')||0,S=Number('')||0;S||(S=ha/100);var ca=function(){var ja=!1;return ja}();a.push({oa:235,studyId:235,experimentId:105357150,controlId:105357151,
controlId2:0,probability:S,active:ca,la:1});var ta=Number('')||0,ka=Number('')||0;ka||(ka=ta/100);var ea=function(){var ja=!1;return ja}();a.push({oa:264,studyId:264,experimentId:115752876,controlId:115752874,controlId2:115752875,probability:ka,active:ea,la:0});var X=Number('')||
0,la=Number('0.5')||0;la||(la=X/100);var ya=function(){var ja=!1;return ja}();a.push({oa:203,studyId:203,experimentId:115480710,controlId:115480709,controlId2:115489982,probability:la,active:ya,la:0});var xa=Number('')||0,Va=Number('')||
0;Va||(Va=xa/100);var $a=function(){var ja=!1;ja=!0;return ja}();a.push({oa:197,studyId:197,experimentId:105113532,controlId:105113531,controlId2:0,probability:Va,active:$a,la:0});var bc=Number('')||0,cc=Number('0.2')||0;cc||(cc=bc/100);var Mb=function(){var ja=!1;return ja}();a.push({oa:243,studyId:243,experimentId:115616985,controlId:115616986,controlId2:0,probability:cc,active:Mb,la:0});var ad=Number('')||0,bd=Number('')||0;bd||(bd=ad/100);var Ph=function(){var ja=!1;
return ja}();a.push({oa:171,studyId:171,experimentId:104967143,controlId:104967140,controlId2:0,probability:bd,active:Ph,la:0});var cH=Number('')||0,Bn=Number('0')||0;Bn||(Bn=cH/100);var dH=function(){var ja=!1;return ja}();
a.push({oa:254,studyId:254,experimentId:115583767,controlId:115583768,controlId2:115583769,probability:Bn,active:dH,la:0});var eH=Number('')||0,Cn=Number('')||0;Cn||(Cn=eH/100);var fH=function(){var ja=!1;
return ja}();a.push({oa:253,studyId:253,experimentId:115583770,controlId:115583771,controlId2:115583772,probability:Cn,active:fH,la:0});var gH=Number('')||0,Dn=Number('')||0;Dn||(Dn=gH/100);var hH=function(){var ja=!1;
return ja}();a.push({oa:266,studyId:266,experimentId:115718529,controlId:115718530,controlId2:115718531,probability:Dn,active:hH,la:0});var iH=Number('')||0,En=Number('')||0;En||(En=iH/100);var jH=function(){var ja=!1;
return ja}();a.push({oa:267,studyId:267,experimentId:115718526,controlId:115718527,controlId2:115718528,probability:En,active:jH,la:0});var kH=Number('')||0,Fn=Number('0.01')||0;Fn||(Fn=kH/100);var lH=function(){var ja=!1;
return ja}();a.push({oa:259,studyId:259,experimentId:105322302,controlId:105322303,controlId2:105322304,probability:Fn,active:lH,la:0});var mH=Number('')||0,Gn=Number('')||0;Gn||(Gn=mH/100);var nH=function(){var ja=!1;return ja}();a.push({oa:249,studyId:249,experimentId:105440521,controlId:105440522,
controlId2:0,focused:!0,probability:Gn,active:nH,la:0});var oH=Number('')||0,Hn=Number('0.5')||0;Hn||(Hn=oH/100);var pH=function(){var ja=!1;return ja}();a.push({oa:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,probability:Hn,active:pH,la:1});var qH=Number('')||
0,In=Number('0.5')||0;In||(In=qH/100);var rH=function(){var ja=!1;return ja}();a.push({oa:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:In,active:rH,la:0});var sH=Number('')||0,Jn=Number('')||
0;Jn||(Jn=sH/100);var tH=function(){var ja=!1;ja=!0;return ja}();a.push({oa:229,studyId:229,experimentId:105359938,controlId:105359937,controlId2:105359936,probability:Jn,active:tH,la:0});var uH=Number('')||0,Kn=Number('')||
0;Kn||(Kn=uH/100);var vH=function(){var ja=!1;return ja}();a.push({oa:261,studyId:261,experimentId:115811758,controlId:115811759,controlId2:0,probability:Kn,active:vH,la:1});var wH=Number('')||0,Ln=Number('')||0;Ln||(Ln=wH/100);
var xH=function(){var ja=!1;return ja}();a.push({oa:225,studyId:225,experimentId:105476338,controlId:105476339,controlId2:105476599,probability:Ln,active:xH,la:0});return a};var R={A:{Ih:"accept_by_default",ug:"add_tag_timing",vg:"ads_event_page_view",Wc:"allow_ad_personalization",Uj:"batch_on_navigation",Yj:"client_id_source",Ke:"consent_event_id",Le:"consent_priority_id",Er:"consent_state",ia:"consent_updated",Sd:"conversion_linker_enabled",Da:"cookie_options",yg:"create_dc_join",zg:"create_fpm_geo_join",Ag:"create_fpm_signals_join",Td:"create_google_join",Oh:"dc_random",Ne:"em_event",Hr:"endpoint_for_debug",tk:"enhanced_client_id_source",Ph:"enhanced_match_result",
wl:"euid_logged_in_state",pe:"euid_mode_enabled",eb:"event_start_timestamp_ms",Al:"event_usage",oi:"extra_tag_experiment_ids",Qr:"add_parameter",ri:"attribution_reporting_experiment",si:"counting_method",Zg:"send_as_iframe",Rr:"parameter_order",ah:"parsed_target",So:"ga4_collection_subdomain",Pl:"gbraid_cookie_marked",Rl:"handle_internally",ba:"hit_type",xd:"hit_type_override",If:"ignore_hit_success_failure",Ur:"is_config_command",fh:"is_consent_update",Jf:"is_conversion",Wl:"is_ecommerce",yd:"is_external_event",
yi:"is_fallback_aw_conversion_ping_allowed",Kf:"is_first_visit",Xl:"is_first_visit_conversion",gh:"is_fl_fallback_conversion_flow_allowed",zd:"is_fpm_encryption",zi:"is_fpm_split",Fb:"is_gcp_conversion",Yl:"is_google_signals_allowed",Bd:"is_merchant_center",hh:"is_new_to_site",Ai:"is_personalization",ih:"is_server_side_destination",te:"is_session_start",am:"is_session_start_conversion",Vr:"is_sgtm_ga_ads_conversion_study_control_group",Wr:"is_sgtm_prehit",bm:"is_sgtm_service_worker",Bi:"is_split_conversion",
Xo:"is_syn",Lf:"join_id",Ci:"join_elapsed",Mf:"join_timer_sec",xe:"tunnel_updated",bs:"prehit_for_retry",es:"promises",hs:"record_aw_latency",Mc:"redact_ads_data",ye:"redact_click_ids",lm:"remarketing_only",Ki:"send_ccm_parallel_ping",ls:"send_ccm_parallel_test_ping",Qf:"send_to_destinations",Li:"send_to_targets",mp:"send_user_data_hit",Za:"source_canonical_id",Aa:"speculative",xm:"speculative_in_message",ym:"suppress_script_load",zm:"syn_or_mod",Em:"transient_ecsid",Rf:"transmission_type",Ra:"user_data",
rs:"user_data_from_automatic",us:"user_data_from_automatic_getter",Gm:"user_data_from_code",rp:"user_data_from_manual",Hm:"user_data_mode",Sf:"user_id_updated"}};var eo={};function fo(a){var b=a,c=a=go[b.studyId]?oa(Object,"assign").call(Object,{},b,{active:!0}):b;c.controlId2&&c.probability<=.25||(c=oa(Object,"assign").call(Object,{},c,{controlId2:0}));Di[c.studyId]=c;a.focused&&(eo[a.studyId]=!0);if(a.la===1){var d=a.studyId;ho(Nl(Il.X.Ii,{}),d);io(d)&&G(d)}else if(a.la===0){var e=a.studyId;ho(jo,e);io(e)&&G(e)}}
function ho(a,b){if(Di[b]){var c=Di[b],d=c.experimentId,e=c.probability;if(!(a.studies||{})[b]){var f=a.studies||{};f[b]=!0;a.studies=f;Di[b].active||(Di[b].probability>.5?Hi(a,d,b):e<=0||e>1||Gi.er(a,b))}}if(!eo[b]){var g=Ji(a,b);g&&Eg.U.H.add(g)}}var jo={};function io(a){return Ii(Nl(Il.X.Ii,{}),a)||Ii(jo,a)}function ko(a){var b=U(a,R.A.oi)||[];return Wj(b)}var go={};
function lo(){go={};var a,b,c=((a=w)==null?void 0:(b=a.location)==null?void 0:b.hash)||"";if(c.indexOf("_te=")!==0){var d=c.substring(5);if(d)for(var e=m(d.split("~")),f=e.next();!f.done;f=e.next()){var g=Number(f.value);g&&(go[g]=!0,G(g))}}for(var h=m(co()),l=h.next();!l.done;l=h.next())fo(l.value);if(H(264)){for(var n=[],p=m(Hg(56)||[]),q=p.next();!q.done;q=p.next()){var r=q.value,u=r[1],t=!!r[2],v=r[3],x=r[4],y=r[5],A=r[6],D=0;switch(r[7]){case 2:D=1;break;case 1:case 0:D=0}if(t||x&&y){var E;a:switch(u){case 249:E=
!0;break a;default:E=!1}n.push({studyId:u,active:t,la:D,focused:E,probability:v,experimentId:x,controlId:y,controlId2:A})}}for(var J=m(n),F=J.next();!F.done;F=J.next())fo(F.value)}};var mo={Gf:{In:"cd",Jn:"ce",Kn:"cf",Ln:"cpf",Mn:"cu"}};var no=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,oo=/\s/;
function po(a,b){if(rb(a)){a=Db(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(no.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var l=0;l<f.length;l++)if(!f[l]||oo.test(f[l])&&(d!=="AW"||l!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function qo(a,b){for(var c={},d=0;d<a.length;++d){var e=po(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[ro[1]]&&f.push(h.destinationId)}for(var l=0;l<f.length;++l)delete c[f[l]];for(var n=[],p=m(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var so={},ro=(so[0]=0,so[1]=1,so[2]=2,so[3]=0,so[4]=1,so[5]=0,so[6]=0,so[7]=0,so);var to=Number(Gg(34,"500")),uo={},vo={},wo={initialized:11,complete:12,interactive:13},xo={},yo=Object.freeze((xo[N.m.pd]=!0,xo)),zo=void 0;function Ao(a,b){if(b.length&&lk){var c;(c=uo)[a]!=null||(c[a]=[]);vo[a]!=null||(vo[a]=[]);var d=b.filter(function(e){return!vo[a].includes(e)});uo[a].push.apply(uo[a],Ba(d));vo[a].push.apply(vo[a],Ba(d));!zo&&d.length>0&&(Xl("tdc",!0),zo=w.setTimeout(function(){$l();uo={};zo=void 0},to))}}
function Bo(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Co(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,u){var t;qd(u)==="object"?t=u[r]:qd(u)==="array"&&(t=u[r]);return t===void 0?yo[r]:t},f=Bo(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,l=e(g,a),n=e(g,b),p=qd(l)==="object"||qd(l)==="array",q=qd(n)==="object"||qd(n)==="array";if(p&&q)Co(l,n,c,h);else if(p||q||l!==n)c[h]=!0}return Object.keys(c)}
function Do(){Wl("tdc",function(){zo&&(w.clearTimeout(zo),zo=void 0);var a=[],b;for(b in uo)uo.hasOwnProperty(b)&&a.push(b+"*"+uo[b].join("."));return a.length?a.join("!"):void 0},!1)};var Eo={T:{Tj:1,Mi:2,Pj:3,mk:4,Qj:5,Xc:6,lk:7,bp:8,om:9,Rj:10,Sj:11,bh:12,Hl:13,El:14,Gl:15,Dl:16,Fl:17,Cl:18,yn:19,Qo:20,Ro:21,Gi:22}};Eo.T[Eo.T.Tj]="ALLOW_INTEREST_GROUPS";Eo.T[Eo.T.Mi]="SERVER_CONTAINER_URL";Eo.T[Eo.T.Pj]="ADS_DATA_REDACTION";Eo.T[Eo.T.mk]="CUSTOMER_LIFETIME_VALUE";Eo.T[Eo.T.Qj]="ALLOW_CUSTOM_SCRIPTS";Eo.T[Eo.T.Xc]="ANY_COOKIE_PARAMS";Eo.T[Eo.T.lk]="COOKIE_EXPIRES";Eo.T[Eo.T.bp]="LEGACY_ENHANCED_CONVERSION_JS_VARIABLE";Eo.T[Eo.T.om]="RESTRICTED_DATA_PROCESSING";Eo.T[Eo.T.Rj]="ALLOW_DISPLAY_FEATURES";
Eo.T[Eo.T.Sj]="ALLOW_GOOGLE_SIGNALS";Eo.T[Eo.T.bh]="GENERATED_TRANSACTION_ID";Eo.T[Eo.T.Hl]="FLOODLIGHT_COUNTING_METHOD_UNKNOWN";Eo.T[Eo.T.El]="FLOODLIGHT_COUNTING_METHOD_STANDARD";Eo.T[Eo.T.Gl]="FLOODLIGHT_COUNTING_METHOD_UNIQUE";Eo.T[Eo.T.Dl]="FLOODLIGHT_COUNTING_METHOD_PER_SESSION";Eo.T[Eo.T.Fl]="FLOODLIGHT_COUNTING_METHOD_TRANSACTIONS";Eo.T[Eo.T.Cl]="FLOODLIGHT_COUNTING_METHOD_ITEMS_SOLD";Eo.T[Eo.T.yn]="ADS_OGT_V1_USAGE";Eo.T[Eo.T.Qo]="FORM_INTERACTION_PERMISSION_DENIED";Eo.T[Eo.T.Ro]="FORM_SUBMIT_PERMISSION_DENIED";
Eo.T[Eo.T.Gi]="MICROTASK_NOT_SUPPORTED";var Fo={},Go=(Fo[N.m.Rh]=Eo.T.Tj,Fo[N.m.sd]=Eo.T.Mi,Fo[N.m.rc]=Eo.T.Mi,Fo[N.m.Ha]=Eo.T.Pj,Fo[N.m.bf]=Eo.T.mk,Fo[N.m.Cg]=Eo.T.Qj,Fo[N.m.zc]=Eo.T.Xc,Fo[N.m.Va]=Eo.T.Xc,Fo[N.m.tb]=Eo.T.Xc,Fo[N.m.fd]=Eo.T.Xc,Fo[N.m.Rb]=Eo.T.Xc,Fo[N.m.Bb]=Eo.T.Xc,Fo[N.m.ub]=Eo.T.lk,Fo[N.m.Sb]=Eo.T.om,Fo[N.m.Dg]=Eo.T.Rj,Fo[N.m.Pb]=Eo.T.Sj,Fo),Ho={},Io=(Ho.unknown=Eo.T.Hl,Ho.standard=Eo.T.El,Ho.unique=Eo.T.Gl,Ho.per_session=Eo.T.Dl,Ho.transactions=Eo.T.Fl,Ho.items_sold=Eo.T.Cl,Ho);var nb=[];function Jo(a,b){b=b===void 0?!1:b;kb("GTAG_EVENT_FEATURE_CHANNEL",a);b&&(nb[a]=!0)}function Ko(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=m(Object.keys(Go)),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)&&Jo(Go[f],b)}};var Lo=function(a,b,c,d,e,f,g,h,l,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.U=d;this.H=e;this.R=f;this.M=g;this.eventMetadata=h;this.onSuccess=l;this.onFailure=n;this.isGtmEvent=p},Mo=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 2:c.push(a.C);break;case 1:c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 4:c.push(a.C),c.push(a.U),c.push(a.H),c.push(a.R)}return c},Q=function(a,b,c,d){for(var e=m(Mo(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},No=function(a){for(var b={},c=Mo(a,4),d=m(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=m(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Lo.prototype.getMergedValues=function(a,b,c){function d(n){sd(n)&&yb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Mo(this,b);g.reverse();for(var h=m(g),l=h.next();!l.done;l=h.next())d(l.value[a]);return f?e:void 0};
var Oo=function(a){for(var b=[N.m.Ye,N.m.Ue,N.m.Ve,N.m.We,N.m.Xe,N.m.Ze,N.m.af],c=Mo(a,3),d=m(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,l=m(b),n=l.next();!n.done;n=l.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Po=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.U={};this.C={};this.M={};this.ja={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Qo=function(a,
b){a.H=b;return a},Ro=function(a,b){a.U=b;return a},So=function(a,b){a.C=b;return a},To=function(a,b){a.M=b;return a},Uo=function(a,b){a.ja=b;return a},Vo=function(a,b){a.R=b;return a},Wo=function(a,b){a.eventMetadata=b||{};return a},Xo=function(a,b){a.onSuccess=b;return a},Yo=function(a,b){a.onFailure=b;return a},Zo=function(a,b){a.isGtmEvent=b;return a},$o=function(a){return new Lo(a.eventId,a.priorityId,a.H,a.U,a.C,a.M,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var ap=new xb,bp={},cp={},fp={name:C(19),set:function(a,b){td(Nb(a,b),bp);dp()},get:function(a){return ep(a,2)},reset:function(){ap=new xb;bp={};dp()}};function ep(a,b){return b!=2?ap.get(a):gp(a)}function gp(a,b){var c=a.split(".");b=b||[];for(var d=bp,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function hp(a,b){cp.hasOwnProperty(a)||(ap.set(a,b),td(Nb(a,b),bp),dp())}
function ip(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=ep(c,1);if(Array.isArray(d)||sd(d))d=td(d,null);cp[c]=d}}function dp(a){yb(cp,function(b,c){ap.set(b,c);td(Nb(b),bp);td(Nb(b,c),bp);a&&delete cp[b]})}function jp(a,b){var c,d=(b===void 0?2:b)!==1?gp(a):ap.get(a);qd(d)==="array"||qd(d)==="object"?c=td(d,null):c=d;return c};var kp={xn:Number(Gg(3))},lp=[],mp=!1;function np(a){lp.push(a)}var op=void 0,pp={},qp=void 0,rp=new function(){var a=5;kp.xn>0&&(a=kp.xn);this.H=a;this.C=0;this.M=[]},sp=1E3;
function tp(a,b){var c=op;if(c===void 0)if(b)c=Zn();else return"";for(var d=[ek("https://"+C(21)),"/a","?id="+C(5)],e=m(lp),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Vc:!!a}),l=m(h),n=l.next();!n.done;n=l.next()){var p=m(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function up(){if(Eg.ja&&(qp&&(w.clearTimeout(qp),qp=void 0),op!==void 0&&vp)){var a=Hl(hl.Z.Kc);if(Dl(a))mp||(mp=!0,Fl(a,up));else{var b;if(!(b=pp[op])){var c=rp;b=c.C<c.H?!1:Fb()-c.M[c.C%c.H]<1E3}if(b||sp--<=0)P(1),pp[op]=!0;else{var d=rp,e=d.C++%d.H;d.M[e]=Fb();var f=tp(!0);bl({destinationId:C(5),endpoint:56,eventId:op},f);mp=vp=!1}}}}function wp(){if(jk&&Eg.ja){var a=tp(!0,!0);bl({destinationId:C(5),endpoint:56,eventId:op},a)}}var vp=!1;
function xp(a){pp[a]||(a!==op&&(up(),op=a),vp=!0,qp||(qp=w.setTimeout(up,500)),tp().length>=2022&&up())}var yp=vb();function zp(){yp=vb()}function Ap(){return[["v","3"],["t","t"],["pid",String(yp)]]};var Bp={};function Cp(a,b,c){jk&&a!==void 0&&(Bp[a]=Bp[a]||[],Bp[a].push(c+b),xp(a))}function Dp(a){var b=a.eventId,c=a.Vc,d=[],e=Bp[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Bp[b];return d};function Ep(a,b,c,d){var e=po(a,!0);e&&Fp.register(e,b,c,d)}function Gp(a,b,c,d){var e=po(c,d.isGtmEvent);e&&(Vi&&(d.deferrable=!0),Fp.push("event",[b,a],e,d))}function Hp(a,b,c,d){var e=po(c,d.isGtmEvent);e&&Fp.push("get",[a,b],e,d)}function Ip(a){var b=po(a,!0),c;b?c=Jp(Fp,b).M:c={};return c}function Kp(a,b){var c=po(a,!0);c&&Lp(Fp,c,b)}
var Mp=function(){this.C={};this.M={};this.R={};this.ja=null;this.H={};this.U=!1;this.status=1},Np=function(a,b,c,d){this.H=Fb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Op=function(){this.destinations={};this.C={};this.commands=[]},Jp=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Mp},Pp=function(a,b,c,d){if(d.C){var e=Jp(a,d.C),f=e.ja;if(f){var g=td(c,null),h=td(e.C[d.C.id],null),l=td(e.H,null),n=td(e.M,null),p=td(a.C,null),q={};if(jk)try{q=
td(bp,null)}catch(x){P(72)}var r=d.C.prefix,u=function(x){Cp(d.messageContext.eventId,r,x)},t=$o(Zo(Yo(Xo(Wo(Uo(To(Vo(So(Ro(Qo(new Po(d.messageContext.eventId,d.messageContext.priorityId),g),h),l),n),p),q),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Cp(d.messageContext.eventId,
r,"1");var x=d.type,y=d.C.id;if(lk&&x==="config"){var A,D=(A=po(y))==null?void 0:A.ids;if(!(D&&D.length>1)){var E,J=Dc("google_tag_data",{});J.td||(J.td={});E=J.td;var F=td(t.R);td(t.C,F);var M=[],T;for(T in E)E.hasOwnProperty(T)&&Co(E[T],F).length&&M.push(T);M.length&&(Ao(y,M),kb("TAGGING",wo[z.readyState]||14));E[y]=F}}f(d.C.id,b,d.H,t)}catch(ha){Cp(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():Fl(e.wa,v)}}};
Op.prototype.register=function(a,b,c,d){var e=Jp(this,a);e.status!==3&&(e.ja=b,e.status=3,e.wa=Hl(c),Lp(this,a,d||{}),this.flush())};
Op.prototype.push=function(a,b,c,d){c!==void 0&&(Jp(this,c).status===1&&(Jp(this,c).status=2,this.push("require",[{}],c,{})),Jp(this,c).U&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[R.A.Qf]||(d.eventMetadata[R.A.Qf]=[c.destinationId]),d.eventMetadata[R.A.Li]||(d.eventMetadata[R.A.Li]=[c.id]));this.commands.push(new Np(a,c,b,d));d.deferrable||this.flush()};
Op.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Oc:void 0,sh:void 0,Xi:void 0,Yi:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Jp(this,g).U?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Jp(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];yb(h,function(t,v){td(Nb(t,v),b.C)});Ko(h,!0);break;case "config":var l=
Jp(this,g);e.Oc={};yb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.Oc)}}(e));var n=!!e.Oc[N.m.ud];delete e.Oc[N.m.ud];var p=g.destinationId===g.id;Ko(e.Oc,!0);n||(p?l.H={}:l.C[g.id]={});l.U&&n||Pp(this,N.m.ma,e.Oc,f);l.U=!0;p?td(e.Oc,l.H):(td(e.Oc,l.C[g.id]),P(70));d=!0;break;case "event":e.sh={};yb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.sh)}}(e));Ko(e.sh);Pp(this,f.args[1],e.sh,f);break;case "get":var q={},r=(q[N.m.kf]=f.args[0],q[N.m.jf]=f.args[1],q);Pp(this,N.m.nc,r,
f);break;case "container_config":e.Xi={};yb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.Xi)}}(e));Jp(this,g).H=e.Xi;break;case "destination_config":var u=Jp(this,g);e.Yi={};yb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.Yi)}}(e));u.C[g.id]||(u.C[g.id]={});u.C[g.id]=e.Yi}this.commands.shift();Qp(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};
var Qp=function(a,b){if(b.type!=="require")if(b.C)for(var c=Jp(a,b.C).R[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.R)for(var g=f.R[b.type]||[],h=0;h<g.length;h++)g[h]()}},Lp=function(a,b,c){var d=td(c,null);td(Jp(a,b).M,d);Jp(a,b).M=d},Fp=new Op;function Rp(a){var b=a.location.href;if(a===a.top)return{url:b,Dq:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1],g;f&&((g=b)==null?void 0:g.indexOf(f))===-1&&(c=!1,b=f)}return{url:b,Dq:c}}function Sp(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Hk(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}}function Tp(){for(var a=w,b=a;a&&a!=a.parent;)a=a.parent,Sp(a)&&(b=a);return b};var Up=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},Vp=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};function Wp(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)};var Xp=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Yp=function(a){var b=w;if(b.top==b)return 0;if(a===void 0?0:a){var c=b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Sp(b.top)?1:2},Zp=function(a){a=a===void 0?document:a;return a.createElement("img")};function $p(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function aq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function bq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Zp(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=wc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}aq(e,"load",f);aq(e,"error",f)};$p(e,"load",f);$p(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function cq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Wp(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});dq(c,b)}
function dq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else bq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var eq=function(){this.ja=this.ja;this.R=this.R};eq.prototype.ja=!1;eq.prototype.dispose=function(){this.ja||(this.ja=!0,this.M())};eq.prototype[ma.Symbol.dispose]=function(){this.dispose()};eq.prototype.addOnDisposeCallback=function(a,b){this.ja?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};eq.prototype.M=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function fq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var gq=function(a,b){b=b===void 0?{}:b;eq.call(this);this.C=null;this.wa={};this.Lc=0;this.U=null;this.H=a;var c;this.yb=(c=b.timeoutMs)!=null?c:500;var d;this.Ya=(d=b.zs)!=null?d:!1};za(gq,eq);gq.prototype.M=function(){this.wa={};this.U&&(aq(this.H,"message",this.U),delete this.U);delete this.wa;delete this.H;delete this.C;eq.prototype.M.call(this)};var iq=function(a){return typeof a.H.__tcfapi==="function"||hq(a)!=null};
gq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ya},d=Vp(function(){return a(c)}),e=0;this.yb!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.yb));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=fq(c),c.internalBlockOnErrors=b.Ya,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{jq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};gq.prototype.removeEventListener=function(a){a&&a.listenerId&&jq(this,"removeEventListener",null,a.listenerId)};
var lq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var l;if(h===0)if(a.purpose&&a.vendor){var n=kq(a.vendor.consents,d===void 0?"755":d);l=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&kq(a.purpose.consents,b)}else l=!0;else l=h===1?a.purpose&&a.vendor?kq(a.purpose.legitimateInterests,
b)&&kq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return l},kq=function(a,b){return!(!a||!a[b])},jq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(hq(a)){mq(a);var g=++a.Lc;a.wa[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},hq=function(a){if(a.C)return a.C;a.C=Xp(a.H,"__tcfapiLocator");return a.C},mq=function(a){if(!a.U){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.wa[d.callId](d.returnValue,d.success)}catch(e){}};a.U=b;$p(a.H,"message",b)}},nq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=fq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(cq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var oq={1:0,3:0,4:0,7:3,9:3,10:3};Gg(32);function pq(){return Un("tcf",function(){return{}})}var qq=function(){return new gq(w,{timeoutMs:-1})};
function rq(){var a=pq(),b=qq();iq(b)&&!sq()&&!tq()&&P(124);if(!a.active&&iq(b)){sq()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,il().active=!0,a.tcString="tcunavailable");Mn();try{b.addEventListener(function(c){if(c.internalErrorState!==0)uq(a),Nn([N.m.aa,N.m.La,N.m.W]),il().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,tq()&&(a.active=!0),!vq(c)||sq()||tq()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in oq)oq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(vq(c)){var g={},h;for(h in oq)if(oq.hasOwnProperty(h))if(h==="1"){var l,n=c,p={fq:!0};p=p===void 0?{}:p;l=nq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.fq)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?lq(n,"1",0):!0:!1;g["1"]=l}else g[h]=lq(c,h,oq[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[N.m.aa]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(Nn([N.m.aa,N.m.La,N.m.W]),il().active=!0):(r[N.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[N.m.W]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":Nn([N.m.W]),vn(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:wq()||""}))}}else Nn([N.m.aa,N.m.La,N.m.W])})}catch(c){uq(a),Nn([N.m.aa,N.m.La,N.m.W]),il().active=!0}}}
function uq(a){a.type="e";a.tcString="tcunavailable"}function vq(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function sq(){return w.gtag_enable_tcf_support===!0}function tq(){return pq().enableAdvertiserConsentMode===!0}function wq(){var a=pq();if(a.active)return a.tcString}function xq(){var a=pq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function yq(a){if(!oq.hasOwnProperty(String(a)))return!0;var b=pq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var zq=[N.m.aa,N.m.ka,N.m.W,N.m.La],Aq={},Bq=(Aq[N.m.aa]=1,Aq[N.m.ka]=2,Aq);function Cq(a){if(a===void 0)return 0;switch(Q(a,N.m.Ob)){case void 0:return 1;case !1:return 3;default:return 2}}function Dq(){return(H(183)?Gg(16).split("~"):Gg(17).split("~")).indexOf(vm())!==-1&&zc.globalPrivacyControl===!0}function Eq(a){if(Dq())return!1;var b=Cq(a);if(b===3)return!1;switch(rl(N.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function Fq(){return tl()||!ql(N.m.aa)||!ql(N.m.ka)}function Gq(){var a={},b;for(b in Bq)Bq.hasOwnProperty(b)&&(a[Bq[b]]=rl(b));return"G1"+jf(a[1]||0)+jf(a[2]||0)}var Hq={},Iq=(Hq[N.m.aa]=0,Hq[N.m.ka]=1,Hq[N.m.W]=2,Hq[N.m.La]=3,Hq);function Jq(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Kq(a){for(var b="1",c=0;c<zq.length;c++){var d=b,e,f=zq[c],g=pl.delegatedConsentTypes[f];e=g===void 0?0:Iq.hasOwnProperty(g)?12|Iq[g]:8;var h=il();h.accessedAny=!0;var l=h.entries[f]||{};e=e<<2|Jq(l.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Jq(l.declare)<<4|Jq(l.default)<<2|Jq(l.update)])}var n=b,p=(Dq()?1:0)<<3,q=(tl()?1:0)<<2,r=Cq(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[pl.containerScopedDefaults.ad_storage<<4|pl.containerScopedDefaults.analytics_storage<<2|pl.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(pl.usedContainerScopedDefaults?1:0)<<2|pl.containerScopedDefaults.ad_personalization]}
function Lq(){if(!ql(N.m.W))return"-";for(var a=Object.keys(Nm),b={},c=m(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=pl.corePlatformServices[e]!==!1}for(var f="",g=m(a),h=g.next();!h.done;h=g.next()){var l=h.value;b[l]&&(f+=Nm[l])}(pl.usedCorePlatformServices?pl.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Mq(){return xm()||(sq()||tq())&&xq()==="1"?"1":"0"}function Nq(){return(xm()?!0:!(!sq()&&!tq())&&xq()==="1")||!ql(N.m.W)}
function Oq(){var a="0",b="0",c;var d=pq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=pq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;xm()&&(h|=1);xq()==="1"&&(h|=2);sq()&&(h|=4);var l;var n=pq();l=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;l==="1"&&(h|=8);il().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Pq(){return vm()==="US-CO"};var Qq={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Rq(a){a=a===void 0?{}:a;var b=C(5).split("-")[0].toUpperCase(),c,d={ctid:C(5),nn:Fg(15),qn:C(14),Fq:Cg(7)?2:1,mr:a.sn,canonicalId:C(6),Zq:(c=Lj())==null?void 0:c.canonicalContainerId,nr:a.Qd===void 0?void 0:a.Qd?10:12};d.canonicalId!==a.Oa&&(d.Oa=a.Oa);var e=Ij();d.Lq=e?e.canonicalContainerId:void 0;Xi?(d.Gh=Qq[b],d.Gh||(d.Gh=0)):d.Gh=Yi?13:10;Eg.H?(d.Wm=0,d.Ep=2):d.Wm=Eg.C?1:3;var f={6:!1};Eg.M===2?f[7]=!0:Eg.M===1&&(f[2]=!0);if(Cc){var g=mj(sj(Cc),"host");g&&(f[8]=g.match(/^(www\.)?googletagmanager\.com$/)===
null)}d.Kp=f;return mf(d,a.qh)};function Sq(a,b,c,d){var e,f=Number(a.Sc!=null?a.Sc:void 0);f!==0&&(e=new Date((b||Fb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,wc:d}};var Tq=["ad_storage","ad_user_data"];function Uq(a,b){if(!a)return kb("TAGGING",32),10;if(b===null||b===void 0||b==="")return kb("TAGGING",33),11;var c=Vq(!1);if(c.error!==0)return kb("TAGGING",34),c.error;if(!c.value)return kb("TAGGING",35),2;c.value[a]=b;var d=Wq(c);d!==0&&kb("TAGGING",36);return d}
function Xq(a){if(!a)return kb("TAGGING",27),{error:10};var b=Vq();if(b.error!==0)return kb("TAGGING",29),b;if(!b.value)return kb("TAGGING",30),{error:2};if(!(a in b.value))return kb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(kb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Vq(a){a=a===void 0?!0:a;if(!ql(Tq))return kb("TAGGING",43),{error:3};try{if(!w.localStorage)return kb("TAGGING",44),{error:1}}catch(f){return kb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=w.localStorage.getItem("_gcl_ls")}catch(f){return kb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return kb("TAGGING",47),{error:12}}}catch(f){return kb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return kb("TAGGING",49),{error:4};
if(b.version!==1)return kb("TAGGING",50),{error:5};try{var e=Yq(b);a&&e&&Wq({value:b,error:0})}catch(f){return kb("TAGGING",48),{error:8}}return{value:b,error:0}}
function Yq(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,kb("TAGGING",54),!0}else{for(var c=!1,d=m(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Yq(a[e.value])||c;return c}return!1}
function Wq(a){if(a.error)return a.error;if(!a.value)return kb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return kb("TAGGING",52),6}try{w.localStorage.setItem("_gcl_ls",c)}catch(d){return kb("TAGGING",53),7}return 0};var Zq={lg:"value",fb:"conversionCount",Ch:1},$q={zh:9,Fh:10,lg:"timeouts",fb:"timeouts",Ch:0},ar=[Zq,$q,{zh:11,Fh:12,lg:"eopCount",fb:"endOfPageCount",Ch:0},{zh:11,Fh:12,lg:"errors",fb:"errors",Ch:0}];function br(a){var b;b=b===void 0?1:b;if(!cr(a))return{};var c=dr(ar),d=c[a.fb];if(d===void 0||d===-1)return c;var e={},f=oa(Object,"assign").call(Object,{},c,(e[a.fb]=d+b,e));return er(f)?f:c}
function dr(a){var b;a:{var c=Xq("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=m(a),h=g.next();!h.done;h=g.next()){var l=h.value;if(e&&cr(l)){var n=e[l.lg];n===void 0||Number.isNaN(n)?f[l.fb]=-1:f[l.fb]=Number(n)}else f[l.fb]=-1}return f}
function fr(){for(var a=br(Zq),b=[],c=m(ar),d=c.next();!d.done;d=c.next()){var e=d.value,f=a[e.fb];if(f===void 0||f<e.Ch)break;b.push(f.toString())}return b.join("~")}function er(a,b){b=b||{};for(var c=Fb(),d=Sq(b,c,!0),e={},f=m(ar),g=f.next();!g.done;g=f.next()){var h=g.value,l=a[h.fb];l!==void 0&&l!==-1&&(e[h.lg]=l)}e.creationTimeMs=c;return Uq("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function cr(a){return ql(["ad_storage","ad_user_data"])?!a.Fh||Xa(a.Fh):!1}
function gr(a){return ql(["ad_storage","ad_user_data"])?!a.zh||Xa(a.zh):!1};var hr={O:{lp:0,Oj:1,xg:2,dk:3,Kh:4,Zj:5,bk:6,ek:7,Lh:8,yl:9,xl:10,ni:11,zl:12,Yg:13,Il:14,Of:15,kp:16,ze:17,Qi:18,Ri:19,Si:20,Bm:21,Ti:22,Nh:23,nk:24}};hr.O[hr.O.lp]="RESERVED_ZERO";hr.O[hr.O.Oj]="ADS_CONVERSION_HIT";hr.O[hr.O.xg]="CONTAINER_EXECUTE_START";hr.O[hr.O.dk]="CONTAINER_SETUP_END";hr.O[hr.O.Kh]="CONTAINER_SETUP_START";hr.O[hr.O.Zj]="CONTAINER_BLOCKING_END";hr.O[hr.O.bk]="CONTAINER_EXECUTE_END";hr.O[hr.O.ek]="CONTAINER_YIELD_END";hr.O[hr.O.Lh]="CONTAINER_YIELD_START";hr.O[hr.O.yl]="EVENT_EXECUTE_END";
hr.O[hr.O.xl]="EVENT_EVALUATION_END";hr.O[hr.O.ni]="EVENT_EVALUATION_START";hr.O[hr.O.zl]="EVENT_SETUP_END";hr.O[hr.O.Yg]="EVENT_SETUP_START";hr.O[hr.O.Il]="GA4_CONVERSION_HIT";hr.O[hr.O.Of]="PAGE_LOAD";hr.O[hr.O.kp]="PAGEVIEW";hr.O[hr.O.ze]="SNIPPET_LOAD";hr.O[hr.O.Qi]="TAG_CALLBACK_ERROR";hr.O[hr.O.Ri]="TAG_CALLBACK_FAILURE";hr.O[hr.O.Si]="TAG_CALLBACK_SUCCESS";hr.O[hr.O.Bm]="TAG_EXECUTE_END";hr.O[hr.O.Ti]="TAG_EXECUTE_START";hr.O[hr.O.Nh]="CUSTOM_PERFORMANCE_START";hr.O[hr.O.nk]="CUSTOM_PERFORMANCE_END";var ir=[],jr={},kr={};function lr(a){if(Xa(19)&&ir.includes(a)){var b;(b=hd())==null||b.mark(a+"-"+hr.O.Nh+"-"+(kr[a]||0))}}function mr(a){if(Xa(19)&&ir.includes(a)){var b=a+"-"+hr.O.nk+"-"+(kr[a]||0),c={start:a+"-"+hr.O.Nh+"-"+(kr[a]||0),end:b},d;(d=hd())==null||d.mark(b);var e,f,g=(f=(e=hd())==null?void 0:e.measure(b,c))==null?void 0:f.duration;g!==void 0&&(kr[a]=(kr[a]||0)+1,jr[a]=g+(jr[a]||0))}};var nr=["2","3"];function or(a){return a.origin!=="null"};function pr(a,b){var c=Nl(Il.X.Ei,Pa()).get(a);if(c&&(!c.expires||(typeof c.expires==="string"?(new Date(c.expires)).getTime():c.expires.getTime())>=Date.now())&&c.value!==void 0)return b?decodeURIComponent(c.value):c.value}function qr(a,b,c){var d=Nl(Il.X.Ei,Pa());d.set(a,{expires:c,value:b});Ll(Il.X.Ei,d)}var rr=0,sr=0;
function tr(a,b,c,d,e){try{lr("3");var f;Xa(20)&&!e&&(f=pr(a,c));var g,h=(g=ur(function(l){return l===a},b,c,d)[a])!=null?g:[];f!==void 0&&(h.includes(f)?sr++:rr++);return h}finally{mr("3")}}function ur(a,b,c,d){var e;if(vr(d)){for(var f={},g=String(b||wr()).split(";"),h=0;h<g.length;h++){var l=g[h].split("="),n=l[0].trim();if(n&&a(n)){var p=l.slice(1).join("=").trim();p&&c&&(p=decodeURIComponent(p));var q=void 0,r=void 0;((q=f)[r=n]||(q[r]=[])).push(p)}}e=f}else e={};return e}
function xr(a,b,c,d,e){if(vr(e)){var f=yr(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=zr(f,function(g){return g.Up},b);if(f.length===1)return f[0];f=zr(f,function(g){return g.Nq},c);return f[0]}}}function Ar(a,b,c,d){var e=wr(),f=window;or(f)&&(f.document.cookie=a);var g=wr();return e!==g||c!==void 0&&tr(b,g,!1,d,!0).indexOf(c)>=0}
function Br(a,b,c,d){function e(x,y,A){if(A==null)return delete h[y],x;h[y]=A;return x+"; "+y+"="+A}function f(x,y){if(y==null)return x;h[y]=!0;return x+"; "+y}if(!vr(c.wc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Cr(b),g=a+"="+b);var h={};Xa(20)&&qr(a,b,c.xj?new Date(Date.now()+Number(c.xj)*1E3):c.expires);g=e(g,"path",c.path);var l;c.expires instanceof Date?l=c.expires.toUTCString():c.expires!=null&&(l=""+c.expires);g=e(g,
"expires",l);g=e(g,"max-age",c.xj);g=e(g,"samesite",c.ar);c.secure&&(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Dr(),q=void 0,r=!1,u=0;u<p.length;++u){var t=p[u]!=="none"?p[u]:void 0,v=e(g,"domain",t);v=f(v,c.flags);try{d&&d(a,h)}catch(x){q=x;continue}r=!0;if(!Er(t,c.path)&&Ar(v,a,b,c.wc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Er(n,c.path)?1:Ar(g,a,b,c.wc)?0:1}
function Fr(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");lr("2");var d=Br(a,b,c);mr("2");return d}function zr(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],l=b(h);l===c?d.push(h):f===void 0||l<f?(e=[h],f=l):l===f&&e.push(h)}return d.length>0?d:e}
function yr(a,b,c){for(var d=[],e=tr(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var l=g.shift();if(l){var n=l.split("-");d.push({Mp:e[f],Np:g.join("."),Up:Number(n[0])||1,Nq:Number(n[1])||1})}}}return d}function Cr(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}var Gr=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Hr=/(^|\.)doubleclick\.net$/i;
function Er(a,b){return a!==void 0&&(Hr.test(window.document.location.hostname)||b==="/"&&Gr.test(a))}function Ir(a){if(!a)return 1;var b=a;Xa(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Jr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}function Kr(a,b){var c=""+Ir(a),d=Jr(b);d>1&&(c+="-"+d);return c}
var wr=function(){return or(window)?window.document.cookie:""},vr=function(a){return a&&Xa(7)?(Array.isArray(a)?a:[a]).every(function(b){return sl(b)&&ql(b)}):!0},Dr=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;Hr.test(e)||Gr.test(e)||a.push("none");return a};function Lr(a){var b=Math.round(Math.random()*2147483647);return a?String(b^ai(a)&2147483647):String(b)}function Mr(a){return[Lr(a),Math.round(Fb()/1E3)].join(".")}function Nr(a,b,c,d,e){var f=Ir(b),g;return(g=xr(a,f,Jr(c),d,e))==null?void 0:g.Np};var Or;function Pr(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Qr,d=Rr,e=Sr();if(!e.init){Qc(z,"mousedown",a);Qc(z,"keyup",a);Qc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Tr(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Sr().decorators.push(f)}
function Ur(a,b,c){for(var d=Sr().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var l=g.domains,n=a,p=!!g.sameHost;if(l&&(p||n!==z.location.hostname))for(var q=0;q<l.length;q++)if(l[q]instanceof RegExp){if(l[q].test(n)){h=!0;break a}}else if(n.indexOf(l[q])>=0||p&&l[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Ib(e,g.callback())}}return e}
function Sr(){var a=Dc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Vr=/(.*?)\*(.*?)\*(.*)/,Wr=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Xr=/^(?:www\.|m\.|amp\.)+/,Yr=/([^?#]+)(\?[^#]*)?(#.*)?/;function Zr(a){var b=Yr.exec(a);if(b)return{Bj:b[1],query:b[2],fragment:b[3]}}function $r(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function as(a,b){var c=[zc.userAgent,(new Date).getTimezoneOffset(),zc.userLanguage||zc.language,Math.floor(Fb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Or)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Or=d;for(var l=4294967295,n=0;n<c.length;n++)l=l>>>8^Or[(l^c.charCodeAt(n))&255];return((l^-1)>>>0).toString(36)}
function bs(a){return function(b){var c=sj(w.location.href),d=c.search.replace("?",""),e=jj(d,"_gl",!1,!0)||"";b.query=cs(e)||{};var f=mj(c,"fragment"),g;var h=-1;if(Kb(f,"_gl="))h=4;else{var l=f.indexOf("&_gl=");l>0&&(h=l+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=cs(g||"")||{};a&&ds(c,d,f)}}function es(a,b){var c=$r(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function ds(a,b,c){function d(g,h){var l=es("_gl",g);l.length&&(l=h+l);return l}if(yc&&yc.replaceState){var e=$r("_gl");if(e.test(b)||e.test(c)){var f=mj(a,"path");b=d(b,"?");c=d(c,"#");yc.replaceState({},"",""+f+b+c)}}}function fs(a,b){var c=bs(!!b),d=Sr();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Ib(e,f.query),a&&Ib(e,f.fragment));return e}
var cs=function(a){try{var b=gs(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=ib(d[e+1]);c[f]=g}kb("TAGGING",6);return c}}catch(h){kb("TAGGING",8)}};function gs(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Vr.exec(d);if(f){c=f;break a}d=lj(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],l;a:{for(var n=g[2],p=0;p<b;++p)if(n===as(h,p)){l=!0;break a}l=!1}if(l)return h;kb("TAGGING",7)}}}
function hs(a,b,c,d,e){function f(p){p=es(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Zr(c);if(!g)return"";var h=g.query||"",l=g.fragment||"",n=a+"="+b;d?l.substring(1).length!==0&&e||(l="#"+f(l.substring(1))):h="?"+f(h.substring(1));return""+g.Bj+h+l}
function is(a,b){function c(n,p,q){var r;a:{for(var u in n)if(n.hasOwnProperty(u)){r=!0;break a}r=!1}if(r){var t,v=[],x;for(x in n)if(n.hasOwnProperty(x)){var y=n[x];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(x),v.push(hb(String(y))))}var A=v.join("*");t=["1",as(A),A].join("*");d?(Xa(3)||Xa(1)||!p)&&js("_gl",t,a,p,q):ks("_gl",t,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Ur(b,1,d),f=Ur(b,2,d),g=Ur(b,4,d),h=Ur(b,3,d);c(e,!1,!1);c(f,!0,!1);Xa(1)&&c(g,!0,!0);for(var l in h)h.hasOwnProperty(l)&&
ls(l,h[l],a)}function ls(a,b,c){c.tagName.toLowerCase()==="a"?ks(a,b,c):c.tagName.toLowerCase()==="form"&&js(a,b,c)}function ks(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Xa(4)||d)){var h=w.location.href,l=Zr(c.href),n=Zr(h);g=!(l&&n&&l.Bj===n.Bj&&l.query===n.query&&l.fragment)}f=g}if(f){var p=hs(a,b,c.href,d,e);oc.test(p)&&(c.href=p)}}
function js(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=hs(a,b,f,d,e);oc.test(h)&&(c.action=h)}}else{for(var l=c.childNodes||[],n=!1,p=0;p<l.length;p++){var q=l[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Qr(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||is(e,e.hostname)}}catch(g){}}function Rr(a){try{var b=a.getAttribute("action");if(b){var c=mj(sj(b),"host");is(a,c)}}catch(d){}}function ms(a,b,c,d){Pr();var e=c==="fragment"?2:1;d=!!d;Tr(a,b,e,d,!1);e===2&&kb("TAGGING",23);d&&kb("TAGGING",24)}
function ns(a,b){Pr();Tr(a,[oj(w.location,"host",!0)],b,!0,!0)}function os(){var a=z.location.hostname,b=Wr.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?lj(f[2])||"":lj(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Xr,""),l=e.replace(Xr,""),n;if(!(n=h===l)){var p="."+l;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ps(a,b){return a===!1?!1:a||b||os()};var qs=["1"],rs={},ss={};function ts(a,b){b=b===void 0?!0:b;var c=us(a.prefix);if(rs[c])vs(a);else if(ws(c,a.path,a.domain)){var d=ss[us(a.prefix)]||{id:void 0,Bh:void 0};b&&xs(a,d.id,d.Bh);vs(a)}else{var e=uj("auiddc");if(e)kb("TAGGING",17),rs[c]=e;else if(b){var f=us(a.prefix),g=Mr();ys(f,g,a);ws(c,a.path,a.domain);vs(a,!0)}}}
function vs(a,b){if((b===void 0?0:b)&&cr(Zq)){var c=Vq(!1);c.error!==0?kb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Wq(c)!==0&&kb("TAGGING",41)):kb("TAGGING",40):kb("TAGGING",39)}if(gr(Zq)&&dr([Zq])[Zq.fb]===-1){for(var d={},e=(d[Zq.fb]=0,d),f=m(ar),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Zq&&gr(h)&&(e[h.fb]=0)}er(e,a)}}
function xs(a,b,c){var d=us(a.prefix),e=rs[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Fb()/1E3)));ys(d,h,a,g*1E3)}}}}function ys(a,b,c,d){var e;e=["1",Kr(c.domain,c.path),b].join(".");var f=Sq(c,d);f.wc=zs();Fr(a,e,f)}function ws(a,b,c){var d=Nr(a,b,c,qs,zs());if(!d)return!1;As(a,d);return!0}
function As(a,b){var c=b.split(".");c.length===5?(rs[a]=c.slice(0,2).join("."),ss[a]={id:c.slice(2,4).join("."),Bh:Number(c[4])||0}):c.length===3?ss[a]={id:c.slice(0,2).join("."),Bh:Number(c[2])||0}:rs[a]=b}function us(a){return(a||"_gcl")+"_au"}function Bs(a){function b(){ql(c)&&a()}var c=zs();wl(function(){b();ql(c)||xl(b,c)},c)}
function Cs(a){var b=fs(!0),c=us(a.prefix);Bs(function(){var d=b[c];if(d){As(c,d);var e=Number(rs[c].split(".")[1])*1E3;if(e){kb("TAGGING",16);var f=Sq(a,e);f.wc=zs();var g=["1",Kr(a.domain,a.path),d].join(".");Fr(c,g,f)}}})}function Ds(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Nr(a,e.path,e.domain,qs,zs());h&&(g[a]=h);return g};Bs(function(){ms(f,b,c,d)})}function zs(){return["ad_storage","ad_user_data"]};function Es(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Lj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Fs(a,b){var c=Es(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Lj]||(d[c[e].Lj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Lj].push(g)}}return d};var Gs={},Hs=(Gs.k={fa:/^[\w-]+$/},Gs.b={fa:/^[\w-]+$/,Ej:!0},Gs.i={fa:/^[1-9]\d*$/},Gs.h={fa:/^\d+$/},Gs.t={fa:/^[1-9]\d*$/},Gs.d={fa:/^[A-Za-z0-9_-]+$/},Gs.j={fa:/^\d+$/},Gs.u={fa:/^[1-9]\d*$/},Gs.l={fa:/^[01]$/},Gs.o={fa:/^[1-9]\d*$/},Gs.g={fa:/^[01]$/},Gs.s={fa:/^.+$/},Gs);var Is={},Ms=(Is[5]={Hh:{2:Js},uj:"2",rh:["k","i","b","u"]},Is[4]={Hh:{2:Js,GCL:Ks},uj:"2",rh:["k","i","b"]},Is[2]={Hh:{GS2:Js,GS1:Ls},uj:"GS2",rh:"sogtjlhd".split("")},Is);function Ns(a,b,c){var d=Ms[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Hh[e];if(f)return f(a,b)}}}
function Js(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(u){}var e={},f=Ms[b];if(f){for(var g=f.rh,h=m(d.split("$")),l=h.next();!l.done;l=h.next()){var n=l.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Hs[p];r&&(r.Ej?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(u){}}return e}}}function Os(a,b,c){var d=Ms[b];if(d)return[d.uj,c||"1",Ps(a,b)].join(".")}
function Ps(a,b){var c=Ms[b];if(c){for(var d=[],e=m(c.rh),f=e.next();!f.done;f=e.next()){var g=f.value,h=Hs[g];if(h){var l=a[g];if(l!==void 0)if(h.Ej&&Array.isArray(l))for(var n=m(l),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+l))}}return d.join("$")}}function Ks(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Ls(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Qs=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Rs(a,b,c){if(Ms[b]){for(var d=[],e=tr(a,void 0,void 0,Qs.get(b)),f=m(e),g=f.next();!g.done;g=f.next()){var h=Ns(g.value,b,c);h&&d.push(Ss(h))}return d}}
function Ts(a){var b=Us;if(Ms[2]){for(var c={},d=ur(a,void 0,void 0,Qs.get(2)),e=Object.keys(d).sort(),f=m(e),g=f.next();!g.done;g=f.next())for(var h=g.value,l=m(d[h]),n=l.next();!n.done;n=l.next()){var p=Ns(n.value,2,b);p&&(c[h]||(c[h]=[]),c[h].push(Ss(p)))}return c}}function Vs(a,b,c,d,e){d=d||{};var f=Kr(d.domain,d.path),g=Os(b,c,f);if(!g)return 1;var h=Sq(d,e,void 0,Qs.get(c));return Fr(a,g,h)}function Ws(a,b){var c=b.fa;return typeof c==="function"?c(a):c.test(a)}
function Ss(a){for(var b=m(Object.keys(a)),c=b.next(),d={};!c.done;d={Vf:void 0},c=b.next()){var e=c.value,f=a[e];d.Vf=Hs[e];d.Vf?d.Vf.Ej?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Ws(h,g.Vf)}}(d)):void 0:typeof f==="string"&&Ws(f,d.Vf)||(a[e]=void 0):a[e]=void 0}return a};var Xs=function(){this.value=0};Xs.prototype.set=function(a){return this.value|=1<<a};var Ys=function(a,b){b<=0||(a.value|=1<<b-1)};Xs.prototype.get=function(){return this.value};Xs.prototype.clear=function(a){this.value&=~(1<<a)};Xs.prototype.clearAll=function(){this.value=0};Xs.prototype.equals=function(a){return this.value===a.value};function Zs(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function $s(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function at(){var a=String,b=w.location.hostname,c=w.location.pathname,d=b=Tb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Tb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(ai((""+b+e).toLowerCase()))};var bt={},ct=(bt.gclid=!0,bt.dclid=!0,bt.gbraid=!0,bt.wbraid=!0,bt),dt=/^\w+$/,et=/^[\w-]+$/,ft={},gt=(ft.aw="_aw",ft.dc="_dc",ft.gf="_gf",ft.gp="_gp",ft.gs="_gs",ft.ha="_ha",ft.ag="_ag",ft.gb="_gb",ft),ht=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,it=/^www\.googleadservices\.com$/;function jt(){return["ad_storage","ad_user_data"]}function kt(a){return!Xa(7)||ql(a)}function lt(a,b){function c(){var d=kt(b);d&&a();return d}wl(function(){c()||xl(c,b)},b)}
function mt(a){return nt(a).map(function(b){return b.gclid})}function ot(a){return pt(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function pt(a){var b=qt(a.prefix),c=rt("gb",b),d=rt("ag",b);if(!d||!c)return[];var e=function(h){return function(l){l.type=h;return l}},f=nt(c).map(e("gb")),g=st(d).map(e("ag"));return f.concat(g).sort(function(h,l){return l.timestamp-h.timestamp})}
function tt(a,b,c,d,e){var f=ub(a,function(g){return g.gclid===b});f?(f.timestamp<c&&(f.timestamp=c,f.Rc=e),f.labels=ut(f.labels||[],d||[])):a.push({version:"2",gclid:b,timestamp:c,labels:d,Rc:e})}function st(a){for(var b=Rs(a,5)||[],c=[],d=m(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=vt(f);h&&tt(c,g.k,h,g.b||[],f.u)}return c.sort(function(l,n){return n.timestamp-l.timestamp})}
function nt(a){for(var b=[],c=tr(a,z.cookie,void 0,jt()),d=m(c),e=d.next();!e.done;e=d.next()){var f=wt(e.value);f!=null&&(f.Rc=void 0,f.Ca=new Xs,f.ab=[1],xt(b,f))}b.sort(function(g,h){return h.timestamp-g.timestamp});return zt(b)}function At(a,b){for(var c=[],d=m(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=m(b),h=g.next();!h.done;h=g.next()){var l=h.value;c.includes(l)||c.push(l)}return c}
function xt(a,b,c){c=c===void 0?!1:c;for(var d,e,f=m(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ca&&b.Ca&&h.Ca.equals(b.Ca)&&(e=h)}if(d){var l,n,p=(l=d.Ca)!=null?l:new Xs,q=(n=b.Ca)!=null?n:new Xs;p.value|=q.value;d.Ca=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Rc=b.Rc);d.labels=At(d.labels||[],b.labels||[]);d.ab=At(d.ab||[],b.ab||[])}else c&&e?oa(Object,"assign").call(Object,e,b):a.push(b)}
function Bt(a){if(!a)return new Xs;var b=new Xs;if(a===1)return Ys(b,2),Ys(b,3),b;Ys(b,a);return b}
function Ct(){var a=Xq("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(et))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Xs;typeof e==="number"?g=Bt(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ca:g,ab:[2]}}catch(h){return null}}
function Dt(){var a=Xq("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(et))return b;var f=new Xs,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ca:f,ab:[2]});return b},[])}catch(b){return null}}
function Et(a){for(var b=[],c=tr(a,z.cookie,void 0,jt()),d=m(c),e=d.next();!e.done;e=d.next()){var f=wt(e.value);f!=null&&(f.Rc=void 0,f.Ca=new Xs,f.ab=[1],xt(b,f))}var g=Ct();g&&(g.Rc=void 0,g.ab=g.ab||[2],xt(b,g));if(Xa(14)){var h=Dt();if(h)for(var l=m(h),n=l.next();!n.done;n=l.next()){var p=n.value;p.Rc=void 0;p.ab=p.ab||[2];xt(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return zt(b)}
function ut(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function qt(a){return a&&typeof a==="string"&&a.match(dt)?a:"_gcl"}function Ft(a,b){if(a){var c={value:a,Ca:new Xs};Ys(c.Ca,b);return c}}
function Gt(a,b,c){var d=sj(a),e=mj(d,"query",!1,void 0,"gclsrc"),f=Ft(mj(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=Ft(jj(g,"gclid",!1),3));e||(e=jj(g,"gclsrc",!1))}return f&&(e===void 0||e==="aw"||e==="aw.ds"||Xa(18)&&e==="aw.dv")?[f]:[]}
function Ht(a,b){var c=sj(a),d=mj(c,"query",!1,void 0,"gclid"),e=mj(c,"query",!1,void 0,"gclsrc"),f=mj(c,"query",!1,void 0,"wbraid");f=Rb(f);var g=mj(c,"query",!1,void 0,"gbraid"),h=mj(c,"query",!1,void 0,"gad_source"),l=mj(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||jj(n,"gclid",!1);e=e||jj(n,"gclsrc",!1);f=f||jj(n,"wbraid",!1);g=g||jj(n,"gbraid",!1);h=h||jj(n,"gad_source",!1)}return It(d,e,l,f,g,h)}function Jt(){return Ht(w.location.href,!0)}
function It(a,b,c,d,e,f){var g={},h=function(l,n){g[n]||(g[n]=[]);g[n].push(l)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(et))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "aw.dv":Xa(18)&&(h(a,"aw"),h(a,"dc"));break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&et.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&et.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&et.test(f)&&(g.gad_source=
f,h(f,"gs"));return g}function Kt(a){for(var b=Jt(),c=!0,d=m(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Ht(w.document.referrer,!1),b.gad_source=void 0);Lt(b,!1,a)}
function Mt(a){Kt(a);var b=Gt(w.location.href,!0,!1);b.length||(b=Gt(w.document.referrer,!1,!0));a=a||{};Nt(a);if(b.length){var c=b[0],d=Fb(),e=Sq(a,d,!0),f=jt(),g=function(){kt(f)&&e.expires!==void 0&&Uq("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ca.get()},expires:Number(e.expires)})};wl(function(){g();kt(f)||xl(g,f)},f)}}
function Nt(a){var b;if(b=Xa(15)){var c=Ot();b=ht.test(c)||it.test(c)||Pt()}if(b){var d;a:{for(var e=sj(w.location.href),f=kj(mj(e,"query")),g=m(Object.keys(f)),h=g.next();!h.done;h=g.next()){var l=h.value;if(!ct[l]){var n=f[l][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Zs(n),r;if(q)c:{var u=q;if(u&&u.length!==0){var t=0;try{for(var v=10;t<u.length&&!(v--<=0);){var x=$s(u,t);if(x===void 0)break;var y=m(x),A=y.next().value,D=y.next().value,E=A,J=D,F=E&7;if(E>>3===16382){if(F!==0)break;
var M=$s(u,J);if(M===void 0)break;r=m(M).next().value===1;break c}var T;d:{var ha=void 0,S=u,ca=J;switch(F){case 0:T=(ha=$s(S,ca))==null?void 0:ha[1];break d;case 1:T=ca+8;break d;case 2:var ta=$s(S,ca);if(ta===void 0)break;var ka=m(ta),ea=ka.next().value;T=ka.next().value+ea;break d;case 5:T=ca+4;break d}T=void 0}if(T===void 0||T>u.length||T<=t)break;t=T}}catch(la){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var X=d;X&&Qt(X,7,a)}}
function Qt(a,b,c){c=c||{};var d=Fb(),e=Sq(c,d,!0),f=jt(),g=function(){if(kt(f)&&e.expires!==void 0){var h=Dt()||[];xt(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ca:Bt(b)},!0);Uq("gcl_aw",h.map(function(l){return{value:{value:l.gclid,creationTimeMs:l.timestamp,linkDecorationSources:l.Ca?l.Ca.get():0},expires:Number(l.expires)}}))}};wl(function(){kt(f)?g():xl(g,f)},f)}
function Lt(a,b,c,d,e){c=c||{};e=e||[];var f=qt(c.prefix),g=d||Fb(),h=Math.round(g/1E3),l=jt(),n=!1,p=!1,q=function(){if(kt(l)){var r=Sq(c,g,!0);r.wc=l;for(var u=function(T,ha){var S=rt(T,f);S&&(Fr(S,ha,r),T!=="gb"&&(n=!0))},t=function(T){var ha=["GCL",h,T];e.length>0&&ha.push(e.join("."));return ha.join(".")},v=m(["aw","dc","gf","ha","gp"]),x=v.next();!x.done;x=v.next()){var y=x.value;a[y]&&u(y,t(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],D=rt("gb",f);!b&&nt(D).some(function(T){return T.gclid===A&&T.labels&&
T.labels.length>0})||u("gb",t(A))}}if(!p&&a.gbraid&&kt("ad_storage")&&(p=!0,!n)){var E=a.gbraid,J=rt("ag",f);if(b||!st(J).some(function(T){return T.gclid===E&&T.labels&&T.labels.length>0})){var F={},M=(F.k=E,F.i=""+h,F.b=e,F);Vs(J,M,5,c,g)}}Rt(a,f,g,c)};wl(function(){q();kt(l)||xl(q,l)},l)}
function Rt(a,b,c,d){if(a.gad_source!==void 0&&kt("ad_storage")){var e=gd();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=rt("gs",b);if(g){var h=Math.floor((Fb()-(fd()||0))/1E3),l,n=at(),p={};l=(p.k=f,p.i=""+h,p.u=n,p);Vs(g,l,5,d,c)}}}}
function St(a,b){var c=fs(!0);lt(function(){for(var d=qt(b.prefix),e=0;e<a.length;++e){var f=a[e];if(gt[f]!==void 0){var g=rt(f,d),h=c[g];if(h){var l=Math.min(Tt(h),Fb()),n;b:{for(var p=l,q=tr(g,z.cookie,void 0,jt()),r=0;r<q.length;++r)if(Tt(q[r])>p){n=!0;break b}n=!1}if(!n){var u=Sq(b,l,!0);u.wc=jt();Fr(g,h,u)}}}}Lt(It(c.gclid,c.gclsrc),!1,b)},jt())}
function Ut(a){var b=["ag"],c=fs(!0),d=qt(a.prefix);lt(function(){for(var e=0;e<b.length;++e){var f=rt(b[e],d);if(f){var g=c[f];if(g){var h=Ns(g,5);if(h){var l=vt(h);l||(l=Fb());var n;a:{for(var p=l,q=Rs(f,5),r=0;r<q.length;++r)if(vt(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(l/1E3);Vs(f,h,5,a,l)}}}}},["ad_storage"])}function rt(a,b){var c=gt[a];if(c!==void 0)return b+c}function Tt(a){return Vt(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function vt(a){return a?(Number(a.i)||0)*1E3:0}function wt(a){var b=Vt(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Vt(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!et.test(a[2])?[]:a}
function Wt(a,b,c,d,e){if(Array.isArray(b)&&or(w)){var f=qt(e),g=function(){for(var h={},l=0;l<a.length;++l){var n=rt(a[l],f);if(n){var p=tr(n,z.cookie,void 0,jt());p.length&&(h[n]=p.sort()[p.length-1])}}return h};lt(function(){ms(g,b,c,d)},jt())}}
function Xt(a,b,c,d){if(Array.isArray(a)&&or(w)){var e=["ag"],f=qt(d),g=function(){for(var h={},l=0;l<e.length;++l){var n=rt(e[l],f);if(!n)return{};var p=Rs(n,5);if(p.length){var q=p.sort(function(r,u){return vt(u)-vt(r)})[0];h[n]=Os(q,5)}}return h};lt(function(){ms(g,a,b,c)},["ad_storage"])}}function zt(a){return a.filter(function(b){return et.test(b.gclid)})}
function Yt(a,b){if(or(w)){for(var c=qt(b.prefix),d={},e=0;e<a.length;e++)gt[a[e]]&&(d[a[e]]=gt[a[e]]);lt(function(){yb(d,function(f,g){var h=tr(c+g,z.cookie,void 0,jt());h.sort(function(u,t){return Tt(t)-Tt(u)});if(h.length){var l=h[0],n=Tt(l),p=Vt(l.split(".")).length!==0?l.split(".").slice(3):[],q={},r;r=Vt(l.split(".")).length!==0?l.split(".")[2]:void 0;q[f]=[r];Lt(q,!0,b,n,p)}})},jt())}}
function Zt(a){var b=["ag"],c=["gbraid"];lt(function(){for(var d=qt(a.prefix),e=0;e<b.length;++e){var f=rt(b[e],d);if(!f)break;var g=Rs(f,5);if(g.length){var h=g.sort(function(q,r){return vt(r)-vt(q)})[0],l=vt(h),n=h.b,p={};p[c[e]]=h.k;Lt(p,!0,a,l,n)}}},["ad_storage"])}function $t(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function au(a){function b(h,l,n){n&&(h[l]=n)}if(tl()){var c=Jt(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:fs(!1)._gs);if($t(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ns(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ns(function(){return g},1)}}}function Pt(){var a=sj(w.location.href);return mj(a,"query",!1,void 0,"gad_source")}
function bu(a){if(!Xa(1))return null;var b=fs(!0).gad_source;if(b!=null)return w.location.hash="",b;if(Xa(2)){b=Pt();if(b!=null)return b;var c=Jt();if($t(c,a))return"0"}return null}function cu(a){var b=bu(a);b!=null&&ns(function(){var c={};return c.gad_source=b,c},4)}function du(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function eu(a,b,c,d){var e=[];c=c||{};if(!kt(jt()))return e;var f=nt(a),g=du(e,f,b);if(g.length&&!d)for(var h=m(g),l=h.next();!l.done;l=h.next()){var n=l.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Sq(c,p,!0);r.wc=jt();Fr(a,q,r)}return e}
function fu(a,b){var c=[];b=b||{};var d=pt(b),e=du(c,d,a);if(e.length)for(var f=m(e),g=f.next();!g.done;g=f.next()){var h=g.value,l=qt(b.prefix),n=rt(h.type,l);if(!n)break;var p=h,q=p.version,r=p.gclid,u=p.labels,t=p.timestamp,v=Math.round(t/1E3);if(h.type==="ag"){var x={},y=(x.k=r,x.i=""+v,x.b=(u||[]).concat([a]),x);Vs(n,y,5,b,t)}else if(h.type==="gb"){var A=[q,v,r].concat(u||[],[a]).join("."),D=Sq(b,t,!0);D.wc=jt();Fr(n,A,D)}}return c}
function gu(a,b){var c=qt(b),d=rt(a,c);if(!d)return 0;var e;e=a==="ag"?st(d):nt(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function hu(a){for(var b=0,c=m(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function iu(a){var b=Math.max(gu("aw",a),hu(kt(jt())?Fs():{})),c=Math.max(gu("gb",a),hu(kt(jt())?Fs("_gac_gb",!0):{}));c=Math.max(c,gu("ag",a));return c>b}
function Ot(){return z.referrer?mj(sj(z.referrer),"host"):""};function uu(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function vu(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function wu(){return["ad_storage","ad_user_data"]}function xu(a){if(H(38)&&!Ml(Il.X.gm)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{uu(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Ll(Il.X.gm,function(d){d.gclid&&Qt(d.gclid,5,a)}),vu(c)||P(178))})}catch(c){P(177)}};wl(function(){kt(wu())?b():xl(b,wu())},wu())}};var yu=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function zu(a){return a.data.action!=="gcl_transfer"?(P(173),!0):a.data.gadSource?a.data.gclid?!1:(P(181),!0):(P(180),!0)}
function Au(a,b){if(H(a)){if(Ml(Il.X.we))return P(176),Il.X.we;if(Ml(Il.X.im))return P(170),Il.X.we;var c=Tp();if(!c)P(171);else if(c.opener){var d=function(g){if(!yu.includes(g.origin))P(172);else if(!zu(g)){var h={gadSource:g.data.gadSource};H(229)&&(h.gclid=g.data.gclid);Ll(Il.X.we,h);a===200&&g.data.gclid&&Qt(String(g.data.gclid),6,b);var l;(l=g.stopImmediatePropagation)==null||l.call(g);aq(c,"message",d)}};if($p(c,"message",d)){Ll(Il.X.im,!0);for(var e=m(yu),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},
f.value);P(174);return Il.X.we}P(175)}}};function Ku(a){var b=Q(a.D,N.m.Gc),c=Q(a.D,N.m.Fc);b&&!c?(a.eventName!==N.m.ma&&a.eventName!==N.m.Yd&&P(131),a.isAborted=!0):!b&&c&&(P(132),a.isAborted=!0)}
function Lu(a){var b=xn(N.m.aa)?Tn.pscdl:"denied";b!=null&&W(a,N.m.Ig,b)}function Mu(a){var b=Yp(!0);W(a,N.m.Ec,b)}function Nu(a){Pq()&&W(a,N.m.je,1)}function Ou(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&lj(a.substring(0,b))===void 0;)b--;return lj(a.substring(0,b))||""}function Pu(a){Qu(a,mo.Gf.Jn,Q(a.D,N.m.ub))}function Qu(a,b,c){Eu(a,N.m.Jc)||W(a,N.m.Jc,{});Eu(a,N.m.Jc)[b]=c}function Ru(a){V(a,R.A.Rf,hl.Z.Ga)}
function Su(a){var b=a.D.getMergedValues(N.m.Dc);b&&a.mergeHitDataForKey(N.m.Dc,b)}function Tu(a,b){b=b===void 0?!1:b;var c=U(a,R.A.Qf),d=Uu(a,"custom_event_accept_rules",!1)&&!b;if(c){var e=c.indexOf(a.target.destinationId)>=0,f=!0;H(240)&&U(a,R.A.Rl)&&(f=U(a,R.A.Za)===Gj());e&&f?V(a,R.A.Ih,!0):(V(a,R.A.Ih,!1),d||(a.isAborted=!0));H(240)&&(a.hasBeenAccepted()?a.isAborted=!0:U(a,R.A.Ih)&&a.accept())}}
function Vu(a){lk&&(gm=!0,a.eventName===N.m.ma?mm(a.D,a.target.id):(U(a,R.A.Ne)||(jm[a.target.id]=!0),bo(U(a,R.A.Za))))}function Wu(a){};var Xu=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Yu=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Zu=/^\d+\.fls\.doubleclick\.net$/,$u=/;gac=([^;?]+)/,av=/;gacgb=([^;?]+)/;
function bv(a,b){if(Zu.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(Xu)?lj(c[1])||"":""}for(var d=[],e=m(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],l=a[g],n=0;n<l.length;n++)h.push(l[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function cv(a,b,c){for(var d=kt(jt())?Fs("_gac_gb",!0):{},e=[],f=!1,g=m(Object.keys(d)),h=g.next();!h.done;h=g.next()){var l=h.value,n=eu("_gac_gb_"+l,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(l+":"+n.join(","))}return{bq:f?e.join(";"):"",aq:bv(d,av)}}function dv(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Yu)?b[1]:void 0}
function ev(a){var b={},c,d,e;Zu.test(z.location.host)&&(c=dv("gclgs"),d=dv("gclst"),e=dv("gcllp"));if(c&&d&&e)b.Yf=c,b.wh=d,b.uh=e;else{var f=Fb(),g=st((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),l=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Rc});h.length>0&&l.length>0&&n.length>0&&(b.Yf=h.join("."),b.wh=l.join("."),b.uh=n.join("."))}return b}
function fv(a,b,c,d){d=d===void 0?!1:d;if(Zu.test(z.location.host)){var e=dv(c);if(e){if(d){var f=new Xs;Ys(f,2);Ys(f,3);return e.split(".").map(function(h){return{gclid:h,Ca:f,ab:[1]}})}return e.split(".").map(function(h){return{gclid:h,Ca:new Xs,ab:[1]}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Et(g):nt(g)}if(b==="wbraid")return nt((a||"_gcl")+"_gb");if(b==="braids")return pt({prefix:a})}return[]}function gv(a){return Zu.test(z.location.host)?!(dv("gclaw")||dv("gac")):iu(a)}
function hv(a,b,c){var d;d=c?fu(a,b):eu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function nv(){return Un("dedupe_gclid",function(){return Mr()})};function tv(a,b,c,d){var e=Mc(),f;if(e===1)a:{var g=C(3);g=g.toLowerCase();for(var h="https://"+g,l="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(l)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==w.location.protocol?a:b)+c};var Fv=[N.m.aa,N.m.W];var Kv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,Lv=/^www.googleadservices.com$/;function Mv(a){a||(a=Nv());return a.Br?!1:a.rq||a.sq||a.uq||a.tq||a.De||a.th||a.cq||a.Yb==="aw.ds"||H(235)&&a.Yb==="aw.dv"||a.iq?!0:!1}
function Nv(){var a={},b=fs(!0);a.Br=!!b._up;var c=Jt(),d=lu();a.rq=c.aw!==void 0;a.sq=c.dc!==void 0;a.uq=c.wbraid!==void 0;a.tq=c.gbraid!==void 0;a.Yb=typeof c.gclsrc==="string"?c.gclsrc:void 0;a.De=d.De;a.th=d.th;var e=z.referrer?mj(sj(z.referrer),"host"):"";a.iq=Kv.test(e);a.cq=Lv.test(e);return a};var lg;function Tv(){var a=data.permissions||{};lg=new rg(C(5),a)};var Uv=Number(Gg(57))||5,Vv=Number(Gg(58))||50,Wv=vb();
var Yv=function(a,b){a&&(Xv("sid",a.targetId,b),Xv("cc",a.clientCount,b),Xv("tl",a.totalLifeMs,b),Xv("hc",a.heartbeatCount,b),Xv("cl",a.clientLifeMs,b))},Xv=function(a,b,c){b!=null&&c.push(a+"="+b)},Zv=function(){var a=z.referrer;if(a){var b;return mj(sj(a),"host")===((b=w.location)==null?void 0:b.host)?1:2}return 0},$v="https://"+C(21)+"/a?",bw=function(){this.U=aw;this.M=0};bw.prototype.H=function(a,b,c,d){var e=Zv(),f,g=[];f=w===w.top&&e!==0&&b?
(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&Xv("si",a.ig,g);Xv("m",0,g);Xv("iss",f,g);Xv("if",c,g);Yv(b,g);d&&Xv("fm",encodeURIComponent(d.substring(0,Vv)),g);this.R(g);};bw.prototype.C=function(a,b,c,d,e){var f=[];Xv("m",1,f);Xv("s",a,f);Xv("po",Zv(),f);b&&(Xv("st",b.state,f),Xv("si",b.ig,f),Xv("sm",b.qg,f));Yv(c,f);Xv("c",d,f);e&&Xv("fm",encodeURIComponent(e.substring(0,Vv)),f);this.R(f);
};bw.prototype.R=function(a){a=a===void 0?[]:a;!jk||this.M>=Uv||(Xv("pid",Wv,a),Xv("bc",++this.M,a),a.unshift("ctid="+C(5)+"&t=s"),this.U(""+$v+a.join("&")))};function cw(a){return a.performance&&a.performance.now()||Date.now()}
var dw=function(a,b){var c=w,d;var e=function(f,g,h){h=h===void 0?{bn:function(){},dn:function(){},Zm:function(){},onFailure:function(){}}:h;this.vp=f;this.C=g;this.M=h;this.ja=this.wa=this.heartbeatCount=this.up=0;this.kh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.ig=cw(this.C);this.qg=cw(this.C);this.U=10};e.prototype.init=function(){this.R(1);this.Ya()};e.prototype.getState=function(){return{state:this.state,
ig:Math.round(cw(this.C)-this.ig),qg:Math.round(cw(this.C)-this.qg)}};e.prototype.R=function(f){this.state!==f&&(this.state=f,this.qg=cw(this.C))};e.prototype.Am=function(){return String(this.up++)};e.prototype.Ya=function(){var f=this;this.heartbeatCount++;this.yb({type:0,clientId:this.id,requestId:this.Am(),maxDelay:this.mh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ja++,g.isDead||f.ja>20){var l=g.isDead&&g.failure.failureType;
f.U=l||10;f.R(4);f.tp();var n,p;(p=(n=f.M).Zm)==null||p.call(n,{failureType:l||10,data:g.failure.data})}else f.R(3),f.Fm();else{if(f.heartbeatCount>g.stats.heartbeatCount+20){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.M).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var u=f.state;f.R(2);if(u!==2)if(f.kh){var t,v;(v=(t=f.M).dn)==null||v.call(t)}else{f.kh=!0;var x,y;(y=(x=f.M).bn)==null||y.call(x)}f.ja=0;f.wp();f.Fm()}}})};e.prototype.mh=function(){return this.state===2?
5E3:500};e.prototype.Fm=function(){var f=this;this.C.setTimeout(function(){f.Ya()},Math.max(0,this.mh()-(cw(this.C)-this.wa)))};e.prototype.Ap=function(f,g,h){var l=this;this.yb({type:1,clientId:this.id,requestId:this.Am(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,u={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},t,v;(v=(t=l.M).onFailure)==null||v.call(t,u);h(u)}})};e.prototype.yb=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.U},g(f);else{var l=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var u=h.H[n];u&&h.Nf(u,7)},(p=f.maxDelay)!=null?p:5E3),r={request:f,pn:g,kn:l,Hq:q};this.H[n]=r;l||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.wa=cw(this.C);f.kn=!1;this.vp(f.request)};e.prototype.wp=function(){for(var f=m(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.kn&&this.sendRequest(h)}};e.prototype.tp=function(){for(var f=
m(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Nf(this.H[g.value],this.U)};e.prototype.Nf=function(f,g){this.Lc(f);var h=f.request;h.failure={failureType:g};f.pn(h)};e.prototype.Lc=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Hq)};e.prototype.oq=function(f){this.wa=cw(this.C);var g=this.H[f.requestId];if(g)this.Lc(g),g.pn(f);else{var h,l;(l=(h=this.M).onFailure)==null||l.call(h,{failureType:14})}};d=new e(a,c,b);return d};var ew;
var fw=function(){ew||(ew=new bw);return ew},aw=function(a){Fl(Hl(hl.Z.Kc),function(){Pc(a)})},gw=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},hw=function(a){var b=a,c,d=Gg(11);d=Gg(10);c=d;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var e;try{e=new URL(a)}catch(f){return null}return e.protocol!==
"https:"?null:e},iw=function(a){var b=Ml(Il.X.qm);return b&&b[a]},jw=function(a,b,c,d,e){var f=this;this.H=d;this.U=this.R=!1;this.ja=null;this.initTime=c;this.C=15;this.M=this.Pp(a);w.setTimeout(function(){f.initialize()},1E3);Sc(function(){f.yq(a,b,e)})};k=jw.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),ig:this.initTime,qg:Math.round(Fb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.M.Ap(a,b,c)};k.getState=function(){return this.M.getState().state};
k.yq=function(a,b,c){var d=w.location.origin,e=this,f=Nc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,l=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?gw(h):"",p;H(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Nc(l+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ja=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===
a.origin&&e.M.oq(r.data)});e.initialize()})};f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Pp=function(a){var b=this,c=dw(function(d){var e;(e=b.ja)==null||e.postMessage(d,a.origin)},{bn:function(){b.R=!0;b.H.H(c.getState(),c.stats)},dn:function(){},Zm:function(d){b.R?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,
d==null?void 0:d.data)):(b.C=(d==null?void 0:d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.U||this.M.init();this.U=!0};function kw(){var a=og(lg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function lw(a,b){var c=Math.round(Fb());b=b===void 0?!1:b;var d=w.location.origin;if(!d||!kw()||H(168))return;fj()&&!a&&(a=""+d+ej()+"/_/service_worker");var e=hw(a);if(e===null||iw(e.origin))return;if(!Ac()){fw().H(void 0,void 0,6);return}var f=new jw(e,!!a,c||Math.round(Fb()),fw(),b);Nl(Il.X.qm,{})[e.origin]=f;}
var mw=function(a,b,c,d){var e;if((e=iw(a))==null||!e.delegate){var f=Ac()?16:6;fw().C(f,void 0,void 0,b.commandType);d({failureType:f});return}iw(a).delegate(b,c,d);};
function nw(a,b,c,d,e){var f=hw();if(f===null){d(Ac()?16:6);return}var g,h=(g=iw(f.origin))==null?void 0:g.initTime,l=Math.round(Fb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?l-h:void 0}};e&&(n.params.encryptionKeyString=e);mw(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function ow(a,b,c,d){var e=hw(a);if(e===null){d("_is_sw=f"+(Ac()?16:6)+"te");return}var f=b?1:0,g=Math.round(Fb()),h,l=(h=iw(e.origin))==null?void 0:h.initTime,n=l?g-l:void 0,p=!1;H(169)&&(p=!0);mw(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:w.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,u,t=(u=iw(e.origin))==
null?void 0:u.getState();t!==void 0&&(r+="s"+t);d(n?r+("t"+n):r+"te")});};function pw(a){if(H(10))return;var b=fj()||Eg.C||!!dk(a.D);H(245)&&(b=Eg.C||!!dk(a.D));if(b||H(168))return;lw(void 0,H(131));};function qw(){var a;a=a===void 0?document:a;var b;return!((b=a.featurePolicy)==null||!b.allowedFeatures().includes("attribution-reporting"))};var Cw=function(a){this.C=1;this.C>0||(this.C=1);this.onSuccess=a.D.onSuccess},Dw=function(a,b){return Qb(function(){a.C--;if(qb(a.onSuccess)&&a.C===0)a.onSuccess()},b>0?b:1)};function Gw(a,b){var c=!!fj();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?ej()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?H(90)&&ym()?Ew():""+ej()+"/ag/g/c":Ew();case 16:return c?H(90)&&ym()?Fw():""+ej()+"/ga/g/c":Fw();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
ej()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?ej()+"/d/pagead/form-data":H(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Bp+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?ej()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 66:return"https://www.google.com/pagead/uconversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 63:return"https://www.googleadservices.com/pagead/conversion";case 64:return c?ej()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 65:return"https://www.google.com/pagead/1p-conversion";case 22:return c?ej()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?ej()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";
case 23:return c?ej()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?ej()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return H(205)?"https://www.google.com/measurement/conversion/":c?ej()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?ej()+"/d/ccm/form-data":H(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 62:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:rc(a,"Unknown endpoint")}};function Iw(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};var Jw="email sha256_email_address phone_number sha256_phone_number first_name last_name".split(" "),Kw="first_name sha256_first_name last_name sha256_last_name street sha256_street city region country postal_code".split(" ");function Lw(a,b){if(!b._tag_metadata){for(var c={},d=0,e=0;e<a.length;e++)d+=Mw(a[e],b,c)?1:0;d>0&&(b._tag_metadata=c)}}function Mw(a,b,c){var d=b[a];if(d===void 0)return!1;c[a]=Array.isArray(d)?d.map(function(){return{mode:"c"}}):{mode:"c"};return!0}
function Nw(a,b,c){function d(f,g){g=String(g).substring(0,100);e.push(""+f+encodeURIComponent(g))}if(!c)return"";var e=[];d("i",String(a));d("f",b);c.mode&&d("m",c.mode);c.isPreHashed&&d("p","1");c.rawLength&&d("r",String(c.rawLength));c.normalizedLength&&d("n",String(c.normalizedLength));c.location&&d("l",c.location);c.selector&&d("s",c.selector);return e.join(".")};function Ow(){this.blockSize=-1};function Pw(a,b){this.blockSize=-1;this.blockSize=64;this.M=Ga.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.H=0;this.C=[];this.ja=a;this.U=b;this.wa=Ga.Int32Array?new Int32Array(64):Array(64);Qw===void 0&&(Ga.Int32Array?Qw=new Int32Array(Rw):Qw=Rw);this.reset()}Ha(Pw,Ow);for(var Sw=[],Tw=0;Tw<63;Tw++)Sw[Tw]=0;var Uw=[].concat(128,Sw);
Pw.prototype.reset=function(){this.R=this.H=0;var a;if(Ga.Int32Array)a=new Int32Array(this.U);else{var b=this.U,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Vw=function(a){for(var b=a.M,c=a.wa,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var l=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,u=a.C[5]|0,t=a.C[6]|0,v=a.C[7]|0,x=0;x<64;x++){var y=((l>>>2|l<<30)^(l>>>13|l<<19)^(l>>>22|l<<10))+(l&n^l&p^n&p)|0,A=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&u^~r&t)+(Qw[x]|0)|0)+(c[x]|0)|0)|0;v=t;t=u;u=r;r=q+A|0;q=p;p=n;n=l;l=A+y|0}a.C[0]=a.C[0]+l|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+u|0;a.C[6]=a.C[6]+t|0;a.C[7]=a.C[7]+v|0};
Pw.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.M[d++]=a.charCodeAt(c++),d==this.blockSize&&(Vw(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.M[d++]=g;d==this.blockSize&&(Vw(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.R+=b};Pw.prototype.digest=function(){var a=[],b=this.R*8;this.H<56?this.update(Uw,56-this.H):this.update(Uw,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.M[c]=b&255,b/=256;Vw(this);for(var d=0,e=0;e<this.ja;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Rw=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Qw;function Ww(){Pw.call(this,8,Xw)}Ha(Ww,Pw);var Xw=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Yw=/^[0-9A-Fa-f]{64}$/;function Zw(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function $w(a){var b=w;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Yw.test(a))return Promise.resolve(a);try{var d=Zw(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return ax(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function ax(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};function Jx(a,b){b&&yb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};function Kx(a,b){var c=Eu(a,N.m.Dc);if(c&&typeof c==="object")for(var d=m(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value,g=c[f];g!==void 0&&(g===null&&(g=""),b["gap."+f]=String(g))}};var Yx={};Yx.O=hr.O;var Zx={Xr:"L",np:"S",vs:"Y",Dr:"B",Pr:"E",Tr:"I",qs:"TC",Sr:"HTC"},$x={np:"S",Or:"V",Gr:"E",ns:"tag"},ay={},by=(ay[Yx.O.Ri]="6",ay[Yx.O.Si]="5",ay[Yx.O.Qi]="7",ay);function cy(){function a(c,d){var e=ob(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var dy=!1;
function wy(a){}function xy(a){}
function yy(){}function zy(a){}
function Ay(a){}function By(a){}
function Cy(){}function Dy(a,b){}
function Ey(a,b,c){}
function Fy(){};var Gy=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function Hy(a,b,c,d,e,f,g,h){var l=oa(Object,"assign").call(Object,{},Gy);c&&(l.body=c,l.method="POST");oa(Object,"assign").call(Object,l,e);h==null||Sk(h);w.fetch(b,l).then(function(n){h==null||Tk(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function u(){p.read().then(function(t){var v;v=t.done;var x=q.decode(t.value,{stream:!v});Iy(d,x);v?(f==null||f(),r()):u()}).catch(function(){r()})}u()})}}).catch(function(){h==null||Tk(h);
g?g():H(128)&&(b+="&_z=retryFetch",c?al(a,b,c):$k(a,b))})};var Jy=function(a){this.M=a;this.C=""},Ky=function(a,b){a.H=b;return a},Iy=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=m(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(l){}e=void 0}Ly(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},My=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c={};Ly(a,(c[b.fallback_url_method]=
[b.fallback_url],c.options={},c))}}},Ly=function(a,b){b&&(Ny(b.send_pixel,b.options,a.M),Ny(b.create_iframe,b.options,a.R),Ny(b.fetch,b.options,a.H))};function Oy(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function Ny(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=sd(b)?b:{},f=m(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var Py=function(a,b){this.Kq=a;this.timeoutMs=b;this.Ta=void 0},Sk=function(a){a.Ta||(a.Ta=setTimeout(function(){a.Kq();a.Ta=void 0},a.timeoutMs))},Tk=function(a){a.Ta&&(clearTimeout(a.Ta),a.Ta=void 0)};var yz=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),zz={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},Az={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},Bz="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function Cz(){var a=ep("gtm.allowlist")||ep("gtm.whitelist");a&&P(9);Xi&&!H(212)?a=["google","gtagfl","lcl","zone","cmpPartners"]:H(212)&&(a=void 0);yz.test(w.location&&w.location.hostname)&&(Xi?P(116):(P(117),Dz&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Jb(Cb(a),zz),c=ep("gtm.blocklist")||ep("gtm.blacklist");c||(c=ep("tagTypeBlacklist"))&&P(3);c?P(8):c=[];yz.test(w.location&&w.location.hostname)&&(c=Cb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));Cb(c).indexOf("google")>=0&&P(2);var d=c&&Jb(Cb(c),Az),e={};return function(f){var g=f&&f[nf.Qa];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=cj[g]||[],l=!0;if(a){var n;if(n=l)a:{if(b.indexOf(g)<0){if(Xi&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){P(11);n=!1;break a}}else{n=!1;break a}}n=!0}l=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var u=wb(d,h||[]);u&&
P(10);q=u}}var t=!l||q;!t&&(h.indexOf("sandboxedScripts")===-1?0:Xi&&h.indexOf("cmpPartners")>=0?!Ez():b&&b.indexOf("sandboxedScripts")!==-1?0:wb(d,Bz))&&(t=!0);return e[g]=t}}function Ez(){var a=og(lg.C,C(5),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var Dz=!1;Dz=!0;H(218)&&(Dz=Bg(48,Dz));function Fz(a,b,c,d,e){if(!Pj(a)){d.loadExperiments=Pi();Rj(a,d,e);var f=Gz(a),g=function(){Aj().container[a]&&(Aj().container[a].state=3);Hz()},h={destinationId:a,endpoint:0};if(fj())dl(h,ej()+"/"+f,void 0,g);else{var l=Kb(a,"GTM-"),n=ck(),p=c?"/gtag/js":"/gtm.js",q=bk(b,p+f);if(!q){var r=C(3)+p;n&&Cc&&l&&(r=Cc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=tv("https://","http://",r+f)}dl(h,q,void 0,g)}}}function Hz(){Sj()||yb(Tj(),function(a,b){Iz(a,b.transportUrl,b.context);P(92)})}
function Iz(a,b,c,d){if(!Qj(a))if(c.loadExperiments||(c.loadExperiments=Pi()),Sj()){var e=Aj(),f=e.destination[a];f?Array.isArray(f)||(f.state=0):(f={state:0,transportUrl:b,context:c,parent:Kj()},e.destination[a]=f);Bj({ctid:a,isDestination:!0},d);P(91)}else{var g=Aj(),h=g.destination[a];h?Array.isArray(h)||(h.state=1):(h={context:c,state:1,parent:Kj()},g.destination[a]=h);Bj({ctid:a,isDestination:!0},d);var l={destinationId:a,endpoint:0};if(fj())dl(l,ej()+("/gtd"+Gz(a,!0)));else{var n="/gtag/destination"+
Gz(a,!0),p=bk(b,n);p||(p=tv("https://","http://",C(3)+n));dl(l,p)}}}function Gz(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a),d=C(19);d!=="dataLayer"&&(c+="&l="+d);if(!Kb(a,"GTM-")||b)c=H(130)?c+(fj()?"&sc=1":"&cx=c"):c+"&cx=c";var e=c,f,g={nn:Fg(15),qn:C(14)};f=mf(g);c=e+("&gtm="+f);ck()&&(c+="&sign="+Ri.Oi);var h=Eg.M;h===1?c+="&fps=fc":h===2&&(c+="&fps=fe");return c};var Jz=function(){this.H=0;this.C={}};Jz.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Ie:c};return d};Jz.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var Lz=function(a,b){var c=[];yb(Kz.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Ie===void 0||b.indexOf(e.Ie)>=0)&&c.push(e.listener)});return c};function Mz(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:C(5)}};function Nz(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var Pz=function(a,b){this.C=!1;this.R=[];this.eventData={tags:[]};this.U=!1;this.H=this.M=0;Oz(this,a,b)},Qz=function(a,b,c,d){if(Ti.hasOwnProperty(b)||b==="__zone")return-1;var e={};sd(d)&&(e=td(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},Rz=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},Sz=function(a){if(!a.C){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.C=!0;a.R.length=0}},Oz=function(a,b,c){b!==void 0&&a.Tf(b);c&&w.setTimeout(function(){Sz(a)},
Number(c))};Pz.prototype.Tf=function(a){var b=this,c=Hb(function(){Sc(function(){a(C(5),b.eventData)})});this.C?c():this.R.push(c)};var Tz=function(a){a.M++;return Hb(function(){a.H++;a.U&&a.H>=a.M&&Sz(a)})},Uz=function(a){a.U=!0;a.H>=a.M&&Sz(a)};var Vz={};function Wz(){return w[Xz()]}
function Xz(){return w.GoogleAnalyticsObject||"ga"}function $z(){var a=C(5);}
function aA(a,b){return function(){var c=Wz(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),l=g.indexOf("&tid="+b)<0;l&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);l&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var gA=["es","1"],hA={},iA={};function jA(a,b){if(jk){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";hA[a]=[["e",c],["eid",a]];xp(a)}}function kA(a){var b=a.eventId,c=a.Vc;if(!hA[b])return[];var d=[];iA[b]||d.push(gA);d.push.apply(d,Ba(hA[b]));c&&(iA[b]=!0);return d};var lA={},mA={},nA={};function oA(a,b,c,d){jk&&H(120)&&((d===void 0?0:d)?(nA[b]=nA[b]||0,++nA[b]):c!==void 0?(mA[a]=mA[a]||{},mA[a][b]=Math.round(c)):(lA[a]=lA[a]||{},lA[a][b]=(lA[a][b]||0)+1))}function pA(a){var b=a.eventId,c=a.Vc,d=lA[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete lA[b];return e.length?[["md",e.join(".")]]:[]}
function qA(a){var b=a.eventId,c=a.Vc,d=mA[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete mA[b];return e.length?[["mtd",e.join(".")]]:[]}function rA(){for(var a=[],b=m(Object.keys(nA)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+nA[d])}return a.length?[["mec",a.join(".")]]:[]};var sA={},tA={};function uA(a,b,c){if(jk&&b){var d=gk(b);sA[a]=sA[a]||[];sA[a].push(c+d);var e=b[nf.Qa];if(!e)throw Error("Error: No function name given for function call.");var f=(Pf[e]?"1":"2")+d;tA[a]=tA[a]||[];tA[a].push(f);xp(a)}}function vA(a){var b=a.eventId,c=a.Vc,d=[],e=sA[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=tA[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete sA[b],delete tA[b]);return d};function wA(a,b,c){c=c===void 0?!1:c;xA().addRestriction(0,a,b,c)}function yA(a,b,c){c=c===void 0?!1:c;xA().addRestriction(1,a,b,c)}function zA(){var a=Gj();return xA().getRestrictions(1,a)}var AA=function(){this.container={};this.C={}},BA=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
AA.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=BA(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
AA.prototype.getRestrictions=function(a,b){var c=BA(this,b);if(a===0){var d,e;return[].concat(Ba((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),Ba((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(Ba((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),Ba((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
AA.prototype.getExternalRestrictions=function(a,b){var c=BA(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};AA.prototype.removeExternalRestrictions=function(a){var b=BA(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function xA(){return Un("r",function(){return new AA})};function CA(a,b,c,d){var e=Nf[a],f=DA(a,b,c,d);if(!f)return null;var g=ag(e[nf.rm],c,[]);if(g&&g.length){var h=g[0];f=CA(h.index,{onSuccess:f,onFailure:h.Qm===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function DA(a,b,c,d){function e(){function x(){nm(3);var M=Fb()-F;Mz(1,a,Nf[a][nf.eh]);uA(c.id,f,"7");Rz(c.Nc,E,"exception",M);H(109)&&Ey(c,f,Yx.O.Qi);J||(J=!0,h())}if(f[nf.ep])h();else{var y=$f(f,c,[]),A=y[nf.Hn];if(A!=null)for(var D=0;D<A.length;D++)if(!xn(A[D])){h();return}var E=Qz(c.Nc,String(f[nf.Qa]),Number(f[nf.oh]),y[nf.METADATA]),J=!1;y.vtp_gtmOnSuccess=function(){if(!J){J=!0;var M=Fb()-F;uA(c.id,Nf[a],"5");Rz(c.Nc,E,"success",M);H(109)&&Ey(c,f,Yx.O.Si);g()}};y.vtp_gtmOnFailure=function(){if(!J){J=
!0;var M=Fb()-F;uA(c.id,Nf[a],"6");Rz(c.Nc,E,"failure",M);H(109)&&Ey(c,f,Yx.O.Ri);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);uA(c.id,f,"1");H(109)&&Dy(c,f);var F=Fb();try{bg(y,{event:c,index:a,type:1})}catch(M){x(M)}H(109)&&Ey(c,f,Yx.O.Bm)}}var f=Nf[a],g=b.onSuccess,h=b.onFailure,l=b.terminate;if(c.isBlocked(f))return null;var n=ag(f[nf.Cm],c,[]);if(n&&n.length){var p=n[0],q=CA(p.index,{onSuccess:g,onFailure:h,terminate:l},c,d);if(!q)return null;
g=q;h=p.Qm===2?l:q}if(f[nf.hm]||f[nf.hp]){var r=f[nf.hm]?Of:c.ur,u=g,t=h;if(!r[a]){var v=EA(a,r,Hb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](u,t)}}return e}function EA(a,b,c){var d=[],e=[];b[a]=FA(d,e,c);return{onSuccess:function(){b[a]=GA;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=HA;for(var f=0;f<e.length;f++)e[f]()}}}function FA(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function GA(a){a()}function HA(a,b){b()};var KA=function(a,b){for(var c=[],d=0;d<Nf.length;d++)if(a[d]){var e=Nf[d];var f=Tz(b.Nc);try{var g=CA(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[nf.Qa];if(!h)throw Error("Error: No function name given for function call.");var l=Pf[h];c.push({vn:d,priorityOverride:(l?l.priorityOverride||0:0)||Nz(e[nf.Qa],1)||0,execute:g})}else IA(d,b),f()}catch(p){f()}}c.sort(JA);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function LA(a,b){if(!Kz)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=Lz(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=Tz(b);try{d[e](a,f)}catch(g){f()}}return!0}function JA(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.vn,h=b.vn;f=g>h?1:g<h?-1:0}return f}
function IA(a,b){if(jk){var c=function(d){var e=b.isBlocked(Nf[d])?"3":"4",f=ag(Nf[d][nf.rm],b,[]);f&&f.length&&c(f[0].index);uA(b.id,Nf[d],e);var g=ag(Nf[d][nf.Cm],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var MA=!1,Kz;function NA(){Kz||(Kz=new Jz);return Kz}
function OA(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(H(109)){}if(d==="gtm.js"){if(MA)return!1;MA=!0}var e=!1,f=zA(),g=td(a,null);if(!f.every(function(u){return u({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}jA(b,d);var h=a.eventCallback,l=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:PA(g,e),ur:[],logMacroError:function(u,t,v){P(6);nm(0);Mz(2,t,v)},cachedModelValues:QA(),Nc:new Pz(function(){if(H(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,
0))},l),originalEventData:g};H(120)&&jk&&(n.reportMacroDiscrepancy=oA);H(109)&&Ay(n.id);var p=gg(n);H(109)&&By(n.id);e&&(p=RA(p));H(109)&&zy(b);var q=KA(p,n),r=LA(a,n.Nc);Uz(n.Nc);d!=="gtm.js"&&d!=="gtm.sync"||$z();return SA(p,q)||r}function QA(){var a={};a.event=jp("event",1);a.ecommerce=jp("ecommerce",1);a.gtm=jp("gtm");a.eventModel=jp("eventModel");return a}
function PA(a,b){var c=Cz();return function(d){if(c(d))return!0;var e=d&&d[nf.Qa];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Gj();f=xA().getRestrictions(0,g);var h=a;b&&(h=td(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var l=cj[e]||[],n=m(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:l,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function RA(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Nf[c][nf.Qa]);if(Si[d]||Nf[c][nf.jp]!==void 0||Nz(d,2))b[c]=!0}return b}function SA(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Nf[c]&&!Ti[String(Nf[c][nf.Qa])])return!0;return!1};function TA(){NA().addListener("gtm.init",function(a,b){Eg.ja=!0;$l();b()})};var UA=!1,VA=0,WA=[];function XA(a){if(!UA){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){UA=!0;for(var e=0;e<WA.length;e++)Sc(WA[e])}WA.push=function(){for(var f=Fa.apply(0,arguments),g=0;g<f.length;g++)Sc(f[g]);return 0}}}function YA(){if(!UA&&VA<140){VA++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");XA()}catch(c){w.setTimeout(YA,50)}}}
function ZA(){var a=w;UA=!1;VA=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")XA();else{Qc(z,"DOMContentLoaded",XA);Qc(z,"readystatechange",XA);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&YA()}Qc(a,"load",XA)}}function $A(a){UA?a():WA.push(a)};function aB(a,b){return arguments.length===1?bB("set",a):bB("set",a,b)}function cB(a,b){return arguments.length===1?bB("config",a):bB("config",a,b)}function dB(a,b,c){c=c||{};c[N.m.rd]=a;return bB("event",b,c)}function bB(){return arguments};var eB=function(){this.messages=[];this.C=[]};eB.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=oa(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};eB.prototype.listen=function(a){this.C.push(a)};
eB.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};eB.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function fB(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[R.A.Za]=C(6);gB().enqueue(a,b,c)}function hB(){var a=iB;gB().listen(a)}
function gB(){return Un("mb",function(){return new eB})};var jB={},kB={};function lB(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Dj:void 0,kj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Dj=po(g,b),e.Dj){var h=Fj();ub(h,function(r){return function(u){return r.Dj.destinationId===u}}(e))?c.push(g):d.push(g)}}else{var l=jB[g]||[];e.kj={};l.forEach(function(r){return function(u){r.kj[u]=!0}}(e));for(var n=Hj(),p=0;p<n.length;p++)if(e.kj[n[p]]){c=c.concat(Fj());break}var q=kB[g]||[];q.length&&(c=c.concat(q))}}return{wj:c,Iq:d}}
function mB(a){yb(jB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function nB(a){yb(kB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var oB=!1,pB=!1;function qB(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=td(b,null),b[N.m.ff]&&(d.eventCallback=b[N.m.ff]),b[N.m.Og]&&(d.eventTimeout=b[N.m.Og]));return d}function rB(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Zn()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function sB(a,b){var c=a&&a[N.m.rd];c===void 0&&(c=ep(N.m.rd,2),c===void 0&&(c="default"));if(rb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?rb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=lB(d,b.isGtmEvent),f=e.wj,g=e.Iq;if(g.length)for(var h=tB(a),l=0;l<g.length;l++){var n=po(g[l],b.isGtmEvent);if(n){var p=n.destinationId,q=void 0;((q=zj(n.destinationId))==null?void 0:q.state)===0||Iz(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var r=f.concat(g);return{wj:qo(f,b.isGtmEvent),
Cp:qo(r,b.isGtmEvent)}}}var uB=void 0,vB=void 0;function wB(a,b,c){var d=td(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&P(136);var e=td(b,null);td(c,e);fB(cB(Hj()[0],e),a.eventId,d)}function tB(a){for(var b=m([N.m.sd,N.m.rc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Fp.C[d];if(e)return e}}
var xB={config:function(a,b){var c=rB(a,b);if(!(a.length<2)&&rb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!sd(a[2])||a.length>3)return;d=a[2]}var e=po(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Cg(7)){var l=Jj(Kj());if(Uj(l)){var n=l.parent,p=n.isDestination;h={Mq:Jj(n),Gq:p};break a}}h=void 0}var q=h;q&&(f=q.Mq,g=q.Gq);jA(c.eventId,"gtag.config");var r=e.destinationId,u=e.id!==r;if(u?Fj().indexOf(r)===-1:Hj().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[N.m.Gc]){var t=tB(d);if(u)Iz(r,t,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;uB?wB(b,v,uB):vB||(vB=td(v,null))}else Fz(r,t,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(P(128),g&&P(130),b.inheritParentConfig)){var x;var y=d;vB?(wB(b,vB,y),x=!1):(!y[N.m.ud]&&Cg(11)&&uB||(uB=td(y,null)),x=!0);x&&f.containers&&f.containers.join(",");return}lk&&(ao===1&&(Tl.mcc=!1),ao=2);if(Cg(11)&&!u&&!d[N.m.ud]){var A=pB;pB=!0;if(A)return}oB||P(43);if(!b.noTargetGroup)if(u){nB(e.id);
var D=e.id,E=d[N.m.Rg]||"default";E=String(E).split(",");for(var J=0;J<E.length;J++){var F=kB[E[J]]||[];kB[E[J]]=F;F.indexOf(D)<0&&F.push(D)}}else{mB(e.id);var M=e.id,T=d[N.m.Rg]||"default";T=T.toString().split(",");for(var ha=0;ha<T.length;ha++){var S=jB[T[ha]]||[];jB[T[ha]]=S;S.indexOf(M)<0&&S.push(M)}}delete d[N.m.Rg];var ca=b.eventMetadata||{};ca.hasOwnProperty(R.A.yd)||(ca[R.A.yd]=!b.fromContainerExecution);b.eventMetadata=ca;delete d[N.m.ff];for(var ta=u?[e.id]:Fj(),ka=0;ka<ta.length;ka++){var ea=
d,X=ta[ka],la=td(b,null),ya=po(X,la.isGtmEvent);ya&&Fp.push("config",[ea],ya,la)}}}}},consent:function(a,b){if(a.length===3){P(39);var c=rB(a,b),d=a[1],e={},f=Om(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===N.m.wg?Array.isArray(h)?NaN:Number(h):g===N.m.hc?(Array.isArray(h)?h:[h]).map(Pm):Qm(h)}b.fromContainerExecution||(e[N.m.W]&&P(139),e[N.m.La]&&P(140));d==="default"?tn(e):d==="update"?vn(e,c):d==="declare"&&b.fromContainerExecution&&sn(e)}},container_config:function(a,b){if(H(240)&&
b.isGtmEvent&&a.length===3&&rb(a[1])&&sd(a[2])){var c=a[2],d=po(a[1],!0);if(d){var e=d.destinationId,f=td(b,null),g=po(e,f.isGtmEvent);g&&Fp.push("container_config",[c],g,f)}}},destination_config:function(a,b){if(H(240)&&b.isGtmEvent&&a.length===3&&rb(a[1])&&sd(a[2])){var c=a[2],d=po(a[1],!0);if(d){var e=d.destinationId,f=td(b,null),g=po(e,f.isGtmEvent);g&&Fp.push("destination_config",[c],g,f)}}},event:function(a,b){var c=a[1];if(!(a.length<2)&&rb(c)){var d=void 0;if(a.length>2){if(!sd(a[2])&&a[2]!==
void 0||a.length>3)return;d=a[2]}var e=qB(c,d),f=rB(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var l=sB(d,b);if(l){for(var n=l.wj,p=l.Cp,q=p.map(function(M){return M.id}),r=p.map(function(M){return M.destinationId}),u=n.map(function(M){return M.id}),t=m(Fj()),v=t.next();!v.done;v=t.next()){var x=v.value;r.indexOf(x)<0&&u.push(x)}jA(g,c);for(var y=m(u),A=y.next();!A.done;A=y.next()){var D=A.value,
E=td(b,null),J=td(d,null);delete J[N.m.ff];var F=E.eventMetadata||{};F.hasOwnProperty(R.A.yd)||(F[R.A.yd]=!E.fromContainerExecution);F[R.A.Li]=q.slice();F[R.A.Qf]=r.slice();E.eventMetadata=F;Gp(c,J,D,E)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[N.m.rd]=q.join(","):delete e.eventModel[N.m.rd];oB||P(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[R.A.zm]&&(b.noGtmEvent=!0);e.eventModel[N.m.Fc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){P(53);if(a.length===
4&&rb(a[1])&&rb(a[2])&&qb(a[3])){var c=po(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){oB||P(43);var f=tB();if(ub(Fj(),function(h){return c.destinationId===h})){rB(a,b);var g={};td((g[N.m.kf]=d,g[N.m.jf]=e,g),null);Hp(d,function(h){Sc(function(){e(h)})},c.id,b)}else Iz(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){oB=!0;var c=rB(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),
f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&rb(a[1])&&qb(a[2])){if(mg(a[1],a[2]),P(74),a[1]==="all"){P(75);var b=!1;try{b=a[2](C(5),"unknown",{})}catch(c){}b||P(76)}}else P(73)},set:function(a,b){var c=void 0;a.length===2&&sd(a[1])?c=td(a[1],null):a.length===3&&rb(a[1])&&(c={},sd(a[2])||Array.isArray(a[2])?c[a[1]]=td(a[2],null):c[a[1]]=a[2]);if(c){var d=rB(a,b),e=d.eventId,f=d.priorityId;td(c,null);C(5);var g=td(c,null);Fp.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=
e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},yB={policy:!0};var AB=function(a){if(zB(a))return a;this.value=a};AB.prototype.getUntrustedMessageValue=function(){return this.value};var zB=function(a){return!a||qd(a)!=="object"||sd(a)?!1:"getUntrustedMessageValue"in a};AB.prototype.getUntrustedMessageValue=AB.prototype.getUntrustedMessageValue;var BB=!1,CB=[];function DB(){if(!BB){BB=!0;for(var a=0;a<CB.length;a++)Sc(CB[a])}}function EB(a){BB?Sc(a):CB.push(a)};var FB=0,GB={},HB=[],IB=[],JB=!1,KB=!1;function LB(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function MB(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return NB(a)}function OB(a,b){if(!sb(b)||b<0)b=0;var c=Yn(),d=0,e=!1,f=void 0;f=w.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(w.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function PB(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(zb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function QB(){var a;if(IB.length)a=IB.shift();else if(HB.length)a=HB.shift();else return;var b;var c=a;if(JB||!PB(c.message))b=c;else{JB=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Zn(),f=Zn(),c.message["gtm.uniqueEventId"]=Zn());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},l={},n={message:(l.event="gtm.init",l["gtm.uniqueEventId"]=f,l),messageContext:{eventId:f}};HB.unshift(n,c);b=h}return b}
function RB(){for(var a=!1,b;!KB&&(b=QB());){KB=!0;delete bp.eventModel;dp();var c=b,d=c.message,e=c.messageContext;if(d==null)KB=!1;else{e.fromContainerExecution&&ip();try{if(qb(d))try{d.call(fp)}catch(J){}else if(Array.isArray(d)){if(rb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),l=ep(f.join("."),2);if(l!=null)try{l[g].apply(l,h)}catch(J){}}}else{var n=void 0;if(zb(d))a:{if(d.length&&rb(d[0])){var p=xB[d[0]];if(p&&(!e.fromContainerExecution||!yB[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,u=r._clear||e.overwriteModelFields,t=m(Object.keys(r)),v=t.next();!v.done;v=t.next()){var x=v.value;x!=="_clear"&&(u&&hp(x),hp(x,r[x]))}$i||($i=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=Zn(),r["gtm.uniqueEventId"]=y,hp("gtm.uniqueEventId",y)),q=OA(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&dp(!0);var A=d["gtm.uniqueEventId"];if(typeof A==="number"){for(var D=GB[String(A)]||[],E=0;E<D.length;E++)IB.push(SB(D[E]));D.length&&IB.sort(LB);
delete GB[String(A)];A>FB&&(FB=A)}KB=!1}}}return!a}
function TB(){if(H(109)){var a=!Eg.R;}var c=RB();if(H(109)){}try{var e=w[C(19)],f=C(5),g=e.hide;if(g&&g[f]!==void 0&&g.end){g[f]=
!1;var h=!0,l;for(l in g)if(g.hasOwnProperty(l)&&g[l]===!0){h=!1;break}h&&(g.end(),g.end=null)}}catch(n){C(5)}return c}function iB(a){if(FB<a.notBeforeEventId){var b=String(a.notBeforeEventId);GB[b]=GB[b]||[];GB[b].push(a)}else IB.push(SB(a)),IB.sort(LB),Sc(function(){KB||RB()})}function SB(a){return{message:a.message,messageContext:a.messageContext}}
function UB(){function a(f){var g={};if(zB(f)){var h=f;f=zB(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=Dc(C(19),[]),c=Xn();c.pruned===!0&&P(83);GB=gB().get();hB();$A(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});EB(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Tn.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new AB(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});HB.push.apply(HB,h);var l=d.apply(b,f),n=Math.max(100,Number(Gg(1,"300")));if(this.length>n)for(P(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof l!=="boolean"||l;return RB()&&p};var e=b.slice(0).map(function(f){return a(f)});HB.push.apply(HB,e);if(!Eg.R){if(H(109)){}Sc(TB)}}
var NB=function(a){return w[C(19)].push(a)};function VB(a){NB(a)};function WB(){var a,b=sj(w.location.href);(a=b.hostname+b.pathname)&&Wl("dl",encodeURIComponent(a));var c;var d=C(5);if(d){var e=Cg(7)?1:0,f,g=Kj(),h=Jj(g),l=(f=h&&h.context)&&f.fromContainerExecution?1:0,n=f&&f.source||0,p=C(6);c=d+";"+p+";"+l+";"+n+";"+e}else c=void 0;var q=c;q&&Wl("tdp",q);var r=Yp(!0);r!==void 0&&Wl("frm",String(r))};var XB={},YB=void 0;
function ZB(){if(an()||lk)Wl("csp",function(){return Object.keys(XB).map(function(a){return[a==="undefined"?"":a].concat(Ba(XB[a])).join(";")}).join("~")||void 0},!1),w.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){P(179);var b=Zk(a.effectiveDirective);if(b){var c;var d=Xk(b,a.blockedURI);c=d?Vk[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=
void 0}var h=e;if(h){for(var l=m(c),n=l.next();!n.done;n=l.next()){var p=n.value;if(!p.on){p.on=!0;if(H(59)){var q={eventId:p.eventId,priorityId:p.priorityId};if(an()){var r=q,u={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(an()){var t=gn("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});t.tagDiagnostics=u;$m(t)}}}$B(p.destinationId,p.endpoint)}}Yk(b,a.blockedURI)}}}}})}
function $B(a,b){var c=String(a);if(XB.hasOwnProperty(c)){var d=XB[c],e=d.findIndex(function(f){return f>=b});if(e>=0&&d[e]===b)return;e<0&&(e=d.length);d.splice(e,0,b)}else XB[c]=[b];Xl("csp",!0);YB===void 0&&H(171)&&(YB=w.setTimeout(function(){if(H(171)){var f=Tl.csp;Tl.csp=!0;Tl.seq=!1;var g=Yl(!1);Tl.csp=f;Tl.seq=!0;Lc(g+"&script=1")}YB=void 0},500))};var aC=void 0;function bC(){H(236)&&w.addEventListener("pageshow",function(a){a&&(Wl("bfc",function(){return aC?"1":"0"}),a.persisted?(aC=!0,Xl("bfc",!0),$l()):aC=!1)})};function cC(){var a;var b=Ij();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Wl("pcid",e)};var dC=/^(https?:)?\/\//;
function eC(){var a=Lj();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=hd())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=m(e),l=h.next();!l.done;l=h.next()){var n=l.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(dC,"")===d.replace(dC,""))){b=g;break a}}P(146)}else P(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Wl("rtg",String(a.canonicalContainerId)),Wl("slo",String(p)),Wl("hlo",a.htmlLoadOrder||"-1"),
Wl("lst",String(a.loadScriptType||"0")))}else P(144)};
function zC(){};var AC=function(){};AC.prototype.toString=function(){return"undefined"};var BC=new AC;
var DC=function(){Un("rm",function(){return{}})[Gj()]=function(a){if(CC.hasOwnProperty(a))return CC[a]}},GC=function(a,b,c){if(a instanceof EC){var d=a,e=d.resolve,f=b,g=String(Zn());FC[g]=[f,c];a=e.call(d,g);b=pb}return{wq:a,onSuccess:b}},HC=function(a){var b=a?0:1;return function(c){P(a?134:135);var d=FC[c];if(d&&typeof d[b]==="function")d[b]();FC[c]=void 0}},EC=function(a){this.valueOf=this.toString;this.resolve=function(b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]===BC?b:a[d]);return c.join("")}};
EC.prototype.toString=function(){return this.resolve("undefined")};var CC={},FC={};function IC(){H(212)&&Xi&&(mg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}}),wA(Gj(),function(a){var b,c;b=a.entityId;c=a.securityGroups;var d="__"+b;return Nz(d,5)||!(!Pf[d]||!Pf[d][5])||c.includes("cmpPartners")}))};function JC(a,b){function c(g){var h=sj(g),l=mj(h,"protocol"),n=mj(h,"host",!0),p=mj(h,"port"),q=mj(h,"path").toLowerCase().replace(/\/$/,"");if(l===void 0||l==="http"&&p==="80"||l==="https"&&p==="443")l="web",p="default";return[l,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function KC(a){return LC(a)?1:0}
function LC(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=td(a,{});td({arg1:c[d],any_of:void 0},e);if(KC(e))return!0}return!1}switch(a["function"]){case "_cn":return eh(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<$g.length;g++){var h=$g[g];if(b[h]!=null){f=b[h](c);break a}}}catch(l){}f=!1}return f;case "_ew":return ah(b,c);case "_eq":return fh(b,c);case "_ge":return gh(b,c);case "_gt":return ih(b,c);case "_lc":return bh(b,c);case "_le":return hh(b,
c);case "_lt":return jh(b,c);case "_re":return dh(b,c,a.ignore_case);case "_sw":return kh(b,c);case "_um":return JC(b,c)}return!1};var MC=function(){this.C=this.gppString=void 0};MC.prototype.reset=function(){this.C=this.gppString=void 0};var NC=new MC;[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var OC=function(a,b,c,d){eq.call(this);this.kh=b;this.Nf=c;this.Lc=d;this.yb=new Map;this.mh=0;this.wa=new Map;this.Ya=new Map;this.U=void 0;this.H=a};za(OC,eq);OC.prototype.M=function(){delete this.C;this.yb.clear();this.wa.clear();this.Ya.clear();this.U&&(aq(this.H,"message",this.U),delete this.U);delete this.H;delete this.Lc;eq.prototype.M.call(this)};
var PC=function(a){if(a.C)return a.C;a.Nf&&a.Nf(a.H)?a.C=a.H:a.C=Xp(a.H,a.kh);var b;return(b=a.C)!=null?b:null},RC=function(a,b,c){if(PC(a))if(a.C===a.H){var d=a.yb.get(b);d&&d(a.C,c)}else{var e=a.wa.get(b);if(e&&e.vj){QC(a);var f=++a.mh;a.Ya.set(f,{Eh:e.Eh,Tp:e.Xm(c),persistent:b==="addEventListener"});a.C.postMessage(e.vj(c,f),"*")}}},QC=function(a){a.U||(a.U=function(b){try{var c;c=a.Lc?a.Lc(b):void 0;if(c){var d=c.Pq,e=a.Ya.get(d);if(e){e.persistent||a.Ya.delete(d);var f;(f=e.Eh)==null||f.call(e,
e.Tp,c.payload)}}}catch(g){}},$p(a.H,"message",a.U))};var SC=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},TC=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},UC={Xm:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Eh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},VC={Xm:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Eh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function WC(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,Pq:b.__gppReturn.callId}}
var XC=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;eq.call(this);this.caller=new OC(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},WC);this.caller.yb.set("addEventListener",SC);this.caller.wa.set("addEventListener",UC);this.caller.yb.set("removeEventListener",TC);this.caller.wa.set("removeEventListener",VC);this.timeoutMs=c!=null?c:500};za(XC,eq);XC.prototype.M=function(){this.caller.dispose();eq.prototype.M.call(this)};
XC.prototype.addEventListener=function(a){var b=this,c=Vp(function(){a(YC,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);RC(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(l){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(ZC,!0);return}a($C,!0)}}})};
XC.prototype.removeEventListener=function(a){RC(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var $C={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},YC={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},ZC={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function aD(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){NC.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");NC.C=d}}function bD(){try{var a=new XC(w,{timeoutMs:-1});PC(a.caller)&&a.addEventListener(aD)}catch(b){}};function cD(){var a=[["cv",C(1)],["rv",C(14)],["tc",Nf.filter(function(c){return c}).length]],b=Fg(15);b&&a.push(["x",b]);Wj()&&a.push(["tag_exp",Wj()]);return a};function dD(a){a.Vc&&(sr=rr=0);return[["cd",""+rr],["cd",""+sr]]};var eD={};function Jg(a){eD[a]=(eD[a]||0)+1}function fD(){for(var a=eD,b=[],c=m(Object.keys(a)),d=c.next();!d.done;d=c.next()){var e=d.value;b.push(e+"."+a[e])}return b.length===0?[]:[["bdm",b.join("~")]]};var gD={},hD={};function iD(a){var b=a.eventId,c=a.Vc,d=[],e=gD[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=hD[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete gD[b],delete hD[b]);return d};function jD(){return!1}function kD(){var a={};return function(b,c,d){}};function lD(){var a=mD;return function(b,c,d){var e=d&&d.event;nD(c);var f=Rh(b)?void 0:1,g=new cb;yb(c,function(r,u){var t=Id(u,void 0,f);t===void 0&&u!==void 0&&P(44);g.set(r,t)});a.Mb(eg());var h={Km:sg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Tf:e!==void 0?function(r){e.Nc.Tf(r)}:void 0,Jb:function(){return b},log:function(){},Yp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},Xq:!!Nz(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(jD()){var l=kD(),n,p;h.pb={Kj:[],Uf:{},ac:function(r,u,t){u===1&&(n=r);u===7&&(p=t);l(r,u,t)},Dh:ki()};h.log=function(r){var u=Fa.apply(1,arguments);n&&l(n,4,{level:r,source:p,message:u})}}var q=df(a,h,[b,g]);a.Mb();q instanceof Ia&&(q.type==="return"?q=q.data:q=void 0);return B(q,void 0,f)}}function nD(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;qb(b)&&(a.gtmOnSuccess=function(){Sc(b)});qb(c)&&(a.gtmOnFailure=function(){Sc(c)})};function oD(a){}oD.K="internal.addAdsClickIds";function pD(a,b){var c=this;}pD.publicName="addConsentListener";var qD=!1;function rD(a){for(var b=0;b<a.length;++b)if(qD)try{a[b]()}catch(c){P(77)}else a[b]()}function sD(a,b,c){var d=this,e;return e}sD.K="internal.addDataLayerEventListener";function tD(a,b,c){}tD.publicName="addDocumentEventListener";function uD(a,b,c,d){}uD.publicName="addElementEventListener";function vD(a){return a.J.nb()};function wD(a){}wD.publicName="addEventCallback";
var xD=function(a){return typeof a==="string"?a:String(Zn())},AD=function(a,b){yD(a,"init",!1)||(zD(a,"init",!0),b())},yD=function(a,b,c){var d=BD(a);return Gb(d,b,c)},CD=function(a,b,c,d){var e=BD(a),f=Gb(e,b,d);e[b]=c(f)},zD=function(a,b,c){BD(a)[b]=c},BD=function(a){var b=Un("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},DD=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":ed(a,"className"),"gtm.elementId":a.for||Tc(a,"id")||"","gtm.elementTarget":a.formTarget||
ed(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||ed(a,"href")||a.src||a.code||a.codebase||"";return d};
function LD(a){}LD.K="internal.addFormAbandonmentListener";function MD(a,b,c,d){}
MD.K="internal.addFormData";var ND={},OD=[],PD={},QD=0,RD=0;
function YD(a,b){}YD.K="internal.addFormInteractionListener";
function eE(a,b){}eE.K="internal.addFormSubmitListener";
function jE(a){}jE.K="internal.addGaSendListener";function kE(a){if(!a)return{};var b=a.Yp;return Mz(b.type,b.index,b.name)}function lE(a){return a?{originatingEntity:kE(a)}:{}};
var nE=function(a,b,c){mE().updateZone(a,b,c)},pE=function(a,b,c,d,e,f){var g=mE();c=c&&Jb(c,oE);for(var h=g.createZone(a,c),l=0;l<b.length;l++){var n=String(b[l]);if(g.registerChild(n,C(5),h)){var p=n,q=a,r=d,u=e,t=f;if(Kb(p,"GTM-"))Fz(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=bB("js",Eb());Fz(p,void 0,!0,{source:1,fromContainerExecution:!0});var x={originatingEntity:u,inheritParentConfig:t};fB(v,q,x);fB(cB(p,r),q,x)}}}return h},mE=function(){return Un("zones",function(){return new qE})},
rE={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},oE={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},qE=function(){this.C={};this.H={};this.M=0};k=qE.prototype;k.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.C[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.Cj],b))return!1;for(var e=0;e<c.tg.length;e++)if(this.H[c.tg[e]].Ee(b))return!0;return!1};k.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.C[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.tg.length;f++){var g=this.H[c.tg[f]];g.Ee(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.Cj],b);return function(l,n){n=n||[];if(!h(l,n))return!1;for(var p=0;p<e.length;++p)if(e[p].M(l,n))return!0;return!1}};k.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.C[a[b]]};k.createZone=function(a,b){var c=String(++this.M);this.H[c]=new sE(a,b);return c};k.updateZone=function(a,
b,c){var d=this.H[a];d&&d.R(b,c)};k.registerChild=function(a,b,c){var d=this.C[a];if(!d&&Tn[a]||!d&&Pj(a)||d&&d.Cj!==b)return!1;if(d)return d.tg.push(c),!1;this.C[a]={Cj:b,tg:[c]};return!0};var sE=function(a,b){this.H=null;this.C=[{eventId:a,Ee:!0}];if(b){this.H={};for(var c=0;c<b.length;c++)this.H[b[c]]=!0}};sE.prototype.R=function(a,b){var c=this.C[this.C.length-1];a<=c.eventId||c.Ee!==b&&this.C.push({eventId:a,Ee:b})};sE.prototype.Ee=function(a){for(var b=this.C.length-1;b>=0;b--)if(this.C[b].eventId<=
a)return this.C[b].Ee;return!1};sE.prototype.M=function(a,b){b=b||[];if(!this.H||rE[a]||this.H[a])return!0;for(var c=0;c<b.length;++c)if(this.H[b[c]])return!0;return!1};function tE(a){var b=Tn.zones;return b?b.getIsAllowedFn(Hj(),a):function(){return!0}}function uE(){var a=Tn.zones;a&&a.unregisterChild(Hj())}
function vE(){yA(Gj(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Tn.zones;return c?c.isActive(Hj(),b):!0});wA(Gj(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return tE(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var wE=function(a,b){this.tagId=a;this.canonicalId=b};
function xE(a,b){var c=this;if(!Bh(a)||!uh(b)&&!wh(b))throw K(this.getName(),["string","Object|undefined"],arguments);var d=B(b,this.J,1)||{},e=d.firstPartyUrl,f=d.onLoad,g=d.loadByDestination===!0,h=d.isGtmEvent===!0;rD([function(){L(c,"load_google_tags",a,e)}]);if(g){if(Qj(a))return a}else if(Pj(a))return a;var l=6,n=vD(this);h&&(l=7);n.Jb()==="__zone"&&(l=1);var p={source:l,fromContainerExecution:!0},q=function(r){wA(r,function(u){for(var t=
xA().getExternalRestrictions(0,Gj()),v=m(t),x=v.next();!x.done;x=v.next()){var y=x.value;if(!y(u))return!1}return!0},!0);yA(r,function(u){for(var t=xA().getExternalRestrictions(1,Gj()),v=m(t),x=v.next();!x.done;x=v.next()){var y=x.value;if(!y(u))return!1}return!0},!0);f&&f(new wE(a,r))};g?Iz(a,e,p,q):Fz(a,e,!Kb(a,"GTM-"),p,q);f&&n.Jb()==="__zone"&&pE(Number.MIN_SAFE_INTEGER,[a],null,{},kE(vD(this)));return a}xE.K="internal.loadGoogleTag";function yE(a){return new Ad("",function(b){var c=this.evaluate(b);if(c instanceof Ad)return new Ad("",function(){var d=Fa.apply(0,arguments),e=this,f=td(vD(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(l){return e.evaluate(l)}),h=this.J.lb();h.Od(f);return c.Kb.apply(c,[h].concat(Ba(g)))})})};function zE(a,b,c){var d=this;}zE.K="internal.addGoogleTagRestriction";var AE={},BE=[];
function IE(a,b){}
IE.K="internal.addHistoryChangeListener";function JE(a,b,c){}JE.publicName="addWindowEventListener";function KE(a,b){return!0}KE.publicName="aliasInWindow";function LE(a,b,c){}LE.K="internal.appendRemoteConfigParameter";function ME(a){var b;return b}
ME.publicName="callInWindow";function NE(a){}NE.publicName="callLater";function OE(a){}OE.K="callOnDomReady";function PE(a){}PE.K="callOnWindowLoad";function QE(a,b){var c;return c}QE.K="internal.computeGtmParameter";function RE(a,b){var c=this;}RE.K="internal.consentScheduleFirstTry";function SE(a,b){var c=this;}SE.K="internal.consentScheduleRetry";function TE(a){var b;return b}TE.K="internal.copyFromCrossContainerData";function UE(a,b){var c;if(!Bh(a)||!Gh(b)&&b!==null&&!wh(b))throw K(this.getName(),["string","number|undefined"],arguments);L(this,"read_data_layer",a);c=(b||2)!==2?ep(a,1):gp(a,[w,z]);var d=Id(c,this.J,Rh(vD(this).Jb())?2:1);d===void 0&&c!==void 0&&P(45);return d}UE.publicName="copyFromDataLayer";
function VE(a){var b=void 0;return b}VE.K="internal.copyFromDataLayerCache";function WE(a){var b;if(!Bh(a))throw K(this.getName(),["string"],arguments);L(this,"access_globals","read",a);var c=a.split("."),d=Lb(w,c,[w,z]);if(!d)return;var e=d[c[c.length-1]];b=Id(e,this.J,2);b===void 0&&e!==void 0&&P(45);return b}WE.publicName="copyFromWindow";function XE(a){var b=void 0;return Id(b,this.J,1)}XE.K="internal.copyKeyFromWindow";var YE=function(a){return a===hl.Z.Ga&&zl[a]===gl.Ja.ue&&!xn(N.m.aa)};var ZE=function(){return"0"},$E=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];H(102)&&b.push("gbraid");return tj(a,b,"0")};var aF={},bF={},cF={},dF={},eF={},fF={},gF={},hF={},iF={},jF={},kF={},lF={},mF={},nF={},oF={},pF={},qF={},rF={},sF={},tF={},uF={},vF={},wF={},xF={},yF={},zF={},AF=(zF[N.m.Ma]=(aF[2]=[YE],aF),zF[N.m.wf]=(bF[2]=[YE],bF),zF[N.m.hf]=(cF[2]=[YE],cF),zF[N.m.nl]=(dF[2]=[YE],dF),zF[N.m.ol]=(eF[2]=[YE],eF),zF[N.m.pl]=(fF[2]=[YE],fF),zF[N.m.ql]=(gF[2]=[YE],gF),zF[N.m.rl]=(hF[2]=[YE],hF),zF[N.m.Eb]=(iF[2]=[YE],iF),zF[N.m.xf]=(jF[2]=[YE],jF),zF[N.m.yf]=(kF[2]=[YE],kF),zF[N.m.zf]=(lF[2]=[YE],lF),zF[N.m.Af]=(mF[2]=
[YE],mF),zF[N.m.Bf]=(nF[2]=[YE],nF),zF[N.m.Cf]=(oF[2]=[YE],oF),zF[N.m.Df]=(pF[2]=[YE],pF),zF[N.m.Ef]=(qF[2]=[YE],qF),zF[N.m.rb]=(rF[1]=[YE],rF),zF[N.m.bd]=(sF[1]=[YE],sF),zF[N.m.hd]=(tF[1]=[YE],tF),zF[N.m.ce]=(uF[1]=[YE],uF),zF[N.m.Se]=(vF[1]=[function(a){return H(102)&&YE(a)}],vF),zF[N.m.jd]=(wF[1]=[YE],wF),zF[N.m.za]=(xF[1]=[YE],xF),zF[N.m.Xa]=(yF[1]=[YE],yF),zF),BF={},CF=(BF[N.m.rb]=ZE,BF[N.m.bd]=ZE,BF[N.m.hd]=ZE,BF[N.m.ce]=ZE,BF[N.m.Se]=ZE,BF[N.m.jd]=function(a){if(!sd(a))return{};var b=td(a,
null);delete b.match_id;return b},BF[N.m.za]=$E,BF[N.m.Xa]=$E,BF),DF={},EF={},FF=(EF[R.A.Ra]=(DF[2]=[YE],DF),EF),GF={};var HF=function(a,b,c,d){this.C=a;this.M=b;this.R=c;this.U=d};HF.prototype.getValue=function(a){a=a===void 0?hl.Z.Gb:a;if(!this.M.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.U(this.C):this.C};HF.prototype.H=function(){return qd(this.C)==="array"||sd(this.C)?td(this.C,null):this.C};
var IF=function(){},JF=function(a,b){this.conditions=a;this.C=b},KF=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new HF(c,e,g,a.C[b]||IF)},LF,MF;var NF,OF=!1;function PF(){OF=!0;var a=!1;if(H(218)&&Bg(52,a,!1))NF=productSettings,productSettings=void 0;else{}NF=NF||{}}function QF(a){OF||PF();return NF[a]};var RF=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=m(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;V(this,g,d[g])}},Eu=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,U(a,R.A.Rf))},W=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(LF!=null||(LF=new JF(AF,CF)),e=KF(LF,b,c));d[b]=e};
RF.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return W(this,a,b),!0;if(!sd(c))return!1;W(this,a,oa(Object,"assign").call(Object,c,b));return!0};var SF=function(a,b){b=b===void 0?{}:b;for(var c=m(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
RF.prototype.copyToHitData=function(a,b,c){var d=Q(this.D,a);d===void 0&&(d=b);if(rb(d)&&c!==void 0&&H(92))try{d=c(d)}catch(e){}d!==void 0&&W(this,a,d)};
var U=function(a,b){var c=a.metadata[b];if(b===R.A.Rf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,U(a,R.A.Rf))},V=function(a,b,c){var d=a.metadata,e;c===void 0?e=c:(MF!=null||(MF=new JF(FF,GF)),e=KF(MF,b,c));d[b]=e},TF=function(a,b){b=b===void 0?{}:b;for(var c=m(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},Uu=function(a,b,c){var d=QF(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c},UF=function(a){for(var b=new RF(a.target,a.eventName,a.D),c=SF(a),d=m(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;W(b,f,c[f])}for(var g=TF(a),h=m(Object.keys(g)),l=h.next();!l.done;l=h.next()){var n=l.value;V(b,n,g[n])}b.isAborted=a.isAborted;return b},VF=function(a){var b=a.D,c=b.eventId,d=b.priorityId;return d?c+"_"+d:String(c)};
RF.prototype.accept=function(){var a=Nl(Il.X.mi,{}),b=VF(this),c=this.target.destinationId;a[b]||(a[b]={});a[b][c]=Gj();var d=Il.X.mi;if(Jl(d)){var e;(e=Kl(d))==null||e.notify()}};RF.prototype.hasBeenAccepted=function(a){var b=Ml(Il.X.mi);if(!b)return!1;var c=b[VF(this)];return c?c[a!=null?a:this.target.destinationId]!==void 0:!1};function WF(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Eu(a,b)},setHitData:function(b,c){W(a,b,c)},setHitDataIfNotDefined:function(b,c){Eu(a,b)===void 0&&W(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return U(a,b)},setMetadata:function(b,c){V(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return Q(a.D,b)},mb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return sd(c)?a.mergeHitDataForKey(b,c):!1},accept:function(){a.accept()},hasBeenAccepted:function(b){return a.hasBeenAccepted(b)}}};function XF(a,b){var c;return c}XF.K="internal.copyPreHit";function YF(a,b){var c=null;if(!Bh(a)||!Bh(b))throw K(this.getName(),["string","string"],arguments);L(this,"access_globals","readwrite",a);L(this,"access_globals","readwrite",b);var d=[w,z],e=a.split("."),f=Lb(w,e,d),g=e[e.length-1];if(f===void 0)throw Error("Path "+a+" does not exist.");var h=f[g];if(h)return qb(h)?Id(h,this.J,2):null;var l;h=function(){if(!qb(l.push))throw Error("Object at "+b+" in window is not an array.");l.push.call(l,
arguments)};f[g]=h;var n=b.split("."),p=Lb(w,n,d),q=n[n.length-1];if(p===void 0)throw Error("Path "+n+" does not exist.");l=p[q];l===void 0&&(l=[],p[q]=l);c=function(){h.apply(h,Array.prototype.slice.call(arguments,0))};return Id(c,this.J,2)}YF.publicName="createArgumentsQueue";function ZF(a){return Id(function(c){var d=Wz();if(typeof c==="function")d(function(){c(function(f,g,h){var l=
Wz(),n=l&&l.getByName&&l.getByName(f);return(new w.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.J,1)}ZF.K="internal.createGaCommandQueue";function $F(a){if(!Bh(a))throw K(this.getName(),["string"],arguments);L(this,"access_globals","readwrite",a);var b=a.split("."),c=Lb(w,b,[w,z]),d=b[b.length-1];if(!c)throw Error("Path "+a+" does not exist.");var e=c[d];e===void 0&&(e=[],c[d]=e);return Id(function(){if(!qb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.J,
Rh(vD(this).Jb())?2:1)}$F.publicName="createQueue";function aG(a,b){var c=null;if(!Bh(a)||!Ch(b))throw K(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new Fd(new RegExp(a,d))}catch(e){}return c}aG.K="internal.createRegex";function bG(a){}bG.K="internal.declareConsentState";function cG(a){var b="";return b}cG.K="internal.decodeUrlHtmlEntities";function dG(a,b,c){var d;return d}dG.K="internal.decorateUrlWithGaCookies";function eG(){}eG.K="internal.deferCustomEvents";function fG(a){return gG?z.querySelector(a):null}
function hG(a,b){if(!gG)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var iG=!1;
if(z.querySelectorAll)try{var jG=z.querySelectorAll(":root");jG&&jG.length==1&&jG[0]==z.documentElement&&(iG=!0)}catch(a){}var gG=iG;function kG(){var a=w.screen;return{width:a?a.width:0,height:a?a.height:0}}
function lG(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!w.getComputedStyle)return!0;var c=w.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=w.getComputedStyle(d,null))}return!1}function MH(a){var b;return b}MH.K="internal.detectUserProvidedData";
var PH=function(a){var b=Wc(a,["button","input"],50);if(!b)return null;var c=String(b.tagName).toLowerCase();if(c==="button")return b;if(c==="input"){var d=Tc(b,"type");if(d==="button"||d==="submit"||d==="image"||d==="file"||d==="reset")return b}return null},QH=function(a,b,c){var d=c.target;if(d){var e=yD(a,"individualElementIds",[]);if(e.length>0){var f=DD(d,b,e);NB(f)}var g=!1,h=yD(a,"commonButtonIds",[]);if(h.length>0){var l=PH(d);if(l){var n=DD(l,b,h);NB(n);g=!0}}var p=yD(a,"selectorToTriggerIds",
{}),q;for(q in p)if(p.hasOwnProperty(q)){var r=g?p[q].filter(function(v){return h.indexOf(v)===-1}):p[q];if(r.length!==0){var u=hG(d,q);if(u){var t=DD(u,b,r);NB(t)}}}}};
function RH(a,b){if(!vh(a))throw K(this.getName(),["Object|undefined","any"],arguments);var c=a?B(a):{},d=Bb(c.matchCommonButtons),e=!!c.cssSelector,f=xD(b);L(this,"detect_click_events",c.matchCommonButtons,c.cssSelector);var g=c.useV2EventName?"gtm.click-v2":"gtm.click",h=c.useV2EventName?"ecl":"cl",l=function(p){p.push(f);return p};if(e||d){if(d&&CD(h,"commonButtonIds",l,[]),e){var n=Db(String(c.cssSelector));CD(h,"selectorToTriggerIds",
function(p){p.hasOwnProperty(n)||(p[n]=[]);l(p[n]);return p},{})}}else CD(h,"individualElementIds",l,[]);AD(h,function(){Qc(z,"click",function(p){QH(h,g,p)},!0)});return f}RH.K="internal.enableAutoEventOnClick";
function ZH(a,b){return p}ZH.K="internal.enableAutoEventOnElementVisibility";function $H(){}$H.K="internal.enableAutoEventOnError";var aI={},bI=[],cI={},dI=0,eI=0;
function kI(a,b){var c=this;return d}kI.K="internal.enableAutoEventOnFormInteraction";
function pI(a,b){var c=this;return f}pI.K="internal.enableAutoEventOnFormSubmit";
function uI(){var a=this;}uI.K="internal.enableAutoEventOnGaSend";var vI={},wI=[];
function DI(a,b){var c=this;return f}DI.K="internal.enableAutoEventOnHistoryChange";var EI=["http://","https://","javascript:","file://"];
var FI=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=ed(b,"href");if(c.indexOf(":")!==-1&&!EI.some(function(h){return Kb(c,h)}))return!1;var d=c.indexOf("#"),e=ed(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=pj(sj(c)),g=pj(sj(w.location.href));return f!==g}return!0},GI=function(a,b){for(var c=mj(sj((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||ed(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},HI=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.C||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Wc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=yD("lcl",e?"nv.mwt":"mwt",0),g;g=e?yD("lcl","nv.ids",[]):yD("lcl","ids",[]);for(var h=[],l=0;l<g.length;l++){var n=g[l],p=yD("lcl","aff.map",{})[n];p&&!GI(p,d)||h.push(n)}if(h.length){var q=FI(c,d),r=DD(d,"gtm.linkClick",
h);r["gtm.elementText"]=Uc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var u=!!ub(String(ed(d,"rel")||"").split(" "),function(y){return y.toLowerCase()==="noreferrer"}),t=w[(ed(d,"target")||"_self").substring(1)],v=!0,x=OB(function(){var y;if(y=v&&t){var A;a:if(u){var D;try{D=new MouseEvent(c.type,{bubbles:!0})}catch(E){if(!z.createEvent){A=!1;break a}D=z.createEvent("MouseEvents");D.initEvent(c.type,!0,!0)}D.C=!0;c.target.dispatchEvent(D);A=!0}else A=!1;y=!A}y&&(t.location.href=ed(d,
"href"))},f);if(MB(r,x,f))v=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else MB(r,function(){},f||2E3);return!0}}}var b=0;Qc(z,"click",a,!1);Qc(z,"auxclick",a,!1)};
function II(a,b){var c=this;if(!vh(a))throw K(this.getName(),["Object|undefined","any"],arguments);var d=B(a);rD([function(){L(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=xD(b);if(e){var l=Number(d.waitForTagsTimeout);l>0&&isFinite(l)||(l=2E3);var n=function(q){return Math.max(l,q)};CD("lcl","mwt",n,0);f||CD("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};CD("lcl","ids",p,[]);f||CD("lcl","nv.ids",p,[]);g&&CD("lcl","aff.map",function(q){q[h]=g;return q},{});yD("lcl","init",!1)||(HI(),zD("lcl","init",!0));return h}II.K="internal.enableAutoEventOnLinkClick";var JI,KI;
function VI(a,b){var c=this;return d}VI.K="internal.enableAutoEventOnScroll";function WI(a){return function(){if(a.limit&&a.zj>=a.limit)a.Ah&&w.clearInterval(a.Ah);else{a.zj++;var b=Fb();NB({event:a.eventName,"gtm.timerId":a.Ah,"gtm.timerEventNumber":a.zj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.un,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.un,"gtm.triggers":a.zr})}}}
function XI(a,b){
return f}XI.K="internal.enableAutoEventOnTimer";var tc=Da(["data-gtm-yt-inspected-"]),ZI=["www.youtube.com","www.youtube-nocookie.com"],$I,aJ=!1;
function kJ(a,b){var c=this;return e}kJ.K="internal.enableAutoEventOnYouTubeActivity";aJ=!1;function lJ(a,b){if(!Bh(a)||!vh(b))throw K(this.getName(),["string","Object|undefined"],arguments);var c=b?B(b):{},d=a,e=!1;return e}lJ.K="internal.evaluateBooleanExpression";var mJ;function nJ(a){var b=!1;return b}nJ.K="internal.evaluateMatchingRules";var yJ="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function zJ(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function AJ(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=oa(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function BJ(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function CJ(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function DJ(a){if(!CJ(a))return null;var b=zJ(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(yJ).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var EJ=function(a){var b={};b[N.m.xf]=a.architecture;b[N.m.yf]=a.bitness;a.fullVersionList&&(b[N.m.zf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[N.m.Af]=a.mobile?"1":"0";b[N.m.Bf]=a.model;b[N.m.Cf]=a.platform;b[N.m.Df]=a.platformVersion;b[N.m.Ef]=a.wow64?"1":"0";return b},FJ=function(a){var b=0,c=function(h,l){try{a(h,l)}catch(n){}},d=w,e=AJ(d);if(e)c(e);else{var f=BJ(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),
1E3);var g=d.setTimeout(function(){c.jg||(c.jg=!0,P(106),c(null,Error("Timeout")))},b);f.then(function(h){c.jg||(c.jg=!0,P(104),d.clearTimeout(g),c(h))}).catch(function(h){c.jg||(c.jg=!0,P(105),d.clearTimeout(g),c(null,h))})}else c(null)}},HJ=function(){var a=w;if(CJ(a)&&(GJ=Fb(),!BJ(a))){var b=DJ(a);b&&(b.then(function(){P(95)}),b.catch(function(){P(96)}))}},GJ;function NJ(){var a=w.__uspapi;if(qb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function TJ(a){var b=ob("GTAG_EVENT_FEATURE_CHANNEL");b&&(W(a,N.m.pf,b),mb())};function CK(){return yq(7)&&yq(9)&&yq(10)};function xL(a,b,c,d){}xL.K="internal.executeEventProcessor";function yL(a){var b;if(!Bh(a))throw K(this.getName(),["string"],arguments);L(this,"unsafe_run_arbitrary_javascript");try{var c=w.google_tag_manager;c&&typeof c.e==="function"&&(b=c.e(a))}catch(d){}return Id(b,this.J,1)}yL.K="internal.executeJavascriptString";function zL(a){var b;return b};function AL(a){var b="";return b}AL.K="internal.generateClientId";function BL(a){var b={};return Id(b)}BL.K="internal.getAdsCookieWritingOptions";function CL(a,b){var c=!1;return c}CL.K="internal.getAllowAdPersonalization";function DL(){var a;return a}DL.K="internal.getAndResetEventUsage";function EL(a,b){b=b===void 0?!0:b;var c;return c}EL.K="internal.getAuid";var FL=null;function GL(){var a=new cb;return a}GL.publicName="getContainerVersion";function HL(a,b){b=b===void 0?!0:b;var c;return c}HL.publicName="getCookieValues";function IL(){var a="";return a}IL.K="internal.getCorePlatformServicesParam";function JL(){return um()}JL.K="internal.getCountryCode";function KL(){var a=[];a=Fj();return Id(a)}KL.K="internal.getDestinationIds";function LL(a){var b=new cb;return b}LL.K="internal.getDeveloperIds";function ML(a){var b;return b}ML.K="internal.getEcsidCookieValue";function NL(a,b){var c=null;return c}NL.K="internal.getElementAttribute";function OL(a){var b=null;return b}OL.K="internal.getElementById";function PL(a){var b="";return b}PL.K="internal.getElementInnerText";function QL(a,b){var c=null;return Id(c)}QL.K="internal.getElementProperty";function RL(a){var b;return b}RL.K="internal.getElementValue";function SL(a){var b=0;return b}SL.K="internal.getElementVisibilityRatio";function TL(a){var b=null;return b}TL.K="internal.getElementsByCssSelector";
function UL(a){var b;if(!Bh(a))throw K(this.getName(),["string"],arguments);L(this,"read_event_data",a);var c;a:{var d=a,e=vD(this).originalEventData;if(e){for(var f=e,g={},h={},l={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),u=0;u<r.length;u++){for(var t=r[u].split("."),v=0;v<t.length;v++)n.push(t[v]),v!==t.length-1&&n.push(l);u!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var x=[],y="",A=m(n),D=A.next();!D.done;D=
A.next()){var E=D.value;E===l?(x.push(y),y=""):y=E===g?y+"\\":E===h?y+".":y+E}y&&x.push(y);for(var J=m(x),F=J.next();!F.done;F=J.next()){if(f==null){c=void 0;break a}f=f[F.value]}c=f}else c=void 0}b=Id(c,this.J,1);return b}UL.K="internal.getEventData";function VL(a){var b=null;return b}VL.K="internal.getFirstElementByCssSelector";var WL={};WL.disableUserDataWithoutCcd=H(223);WL.enableDecodeUri=H(92);WL.enableGaAdsConversions=H(122);WL.enableGaAdsConversionsClientId=H(121);WL.enableOverrideAdsCps=H(170);WL.enableUrlDecodeEventUsage=H(139);function XL(){return Id(WL)}XL.K="internal.getFlags";function YL(){var a;return a}YL.K="internal.getGsaExperimentId";function ZL(){return new Fd(BC)}ZL.K="internal.getHtmlId";function $L(a){var b;return b}$L.K="internal.getIframingState";function aM(a,b){var c={};return Id(c)}aM.K="internal.getLinkerValueFromLocation";function bM(){var a=new cb;return a}bM.K="internal.getPrivacyStrings";function cM(a,b){var c;return c}cM.K="internal.getProductSettingsParameter";function dM(a,b){var c;return c}dM.publicName="getQueryParameters";function eM(a,b){var c;return c}eM.publicName="getReferrerQueryParameters";function fM(a){var b="";if(!Ch(a))throw K(this.getName(),["string|undefined"],arguments);L(this,"get_referrer",a);b=oj(sj(z.referrer),a);return b}fM.publicName="getReferrerUrl";function gM(){return vm()}gM.K="internal.getRegionCode";function hM(a,b){var c;return c}hM.K="internal.getRemoteConfigParameter";function iM(){var a=new cb;a.set("width",0);a.set("height",0);return a}iM.K="internal.getScreenDimensions";function jM(){var a="";return a}jM.K="internal.getTopSameDomainUrl";function kM(){var a="";return a}kM.K="internal.getTopWindowUrl";function lM(a){var b="";if(!Ch(a))throw K(this.getName(),["string|undefined"],arguments);L(this,"get_url",a);b=mj(sj(w.location.href),a);return b}lM.publicName="getUrl";function mM(){L(this,"get_user_agent");return zc.userAgent}mM.K="internal.getUserAgent";function nM(){var a;return a?Id(EJ(a)):a}nM.K="internal.getUserAgentClientHints";function uM(){var a=w;return a.gaGlobal=a.gaGlobal||{}}function vM(){var a=uM();a.hid=a.hid||vb();return a.hid}function wM(a,b){var c=uM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};function UM(a){(Rv(a)||fj())&&W(a,N.m.tl,vm()||um());!Rv(a)&&fj()&&W(a,N.m.xi,"::")}function VM(a){if(fj()&&!Rv(a)&&(ym()||W(a,N.m.Vk,!0),H(78))){Pu(a);Qu(a,mo.Gf.Ln,Rm(Q(a.D,N.m.Va)));var b=mo.Gf.Mn;var c=Q(a.D,N.m.zc);Qu(a,b,c===!0?1:c===!1?0:void 0);Qu(a,mo.Gf.Kn,Rm(Q(a.D,N.m.Bb)));Qu(a,mo.Gf.In,Kr(Qm(Q(a.D,N.m.tb)),Qm(Q(a.D,N.m.Rb))))}};var pN={AW:Il.X.zn,G:Il.X.To,DC:Il.X.Po};function qN(a){var b=ex(a);return""+ai(b.map(function(c){return c.value}).join("!"))}function rN(a){var b=po(a);return b&&pN[b.prefix]}function sN(a,b){var c=a[b];c&&(c.clearTimerId&&w.clearTimeout(c.clearTimerId),c.clearTimerId=w.setTimeout(function(){delete a[b]},36E5))};var YN=function(a){for(var b={},c=String(XN.cookie).split(";"),d=0;d<c.length;d++){var e=c[d].split("="),f=e[0].trim();if(f&&a(f)){var g=e.slice(1).join("=").trim();g&&(g=decodeURIComponent(g));var h=void 0,l=void 0;((h=b)[l=f]||(h[l]=[])).push(g)}}return b};var ZN=window,XN=document,$N=function(a){var b=ZN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||XN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&ZN["ga-disable-"+a]===!0)return!0;try{var c=ZN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(f){}for(var d=YN(function(f){return f==="AMP_TOKEN"}).AMP_TOKEN||[],e=0;e<d.length;e++)if(d[e]=="$OPT_OUT")return!0;return XN.getElementById("__gaOptOutExtension")?!0:!1};var aO="gclid dclid gclsrc wbraid gbraid gad_source gad_campaignid utm_source utm_medium utm_campaign utm_term utm_content utm_id".split(" ");function bO(){var a=z.location,b,c=a==null?void 0:(b=a.search)==null?void 0:b.replace("?",""),d;if(c){for(var e=[],f=kj(c,!0),g=m(aO),h=g.next();!h.done;h=g.next()){var l=h.value,n=f[l];if(n)for(var p=0;p<n.length;p++){var q=n[p];q!==void 0&&e.push({name:l,value:q})}}d=e}else d=[];return d};
function mO(a){yb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[N.m.Ub]||{};yb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function SO(a,b){}function TO(a,b){var c=function(){};return c}
function UO(a,b,c){}var VO=I.P.qk,WO=I.P.rk;var XO=TO;function YO(a,b){if(H(240)){var c=Fj();c&&c.indexOf(b)>-1&&(a[R.A.Rl]=!0)}}var ZO=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function $O(a,b,c){var d=this;if(!Bh(a)||!vh(b)||!vh(c))throw K(this.getName(),["string","Object|undefined","Object|undefined"],arguments);var e=b?B(b):{};rD([function(){return L(d,"configure_google_tags",a,e)}]);var f=c?B(c):{},g=vD(this);f.originatingEntity=kE(g);fB(cB(a,e),g.eventId,f);}$O.K="internal.gtagConfig";
function bP(a,b){}
bP.publicName="gtagSet";function cP(){var a={};return a};function dP(a){}dP.K="internal.initializeServiceWorker";function eP(a,b){}eP.publicName="injectHiddenIframe";var fP=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function gP(a,b,c,d,e){}gP.K="internal.injectHtml";var kP={};var lP=function(a,b,c,d,e,f){f?e[f]?(e[f][0].push(c),e[f][1].push(d)):(e[f]=[[c],[d]],Lc(a,function(){for(var g=e[f][0],h=0;h<g.length;h++)Sc(g[h]);g.push=function(l){Sc(l);return 0}},function(){for(var g=e[f][1],h=0;h<g.length;h++)Sc(g[h]);e[f]=null},b)):Lc(a,c,d,b)};
function mP(a,b,c,d){if(!(Bh(a)&&yh(b)&&yh(c)&&Ch(d)))throw K(this.getName(),["string","function|undefined","function|undefined","string|undefined"],arguments);L(this,"inject_script",a);var e=this.J;lP(a,void 0,function(){b&&b.Kb(e)},function(){c&&c.Kb(e)},kP,d);}var nP={dl:1,id:1},oP={};
function pP(a,b,c,d){}H(160)?pP.publicName="injectScript":mP.publicName="injectScript";pP.K="internal.injectScript";function qP(){var a=!1;return a}qP.K="internal.isAutoPiiEligible";function rP(a){var b=!0;return b}rP.publicName="isConsentGranted";function sP(a){var b=!1;return b}sP.K="internal.isDebugMode";function tP(){return xm()}tP.K="internal.isDmaRegion";function uP(a){var b=!1;return b}uP.K="internal.isEntityInfrastructure";function vP(a){var b=!1;if(!Gh(a))throw K(this.getName(),["number"],[a]);b=H(a);return b}vP.K="internal.isFeatureEnabled";function wP(){var a=!1;return a}wP.K="internal.isFpfe";function xP(){var a=!1;return a}xP.K="internal.isGcpConversion";function yP(){var a=!1;return a}yP.K="internal.isLandingPage";function zP(){var a=!1;a=Xi;return a}zP.K="internal.isOgt";function AP(){var a;return a}AP.K="internal.isSafariPcmEligibleBrowser";function BP(){var a=fi(function(b){vD(this).log("error",b)});a.publicName="JSON";return a};function CP(a){var b=void 0;if(!Bh(a))throw K(this.getName(),["string"],arguments);b=sj(a);return Id(b)}CP.K="internal.legacyParseUrl";function DP(){return!1}
var EP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function FP(){try{L(this,"logging")}catch(d){return}if(!console)return;for(var a=Array.prototype.slice.call(arguments,0),b=0;b<a.length;b++)a[b]=B(a[b],this.J);var c=vD(this);console.log.apply(console,a);kE(c);}FP.publicName="logToConsole";function GP(a,b){}GP.K="internal.mergeRemoteConfig";function HP(a,b,c){c=c===void 0?!0:c;var d=[];return Id(d)}HP.K="internal.parseCookieValuesFromString";function IP(a){var b=void 0;if(typeof a!=="string")return;a&&Kb(a,"//")&&(a=z.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(x){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],l=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],l]:e[h].push(l):e[h]=l}c=Id({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=sj(a)}catch(x){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var u=q[r].split("="),t=u[0],v=lj(u.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(t)?typeof p[t]==="string"?p[t]=[p[t],v]:p[t].push(v):p[t]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Id(n);
return b}IP.publicName="parseUrl";function JP(a){}JP.K="internal.processAsNewEvent";function KP(a,b,c){var d;return d}KP.K="internal.pushToDataLayer";function LP(a){var b=Fa.apply(1,arguments),c=!1;if(!Bh(a))throw K(this.getName(),["string"],arguments);for(var d=[this,a],e=m(b),f=e.next();!f.done;f=e.next())d.push(B(f.value,this.J,1));try{L.apply(null,d),c=!0}catch(g){return!1}return c}LP.publicName="queryPermission";function MP(a){var b=this;}MP.K="internal.queueAdsTransmission";function NP(a){var b=void 0;return b}NP.publicName="readAnalyticsStorage";function OP(){var a="";return a}OP.publicName="readCharacterSet";function PP(){return C(19)}PP.K="internal.readDataLayerName";function QP(){var a="";return a}QP.publicName="readTitle";function RP(a,b){var c=this;}RP.K="internal.registerCcdCallback";function SP(a,b){return!0}SP.K="internal.registerDestination";var TP=["config","event","get","set"];function UP(a,b,c){}UP.K="internal.registerGtagCommandListener";function VP(a,b){var c=!1;return c}VP.K="internal.removeDataLayerEventListener";function WP(a,b){}
WP.K="internal.removeFormData";function XP(){}XP.publicName="resetDataLayer";function YP(a,b,c){var d=void 0;return d}YP.K="internal.scrubUrlParams";function ZP(a){}ZP.K="internal.sendAdsHit";function $P(a,b,c,d){}$P.K="internal.sendGtagEvent";function aQ(a,b,c){}aQ.publicName="sendPixel";function bQ(a,b){}bQ.K="internal.setAnchorHref";function cQ(a){}cQ.K="internal.setContainerConsentDefaults";function dQ(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}dQ.publicName="setCookie";function eQ(a){}eQ.K="internal.setCorePlatformServices";function fQ(a,b){}fQ.K="internal.setDataLayerValue";function gQ(a){}gQ.publicName="setDefaultConsentState";function hQ(a,b){}hQ.K="internal.setDelegatedConsentType";function iQ(a,b){}iQ.K="internal.setFormAction";function jQ(a,b,c){c=c===void 0?!1:c;}jQ.K="internal.setInCrossContainerData";function kQ(a,b,c){return!1}kQ.publicName="setInWindow";function lQ(a,b,c){}lQ.K="internal.setProductSettingsParameter";function mQ(a,b,c){}mQ.K="internal.setRemoteConfigParameter";function nQ(a,b){}nQ.K="internal.setTransmissionMode";function oQ(a,b,c,d){var e=this;}oQ.publicName="sha256";function pQ(a,b,c){}
pQ.K="internal.sortRemoteConfigParameters";function qQ(a){}qQ.K="internal.storeAdsBraidLabels";function rQ(a,b){var c=void 0;return c}rQ.K="internal.subscribeToCrossContainerData";function sQ(a){}sQ.K="internal.taskSendAdsHits";var tQ={},uQ={};tQ.getItem=function(a){var b=null;return b};tQ.setItem=function(a,b){};
tQ.removeItem=function(a){};tQ.clear=function(){};tQ.publicName="templateStorage";function vQ(a,b){var c=!1;return c}vQ.K="internal.testRegex";function wQ(a){var b;return b};function xQ(a,b){}xQ.K="internal.trackUsage";function yQ(a,b){var c;return c}yQ.K="internal.unsubscribeFromCrossContainerData";function zQ(a){}zQ.publicName="updateConsentState";function AQ(a){var b=!1;return b}AQ.K="internal.userDataNeedsEncryption";var BQ;function CQ(a,b,c){BQ=BQ||new qi;BQ.add(a,b,c)}function DQ(a,b){var c=BQ=BQ||new qi;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=qb(b)?Jh(a,b):Kh(a,b)}
function EQ(){return function(a){var b;var c=BQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.J.nb();if(e){var f=!1,g=e.Jb();if(g){Rh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function FQ(){var a=function(c){return void DQ(c.K,c)},b=function(c){return void CQ(c.publicName,c)};b(pD);b(wD);b(KE);b(ME);b(NE);b(UE);b(WE);b(YF);b(BP());b($F);b(GL);b(HL);b(dM);b(eM);b(fM);b(lM);b(bP);b(eP);b(rP);b(FP);b(IP);b(LP);b(OP);b(QP);b(aQ);b(dQ);b(gQ);b(kQ);b(oQ);b(tQ);b(zQ);CQ("Math",Oh());CQ("Object",oi);CQ("TestHelper",si());CQ("assertApi",Lh);CQ("assertThat",Mh);CQ("decodeUri",Sh);CQ("decodeUriComponent",Th);CQ("encodeUri",Uh);CQ("encodeUriComponent",Vh);CQ("fail",$h);CQ("generateRandom",
ci);CQ("getTimestamp",di);CQ("getTimestampMillis",di);CQ("getType",ei);CQ("makeInteger",gi);CQ("makeNumber",hi);CQ("makeString",ii);CQ("makeTableMap",ji);CQ("mock",mi);CQ("mockObject",ni);CQ("fromBase64",zL,!("atob"in w));CQ("localStorage",EP,!DP());CQ("toBase64",wQ,!("btoa"in w));a(oD);a(sD);a(MD);a(YD);a(eE);a(jE);a(zE);a(IE);a(LE);a(OE);a(PE);a(QE);a(RE);a(SE);a(TE);a(VE);a(XE);a(XF);a(ZF);a(aG);a(bG);a(cG);a(dG);a(eG);a(MH);a(RH);a(ZH);a($H);a(kI);a(pI);a(uI);a(DI);a(II);a(VI);a(XI);a(kJ);a(lJ);
a(nJ);a(xL);a(yL);a(AL);a(BL);a(CL);a(DL);a(EL);a(JL);a(KL);a(LL);a(ML);a(NL);a(OL);a(PL);a(QL);a(RL);a(SL);a(TL);a(UL);a(VL);a(XL);a(YL);a(ZL);a($L);a(aM);a(bM);a(cM);a(gM);a(hM);a(iM);a(jM);a(kM);a(nM);a($O);a(dP);a(gP);a(pP);a(qP);a(sP);a(tP);a(uP);a(vP);a(wP);a(xP);a(yP);a(zP);a(AP);a(CP);a(xE);a(GP);a(HP);a(JP);a(KP);a(MP);a(PP);a(RP);a(SP);a(UP);a(VP);a(WP);a(YP);a(ZP);a($P);a(bQ);a(cQ);a(eQ);a(fQ);a(hQ);a(iQ);a(jQ);a(lQ);a(mQ);a(nQ);a(pQ);a(qQ);a(rQ);a(sQ);a(vQ);a(xQ);a(yQ);a(AQ);DQ("internal.IframingStateSchema",
cP());DQ("internal.quickHash",bi);
H(104)&&a(IL);H(160)?b(pP):b(mP);H(177)&&b(NP);return EQ()};var mD;
function GQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;mD=new bf;HQ();Jf=lD();var e=mD,f=FQ(),g=new Bd("require",f);g.Sa();e.C.C.set("require",g);Ya.set("require",g);for(var h=[],l=0;l<c.length;l++){var n=c[l];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[l]&&d[l].length&&dg(n,d[l]);try{mD.execute(n),H(120)&&jk&&n[0]===50&&h.push(n[1])}catch(r){}}H(120)&&(Wf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");cj[q]=["sandboxedScripts"]}IQ(b)}function HQ(){mD.Uc(function(a,b,c){Tn.SANDBOXED_JS_SEMAPHORE=Tn.SANDBOXED_JS_SEMAPHORE||0;Tn.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Tn.SANDBOXED_JS_SEMAPHORE--}})}function IQ(a){a&&yb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");cj[e]=cj[e]||[];cj[e].push(b)}})};function JQ(a){fB(aB("developer_id."+a,!0),0,{})};var KQ=Array.isArray;function LQ(a,b){return td(a,b||null)}function Y(a){return window.encodeURIComponent(a)}function MQ(a,b,c){Pc(a,b,c)}
function NQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=mj(sj(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function OQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function PQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=OQ(b,"parameter","parameterValue");e&&(c=LQ(e,c))}return c}function QQ(a,b,c){return a===void 0||a===c?b:a}function RQ(){try{if(!H(243))return null;var a=[],b;a:{try{b=!!fG('script[data-requiremodule^="mage/"]');break a}catch(g){}b=!1}b&&a.push("ac");var c;a:{try{c=!!fG('script[src^="//assets.squarespace.com/"]');break a}catch(g){}c=!1}c&&a.push("sqs");var d;a:{try{d=!!fG('script[id="d-js-core"]');break a}catch(g){}d=!1}d&&a.push("dud");var e;a:{try{e=!!fG('script[src*="woocommerce"],link[href*="woocommerce"],[class|="woocommerce"]');break a}catch(g){}e=!1}e&&a.push("woo");var f;a:{try{f=!!fG('meta[content*="fourthwall"],script[src*="fourthwall"],link[href*="fourthwall"]');
break a}catch(g){}f=!1}f&&a.push("fw");if(a.length>0)return{plf:a.join(".")}}catch(g){}return null};function SQ(a,b,c){return Lc(a,b,c,void 0)}function TQ(a,b){return ep(a,b||2)}function UQ(a,b){w[a]=b}function VQ(a,b,c){var d=w;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}var WQ={};var Z={securityGroups:{}};

Z.securityGroups.access_globals=["google"],function(){function a(b,c,d){var e={key:d,read:!1,write:!1,execute:!1};switch(c){case "read":e.read=!0;break;case "write":e.write=!0;break;case "readwrite":e.read=e.write=!0;break;case "execute":e.execute=!0;break;default:throw Error("Invalid "+b+" request "+c);}return e}(function(b){Z.__access_globals=b;Z.__access_globals.F="access_globals";Z.__access_globals.isVendorTemplate=!0;Z.__access_globals.priorityOverride=0;Z.__access_globals.isInfrastructure=!1;
Z.__access_globals["5"]=!1})(function(b){for(var c=b.vtp_keys||[],d=b.vtp_createPermissionError,e=[],f=[],g=[],h=0;h<c.length;h++){var l=c[h],n=l.key;l.read&&e.push(n);l.write&&f.push(n);l.execute&&g.push(n)}return{assert:function(p,q,r){if(!rb(r))throw d(p,{},"Key must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else if(q==="readwrite"){if(f.indexOf(r)>-1&&e.indexOf(r)>-1)return}else if(q==="execute"){if(g.indexOf(r)>-1)return}else throw d(p,
{},"Operation must be either 'read', 'write', or 'execute', was "+q);throw d(p,{},"Prohibited "+q+" on global variable: "+r+".");},V:a}})}();
Z.securityGroups.get_referrer=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_referrer=b;Z.__get_referrer.F="get_referrer";Z.__get_referrer.isVendorTemplate=!0;Z.__get_referrer.priorityOverride=0;Z.__get_referrer.isInfrastructure=!1;Z.__get_referrer["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),
b.vtp_query&&c.push("query"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!rb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!rb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},V:a}})}();
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!rb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Zg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},V:a}})}();


Z.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_data_layer=b;Z.__read_data_layer.F="read_data_layer";Z.__read_data_layer.isVendorTemplate=!0;Z.__read_data_layer.priorityOverride=0;Z.__read_data_layer.isInfrastructure=!1;Z.__read_data_layer["5"]=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!rb(g))throw e(f,{},"Keys must be strings.");if(c!=="any"){try{if(Zg(g,
d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},V:a}})}();



Z.securityGroups.gaawe=["google"],function(){function a(f,g,h){for(var l=0;l<g.length;l++)f.hasOwnProperty(g[l])&&(f[g[l]]=h(f[g[l]]))}function b(f,g,h){var l={},n=function(t,v){l[t]=l[t]||v},p=function(t,v,x){x=x===void 0?!1:x;c.push(WO);if(t){l.items=l.items||[];for(var y={},A=0;A<t.length;y={mg:void 0},A++)y.mg={},yb(t[A],function(E){return function(J,F){x&&J==="id"?E.mg.promotion_id=F:x&&J==="name"?E.mg.promotion_name=F:E.mg[J]=F}}(y)),l.items.push(y.mg)}if(v)for(var D in v)d.hasOwnProperty(D)?n(d[D],
v[D]):n(D,v[D])},q;f.vtp_getEcommerceDataFrom==="dataLayer"?(q=f.vtp_gtmCachedValues.eventModel)||(q=f.vtp_gtmCachedValues.ecommerce):(q=f.vtp_ecommerceMacroData,sd(q)&&q.ecommerce&&!q.items&&(q=q.ecommerce));if(sd(q)){var r=!1,u;for(u in q)q.hasOwnProperty(u)&&(r||(c.push(VO),r=!0),u==="currencyCode"?n("currency",q.currencyCode):u==="impressions"&&g===N.m.jc?p(q.impressions,null):u==="promoClick"&&g===N.m.yc?p(q.promoClick.promotions,q.promoClick.actionField,!0):u==="promoView"&&g===N.m.kc?p(q.promoView.promotions,
q.promoView.actionField,!0):e.hasOwnProperty(u)?g===e[u]&&p(q[u].products,q[u].actionField):l[u]=q[u]);LQ(l,h)}}var c=[],d={id:"transaction_id",revenue:"value",list:"item_list_name"},e={click:"select_item",detail:"view_item",add:"add_to_cart",remove:"remove_from_cart",checkout:"begin_checkout",checkout_option:"checkout_option",purchase:"purchase",refund:"refund"};(function(f){Z.__gaawe=f;Z.__gaawe.F="gaawe";Z.__gaawe.isVendorTemplate=!0;Z.__gaawe.priorityOverride=0;Z.__gaawe.isInfrastructure=!1;Z.__gaawe["5"]=
!0})(function(f){var g;g=f.vtp_migratedToV2?String(f.vtp_measurementIdOverride):String(f.vtp_measurementIdOverride||f.vtp_measurementId);if(rb(g)&&g.indexOf("G-")===0){var h=String(f.vtp_eventName),l={};c=[];f.vtp_sendEcommerceData&&(Fm.hasOwnProperty(h)||h==="checkout_option")&&b(f,h,l);var n=f.vtp_eventSettingsVariable;if(n)for(var p in n)n.hasOwnProperty(p)&&(l[p]=n[p]);if(f.vtp_eventSettingsTable){var q=OQ(f.vtp_eventSettingsTable,"parameter","parameterValue"),r;for(r in q)l[r]=q[r]}var u=OQ(f.vtp_eventParameters,
"name","value"),t;for(t in u)u.hasOwnProperty(t)&&(l[t]=u[t]);var v=f.vtp_userDataVariable;v&&(l[N.m.xb]=v);if(l.hasOwnProperty(N.m.Ub)||f.vtp_userProperties){var x=l[N.m.Ub]||{};LQ(OQ(f.vtp_userProperties,"name","value"),x);l[N.m.Ub]=x}var y={originatingEntity:Mz(1,f.vtp_gtmEntityIndex,f.vtp_gtmEntityName)},A={};c.length>0&&(A[R.A.Al]=c);YO(A,g);Object.keys(A).length>0&&(y.eventMetadata=A);a(l,Gm,function(E){return Bb(E)});a(l,Im,function(E){return Number(E)});var D=f.vtp_gtmEventId;y.noGtmEvent=
!0;fB(dB(g,h,l),D,y);Sc(f.vtp_gtmOnSuccess)}else Sc(f.vtp_gtmOnFailure)})}();

Z.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_link_click_events=b;Z.__detect_link_click_events.F="detect_link_click_events";Z.__detect_link_click_events.isVendorTemplate=!0;Z.__detect_link_click_events.priorityOverride=0;Z.__detect_link_click_events.isInfrastructure=!1;Z.__detect_link_click_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.waitForTags)throw d(e,
{},"Prohibited option waitForTags.");},V:a}})}();
Z.securityGroups.load_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,firstPartyUrl:d}}(function(b){Z.__load_google_tags=b;Z.__load_google_tags.F="load_google_tags";Z.__load_google_tags.isVendorTemplate=!0;Z.__load_google_tags.priorityOverride=0;Z.__load_google_tags.isInfrastructure=!1;Z.__load_google_tags["5"]=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_allowFirstPartyUrls||!1,e=b.vtp_allowedFirstPartyUrls||"specific",f=b.vtp_urls||[],g=b.vtp_tagIds||[],h=b.vtp_createPermissionError;
return{assert:function(l,n,p){(function(q){if(!rb(q))throw h(l,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||g.indexOf(q)===-1))throw h(l,{},"Prohibited Tag ID: "+q+".");})(n);(function(q){if(q!==void 0){if(!rb(q))throw h(l,{},"First party URL must be a string.");if(d){if(e==="any")return;if(e==="specific")try{if(qh(sj(q),f))return}catch(r){throw h(l,{},"Invalid first party URL filter.");}}throw h(l,{},"Prohibited first party URL: "+q);}})(p)},V:a}})}();




Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.F="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!rb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!rb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},V:a}})}();

Z.securityGroups.inject_script=["google"],function(){function a(b,c){return{url:c}}(function(b){Z.__inject_script=b;Z.__inject_script.F="inject_script";Z.__inject_script.isVendorTemplate=!0;Z.__inject_script.priorityOverride=0;Z.__inject_script.isInfrastructure=!1;Z.__inject_script["5"]=!1})(function(b){var c=b.vtp_urls||[],d=b.vtp_createPermissionError;return{assert:function(e,f){if(!rb(f))throw d(e,{},"Script URL must be a string.");try{if(qh(sj(f),c))return}catch(g){throw d(e,{},"Invalid script URL filter.");
}throw d(e,{},"Prohibited script URL: "+f);},V:a}})}();
Z.securityGroups.unsafe_run_arbitrary_javascript=["google"],function(){function a(){return{}}(function(b){Z.__unsafe_run_arbitrary_javascript=b;Z.__unsafe_run_arbitrary_javascript.F="unsafe_run_arbitrary_javascript";Z.__unsafe_run_arbitrary_javascript.isVendorTemplate=!0;Z.__unsafe_run_arbitrary_javascript.priorityOverride=0;Z.__unsafe_run_arbitrary_javascript.isInfrastructure=!1;Z.__unsafe_run_arbitrary_javascript["5"]=!1})(function(){return{assert:function(){},V:a}})}();





Z.securityGroups.detect_click_events=["google"],function(){function a(b,c,d){return{matchCommonButtons:c,cssSelector:d}}(function(b){Z.__detect_click_events=b;Z.__detect_click_events.F="detect_click_events";Z.__detect_click_events.isVendorTemplate=!0;Z.__detect_click_events.priorityOverride=0;Z.__detect_click_events.isInfrastructure=!1;Z.__detect_click_events["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e,f){if(e!==void 0&&typeof e!=="boolean")throw c(d,{},"matchCommonButtons must be a boolean.");
if(f!==void 0&&typeof f!=="string")throw c(d,{},"cssSelector must be a string.");},V:a}})}();
Z.securityGroups.logging=["google"],function(){function a(){return{}}(function(b){Z.__logging=b;Z.__logging.F="logging";Z.__logging.isVendorTemplate=!0;Z.__logging.priorityOverride=0;Z.__logging.isInfrastructure=!1;Z.__logging["5"]=!1})(function(b){var c=b.vtp_environments||"debug",d=b.vtp_createPermissionError;return{assert:function(e){var f;if(f=c!=="all"&&!0){var g=!1;f=!g}if(f)throw d(e,{},"Logging is not enabled in all environments");
},V:a}})}();
Z.securityGroups.configure_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,configuration:d}}(function(b){Z.__configure_google_tags=b;Z.__configure_google_tags.F="configure_google_tags";Z.__configure_google_tags.isVendorTemplate=!0;Z.__configure_google_tags.priorityOverride=0;Z.__configure_google_tags.isInfrastructure=!1;Z.__configure_google_tags["5"]=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_tagIds||[],e=b.vtp_createPermissionError;return{assert:function(f,
g){if(!rb(g))throw e(f,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||d.indexOf(g)===-1))throw e(f,{},"Prohibited configuration for Tag ID: "+g+".");},V:a}})}();





var Wn={dataLayer:fp,callback:function(a){bj.hasOwnProperty(a)&&qb(bj[a])&&bj[a]();delete bj[a]},bootstrap:0};Wn.onHtmlSuccess=HC(!0),Wn.onHtmlFailure=HC(!1);
function XQ(){Vn();Nj();Hz();Ib(cj,Z.securityGroups);var a=Jj(Kj()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;fn(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||P(142);DC(),Sf({Bq:function(d){return d===BC},Qp:function(d){return new EC(d)},Cq:function(d){for(var e=!1,f=!1,g=2;g<d.length;g++)e=e||d[g]===8,f=f||d[g]===16;return e&&f},Rq:function(d){var e;if(d===BC)e=d;else{var f=Zn();CC[f]=d;e='google_tag_manager["rm"]["'+Gj()+'"]('+f+")"}return e}});
Vf={Lp:jg}}var YQ=!1;H(218)&&(YQ=Bg(47,YQ));
function rm(){try{if(YQ||!Vj()){Qi();H(218)&&(Eg.C=Bg(50,Eg.C));
H(218)&&(Eg.R=Bg(51,Eg.R));if(H(109)){}Wa[7]=!0;var a=Un("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});on(a);Sn();bD();rq();$n();if(Oj()){C(5);uE();xA().removeExternalRestrictions(Gj());}else{HJ();lo();Tf();Pf=Z;Qf=KC;Tv();GQ();XQ();IC();pm||(om=tm(),om["0"]&&Nl(Il.X.se,JSON.stringify(om)));On();UB();ZA();BB=!1;z.readyState==="complete"?DB():Qc(w,"load",DB);TA();jk&&(np(Ap),w.setInterval(zp,864E5),np(cD),np(kA),np(cy),np(Dp),np(iD),np(vA),H(120)&&(np(pA),np(qA),np(rA)),
eD={},np(fD),Ig(),H(261)&&np(dD));lk&&(dm(),Do(),WB(),eC(),cC(),Wl("bt",String(Eg.H?2:Eg.C?1:0)),Wl("ct",String(Eg.H?0:Eg.C?1:3)),ZB(),bC());zC();nm(1);vE();aj=Fb();Wn.bootstrap=aj;Eg.R&&TB();H(109)&&yy();H(134)&&(typeof w.name==="string"&&Kb(w.name,"web-pixel-sandbox-CUSTOM")&&id()?JQ("dMDg0Yz"):w.Shopify&&(JQ("dN2ZkMj"),id()&&JQ("dNTU0Yz")))}}}catch(b){nm(4),
wp()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Tm(n)&&(l=h.Bl)}function c(){l&&Cc?g(l):a()}if(!w[C(37)]){var d=!1;if(z.referrer){var e=sj(z.referrer);d=oj(e,"host")===C(38)}if(!d){var f=tr(C(39));d=!(!f.length||!f[0].length)}d&&(w[C(37)]=!0,Lc(C(40)))}var g=function(t){var v="GTM",x="GTM";Xi&&(v="OGT",x="GTAG");var y=C(23),A=w[y];A||(A=[],w[y]=A,Lc("https://"+C(3)+"/debug/bootstrap?id="+C(5)+"&src="+x+"&cond="+String(t)+"&gtm="+Rq()));var D={messageType:"CONTAINER_STARTING",data:{scriptSource:Cc,
containerProduct:v,debug:!1,id:C(5),targetRef:{ctid:C(5),isDestination:Ej(),canonicalId:C(6)},aliases:Hj(),destinations:Fj()}};D.data.resume=function(){a()};Cg(2)&&(D.data.initialPublish=!0);A.push(D)},h={Wo:1,Ql:2,km:3,kk:4,Bl:5};h[h.Wo]="GTM_DEBUG_LEGACY_PARAM";h[h.Ql]="GTM_DEBUG_PARAM";h[h.km]="REFERRER";h[h.kk]="COOKIE";h[h.Bl]="EXTENSION_PARAM";var l=void 0,n=void 0,p=mj(w.location,"query",!1,void 0,"gtm_debug");Tm(p)&&(l=h.Ql);if(!l&&z.referrer){var q=sj(z.referrer);oj(q,"host")===C(24)&&(l=
h.km)}if(!l){var r=tr("__TAG_ASSISTANT");r.length&&r[0].length&&(l=h.kk)}l||b();if(!l&&Sm(n)){var u=!1;Qc(z,"TADebugSignal",function(){u||(u=!0,b(),c())},!1);w.setTimeout(function(){u||(u=!0,b(),c())},200)}else c()})(function(){!YQ||tm()["0"]?rm():qm()});

})()

