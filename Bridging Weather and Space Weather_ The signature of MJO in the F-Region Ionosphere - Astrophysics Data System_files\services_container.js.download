define(["js/components/facade","js/components/generic_module","js/mixins/hardened","underscore"],function(e,s,t,r){s=s.extend({initialize:function(e){this._services=r.has(e,"services")?r.clone(e.services):{}},activate:function(){var s=arguments;r.each(r.values(this._services),function(e){r.isObject(e)&&"activate"in e&&e.activate.apply(e,s)})},destroy:function(){for(var e in this._services)this.remove(e)},add:function(e,s){if(this._services.hasOwnProperty(e))throw new Error("The service: "+e+" is already registered, remove it first!");if(!e||!s||!r.isString(e))throw new Error("The key must be a string and the service is an object");this._services[e]=s},remove:function(e,s){var t;return this._services.hasOwnProperty(e)?("destroy"in(t=this._services[e])&&t.destroy(),delete this._services[e],t):null},has:function(e){return this._services.hasOwnProperty(e)},get:function(e){return this._services[e]},getAll:function(){return r.pairs(this._services)}});return r.extend(s.prototype,t,{getHardenedInstance:function(){var e,s,t={};for(s in this._services)e=this._services[s],r.isObject(e)&&"getHardenedInstance"in e&&(t[s]=!0);var i=new this.constructor({services:this._getHardenedInstance(t,this._services)});return this._getHardenedInstance({get:!0,has:!0},i)}}),s});
//# sourceMappingURL=services_container.js.map