if(null==window.BRadded&&(window.BRadded=(()=>{let baseOBJReset={attributes:{session:0,sid:0,ag:null,gd:"",ins:"",instyp:"",insnm:"",tm:{v:null,u:""},bp:null,ps:null,rr:null,lt:[],dx:[],rx:[],ph:[],lth:[],dxh:[],rxh:[],phh:[],daw:null,pn:[]}};function eraseCookie(name){try{document.cookie=name+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"}catch(error){}}let baseOBJ=JSON.parse(JSON.stringify(baseOBJReset));return{updateParameters:function(key,value){try{switch(key=key.trim().toLowerCase()){case"all":baseOBJ.attributes={...baseOBJ.attributes,...value};break;case"session":var sid,expires,date;1==value&&((baseOBJ=JSON.parse(JSON.stringify(baseOBJReset))).attributes.session=1,sid="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(c){var r=16*Math.random()|0;return("x"==c?r:3&r|8).toString(16)}),baseOBJ.attributes.sid=sid,expires="",(date=new Date).setTime(date.getTime()+18e5),expires="; expires="+date.toUTCString(),document.cookie="_docereeSessionContextId="+(sid||"")+expires+"; path=/"),0==value&&(baseOBJ.attributes.session=0,eraseCookie("_docereeSessionContextId"),eraseCookie("_docereeSessionContext"),baseOBJ=JSON.parse(JSON.stringify(baseOBJReset)));break;case"age":baseOBJ.attributes.ag=""+value;break;case"gender":baseOBJ.attributes.gd=""+value;break;case"insurance":baseOBJ.attributes.ins=""+value;break;case"insurancetype":baseOBJ.attributes.instyp=value;break;case"insurancename":baseOBJ.attributes.insnm=value;break;case"temperature":baseOBJ.attributes.tm={v:""+value.v,u:""+value.u};break;case"bp":baseOBJ.attributes.bp=""+value;break;case"pulse":baseOBJ.attributes.ps=""+value;break;case"respiration":baseOBJ.attributes.rr=""+value;break;case"labtest":baseOBJ.attributes.lt=value;break;case"diagnosis":baseOBJ.attributes.dx=value;break;case"prescription":baseOBJ.attributes.rx=value;break;case"pharmacy":baseOBJ.attributes.ph=value;break;case"labtesthistory":baseOBJ.attributes.lth=value;break;case"diagnosishistory":baseOBJ.attributes.dxh=value;break;case"prescriptionhistory":baseOBJ.attributes.rxh=value;break;case"pharmacyhistory":baseOBJ.attributes.phh=value;break;case"daw":baseOBJ.attributes.daw=(value=>{try{return"1"==value.toString()}catch(error){return null}})(value);break;case"pharmacynotes":baseOBJ.attributes.pn=value}}catch(error){console.log("error in updating parameters",error.message)}},objGetter:function(){try{let tempAttributes={};Object.keys(baseOBJ.attributes).map(key=>{"tm"===key&&null==baseOBJ.attributes.tm.v||null==baseOBJ.attributes[key]||""===baseOBJ.attributes[key]||"session"==key||Array.isArray(baseOBJ.attributes[key])&&0==baseOBJ.attributes[key].length||(tempAttributes[key]=baseOBJ.attributes[key])});var baseOBJ2={...baseOBJ,attributes:{...tempAttributes}};return JSON.stringify(baseOBJ2)}catch(error){console.log("error in objGetter",error.message)}},isBRActive:function(){try{return!!docereeAds.getCookie("_docereeSessionContextId")||(eraseCookie("_docereeSessionContext"),!1)}catch(error){console.log("error in isBRActive",error.message)}}}})()),"undefined"==typeof divObserver&&(window.divObserver=(()=>{let observer=null,status=[];function log(msg){console.log(msg)}var getCurrentUTCTime=function(){var d1=new Date;return d1.toUTCString(),Math.floor(d1.getTime()/1e3)},diffPixel=function(id){try{status[id].outTime=(new Date).getTime(),status[id].timer=!1;var difference=status[id].outTime-status[id].timeIn;status[id].outTime=0,status[id].timeIn=0,difference/=1e3,difference=parseInt(difference)}catch(error){log(error.message)}},refresh=id=>{try{var box;status&&status[id]&&(status[id].timeout&&null!=status[id].timeout&&(clearTimeout(status[id].timeout),status[id].timeout=null),status[id].interval&&null!=status[id].interval&&(clearTimeout(status[id].interval),status[id].interval=null),status[id].timeIn&&0<status[id].timeIn&&status[id].timer&&1==status[id].timer&&diffPixel(id),box=document.getElementById(id),observer.unobserve(box))}catch(error){log(error.message)}},pixelFire=(id,time,percent,type=0,std)=>{try{let url=status[id].url;url=(url=(url=0==type?url.replace("_viewTotalTime",time):url.replace("{{VIEWED_TIME}}",time)).replace("{{VIEWED_PERCENTAGE}}",percent)).replace("{{EVENT_CLIENT_TIME}}",getCurrentUTCTime()),(new Image).src=url}catch(error){log(error.message)}};document.addEventListener("visibilitychange",function(event){try{if(document.hidden)try{for(var i in status)refresh(i)}catch(e){log(e.message)}else(()=>{try{for(var i in status){var box=document.getElementById(i);observer.observe(box)}}catch(e){log(e.message)}})()}catch(e){log(e.message)}});let callback=entries=>{try{entries.forEach(entry=>{let id=entry.target.id,timeNow;.5<=entry.intersectionRatio?(0==status[id].mrcFire&&null==status[id].timeout&&(status[id].timeout=setTimeout(()=>{status[id].mrcFire=!0,pixelFire(id,1,50,1,"mrc")},1e3)),0==status[id].timer&&(status[id].timeIn=(new Date).getTime(),status[id].timer=!0)):(null!=status[id].timeout&&(clearTimeout(status[id].timeout),status[id].timeout=null),0<status[id].timeIn&&1==status[id].timer&&diffPixel(id)),entry.intersectionRatio>=status[id].limit&&1==status[id].customEvent?null==status[id].interval&&(timeNow=status[id].time,status[id].interval=setTimeout(()=>{status[id].customEvent=!1,status[id].interval=null},1e3*timeNow)):(clearTimeout(status[id].interval),status[id].interval=null)})}catch(e){log(e.message)}};try{window.IntersectionObserver?observer=new IntersectionObserver(callback,{root:null,threshold:[.5]}):log("IntersectionObserver not supported")}catch(e){log(e.message)}return{register:function(response,elementId){try{var obj,box;response&&response.hasOwnProperty("adViewedURL")&&response.adViewedURL&&((obj={id:elementId,mrcFire:!1,interval:null,limit:response.hasOwnProperty("minViewPercentage")?response.minViewPercentage:null,time:response.hasOwnProperty("minViewTime")?response.minViewTime:null,timeout:null,url:response.adViewedURL,timer:!1,timeIn:0,outTime:0,customEvent:!1}).limit&&obj.time&&50<=obj.limit&&(obj.limit=obj.limit/100,obj.customEvent=!0),box=document.getElementById(elementId),status[elementId]=obj,observer)&&observer.observe(box)}catch(e){log(e.message)}},refresh:refresh}})()),!window.docereeAds||docereeAds.hasOwnProperty("obj")){var storedParams={};try{window.docereeAds&&(storedParams={...docereeAds.obj})}catch(error){onConsole(error.message)}window.docereeAds=function(){let isQA="[[IS_QA_FALSE]]",templateRequired="[[SELF_VALIDATION_ENABLED]]",pixelFireMacro="[[DC_HEADER_FALSE]]",deviceUUIDEnabled="true",isPrebid="false";var BASE_EVENT="https://dai.doceree.com",AD_URL="https://dai.doceree.com/drs/quest",GET_TEMPLATE_URL="https://dai.doceree.com/dop/getHcpSelfValidation",POST_HCP_VALIDATION_STATUS="https://dai.doceree.com/dop/updateHcpSelfValidation";"true"==isQA&&(AD_URL="https://qa-ad-test.doceree.com/drs/quest",BASE_EVENT="https://qa-ad-test.doceree.com",GET_TEMPLATE_URL="https://qa-identity.doceree.com/getHcpSelfValidation",POST_HCP_VALIDATION_STATUS="https://qa-identity.doceree.com/updateHcpSelfValidation");let COUNTRY_FETCH="https://dai.doceree.com/dop/cm",clientId="84",siteId="281",MAX_AD_COUNT=100,cookieval="";var adContainer=[],ie9Versions=!1,platFormContext={},refreshTimer=null,scrollTimer=null,negativeResponsedAdUnits=[];function strExactReplace($str,$search,$replace){try{return(ie9Versions?$str.split($search):$str.split(new RegExp("\\b"+$search+"\\b","gi"))).join($replace)}catch(e){onConsole("Error strExactReplace :"+e)}}let adSlotList=[];var getCurrentUTCTime=function(){var d1=new Date;return d1.toUTCString(),Math.floor(d1.getTime()/1e3)};function getAdUnit(){let firstSlot="";try{0<adSlotList.length&&(firstSlot=adSlotList.shift())}catch(error){}return firstSlot}function getPlatformContext(){var pd=null;try{var x=atob(getCookie("_docereePlatformContext")),pd=JSON.parse(x)}catch(error){}return pd}function returnUrl(){var url="";try{url=window.top.location.href}catch(error){url=""}return url}async function fetchAD(){try{var adunit=getAdUnit();if(adContainer[adunit].adRequested=!0,shouldRefresh(adunit)){adContainer[adunit].adCount=adContainer[adunit].adCount+1;var pd=getPlatformContext(),pageurl=returnUrl(),dvd=checkDeviceDetails(),metakeywords=(kwdMeta=getMetaContent("keywords"))?kwdMeta.split(",").map(k=>k.trim()):[];if(""!=adunit&&adContainer[adunit].adCount<=MAX_AD_COUNT){let obj={...pd,adunit:adunit,userid:getCuratorId(),pageurl:pageurl,mt:{kwd:metakeywords}},br=getSessionContext(),active=BRadded.isBRActive();null!=br&&active&&(obj={...obj,br:br}),dvd&&(obj={...obj,dvd:dvd});var curatorId,tppCookie,datap=await(await fetch(AD_URL,{method:"POST",credentials:"include",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(obj)})).json();datap&&datap.hasOwnProperty("response")&&0<datap.response.length&&(curatorId=getCookie("_curator_id"),tppCookie=getCookie("_doc_tpp2"),(datap.response[0].hasOwnProperty("userId")&&curatorId!==datap.response[0].userId||!tppCookie)&&(setCookie("_curator_id",datap.response[0].userId,365),fireSnycPixel(datap.response[0].userId),setCookie("_doc_tpp2",""+datap.response[0].userId,1)),datap.response[0].hasOwnProperty("isDisplay"))&&1==datap.response[0].isDisplay&&(adContainer[adunit].isDisplay=!0),renderAd(datap)}}else setRefreshTimer(adunit);0<adSlotList.length&&fetchAD()}catch(error){0<adSlotList.length&&fetchAD(),onConsole("error while fetching ad: ",error.message)}}let callback=(entries,observer)=>{try{entries.forEach(entry=>{var target=entry.target;adContainer[target.id].adRequested||entry.isIntersecting&&0<entry.intersectionRatio&&(adSlotList.push(target.id),fetchAD())})}catch(error){onConsole("error in callback")}},reset=id=>{try{adContainer[id]={id:id,isObserved:!0,adRequested:!1,timer:null,adCount:0,isDisplay:!1}}catch(error){onConsole("error in reset function",error)}},observerForAdSlots=new IntersectionObserver(callback,{root:null,rootMargin:"0px",threshold:[.1]}),addAdSlotinObserver=targetEl2=>{try{for(var clname,elm,p,targetEl=Array.from(targetEl2),i=0;i<targetEl.length;i++)"undefined"!=targetEl[i]&&(clname=targetEl[i].className,elm=document.getElementById(targetEl[i].id),p=strExactReplace(clname,"datag","datagob"),adContainer.hasOwnProperty(targetEl[i].id)&&clearTimeout(adContainer[targetEl[i].id].timer),reset(targetEl[i].id),observerForAdSlots.unobserve(elm),observerForAdSlots.observe(elm),elm.className=p)}catch(error){onConsole(error)}},getAdSlotFromPage=(window.addEventListener("hashchange",function(){getAdSlotFromPage()}),scrollDebounceFunction=function(){try{scrollTimer=setTimeout(()=>{isValidationRequired()||getAdSlotFromPage()},1e3)}catch(error){onConsole("err in debounce")}},document.addEventListener("scroll",()=>{null!==scrollTimer&&(clearTimeout(scrollTimer),scrollTimer=null),scrollDebounceFunction()}),()=>{var count=0;try{var datag=document.getElementsByClassName("datag"),count=datag.length;0<datag.length&&addAdSlotinObserver(datag)}catch(error){}return count});function uuid(){try{var uid="",time=(new Date).getTime(),num1=Math.abs(window.location.href.split("").reduce((a,b)=>(a<<5)-a+b.charCodeAt(0)|0,0)),num2=Math.abs(Date.UTC(Math.floor(99*Math.random()))+(new Date).getTime()).toString().split("").reverse().join("").substr(0,5),num3=performance&&performance.now&&Math.floor(performance.now())||Math.floor(999*Math.random());return"DE.V1."+(uid=12==(uid=num1+parseInt(num2)+num3+num3.toString(16)).length?uid:uid.substr(0,12))+"."+time}catch{onConsole(e)}}let renderInFrame=(ad_unit,snippet)=>{try{var adunit=document.getElementById(ad_unit),iframe=(adunit.innerHTML="",document.createElement("iframe"));iframe.style.width="100%",iframe.style.height="100%",iframe.style.border="0",iframe.setAttribute("scrolling","no"),iframe.setAttribute("frameBorder","0"),iframe.style.overflow="hidden",iframe.id="ifr_"+ad_unit,adunit.appendChild(iframe),document.getElementById(iframe.id).onload=function(){this.contentWindow.document.body.style.margin=0,this.contentWindow.document.body.style.padding=0},iframe.contentWindow.document.write(snippet),iframe.contentWindow.document.body.style.margin=0,iframe.contentWindow.document.body.style.padding=0,iframe.contentWindow.document.close()}catch(error){onConsole(error)}},getRenderedURL=url=>url.replace("{{EVENT_CLIENT_TIME}}",getCurrentUTCTime());function shouldRefresh(adunit){var currTime,ts,isBool=!0;try{adContainer[adunit].hasOwnProperty("isDisplay")&&1==adContainer[adunit].isDisplay&&(ts=localStorage.getItem(adunit),currTime=(new Date).getTime(),isBool=!(ts=new Date(""+ts).getTime())||0<currTime-ts)}catch(error){isBool=!0}return isBool}function emptyDiv(id){try{document.getElementById(id).innerHTML=`<p style="justify-content: center;
                align-items: center;
                display: flex;
                color: grey;
                margin: 0px;
                font-weight: 200;
                font-size: 12px;
                font-family: 'Work sans';
                letter-spacing: 2px;
                height: 100%;">Advertisement</p>`}catch(error){onConsole("Error in empty div",error)}}function callbackAd(t,status){try{var adUnit=document.getElementById(t),callback=adUnit.getAttribute("data-cb");if(callback){let ev=eval(callback);"function"==typeof ev?ev(t,status):onConsole("the callback provided is not a function")}}catch(error){onConsole("The registered callback method is not available")}}function setRefreshTimer(t){try{adContainer.hasOwnProperty(t)&&null==adContainer[t].timer&&(clearTimeout(adContainer[t].timer),adContainer[t].timer=setTimeout(function(){try{adContainer[t].timer=null,adContainer[t].adRequested=!1;var element=document.getElementById(t);"visible"==document.visibilityState?element&&(observerForAdSlots.unobserve(element),observerForAdSlots.observe(element)):negativeResponsedAdUnits.push(t)}catch(error){}},3e4))}catch(error){}}function renderAd(data){try{var addata,anchorStart,anchorEnd,snippet,t=null;data.hasOwnProperty("response")&&0<data.response.length&&1==data.response[0].status?(anchorStart=null!=(addata=data.response[0]).clickURL?`<a href="${addata.clickURL}" target="_blank">`:"",anchorEnd=null!=addata.clickURL?"</a>":"",snippet="",addata.imagePath&&(snippet=`<div style="display: flex; justify-content: center; align-items: center; height: 100%;">${""}${anchorStart}<img src=${addata.imagePath} />${anchorEnd}</div>`),addata.script&&(snippet=`<div style="display: flex; justify-content: center; align-items: center; height: 100%;">${""} ${addata.script}</div>`),emptyDiv(t=addata.adUnit),renderInFrame(t,snippet),(new Image).src=getRenderedURL(addata.adRenderURL),divObserver.refresh(t),divObserver.register(data.response[0],t),callbackAd(t,1)):data.hasOwnProperty("response")&&0<data.response.length&&-1==data.response[0].status&&(emptyDiv(t=data.response[0].adUnit),callbackAd(t,-1)),setRefreshTimer(t)}catch(error){onConsole(error)}}function setCookie(name,value,days){var date,expires="";days&&((date=new Date).setTime(date.getTime()+24*days*60*60*1e3),expires="; expires="+date.toUTCString()),document.cookie=name+"="+(value||"")+expires+"; path=/"}function getCookie(name){for(var nameEQ=name+"=",ca=document.cookie.split(";"),i=0;i<ca.length;i++){for(var c=ca[i];" "==c.charAt(0);)c=c.substring(1,c.length);if(0==c.indexOf(nameEQ))return c.substring(nameEQ.length,c.length)}return null}function getMetaContent(metaNameRegex){let meta="";try{var metas=document.getElementsByTagName("meta"),regex=new RegExp(metaNameRegex);for(let i=0;i<metas.length;i++){var m=metas[i],nameAttr=m.getAttribute("name");if(nameAttr&&regex.test(nameAttr)){meta=m.getAttribute("content")||"";break}}}catch(e){onConsole(e)}return meta}document.addEventListener("visibilitychange",function(event){try{"visible"==document.visibilityState?0<negativeResponsedAdUnits.length&&null==refreshTimer&&(refreshTimer=setTimeout(()=>{clearTimeout(refreshTimer),refreshTimer=null,negativeResponsedAdUnits.forEach(ele=>{var element=document.getElementById(ele);ele&&(observerForAdSlots.unobserve(element),observerForAdSlots.observe(element))}),negativeResponsedAdUnits=[]},1e3)):(clearTimeout(refreshTimer),refreshTimer=null)}catch(error){}});let checkSetCurtorId=fn=>{try{fn()}catch(error){}},checkDocereeId=()=>{try{var docereeId=getCookie("_docereeId"),curatorId=getCookie("_curator_id");(docereeId=docereeId&&JSON.parse(docereeId))&&docereeId.hasOwnProperty("platformUid")&&!curatorId?setCookie("_curator_id",curatorId=docereeId.platformUid,365):null==curatorId&&setCookie("_curator_id",curatorId=uuid(),365)}catch(error){}},getCountryBool=async()=>{try{var cmCookie=getCookie("_cm");if(cmCookie){let data=JSON.parse(atob(cmCookie));if(data.hasOwnProperty("1")&&data[1])return!0}else if((data=await getCountry())[1])return!0;return!1}catch(error){return!1}},fireSnycPixel=async curatorId=>{try{var imgUrlSemcast,imgUrlThrottle,imgUrlLiveIntent,liveRampPixel;"true"!=pixelFireMacro&&await getCountryBool()&&(imgUrlSemcast="https://idxgm.rtactivate.com/tagid/159351/?id="+curatorId,(new Image).src=imgUrlSemcast,imgUrlThrottle="https://thrtle.com/insync?vxii_pid=10069&vxii_pdid="+curatorId,(new Image).src=imgUrlThrottle,imgUrlLiveIntent="https://i.liadm.com/s/65864?bidder_id=246517&bidder_uuid="+curatorId,(new Image).src=imgUrlLiveIntent,liveRampPixel="https://idsync.rlcdn.com/713418.gif?partner_uid="+curatorId,(new Image).src=liveRampPixel)}catch(error){}},fetchTPC=async()=>{try{if(!getCookie("_doc_tpp2")&&"true"==isPrebid){let data=await(await fetch(AD_URL,{method:"POST",credentials:"include",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify({userid:getCuratorId(),tps:1})})).json();"-1"!=(data=data.hasOwnProperty("uid")&&data.uid)?(setCookie("_curator_id",data,365),fireSnycPixel(data),setCookie("_doc_tpp2",""+data,1)):setCookie("_doc_tpp2","-1",.04)}}catch(error){console.log(error)}},checkDeviceDetails=()=>{try{var deviceDetails;return"true"==deviceUUIDEnabled&&(getCookie("doc_dev_uuid")||(deviceDetails=createDeviceUUID(),deviceDetails=JSON.stringify(deviceDetails),setCookie("doc_dev_uuid",deviceDetails=btoa(deviceDetails),365),deviceDetails))}catch(error){return!1}};function createDeviceUUID(){try{let source=("undefined"!=typeof navigator&&navigator.userAgent||"").toLowerCase();var isBot=/(\+https:\/\/developers\.google\.com\/\+\/web\/snippet\/|googlebot|baiduspider|gurujibot|yandexbot|slurp|msnbot|bingbot|facebookexternalhit|linkedinbot|twitterbot|slackbot|telegrambot|applebot|pingdom|tumblr|embedly|spbot)/i.test(source);var detect=options=>{try{return options.find(([,r])=>(regex=>{try{return regex.test(source)}catch(e){return!1}})(r))?.[0]||"Unknown"}catch(e){return"Unknown"}};return{deviceType:/smart-tv|smarttv|googletv|appletv|hbbtv|pov_tv|netcast.tv/.test(source)?"SmartTV":/tablet/.test(source)||/ipad/.test(source)?"Tablet":/mobile/.test(source)||/iphone|ipod|android|blackberry|bada|windows phone/.test(source)?"Mobile":/windows|linux|macintosh|cros/.test(source)?"Desktop":"Unknown",browser:detect([["Edge",/edg(e|ios|a)?/i],["PhantomJS",/phantomjs/i],["Konqueror",/konqueror/i],["Amaya",/amaya/i],["Epiphany",/epiphany/i],["SeaMonkey",/seamonkey/i],["Flock",/flock/i],["OmniWeb",/omniweb/i],["Opera",/opera|opr/i],["Chrome",/chrome|crios/i],["Safari",/^((?!chrome|crios).)*safari/i],["WinJs",/msapphost/i],["IE",/msie|trident/i],["Firefox",/firefox/i],["UCBrowser",/ucbrowser/i]]),os:detect([["Windows 10.0",/windows nt 10\.0/],["Windows 8.1",/windows nt 6\.3/],["Windows 8",/windows nt 6\.2/],["Windows 7",/windows nt 6\.1/],["Windows Vista",/windows nt 6\.0/],["Windows XP",/windows nt 5\.1/],["macOS",/os x|macintosh/],["Linux",/linux/],["Android",/android/],["iOS",/iphone|ipad|ipod/],["Chrome OS",/cros/]]),platform:detect([["Microsoft Windows",/windows nt/],["Microsoft Windows Phone",/windows phone/],["Apple Mac",/macintosh/],["Android",/android/],["Blackberry",/blackberry/],["Linux",/linux/],["iPad",/ipad/],["iPhone",/iphone/],["iPod",/ipod/]]),language:("undefined"!=typeof navigator&&(navigator.language||navigator.userLanguage||"unknown"))?.toLowerCase()||"unknown",resolution:"undefined"!=typeof screen?[screen.availWidth,screen.availHeight]:[0,0],colorDepth:"undefined"!=typeof screen&&screen.colorDepth||-1,pixelDepth:"undefined"!=typeof screen&&screen.pixelDepth||-1,cpuCores:"undefined"!=typeof navigator&&navigator.hardwareConcurrency||-1,isBot:isBot}}catch(error){return{}}}function setDocereePlatformContext(userObj){try{void 0!==userObj.email&&(platFormContext.email=userObj.email),void 0!==userObj.firstname&&(platFormContext.firstname=userObj.firstname),void 0!==userObj.lastname&&(platFormContext.lastname=userObj.lastname),void 0!==userObj.mobile&&(platFormContext.mobile=userObj.mobile),void 0!==userObj.specialization&&(platFormContext.specialization=userObj.specialization),void 0!==userObj.organization&&(platFormContext.organization=userObj.organization),void 0!==userObj.hcpid&&(platFormContext.hcpid=userObj.hcpid),void 0!==userObj.dob&&(platFormContext.dob=userObj.dob),void 0!==userObj.gender&&(platFormContext.gender=userObj.gender),void 0!==userObj.city&&(platFormContext.city=userObj.city),void 0!==userObj.state&&(platFormContext.state=userObj.state),void 0!==userObj.country&&(platFormContext.country=userObj.country),void 0!==userObj.zipcode&&(platFormContext.zipcode=userObj.zipcode),void 0!==userObj.hashedhcpid&&(platFormContext.hashedhcpid=userObj.hashedhcpid),void 0!==userObj.hashedemail&&(platFormContext.hashedemail=userObj.hashedemail),void 0!==userObj.hashedmobile&&(platFormContext.hashedmobile=userObj.hashedmobile),void 0!==userObj.userconsent&&(platFormContext.upref=userObj.userconsent),void 0!==userObj.privacytype&&(platFormContext.pcytyp=userObj.privacytype),void 0!==userObj.privacystring&&(platFormContext.pcystr=userObj.privacystring),setCookie("_docereePlatformContext",btoa(JSON.stringify(platFormContext)),1)}catch(error){onConsole(error)}}function storeSessionContext(){try{var expires,date=new Date,b=(date.setTime(date.getTime()+18e5),expires="; expires="+date.toUTCString(),btoa(BRadded.objGetter()));document.cookie="_docereeSessionContext="+b+expires+"; path=/"}catch(error){}}function getSessionContext(){try{return getCookie("_docereeSessionContext")}catch(error){}}function createSessionContext(){var b=getCookie("_docereeSessionContext");null!=b&&(b=JSON.parse(atob(b)),BRadded.updateParameters("all",b.attributes))}function eventCall(status,sid,hcpID,uid){try{let eventAPI=BASE_EVENT+"/drs/nEvent?eType=4&status="+status;uid&&(eventAPI=eventAPI+"&uid="+uid),hcpID&&(eventAPI=eventAPI+"&hid="+hcpID),sid&&0!==sid&&(eventAPI=eventAPI+"&sid="+sid,(new Image).src=eventAPI)}catch(error){onConsole("ERROR in firing event",error.message)}}function add(key,value){try{if("userdetails"!=(key=key.toLowerCase())){if("session"!=key)BRadded.updateParameters(key,value),"diagnosis"!=key&&"prescription"!=key||!BRadded.isBRActive()||("diagnosis"==key&&0,"prescription"==key&&0);else{var hcpID=getPlatformContext().hcpid,uid=getCuratorId();if(1==value&&(BRadded.updateParameters(key,value),eventCall(1,JSON.parse(BRadded.objGetter()).attributes.sid,hcpID,uid)),0==value){let BR=JSON.parse(BRadded.objGetter()),sid=BR.attributes.sid;eventCall(0,sid,hcpID,uid),BRadded.updateParameters(key,value)}}storeSessionContext()}else checkSetCurtorId(()=>{setDocereePlatformContext(value)})}catch(error){}}function getCuratorId(){try{var curatorId=getCookie("_curator_id");return null!==curatorId?curatorId:null}catch(error){onConsole(error.message)}}async function getCountry(){try{var data;return setCookie("_cm",data=(await(await fetch(COUNTRY_FETCH,{method:"GET",credentials:"include",headers:{Accept:"application/json","Content-Type":"application/json"}})).json()).data,1),JSON.parse(atob(data))}catch(error){return{}}}function checkSlot(){setTimeout(()=>{clearInterval(slotInerval)},5e3),slotInerval=setInterval(function(){0<getAdSlotFromPage()&&clearInterval(slotInerval)},500)}function addAsyncValues(){try{0<Object.keys(storedParams).length&&Object.keys(storedParams).map(d=>{add(d,storedParams[d])})}catch(error){}}function isValidationRequired(){var bool=!1;try{"true"!=templateRequired||getCookie("HCnst")||(bool=!0)}catch(error){}return bool}function onConsole(msg){try{new URLSearchParams(window.location.search).forEach(function(value,key){"debugger"==key&&console.log(msg)})}catch(error){}}function register(){try{checkSlot()}catch(error){}}let getTemp2=async data=>{try{let nSC=atob(data.script);var URIFont=encodeURIComponent(data.font);return nSC='<meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0">'+(nSC=(nSC=(nSC=nSC.split("[['FONTURL_VALIDATION']]").join(URIFont||"Arial")).split("[['COLOR_VALIDATION']]").join("#"+data.fontColour)).split("[['FONT_VALIDATION']]").join(data.font))}catch(error){return onConsole("Error in Temp2",error.message),""}},getTemplate=()=>{try{return` <style>
            #doc-overlay {
              display: none;
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.5);
              justify-content: center;
              align-items: center;
              z-index: 999;
              overflow: hidden;
            }
        
            #doc-popup {
                background: #fff;
                border-radius: 10px;
                min-width: 600px;
                height: 236px;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                padding: 10px;
                box-sizing: border-box;
            }

            .top{
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%,-50%);
              }
          
              .bottom{
                position: absolute;
                left: 50%;
                bottom: 0%;
                transform: translate(-50%,0%);
              }

              @media only screen and (max-width:768px){
                #doc-popup{
                    background: #fff;
                    border-radius: 10px;
                    min-width: 900px;
                    height: 500px;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    padding: 15px;
                    box-sizing: border-box;
                }
                .fw-600{
                    font-size:40px
                }
            }

        
           
          </style>        
            <div id="doc-overlay">
                <div id="doc-popup">            
                </div>
            </div>   
            `}catch(error){}};function addFunctionality(data){try{return`
                <script>
                try{
                    function showPopup() {
                        window.top.document.getElementById('doc-overlay').style.display = 'flex';
                      }
                      function setCookie(name, value, days) {
                            var expires = "";
                            if (days) {
                                var date = new Date();
                                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                                expires = "; expires=" + date.toUTCString();
                            }
                            document.cookie = name + "=" + (value || "") + expires + "; path=/";
                        };
                  
                      // Function to hide the popup
                      function hidePopup() {
                        window.top.document.getElementById('doc-overlay').style.display = 'none';
                        window.top.document.body.style.overflow = 'auto'; // Allow scrolling again
                      }

                      function docereeRedirect(link){
                        const anchor = window.top.document.createElement('a');
                        anchor.setAttribute('href',link);
                        anchor.setAttribute('target', '_self')
                        anchor.click();
                      }
                  
                      // Event listener for the cookie settings button
                      document.getElementById('cookie-accept-btn').addEventListener('click', async function() {
                          setCookie('HCnst',1,365);
                          const obj = {
                            'siteId' : ${siteId},
                            'hcpStatus' : 1,
                            'uuid' : '${getCuratorId()}'
                        }
                        const resp = await fetch('${POST_HCP_VALIDATION_STATUS}',{
                            method: 'POST',
                            credentials: 'include',
                            headers: {
                                'Accept': 'application/json',
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(obj)
                        } );
                          if(${!!data.acceptUrl}){
                            docereeRedirect('${data.acceptUrl}');
                          }
                          window.parent.docereeAds.register();
                          hidePopup();
                      });
                      
                      document.getElementById('cookie-decline-btn').addEventListener('click', function() {
                          setCookie('HCnst','0',15);
                          if(${!!data.closeUrl}){
                            docereeRedirect('${data.closeUrl}');
                          }
                          window.parent.docereeAds.register();
                          hidePopup();
                      });
                      
                      document.getElementById('doc-close-btn').addEventListener('click', function() {
                          setCookie('HCnst',-1,0.25);
                          if(${!!data.closeUrl}){
                            docereeRedirect('${data.closeUrl}');
                          }
                          window.parent.docereeAds.register();
                          hidePopup();
                      });

                      showPopup();
                }catch(e){
                    console.log(e);
                }
                
                </script>`}catch(e){console.log(e)}}let checkTemplate=async()=>{try{var html,iframe,temp2,data,obj={siteId:siteId,uuid:getCuratorId()};null!==(data=(await(await fetch(GET_TEMPLATE_URL,{method:"POST",credentials:"include",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(obj)})).json()).data).script?(html=getTemplate(),document.body.innerHTML+=html,(iframe=document.createElement("iframe")).style.width="100%",iframe.style.height="100%",iframe.style.border="0",iframe.setAttribute("scrolling","yes"),iframe.setAttribute("frameBorder","0"),iframe.style.overflow="hidden",iframe.id="ifr_selfValidation",temp2=await getTemp2(data),document.getElementById("doc-popup").appendChild(iframe),iframe.contentWindow.document.write(temp2+addFunctionality(data))):(1==data.valStatus?setCookie("HCnst",1,365):setCookie("HCnst",0,15),checkSlot())}catch(error){onConsole("error in check template",error.message)}};function init(){addAsyncValues(),checkDocereeId(),createSessionContext(),fetchTPC(),isValidationRequired()?window.addEventListener("load",checkTemplate):checkSlot()}return init(),{add:add,getCookie:getCookie,register:register}}()}function getCuratorId(){try{var curatorId=docereeAds.getCookie("_curator_id");return null!==curatorId?curatorId:null}catch(error){}}
