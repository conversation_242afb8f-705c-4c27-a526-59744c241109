// For license information, see `https://assets.adobedtm.com/4a848ae9611a/032db4f73473/launch-a6263b31083f.js`.
window._satellite=window._satellite||{},window._satellite.container={buildInfo:{minified:!0,buildDate:"2025-06-03T14:51:57Z",turbineBuildDate:"2024-08-22T17:32:44Z",turbineVersion:"28.0.0"},environment:{id:"EN3ca599b396364deba81a6e8f25970c1a",stage:"production"},dataElements:{"Visitor - Industry":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.visitor.industry"}},"Order - Payment Method":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.order.paymentMethod"}},"Event - Search Results Click Position":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.search.resultsPosition"}},"Event - Rows Exported":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.export.rows"}},"Search - Results per Page":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.resultsPerPage"}},"Email - Broadlog ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/queryStringParameter.js",settings:{name:"bid",caseInsensitive:!0}},"Visitor - IP Address":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){if(pageData&&pageData.visitor&&pageData.visitor.ipAddress){const e=pageData.visitor.ipAddress.split(".");return 4===e.length?`${e[0]}.${e[1]}.x.x`:pageData.visitor.ipAddress}return""}}},"Event - Survey Score":{modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.survey.score"}},"Page - Section Name":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.sectionName"}},"Search - Details":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.details"}},"Page - Product Name":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return window.pageData&&pageData.page&&pageData.page.productName&&pageData.page.productName.length<3?pageData.page.productName:""}}},"Campaign - ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/queryStringParameter.js",settings:{name:"dgcid",caseInsensitive:!0}},"Page - Website Extensions":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){var e=window.pageData&&pageData.savedEvents&&pageData.savedEvents.wx?pageData.savedEvents.wx.split("|"):[];return window.ga&&e.push("3"),window.pendo&&e.push("4"),window.usabilla_live&&e.push("5"),window.optimizely&&e.push("6"),window.mendeleyWebImporter&&e.push("7"),e.join("|")}}},"Page - Load Time":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.loadTime"}},"Form - Step + Name":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return window.eventData&&eventData.form&&eventData.form.type&&eventData.form.step?eventData.form.type+":"+eventData.form.step:window.pageData&&pageData.form&&pageData.form.type&&pageData.form.step?pageData.form.type+":"+pageData.form.step:""}}},"Event - Navigation Link Name":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.navigationLink.name"}},"Page - Test ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){var e=[];if(window.pageDataTracker){var t=pageDataTracker.getCookie("id_ab");if(t){var a=t.match(/([^:]*:[^:]*:[^:]*):([^:]*)(:[^:]*)?/);a&&(t=a[1]),e.push("cookie-"+t)}}return window.pageData&&pageData.page&&pageData.page.testId&&e.push(pageData.page.testId),e.join("|")}}},"Page - Widget Names":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return window.eventData&&eventData.link&&eventData.link.widgetName?eventData.link.widgetName:window.pageData&&pageData.page&&pageData.page.widgetNames?pageData.page.widgetNames.join("|"):void 0}}},"Search - Sort Type":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.sortType"}},"Page - Identity User":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){var e=[];if(window.pageDataTracker){var t=pageDataTracker.getCookie("id_ab");if(t){var a=t.match(/([^:]*:[^:]*:[^:]*):([^:]*)(:[^:]*)?/);a&&(t=a[3],e.push("cookie-"+(t||"empty")))}}return window.pageData&&pageData.page&&pageData.page.identityUser&&e.push(pageData.page.identityUser),e.join("|")}}},"Visitor - Account Name":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return window.pageData&&pageData.visitor&&pageData.visitor.accountName&&"ae:"!=pageData.visitor.accountName&&"AE:"!=pageData.visitor.accountName?pageData.visitor.accountName:""}}},"Search - Type":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.type"}},"Event - LinkOut Destination":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.linkOut"}},"Event - NPS Score":{modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.nps.score"}},"Search - Criteria":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){var e="";return window.pageData&&(pageData.search&&(e=pageData.search.criteria||""),!e&&pageData.savedEvents&&(e=pageData.savedEvents.autoSuggestSearchTerm||""),!e&&window.eventData&&eventData.search&&eventData.search.criteria&&(e=eventData.search.criteria)),e}}},"Event - LinkOut Referring Product":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.referringProduct"}},"Visitor - Consortium + Account":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return pageDataTracker.getConsortiumAccountId()}}},"Event - Button Type":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.buttonType"}},"Search - Total Results":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.totalResults"}},"Page - Type":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.type"}},"Page - Currency Code":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.currencyCode"}},"Event - Conversion Driver":{defaultValue:"",forceLowerCase:!0,cleanText:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.conversionDriver.name"}},"Page - Do Not Track":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.noTracking"}},"Visitor - Access Type":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){var e="";return window.pageData&&pageData.visitor&&pageData.visitor.accessType&&(e=pageData.visitor.accessType.toLowerCase()),"ae:anon-ip"==e&&(e="ae:anon_ip"),"ae:anon-guest"==e&&(e="ae:anon_guest"),e}}},"Page - Environment":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.environment"}},"Email - Message ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/queryStringParameter.js",settings:{name:"cid",caseInsensitive:!0}},s_blacklist:{modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return"sd:accessbar|sd:accessbar:fta:single-article|sd:accessbar:fta:full-issue"==s.list3&&"cta impression"==s.linkName}}},"Event - GenAI Details":{modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){for(var e=["inputSource","inputOrigin","component","componentVersion"],t=[],a=0;a<e.length;a++){var n=e[a];eventData&&eventData.genAI&&eventData.genAI[n]?t.push(eventData.genAI[n]):t.push("none")}return t.join("^")}}},"Form - Error Type":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.form.errorType"}},"Event - GenAI Input":{forceLowerCase:!0,modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.genAI.input"}},"Event - Updated User Fields":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.user.fieldsUpdated"}},"Page - Error Type":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){if(window.pageData&&pageData.page){if(pageData.page.errorType)return pageData.page.errorType;if(pageData.form&&pageData.form.errorType)return pageData.form.errorType}return""}}},"Visitor - Login Status":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.visitor.loginStatus"}},"Visitor - SIS ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.visitor.sisId"}},"Visitor - Consortium ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.visitor.consortiumId"}},"Order - ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.order.id"}},"Event - Link Referral Name":{defaultValue:"",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.link.referralName"}},"Page - Experimentation User Id":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){var e=[];if(window.pageDataTracker){var t=pageDataTracker.getCookie("id_ab");if(t){var a=t.match(/([^:]*:[^:]*:[^:]*):([^:]*)(:[^:]*)?/);a&&(t=a[2],e.push("cookie-"+(t||"empty")))}}return window.pageData&&pageData.page&&pageData.page.experimentationUserId&&e.push(pageData.page.experimentationUserId),window.pageData&&pageData.page&&pageData.page.experimentationUserID&&e.push(pageData.page.experimentationUserID),e.join("|")}}},"Event - Share Platform":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.sharePlatform"}},"Page - Load Timestamp":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.loadTimestamp"}},"Event - Survey Comment":{modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.survey.comment"}},Source:{modulePath:"core/src/lib/dataElements/constant.js",settings:{value:"sd"}},"Search - Advanced Criteria":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.advancedCriteria"}},"Event - NPS Comment":{modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.nps.comment"}},"Visitor - Details":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.visitor.details"}},"Visitor - Login Success":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.visitor.loginSuccess"}},"Order - Promo Code":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.order.promoCode"}},"Search - Within Content Criteria":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.withinContentCriteria"}},"Event - Action Name":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.action.name"}},"Page - Language":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.language"}},"Event - Survey Meta Data":{modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){for(var e=["type","targeting","question"],t=[],a=0;a<e.length;a++){var n=e[a];eventData&&eventData.survey&&eventData.survey[n]?t.push(eventData.survey[n]):t.push("none")}return t.join("^")}}},"Page - Product Application Version":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){var e=[];return window.eventData&&eventData.page&&eventData.page.productAppVersion?e.push(eventData.page.productAppVersion):window.pageData&&pageData.page&&pageData.page.productAppVersion?e.push(pageData.page.productAppVersion):e.push("none"),window.eventData&&eventData.page&&eventData.page.recommenderVersion?e.push(eventData.page.recommenderVersion):window.pageData&&pageData.page&&pageData.page.recommenderVersion?e.push(pageData.page.recommenderVersion):e.push("none"),e.join("^")}}},"Search - Facet List":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return pageDataTracker.getSearchFacets()}}},"Search - Current Page":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.currentPage"}},"Maturity Level":{modulePath:"core/src/lib/dataElements/constant.js",settings:{value:"2"}},"Search - Data Form Criteria":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.dataFormCriteria"}},"Event - GenAI Answer Details":{forceLowerCase:!0,modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.genAI.answerDetails"}},"Page - Journal Info":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return pageDataTracker.getJournalInfo()}}},"Promo - IDs":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){for(var e=document.getElementsByTagName("a"),t=[],a=0;a<e.length;a++){var n=e[a].getAttribute("data-sc-promo-id");n&&t.push(n)}if(window.pageData&&pageData.cta&&pageData.cta.ids)for(a=0;a<pageData.cta.ids.length;a++)t.push(pageData.cta.ids[a]);if(window.eventData&&eventData.cta&&eventData.cta.ids)for(a=0;a<eventData.cta.ids.length;a++)t.push(eventData.cta.ids[a]);return t.join("|")}}},"Visitor - User ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.visitor.userId"}},"Promo - Clicked ID":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return window.clickedPromoId?window.clickedPromoId:""}}},"Event - AutoSuggest Search Term":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.savedEvents.autoSuggestSearchTerm"}},"Visitor - App Session ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){if(window.pageData&&pageData.visitor&&pageData.visitor.appSessionId)return pageData.visitor.appSessionId;var e=pageDataTracker.getCookie("sd_session_id");if(!e){e=navigator.userAgent,window.pageData&&pageData.visitor&&pageData.visitor.ipAddress&&(e+="|"+pageData.visitor.ipAddress);var t=new Date;e+="|",e+=t.getFullYear(),e+=t.getMonth(),e+=t.getDate(),e+=t.getHours()}return e}}},"Search - Feature Used":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.search.featureName"}},blacklisted:{modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){try{var e=["article:view_abstract_link","reaxys-image","article:view_fulltext_link","sd:article:mendeley-extensionlink","sd:article:sdf-singledownload","sd:article:sdf-bulkdownload","sd:article:sdf-morethan50","sd:article:sdf-singledownload:appendix","sd:article:sdf-bulkdownload:appendix","sd:article:sdf-bulkdownload:outline","sd:article:special-issue","sd:article:virtual-special-issue-link","sd:srp:article-list","sd:srp:subscribed-facet","sd:accessbar:another institution","sd:accessbar:patient-access","sd:authors:grantslist:empty"];if("ctaImpression"==eventData.eventName&&e.indexOf(eventData.cta.ids[0])>=0)return!0}catch(e){}return!1}}},"Event - Link Name":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"eventData.link.name"}},"Search - Within Results Criteria":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.search.withinResultsCriteria"}},"Event - Alert Type":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return window.eventData&&eventData.alert&&(eventData.alert.frequency||eventData.alert.type)?(eventData.alert.frequency||"no frequency")+"|"+(eventData.alert.type||"no type"):""}}},"Page - Product Feature":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return window.eventData&&eventData.feature?eventData.feature.name||"":window.pageData&&pageData.feature?pageData.feature.name||"":void 0}}},"Event - AutoSuggest Search Data":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.savedEvents.autoSuggestSearchData"}},"Page - Analytics Pagename":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.analyticsPagename"}},"Search - Result Types":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return pageDataTracker.getSearchResultsByType()}}},serverState:{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"targetServerState"}},"Page - Business Unit":{defaultValue:"",forceLowerCase:!0,storageDuration:"pageview",modulePath:"core/src/lib/dataElements/customCode.js",settings:{source:function(){return(window.pageData&&pageData.page&&pageData.page.businessUnit?pageData.page.businessUnit:"").replace(/RAP\:/g,"RP:")}}},"Visitor - Account ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.visitor.accountId"}},"Email - Recipient ID":{defaultValue:"",storageDuration:"pageview",modulePath:"core/src/lib/dataElements/queryStringParameter.js",settings:{name:"eid",caseInsensitive:!0}},"Page - Test Subject ID":{modulePath:"core/src/lib/dataElements/javascriptVariable.js",settings:{path:"pageData.page.testSubjectId"}}},extensions:{core:{displayName:"Core",hostedLibFilesBaseUrl:"https://assets.adobedtm.com/extensions/EP6a6d85ccbeaa4750848f31959dd9eec5/",modules:{"core/src/lib/dataElements/javascriptVariable.js":{name:"javascript-variable",displayName:"JavaScript Variable",script:function(e,t,a){"use strict";var n=a("../helpers/getObjectProperty.js");e.exports=function(e){return n(window,e.path)}}},"core/src/lib/dataElements/queryStringParameter.js":{name:"query-string-parameter",displayName:"Query String Parameter",script:function(e,t,a){"use strict";var n=a("@adobe/reactor-window"),r=a("@adobe/reactor-query-string");e.exports=function(e){var t=r.parse(n.location.search);if(!e.caseInsensitive)return t[e.name];for(var a=e.name.toLowerCase(),i=Object.keys(t),s=0;s<i.length;s++){var o=i[s];if(o.toLowerCase()===a)return t[o]}}}},"core/src/lib/dataElements/customCode.js":{name:"custom-code",displayName:"Custom Code",script:function(e){"use strict";e.exports=function(e,t){return e.source(t)}}},"core/src/lib/dataElements/constant.js":{name:"constant",displayName:"Constant",script:function(e){"use strict";e.exports=function(e){return e.value}}},"core/src/lib/events/directCall.js":{name:"direct-call",displayName:"Direct Call",script:function(e,t,a,n){"use strict";var r={};window._satellite=window._satellite||{},window._satellite.track=function(e,t){e=e.trim();var a=r[e];if(a){var i={identifier:e,detail:t};a.forEach((function(e){e(i)}));var s=['Rules using the direct call event type with identifier "'+e+'" have been triggered'+(t?" with additional detail:":".")];t&&s.push(t),n.logger.log.apply(n.logger,s)}else n.logger.log('"'+e+'" does not match any direct call identifiers.')},e.exports=function(e,t){var a=r[e.identifier];a||(a=r[e.identifier]=[]),a.push(t)}}},"core/src/lib/conditions/path.js":{name:"path",displayName:"Path Without Query String",script:function(e,t,a){"use strict";var n=a("@adobe/reactor-document"),r=a("../helpers/textMatch");e.exports=function(e){var t=n.location.pathname;return e.paths.some((function(e){var a=e.valueIsRegex?new RegExp(e.value,"i"):e.value;return r(t,a)}))}}},"core/src/lib/actions/customCode.js":{name:"custom-code",displayName:"Custom Code",script:function(e,t,a,n){"use strict";var r,i,s,o,c=a("@adobe/reactor-document"),l=a("@adobe/reactor-promise"),u=a("./helpers/decorateCode"),d=a("./helpers/loadCodeSequentially"),p=a("../../../node_modules/postscribe/dist/postscribe"),g=a("./helpers/unescapeHtmlCode"),f=a("../helpers/findPageScript").getTurbine,m=(i=function(e){p(c.body,e,{beforeWriteToken:function(e){var t=e.tagName&&e.tagName.toLowerCase();return r&&"script"===t&&(e.attrs.nonce=r),"script"!==t&&"style"!==t||(Object.keys(e.attrs||{}).forEach((function(t){e.attrs[t]=g(e.attrs[t])})),e.src&&(e.src=g(e.src))),e},error:function(e){n.logger.error(e.msg)}})},s=[],o=function(){if(c.body)for(;s.length;)i(s.shift());else setTimeout(o,20)},function(e){s.push(e),o()}),v=function(){if(c.currentScript)return c.currentScript.async;var e=f();return!e||e.async}();e.exports=function(e,t){var a;r=n.getExtensionSettings().cspNonce;var i={settings:e,event:t},s=i.settings.source;if(s)return i.settings.isExternal?d(s).then((function(e){return e?(a=u(i,e),m(a.code),a.promise):l.resolve()})):(a=u(i,s),v||"loading"!==c.readyState?m(a.code):c.write&&!1===n.propertySettings.ruleComponentSequencingEnabled?c.write(a.code):m(a.code),a.promise)}}},"core/src/lib/events/libraryLoaded.js":{name:"library-loaded",displayName:"Library Loaded (Page Top)",script:function(e,t,a){"use strict";var n=a("./helpers/pageLifecycleEvents");e.exports=function(e,t){n.registerLibraryLoadedTrigger(t)}}},"core/src/lib/events/customCode.js":{name:"custom-code",displayName:"Custom Code",script:function(e){"use strict";e.exports=function(e,t){e.source(t)}}},"core/src/lib/events/click.js":{name:"click",displayName:"Click",script:function(e,t,a){"use strict";var n=a("@adobe/reactor-window"),r=a("./helpers/createBubbly")(),i=new(a("./helpers/weakMap")),s=2,o=a("../helpers/stringAndNumberUtils").castToNumberIfString,c=function(e){if(!e.ctrlKey&&!e.metaKey&&e.button!==s)for(var t=e.target;t;){var a=t.tagName;if(a&&"a"===a.toLowerCase()){var r=t.getAttribute("href"),i=t.getAttribute("target");return r&&(!i||"_self"===i||"_top"===i&&n.top===n||i===n.name)?t:void 0}t=t.parentNode}};document.addEventListener("click",r.evaluateEvent,!0),e.exports=function(e,t){r.addListener(e,(function(a){var r=a.nativeEvent;if(!r.s_fe){var s=o(e.anchorDelay);if(s&&!i.has(r)){var l=c(r);l&&(r.preventDefault(),setTimeout((function(){n.location=l.href}),s)),i.set(r,!0)}t(a)}}))},e.exports.__reset=r.__reset}},"core/src/lib/helpers/getObjectProperty.js":{script:function(e){"use strict";e.exports=function(e,t){for(var a=t.split("."),n=e,r=0,i=a.length;r<i;r++){if(null==n)return;n=n[a[r]]}return n}}},"core/src/lib/helpers/textMatch.js":{script:function(e){"use strict";e.exports=function(e,t){if(null==t)throw new Error("Illegal Argument: Pattern is not present");return null!=e&&("string"==typeof t?e===t:t instanceof RegExp&&t.test(e))}}},"core/src/lib/actions/helpers/decorateCode.js":{script:function(e,t,a){"use strict";var n=a("./decorators/decorateGlobalJavaScriptCode"),r=a("./decorators/decorateNonGlobalJavaScriptCode"),i={javascript:function(e,t){return e.settings.global?n(e,t):r(e,t)},html:a("./decorators/decorateHtmlCode")};e.exports=function(e,t){return i[e.settings.language](e,t)}}},"core/src/lib/actions/helpers/loadCodeSequentially.js":{script:function(e,t,a){"use strict";var n=a("@adobe/reactor-promise"),r=a("./getSourceByUrl"),i=n.resolve();e.exports=function(e){var t=new n((function(t){var a=r(e);n.all([a,i]).then((function(e){var a=e[0];t(a)}))}));return i=t,t}}},"core/node_modules/postscribe/dist/postscribe.js":{script:function(e,t){var a,n;a=this,n=function(){return function(e){function t(n){if(a[n])return a[n].exports;var r=a[n]={exports:{},id:n,loaded:!1};return e[n].call(r.exports,r,r.exports,t),r.loaded=!0,r.exports}var a={};return t.m=e,t.c=a,t.p="",t(0)}([function(e,t,a){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}var r=n(a(1));e.exports=r.default},function(e,t,a){"use strict";function n(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t.default=e,t}function r(e){return e&&e.__esModule?e:{default:e}}function i(){}function s(){var e=f.shift();if(e){var t=d.last(e);t.afterDequeue(),e.stream=o.apply(void 0,e),t.afterStreamStart()}}function o(e,t,a){function n(e){e=a.beforeWrite(e),m.write(e),a.afterWrite(e)}(m=new u.default(e,a)).id=g++,m.name=a.name||m.id,c.streams[m.name]=m;var r=e.ownerDocument,o={close:r.close,open:r.open,write:r.write,writeln:r.writeln};l(r,{close:i,open:i,write:function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return n(t.join(""))},writeln:function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return n(t.join("")+"\n")}});var d=m.win.onerror||i;return m.win.onerror=function(e,t,n){a.error({msg:e+" - "+t+": "+n}),d.apply(m.win,[e,t,n])},m.write(t,(function(){l(r,o),m.win.onerror=d,a.done(),m=null,s()})),m}function c(e,t,a){if(d.isFunction(a))a={done:a};else if("clear"===a)return f=[],m=null,void(g=0);a=d.defaults(a,p);var n=[e=/^#/.test(e)?window.document.getElementById(e.substr(1)):e.jquery?e[0]:e,t,a];return e.postscribe={cancel:function(){n.stream?n.stream.abort():n[1]=i}},a.beforeEnqueue(n),f.push(n),m||s(),e.postscribe}t.__esModule=!0;var l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e};t.default=c;var u=r(a(2)),d=n(a(4)),p={afterAsync:i,afterDequeue:i,afterStreamStart:i,afterWrite:i,autoFix:!0,beforeEnqueue:i,beforeWriteToken:function(e){return e},beforeWrite:function(e){return e},done:i,error:function(e){throw new Error(e.msg)},releaseAsync:!1},g=0,f=[],m=null;l(c,{streams:{},queue:f,WriteStream:u.default})},function(e,t,a){"use strict";function n(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t.default=e,t}function r(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){var a=p+t,n=e.getAttribute(a);return u.existy(n)?String(n):n}function o(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=p+t;u.existy(a)&&""!==a?e.setAttribute(n,a):e.removeAttribute(n)}t.__esModule=!0;var c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},l=r(a(3)),u=n(a(4)),d=!1,p="data-ps-",g="ps-style",f="ps-script",m=function(){function e(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};i(this,e),this.root=t,this.options=a,this.doc=t.ownerDocument,this.win=this.doc.defaultView||this.doc.parentWindow,this.parser=new l.default("",{autoFix:a.autoFix}),this.actuals=[t],this.proxyHistory="",this.proxyRoot=this.doc.createElement(t.nodeName),this.scriptStack=[],this.writeQueue=[],o(this.proxyRoot,"proxyof",0)}return e.prototype.write=function(){var e;for((e=this.writeQueue).push.apply(e,arguments);!this.deferredRemote&&this.writeQueue.length;){var t=this.writeQueue.shift();u.isFunction(t)?this._callFunction(t):this._writeImpl(t)}},e.prototype._callFunction=function(e){var t={type:"function",value:e.name||e.toString()};this._onScriptStart(t),e.call(this.win,this.doc),this._onScriptDone(t)},e.prototype._writeImpl=function(e){this.parser.append(e);for(var t=void 0,a=void 0,n=void 0,r=[];(t=this.parser.readToken())&&!(a=u.isScript(t))&&!(n=u.isStyle(t));)(t=this.options.beforeWriteToken(t))&&r.push(t);r.length>0&&this._writeStaticTokens(r),a&&this._handleScriptToken(t),n&&this._handleStyleToken(t)},e.prototype._writeStaticTokens=function(e){var t=this._buildChunk(e);return t.actual?(t.html=this.proxyHistory+t.actual,this.proxyHistory+=t.proxy,this.proxyRoot.innerHTML=t.html,d&&(t.proxyInnerHTML=this.proxyRoot.innerHTML),this._walkChunk(),d&&(t.actualInnerHTML=this.root.innerHTML),t):null},e.prototype._buildChunk=function(e){for(var t=this.actuals.length,a=[],n=[],r=[],i=e.length,s=0;s<i;s++){var o=e[s],c=o.toString();if(a.push(c),o.attrs){if(!/^noscript$/i.test(o.tagName)){var l=t++;n.push(c.replace(/(\/?>)/," "+p+"id="+l+" $1")),o.attrs.id!==f&&o.attrs.id!==g&&r.push("atomicTag"===o.type?"":"<"+o.tagName+" "+p+"proxyof="+l+(o.unary?" />":">"))}}else n.push(c),r.push("endTag"===o.type?c:"")}return{tokens:e,raw:a.join(""),actual:n.join(""),proxy:r.join("")}},e.prototype._walkChunk=function(){for(var e=void 0,t=[this.proxyRoot];u.existy(e=t.shift());){var a=1===e.nodeType;if(!a||!s(e,"proxyof")){a&&(this.actuals[s(e,"id")]=e,o(e,"id"));var n=e.parentNode&&s(e.parentNode,"proxyof");n&&this.actuals[n].appendChild(e)}t.unshift.apply(t,u.toArray(e.childNodes))}},e.prototype._handleScriptToken=function(e){var t=this,a=this.parser.clear();a&&this.writeQueue.unshift(a),e.src=e.attrs.src||e.attrs.SRC,(e=this.options.beforeWriteToken(e))&&(e.src&&this.scriptStack.length?this.deferredRemote=e:this._onScriptStart(e),this._writeScriptToken(e,(function(){t._onScriptDone(e)})))},e.prototype._handleStyleToken=function(e){var t=this.parser.clear();t&&this.writeQueue.unshift(t),e.type=e.attrs.type||e.attrs.TYPE||"text/css",(e=this.options.beforeWriteToken(e))&&this._writeStyleToken(e),t&&this.write()},e.prototype._writeStyleToken=function(e){var t=this._buildStyle(e);this._insertCursor(t,g),e.content&&(t.styleSheet&&!t.sheet?t.styleSheet.cssText=e.content:t.appendChild(this.doc.createTextNode(e.content)))},e.prototype._buildStyle=function(e){var t=this.doc.createElement(e.tagName)
;return t.setAttribute("type",e.type),u.eachKey(e.attrs,(function(e,a){t.setAttribute(e,a)})),t},e.prototype._insertCursor=function(e,t){this._writeImpl('<span id="'+t+'"/>');var a=this.doc.getElementById(t);a&&a.parentNode.replaceChild(e,a)},e.prototype._onScriptStart=function(e){e.outerWrites=this.writeQueue,this.writeQueue=[],this.scriptStack.unshift(e)},e.prototype._onScriptDone=function(e){e===this.scriptStack[0]?(this.scriptStack.shift(),this.write.apply(this,e.outerWrites),!this.scriptStack.length&&this.deferredRemote&&(this._onScriptStart(this.deferredRemote),this.deferredRemote=null)):this.options.error({msg:"Bad script nesting or script finished twice"})},e.prototype._writeScriptToken=function(e,t){var a=this._buildScript(e),n=this._shouldRelease(a),r=this.options.afterAsync;e.src&&(a.src=e.src,this._scriptLoadHandler(a,n?r:function(){t(),r()}));try{this._insertCursor(a,f),a.src&&!n||t()}catch(e){this.options.error(e),t()}},e.prototype._buildScript=function(e){var t=this.doc.createElement(e.tagName);return u.eachKey(e.attrs,(function(e,a){t.setAttribute(e,a)})),e.content&&(t.text=e.content),t},e.prototype._scriptLoadHandler=function(e,t){function a(){e=e.onload=e.onreadystatechange=e.onerror=null}function n(){a(),null!=t&&t(),t=null}function r(e){a(),s(e),null!=t&&t(),t=null}function i(e,t){var a=e["on"+t];null!=a&&(e["_on"+t]=a)}var s=this.options.error;i(e,"load"),i(e,"error"),c(e,{onload:function(){if(e._onload)try{e._onload.apply(this,Array.prototype.slice.call(arguments,0))}catch(t){r({msg:"onload handler failed "+t+" @ "+e.src})}n()},onerror:function(){if(e._onerror)try{e._onerror.apply(this,Array.prototype.slice.call(arguments,0))}catch(t){return void r({msg:"onerror handler failed "+t+" @ "+e.src})}r({msg:"remote script failed "+e.src})},onreadystatechange:function(){/^(loaded|complete)$/.test(e.readyState)&&n()}})},e.prototype._shouldRelease=function(e){return!/^script$/i.test(e.nodeName)||!!(this.options.releaseAsync&&e.src&&e.hasAttribute("async"))},e}();t.default=m},function(e){var t;t=function(){return function(e){function t(n){if(a[n])return a[n].exports;var r=a[n]={exports:{},id:n,loaded:!1};return e[n].call(r.exports,r,r.exports,t),r.loaded=!0,r.exports}var a={};return t.m=e,t.c=a,t.p="",t(0)}([function(e,t,a){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}var r=n(a(1));e.exports=r.default},function(e,t,a){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t.default=e,t}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0;var s=r(a(2)),o=r(a(3)),c=n(a(6)),l=a(5),u={comment:/^<!--/,endTag:/^<\//,atomicTag:/^<\s*(script|style|noscript|iframe|textarea)[\s\/>]/i,startTag:/^</,chars:/^[^<]/},d=function(){function e(){var t=this,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};i(this,e),this.stream=a;var r=!1,o={};for(var l in s)s.hasOwnProperty(l)&&(n.autoFix&&(o[l+"Fix"]=!0),r=r||o[l+"Fix"]);r?(this._readToken=(0,c.default)(this,o,(function(){return t._readTokenImpl()})),this._peekToken=(0,c.default)(this,o,(function(){return t._peekTokenImpl()}))):(this._readToken=this._readTokenImpl,this._peekToken=this._peekTokenImpl)}return e.prototype.append=function(e){this.stream+=e},e.prototype.prepend=function(e){this.stream=e+this.stream},e.prototype._readTokenImpl=function(){var e=this._peekTokenImpl();if(e)return this.stream=this.stream.slice(e.length),e},e.prototype._peekTokenImpl=function(){for(var e in u)if(u.hasOwnProperty(e)&&u[e].test(this.stream)){var t=o[e](this.stream);if(t)return"startTag"===t.type&&/script|style/i.test(t.tagName)?null:(t.text=this.stream.substr(0,t.length),t)}},e.prototype.peekToken=function(){return this._peekToken()},e.prototype.readToken=function(){return this._readToken()},e.prototype.readTokens=function(e){for(var t=void 0;t=this.readToken();)if(e[t.type]&&!1===e[t.type](t))return},e.prototype.clear=function(){var e=this.stream;return this.stream="",e},e.prototype.rest=function(){return this.stream},e}();for(var p in t.default=d,d.tokenToString=function(e){return e.toString()},d.escapeAttributes=function(e){var t={};for(var a in e)e.hasOwnProperty(a)&&(t[a]=(0,l.escapeQuotes)(e[a],null));return t},d.supports=s,s)s.hasOwnProperty(p)&&(d.browserHasFlaw=d.browserHasFlaw||!s[p]&&p)},function(e,t){"use strict";t.__esModule=!0;var a=!1,n=!1,r=window.document.createElement("div");try{var i="<P><I></P></I>";r.innerHTML=i,t.tagSoup=a=r.innerHTML!==i}catch(e){t.tagSoup=a=!1}try{r.innerHTML="<P><i><P></P></i></P>",t.selfClose=n=2===r.childNodes.length}catch(e){t.selfClose=n=!1}r=null,t.tagSoup=a,t.selfClose=n},function(e,t,a){"use strict";function n(e){var t=e.indexOf("-->");if(t>=0)return new l.CommentToken(e.substr(4,t-1),t+3)}function r(e){var t=e.indexOf("<");return new l.CharsToken(t>=0?t:e.length)}function i(e){var t,a,n;if(-1!==e.indexOf(">")){var r=e.match(u.startTag);if(r){var i=(t={},a={},n=r[2],r[2].replace(u.attr,(function(e,r){arguments[2]||arguments[3]||arguments[4]||arguments[5]?arguments[5]?(t[arguments[5]]="",a[arguments[5]]=!0):t[r]=arguments[2]||arguments[3]||arguments[4]||u.fillAttr.test(r)&&r||"":t[r]="",n=n.replace(e,"")})),{v:new l.StartTagToken(r[1],r[0].length,t,a,!!r[3],n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""))});if("object"===(void 0===i?"undefined":c(i)))return i.v}}}function s(e){var t=i(e);if(t){var a=e.slice(t.length);if(a.match(new RegExp("</\\s*"+t.tagName+"\\s*>","i"))){var n=a.match(new RegExp("([\\s\\S]*?)</\\s*"+t.tagName+"\\s*>","i"));if(n)return new l.AtomicTagToken(t.tagName,n[0].length+t.length,t.attrs,t.booleanAttrs,n[1])}}}function o(e){var t=e.match(u.endTag);if(t)return new l.EndTagToken(t[1],t[0].length)}t.__esModule=!0;var c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.comment=n,t.chars=r,t.startTag=i,t.atomicTag=s,t.endTag=o;var l=a(4),u={startTag:/^<([\-A-Za-z0-9_]+)((?:\s+[\w\-]+(?:\s*=?\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,endTag:/^<\/([\-A-Za-z0-9_]+)[^>]*>/,attr:/(?:([\-A-Za-z0-9_]+)\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))|(?:([\-A-Za-z0-9_]+)(\s|$)+)/g,fillAttr:/^(checked|compact|declare|defer|disabled|ismap|multiple|nohref|noresize|noshade|nowrap|readonly|selected)$/i}},function(e,t,a){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.EndTagToken=t.AtomicTagToken=t.StartTagToken=t.TagToken=t.CharsToken=t.CommentToken=t.Token=void 0;var r=a(5),i=(t.Token=function e(t,a){n(this,e),this.type=t,this.length=a,this.text=""},t.CommentToken=function(){function e(t,a){n(this,e),this.type="comment",this.length=a||(t?t.length:0),this.text="",this.content=t}return e.prototype.toString=function(){return"<!--"+this.content},e}(),t.CharsToken=function(){function e(t){n(this,e),this.type="chars",this.length=t,this.text=""}return e.prototype.toString=function(){return this.text},e}(),t.TagToken=function(){function e(t,a,r,i,s){n(this,e),this.type=t,this.length=r,this.text="",this.tagName=a,this.attrs=i,this.booleanAttrs=s,this.unary=!1,this.html5Unary=!1}return e.formatTag=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a="<"+e.tagName;for(var n in e.attrs)if(e.attrs.hasOwnProperty(n)){a+=" "+n;var i=e.attrs[n];void 0!==e.booleanAttrs&&void 0!==e.booleanAttrs[n]||(a+='="'+(0,r.escapeQuotes)(i)+'"')}return e.rest&&(a+=" "+e.rest),e.unary&&!e.html5Unary?a+="/>":a+=">",null!=t&&(a+=t+"</"+e.tagName+">"),a},e}());t.StartTagToken=function(){function e(t,a,r,i,s,o){n(this,e),this.type="startTag",this.length=a,this.text="",this.tagName=t,this.attrs=r,this.booleanAttrs=i,this.html5Unary=!1,this.unary=s,this.rest=o}return e.prototype.toString=function(){return i.formatTag(this)},e}(),t.AtomicTagToken=function(){function e(t,a,r,i,s){n(this,e),this.type="atomicTag",this.length=a,this.text="",this.tagName=t,this.attrs=r,this.booleanAttrs=i,this.unary=!1,this.html5Unary=!1,this.content=s}return e.prototype.toString=function(){return i.formatTag(this,this.content)},e}(),t.EndTagToken=function(){function e(t,a){n(this,e),this.type="endTag",this.length=a,this.text="",this.tagName=t}return e.prototype.toString=function(){return"</"+this.tagName+">"},e}()},function(e,t){"use strict";function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e?e.replace(/([^"]*)"/g,(function(e,t){return/\\/.test(t)?t+'"':t+'\\"'})):t}t.__esModule=!0,t.escapeQuotes=a},function(e,t){"use strict";function a(e){return e&&"startTag"===e.type&&(e.unary=o.test(e.tagName)||e.unary,e.html5Unary=!/\/>$/.test(e.text)),e}function n(e,t){var n=e.stream,r=a(t());return e.stream=n,r}function r(e,t){var a=t.pop();e.prepend("</"+a.tagName+">")}function i(){var e=[];return e.last=function(){return this[this.length-1]},e.lastTagNameEq=function(e){var t=this.last();return t&&t.tagName&&t.tagName.toUpperCase()===e.toUpperCase()},e.containsTagName=function(e){for(var t,a=0;t=this[a];a++)if(t.tagName===e)return!0;return!1},e}function s(e,t,s){function o(){var t=n(e,s);t&&u[t.type]&&u[t.type](t)}var l=i(),u={startTag:function(a){var n=a.tagName;"TR"===n.toUpperCase()&&l.lastTagNameEq("TABLE")?(e.prepend("<TBODY>"),o()):t.selfCloseFix&&c.test(n)&&l.containsTagName(n)?l.lastTagNameEq(n)?r(e,l):(e.prepend("</"+a.tagName+">"),o()):a.unary||l.push(a)},endTag:function(a){l.last()?t.tagSoupFix&&!l.lastTagNameEq(a.tagName)?r(e,l):l.pop():t.tagSoupFix&&(s(),o())}};return function(){return o(),a(s())}}t.__esModule=!0,t.default=s;var o=/^(AREA|BASE|BASEFONT|BR|COL|FRAME|HR|IMG|INPUT|ISINDEX|LINK|META|PARAM|EMBED)$/i,c=/^(COLGROUP|DD|DT|LI|OPTIONS|P|TD|TFOOT|TH|THEAD|TR)$/i}])},e.exports=t()},function(e,t){"use strict";function a(e){return null!=e}function n(e){return"function"==typeof e}function r(e,t,a){var n=void 0,r=e&&e.length||0;for(n=0;n<r;n++)t.call(a,e[n],n)}function i(e,t,a){for(var n in e)e.hasOwnProperty(n)&&t.call(a,n,e[n])}function s(e,t){return e=e||{},i(t,(function(t,n){a(e[t])||(e[t]=n)})),e}function o(e){try{return Array.prototype.slice.call(e)}catch(n){var t=(a=[],r(e,(function(e){a.push(e)})),{v:a});if("object"===(void 0===t?"undefined":p(t)))return t.v}var a}function c(e){return e[e.length-1]}function l(e,t){return!(!e||"startTag"!==e.type&&"atomicTag"!==e.type||!("tagName"in e)||!~e.tagName.toLowerCase().indexOf(t))}function u(e){return l(e,"script")}function d(e){return l(e,"style")}t.__esModule=!0;var p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.existy=a,t.isFunction=n,t.each=r,t.eachKey=i,t.defaults=s,t.toArray=o,t.last=c,t.isTag=l,t.isScript=u,t.isStyle=d}])},"object"==typeof t&&"object"==typeof e?e.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof t?t.postscribe=n():a.postscribe=n()}},"core/src/lib/actions/helpers/unescapeHtmlCode.js":{script:function(e,t,a){"use strict";var n=a("@adobe/reactor-document").createElement("div");e.exports=function(e){return n.innerHTML=e,n.textContent||n.innerText||e}}},"core/src/lib/helpers/findPageScript.js":{script:function(e,t,a){"use strict";var n=a("@adobe/reactor-document"),r=function(e){for(var t=n.querySelectorAll("script"),a=0;a<t.length;a++){var r=t[a];if(e.test(r.src))return r}},i=function(){return r(new RegExp(/(launch|satelliteLib)-[^\/]+.js(\?.*)?$/))};e.exports={getTurbine:i,byRegexPattern:r}}},"core/src/lib/actions/helpers/decorators/decorateGlobalJavaScriptCode.js":{script:function(e,t,a){"use strict";var n=a("@adobe/reactor-promise");e.exports=function(e,t){return{code:"<script>\n"+t+"\n</script>",promise:n.resolve()}}}},"core/src/lib/actions/helpers/decorators/decorateNonGlobalJavaScriptCode.js":{script:function(e,t,a){"use strict";var n=a("@adobe/reactor-promise"),r=0;e.exports=function(e,t){var a="_runScript"+ ++r,i=new n((function(t,r){_satellite[a]=function(i){delete _satellite[a],new n((function(t){t(i.call(e.event.element,e.event,e.event.target,n))})).then(t,r)}}));return{code:'<script>_satellite["'+a+'"](function(event, target, Promise) {\n'+t+"\n});</script>",promise:i}}}},"core/src/lib/actions/helpers/decorators/decorateHtmlCode.js":{script:function(e,t,a,n){"use strict";var r=a("@adobe/reactor-promise"),i=0,s={};window._satellite=window._satellite||{},window._satellite._onCustomCodeSuccess=function(e){var t=s[e];t&&(delete s[e],t.resolve())},window._satellite._onCustomCodeFailure=function(e){var t=s[e];t&&(delete s[e],t.reject())};var o=function(e){return-1!==e.indexOf("${reactorCallbackId}")},c=function(e,t){return e.replace(/\${reactorCallbackId}/g,t)},l=function(e){return e.settings.isExternal};e.exports=function(e,t){var a;return l(e)&&(t=n.replaceTokens(t,e.event)),o(t)?(a=new r((function(e,t){s[String(i)]={resolve:e,reject:t}})),t=c(t,i),i+=1):a=r.resolve(),{code:t,promise:a}}}},"core/src/lib/actions/helpers/getSourceByUrl.js":{script:function(e,t,a){"use strict";var n=a("@adobe/reactor-load-script"),r=a("@adobe/reactor-promise"),i=a("../../helpers/findPageScript").byRegexPattern,s={},o={},c=function(e){return o[e]||(o[e]=n(e)),o[e]};_satellite.__registerScript=function(e,t){var a;if(document.currentScript)a=document.currentScript.getAttribute("src");else{var n=new RegExp(".*"+e+".*");a=i(n).getAttribute("src")}s[a]=t},e.exports=function(e){return s[e]?r.resolve(s[e]):new r((function(t){c(e).then((function(){t(s[e])}),(function(){t()}))}))}}},"core/src/lib/events/helpers/pageLifecycleEvents.js":{script:function(e,t,a){"use strict";var n=a("@adobe/reactor-window"),r=a("@adobe/reactor-document"),i=-1!==n.navigator.appVersion.indexOf("MSIE 10"),s="WINDOW_LOADED",o="DOM_READY",c="PAGE_BOTTOM",l=[c,o,s],u=function(e,t){return{element:e,target:e,nativeEvent:t}},d={};l.forEach((function(e){d[e]=[]}));var p=function(e,t){l.slice(0,f(e)+1).forEach((function(e){m(t,e)}))},g=function(){return"complete"===r.readyState?s:"interactive"===r.readyState?i?null:o:void 0},f=function(e){return l.indexOf(e)},m=function(e,t){d[t].forEach((function(t){v(e,t)})),d[t]=[]},v=function(e,t){var a=t.trigger,n=t.syntheticEventFn;a(n?n(e):null)};n._satellite=n._satellite||{},n._satellite.pageBottom=p.bind(null,c),r.addEventListener("DOMContentLoaded",p.bind(null,o),!0),n.addEventListener("load",p.bind(null,s),!0),n.setTimeout((function(){var e=g();e&&p(e)}),0),e.exports={registerLibraryLoadedTrigger:function(e){e()},registerPageBottomTrigger:function(e){d[c].push({trigger:e})},registerDomReadyTrigger:function(e){d[o].push({trigger:e,syntheticEventFn:u.bind(null,r)})},registerWindowLoadedTrigger:function(e){d[s].push({trigger:e,syntheticEventFn:u.bind(null,n)})}}}},"core/src/lib/events/helpers/createBubbly.js":{script:function(e,t,a){"use strict";var n=a("./weakMap"),r=a("./matchesProperties"),i=a("./matchesSelector");e.exports=function(){var e=[],t=new n,a={addListener:function(t,a){e.push({settings:t,callback:a})},evaluateEvent:function(a,n){if(e.length&&!t.has(a)){for(var s=a.target,o=!1;s;){for(var c=!1,l=!1,u=0;u<e.length;u++){var d=e[u],p=d.settings.elementSelector,g=d.settings.elementProperties;if((!1!==d.settings.bubbleFireIfChildFired||!o)&&(s===a.target||!1!==d.settings.bubbleFireIfParent)&&(s===a.target||p||g&&Object.keys(g).length)&&(!p||i(s,p))&&(!g||r(s,g))){var f={};n?Object.keys(a).forEach((function(e){f[e]=a[e]})):f.nativeEvent=a,f.element=s,f.target=a.target,!1!==d.callback(f)&&(l=!0,d.settings.bubbleStop&&(c=!0))}}if(c)break;l&&(o=!0),s=s.parentNode}t.set(a,!0)}},__reset:function(){e=[]}};return a}}},"core/src/lib/events/helpers/weakMap.js":{script:function(e,t,a){"use strict";var n=a("@adobe/reactor-window").WeakMap;if(void 0===n){var r=Object.defineProperty,i=Date.now()%1e9;(n=function(){this.name="__st"+(1e9*Math.random()>>>0)+i+++"__"}).prototype={set:function(e,t){var a=e[this.name];return a&&a[0]===e?a[1]=t:r(e,this.name,{value:[e,t],writable:!0}),this},get:function(e){var t;return(t=e[this.name])&&t[0]===e?t[1]:void 0},delete:function(e){var t=e[this.name];return!(!t||t[0]!==e||(t[0]=t[1]=void 0,0))},has:function(e){var t=e[this.name];return!!t&&t[0]===e}}}e.exports=n}},"core/src/lib/helpers/stringAndNumberUtils.js":{script:function(e){"use strict";var t=function(e){return"number"==typeof e&&isFinite(e)},a=function(e){return"string"==typeof e||e instanceof String},n=function(e){return t(e)?String(e):e},r=function(e){return a(e)?Number(e):e};e.exports={isNumber:t,isString:a,castToStringIfNumber:n,castToNumberIfString:r}}},"core/src/lib/events/helpers/matchesProperties.js":{script:function(e,t,a){"use strict";var n=a("./../../helpers/textMatch"),r=function(e,t){return"@text"===t||"innerText"===t?e.textContent||e.innerText:t in e?e[t]:e.getAttribute?e.getAttribute(t):void 0};e.exports=function(e,t){return!t||t.every((function(t){var a=r(e,t.name),i=t.valueIsRegex?new RegExp(t.value,"i"):t.value;return n(a,i)}))}}},"core/src/lib/events/helpers/matchesSelector.js":{script:function(e,t,a,n){"use strict";e.exports=function(e,t){var a=e.matches||e.msMatchesSelector;if(a)try{return a.call(e,t)}catch(e){return n.logger.warn("Matching element failed. "+t+" is not a valid selector."),!1}return!1}}}}},"adobe-analytics":{displayName:"Adobe Analytics",hostedLibFilesBaseUrl:"https://assets.adobedtm.com/extensions/EP31dbb9c60e404ba1aa6e746d49be6f29/",settings:{orgId:"4D6368F454EC41940A4C98A6@AdobeOrg",customSetup:{source:function(e){function t(e,t,a){return function(n){var r=n;if(r)do{if(r.getAttribute&&(regionId=r.getAttribute(t),regionId))return regionId.toLowerCase();if(a&&("A"==r.tagName||"BUTTON"==r.tagName))break}while(r&&(r=r.parentNode));var i=e.call(this,n);return i&&-1!==i.indexOf("@")?(window.pageDataTracker&&(i=pageDataTracker.md5(i).substring(0,16)),"DTM filtered (@):"+i):i}}if(window.s=e,!window.pageDataTracker)return!1;pageDataTracker.mapAdobeVars(e),e._tpDST={2012:"3/11,11/4",2013:"3/10,11/3",2014:"3/9,11/2",2015:"3/8,11/1",2016:"3/13,11/6",2017:"3/12,11/5",2018:"3/11,11/4",2019:"3/10,11/3",2020:"3/8,11/1",2021:"3/14,11/7",2022:"3/13,11/6",2023:"3/12,11/5",2024:"3/10,11/3",2025:"3/9,11/2",2026:"3/8,11/1",2027:"3/14,11/7",2028:"3/12,11/5",2029:"3/11,11/4",2030:"3/10,11/3",2031:"3/9,11/2",2032:"3/14,11/7",2033:"3/13,11/6"},e.ActivityMap.region=t(e.ActivityMap.region,"data-aa-region",!1),e.ActivityMap.link=t(e.ActivityMap.link,"data-aa-name",!0),e.usePlugins=!0,e.prop68&&"md"==e.prop68&&(e.trackOffline=!0),e.cookieDomainPeriods="2";for(var a=["com","edu","gov","ac","org","net","co","go"],n=0;n<a.length;n++)if(document.location.hostname.indexOf("."+a[n]+".")>0){e.cookieDomainPeriods="3";break}e.linkInternalFilters=(e.linkInternalFilters?e.linkInternalFilters+",":"")+document.location.hostname,window.s_doPlugins=function(e){if(window.pageData&&"true"!=pageData.page.noTracking&&window.pageData_isLoaded||(e.abort=!0,window.pageData)){try{if(window.ddqueue&&window.ddqueue.length>0){var t=JSON.parse(window.ddqueue.shift());window.eventData=t.eventData,window.pageData=t.pageData}}catch(e){}pageData.page.lastTrackedPage=pageData.page.analyticsPagename,"object"!=typeof window.eventData||"newPage"!=window.eventData.eventName&&"searchResultsUpdated"!=window.eventData.eventName||(e.clearVars(),e.pageLoaded=!1,pageDataTracker.getEvents()),pageDataTracker.mapAdobeVars(e);try{if(promos=[],e.getValue("eventData.cta.ids"))promos.push.apply(promos,e.getValue("eventData.cta.ids")),e.linkTrackVars=e.apl(e.linkTrackVars,"list3",",",2);else{for(var a=document.getElementsByTagName("a"),n=0;n<a.length;n++){var r=a[n].getAttribute("data-sc-promo-id");r&&promos.push(r)}e.getValue("pageData.cta.ids")&&promos.push.apply(promos,e.getValue("pageData.cta.ids"))}e.list3=promos.join("|")}catch(e){_satellite.logger.error(e)}if(e.list3){var i=e.list3.split("|");for(n=0;n<i.length;n++)i[n]=e.productPrefix(i[n]);e.list3=i.join("|")}e.eVar21=_satellite.getVar("Promo - Clicked ID"),e.eVar21&&(e.list3=e.eVar21=e.productPrefix(e.eVar21),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar21",",",2),window.clickedPromoId&&(window.clickedPromoId=void 0)),e.getCustomReportSuites(),e.tpEls=e.getTimeParting("n","-5").split("|"),e.prop5=e.tpEls[0]+" "+e.tpEls[1],e.prop9=e.version,e.server=document.location.hostname,e.prop18=document.location.hostname+document.location.pathname,e.prop32=document.location.protocol.replace(/\:/g,""),e.prop35=e.getUrlWithHashbang(),e.eVar8=e.getDaysSinceLastVisit("v8");var s,o=Visitor.getInstance("4D6368F454EC41940A4C98A6@AdobeOrg");if(e.eVar50=o.getMarketingCloudVisitorID(),e.prop12){var c=0;e.eVar33&&null!==e.eVar33.match(/(reg(-|_|:))|registered/gi)&&(c=1),o.setCustomerIDs({userid:{id:e.prop12,authState:c}})}if(window.pageData&&pageData.page&&"cert"==pageData.page.environment&&(e.account=e.account.replace(/\-(prod)/gi,"-dev")),e.campaign&&e.prop2&&0!=e.campaign.indexOf(e.prop2+":")&&(e.campaign=e.prop2+":"+e.campaign),e.campaign&&(e.eVar108="D=v0",e.linkTrackVars=e.apl(e.linkTrackVars,"eVar108",",",2)),e.events=e.events?e.events:"",e.products=pageDataTracker.setProductsVariable(),!e.pageLoaded&&e.pageName){if(e.prop19=e.getPreviousValue(e.pageName,"c19",""),e.prop19){var l=e.getPercentPageViewed(e.pageName);l&&l.length>2&&"undefined"!=l[1]&&void 0!==l[1]&&"undefined"!=l[2]&&void 0!==l[2]?e.prop17=l[1]+"|"+l[2]:e.prop17="no data available"}if(e.eVar66=e.eVar67="+1",e.events=e.apl(e.events,"event27",",",2),window.pageData&&pageData.trackEvents&&pageData.trackEvents instanceof Array){for(var u={associationStart:"event199",associated:"event200",contentEdit:"event190",contentAddition:"event79",recommendationViews:"event257,event264",accountAssociationStart:"event333"},d=0;d<pageData.trackEvents.length;d++){var p=u[pageData.trackEvents[d]];p&&(e.events=e.apl(e.events,p,",",2))}pageData.trackEvents=[]}var g=_satellite.getVar("Visitor - User ID"),f=new Date;if(g){g=g.toLowerCase();var m=new Date(f.getFullYear(),0,1),v=g+f.getFullYear()+f.getMonth();v=pageDataTracker.md5(v).substring(0,20),e.events=e.apl(e.events,"event203:"+v,",",2);var h=g+f.getFullYear()+f.getMonth();h=e.productPrefix(h),h=pageDataTracker.md5(h).substring(0,20),e.events=e.apl(e.events,"event320:"+h,",",2);var b=g+f.getFullYear()+Math.ceil(((f.getTime()-m.getTime())/864e5+m.getDay()+1)/7);b=e.productPrefix(b),b=pageDataTracker.md5(b).substring(0,20),e.events=e.apl(e.events,"event321:"+b,",",2)}var k=_satellite.getVar("Visitor - Account ID");if(k&&(k=k.toLowerCase(),k+=f.getFullYear(),k+=f.getMonth(),k=pageDataTracker.md5(k).substring(0,20),e.events=e.apl(e.events,"event205:"+k,",",2)),e.eVar72&&(e.events=e.apl(e.events,"event9",",",2)),window.pageData&&pageData.visitor&&pageData.visitor.loginSuccess&&"true"==pageData.visitor.loginSuccess&&(e.events=e.apl(e.events,"event23",",",2),pageData.visitor.loginSuccess="false"),window.pageData&&pageData.visitor&&pageData.visitor.loginFailure&&"true"==pageData.visitor.loginFailure&&(e.events=e.apl(e.events,"event134",",",2),pageData.visitor.loginFailure="false"),e.eVar19&&(e.eVar19=e.cleanUrlData(e.eVar19),e.prop21||(e.prop21=e.eVar19)),"0"==e.eVar3&&(e.eVar3="zero"),e.prop21){e.prop21=e.cleanUrlData(e.prop21),e.getValOnce(e.prop21,"c21",0)&&(e.events=e.apl(e.events,"event3",",",2),e.eVar35=e.eVar36="+1","0"==e.eVar3||"zero"==e.eVar3?(e.eVar3="zero",e.events=e.apl(e.events,"event4",",",2)):e.eVar3&&(e.events=e.apl(e.events,"event14="+e.eVar3,",",2)));var y=_satellite.getVar("Search - Results per Page");e.getValOnce((e.eVar19&&e.eVar19==e.prop21?"":e.prop21)+":"+_satellite.getVar("Search - Current Page"),"e13",0)&&y&&(e.events=e.apl(e.events,"event13="+y,",",2))}e.eVar117&&e.getValOnce(e.eVar117,"v117",0)&&(e.events=e.apl(e.events,"event198",",",2)),e.prop13&&e.getValOnce(e.prop13,"c13",0)&&(e.events=e.apl(e.events,"event24",",",2)),(e.prop7||e.eVar46)&&e.getValOnce(e.prop7||e.eVar46,"c7",0)&&(e.events=e.apl(e.events,"event6",",",2)),e.prop60&&e.getValOnce(e.prop60,"c60",0)&&(e.events=e.apl(e.events,"event88",",",2));var D=e.getValOnce("1","e41",0);e.clickPast(D,"event41","event42"),e.events.indexOf("event41")>-1&&C&&e.c_w("v31",C),e.list3&&(e.events=e.apl(e.events,"event21",",",2));var C=_satellite.getVar("Page - Load Timestamp"),w=e.getPreviousValue(C,"v68","")||C,T=e.c_r("v31")||C;if(C)try{var S=new Date(parseInt(C)),P=S.getTime(),V=this.isDST(S);e.eVar113=(Math.floor((P/1e3-18e3+3600*V)%86400)+1).toString(),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar113",",",2)}catch(e){_satellite.notify("Error setting s.eVar113: "+e.message)}if(C&&w)try{var _=new Date(parseInt(C)),E=new Date(parseInt(w)),I=new Date(parseInt(T)),A=_.getTime()-E.getTime();A>0&&(e.eVar68="+"+(A/1e3).toFixed(0)),e.currentVisitTime=((_.getTime()-I.getTime())/1e3).toFixed(0)}catch(e){}if(pageData.eventList)for(n=0;n<pageData.eventList.length;n++)"product turnaway"==pageData.eventList[n]&&(e.events=e.apl(e.events,"event43",",",2));if(e.eVar43&&(e.events=e.apl(e.events,"event26",",",2)),e.eVar44&&(e.events=e.apl(e.events,"event17",",",2)),pageData.page&&pageData.page.purchaseStep){var O="";switch(pageData.page.purchaseStep){case"cart":O="scView";break;case"login":case"checkout":case"shipping":case"payment":O="scCheckout";break;case"purchase":O="purchase"}O&&(e.events=e.apl(e.events,O,",",2),"scView"==O&&(e.events=e.apl(e.events,"scOpen",",",2)))}e.list2&&(e.events=e.apl(e.events,"event178",",",2));try{var L=sessionStorage.getItem("aamm");L&&(pageDataTracker.addMessage("b"+L),sessionStorage.removeItem("aamm"));var j="_pageDataTracker_tpcf",x=sessionStorage.getItem(j);x&&(pageDataTracker.addMessage("true"==x?"3p1":"3p0"),sessionStorage.removeItem(j)),e.prop66=pageDataTracker.getMessages(),e.prop66&&(e.linkTrackVars=e.apl(e.linkTrackVars,"prop66",",",2));var M=pageDataTracker.getPerformance();M.du&&(e.eVar114=M.du,e.linkTrackVars=e.apl(e.linkTrackVars,"eVar114",",",2)),M.lt&&(e.eVar115=M.lt,e.linkTrackVars=e.apl(e.linkTrackVars,"eVar115",",",2))}catch(e){}for(var N=["utm_campaign","dgcid","utm_dgroup","utm_in","utm_medium","utm_acid","cmx_id","sis_id","utm_source","utm_term","utm_content"],R="",F=!1,U=0;U<N.length;U++){var B=e.Util.getQueryParam(N[U])||"";B&&(F=!0),R+=B+"|"}if(F&&(e.eVar125=R),e.prop14&&!isNaN(parseFloat(e.prop14))&&isFinite(e.prop14)&&(e.events=e.apl(e.events,"event229="+e.prop14,",",2),e.events=e.apl(e.events,"event230",",",2)),window.pageData&&pageData.page)for(var H=0;H<5;H++){var W=2*H+306,Y=pageData.page["customPerformance"+(H+1)];isFinite(parseFloat(Y))&&(e.events=e.apl(e.events,"event"+W+"="+parseFloat(Y),",",2),e.events=e.apl(e.events,"event"+(W+1),",",2))}window.pageDataTracker_ec&&pageDataTracker_ec>0&&(e.events=e.apl(e.events,"event227="+pageDataTracker_ec,",",2),pageDataTracker_ec=0),window.pageDataTracker_wc&&pageDataTracker_wc>0&&(e.events=e.apl(e.events,"event228="+pageDataTracker_wc,",",2),pageDataTracker_wc=0),e.pageLoaded=!0}if(e.prop4&&null!==e.prop4.match(/^CP\-/gi)&&!e.linkType||e.linkName&&"content view"==e.linkName){if((s=pageDataTracker.getContentItem())&&s.id&&!s.turnawayId){e.events=e.apl(e.events,"prodView",",",2),e.events=e.apl(e.events,"event5",",",2),e.events=e.apl(e.events,"event40",",",2),e.events=e.apl(e.events,"event181",",",2),e.events=e.apl(e.events,"event182",",",2),e.events=e.apl(e.events,"event184",",",2),e.events=e.apl(e.events,"event239",",",2),e.events=e.apl(e.events,"event240",",",2);var G=_satellite.getVar("Visitor - App Session ID"),X=pageDataTracker.md5((G||"none")+s.id).substring(0,20);e.events=e.apl(e.events,"event201:"+X,",",2),s.type&&-1!==s.type.toLowerCase().indexOf("scope-full")&&(e.events=e.apl(e.events,"event202:"+X,",",2)),s&&s.type&&(e.hier2=s.type,e.linkTrackVars=e.apl(e.linkTrackVars,"hier2",",",2)),e.eVar24="+1",e.eVar25="+1",e.prop11=s.id,e.prop31=pageDataTracker.getBibliographicInfo(s),s.format&&(null!==s.format.match(/\-X?HTML/gi)?(O="",O=null!==s.format.match(/scope\-abstract/gi)||null!==s.type.match(/scope\-abstract/gi)?"event33":"event29",e.events=e.apl(e.events,O,",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,O,",",2)):null!==s.format.match(/\-PDF/gi)&&(e.events=e.apl(e.events,"event30",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event30",",",2))),null!==e.prop4.match(/^CP\-DL/gi)&&(e.events=e.apl(e.events,"event19",",",2)),s.viewState&&("login"==s.viewState?(e.events=e.apl(e.events,"event103",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event103",",",2)):"upsell"==s.viewState&&(e.events=e.apl(e.events,"event104",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event104",",",2))),s.indexTerms&&(e.prop56=s.indexTerms),e.currentVisitTime&&(e.eVar31=e.currentVisitTime,e.eVar31&&"0"!=e.eVar31||(e.eVar31="zero")),e.linkTrackVars=e.apl(e.linkTrackVars,"events",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar24",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar25",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar31",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop11",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop31",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"prodView",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event5",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event40",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event239",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event240",",",2)}}else e.events=e.removeFromList("prodView,event5,event40,event29,event33,event30,event239,event240",",",e.events,",");e.products&&e.products.indexOf("event239=")>=0&&(e.events=e.apl(e.events,"event239",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event239",",",2)),e.products&&e.products.indexOf("event240=")>=0&&(e.events=e.apl(e.events,"event240",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event240",",",2)),e.eVar21&&(e.events=e.apl(e.events,"event22",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar21",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"events",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event22",",",2)),e.prop23&&e.products&&(s=pageDataTracker.getContentItem())&&s.id&&(e.prop22=s.id+":"+e.prop23,e.linkTrackVars=e.apl(e.linkTrackVars,"prop22",",",2)),e.eVar60&&e.getValOnce(e.eVar60,"v60",0)&&(e.events=e.apl(e.events,"event75",",",2)),e.eVar61&&e.getValOnce(e.eVar61,"v61",0)&&(e.events=e.apl(e.events,"event76",",",2)),e.eVar15&&e.getValOnce(_satellite.getVar("Search - Criteria"),"e78",0)&&(e.events=e.apl(e.events,"event78",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event78",",",2),window.eventData&&eventData.search&&(eventData.search.resultsPosition="")),e.eVar37&&e.products&&e.isTracked("eVar37")&&(e.events=e.apl(e.events,"event44",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"events",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event44",",",2));var q=_satellite.getVar("Form - Step + Name");if(q&&"login:start"===q?(e.linkTrackVars=e.apl(e.linkTrackVars,"events",",",2),e.events=e.apl(e.events,"event141",",",2)):q&&"loginregistration:start"===q?(e.linkTrackVars=e.apl(e.linkTrackVars,"events",",",2),e.events=e.apl(e.events,"event185",",",2)):q&&"termsagreement:start"===q?(e.linkTrackVars=e.apl(e.linkTrackVars,"events",",",2),e.events=e.apl(e.events,"event186",",",2)):q&&"termsagreement:complete"===q?(e.linkTrackVars=e.apl(e.linkTrackVars,"events",",",2),e.events=e.apl(e.events,"event187",",",2)):q?(O="",O=q.indexOf("complete")>-1?"event"+(q.indexOf("register")>-1||q.indexOf("registration")>-1?"2":"47"):"event"+(q.indexOf("register")>-1||q.indexOf("registration")>-1?"1":"46"),e.linkTrackVars=e.apl(e.linkTrackVars,"events",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,O,",",2),e.events=e.apl(e.events,O,",",2)):e.events=e.removeFromList("event1,event2,event46,event47",",",e.events,","),(q.indexOf("register")>-1||q.indexOf("registration")>-1)&&e.isTracked("eVar43")&&(e.events=e.apl(e.events,"event216",",",2),e.linkTrackEvents=e.apl(e.linkTrackEvents,"event216",",",2)),document.location.href.length>255?e.pageURL=document.location.href.substring(0,255):e.pageURL=document.location.href,e.pageURL&&0===e.pageURL.indexOf("file:")&&(e.pageURL=e.prop18=e.prop35="file://[filepath sanitized for GDPR compliance]"),e.campaign&&-1!==e.campaign.indexOf("raven")?e.referrer="mail://raven":e.campaign&&-1!==e.campaign.indexOf("email")?e.referrer="mail://campaigns":document.referrer&&document.referrer.length>255&&!e.referrer?e.referrer=document.referrer.substring(0,255):document.referrer||(e.referrer=e.Util.getQueryParam("aaref")),e.prop8&&(e.linkTrackVars=e.apl(e.linkTrackVars,"prop8",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar18",",",2)),
e.eVar109=e.getFullReferringDomains(),e.eVar109&&(e.linkTrackVars=e.apl(e.linkTrackVars,"eVar109",",",2)),e.linkTrackVars=e.linkTrackVars||"",e.linkTrackVars=e.removeFromList("eVar118,eVar119,eVar120,eVar121,eVar144",",",e.linkTrackVars,","),window.eventData&&eventData.link&&eventData.link.location&&(e.eVar118=eventData.link.location,e.linkTrackVars=e.apl(e.linkTrackVars,"eVar118",",",2)),window.eventData&&eventData.link&&eventData.link.name&&(e.eVar119=eventData.link.name,e.linkTrackVars=e.apl(e.linkTrackVars,"eVar119",",",2)),window.eventData&&eventData.link&&eventData.link.type&&(e.eVar120=eventData.link.type,e.linkTrackVars=e.apl(e.linkTrackVars,"eVar120",",",2)),window.eventData&&eventData.link&&eventData.link.destination&&(e.eVar121=eventData.link.destination,e.linkTrackVars=e.apl(e.linkTrackVars,"eVar121",",",2)),window.eventData&&eventData.link&&eventData.link.userInputMethod&&(e.eVar144=eventData.link.userInputMethod,e.linkTrackVars=e.apl(e.linkTrackVars,"eVar144",",",2)),e.eVar103&&(e.eVar110=e.eVar110?e.eVar110:"D=c19"),!e.isTracked("eVar103")&&window.eventData&&eventData.conversionDriver&&eventData.conversionDriver.name&&(e.eVar103=eventData.conversionDriver.name,e.eVar110="D=pageName",e.linkTrackVars=e.apl(e.linkTrackVars,"eVar103,eVar110",",",2)),e.eVar103&&e.isTracked("eVar103")&&(e.eVar145="D=v103",e.linkTrackVars=e.apl(e.linkTrackVars,"eVar145",",",2)),e.events&&e.events.split(",").indexOf("event2")>-1&&(e.events=e.apl(e.events,"event7",",",2)),e.linkObject&&e.linkURL&&"e"==e.linkType)try{e.eVar158=e.extractHostname(e.linkURL),e.eVar158&&(e.linkTrackVars=e.apl(e.linkTrackVars,"eVar158",",",2))}catch(e){}window.navigator&&navigator.userAgent&&(e.eVar186=navigator.userAgent,e.linkTrackVars=e.apl(e.linkTrackVars,"eVar186",",",2)),e.trackEventsList(e,"prop69"),e.prop65=_satellite.getVar("Page - Online State"),e.prop65&&(e.linkTrackVars=e.apl(e.linkTrackVars,"prop65",",",2));var z=(g=_satellite.getVar("Visitor - User ID")).match(/^ae:([0-9]+)$/i);z&&z.length>1&&(e.prop72=z[1],e.linkTrackVars=e.apl(e.linkTrackVars,"prop72",",",2)),e.prop2&&-1!==["pr","sv"].indexOf(e.prop2)&&(e.prop41=g,e.linkTrackVars=e.apl(e.linkTrackVars,"prop41",",",2));var J=sessionStorage.getItem("mf_session");J&&(e.prop44=J,e.linkTrackVars=e.apl(e.linkTrackVars,"prop44",",",2));var K=[];window.ga&&K.push("3"),window.pendo&&K.push("4"),window.usabilla_live&&K.push("5"),window.optimizely&&K.push("6"),window.mendeleyWebImporter&&K.push("7"),window.OneTrust&&K.push("8"),window.mouseflow&&K.push("9"),K.length>0&&(e.prop34=e.prop34?e.prop34+"|"+K.join("|"):K.join("|")),e.prop29=e.eVar7?"D=v7":"",e.prop36=e.list3?"D=l3":"",e.prop37=e.eVar33?"D=v33":"",e.eVar1=e.prop21?"D=c21":"",e.eVar2=e.prop6?"D=c6":"",e.eVar4=e.prop2?"D=c2":"",e.eVar5=e.prop5?"D=c5":"",e.eVar9=e.prop16?"D=c16":"",e.eVar10=e.prop18?"D=c18":"",e.eVar11=e.pageName?"D=pageName":"",e.eVar13=e.prop4?"D=c4":"",e.eVar14=e.purchaseID?"D=purchaseID":"",e.eVar16=e.prop1?"D=c1":"",e.eVar18=e.prop8?"D=c8":"",e.eVar26=e.prop13?"D=c13":"",e.eVar29=e.prop12?"D=c12":"",e.eVar32=e.prop19?"D=c19":"",e.eVar46=e.prop7?"D=c7":"",e.eVar98=e.prop63?"D=c63":"",e.eVar101="D=g",e.eVar147=e.prop33?"D=c33":"",e.hier1=e.pageName?"D=pageName":"",e.hier3=e.prop19?"D=c19":"",e.list1=e.prop7?"D=c7":"",e.linkTrackVars=e.apl(e.linkTrackVars,"prop2",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop3",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop4",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop5",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop9",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop16",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop18",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop35",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop19",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop24",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop32",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"prop33",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar4",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar5",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar8",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar9",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar10",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar11",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar13",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar32",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar50",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar59",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar101",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"eVar147",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"products",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"currencyCode",",",2),e.linkTrackVars=e.apl(e.linkTrackVars,"channel",",",2),e.isTracked("list3")&&(e.linkTrackVars=e.apl(e.linkTrackVars,"prop36",",",2)),e.isTracked("eVar33")&&(e.linkTrackVars=e.apl(e.linkTrackVars,"prop37",",",2)),e.prop38=_satellite.getVar("Maturity Level"),e.linkTrackVars=e.apl(e.linkTrackVars,"prop38",",",2),e.prop39=_satellite.getVar("Source"),e.linkTrackVars=e.apl(e.linkTrackVars,"prop39",",",2),e.prop39&&"id"==e.prop39&&(e.prop42=e.eVar33?"D=v33":"",e.isTracked("eVar33")&&(e.linkTrackVars=e.apl(e.linkTrackVars,"prop42",",",2))),e.prop3=e.getKPIName(),e.abort=_satellite.getVar("s_blacklist")}},e.doPlugins=window.s_doPlugins,e.registerPreTrackCallback((function(e){var t=window.pageDataTracker_preTrack;t&&"function"==typeof t&&t(e)}));try{e.loadModule("Media"),e.Media.autoTrack=!1,e.Media.trackWhilePlaying=!1,e.Media.playerName="elsevier video player",e.Media.segmentByMilestones=!0,e.Media.trackMilestones="50",e.Media.trackUsingContextData=!0,e.Media.contextDataMapping={"a.media.name":"eVar77,prop10","a.media.view":"event105","a.media.timePlayed":"event108","a.media.complete":"event107","a.media.milestones":{50:"event106"}},e.Media.trackVars="prop17,eVar52,eVar1,eVar2,eVar3",e.Media.trackEvents="event66"}catch(o){}if(e.isTracked=function(t){return-1!==e.split(e.linkTrackVars,",").indexOf(t)},e.getValue=function(e){var t=window;propChain=e.split(".");for(var a=0,n=propChain.length;a<n;a++){if(null==t)return;t=t[propChain[a]]}return t},e.productPrefix=function(e){if(window.pageData&&pageData.page&&pageData.page.productName){var t=pageData.page.productName.toLowerCase()+":";if(0!==e.indexOf(t))return t+e}return e},e.trackEventsList=function(e,t){try{for(var a=e.events?e.events.split(","):[],n=e.linkTrackEvents?e.linkTrackEvents.split(","):[],r=[],i=0;i<a.length;i++)a[i].indexOf(":")>=0?a[i]=a[i].substring(0,a[i].indexOf(":")):a[i].indexOf("=")>=0&&(a[i]=a[i].substring(0,a[i].indexOf("="))),e.linkType?n.indexOf(a[i])>=0&&r.push(a[i]):r.push(a[i]);for(i=0;i<r.length;i++)0==r[i].indexOf("event")&&(r[i]="e"+r[i].substring(5,r[i].length));e[t]=r.join(","),e.linkTrackVars=e.apl(e.linkTrackVars,t,",",2)}catch(a){e[t]="",_satellite.notify("s.trackEventsList: "+a.message)}},e.isDST=function(t){var a=!1;try{var n,r,i=e._tpDST[t.getFullYear()].split(/,/);n=new Date(i[0]+"/"+t.getFullYear()),r=new Date(i[1]+"/"+t.getFullYear()),n=new Date(n.getTime()+42e4),r=new Date(r.getTime()+36e4),a=t>n&&t<r}catch(e){a=!1,_satellite.notify("s.isDST: Error checking for DST:"+e.message)}return a},e.getUrlWithHashbang=function(){var e=document.location.protocol+"//"+document.location.hostname+document.location.pathname;try{var t;for(str=document.location.href.toString(),_regex=/^[^\?]+/g;null!==(t=_regex.exec(str));)for(t.index===_regex.lastIndex&&_regex.lastIndex++,groupIndex=0;groupIndex<t.length;groupIndex++)0===groupIndex&&(e=t[groupIndex].toString())}catch(e){_satellite.notify("s.getUrlWithHashbang: "+e.message)}return e},e.extractHostname=function(e){return(e.indexOf("://")>-1?e.split("/")[2]:e.split("/")[0]).split(":")[0].split("?")[0]},e.extractRootDomain=function(t){var a=e.extractHostname(t),n=a.split("."),r=n.length;return r>2&&(a=n[r-2]+"."+n[r-1],2==n[r-2].length&&2==n[r-1].length&&(a=n[r-3]+"."+a)),a},e.getCustomReportSuites=function(){if("jb"==e.prop2){var t="dev",a="";if(window.pageData&&pageData.page){var n=pageData.page.server||document.location.hostname,r=e.Util.getQueryParam("code");t=null===document.location.hostname.match(/\-(qa|test|stag)/g)?"prod":"dev",a="cell"==n||"www.cell.com"==n||"cell-site"==r?"cell":"www.thelancet.com"==n||"lancet-site"==r?"the lancet":"other journal branded sites"}return e.channel=a,"elsevier-ha-"+t+",elsevier-global-"+t}if(window.pageData&&pageData.page&&pageData.page.secondaryProductName){var i=["sd","md","ev","ez","pr","sv","ss","mi","jb","id","ih","hb","pv","ds","jc","ec","ck","pm","ed","si","qu","ex","ws","st","dr","ps","gf","ve","pp","em","kn","ci","eb","hm","es","sc","eaq","fi","co","el","evo","so","me","nco","h2","cpem","cl","as","prx","tox","mt","ad","bd","my","cp","er","cs","pe","tb","cw","am","c2","ns","rn","bp","at","ep","iq","path","rad","stat","gsna","mc","api","cc","pb","med","bpdg","bpeg","bpex","uca","hps","hcp","aude","sutd","rh","jpoc","chem","ezel","vlt","eoap","brxt","plum","ic","et","cin","rx2","oa","eman","elsa","jf","ji","exl","zoom","fc"],s=[["SANDBOX","sandbox"],["MDY/mendeley","md"],["SD/science","sd"],["SC/scopus","sc"],["SVE/SciVal","sv"],["RA/researcher","pb"],["ECOM/elscom","ec"],["eman/eman","eman"],["ERH/erh","rvh"]],o=(t="dev",pageData.page.secondaryProductName.toLowerCase());if(pageData.page.environment&&(t="prod"==pageData.page.environment||"cert"==pageData.page.environment?pageData.page.environment:"dev"),i.indexOf(o)>-1)return e.eVar107=o,e.account+",elsevier-"+o+"-"+t;for(var c=0;c<s.length;c++)if(o==s[c][0].toLowerCase())return e.eVar107=s[c][1],e.account+",elsevier-"+s[c][1]+"-"+t;return e.eVar107=o,e.account}return e.account},e.removeFromList=function(e,t,a,n){t=t||",",n=n||",";for(var r=e.split(t),i=a.split(n),s=[],o=0;o<i.length;o++){for(var c=!1,l=0;l<r.length;l++)if(i[o]==r[l]){c=!0;break}c||s.push(i[o])}return s.join(n)},e.cleanUrlData=function(e){return e?e=(e=(e=(e=(e=e.replace(/\+/g," ")).replace(/[',"]/g,"")).replace(/\t/g,"")).replace(/\n/g,"")).toLowerCase():""},e.getProductNum=function(){var e,t=this,a=new Date;return e=t.c_r("pn")?parseInt(t.c_r("pn"))+1:1,a.setTime(a.getTime()+2592e6),t.c_w("pn",e,a),e},e.getKPIName=function(){for(var t={rx:["2","3","5","6","9","12","23","25","37","39","87"],rx2:["2","3","5","6","9","12","23","25","37","39","87"],sd:["2","3","5","9","23","37"],"knovel pi":["2","3","5","9","19","21","22","23","37","48"],qu:["3","5","19","29","30","33","39","84"],md:["5","39","79","23","2","9","3","37","6","19","25","48","172","21","22"],sc:["5","39","79","23","2","9","3","37","6","19","25","48","172","21","22"],sd:["19","21","22","6","39","25","48"],default:["3","37","6","5","39","19","25","48","9","12","23","2","21","22"]},a={2:"registration",3:"search",5:"content view",6:"facet/filter search",9:"save alert",12:"save search",19:"file downloads",21:"cta impression",22:"cta click",23:"user login",25:"link out",29:"full-text html view",30:"pdf view",33:"abstract html view",37:"search results click",39:"content export",48:"add to my list",79:"content addition/import",84:"content detail/abstract window view",87:"in-page click",172:"profile self claim"},n=e.prop2&&t[e.prop2]?t[e.prop2]:t.default,r=0;r<n.length;r++){var i=n[r],s=new RegExp("event"+i+"(,|$)","g");if(a[i]&&e.events.match(s))return a[i]}return""},e.apl=new Function("l","v","d","u","var s=this,m=0;if(!l)l='';if(u){var i,n,a=l.split(d);for(i=0;i<a.length;i++){n=a[i];m=m||(u==1?(n==v):(n.toLowerCase()==v.toLowerCase()));}}if(!m)l=l?l+d+v:v;return l"),e.getValOnce=new Function("v","c","e","var s=this,a=new Date,v=v?v:v='',c=c?c:c='s_gvo',e=e?e:0,k=s.c_r(c);if(v){a.setTime(a.getTime()+e*86400000);s.c_w(c,v,e?a:0);}return v==k?'':v"),e.getTimeParting=new Function("h","z","var s=this,od;od=new Date('1/1/2000');if(od.getDay()!=6||od.getMonth()!=0){return'Data Not Available';}else{var H,M,D,U,ds,de,tm,da=['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'],d=new Date();z=z?z:0;z=parseFloat(z);if(s._tpDST){var dso=s._tpDST[d.getFullYear()].split(/,/);ds=new Date(dso[0]+'/'+d.getFullYear());de=new Date(dso[1]+'/'+d.getFullYear());if(h=='n'&&d>ds&&d<de){z=z+1;}else if(h=='s'&&(d>de||d<ds)){z=z+1;}}d=d.getTime()+(d.getTimezoneOffset()*60000);d=new Date(d+(3600000*z));H=d.getHours();M=d.getMinutes();M=M<30?'00':'30';D=d.getDay();U=' AM';if(H>=12){U=' PM';H=H-12;}if(H==0){H=12;}D=da[D];tm=H+':'+M+U;return(tm+'|'+D);}"),e.getDaysSinceLastVisit=new Function("c","var s=this,e=new Date(),es=new Date(),cval,cval_s,cval_ss,ct=e.getTime(),day=24*60*60*1000,f1,f2,f3,f4,f5;e.setTime(ct+3*365*day);es.setTime(ct+30*60*1000);f0='Cookies Not Supported';f1='First Visit';f2='More than 30 days';f3='More than 7 days';f4='Less than 7 days';f5='Less than 1 day';cval=s.c_r(c);if(cval.length==0){s.c_w(c,ct,e);s.c_w(c+'_s',f1,es);}else{var d=ct-cval;if(d>30*60*1000){if(d>30*day){s.c_w(c,ct,e);s.c_w(c+'_s',f2,es);}else if(d<30*day+1 && d>7*day){s.c_w(c,ct,e);s.c_w(c+'_s',f3,es);}else if(d<7*day+1 && d>day){s.c_w(c,ct,e);s.c_w(c+'_s',f4,es);}else if(d<day+1){s.c_w(c,ct,e);s.c_w(c+'_s',f5,es);}}else{s.c_w(c,ct,e);cval_ss=s.c_r(c+'_s');s.c_w(c+'_s',cval_ss,es);}}cval_s=s.c_r(c+'_s');if(cval_s.length==0) return f0;else if(cval_s!=f1&&cval_s!=f2&&cval_s!=f3&&cval_s!=f4&&cval_s!=f5) return '';else return cval_s;"),e.getPreviousValue=new Function("v","c","el","var s=this,t=new Date,i,j,r='';t.setTime(t.getTime()+1800000);if(el){if(s.events){i=s.split(el,',');j=s.split(s.events,',');for(x in i){for(y in j){if(i[x]==j[y]){if(s.c_r(c)) r=s.c_r(c);v?s.c_w(c,v,t):s.c_w(c,'no value',t);return r}}}}}else{if(s.c_r(c)) r=s.c_r(c);v?s.c_w(c,v,t):s.c_w(c,'no value',t);return r}"),e.split=new Function("l","d","var i,x=0,a=new Array;while(l){i=l.indexOf(d);i=i>-1?i:l.length;a[x++]=l.substring(0,i);l=l.substring(i+d.length);}return a"),e.getPercentPageViewed=new Function("n","var s=this,W=window,EL=W.addEventListener,AE=W.attachEvent,E=['load','unload','scroll','resize','zoom','keyup','mouseup','touchend','orientationchange','pan'];W.s_Obj=s;s_PPVid=(n=='-'?s.pageName:n)||s.pageName||location.href;if(!W.s_PPVevent){s.s_PPVg=function(n,r){var k='s_ppv',p=k+'l',c=s.c_r(n||r?k:p),a=c.indexOf(',')>-1?c.split(',',10):[''],l=a.length,i;a[0]=unescape(a[0]);r=r||(n&&n!=a[0])||0;a.length=10;if(typeof a[0]!='string')a[0]='';for(i=1;i<10;i++)a[i]=!r&&i<l?parseInt(a[i])||0:0;if(l<10||typeof a[9]!='string')a[9]='';if(r){s.c_w(p,c);s.c_w(k,'?')}return a};W.s_PPVevent=function(e){var W=window,D=document,B=D.body,E=D.documentElement,S=window.screen||0,Ho='offsetHeight',Hs='scrollHeight',Ts='scrollTop',Wc='clientWidth',Hc='clientHeight',C=100,M=Math,J='object',N='number',s=W.s_Obj||W.s||0;e=e&&typeof e==J?e.type||'':'';if(!e.indexOf('on'))e=e.substring(2);s_PPVi=W.s_PPVi||0;if(W.s_PPVt&&!e){clearTimeout(s_PPVt);s_PPVt=0;if(s_PPVi<2)s_PPVi++}if(typeof s==J){var h=M.max(B[Hs]||E[Hs],B[Ho]||E[Ho],B[Hc]||E[Hc]),X=W.innerWidth||E[Wc]||B[Wc]||0,Y=W.innerHeight||E[Hc]||B[Hc]||0,x=S?S.width:0,y=S?S.height:0,r=M.round(C*(W.devicePixelRatio||1))/C,b=(D.pageYOffset||E[Ts]||B[Ts]||0)+Y,p=h>0&&b>0?M.round(C*b/h):0,O=W.orientation,o=!isNaN(O)?M.abs(o)%180:Y>X?0:90,L=e=='load'||s_PPVi<1,a=s.s_PPVg(s_PPVid,L),V=function(i,v,f,n){i=parseInt(typeof a==J&&a.length>i?a[i]:'0')||0;v=typeof v!=N?i:v;v=f||v>i?v:i;return n?v:v>C?C:v<0?0:v};if(new RegExp('(iPod|iPad|iPhone)').exec(navigator.userAgent||'')&&o){o=x;x=y;y=o}o=o?'P':'L';a[9]=L?'':a[9].substring(0,1);s.c_w('s_ppv',escape(W.s_PPVid)+','+V(1,p,L)+','+(L||!V(2)?p:V(2))+','+V(3,b,L,1)+','+X+','+Y+','+x+','+y+','+r+','+a[9]+(a[9]==o?'':o))}if(!W.s_PPVt&&e!='unload')W.s_PPVt=setTimeout(W.s_PPVevent,333)};for(var f=W.s_PPVevent,i=0;i<E.length;i++)if(EL)EL(E[i],f,false);else if(AE)AE('on'+E[i],f);f()};var a=s.s_PPVg();return!n||n=='-'?a[1]:a"),e.p_fo=new Function("n","var s=this;if(!s.__fo){s.__fo=new Object;}if(!s.__fo[n]){s.__fo[n]=new Object;return 1;}else {return 0;}"),e.clickPast=new Function("scp","ct_ev","cp_ev","cpc","var s=this,scp,ct_ev,cp_ev,cpc,ev,tct;if(s.p_fo(ct_ev)==1){if(!cpc){cpc='s_cpc';}ev=s.events?s.events+',':'';if(scp){s.events=ev+ct_ev;s.c_w(cpc,1,0);}else{if(s.c_r(cpc)>=1){s.events=ev+cp_ev;s.c_w(cpc,0,0);}}}"),!e.__ccucr){function r(e){var t,a,n,r=this,i=(new Date,r.c_rr(e)),s=r.c_rspers();return i||(e=r.escape?r.escape(e):encodeURIComponent(e),t=s.indexOf(" "+e+"="),a=(t=(s=t<0?r.c_rr("s_sess"):s).indexOf(" "+e+"="))<0?t:s.indexOf("|",t),n=t<0?t:s.indexOf(";",t),a=a>0?a:n,i=t<0?"":r.unescape?r.unescape(s.substring(t+2+e.length,a<0?s.length:a)):decodeURIComponent(s.substring(t+2+e.length,a<0?s.length:a)))}function i(){var e=this.c_rr("s_pers"),t=(new Date).getTime(),a=null,n=[],r="";if(!e)return r;for(var i=0,s=(n=e.split(";")).length;i<s;i++)(a=n[i].match(/\|([0-9]+)$/))&&parseInt(a[1])>=t&&(r+=n[i]+";");return r}e.c_rr=e.c_r,e.__ccucr=!0,e.c_rspers=i,e.c_r=e.cookieRead=r}if(!e.__ccucw){function s(e,t,a){var n=_satellite.getVar("Consent Adobe");if(n&&!1===n.aa)return!1;var r,i,s,o,c,l=this,u=new Date,d=0,p="s_pers",g="s_sess",f=0,m=0;if(u.setTime(u.getTime()-6e4),l.c_rr(e)&&l.c_wr(e,"",u),e=l.escape?l.escape(e):encodeURIComponent(e),(s=(r=l.c_rspers()).indexOf(" "+e+"="))>-1&&(r=r.substring(0,s)+r.substring(r.indexOf(";",s)+1),f=1),(s=(i=l.c_rr(g)).indexOf(" "+e+"="))>-1&&(i=i.substring(0,s)+i.substring(i.indexOf(";",s)+1),m=1),u=new Date,a?(1==a&&(c=(a=new Date).getYear(),a.setYear(c+5+(c<1900?1900:0))),a.getTime()>u.getTime()&&(r+=" "+e+"="+(l.escape?l.escape(t):encodeURIComponent(t))+"|"+a.getTime()+";",f=1)):(i+=" "+e+"="+(l.escape?l.escape(t):encodeURIComponent(t))+";",m=1),i=i.replace(/%00/g,""),r=r.replace(/%00/g,""),m&&l.c_wr(g,i,0),f){for(o=r;o&&-1!=o.indexOf(";");){var v=parseInt(o.substring(o.indexOf("|")+1,o.indexOf(";")));o=o.substring(o.indexOf(";")+1),d=d<v?v:d}u.setTime(d),l.c_wr(p,r,u)}return t==l.c_r(l.unescape?l.unescape(e):decodeURIComponent(e))}e.c_wr=e.c_w,e.__ccucw=!0,e.c_w=e.cookieWrite=s}e.getFullReferringDomains=new Function("var i,s=this,dr=window.document.referrer,n=s.linkInternalFilters.split(',');if(dr){var r=dr.split('/')[2],l=n.length;for(i=0;i<=l;i++){if(r.indexOf(n[i])!=-1){r='';i=l+1;}}return r}")}},libraryCode:{type:"managed",accounts:{staging:["elsevier-global-dev"],production:["elsevier-global-prod"],development:["elsevier-global-dev"]},useActivityMap:!0,scopeTrackerGlobally:!0},trackerProperties:{charSet:"UTF-8",currencyCode:"%Page - Currency Code%",cookieLifetime:"********",trackingServer:"metrics.elsevier.com",trackInlineStats:!0,trackDownloadLinks:!0,trackExternalLinks:!0,linkExternalFilters:[],linkInternalFilters:["javascript:","mailto:","sciencedirect.com","tel:"],trackingServerSecure:"smetrics.elsevier.com",linkDownloadFileTypes:["avi","css","csv","doc","docx","eps","exe","jpg","js","m4v","mov","mp3","pdf","png","ppt","pptx","rar","svg","tab","txt","vsd","vxd","wav","wma","wmv","xls","xlsx","xml","zip"]}},modules:{"adobe-analytics/src/lib/actions/setVariables.js":{name:"set-variables",displayName:"Set Variables",script:function(e,t,a,n){"use strict";var r=a("../sharedModules/getTracker"),i=a("../helpers/applyTrackerVariables");e.exports=function(e,t){return r().then((function(a){n.logger.info("Set variables on the tracker."),i(a,e.trackerProperties),e.customSetup&&e.customSetup.source&&e.customSetup.source.call(t.element,t,a)}),(function(e){n.logger.error("Cannot set variables: "+e)}))}}},"adobe-analytics/src/lib/actions/sendBeacon.js":{name:"send-beacon",displayName:"Send Beacon",script:function(e,t,a,n){"use strict";var r=a("../sharedModules/getTracker"),i=a("../helpers/getNodeLinkText"),s=function(e){return e&&e.nodeName&&"a"===e.nodeName.toLowerCase()},o=function(e){return s(e)?i(e):"link clicked"},c=function(e,t,a){if("page"===t.type)n.logger.info("Firing page view beacon."),e.t();else{var r={linkType:t.linkType||"o",linkName:t.linkName||o(a)};n.logger.info("Firing link track beacon using the values: "+JSON.stringify(r)+"."),e.tl(s(a)?a:"true",r.linkType,r.linkName)}};e.exports=function(e,t){return r().then((function(a){c(a,e,t.element)}),(function(e){n.logger.error("Cannot send beacon: "+e)}))}}},"adobe-analytics/src/lib/actions/clearVariables.js":{name:"clear-variables",displayName:"Clear Variables",script:function(e,t,a,n){"use strict";var r=a("../sharedModules/getTracker");e.exports=function(){return r().then((function(e){e.clearVars&&(n.logger.info("Clear variables."),e.clearVars())}),(function(e){n.logger.error("Cannot clear variables: "+e)}))}}},"adobe-analytics/src/lib/sharedModules/getTracker.js":{script:function(e,t,a,n){"use strict";var r,i=a("@adobe/reactor-cookie"),s=a("@adobe/reactor-promise"),o=a("@adobe/reactor-window"),c=a("../helpers/settingsHelper"),l=a("../helpers/augmenters"),u=a("../helpers/applyTrackerVariables"),d=a("../helpers/loadLibrary"),p=a("../helpers/generateVersion")(n.buildInfo.turbineBuildDate),g="beforeSettings",f=n.getSharedModule("adobe-mcid","mcid-instance"),m=function(e){return!e||"true"===i.get(e)},v=function(e){return s.all(l.map((function(t){var a;try{a=t(e)}catch(e){setTimeout((function(){throw e}))}return s.resolve(a)}))).then((function(){return e}))},h=function(e){return f&&(n.logger.info("Setting MCID instance on the tracker."),e.visitor=f),e},b=function(e){return n.logger.info('Setting version on tracker: "'+p+'".'),void 0!==e.tagContainerMarker?e.tagContainerMarker=p:"string"==typeof e.version&&e.version.substring(e.version.length-5)!=="-"+p&&(e.version+="-"+p),e},k=function(e,t,a){return t.loadPhase===g&&t.source&&(n.logger.info("Calling custom script before settings."),t.source.call(o,a)),u(a,e||{}),t.loadPhase!==g&&t.source&&(n.logger.info("Calling custom script after settings."),t.source.call(o,a)),a},y=function(e,t){return c.isAudienceManagementEnabled(e)&&(t.loadModule("AudienceManagement"),n.logger.info("Initializing AudienceManagement module"),t.AudienceManagement.setup(e.moduleProperties.audienceManager.config)),t},D=(r=n.getExtensionSettings(),m(r.trackingCookieName)?d(r).then(v).then(h).then(b).then(k.bind(null,r.trackerProperties,r.customSetup||{})).then(y.bind(null,r)):s.reject("EU compliance was not acknowledged by the user."));e.exports=function(){return D}},name:"get-tracker",shared:!0},"adobe-analytics/src/lib/sharedModules/augmentTracker.js":{name:"augment-tracker",shared:!0,script:function(e,t,a){"use strict";var n=a("../helpers/augmenters");e.exports=function(e){n.push(e)}}},"adobe-analytics/src/lib/helpers/applyTrackerVariables.js":{script:function(e,t,a,n){"use strict";var r=a("@adobe/reactor-query-string"),i=a("@adobe/reactor-window"),s=/eVar([0-9]+)/,o=/prop([0-9]+)/,c=new RegExp("^(eVar[0-9]+)|(prop[0-9]+)|(hier[0-9]+)|campaign|purchaseID|channel|server|state|zip|pageType$"),l=function(e,t,a){return a.indexOf(e)===t},u=function(e,t,a){var n=Object.keys(t).filter(c.test.bind(c));return a&&n.push("events"),(n=n.concat((e.linkTrackVars||"").split(","))).filter((function(e,t){return"None"!==e&&e&&l(e,t,n)})).join(",")},d=function(e,t){var a=t.map((function(e){return e.name}));return(a=a.concat((e.linkTrackEvents||"").split(","))).filter((function(e,t){return"None"!==e&&l(e,t,a)})).join(",")},p=function(e,t,a){e[t]=a[t].join(",")},g=function(e,t,a){var n=a.dynamicVariablePrefix||"D=";a[t].forEach((function(t){var a;if("value"===t.type)a=t.value;else{var r=s.exec(t.value);if(r)a=n+"v"+r[1];else{var i=o.exec(t.value);i&&(a=n+"c"+i[1])}}e[t.name]=a}))},f={linkDownloadFileTypes:p,linkExternalFilters:p,linkInternalFilters:p,hierarchies:function(e,t,a){a[t].forEach((function(t){e[t.name]=t.sections.join(t.delimiter)}))},props:g,eVars:g,campaign:function(e,t,a){if("queryParam"===a[t].type){var n=r.parse(i.location.search);e[t]=n[a[t].value]}else e[t]=a[t].value},events:function(e,t,a){var n=a[t].map((function(e){var t=e.name;return e.id&&(t=[t,e.id].join(":")),e.value&&(t=[t,e.value].join("=")),t}));e[t]=n.join(",")}};e.exports=function(e,t){var a={};t=t||{},Object.keys(t).forEach((function(e){var n=f[e],r=t[e];n?n(a,e,t):a[e]=r})),a.events&&e.events&&e.events.length>0&&(a.events=e.events+","+a.events);var r=t&&t.events&&t.events.length>0,i=u(e,a,r);i&&(a.linkTrackVars=i);var s=d(e,t.events||[]);s&&(a.linkTrackEvents=s),n.logger.info('Applying the following properties on tracker: "'+JSON.stringify(a)+'".'),Object.keys(a).forEach((function(t){e[t]=a[t]}))}}},"adobe-analytics/src/lib/helpers/settingsHelper.js":{script:function(e,t,a,n){"use strict";var r=a("@adobe/reactor-window"),i={LIB_TYPES:{MANAGED:"managed",PREINSTALLED:"preinstalled",REMOTE:"remote",CUSTOM:"custom"},MANAGED_LIB_PATHS:{APP_MEASUREMENT:"AppMeasurement.js",ACTIVITY_MAP:"AppMeasurement_Module_ActivityMap.js",AUDIENCE_MANAGEMENT:"AppMeasurement_Module_AudienceManagement.js"},getReportSuites:function(e){var t=e.production;return e[n.environment.stage]&&(t=e[n.environment.stage]),t.join(",")},isActivityMapEnabled:function(e){return!(e.libraryCode&&!e.libraryCode.useActivityMap&&!1===e.libraryCode.useActivityMap)},isAudienceManagementEnabled:function(e){var t=!1;return e&&e.moduleProperties&&e.moduleProperties.audienceManager&&e.moduleProperties.audienceManager.config&&r&&r._satellite&&r._satellite.company&&r._satellite.company.orgId&&(t=!0),t}};e.exports=i}},"adobe-analytics/src/lib/helpers/augmenters.js":{script:function(e){"use strict";e.exports=[]}},"adobe-analytics/src/lib/helpers/loadLibrary.js":{script:function(e,t,a,n){"use strict";var r=a("@adobe/reactor-load-script"),i=a("@adobe/reactor-window"),s=a("@adobe/reactor-promise"),o=a("./settingsHelper"),c=a("./pollHelper"),l=function(e,t){if(!i.s_gi)throw new Error("Unable to create AppMeasurement tracker, `s_gi` function not found."+i.AppMeasurement);n.logger.info('Creating AppMeasurement tracker with these report suites: "'+t+'"');var a=i.s_gi(t);return e.libraryCode.scopeTrackerGlobally&&(n.logger.info("Setting the tracker as window.s"),i.s=a),a},u=function(e){var t=[];switch(e.libraryCode.type){case o.LIB_TYPES.MANAGED:t.push(n.getHostedLibFileUrl(o.MANAGED_LIB_PATHS.APP_MEASUREMENT)),o.isActivityMapEnabled(e)&&t.push(n.getHostedLibFileUrl(o.MANAGED_LIB_PATHS.ACTIVITY_MAP));break;case o.LIB_TYPES.CUSTOM:t.push(e.libraryCode.source);break;case o.LIB_TYPES.REMOTE:t.push("https:"===i.location.protocol?e.libraryCode.httpsUrl:e.libraryCode.httpUrl)}if(o.isAudienceManagementEnabled(e)){var a={namespace:i._satellite.company.orgId};e.moduleProperties.audienceManager.config.visitorService=a,t.push(n.getHostedLibFileUrl(o.MANAGED_LIB_PATHS.AUDIENCE_MANAGEMENT))}return t},d=function(e){return s.all(u(e).map((function(e){return n.logger.info("Loading script: "+e),r(e)})))},p=function(e,t){if(e.libraryCode.accounts)if(t.sa){var a=o.getReportSuites(e.libraryCode.accounts);n.logger.info('Setting the following report suites on the tracker: "'+a+'"'),t.sa(a)}else n.logger.warn("Cannot set report suites on tracker. `sa` method not available.");return t},g=function(e){if(i[e])return n.logger.info('Found tracker located at: "'+e+'".'),i[e];throw new Error('Cannot find the global variable name: "'+e+'".')};e.exports=function(e){var t=d(e);switch(e.libraryCode.type){case o.LIB_TYPES.MANAGED:var a=o.getReportSuites(e.libraryCode.accounts);return t.then(l.bind(null,e,a));case o.LIB_TYPES.PREINSTALLED:return t.then(c.poll.bind(null,i,e.libraryCode.trackerVariableName)).then(p.bind(null,e));case o.LIB_TYPES.CUSTOM:case o.LIB_TYPES.REMOTE:return t.then(g.bind(null,e.libraryCode.trackerVariableName)).then(p.bind(null,e));default:throw new Error("Cannot load library. Type not supported.")}}}},"adobe-analytics/src/lib/helpers/generateVersion.js":{script:function(e){"use strict";var t=8,a=function(e){return e.getUTCDate().toString(36)},n=function(e){return e.substr(e.length-1)},r=function(e){return Math.floor(e.getUTCHours()/t)},i=function(e){var t=(e.getUTCMonth()+1+12*r(e)).toString(36);return n(t)},s=function(e){return(e.getUTCFullYear()-2010).toString(36)};e.exports=function(e){var t=new Date(e);if(isNaN(t))throw new Error("Invalid date provided");return("L"+s(t)+i(t)+a(t)).toUpperCase()}}},"adobe-analytics/src/lib/helpers/pollHelper.js":{script:function(e,t,a,n){"use strict";var r=a("@adobe/reactor-promise"),i=40,s=250,o=function(e,t,a){n.logger.info('Found property located at: "'+t+'"].'),e(a)},c=function(e,t){return new r((function(a,n){if(e[t])return o(a,t,e[t]);var r=1,c=setInterval((function(){e[t]&&(o(a,t,e[t]),clearInterval(c)),r>=i&&(clearInterval(c),n(new Error('Bailing out. Cannot find the variable name: "'+t+'"].'))),r++}),s)}))};e.exports={poll:function(e,t){return n.logger.info('Waiting for the property to become accessible at: "'+t+'"].'),c(e,t)}}}},"adobe-analytics/src/lib/helpers/getNodeLinkText.js":{script:function(e){"use strict";var t=function(e){return e&&e.replace(/\s+/g," ").trim()},a=/^(SCRIPT|STYLE|LINK|CANVAS|NOSCRIPT|#COMMENT)$/i,n=function(e){return!(e&&e.nodeName&&e.nodeName.match(a))},r=function(e){var t=[],a=!1;return n(e)?(t.push(e),e.childNodes&&Array.prototype.slice.call(e.childNodes).forEach((function(e){var n=r(e);t=t.concat(n.supportedNodes),a=a||n.includesUnsupportedNodes}))):a=!0,{supportedNodes:t,includesUnsupportedNodes:a}},i=function(e,t,a){var n;return a&&a!==e.nodeName.toUpperCase()||(n=e.getAttribute(t)),n};e.exports=function(e){var a=t(e.innerText||e.textContent),n=r(e);if(!a||n.includesUnsupportedNodes){var s,o,c,l,u=[];n.supportedNodes.forEach((function(e){e.getAttribute&&(s=s||t(e.getAttribute("alt")),o=o||t(e.getAttribute("title")),c=c||t(i(e,"value","INPUT")),l=l||t(i(e,"src","IMG"))),e.nodeValue&&u.push(e.nodeValue)})),(a=t(u.join("")))||(a=t(s||o||c||l||""))}return a}}}}},"adobe-mcid":{displayName:"Experience Cloud ID Service",hostedLibFilesBaseUrl:"https://assets.adobedtm.com/extensions/EP31a59fd25d824db7be52972a70e94c1c/",settings:{orgId:"4D6368F454EC41940A4C98A6@AdobeOrg",variables:[{name:"serverState",value:"%serverState%"},{name:"trackingServer",value:"metrics.elsevier.com"},{name:"trackingServerSecure",value:"smetrics.elsevier.com"},{name:"marketingCloudServer",value:"metrics.elsevier.com"},{name:"marketingCloudServerSecure",value:"smetrics.elsevier.com"},{name:"cookieLifetime",value:"********"}]},modules:{"adobe-mcid/src/lib/sharedModules/mcidInstance.js":{script:function(e,t,a,n){"use strict";var r=a("@adobe/reactor-document"),i=a("../codeLibrary/VisitorAPI"),s=a("../../view/utils/timeUnits"),o=function(e){return e.reduce((function(e,t){var a=/^(true|false)$/i.test(t.value)?JSON.parse(t.value):t.value;return e[t.name]=a,e}),{})},c=function(e){var t=n.getExtensionSettings();if("string"!=typeof t.orgId)throw new TypeError("Org ID is not a string.");var a=o(t.variables||[]),r=t.doesOptInApply;r&&("boolean"==typeof r?a.doesOptInApply=r:t.optInCallback&&(a.doesOptInApply=t.optInCallback));var i=t.isOptInStorageEnabled;i&&(a.isOptInStorageEnabled=i);var c=t.optInCookieDomain;c&&(a.optInCookieDomain=c);var l=t.optInStorageExpiry;if(l){var u=t.timeUnit;if(u&&s[u]){var d=l*s[u];a.optInStorageExpiry=d}}else!0===i&&(a.optInStorageExpiry=33696e3);var p=t.previousPermissions;p&&(a.previousPermissions=p);var g=t.preOptInApprovals;if(g)a.preOptInApprovals=g;else{var f=t.preOptInApprovalInput;f&&(a.preOptInApprovals=f)}var m=t.isIabContext;m&&(a.isIabContext=m);var v=e.getInstance(t.orgId,a);return n.logger.info('Created instance using orgId: "'+t.orgId+'"'),n.logger.info("Set variables: "+JSON.stringify(a)),v.getMarketingCloudVisitorID((function(e){n.logger.info("Obtained Marketing Cloud Visitor Id: "+e)}),!0),v},l=function(e){return(n.getExtensionSettings().pathExclusions||[]).some((function(t){return t.valueIsRegex?new RegExp(t.value,"i").test(e):t.value===e}))},u=null;_satellite.getVisitorId=function(){return u},l(r.location.pathname)?n.logger.warn("MCID library not loaded. One of the path exclusions matches the current path."):u=c(i),e.exports=u},name:"mcid-instance",shared:!0},"adobe-mcid/src/lib/codeLibrary/VisitorAPI.js":{script:function(e){e.exports=(function(){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function a(){return{callbacks:{},add:function(e,t){this.callbacks[e]=this.callbacks[e]||[];var a=this.callbacks[e].push(t)-1,n=this;return function(){n.callbacks[e].splice(a,1)}},execute:function(e,t){if(this.callbacks[e]){t=(t=void 0===t?[]:t)instanceof Array?t:[t];try{for(;this.callbacks[e].length;){var a=this.callbacks[e].shift();"function"==typeof a?a.apply(null,t):a instanceof Array&&a[1].apply(a[0],t)}delete this.callbacks[e]}catch(e){}}},executeAll:function(e,t){
(t||e&&!I.isObjectEmpty(e))&&Object.keys(this.callbacks).forEach((function(t){var a=void 0!==e[t]?e[t]:"";this.execute(t,a)}),this)},hasCallbacks:function(){return Boolean(Object.keys(this.callbacks).length)}}}function n(e,t,a){var n=null==e?void 0:e[t];return void 0===n?a:n}function r(e){for(var t=/^\d+$/,a=0,n=e.length;a<n;a++)if(!t.test(e[a]))return!1;return!0}function i(e,t){for(;e.length<t.length;)e.push("0");for(;t.length<e.length;)t.push("0")}function s(e,t){for(var a=0;a<e.length;a++){var n=parseInt(e[a],10),r=parseInt(t[a],10);if(n>r)return 1;if(r>n)return-1}return 0}function o(e,t){if(e===t)return 0;var a=e.toString().split("."),n=t.toString().split(".");return r(a.concat(n))?(i(a,n),s(a,n)):NaN}function c(e){return e===Object(e)&&0===Object.keys(e).length}function l(e){return"function"==typeof e||e instanceof Array&&e.length}function u(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){return!0};this.log=ue("log",e,t),this.warn=ue("warn",e,t),this.error=ue("error",e,t)}function d(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).cookieName,t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).cookies;if(!e||!t)return{get:Se,set:Se,remove:Se};var a={remove:function(){t.remove(e)},get:function(){var a=t.get(e),n={};try{n=JSON.parse(a)}catch(a){n={}}return n},set:function(n,r){r=r||{};var i=a.get(),s=Object.assign(i,n);t.set(e,JSON.stringify(s),{domain:r.optInCookieDomain||"",cookieLifetime:r.optInStorageExpiry||3419e4,secure:r.secure,sameSite:r.sameSite,expires:!0})}};return a}function p(e){this.name=this.constructor.name,this.message=e,"function"==typeof Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error(e).stack}function g(){function e(e,t){var a=me(e);return a.length?a.every((function(e){return!!t[e]})):ve(t)}function t(){_(P),V(te.COMPLETE),b(h.status,h.permissions),o&&v.set(h.permissions,{optInCookieDomain:c,optInStorageExpiry:l,secure:p,sameSite:g}),k.execute(Ae)}function a(e){return function(a,n){if(!he(a))throw new Error("[OptIn] Invalid category(-ies). Please use the `OptIn.Categories` enum.");return V(te.CHANGED),Object.assign(P,be(me(a),e)),n||t(),h}}var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.doesOptInApply,i=n.previousPermissions,s=n.preOptInApprovals,o=n.isOptInStorageEnabled,c=n.optInCookieDomain,l=n.optInStorageExpiry,u=n.isIabContext,p=n.secureCookie,g=n.sameSiteCookie,f=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).cookies,m=Pe(i);Ve(m,"Invalid `previousPermissions`!"),Ve(s,"Invalid `preOptInApprovals`!");var v=d({cookieName:"adobeujs-optin"},{cookies:f}),h=this,b=ee(h),k=oe(),y=De(m),D=De(s),C=o?v.get():{},w={},T=function(e,t){return Ce(e)||t&&Ce(t)?te.COMPLETE:te.PENDING}(y,C),S=function(e,t,a){var n=be(se,!r);return r?Object.assign({},n,e,t,a):n}(D,y,C),P=ke(S),V=function(e){return T=e},_=function(e){return S=e};h.deny=a(!1),h.approve=a(!0),h.denyAll=h.deny.bind(h,se),h.approveAll=h.approve.bind(h,se),h.isApproved=function(t){return e(t,h.permissions)},h.isPreApproved=function(t){return e(t,D)},h.fetchPermissions=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=t?h.on(te.COMPLETE,e):Se;return!r||r&&h.isComplete||s?e(h.permissions):t||k.add(Ae,(function(){return e(h.permissions)})),a},h.complete=function(){h.status===te.CHANGED&&t()},h.registerPlugin=function(e){if(!e||!e.name||"function"!=typeof e.onRegister)throw new Error(Oe);w[e.name]||(w[e.name]=e,e.onRegister.call(e,h))},h.execute=Ie(w),h.memoizeContent=function(e){Te(e)&&v.set(e,{optInCookieDomain:c,optInStorageExpiry:l,secure:p,sameSite:g})},h.getMemoizedContent=function(e){var t=v.get();if(t)return t[e]},Object.defineProperties(h,{permissions:{get:function(){return S}},status:{get:function(){return T}},Categories:{get:function(){return ae}},doesOptInApply:{get:function(){return!!r}},isPending:{get:function(){return h.status===te.PENDING}},isComplete:{get:function(){return h.status===te.COMPLETE}},__plugins:{get:function(){return Object.keys(w)}},isIabContext:{get:function(){return u}}})}function f(e,t){function a(){r=null,e.call(e,new p("The call took longer than you wanted!"))}function n(){r&&(clearTimeout(r),e.apply(e,arguments))}if(void 0===t)return e;var r=setTimeout(a,t);return n}function m(){if(window.__tcfapi)return window.__tcfapi;var e=window;if(e!==window.top){for(var t;!t;){e=e.parent;try{e.frames.__tcfapiLocator&&(t=e)}catch(e){}if(e===window.top)break}if(t){var a={};return window.__tcfapi=function(e,n,r,i){var s=Math.random()+"",o={__tcfapiCall:{command:e,parameter:i,version:n,callId:s}};a[s]=r,t.postMessage(o,"*")},window.addEventListener("message",(function(e){var t=e.data;if("string"==typeof t)try{t=JSON.parse(e.data)}catch(e){}if(t.__tcfapiReturn){var n=t.__tcfapiReturn;"function"==typeof a[n.callId]&&(a[n.callId](n.returnValue,n.success),delete a[n.callId])}}),!1),window.__tcfapi}ge.error("__tcfapi not found")}else ge.error("__tcfapi not found")}function v(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=!0===e.vendor.consents[t],r=a.every((function(t){return!0===e.purpose.consents[t]}));return n&&r}function h(){var e=this;e.name="iabPlugin",e.version="0.0.2";var t,a=oe(),n={transparencyAndConsentData:null},r=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n[e]=t};e.fetchConsentData=function(e){var t=f(e.callback,e.timeout);i({callback:t})},e.isApproved=function(e){var t=e.callback,a=e.category,r=e.timeout;if(n.transparencyAndConsentData)return t(null,v(n.transparencyAndConsentData,ne[a],re[a]));var s=f((function(e,n){t(e,v(n,ne[a],re[a]))}),r);i({category:a,callback:s})},e.onRegister=function(a){t=a;var n=Object.keys(ne),r=function(e,t){!e&&t&&(n.forEach((function(e){var n=v(t,ne[e],re[e]);a[n?"approve":"deny"](e,!0)})),a.complete())};e.fetchConsentData({callback:r})};var i=function(e){var i=e.callback;if(n.transparencyAndConsentData)return i(null,n.transparencyAndConsentData);a.add("FETCH_CONSENT_DATA",i),s((function(e,i){if(i){var s=ke(e),o=t.getMemoizedContent("iabConsentHash"),c=pe(s.tcString).toString(32);s.consentString=e.tcString,s.hasConsentChangedSinceLastCmpPull=o!==c,r("transparencyAndConsentData",s),t.memoizeContent({iabConsentHash:c})}a.execute("FETCH_CONSENT_DATA",[null,n.transparencyAndConsentData])}))},s=function(e){var t=Ee(ne),a=m();"function"==typeof a&&a("getTCData",2,e,t)}}var b="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};Object.assign=Object.assign||function(e){for(var t,a,n=1;n<arguments.length;++n)for(t in a=arguments[n])Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t]);return e};var k,y,D={MESSAGES:{HANDSHAKE:"HANDSHAKE",GETSTATE:"GETSTATE",PARENTSTATE:"PARENTSTATE"},STATE_KEYS_MAP:{MCMID:"MCMID",MCAID:"MCAID",MCAAMB:"MCAAMB",MCAAMLH:"MCAAMLH",MCOPTOUT:"MCOPTOUT",CUSTOMERIDS:"CUSTOMERIDS"},ASYNC_API_MAP:{MCMID:"getMarketingCloudVisitorID",MCAID:"getAnalyticsVisitorID",MCAAMB:"getAudienceManagerBlob",MCAAMLH:"getAudienceManagerLocationHint",MCOPTOUT:"isOptedOut",ALLFIELDS:"getVisitorValues"},SYNC_API_MAP:{CUSTOMERIDS:"getCustomerIDs"},ALL_APIS:{MCMID:"getMarketingCloudVisitorID",MCAAMB:"getAudienceManagerBlob",MCAAMLH:"getAudienceManagerLocationHint",MCOPTOUT:"isOptedOut",MCAID:"getAnalyticsVisitorID",CUSTOMERIDS:"getCustomerIDs",ALLFIELDS:"getVisitorValues"},FIELDGROUP_TO_FIELD:{MC:"MCMID",A:"MCAID",AAM:"MCAAMB"},FIELDS:{MCMID:"MCMID",MCOPTOUT:"MCOPTOUT",MCAID:"MCAID",MCAAMLH:"MCAAMLH",MCAAMB:"MCAAMB"},AUTH_STATE:{UNKNOWN:0,AUTHENTICATED:1,LOGGED_OUT:2},OPT_OUT:{GLOBAL:"global"},SAME_SITE_VALUES:{LAX:"Lax",STRICT:"Strict",NONE:"None"}},C=D.STATE_KEYS_MAP,w=function(e){function t(){}function a(t,a){var n=this;return function(){var r=e(0,t),i={};return i[t]=r,n.setStateAndPublish(i),a(r),r}}this.getMarketingCloudVisitorID=function(e){e=e||t;var n=this.findField(C.MCMID,e),r=a.call(this,C.MCMID,e);return void 0!==n?n:r()},this.getVisitorValues=function(e){this.getMarketingCloudVisitorID((function(t){e({MCMID:t})}))}},T=D.MESSAGES,S=D.ASYNC_API_MAP,P=D.SYNC_API_MAP,V=function(){function e(){}function t(e,t){var a=this;return function(){return a.callbackRegistry.add(e,t),a.messageParent(T.GETSTATE),""}}function a(a){this[S[a]]=function(n){n=n||e;var r=this.findField(a,n),i=t.call(this,a,n);return void 0!==r?r:i()}}function n(t){this[P[t]]=function(){return this.findField(t,e)||{}}}Object.keys(S).forEach(a,this),Object.keys(P).forEach(n,this)},_=D.ASYNC_API_MAP,E=function(){Object.keys(_).forEach((function(e){this[_[e]]=function(t){this.callbackRegistry.add(e,t)}}),this)},I=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(t,a){a.isObjectEmpty=function(e){return e===Object(e)&&0===Object.keys(e).length},a.isValueEmpty=function(e){return""===e||a.isObjectEmpty(e)};var n=function(){var e=navigator.appName,t=navigator.userAgent;return"Microsoft Internet Explorer"===e||t.indexOf("MSIE ")>=0||t.indexOf("Trident/")>=0&&t.indexOf("Windows NT 6")>=0};a.getIeVersion=function(){return document.documentMode?document.documentMode:n()?7:null},a.isFirefox=function(e){return!!/Firefox\/([0-9\.]+)(?:\s|$)/.test(e||window.navigator.userAgent)},a.encodeAndBuildRequest=function(e,t){return e.map(encodeURIComponent).join(t)},a.isObject=function(t){return null!==t&&"object"===e(t)&&!1===Array.isArray(t)},a.defineGlobalNamespace=function(){return window.adobe=a.isObject(window.adobe)?window.adobe:{},window.adobe},a.pluck=function(e,t){return t.reduce((function(t,a){return e[a]&&(t[a]=e[a]),t}),Object.create(null))},a.parseOptOut=function(e,t,a){t||(t=a,e.d_optout&&e.d_optout instanceof Array&&(t=e.d_optout.join(",")));var n=parseInt(e.d_ottl,10);return isNaN(n)&&(n=7200),{optOut:t,d_ottl:n}},a.normalizeBoolean=function(e){var t=e;return"true"===e?t=!0:"false"===e&&(t=!1),t}})),A=(I.isObjectEmpty,I.isValueEmpty,I.getIeVersion,I.isFirefox,I.encodeAndBuildRequest,I.isObject,I.defineGlobalNamespace,I.pluck,I.parseOptOut,I.normalizeBoolean,a),O=D.MESSAGES,L={0:"prefix",1:"orgID",2:"state"},j=function(e,t){this.parse=function(e){try{var t={};return e.data.split("|").forEach((function(e,a){void 0!==e&&(t[L[a]]=2!==a?e:JSON.parse(e))})),t}catch(e){}},this.isInvalid=function(a){var n=this.parse(a);if(!n||Object.keys(n).length<2)return!0;var r=e!==n.orgID,i=!t||a.origin!==t,s=-1===Object.keys(O).indexOf(n.prefix);return r||i||s},this.send=function(a,n,r){var i=n+"|"+e;r&&r===Object(r)&&(i+="|"+JSON.stringify(r));try{a.postMessage(i,t)}catch(e){}}},x=D.MESSAGES,M=function(e,t,a,n){function r(e){Object.assign(g,e)}function i(e){Object.assign(g.state,e),Object.assign(g.state.ALLFIELDS,e),g.callbackRegistry.executeAll(g.state)}function s(e){if(!v.isInvalid(e)){m=!1;var t=v.parse(e);g.setStateAndPublish(t.state)}}function o(e){!m&&f&&(m=!0,v.send(n,e))}function c(){r(new w(a._generateID)),g.getMarketingCloudVisitorID(),g.callbackRegistry.executeAll(g.state,!0),b.removeEventListener("message",l)}function l(e){if(!v.isInvalid(e)){var t=v.parse(e);m=!1,b.clearTimeout(g._handshakeTimeout),b.removeEventListener("message",l),r(new V(g)),b.addEventListener("message",s),g.setStateAndPublish(t.state),g.callbackRegistry.hasCallbacks()&&o(x.GETSTATE)}}function u(){f&&postMessage?(b.addEventListener("message",l),o(x.HANDSHAKE),g._handshakeTimeout=setTimeout(c,250)):c()}function d(){b.s_c_in||(b.s_c_il=[],b.s_c_in=0),g._c="Visitor",g._il=b.s_c_il,g._in=b.s_c_in,g._il[g._in]=g,b.s_c_in++}function p(){function e(e){0!==e.indexOf("_")&&"function"==typeof a[e]&&(g[e]=function(){})}Object.keys(a).forEach(e),g.getSupplementalDataID=a.getSupplementalDataID,g.isAllowed=function(){return!0}}var g=this,f=t.whitelistParentDomain;g.state={ALLFIELDS:{}},g.version=a.version,g.marketingCloudOrgID=e,g.cookieDomain=a.cookieDomain||"",g._instanceType="child";var m=!1,v=new j(e,f);g.callbackRegistry=A(),g.init=function(){d(),p(),r(new E(g)),u()},g.findField=function(e,t){if(void 0!==g.state[e])return t(g.state[e]),g.state[e]},g.messageParent=o,g.setStateAndPublish=i},N=D.MESSAGES,R=D.ALL_APIS,F=D.ASYNC_API_MAP,U=D.FIELDGROUP_TO_FIELD,B=function(e,t){function a(){var t={};return Object.keys(R).forEach((function(a){var n=R[a],r=e[n]();I.isValueEmpty(r)||(t[a]=r)})),t}function n(){var t=[];return e._loading&&Object.keys(e._loading).forEach((function(a){if(e._loading[a]){var n=U[a];t.push(n)}})),t.length?t:null}function r(t){return function a(){var r=n();if(r){var i=F[r[0]];e[i](a,!0)}else t()}}function i(e,n){var r=a();t.send(e,n,r)}function s(e){c(e),i(e,N.HANDSHAKE)}function o(e){r((function(){i(e,N.PARENTSTATE)}))()}function c(a){function n(n){r.call(e,n),t.send(a,N.PARENTSTATE,{CUSTOMERIDS:e.getCustomerIDs()})}var r=e.setCustomerIDs;e.setCustomerIDs=n}return function(e){t.isInvalid(e)||(t.parse(e).prefix===N.HANDSHAKE?s:o)(e.source)}},H=function(e,t){function a(e){return function(a){n[e]=a,++r===i&&t(n)}}var n={},r=0,i=Object.keys(e).length;Object.keys(e).forEach((function(t){var n=e[t];if(n.fn){var r=n.args||[];r.unshift(a(t)),n.fn.apply(n.context||null,r)}}))},W={get:function(e){e=encodeURIComponent(e);var t=(";"+document.cookie).split(" ").join(";"),a=t.indexOf(";"+e+"="),n=a<0?a:t.indexOf(";",a+1);return a<0?"":decodeURIComponent(t.substring(a+2+e.length,n<0?t.length:n))},set:function(e,t,a){var r=n(a,"cookieLifetime"),i=n(a,"expires"),s=n(a,"domain"),o=n(a,"secure"),c=n(a,"sameSite"),l=o?"Secure":"",u=c?"SameSite="+c+";":"";if(i&&"SESSION"!==r&&"NONE"!==r){var d=""!==t?parseInt(r||0,10):-60;if(d)(i=new Date).setTime(i.getTime()+1e3*d);else if(1===i){var p=(i=new Date).getYear();i.setYear(p+2+(p<1900?1900:0))}}else i=0;return e&&"NONE"!==r?(document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+"; path=/;"+(i?" expires="+i.toGMTString()+";":"")+(s?" domain="+s+";":"")+u+l,this.get(e)===t):0},remove:function(e,t){var a=n(t,"domain");a=a?" domain="+a+";":"";var r=n(t,"secure"),i=n(t,"sameSite"),s=r?"Secure":"",o=i?"SameSite="+i+";":"";document.cookie=encodeURIComponent(e)+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"+a+o+s}},Y=function(e,t){!e&&b.location&&(e=b.location.hostname);var a,n=e.split("."),r=t||{};for(a=n.length-2;a>=0;a--)if(r.domain=n.slice(a).join("."),W.set("TEST_AMCV_COOKIE_WRITE","cookie",r))return W.remove("TEST_AMCV_COOKIE_WRITE",r),r.domain;return""},G={compare:o,isLessThan:function(e,t){return o(e,t)<0},areVersionsDifferent:function(e,t){return 0!==o(e,t)},isGreaterThan:function(e,t){return o(e,t)>0},isEqual:function(e,t){return 0===o(e,t)}},X=!!b.postMessage,q={postMessage:function(e,t,a){var n=1;t&&(X?a.postMessage(e,t.replace(/([^:]+:\/\/[^\/]+).*/,"$1")):t&&(a.location=t.replace(/#.*$/,"")+"#"+ +new Date+n+++"&"+e))},receiveMessage:function(e,t){var a;try{X&&(e&&(a=function(a){if("string"==typeof t&&a.origin!==t||"[object Function]"===Object.prototype.toString.call(t)&&!1===t(a.origin))return!1;e(a)}),b.addEventListener?b[e?"addEventListener":"removeEventListener"]("message",a):b[e?"attachEvent":"detachEvent"]("onmessage",a))}catch(e){}}},z=function(e){var t,a,n="0123456789",r="",i="",s=8,o=10,c=10,l=(""+Date.now()).substr(-6).split("").reverse("").join("");if(1==e){for(n+="ABCDEF",t=0;16>t;t++)a=Math.floor(Math.random()*s),4>t&&l[t]<s&&(a=+l[t]),r+=n.substring(a,a+1),a=Math.floor(Math.random()*s),i+=n.substring(a,a+1),s=16;return r+"-"+i}for(t=0;19>t;t++)a=Math.floor(Math.random()*o),6>t&&l[t]<o?(r+=l[t],a=l[t]):r+=n.substring(a,a+1),0===t&&9==a?o=3:((1==t||2==t)&&10!=o&&2>a||2<t)&&(o=10),a=Math.floor(Math.random()*c),i+=n.substring(a,a+1),0===t&&9==a?c=3:((1==t||2==t)&&10!=c&&2>a||2<t)&&(c=10);return r+i},J=function(e){return{corsMetadata:function(){var e="none",t=!0;return"undefined"!=typeof XMLHttpRequest&&XMLHttpRequest===Object(XMLHttpRequest)&&("withCredentials"in new XMLHttpRequest?e="XMLHttpRequest":"undefined"!=typeof XDomainRequest&&XDomainRequest===Object(XDomainRequest)&&(t=!1),Object.prototype.toString.call(b.HTMLElement).indexOf("Constructor")>0&&(t=!1)),{corsType:e,corsCookiesEnabled:t}}(),getCORSInstance:function(){return"none"===this.corsMetadata.corsType?null:new b[this.corsMetadata.corsType]},fireCORS:function(t,a){function n(e){var a;try{if((a=JSON.parse(e))!==Object(a))return void r.handleCORSError(t,null,"Response is not JSON")}catch(e){return void r.handleCORSError(t,e,"Error parsing response as JSON")}try{for(var n=t.callback,i=b,s=0;s<n.length;s++)i=i[n[s]];i(a)}catch(e){r.handleCORSError(t,e,"Error forming callback function")}}var r=this;a&&(t.loadErrorHandler=a);try{var i=this.getCORSInstance();i.open("get",t.corsUrl+"&ts="+(new Date).getTime(),!0),"XMLHttpRequest"===this.corsMetadata.corsType&&(i.withCredentials=!0,i.timeout=e.loadTimeout,i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.onreadystatechange=function(){4===this.readyState&&200===this.status&&n(this.responseText)}),i.onerror=function(e){r.handleCORSError(t,e,"onerror")},i.ontimeout=function(e){r.handleCORSError(t,e,"ontimeout")},i.send(),e._log.requests.push(t.corsUrl)}catch(e){this.handleCORSError(t,e,"try-catch")}},handleCORSError:function(t,a,n){e.CORSErrors.push({corsData:t,error:a,description:n}),t.loadErrorHandler&&("ontimeout"===n?t.loadErrorHandler(!0):t.loadErrorHandler(!1))}}},K={POST_MESSAGE_ENABLED:!!b.postMessage,DAYS_BETWEEN_SYNC_ID_CALLS:1,MILLIS_PER_DAY:864e5,ADOBE_MC:"adobe_mc",ADOBE_MC_SDID:"adobe_mc_sdid",VALID_VISITOR_ID_REGEX:/^[0-9a-fA-F\-]+$/,ADOBE_MC_TTL_IN_MIN:5,VERSION_REGEX:/vVersion\|((\d+\.)?(\d+\.)?(\*|\d+))(?=$|\|)/,FIRST_PARTY_SERVER_COOKIE:"s_ecid"},$=function(e,t){var a=b.document;return{THROTTLE_START:3e4,MAX_SYNCS_LENGTH:649,throttleTimerSet:!1,id:null,onPagePixels:[],iframeHost:null,getIframeHost:function(e){if("string"==typeof e){var t=e.split("/");return t[0]+"//"+t[2]}},subdomain:null,url:null,getUrl:function(){var t,n="http://fast.",r="?d_nsid="+e.idSyncContainerID+"#"+encodeURIComponent(a.location.origin);return this.subdomain||(this.subdomain="nosubdomainreturned"),e.loadSSL&&(n=e.idSyncSSLUseAkamai?"https://fast.":"https://"),t=n+this.subdomain+".demdex.net/dest5.html"+r,this.iframeHost=this.getIframeHost(t),this.id="destination_publishing_iframe_"+this.subdomain+"_"+e.idSyncContainerID,t},checkDPIframeSrc:function(){var t="?d_nsid="+e.idSyncContainerID+"#"+encodeURIComponent(a.location.href);"string"==typeof e.dpIframeSrc&&e.dpIframeSrc.length&&(this.id="destination_publishing_iframe_"+(e._subdomain||this.subdomain||(new Date).getTime())+"_"+e.idSyncContainerID,this.iframeHost=this.getIframeHost(e.dpIframeSrc),this.url=e.dpIframeSrc+t)},idCallNotProcesssed:null,doAttachIframe:!1,startedAttachingIframe:!1,iframeHasLoaded:null,iframeIdChanged:null,newIframeCreated:null,originalIframeHasLoadedAlready:null,iframeLoadedCallbacks:[],regionChanged:!1,timesRegionChanged:0,sendingMessages:!1,messages:[],messagesPosted:[],messagesReceived:[],messageSendingInterval:K.POST_MESSAGE_ENABLED?null:100,onPageDestinationsFired:[],jsonForComparison:[],jsonDuplicates:[],jsonWaiting:[],jsonProcessed:[],canSetThirdPartyCookies:!0,receivedThirdPartyCookiesNotification:!1,readyToAttachIframePreliminary:function(){return!(e.idSyncDisableSyncs||e.disableIdSyncs||e.idSyncDisable3rdPartySyncing||e.disableThirdPartyCookies||e.disableThirdPartyCalls)},readyToAttachIframe:function(){return this.readyToAttachIframePreliminary()&&(this.doAttachIframe||e._doAttachIframe)&&(this.subdomain&&"nosubdomainreturned"!==this.subdomain||e._subdomain)&&this.url&&!this.startedAttachingIframe},attachIframe:function(){function e(){(r=a.createElement("iframe")).sandbox="allow-scripts allow-same-origin",r.title="Adobe ID Syncing iFrame",r.id=n.id,r.name=n.id+"_name",r.style.cssText="display: none; width: 0; height: 0;",r.src=n.url,n.newIframeCreated=!0,t(),a.body.appendChild(r)}function t(e){r.addEventListener("load",(function(){r.className="aamIframeLoaded",n.iframeHasLoaded=!0,n.fireIframeLoadedCallbacks(e),n.requestToProcess()}))}this.startedAttachingIframe=!0;var n=this,r=a.getElementById(this.id);r?"IFRAME"!==r.nodeName?(this.id+="_2",this.iframeIdChanged=!0,e()):(this.newIframeCreated=!1,"aamIframeLoaded"!==r.className?(this.originalIframeHasLoadedAlready=!1,t("The destination publishing iframe already exists from a different library, but hadn't loaded yet.")):(this.originalIframeHasLoadedAlready=!0,this.iframeHasLoaded=!0,this.iframe=r,this.fireIframeLoadedCallbacks("The destination publishing iframe already exists from a different library, and had loaded alresady."),this.requestToProcess())):e(),this.iframe=r},fireIframeLoadedCallbacks:function(e){this.iframeLoadedCallbacks.forEach((function(t){"function"==typeof t&&t({message:e||"The destination publishing iframe was attached and loaded successfully."})})),this.iframeLoadedCallbacks=[]},requestToProcess:function(t){function a(){r.jsonForComparison.push(t),r.jsonWaiting.push(t),r.processSyncOnPage(t)}var n,r=this;if(t===Object(t)&&t.ibs)if(n=JSON.stringify(t.ibs||[]),this.jsonForComparison.length){var i,s,o,c=!1;for(i=0,s=this.jsonForComparison.length;i<s;i++)if(o=this.jsonForComparison[i],n===JSON.stringify(o.ibs||[])){c=!0;break}c?this.jsonDuplicates.push(t):a()}else a();if((this.receivedThirdPartyCookiesNotification||!K.POST_MESSAGE_ENABLED||this.iframeHasLoaded)&&this.jsonWaiting.length){var l=this.jsonWaiting.shift();this.process(l),this.requestToProcess()}e.idSyncDisableSyncs||e.disableIdSyncs||!this.iframeHasLoaded||!this.messages.length||this.sendingMessages||(this.throttleTimerSet||(this.throttleTimerSet=!0,setTimeout((function(){r.messageSendingInterval=K.POST_MESSAGE_ENABLED?null:150}),this.THROTTLE_START)),this.sendingMessages=!0,this.sendMessages())},getRegionAndCheckIfChanged:function(t,a){var n=e._getField("MCAAMLH"),r=t.d_region||t.dcs_region;return n?r&&(e._setFieldExpire("MCAAMLH",a),e._setField("MCAAMLH",r),parseInt(n,10)!==r&&(this.regionChanged=!0,this.timesRegionChanged++,e._setField("MCSYNCSOP",""),e._setField("MCSYNCS",""),n=r)):(n=r)&&(e._setFieldExpire("MCAAMLH",a),e._setField("MCAAMLH",n)),n||(n=""),n},processSyncOnPage:function(e){var t,a,n,r;if((t=e.ibs)&&t instanceof Array&&(a=t.length))for(n=0;n<a;n++)(r=t[n]).syncOnPage&&this.checkFirstPartyCookie(r,"","syncOnPage")},process:function(e){var t,a,n,r,i,s=encodeURIComponent,o=!1;if((t=e.ibs)&&t instanceof Array&&(a=t.length))for(o=!0,n=0;n<a;n++)r=t[n],i=[s("ibs"),s(r.id||""),s(r.tag||""),I.encodeAndBuildRequest(r.url||[],","),s(r.ttl||""),"","",r.fireURLSync?"true":"false"],r.syncOnPage||(this.canSetThirdPartyCookies?this.addMessage(i.join("|")):r.fireURLSync&&this.checkFirstPartyCookie(r,i.join("|")));o&&this.jsonProcessed.push(e)},checkFirstPartyCookie:function(t,a,n){var r="syncOnPage"===n,i=r?"MCSYNCSOP":"MCSYNCS";e._readVisitor();var s,o,c=e._getField(i),l=!1,u=!1,d=Math.ceil((new Date).getTime()/K.MILLIS_PER_DAY);c?(s=c.split("*"),l=(o=this.pruneSyncData(s,t.id,d)).dataPresent,u=o.dataValid,l&&u||this.fireSync(r,t,a,s,i,d)):(s=[],this.fireSync(r,t,a,s,i,d))},pruneSyncData:function(e,t,a){var n,r,i,s=!1,o=!1;for(r=0;r<e.length;r++)n=e[r],i=parseInt(n.split("-")[1],10),n.match("^"+t+"-")?(s=!0,a<i?o=!0:(e.splice(r,1),r--)):a>=i&&(e.splice(r,1),r--);return{dataPresent:s,dataValid:o}},manageSyncsSize:function(e){if(e.join("*").length>this.MAX_SYNCS_LENGTH)for(e.sort((function(e,t){return parseInt(e.split("-")[1],10)-parseInt(t.split("-")[1],10)}));e.join("*").length>this.MAX_SYNCS_LENGTH;)e.shift()},fireSync:function(t,a,n,r,i,s){var o=this;if(t){if("img"===a.tag){var c,l,u,d,p=a.url,g=e.loadSSL?"https:":"http:";for(c=0,l=p.length;c<l;c++){u=p[c],d=/^\/\//.test(u);var f=new Image;f.addEventListener("load",function(t,a,n,r){return function(){o.onPagePixels[t]=null,e._readVisitor();var s,c,l,u,d=e._getField(i),p=[];if(d)for(c=0,l=(s=d.split("*")).length;c<l;c++)(u=s[c]).match("^"+a.id+"-")||p.push(u);o.setSyncTrackingData(p,a,n,r)}}(this.onPagePixels.length,a,i,s)),f.src=(d?g:"")+u,this.onPagePixels.push(f)}}}else this.addMessage(n),this.setSyncTrackingData(r,a,i,s)},addMessage:function(t){var a=encodeURIComponent(e._enableErrorReporting?"---destpub-debug---":"---destpub---");this.messages.push((K.POST_MESSAGE_ENABLED?"":a)+t)},setSyncTrackingData:function(t,a,n,r){t.push(a.id+"-"+(r+Math.ceil(a.ttl/60/24))),this.manageSyncsSize(t),e._setField(n,t.join("*"))},sendMessages:function(){var e,t=this,a="",n=encodeURIComponent;this.regionChanged&&(a=n("---destpub-clear-dextp---"),this.regionChanged=!1),this.messages.length?K.POST_MESSAGE_ENABLED?(e=a+n("---destpub-combined---")+this.messages.join("%01"),this.postMessage(e),this.messages=[],this.sendingMessages=!1):(e=this.messages.shift(),this.postMessage(a+e),setTimeout((function(){t.sendMessages()}),this.messageSendingInterval)):this.sendingMessages=!1},postMessage:function(e){q.postMessage(e,this.url,this.iframe.contentWindow),this.messagesPosted.push(e)},receiveMessage:function(e){var t,a=/^---destpub-to-parent---/;"string"==typeof e&&a.test(e)&&("canSetThirdPartyCookies"===(t=e.replace(a,"").split("|"))[0]&&(this.canSetThirdPartyCookies="true"===t[1],this.receivedThirdPartyCookiesNotification=!0,this.requestToProcess()),this.messagesReceived.push(e))},processIDCallData:function(n){(null==this.url||n.subdomain&&"nosubdomainreturned"===this.subdomain)&&("string"==typeof e._subdomain&&e._subdomain.length?this.subdomain=e._subdomain:this.subdomain=n.subdomain||"",this.url=this.getUrl()),n.ibs instanceof Array&&n.ibs.length&&(this.doAttachIframe=!0),this.readyToAttachIframe()&&(e.idSyncAttachIframeOnWindowLoad?(t.windowLoaded||"complete"===a.readyState||"loaded"===a.readyState)&&this.attachIframe():this.attachIframeASAP()),"function"==typeof e.idSyncIDCallResult?e.idSyncIDCallResult(n):this.requestToProcess(n),"function"==typeof e.idSyncAfterIDCallResult&&e.idSyncAfterIDCallResult(n)},canMakeSyncIDCall:function(t,a){return e._forceSyncIDCall||!t||a-t>K.DAYS_BETWEEN_SYNC_ID_CALLS},attachIframeASAP:function(){function e(){t.startedAttachingIframe||(a.body?t.attachIframe():setTimeout(e,30))}var t=this;e()}}},Q={audienceManagerServer:{},audienceManagerServerSecure:{},cookieDomain:{},cookieLifetime:{},cookieName:{},doesOptInApply:{type:"boolean"},disableThirdPartyCalls:{type:"boolean"},discardTrackingServerECID:{type:"boolean"},idSyncAfterIDCallResult:{},idSyncAttachIframeOnWindowLoad:{type:"boolean"},idSyncContainerID:{},idSyncDisable3rdPartySyncing:{type:"boolean"},disableThirdPartyCookies:{type:"boolean"},idSyncDisableSyncs:{type:"boolean"},disableIdSyncs:{type:"boolean"},idSyncIDCallResult:{},idSyncSSLUseAkamai:{type:"boolean"},isCoopSafe:{type:"boolean"},isIabContext:{type:"boolean"},isOptInStorageEnabled:{type:"boolean"},loadSSL:{type:"boolean"},loadTimeout:{},marketingCloudServer:{},marketingCloudServerSecure:{},optInCookieDomain:{},optInStorageExpiry:{},overwriteCrossDomainMCIDAndAID:{type:"boolean"},preOptInApprovals:{},previousPermissions:{},resetBeforeVersion:{},sdidParamExpiry:{},serverState:{},sessionCookieName:{},secureCookie:{type:"boolean"},sameSiteCookie:{},takeTimeoutMetrics:{},trackingServer:{},trackingServerSecure:{},useLocalStorage:{type:"boolean"},whitelistIframeDomains:{},whitelistParentDomain:{}},Z={getConfigNames:function(){return Object.keys(Q)},getConfigs:function(){return Q},normalizeConfig:function(e,t){return Q[e]&&"boolean"===Q[e].type?"function"!=typeof t?t:t():t}},ee=function(e){var t={};return e.on=function(e,a,n){if(!a||"function"!=typeof a)throw new Error("[ON] Callback should be a function.");t.hasOwnProperty(e)||(t[e]=[]);var r=t[e].push({callback:a,context:n})-1;return function(){t[e].splice(r,1),t[e].length||delete t[e]}},e.off=function(e,a){t.hasOwnProperty(e)&&(t[e]=t[e].filter((function(e){if(e.callback!==a)return e})))},e.publish=function(e){if(t.hasOwnProperty(e)){var a=[].slice.call(arguments,1);t[e].slice(0).forEach((function(e){e.callback.apply(e.context,a)}))}},e.publish},te={PENDING:"pending",CHANGED:"changed",COMPLETE:"complete"},ae={AAM:"aam",ADCLOUD:"adcloud",ANALYTICS:"aa",CAMPAIGN:"campaign",ECID:"ecid",LIVEFYRE:"livefyre",TARGET:"target",MEDIA_ANALYTICS:"mediaaa"},ne=(t(k={},ae.AAM,565),t(k,ae.ECID,565),k),re=(t(y={},ae.AAM,[1,10]),t(y,ae.ECID,[1,10]),y),ie=["videoaa","iabConsentHash"],se=function(e){return Object.keys(e).map((function(t){return e[t]}))}(ae),oe=function(){var e={};return e.callbacks=Object.create(null),e.add=function(t,a){if(!l(a))throw new Error("[callbackRegistryFactory] Make sure callback is a function or an array of functions.");e.callbacks[t]=e.callbacks[t]||[];var n=e.callbacks[t].push(a)-1;return function(){e.callbacks[t].splice(n,1)}},e.execute=function(t,a){if(e.callbacks[t]){a=(a=void 0===a?[]:a)instanceof Array?a:[a];try{for(;e.callbacks[t].length;){var n=e.callbacks[t].shift();"function"==typeof n?n.apply(null,a):n instanceof Array&&n[1].apply(n[0],a)}delete e.callbacks[t]}catch(e){}}},e.executeAll=function(t,a){(a||t&&!c(t))&&Object.keys(e.callbacks).forEach((function(a){var n=void 0!==t[a]?t[a]:"";e.execute(a,n)}),e)},e.hasCallbacks=function(){return Boolean(Object.keys(e.callbacks).length)},e},ce=function(){},le=function(e){var t=window.console;return!!t&&"function"==typeof t[e]},ue=function(e,t,a){return a()?function(){if(le(e)){for(var a=arguments.length,n=new Array(a),r=0;r<a;r++)n[r]=arguments[r];console[e].apply(console,[t].concat(n))}}:ce},de=u,pe=function(){for(var e=[],t=0;t<256;t++){for(var a=t,n=0;n<8;n++)a=1&a?3988292384^a>>>1:a>>>1;e.push(a)}return function(t,a){t=unescape(encodeURIComponent(t)),a||(a=0),a^=-1;for(var n=0;n<t.length;n++){var r=255&(a^t.charCodeAt(n));a=a>>>8^e[r]}return(a^=-1)>>>0}}(),ge=new de("[ADOBE OPT-IN]"),fe=function(t,a){return e(t)===a},me=function(e,t){return e instanceof Array?e:fe(e,"string")?[e]:t||[]},ve=function(e){var t=Object.keys(e);return!!t.length&&t.every((function(t){return!0===e[t]}))},he=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!(!e||ye(e))&&me(e).every((function(e){return se.indexOf(e)>-1||t&&ie.indexOf(e)>-1}))},be=function(e,t){return e.reduce((function(e,a){return e[a]=t,e}),{})},ke=function(e){return JSON.parse(JSON.stringify(e))},ye=function(e){return"[object Array]"===Object.prototype.toString.call(e)&&!e.length},De=function(e){if(Te(e))return e;try{return JSON.parse(e)}catch(e){return{}}},Ce=function(e){return void 0===e||(Te(e)?he(Object.keys(e),!0):we(e))},we=function(e){try{var t=JSON.parse(e);return!!e&&fe(e,"string")&&he(Object.keys(t),!0)}catch(e){return!1}},Te=function(e){return null!==e&&fe(e,"object")&&!1===Array.isArray(e)},Se=function(){},Pe=function(e){return fe(e,"function")?e():e},Ve=function(e,t){Ce(e)||ge.error("".concat(t))},_e=function(e){return Object.keys(e).map((function(t){return e[t]}))},Ee=function(e){return _e(e).filter((function(e,t,a){return a.indexOf(e)===t}))},Ie=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.command,n=t.params,r=void 0===n?{}:n,i=t.callback,s=void 0===i?Se:i;if(!a||-1===a.indexOf("."))throw new Error("[OptIn.execute] Please provide a valid command.");try{var o=a.split("."),c=e[o[0]],l=o[1];if(!c||"function"!=typeof c[l])throw new Error("Make sure the plugin and API name exist.");var u=Object.assign(r,{callback:s});c[l].call(c,u)}catch(e){ge.error("[execute] Something went wrong: "+e.message)}}};p.prototype=Object.create(Error.prototype),p.prototype.constructor=p;var Ae="fetchPermissions",Oe="[OptIn#registerPlugin] Plugin is invalid.";g.Categories=ae,g.TimeoutError=p;var Le=Object.freeze({OptIn:g,IabPlugin:h}),je=function(e,t){e.publishDestinations=function(a){var n=arguments[1],r=arguments[2];try{r="function"==typeof r?r:a.callback}catch(e){r=function(){}}var i=t;if(i.readyToAttachIframePreliminary()){if("string"==typeof a){if(!a.length)return void r({error:"subdomain is not a populated string."});if(!(n instanceof Array&&n.length))return void r({error:"messages is not a populated array."});var s=!1;if(n.forEach((function(e){"string"==typeof e&&e.length&&(i.addMessage(e),s=!0)})),!s)return void r({error:"None of the messages are populated strings."})}else{if(!I.isObject(a))return void r({error:"Invalid parameters passed."});var o=a;if("string"!=typeof(a=o.subdomain)||!a.length)return void r({error:"config.subdomain is not a populated string."});var c=o.urlDestinations;if(!(c instanceof Array&&c.length))return void r({error:"config.urlDestinations is not a populated array."});var l=[];c.forEach((function(e){I.isObject(e)&&(e.hideReferrer?e.message&&i.addMessage(e.message):l.push(e))})),function e(){l.length&&setTimeout((function(){var t=new Image,a=l.shift();t.src=a.url,i.onPageDestinationsFired.push(a),e()}),100)}()}i.iframe?(r({
message:"The destination publishing iframe is already attached and loaded."}),i.requestToProcess()):!e.subdomain&&e._getField("MCMID")?(i.subdomain=a,i.doAttachIframe=!0,i.url=i.getUrl(),i.readyToAttachIframe()?(i.iframeLoadedCallbacks.push((function(e){r({message:"Attempted to attach and load the destination publishing iframe through this API call. Result: "+(e.message||"no result")})})),i.attachIframe()):r({error:"Encountered a problem in attempting to attach and load the destination publishing iframe through this API call."})):i.iframeLoadedCallbacks.push((function(e){r({message:"Attempted to attach and load the destination publishing iframe through normal Visitor API processing. Result: "+(e.message||"no result")})}))}else r({error:"The destination publishing iframe is disabled in the Visitor library."})}},xe=function e(t){function a(e,t){return e>>>t|e<<32-t}for(var n,r,i=Math.pow,s=i(2,32),o="",c=[],l=8*t.length,u=e.h=e.h||[],d=e.k=e.k||[],p=d.length,g={},f=2;p<64;f++)if(!g[f]){for(n=0;n<313;n+=f)g[n]=f;u[p]=i(f,.5)*s|0,d[p++]=i(f,1/3)*s|0}for(t+="\x80";t.length%64-56;)t+="\0";for(n=0;n<t.length;n++){if((r=t.charCodeAt(n))>>8)return;c[n>>2]|=r<<(3-n)%4*8}for(c[c.length]=l/s|0,c[c.length]=l,r=0;r<c.length;){var m=c.slice(r,r+=16),v=u;for(u=u.slice(0,8),n=0;n<64;n++){var h=m[n-15],b=m[n-2],k=u[0],y=u[4],D=u[7]+(a(y,6)^a(y,11)^a(y,25))+(y&u[5]^~y&u[6])+d[n]+(m[n]=n<16?m[n]:m[n-16]+(a(h,7)^a(h,18)^h>>>3)+m[n-7]+(a(b,17)^a(b,19)^b>>>10)|0);(u=[D+((a(k,2)^a(k,13)^a(k,22))+(k&u[1]^k&u[2]^u[1]&u[2]))|0].concat(u))[4]=u[4]+D|0}for(n=0;n<8;n++)u[n]=u[n]+v[n]|0}for(n=0;n<8;n++)for(r=3;r+1;r--){var C=u[n]>>8*r&255;o+=(C<16?0:"")+C.toString(16)}return o},Me=function(e,t){return"SHA-256"!==t&&"SHA256"!==t&&"sha256"!==t&&"sha-256"!==t||(e=xe(e)),e},Ne=function(e){return String(e).trim().toLowerCase()},Re=Le.OptIn;I.defineGlobalNamespace(),window.adobe.OptInCategories=Re.Categories;var Fe=function(t,a,n){function r(){k._customerIDsHashChanged=!1}function i(e){var t=e;return function(e){var a=e||P.location.href;try{var n=k._extractParamFromUri(a,t);if(n)return te.parsePipeDelimetedKeyValues(n)}catch(e){}}}function s(e){function t(e,t,a){e&&e.match(K.VALID_VISITOR_ID_REGEX)&&(a===O&&(S=!0),t(e))}t(e[O],k.setMarketingCloudVisitorID,O),k._setFieldExpire(R,-1),t(e[M],k.setAnalyticsVisitorID)}function o(e){e=e||{},k._supplementalDataIDCurrent=e.supplementalDataIDCurrent||"",k._supplementalDataIDCurrentConsumed=e.supplementalDataIDCurrentConsumed||{},k._supplementalDataIDLast=e.supplementalDataIDLast||"",k._supplementalDataIDLastConsumed=e.supplementalDataIDLastConsumed||{}}function c(e){function t(e,t,a){return(a=a?a+="|":a)+(e+"=")+encodeURIComponent(t)}function a(e,a){var n=a[0],r=a[1];return null!=r&&r!==F&&(e=t(n,r,e)),e}return function(e){return(e=e?e+="|":e)+"TS="+te.getTimestampInSeconds()}(e.reduce(a,""))}function l(e){var t=e.minutesToLive,a="";return(k.idSyncDisableSyncs||k.disableIdSyncs)&&(a=a||"Error: id syncs have been disabled"),"string"==typeof e.dpid&&e.dpid.length||(a=a||"Error: config.dpid is empty"),"string"==typeof e.url&&e.url.length||(a=a||"Error: config.url is empty"),void 0===t?t=20160:(t=parseInt(t,10),(isNaN(t)||t<=0)&&(a=a||"Error: config.minutesToLive needs to be a positive number")),{error:a,ttl:t}}function u(){return!(!k.configs.doesOptInApply||y.optIn.isComplete&&d())}function d(){return k.configs.doesOptInApply&&k.configs.isIabContext?y.optIn.isApproved(y.optIn.Categories.ECID)&&T:y.optIn.isApproved(y.optIn.Categories.ECID)}function p(){[["getMarketingCloudVisitorID"],["setCustomerIDs",void 0],["syncIdentity",void 0],["getAnalyticsVisitorID"],["getAudienceManagerLocationHint"],["getLocationHint"],["getAudienceManagerBlob"]].forEach((function(e){var t=e[0],a=2===e.length?e[1]:"",n=k[t];k[t]=function(e){return d()&&k.isAllowed()?n.apply(k,arguments):("function"==typeof e&&k._callCallback(e,[a]),a)}}))}function g(){var e=k._getAudienceManagerURLData(),t=e.url;return k._loadData(A,t,null,e)}function f(e,t){if(T=!0,e)throw new Error("[IAB plugin] : "+e);t&&t.gdprApplies&&(C=t.consentString,w=t.hasConsentChangedSinceLastCmpPull?1:0),g(),h()}function m(e,t){if(T=!0,e)throw new Error("[IAB plugin] : "+e);t.gdprApplies&&(C=t.consentString,w=t.hasConsentChangedSinceLastCmpPull?1:0),k.init(),h()}function v(){y.optIn.isComplete&&(y.optIn.isApproved(y.optIn.Categories.ECID)?k.configs.isIabContext?y.optIn.execute({command:"iabPlugin.fetchConsentData",callback:m}):(k.init(),h()):k.configs.isIabContext?y.optIn.execute({command:"iabPlugin.fetchConsentData",callback:f}):(p(),h()))}function h(){y.optIn.off("complete",v)}if(!n||n.split("").reverse().join("")!==t)throw new Error("Please use `Visitor.getInstance` to instantiate Visitor.");var k=this,y=window.adobe,C="",w=0,T=!1,S=!1;k.version="5.5.0";var P=b,V=P.Visitor;V.version=k.version,V.AuthState=D.AUTH_STATE,V.OptOut=D.OPT_OUT,P.s_c_in||(P.s_c_il=[],P.s_c_in=0),k._c="Visitor",k._il=P.s_c_il,k._in=P.s_c_in,k._il[k._in]=k,P.s_c_in++,k._instanceType="regular",k._log={requests:[]},k.marketingCloudOrgID=t,k.cookieName="AMCV_"+t,k.sessionCookieName="AMCVS_"+t;var _={};a&&a.secureCookie&&a.sameSiteCookie&&(_={sameSite:a.sameSiteCookie,secure:a.secureCookie}),k.cookieDomain=k.useLocalStorage?"":Y(null,_),k.loadSSL=!0,k.loadTimeout=3e4,k.CORSErrors=[],k.marketingCloudServer=k.audienceManagerServer="dpm.demdex.net",k.sdidParamExpiry=30;var E=null,A="MC",O="MCMID",L="MCIDTS",x="A",M="MCAID",N="AAM",R="MCAAMB",F="NONE",U=function(e){return!Object.prototype[e]},X=J(k);k.FIELDS=D.FIELDS,k.cookieRead=function(e){return k.useLocalStorage?e===k.sessionCookieName?sessionStorage.getItem(e):localStorage.getItem(e):W.get(e)},k.cookieWrite=function(e,t,a){var n=""+t;if(k.useLocalStorage)return e===k.sessionCookieName?sessionStorage.setItem(e,n):localStorage.setItem(e,n);var r=k.cookieLifetime?(""+k.cookieLifetime).toUpperCase():"",i={expires:a,domain:k.cookieDomain,cookieLifetime:r};return k.configs&&k.configs.secureCookie&&"https:"===location.protocol&&(i.secure=!0),k.configs&&k.configs.sameSiteCookie&&"https:"===location.protocol&&(i.sameSite=D.SAME_SITE_VALUES[k.configs.sameSiteCookie.toUpperCase()]||"Lax"),W.set(e,n,i)},k.removeCookie=function(e){if(k.useLocalStorage)return e===k.sessionCookieName?sessionStorage.removeItem(e):localStorage.removeItem(e);var t={domain:k.cookieDomain};return k.configs&&k.configs.secureCookie&&"https:"===location.protocol&&(t.secure=!0),k.configs&&k.configs.sameSiteCookie&&"https:"===location.protocol&&(t.sameSite=D.SAME_SITE_VALUES[k.configs.sameSiteCookie.toUpperCase()]||"Lax"),W.remove(e,t)},k.resetState=function(e){e?k._mergeServerState(e):o()},k._isAllowedDone=!1,k._isAllowedFlag=!1,k.isAllowed=function(){return k._isAllowedDone||(k._isAllowedDone=!0,(k.cookieRead(k.cookieName)||k.cookieWrite(k.cookieName,"T",1))&&(k._isAllowedFlag=!0)),"T"===k.cookieRead(k.cookieName)&&k.removeCookie(k.cookieName),k._isAllowedFlag},k.setMarketingCloudVisitorID=function(e){k._setMarketingCloudFields(e)},k._use1stPartyMarketingCloudServer=!1,k.getMarketingCloudVisitorID=function(e,t){k.marketingCloudServer&&k.marketingCloudServer.indexOf(".demdex.net")<0&&(k._use1stPartyMarketingCloudServer=!0);var a=k._getAudienceManagerURLData("_setMarketingCloudFields"),n=a.url;return k._getRemoteField(O,n,e,t,a)};var Q=function(e,t){var a={};k.getMarketingCloudVisitorID((function(){t.forEach((function(e){a[e]=k._getField(e,!0)})),-1!==t.indexOf("MCOPTOUT")?k.isOptedOut((function(t){a.MCOPTOUT=t,e(a)}),null,!0):e(a)}),!0)};k.getVisitorValues=function(e,t){var a={MCMID:{fn:k.getMarketingCloudVisitorID,args:[!0],context:k},MCOPTOUT:{fn:k.isOptedOut,args:[void 0,!0],context:k},MCAID:{fn:k.getAnalyticsVisitorID,args:[!0],context:k},MCAAMLH:{fn:k.getAudienceManagerLocationHint,args:[!0],context:k},MCAAMB:{fn:k.getAudienceManagerBlob,args:[!0],context:k}},n=t&&t.length?I.pluck(a,t):a;t&&-1===t.indexOf("MCAID")?Q(e,t):H(n,e)},k._currentCustomerIDs={},k._customerIDsHashChanged=!1,k._newCustomerIDsHash="",k.setCustomerIDs=function(t,a){if(!k.isOptedOut()&&t){if(!I.isObject(t)||I.isObjectEmpty(t))return!1;var n,i,s,o;for(n in k._readVisitor(),t)if(U(n)&&(k._currentCustomerIDs.dataSources=k._currentCustomerIDs.dataSources||{},a=(i=t[n]).hasOwnProperty("hashType")?i.hashType:a,i))if("object"===e(i)){var c={};if(i.id){if(a){if(!(o=Me(Ne(i.id),a)))return;i.id=o,c.hashType=a}c.id=i.id}null!=i.authState&&(c.authState=i.authState),k._currentCustomerIDs.dataSources[n]=c}else if(a){if(!(o=Me(Ne(i),a)))return;k._currentCustomerIDs.dataSources[n]={id:o,hashType:a}}else k._currentCustomerIDs.dataSources[n]={id:i};var l=k.getCustomerIDs(!0),u=k._getField("MCCIDH"),d="";for(s in u||(u=0),l){var p=l[s];if(!I.isObjectEmpty(p))for(n in p)U(n)&&(d+=(d?"|":"")+n+"|"+((i=p[n]).id?i.id:"")+(i.authState?i.authState:""))}k._newCustomerIDsHash=String(k._hash(d)),k._newCustomerIDsHash!==u&&(k._customerIDsHashChanged=!0,k._mapCustomerIDs(r))}},k.syncIdentity=function(t,a){if(!k.isOptedOut()&&t){if(!I.isObject(t)||I.isObjectEmpty(t))return!1;var n,i,s,o,c;for(n in k._readVisitor(),t)if(U(n)&&(k._currentCustomerIDs.nameSpaces=k._currentCustomerIDs.nameSpaces||{},a=(i=t[n]).hasOwnProperty("hashType")?i.hashType:a,i&&"object"===e(i))){var l={};if(i.id){if(a){if(!(s=Me(Ne(i.id),a)))return;i.id=s,l.hashType=a}l.id=i.id}null!=i.authState&&(l.authState=i.authState),i.dataSource&&(k._currentCustomerIDs.dataSources=k._currentCustomerIDs.dataSources||{},o=i.dataSource,k._currentCustomerIDs.dataSources[o]=l),k._currentCustomerIDs.nameSpaces[n]=l}var u=k.getCustomerIDs(!0),d=k._getField("MCCIDH"),p="";for(c in d||(d="0"),u){var g=u[c];if(!I.isObjectEmpty(g))for(n in g)U(n)&&(p+=(p?"|":"")+n+"|"+((i=g[n]).id?i.id:"")+(i.authState?i.authState:""))}k._newCustomerIDsHash=String(k._hash(p)),k._newCustomerIDsHash!==d&&(k._customerIDsHashChanged=!0,k._mapCustomerIDs(r))}},k.getCustomerIDs=function(e){k._readVisitor();var t,a,n={dataSources:{},nameSpaces:{}},r=k._currentCustomerIDs.dataSources;for(t in r)U(t)&&(a=r[t]).id&&(n.dataSources[t]||(n.dataSources[t]={}),n.dataSources[t].id=a.id,null!=a.authState?n.dataSources[t].authState=a.authState:n.dataSources[t].authState=V.AuthState.UNKNOWN,a.hashType&&(n.dataSources[t].hashType=a.hashType));var i=k._currentCustomerIDs.nameSpaces;for(t in i)U(t)&&(a=i[t]).id&&(n.nameSpaces[t]||(n.nameSpaces[t]={}),n.nameSpaces[t].id=a.id,null!=a.authState?n.nameSpaces[t].authState=a.authState:n.nameSpaces[t].authState=V.AuthState.UNKNOWN,a.hashType&&(n.nameSpaces[t].hashType=a.hashType));return e?n:n.dataSources},k.setAnalyticsVisitorID=function(e){k._setAnalyticsFields(e)},k.getAnalyticsVisitorID=function(e,t,a){if(!te.isTrackingServerPopulated()&&!a)return k._callCallback(e,[""]),"";var n="";if(a||(n=k.getMarketingCloudVisitorID((function(){k.getAnalyticsVisitorID(e,!0)}))),n||a){var r=a?k.marketingCloudServer:k.trackingServer,i="";k.loadSSL&&(a?k.marketingCloudServerSecure&&(r=k.marketingCloudServerSecure):k.trackingServerSecure&&(r=k.trackingServerSecure));var s={};if(r){var o="http"+(k.loadSSL?"s":"")+"://"+r+"/id",c=k.configs.cookieLifetime,l="d_visid_ver="+k.version+"&mcorgid="+encodeURIComponent(k.marketingCloudOrgID)+(n?"&mid="+encodeURIComponent(n):"")+(c?"&cl="+encodeURIComponent(c):"")+(k.idSyncDisable3rdPartySyncing||k.disableThirdPartyCookies?"&d_coppa=true":""),u=["s_c_il",k._in,"_set"+(a?"MarketingCloud":"Analytics")+"Fields"];i=o+"?"+l+"&callback=s_c_il%5B"+k._in+"%5D._set"+(a?"MarketingCloud":"Analytics")+"Fields",s.corsUrl=o+"?"+l,s.callback=u}return s.url=i,k._getRemoteField(a?O:M,i,e,t,s)}return""},k.getAudienceManagerLocationHint=function(e,t){if(k.getMarketingCloudVisitorID((function(){k.getAudienceManagerLocationHint(e,!0)}))){var a=k._getField(M);if(!a&&te.isTrackingServerPopulated()&&(a=k.getAnalyticsVisitorID((function(){k.getAudienceManagerLocationHint(e,!0)}))),a||!te.isTrackingServerPopulated()){var n=k._getAudienceManagerURLData(),r=n.url;return k._getRemoteField("MCAAMLH",r,e,t,n)}}return""},k.getLocationHint=k.getAudienceManagerLocationHint,k.getAudienceManagerBlob=function(e,t){if(k.getMarketingCloudVisitorID((function(){k.getAudienceManagerBlob(e,!0)}))){var a=k._getField(M);if(!a&&te.isTrackingServerPopulated()&&(a=k.getAnalyticsVisitorID((function(){k.getAudienceManagerBlob(e,!0)}))),a||!te.isTrackingServerPopulated()){var n=k._getAudienceManagerURLData(),r=n.url;return k._customerIDsHashChanged&&k._setFieldExpire(R,-1),k._getRemoteField(R,r,e,t,n)}}return""},k._supplementalDataIDCurrent="",k._supplementalDataIDCurrentConsumed={},k._supplementalDataIDLast="",k._supplementalDataIDLastConsumed={},k.getSupplementalDataID=function(e,t){k._supplementalDataIDCurrent||t||(k._supplementalDataIDCurrent=k._generateID(1));var a=k._supplementalDataIDCurrent;return k._supplementalDataIDLast&&!k._supplementalDataIDLastConsumed[e]?(a=k._supplementalDataIDLast,k._supplementalDataIDLastConsumed[e]=!0):a&&(k._supplementalDataIDCurrentConsumed[e]&&(k._supplementalDataIDLast=k._supplementalDataIDCurrent,k._supplementalDataIDLastConsumed=k._supplementalDataIDCurrentConsumed,k._supplementalDataIDCurrent=a=t?"":k._generateID(1),k._supplementalDataIDCurrentConsumed={}),a&&(k._supplementalDataIDCurrentConsumed[e]=!0)),a};var Z=!1;k._liberatedOptOut=null,k.getOptOut=function(e,t){var a=k._getAudienceManagerURLData("_setMarketingCloudFields"),n=a.url;if(d())return k._getRemoteField("MCOPTOUT",n,e,t,a);if(k._registerCallback("liberatedOptOut",e),null!==k._liberatedOptOut)return k._callAllCallbacks("liberatedOptOut",[k._liberatedOptOut]),Z=!1,k._liberatedOptOut;if(Z)return null;Z=!0;var r="liberatedGetOptOut";return a.corsUrl=a.corsUrl.replace(/\.demdex\.net\/id\?/,".demdex.net/optOutStatus?"),a.callback=[r],b[r]=function(e){if(e===Object(e)){var t,a,n=I.parseOptOut(e,t,F);t=n.optOut,a=1e3*n.d_ottl,k._liberatedOptOut=t,setTimeout((function(){k._liberatedOptOut=null}),a)}k._callAllCallbacks("liberatedOptOut",[t]),Z=!1},X.fireCORS(a),null},k.isOptedOut=function(e,t,a){t||(t=V.OptOut.GLOBAL);var n=k.getOptOut((function(a){var n=a===V.OptOut.GLOBAL||a.indexOf(t)>=0;k._callCallback(e,[n])}),a);return n?n===V.OptOut.GLOBAL||n.indexOf(t)>=0:null};var ee={subscribed:!1,callbacks:[]};k.onReceiveEcid=function(e){if(d())return k.getMarketingCloudVisitorID(e,!0);ee.subscribed=!0,e&&"function"==typeof e&&ee.callbacks.push(e)},k._fields=null,k._fieldsExpired=null,k._hash=function(e){var t,a=0;if(e)for(t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t),a&=a;return a},k._generateID=z,k._generateLocalMID=function(){var e=k._generateID(0);return re.isClientSideMarketingCloudVisitorID=!0,e},k._callbackList=null,k._callCallback=function(e,t){try{"function"==typeof e?e.apply(P,t):e[1].apply(e[0],t)}catch(e){}},k._registerCallback=function(e,t){t&&(null==k._callbackList&&(k._callbackList={}),null==k._callbackList[e]&&(k._callbackList[e]=[]),k._callbackList[e].push(t))},k._callAllCallbacks=function(e,t){if(null!=k._callbackList){var a=k._callbackList[e];if(a)for(;a.length>0;)k._callCallback(a.shift(),t)}},k._addQuerystringParam=function(e,t,a,n){var r=encodeURIComponent(t)+"="+encodeURIComponent(a),i=te.parseHash(e),s=te.hashlessUrl(e);if(-1===s.indexOf("?"))return s+"?"+r+i;var o=s.split("?"),c=o[0]+"?",l=o[1];return c+te.addQueryParamAtLocation(l,r,n)+i},k._extractParamFromUri=function(e,t){var a=new RegExp("[\\?&#]"+t+"=([^&#]*)").exec(e);if(a&&a.length)return decodeURIComponent(a[1])},k._parseAdobeMcFromUrl=i(K.ADOBE_MC),k._parseAdobeMcSdidFromUrl=i(K.ADOBE_MC_SDID),k._attemptToPopulateSdidFromUrl=function(e){var a=k._parseAdobeMcSdidFromUrl(e),n=1e9;a&&a.TS&&(n=te.getTimestampInSeconds()-a.TS),a&&a.SDID&&a.MCORGID===t&&n<k.sdidParamExpiry&&(k._supplementalDataIDCurrent=a.SDID,k._supplementalDataIDCurrentConsumed.SDID_URL_PARAM=!0)},k._attemptToPopulateIdsFromUrl=function(){var e=k._parseAdobeMcFromUrl();if(e&&e.TS){var a=te.getTimestampInSeconds()-e.TS;if(Math.floor(a/60)>K.ADOBE_MC_TTL_IN_MIN||e.MCORGID!==t)return;s(e)}},k._mergeServerState=function(e){if(e)try{if((e=function(e){return te.isObject(e)?e:JSON.parse(e)}(e))[k.marketingCloudOrgID]){var t=e[k.marketingCloudOrgID];!function(e){te.isObject(e)&&k.setCustomerIDs(e)}(t.customerIDs),o(t.sdid)}}catch(e){throw new Error("`serverState` has an invalid format.")}},k._timeout=null,k._loadData=function(e,t,a,n){t=k._addQuerystringParam(t,"d_fieldgroup",e,1),n.url=k._addQuerystringParam(n.url,"d_fieldgroup",e,1),n.corsUrl=k._addQuerystringParam(n.corsUrl,"d_fieldgroup",e,1),re.fieldGroupObj[e]=!0,n===Object(n)&&n.corsUrl&&"XMLHttpRequest"===X.corsMetadata.corsType&&X.fireCORS(n,a,e)},k._clearTimeout=function(e){null!=k._timeout&&k._timeout[e]&&(clearTimeout(k._timeout[e]),k._timeout[e]=0)},k._settingsDigest=0,k._getSettingsDigest=function(){if(!k._settingsDigest){var e=k.version;k.audienceManagerServer&&(e+="|"+k.audienceManagerServer),k.audienceManagerServerSecure&&(e+="|"+k.audienceManagerServerSecure),k._settingsDigest=k._hash(e)}return k._settingsDigest},k._readVisitorDone=!1,k._readVisitor=function(){if(!k._readVisitorDone){k._readVisitorDone=!0;var e,t,a,n,r,i,s=k._getSettingsDigest(),o=!1,c=k.cookieRead(k.cookieName),l=new Date;if(c||S||k.discardTrackingServerECID||(c=k.cookieRead(K.FIRST_PARTY_SERVER_COOKIE)),null==k._fields&&(k._fields={}),c&&"T"!==c)for((c=c.split("|"))[0].match(/^[\-0-9]+$/)&&(parseInt(c[0],10)!==s&&(o=!0),c.shift()),c.length%2==1&&c.pop(),e=0;e<c.length;e+=2)a=(t=c[e].split("-"))[0],n=c[e+1],t.length>1?(r=parseInt(t[1],10),i=t[1].indexOf("s")>0):(r=0,i=!1),o&&("MCCIDH"===a&&(n=""),r>0&&(r=l.getTime()/1e3-60)),a&&n&&(k._setField(a,n,1),r>0&&(k._fields["expire"+a]=r+(i?"s":""),(l.getTime()>=1e3*r||i&&!k.cookieRead(k.sessionCookieName))&&(k._fieldsExpired||(k._fieldsExpired={}),k._fieldsExpired[a]=!0)));!k._getField(M)&&te.isTrackingServerPopulated()&&(c=k.cookieRead("s_vi"))&&(c=c.split("|")).length>1&&c[0].indexOf("v1")>=0&&((e=(n=c[1]).indexOf("["))>=0&&(n=n.substring(0,e)),n&&n.match(K.VALID_VISITOR_ID_REGEX)&&k._setField(M,n))}},k._appendVersionTo=function(e){var t="vVersion|"+k.version,a=e?k._getCookieVersion(e):null;return a?G.areVersionsDifferent(a,k.version)&&(e=e.replace(K.VERSION_REGEX,t)):e+=(e?"|":"")+t,e},k._writeVisitor=function(){var e,t,a=k._getSettingsDigest();for(e in k._fields)U(e)&&k._fields[e]&&"expire"!==e.substring(0,6)&&(t=k._fields[e],a+=(a?"|":"")+e+(k._fields["expire"+e]?"-"+k._fields["expire"+e]:"")+"|"+t);a=k._appendVersionTo(a),k.cookieWrite(k.cookieName,a,1)},k._getField=function(e,t){return null==k._fields||!t&&k._fieldsExpired&&k._fieldsExpired[e]?null:k._fields[e]},k._setField=function(e,t,a){null==k._fields&&(k._fields={}),k._fields[e]=t,a||k._writeVisitor()},k._getFieldList=function(e,t){var a=k._getField(e,t);return a?a.split("*"):null},k._setFieldList=function(e,t,a){k._setField(e,t?t.join("*"):"",a)},k._getFieldMap=function(e,t){var a=k._getFieldList(e,t);if(a){var n,r={};for(n=0;n<a.length;n+=2)r[a[n]]=a[n+1];return r}return null},k._setFieldMap=function(e,t,a){var n,r=null;if(t)for(n in r=[],t)U(n)&&(r.push(n),r.push(t[n]));k._setFieldList(e,r,a)},k._setFieldExpire=function(e,t,a){var n=new Date;n.setTime(n.getTime()+1e3*t),null==k._fields&&(k._fields={}),k._fields["expire"+e]=Math.floor(n.getTime()/1e3)+(a?"s":""),t<0?(k._fieldsExpired||(k._fieldsExpired={}),k._fieldsExpired[e]=!0):k._fieldsExpired&&(k._fieldsExpired[e]=!1),a&&(k.cookieRead(k.sessionCookieName)||k.cookieWrite(k.sessionCookieName,"1"))},k._findVisitorID=function(t){return t&&("object"===e(t)&&(t=t.d_mid?t.d_mid:t.visitorID?t.visitorID:t.id?t.id:t.uuid?t.uuid:""+t),t&&"NOTARGET"===(t=t.toUpperCase())&&(t=F),t&&(t===F||t.match(K.VALID_VISITOR_ID_REGEX))||(t="")),t},k._setFields=function(t,a){if(k._clearTimeout(t),null!=k._loading&&(k._loading[t]=!1),re.fieldGroupObj[t]&&re.setState(t,!1),t===A){!0!==re.isClientSideMarketingCloudVisitorID&&(re.isClientSideMarketingCloudVisitorID=!1);var n=k._getField(O);if(!n||k.overwriteCrossDomainMCIDAndAID){if(!(n="object"===e(a)&&a.mid?a.mid:k._findVisitorID(a))){if(k._use1stPartyMarketingCloudServer&&!k.tried1stPartyMarketingCloudServer)return k.tried1stPartyMarketingCloudServer=!0,void k.getAnalyticsVisitorID(null,!1,!0);n=k._generateLocalMID()}k._setField(O,n)}n&&n!==F||(n=""),"object"===e(a)&&((a.d_region||a.dcs_region||a.d_blob||a.blob)&&k._setFields(N,a),k._use1stPartyMarketingCloudServer&&a.mid&&k._setFields(x,{id:a.id})),k._callAllCallbacks(O,[n])}if(t===N&&"object"===e(a)){var r=604800;null!=a.id_sync_ttl&&a.id_sync_ttl&&(r=parseInt(a.id_sync_ttl,10));var i=ae.getRegionAndCheckIfChanged(a,r);k._callAllCallbacks("MCAAMLH",[i]);var s=k._getField(R);(a.d_blob||a.blob)&&((s=a.d_blob)||(s=a.blob),k._setFieldExpire(R,r),k._setField(R,s)),s||(s=""),k._callAllCallbacks(R,[s]),!a.error_msg&&k._newCustomerIDsHash&&k._setField("MCCIDH",k._newCustomerIDsHash)}if(t===x){var o=k._getField(M);o&&!k.overwriteCrossDomainMCIDAndAID||((o=k._findVisitorID(a))?o!==F&&k._setFieldExpire(R,-1):o=F,k._setField(M,o)),o&&o!==F||(o=""),k._callAllCallbacks(M,[o])}if(k.idSyncDisableSyncs||k.disableIdSyncs)ae.idCallNotProcesssed=!0;else{ae.idCallNotProcesssed=!1;var c={};c.ibs=a.ibs,c.subdomain=a.subdomain,ae.processIDCallData(c)}if(a===Object(a)){var l,u;d()&&k.isAllowed()&&(l=k._getField("MCOPTOUT"));var p=I.parseOptOut(a,l,F);l=p.optOut,u=p.d_ottl,k._setFieldExpire("MCOPTOUT",u,!0),k._setField("MCOPTOUT",l),k._callAllCallbacks("MCOPTOUT",[l])}},k._loading=null,k._getRemoteField=function(e,t,a,n,r){var i,s="",o=te.isFirstPartyAnalyticsVisitorIDCall(e),c={MCAAMLH:!0,MCAAMB:!0};if(d()&&k.isAllowed())if(k._readVisitor(),!(!(s=k._getField(e,!0===c[e]))||k._fieldsExpired&&k._fieldsExpired[e])||k.disableThirdPartyCalls&&!o)s||(e===O?(k._registerCallback(e,a),s=k._generateLocalMID(),k.setMarketingCloudVisitorID(s)):e===M?(k._registerCallback(e,a),s="",k.setAnalyticsVisitorID(s)):(s="",n=!0));else if(e===O||"MCOPTOUT"===e?i=A:"MCAAMLH"===e||e===R?i=N:e===M&&(i=x),i)return!t||null!=k._loading&&k._loading[i]||(null==k._loading&&(k._loading={}),k._loading[i]=!0,i===N&&(w=0),k._loadData(i,t,(function(t){if(!k._getField(e)){t&&re.setState(i,!0);var a="";e===O?a=k._generateLocalMID():i===N&&(a={error_msg:"timeout"}),k._setFields(i,a)}}),r)),k._registerCallback(e,a),s||(t||k._setFields(i,{id:F}),"");return e!==O&&e!==M||s!==F||(s="",n=!0),a&&n&&k._callCallback(a,[s]),e===O&&ee.subscribed&&(ee.callbacks&&ee.callbacks.length&&ee.callbacks.forEach((function(e){k._callCallback(e,[s])})),ee.subscribed=!1,ee.callbacks.length=0),s},k._setMarketingCloudFields=function(e){k._readVisitor(),k._setFields(A,e)},k._mapCustomerIDs=function(e){k.getAudienceManagerBlob(e,!0)},k._setAnalyticsFields=function(e){k._readVisitor(),k._setFields(x,e)},k._setAudienceManagerFields=function(e){k._readVisitor(),k._setFields(N,e)},k._getAudienceManagerURLData=function(e){var t=k.audienceManagerServer,a="",n=k._getField(O),r=k._getField(R,!0),i=k._getField(M),s=i&&i!==F?"&d_cid_ic=AVID%01"+encodeURIComponent(i):"";if(k.loadSSL&&k.audienceManagerServerSecure&&(t=k.audienceManagerServerSecure),t){var o,c,l,u=k.getCustomerIDs(!0);if(u)for(c in u){var d=u[c];if(!I.isObjectEmpty(d)){var p="nameSpaces"===c?"&d_cid_ns=":"&d_cid_ic=";for(o in d)U(o)&&(l=d[o],s+=p+encodeURIComponent(o)+"%01"+encodeURIComponent(l.id?l.id:"")+(l.authState?"%01"+l.authState:""))}}e||(e="_setAudienceManagerFields");var g="http"+(k.loadSSL?"s":"")+"://"+t+"/id",f="d_visid_ver="+k.version+(C&&-1!==g.indexOf("demdex.net")?"&gdpr=1&gdpr_consent="+C:"")+(w&&-1!==g.indexOf("demdex.net")?"&d_cf="+w:"")+"&d_rtbd=json&d_ver=2"+(!n&&k._use1stPartyMarketingCloudServer?"&d_verify=1":"")+"&d_orgid="+encodeURIComponent(k.marketingCloudOrgID)+"&d_nsid="+(k.idSyncContainerID||0)+(n?"&d_mid="+encodeURIComponent(n):"")+(k.idSyncDisable3rdPartySyncing||k.disableThirdPartyCookies?"&d_coppa=true":"")+(!0===E?"&d_coop_safe=1":!1===E?"&d_coop_unsafe=1":"")+(r?"&d_blob="+encodeURIComponent(r):"")+s,m=["s_c_il",k._in,e];return{url:a=g+"?"+f+"&d_cb=s_c_il%5B"+k._in+"%5D."+e,corsUrl:g+"?"+f,callback:m}}return{url:a}},k.appendVisitorIDsTo=function(e){try{var t=[[O,k._getField(O)],[M,k._getField(M)],["MCORGID",k.marketingCloudOrgID]];return k._addQuerystringParam(e,K.ADOBE_MC,c(t))}catch(t){return e}},k.appendSupplementalDataIDTo=function(e,t){if(!(t=t||k.getSupplementalDataID(te.generateRandomString(),!0)))return e;try{var a=c([["SDID",t],["MCORGID",k.marketingCloudOrgID]]);return k._addQuerystringParam(e,K.ADOBE_MC_SDID,a)}catch(t){return e}};var te={parseHash:function(e){var t=e.indexOf("#");return t>0?e.substr(t):""},hashlessUrl:function(e){var t=e.indexOf("#");return t>0?e.substr(0,t):e},addQueryParamAtLocation:function(e,t,a){var n=e.split("&");return a=null!=a?a:n.length,n.splice(a,0,t),n.join("&")},isFirstPartyAnalyticsVisitorIDCall:function(e,t,a){return e===M&&(t||(t=k.trackingServer),a||(a=k.trackingServerSecure),!("string"!=typeof(n=k.loadSSL?a:t)||!n.length)&&n.indexOf("2o7.net")<0&&n.indexOf("omtrdc.net")<0);var n},isObject:function(e){return Boolean(e&&e===Object(e))},removeCookie:function(e){W.remove(e,{domain:k.cookieDomain})},isTrackingServerPopulated:function(){return!!k.trackingServer||!!k.trackingServerSecure},getTimestampInSeconds:function(){return Math.round((new Date).getTime()/1e3)},parsePipeDelimetedKeyValues:function(e){return e.split("|").reduce((function(e,t){var a=t.split("=");return e[a[0]]=decodeURIComponent(a[1]),e}),{})},generateRandomString:function(e){e=e||5;for(var t="",a="abcdefghijklmnopqrstuvwxyz0123456789";e--;)t+=a[Math.floor(Math.random()*a.length)];return t},normalizeBoolean:function(e){return"true"===e||"false"!==e&&e},parseBoolean:function(e){return"true"===e||"false"!==e&&null},replaceMethodsWithFunction:function(e,t){for(var a in e)e.hasOwnProperty(a)&&"function"==typeof e[a]&&(e[a]=t);return e}};k._helpers=te;var ae=$(k,V);k._destinationPublishing=ae,k.timeoutMetricsLog=[];var ne,re={isClientSideMarketingCloudVisitorID:null,MCIDCallTimedOut:null,AnalyticsIDCallTimedOut:null,AAMIDCallTimedOut:null,fieldGroupObj:{},setState:function(e,t){switch(e){case A:!1===t?!0!==this.MCIDCallTimedOut&&(this.MCIDCallTimedOut=!1):this.MCIDCallTimedOut=t;break;case x:!1===t?!0!==this.AnalyticsIDCallTimedOut&&(this.AnalyticsIDCallTimedOut=!1):this.AnalyticsIDCallTimedOut=t;break;case N:!1===t?!0!==this.AAMIDCallTimedOut&&(this.AAMIDCallTimedOut=!1):this.AAMIDCallTimedOut=t}}};k.isClientSideMarketingCloudVisitorID=function(){return re.isClientSideMarketingCloudVisitorID},k.MCIDCallTimedOut=function(){return re.MCIDCallTimedOut},k.AnalyticsIDCallTimedOut=function(){return re.AnalyticsIDCallTimedOut},k.AAMIDCallTimedOut=function(){return re.AAMIDCallTimedOut},k.idSyncGetOnPageSyncInfo=function(){return k._readVisitor(),k._getField("MCSYNCSOP")},k.idSyncByURL=function(e){if(!k.isOptedOut()){var t=l(e||{});if(t.error)return t.error;var a,n,r=e.url,i=encodeURIComponent,s=ae;return r=r.replace(/^https:/,"").replace(/^http:/,""),a=I.encodeAndBuildRequest(["",e.dpid,e.dpuuid||""],","),n=["ibs",i(e.dpid),"img",i(r),t.ttl,"",a],s.addMessage(n.join("|")),s.requestToProcess(),"Successfully queued"}},k.idSyncByDataSource=function(e){if(!k.isOptedOut())return e===Object(e)&&"string"==typeof e.dpuuid&&e.dpuuid.length?(e.url="//dpm.demdex.net/ibs:dpid="+e.dpid+"&dpuuid="+e.dpuuid,k.idSyncByURL(e)):"Error: config or config.dpuuid is empty"},je(k,ae),k._getCookieVersion=function(e){e=e||k.cookieRead(k.cookieName);var t=K.VERSION_REGEX.exec(e);return t&&t.length>1?t[1]:null},k._resetAmcvCookie=function(e){var t=k._getCookieVersion();t&&!G.isLessThan(t,e)||k.removeCookie(k.cookieName)},k.setAsCoopSafe=function(){E=!0},k.setAsCoopUnsafe=function(){E=!1},function(){if(k.configs=Object.create(null),te.isObject(a))for(var e in a)U(e)&&(k[e]=a[e],k.configs[e]=a[e])}(),p(),k.init=function(){u()&&(y.optIn.fetchPermissions(v,!0),!y.optIn.isApproved(y.optIn.Categories.ECID))||ne||(ne=!0,function(){if(te.isObject(a)){k.idSyncContainerID=k.idSyncContainerID||0,E="boolean"==typeof k.isCoopSafe?k.isCoopSafe:te.parseBoolean(k.isCoopSafe),k.resetBeforeVersion&&k._resetAmcvCookie(k.resetBeforeVersion),k._attemptToPopulateIdsFromUrl(),k._attemptToPopulateSdidFromUrl(),k._readVisitor();var e=k._getField(L),t=Math.ceil((new Date).getTime()/K.MILLIS_PER_DAY);k.idSyncDisableSyncs||k.disableIdSyncs||!ae.canMakeSyncIDCall(e,t)||(k._setFieldExpire(R,-1),k._setField(L,t)),k.getMarketingCloudVisitorID(),k.getAudienceManagerLocationHint(),k.getAudienceManagerBlob(),k._mergeServerState(k.serverState)}else k._attemptToPopulateIdsFromUrl(),k._attemptToPopulateSdidFromUrl()}(),function(){if(!k.idSyncDisableSyncs&&!k.disableIdSyncs){ae.checkDPIframeSrc();var e=function(){var e=ae;e.readyToAttachIframe()&&e.attachIframe()};P.addEventListener("load",(function(){V.windowLoaded=!0,e()}));try{q.receiveMessage((function(e){ae.receiveMessage(e.data)}),ae.iframeHost)}catch(e){}}}(),k.whitelistIframeDomains&&K.POST_MESSAGE_ENABLED&&(k.whitelistIframeDomains=k.whitelistIframeDomains instanceof Array?k.whitelistIframeDomains:[k.whitelistIframeDomains],k.whitelistIframeDomains.forEach((function(e){var a=new j(t,e),n=B(k,a);q.receiveMessage(n,e)}))))}};Fe.config=Z,b.Visitor=Fe;var Ue=Fe,Be=function(e){if(I.isObject(e))return Object.keys(e).filter((function(t){return""!==e[t]&&Z.getConfigs()[t]})).reduce((function(t,a){var n=Z.normalizeConfig(a,e[a]),r=I.normalizeBoolean(n);return t[a]=r,t}),Object.create(null))},He=Le.OptIn,We=Le.IabPlugin;Ue.getInstance=function(e,t){if(!e)throw new Error("Visitor requires Adobe Marketing Cloud Org ID.");e.indexOf("@")<0&&(e+="@AdobeOrg");var a=function(){var t=b.s_c_il;if(t)for(var a=0;a<t.length;a++){var n=t[a];if(n&&"Visitor"===n._c&&n.marketingCloudOrgID===e)return n}}();if(a)return a;var n=Be(t)||{};!function(e){b.adobe.optIn=b.adobe.optIn||function(){var t=I.pluck(e,["doesOptInApply","previousPermissions","preOptInApprovals","isOptInStorageEnabled","optInStorageExpiry","isIabContext","sameSiteCookie","secureCookie"]),a=e.optInCookieDomain||e.cookieDomain;a=(a=a||Y())===window.location.hostname?"":a,t.optInCookieDomain=a;var n=new He(t,{cookies:W});if(t.isIabContext&&t.doesOptInApply){var r=new We;n.registerPlugin(r)}return n}()}(n||{});var r=e.split("").reverse().join(""),i=new Ue(e,null,r);n.cookieDomain&&(i.cookieDomain=n.cookieDomain),n.sameSiteCookie&&n.secureCookie&&(i.configs={sameSiteCookie:n.sameSiteCookie,secureCookie:n.secureCookie}),b.s_c_il.splice(--b.s_c_in,1);var s=I.getIeVersion();if("number"==typeof s&&s<10)return i._helpers.replaceMethodsWithFunction(i,(function(){}));var o=function(){try{return b.self!==b.parent}catch(e){return!0}}()&&(!function(e){return e.cookieWrite("TEST_AMCV_COOKIE","T",1),"T"===e.cookieRead("TEST_AMCV_COOKIE")&&(e.removeCookie("TEST_AMCV_COOKIE"),!0)}(i)||I.isFirefox()&&!function(t){var a="AMCV_"+e;return!!t.cookieRead(a)}(i)&&n.whitelistParentDomain)&&b.parent?new M(e,n,i,b.parent):new Ue(e,n,r);return i=null,o.init(),o},function(){function e(){Ue.windowLoaded=!0}b.addEventListener?b.addEventListener("load",e):b.attachEvent&&b.attachEvent("onload",e),Ue.codeLoadEnd=(new Date).getTime()}()}(),Visitor)}},"adobe-mcid/src/view/utils/timeUnits.js":{script:function(e){var t={Hours:3600,Days:86400,Weeks:604800,Months:2592e3,Years:31536e3};e.exports=t}}}}},company:{orgId:"4D6368F454EC41940A4C98A6@AdobeOrg",dynamicCdnEnabled:!1},property:{name:"rp:sd - ScienceDirect",settings:{domains:["sciencedirect.com"],undefinedVarsReturnEmpty:!1,ruleComponentSequencingEnabled:!1},id:"PR014ce6030bed451cb607c85b0470f621"},rules:[{id:"RL020b5b14d9b64d7fb1b0240cf331bbea",name:"Event - Content Download Start",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"contentDownloadStart"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event123"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"",linkType:"o"}}]},{id:"RL0478570a119b46a7bdcadc0ec3a39e31",name:"Event - Save To List Start",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"saveToListStart"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event273"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"save to list start",linkType:"o"}}]},{id:"RL1c1c3e610fbe4858864b5a15cd23ce8e",name:"Event - Recommendation Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"recommendationClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{props:[{name:"prop33",type:"value",value:"%Page - Product Application Version%"}],events:[{
name:"event265"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"recommendation click",linkType:"o"}}]},{id:"RL1dfc461db42747c7b1e370ef2889fbff",name:"Event - Recommendation Views",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"recommendationViews"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{props:[{name:"prop33",type:"value",value:"%Page - Product Application Version%"}],events:[{name:"event257"},{name:"event264"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"recommendation views",linkType:"o"}}]},{id:"RL1fcbd726245d435ab0d9721313273c1d",name:"Load Mouseflow",events:[{modulePath:"core/src/lib/events/libraryLoaded.js",settings:{},ruleOrder:50}],conditions:[{modulePath:"core/src/lib/conditions/path.js",settings:{paths:[{value:"/answers"},{value:"/ai"}]}}],actions:[{modulePath:"core/src/lib/actions/customCode.js",settings:{source:'// tracking the full path for mouseflow\nvar mouseflowPath = window.location.hostname + window.location.pathname;\nvar mouseflowCrossDomainSupport = true;\n\nwindow._mfq = window._mfq || [];\n_mfq.push(function(mf) {\n    if (mf.isRecording()) {\n        sessionStorage.setItem("mf_session", mf.getSessionId());\n    } else {\n        sessionStorage.removeItem("mf_session");\n    }\n});\n\n(function() {\n  var mf = document.createElement("script");\n  mf.type = "text/javascript"; mf.defer = true;\n  mf.src = "//cdn.mouseflow.com/projects/5794b57f-ff28-4314-9e51-4be609a67b4d.js";\n  document.getElementsByTagName("head")[0].appendChild(mf);\n})();\n',language:"javascript"}}]},{id:"RL204730f95c194cc5a372c8ba91cccd77",name:"Event - Clear History",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"clearHistory"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event243"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"clear history",linkType:"o"}}]},{id:"RL2409ed1c40f34feba0a2fb0c144ab8f8",name:"Load - pageDataTracker",events:[{modulePath:"core/src/lib/events/libraryLoaded.js",settings:{},ruleOrder:50}],conditions:[],actions:[{modulePath:"core/src/lib/actions/customCode.js",settings:{global:!0,
source:"var pageDataTracker = {\n    eventCookieName: 'eventTrack',\n    debugCookie: 'els-aa-debugmode',\n    debugCounter: 1,\n    warnings: [],\n    measures: {},\n    timeoffset: 0\n\n    ,trackPageLoad: function(data) {\n        if (window.pageData && ((pageData.page && pageData.page.noTracking == 'true') || window.pageData_isLoaded)) {\n            return false;\n        }\n\n        this.updatePageData(data);\n\n        this.initWarnings();\n        if(!(window.pageData && pageData.page && pageData.page.name)) {\n            console.error('pageDataTracker.trackPageLoad() called without pageData.page.name being defined!');\n            return;\n        }\n\n        this.processIdPlusData(window.pageData);\n\n        if(window.pageData && pageData.page && !pageData.page.loadTime) {\n          pageData.page.loadTime = performance ? Math.round((performance.now())).toString() : '';\n        }\n\n        if(window.pageData && pageData.page) {\n            var localTime = new Date().getTime();\n            if(pageData.page.loadTimestamp) {\n                // calculate timeoffset\n                var serverTime = parseInt(pageData.page.loadTimestamp);\n                if(!isNaN(serverTime)) {\n                    this.timeoffset = pageData.page.loadTimestamp - localTime;\n                }\n            } else {\n                pageData.page.loadTimestamp = localTime;\n            }\n        }\n\n        this.validateData(window.pageData);\n\n        try {\n            var cookieTest = 'aa-cookie-test';\n            this.setCookie(cookieTest, cookieTest);\n            if(this.getCookie(cookieTest) != cookieTest) {\n                this.warnings.push('dtm5');\n            }\n            this.deleteCookie(cookieTest);\n        } catch(e){\n            this.warnings.push('dtm5');\n        }\n\n        this.registerCallbacks();\n        this.setAnalyticsData();\n\n        // handle any cookied event data\n        this.getEvents();\n\n        window.pageData_isLoaded = true;\n\n        this.debugMessage('Init - trackPageLoad()', window.pageData);\n\n        _satellite.track('eventDispatcher', JSON.stringify({\n          eventName: 'newPage',\n          eventData: {eventName: 'newPage'},\n          pageData: window.pageData\n        }));\n    }\n\n    ,trackEvent: function(event, data, callback) {\n        if (window.pageData && pageData.page && pageData.page.noTracking == 'true') {\n            return false;\n        }\n        \n        if(!window.pageData_isLoaded) {\n            if(this.isDebugEnabled()) {\n                console.log('[AA] pageDataTracker.trackEvent() called without calling trackPageLoad() first.');\n            }\n            return false;\n        }\n\n        if (event) {\n            this.initWarnings();\n            if(event === 'newPage') {\n                // auto fillings\n                if(data && data.page && !data.page.loadTimestamp) {\n                    data.page.loadTimestamp = ''+(new Date().getTime() + this.timeoffset);\n                }\n                this.processIdPlusData(data);\n            }\n\n            window.eventData = data ? data : {};\n            window.eventData.eventName = event;\n            if(!_satellite.getVar('blacklisted')) {\n                this.handleEventData(event, data);\n    \n                if(event === 'newPage') {\n                    this.validateData(window.pageData);\n                }\n                this.debugMessage('Event: ' + event, data);\n    \n                _satellite.track('eventDispatcher', JSON.stringify({\n                  eventName: event,\n                  eventData: window.eventData,\n                  pageData: window.pageData\n                }));\n            } else {\n                this.debugMessage('!! Blocked Event: ' + event, data);\n            }\n        }\n\n        if (typeof(callback) == 'function') {\n            callback.call();\n        }\n    }\n\n    ,processIdPlusData: function(data) {\n        if(data && data.visitor && data.visitor.idPlusData) {\n            var idPlusFields = ['userId', 'accessType', 'accountId', 'accountName'];\n            for(var i=0; i < idPlusFields.length; i++) {\n                if(typeof data.visitor.idPlusData[idPlusFields[i]] !== 'undefined') {\n                    data.visitor[idPlusFields[i]] = data.visitor.idPlusData[idPlusFields[i]];\n                }\n            }\n            data.visitor.idPlusData = undefined;\n        }\n    }\n\n    ,validateData: function(data) {\n        if(!data) {\n            this.warnings.push('dv0');\n            return;\n        }\n\n        // top 5\n        if(!(data.visitor && data.visitor.accessType)) {\n            this.warnings.push('dv1');\n        }\n        if(data.visitor && (data.visitor.accountId || data.visitor.accountName)) {\n            if(!data.visitor.accountName) {\n                this.warnings.push('dv2');\n            }\n            if(!data.visitor.accountId) {\n                this.warnings.push('dv3');\n            }\n        }\n        if(!(data.page && data.page.productName)) {\n            this.warnings.push('dv4');\n        }\n        if(!(data.page && data.page.businessUnit)) {\n            this.warnings.push('dv5');\n        }\n        if(!(data.page && data.page.name)) {\n            this.warnings.push('dv6');\n        }\n\n        // rp mandatory\n        if(data.page && data.page.businessUnit && (data.page.businessUnit.toLowerCase().indexOf('els:rp:') !== -1 || data.page.businessUnit.toLowerCase().indexOf('els:rap:') !== -1)) {\n            if(!(data.page && data.page.loadTimestamp)) {\n                this.warnings.push('dv7');\n            }\n            if(!(data.page && data.page.loadTime)) {\n                this.warnings.push('dv8');\n            }\n            if(!(data.visitor && data.visitor.ipAddress)) {\n                this.warnings.push('dv9');\n            }\n            if(!(data.page && data.page.type)) {\n                this.warnings.push('dv10');\n            }\n            if(!(data.page && data.page.language)) {\n                this.warnings.push('dv11');\n            }\n        }\n\n        // other\n        if(data.page && data.page.environment) {\n            var env = data.page.environment.toLowerCase();\n            if(!(env === 'dev' || env === 'cert' || env === 'prod')) {\n                this.warnings.push('dv12');\n            }\n        }\n        if(data.content && data.content.constructor !== Array) {\n            this.warnings.push('dv13');\n        }\n\n        if(data.visitor && data.visitor.accountId && data.visitor.accountId.indexOf(':') == -1) {\n            this.warnings.push('dv14');\n            data.visitor.accountId = \"data violation\"\n        }\n    }\n\n    ,initWarnings: function() {\n        this.warnings = [];\n        try {\n            var hdn = document.head.childNodes;\n            var libf = false;\n            for(var i=0; i<hdn.length; i++) {\n                if(hdn[i].src && (hdn[i].src.indexOf('satelliteLib') !== -1 || hdn[i].src.indexOf('launch') !== -1)) {\n                    libf = true;\n                    break;\n                }\n            }\n            if(!libf) {\n                this.warnings.push('dtm1');\n            }\n        } catch(e) {}\n\n        try {\n            for (let element of document.querySelectorAll('*')) {\n                // Check if the element has a src attribute and if it meets the criteria\n                if (element.src && element.src.includes('assets.adobedtm.com') && element.src.includes('launch')) {\n                    if (element.tagName.toLowerCase() !== 'script') {\n                        this.warnings.push('dtm5');\n                    }\n                    if (!element.hasAttribute('async')) {\n                        this.warnings.push('dtm4');\n                    }\n                }\n            }\n        } catch (e) { }\n    }\n\n    ,getMessages: function() {\n        return ['v1'].concat(this.warnings).join('|');\n    }\n    ,addMessage: function(message) {\n        this.warnings.push(message);\n    }\n\n    ,getPerformance: function() {\n        var copy = {};\n        for (var attr in this.measures) {\n            if(this.measures.hasOwnProperty(attr)) {\n                copy[attr] = this.measures[attr];\n            }\n        }\n\n        this.measures = {};\n        return copy;\n    }\n\n    ,dtmCodeDesc: {\n        dtm1: 'satellite-lib must be placed in the <head> section',\n        dtm2: 'trackPageLoad() must be placed and called before the closing </body> tag',\n        dtm3: 'trackEvent() must be called at a stage where Document.readyState=complete (e.g. on the load event or a user event)',\n        dtm4: 'Embed codes need to be loaded in async mode',\n        dtm5: 'Embed codes not in type script',\n        dv1: 'visitor.accessType not set but mandatory',\n        dv2: 'visitor.accountName not set but mandatory',\n        dv3: 'visitor.accountId not set but mandatory',\n        dv4: 'page.productName not set but mandatory',\n        dv5: 'page.businessUnit not set but mandatory',\n        dv6: 'page.name not set but mandatory',\n        dv7: 'page.loadTimestamp not set but mandatory',\n        dv8: 'page.loadTime not set but mandatory',\n        dv9: 'visitor.ipAddress not set but mandatory',\n        dv10: 'page.type not set but mandatory',\n        dv11: 'page.language not set but mandatory',\n        dv12: 'page.environment must be set to \\'prod\\', \\'cert\\' or \\'dev\\'',\n        dv13: 'content must be of type array of objects',\n        dv14: 'account number must contain at least one \\':\\', e.g. \\'ae:12345\\''\n    }\n\n    ,debugMessage: function(event, data) {\n        if(this.isDebugEnabled()) {\n            console.log('[AA] --------- [' + (this.debugCounter++) + '] Web Analytics Data ---------');\n            console.log('[AA] ' + event);\n            console.groupCollapsed(\"[AA] AA Data: \");\n            if(window.eventData) {\n                console.log(\"[AA] eventData:\\n\" + JSON.stringify(window.eventData, true, 2));\n            }\n            if(window.pageData) {\n                console.log(\"[AA] pageData:\\n\" + JSON.stringify(window.pageData, true, 2));\n            }\n            console.groupEnd();\n            if(this.warnings.length > 0) {\n                console.groupCollapsed(\"[AA] Warnings (\"+this.warnings.length+\"): \");\n                for(var i=0; i<this.warnings.length; i++) {\n                    var error = this.dtmCodeDesc[this.warnings[i]] ? this.dtmCodeDesc[this.warnings[i]] : 'Error Code: ' + this.warnings[i];\n                    console.log('[AA] ' + error);\n                }\n                console.log('[AA] More can be found here: https://confluence.cbsels.com/display/AA/AA+Error+Catalog');\n                console.groupEnd();\n            }\n            console.log(\"This mode can be disabled by calling 'pageDataTracker.disableDebug()'\");\n        }\n    }\n\n    ,getTrackingCode: function() {\n      var campaign = _satellite.getVar('Campaign - ID');\n      if(!campaign) {\n        campaign = window.sessionStorage ? sessionStorage.getItem('dgcid') : '';\n      }\n      return campaign;\n    }\n\n    ,isDebugEnabled: function() {\n        if(typeof this.debug === 'undefined') {\n            this.debug = (document.cookie.indexOf(this.debugCookie) !== -1) || (window.pageData && pageData.page && pageData.page.environment && pageData.page.environment.toLowerCase() === 'dev');\n            //this.debug = (document.cookie.indexOf(this.debugCookie) !== -1);\n        }\n        return this.debug;\n    }\n\n    ,enableDebug: function(expire) {\n        if (typeof expire === 'undefined') {\n            expire = 86400;\n        }\n        console.log('You just enabled debug mode for Adobe Analytics tracking. This mode will persist for 24h.');\n        console.log(\"This mode can be disabled by calling 'pageDataTracker.disableDebug()'\");\n        this.setCookie(this.debugCookie, 'true', expire, document.location.hostname);\n        this.debug = true;\n    }\n\n    ,disableDebug: function() {\n        console.log('Debug mode is now disabled.');\n        this.deleteCookie(this.debugCookie);\n        this.debug = false;\n    }\n\n    ,setAnalyticsData: function() {\n        if(!(window.pageData && pageData.page && pageData.page.productName && pageData.page.name)) {\n            return;\n        }\n        pageData.page.analyticsPagename = pageData.page.productName + ':' + pageData.page.name;\n\n        var pageEls = pageData.page.name.indexOf(':') > -1 ? pageData.page.name.split(':') : [pageData.page.name];\n        pageData.page.sectionName = pageData.page.productName + ':' + pageEls[0];\n    }\n\n    ,getEvents: function() {\n        pageData.savedEvents = {};\n        pageData.eventList = [];\n\n        var val = this.getCookie(this.eventCookieName);\n        if (val) {\n            pageData.savedEvents = val;\n        }\n\n        this.deleteCookie(this.eventCookieName);\n    }\n\n    ,updatePageData(data) {\n        window.pageData = window.pageData || {};\n        if (data && typeof(data) === 'object') {\n            for (var x in data) {\n                if(data.hasOwnProperty(x) && data[x] instanceof Array) {\n                    pageData[x] = data[x];\n                } else if(data.hasOwnProperty(x) && typeof(data[x]) === 'object') {\n                    if(!pageData[x]) {\n                        pageData[x] = {};\n                    }\n                    for (var y in data[x]) {\n                        if(data[x].hasOwnProperty(y)) {\n                            pageData[x][y] = data[x][y];\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    ,handleEventData: function(event, data) {\n        var val;\n        switch(event) {\n            case 'newPage':\n                this.updatePageData(data);\n                this.setAnalyticsData();\n            case 'saveSearch':\n            case 'searchResultsUpdated':\n                if (data) {\n                    // overwrite page-load object\n                    if (data.search && typeof(data.search) == 'object') {\n                        window.eventData.search.resultsPosition = '';\n                        pageData.search = pageData.search || {};\n                        var fields = ['advancedCriteria', 'criteria', 'currentPage', 'dataFormCriteria', 'facets', 'resultsByType', 'resultsPerPage', 'sortType', 'totalResults', 'type', 'database',\n                        'suggestedClickPosition','suggestedLetterCount','suggestedResultCount', 'autoSuggestCategory', 'autoSuggestDetails','typedTerm','selectedTerm', 'channel',\n                        'facetOperation', 'details'];\n                        for (var i=0; i<fields.length; i++) {\n                            if (data.search[fields[i]]) {\n                                pageData.search[fields[i]] = data.search[fields[i]];\n                            }\n                        }\n                    }\n                }\n                this.setAnalyticsData();\n                break;\n            case 'navigationClick':\n                if (data && data.link) {\n                    window.eventData.navigationLink = {\n                        name: ((data.link.location || 'no location') + ':' + (data.link.name || 'no name'))\n                    };\n                }\n                break;\n            case 'autoSuggestClick':\n                if (data && data.search) {\n                    val = {\n                        autoSuggestSearchData: (\n                            'letterct:' + (data.search.suggestedLetterCount || 'none') +\n                            '|resultct:' + (data.search.suggestedResultCount || 'none') +\n                            '|clickpos:' + (data.search.suggestedClickPosition || 'none')\n                        ).toLowerCase(),\n                        autoSuggestSearchTerm: (data.search.typedTerm || ''),\n                        autoSuggestTypedTerm: (data.search.typedTerm || ''),\n                        autoSuggestSelectedTerm: (data.search.selectedTerm || ''),\n                        autoSuggestCategory: (data.search.autoSuggestCategory || ''),\n                        autoSuggestDetails: (data.search.autoSuggestDetails || '')\n                    };\n                }\n                break;\n            case 'linkOut':\n                if (data && data.content && data.content.length > 0) {\n                    window.eventData.linkOut = data.content[0].linkOut;\n                    window.eventData.referringProduct = _satellite.getVar('Page - Product Name') + ':' + data.content[0].id;\n                }\n                break;\n            case 'socialShare':\n                if (data && data.social) {\n                    window.eventData.sharePlatform = data.social.sharePlatform || '';\n                }\n                break;\n            case 'contentInteraction':\n                if (data && data.action) {\n                    window.eventData.action.name = pageData.page.productName + ':' + data.action.name;\n                }\n                break;\n            case 'searchWithinContent':\n                if (data && data.search) {\n                    window.pageData.search = window.pageData.search || {};\n                    pageData.search.withinContentCriteria = data.search.withinContentCriteria;\n                }\n                break;\n            case 'contentShare':\n                if (data && data.content) {\n                    window.eventData.sharePlatform = data.content[0].sharePlatform;\n                }\n                break;\n            case 'contentLinkClick':\n                if (data && data.link) {\n                    window.eventData.action = { name: pageData.page.productName + ':' + (data.link.type || 'no link type') + ':' + (data.link.name || 'no link name') };\n                }\n                break;\n            case 'contentWindowLoad':\n            case 'contentTabClick':\n                if (data && data.content) {\n                    window.eventData.tabName = data.content[0].tabName || '';\n                    window.eventData.windowName = data.content[0].windowName || '';\n                }\n                break;\n            case 'userProfileUpdate':\n                if (data && data.user) {\n                    if (Object.prototype.toString.call(data.user) === \"[object Array]\") {\n                        window.eventData.user = data.user[0];\n                    }\n                }\n                break;\n            case 'videoStart':\n                if (data.video) {\n                    data.video.length = parseFloat(data.video.length || '0');\n                    data.video.position = parseFloat(data.video.position || '0');\n                    s.Media.open(data.video.id, data.video.length, s.Media.playerName);\n                    s.Media.play(data.video.id, data.video.position);\n                }\n                break;\n            case 'videoPlay':\n                if (data.video) {\n                    data.video.position = parseFloat(data.video.position || '0');\n                    s.Media.play(data.video.id, data.video.position);\n                }\n                break;\n            case 'videoStop':\n                if (data.video) {\n                    data.video.position = parseFloat(data.video.position || '0');\n                    s.Media.stop(data.video.id, data.video.position);\n                }\n                break;\n            case 'videoComplete':\n                if (data.video) {\n                    data.video.position = parseFloat(data.video.position || '0');\n                    s.Media.stop(data.video.id, data.video.position);\n                    s.Media.close(data.video.id);\n                }\n                break;\n            case 'addWebsiteExtension':\n                if(data && data.page) {\n                    val = {\n                        wx: data.page.websiteExtension\n                    }\n                }\n                break;\n        }\n\n        if (val) {\n            this.setCookie(this.eventCookieName, val);\n        }\n    }\n\n    ,registerCallbacks: function() {\n        var self = this;\n        if(window.usabilla_live) {\n            window.usabilla_live('setEventCallback', function(category, action, label, value) {\n                if(action == 'Campaign:Open') {\n                    self.trackEvent('ctaImpression', {\n                        cta: {\n                            ids: ['usabillaid:' + label]\n                        }\n                    });\n                } else if(action == 'Campaign:Success') {\n                    self.trackEvent('ctaClick', {\n                        cta: {\n                            ids: ['usabillaid:' + label]\n                        }\n                    });\n                }\n            });\n        }\n    }\n\n    ,getConsortiumAccountId: function() {\n        var id = '';\n        if (window.pageData && pageData.visitor && (pageData.visitor.consortiumId || pageData.visitor.accountId)) {\n            id = (pageData.visitor.consortiumId || 'no consortium ID') + '|' + (pageData.visitor.accountId || 'no account ID');\n        }\n\n        return id;\n    }\n\n    ,getSearchClickPosition: function() {\n        if (window.eventData && eventData.search && eventData.search.resultsPosition) {\n            var pos = parseInt(eventData.search.resultsPosition), clickPos;\n            if (!isNaN(pos)) {\n                var page = pageData.search.currentPage ? parseInt(pageData.search.currentPage) : '', perPage = pageData.search.resultsPerPage ? parseInt(pageData.search.resultsPerPage) : '';\n                if (!isNaN(page) && !isNaN(perPage)) {\n                    clickPos = pos + ((page - 1) * perPage);\n                }\n            }\n            return clickPos ? clickPos.toString() : eventData.search.resultsPosition;\n        }\n        return '';\n    }\n\n    ,getSearchFacets: function() {\n        var facetList = '';\n        if (window.pageData && pageData.search && pageData.search.facets) {\n            if (typeof(pageData.search.facets) == 'object') {\n                for (var i=0; i<pageData.search.facets.length; i++) {\n                    var f = pageData.search.facets[i];\n                    facetList += (facetList ? '|' : '') + f.name + '=' + f.values.join('^');\n                }\n            }\n        }\n        return facetList;\n    }\n\n    ,getSearchResultsByType: function() {\n        var resultTypes = '';\n        if (window.pageData && pageData.search && pageData.search.resultsByType) {\n            for (var i=0; i<pageData.search.resultsByType.length; i++) {\n                var r = pageData.search.resultsByType[i];\n                resultTypes += (resultTypes ? '|' : '') + r.name + (r.results || r.values ? '=' + (r.results || r.values) : '');\n            }\n        }\n        return resultTypes;\n    }\n\n    ,getJournalInfo: function() {\n        var info = '';\n        if (window.pageData && pageData.journal && (pageData.journal.name || pageData.journal.specialty || pageData.journal.section || pageData.journal.issn || pageData.journal.issueNumber || pageData.journal.volumeNumber || pageData.journal.family || pageData.journal.publisher)) {\n            var journal = pageData.journal;\n            info = (journal.name || 'no name') \n            + '|' + (journal.specialty || 'no specialty') \n            + '|' + (journal.section || 'no section') \n            + '|' + (journal.issn || 'no issn') \n            + '|' + (journal.issueNumber || 'no issue #') \n            + '|' + (journal.volumeNumber || 'no volume #')\n            + '|' + (journal.family || 'no family')\n            + '|' + (journal.publisher || 'no publisher');\n\n        }\n        return info;\n    }\n\n    ,getBibliographicInfo: function(doc) {\n        if (!doc || !(doc.publisher || doc.indexTerms || doc.publicationType || doc.publicationRights || doc.volumeNumber || doc.issueNumber || doc.subjectAreas || doc.isbn)) {\n            return '';\n        }\n\n        var terms = doc.indexTerms ? doc.indexTerms.split('+') : '';\n        if (terms) {\n            terms = terms.slice(0, 5).join('+');\n            terms = terms.length > 100 ? terms.substring(0, 100) : terms;\n        }\n\n        var areas = doc.subjectAreas ? doc.subjectAreas.split('>') : '';\n        if (areas) {\n            areas = areas.slice(0, 5).join('>');\n            areas = areas.length > 100 ? areas.substring(0, 100) : areas;\n        }\n\n        var biblio\t= (doc.publisher || 'none')\n            + '^' + (doc.publicationType || 'none')\n            + '^' + (doc.publicationRights || 'none')\n            + '^' + (terms || 'none')\n            + '^' + (doc.volumeNumber || 'none')\n            + '^' + (doc.issueNumber || 'none')\n            + '^' + (areas || 'none')\n            + '^' + (doc.isbn || 'none');\n\n        return this.stripProductDelimiters(biblio).toLowerCase();\n    }\n\n    ,getContentItem: function() {\n        var docs = window.eventData && eventData.content ? eventData.content : pageData.content;\n        if (docs && docs.length > 0) {\n            return docs[0];\n        }\n    }\n\n    ,getFormattedDate: function(ts) {\n        if (!ts) {\n            return '';\n        }\n\n        var d = new Date(parseInt(ts) * 1000);\n\n        // now do formatting\n        var year = d.getFullYear()\n            ,month = ((d.getMonth() + 1) < 10 ? '0' : '') + (d.getMonth() + 1)\n            ,date = (d.getDate() < 10 ? '0' : '') + d.getDate()\n            ,hours = d.getHours() > 12 ? d.getHours() - 12 : d.getHours()\n            ,mins = (d.getMinutes() < 10 ? '0' : '') + d.getMinutes()\n            ,ampm = d.getHours() > 12 ? 'pm' : 'am';\n\n        hours = (hours < 10 ? '0' : '') + hours;\n        return year + '-' + month + '-' + date;\n    }\n\n    ,getVisitorId: function() {\n        var orgId = '4D6368F454EC41940A4C98A6@AdobeOrg';\n        if(Visitor && Visitor.getInstance(orgId)) {\n            return Visitor.getInstance(orgId).getMarketingCloudVisitorID();\n        } else {\n            return ''\n        }\n    }\n\n    ,setProductsVariable: function() {\n        var prodList = window.eventData && eventData.content ? eventData.content : pageData.content\n            ,prods = [];\n        if (prodList) {\n            for (var i=0; i<prodList.length; i++) {\n                if (prodList[i].id || prodList[i].type || prodList[i].publishDate || prodList[i].onlineDate) {\n                    if (!prodList[i].id) {\n                        prodList[i].id = 'no id';\n                    }\n                    var prodName = (pageData.page.productName || 'xx').toLowerCase();\n                    if (prodList[i].id.indexOf(prodName + ':') != 0) {\n                        prodList[i].id = prodName + ':' + prodList[i].id;\n                    }\n                    prodList[i].id = this.stripProductDelimiters(prodList[i].id);\n                    var merch = [];\n                    if (prodList[i].format) {\n                        merch.push('evar17=' + this.stripProductDelimiters(prodList[i].format.toLowerCase()));\n                    }\n                    if (prodList[i].type) {\n                        var type = prodList[i].type;\n                        if (prodList[i].accessType) {\n                            type += ':' + prodList[i].accessType;\n                        }\n                        merch.push('evar20=' + this.stripProductDelimiters(type.toLowerCase()));\n\n                        if(type.indexOf(':manuscript') > 0) {\n                            /*\n                            var regex = /[a-z]+:manuscript:id:([a-z]+-[a-z]-[0-9]+-[0-9]+)/gmi;\n                            var m = regex.exec(prodList[i].id);\n                            if(m) {\n                                merch.push('evar200=' + m[1]);\n                            }\n                            merch.push('evar200=' + prodList[i].id);\n                            */\n                            a = prodList[i].id.lastIndexOf(':');\n                            if(a>0) {\n                                merch.push('evar200=' + prodList[i].id.substring(a+1).toUpperCase());\n                            }\n                        } else if(type.indexOf(':submission') > 0) {\n                            merch.push('evar200=' + prodList[i].id);\n                        }\n                    }\n                    if(!prodList[i].title) {\n                        prodList[i].title = prodList[i].name;\n                    }\n                    if (prodList[i].title) {\n                        merch.push('evar75=' + this.stripProductDelimiters(prodList[i].title.toLowerCase()));\n                    }\n                    if (prodList[i].breadcrumb) {\n                        merch.push('evar63=' + this.stripProductDelimiters(prodList[i].breadcrumb).toLowerCase());\n                    }\n                    var nowTs = new Date().getTime()/1000;\n                    if (prodList[i].onlineDate && !isNaN(prodList[i].onlineDate)) {\n                        if(prodList[i].onlineDate > 32503680000) {\n                            prodList[i].onlineDate = prodList[i].onlineDate/1000;\n                        }\n                        merch.push('evar122=' + this.stripProductDelimiters(pageDataTracker.getFormattedDate(prodList[i].onlineDate)));\n                        var onlineAge = Math.floor((nowTs - prodList[i].onlineDate) / 86400);\n                        onlineAge = (onlineAge === 0) ? 'zero' : onlineAge;\n                        merch.push('evar128=' + onlineAge);\n                    }\n                    if (prodList[i].publishDate && !isNaN(prodList[i].publishDate)) {\n                        if(prodList[i].publishDate > 32503680000) {\n                            prodList[i].publishDate = prodList[i].publishDate/1000;\n                        }\n                        merch.push('evar123=' + this.stripProductDelimiters(pageDataTracker.getFormattedDate(prodList[i].publishDate)));\n                        var publishAge = Math.floor((nowTs - prodList[i].publishDate) / 86400);\n                        publishAge = (publishAge === 0) ? 'zero' : publishAge;\n                        merch.push('evar127=' + publishAge);\n                    }\n                    if (prodList[i].onlineDate && prodList[i].publishDate) {\n                        merch.push('evar38=' + this.stripProductDelimiters(pageDataTracker.getFormattedDate(prodList[i].onlineDate) + '^' + pageDataTracker.getFormattedDate(prodList[i].publishDate)));\n                    }\n                    if (prodList[i].mapId) {\n                        merch.push('evar70=' + this.stripProductDelimiters(prodList[i].mapId));\n                    }\n\t\t\t\t\tif (prodList[i].relevancyScore) {\n\t\t\t\t\t\tmerch.push('evar71=' + this.stripProductDelimiters(prodList[i].relevancyScore));\n\t\t\t\t\t}\n                    if (prodList[i].status) {\n                        merch.push('evar73=' + this.stripProductDelimiters(prodList[i].status));\n                    }\n                    if (prodList[i].previousStatus) {\n                        merch.push('evar111=' + this.stripProductDelimiters(prodList[i].previousStatus));\n                    }\n                    if (prodList[i].entitlementType) {\n                        merch.push('evar80=' + this.stripProductDelimiters(prodList[i].entitlementType));\n                    }\n                    if (prodList[i].recordType) {\n                        merch.push('evar93=' + this.stripProductDelimiters(prodList[i].recordType));\n                    }\n                    if (prodList[i].exportType) {\n                        merch.push('evar99=' + this.stripProductDelimiters(prodList[i].exportType));\n                    }\n                    if (prodList[i].importType) {\n                        merch.push('evar142=' + this.stripProductDelimiters(prodList[i].importType));\n                    }\n                    if (prodList[i].section) {\n                        merch.push('evar100=' + this.stripProductDelimiters(prodList[i].section));\n                    }\n                    if (prodList[i].detail) {\n                        merch.push('evar104=' + this.stripProductDelimiters(prodList[i].detail.toLowerCase()));\n                    } else if(prodList[i].details) {\n                        merch.push('evar104=' + this.stripProductDelimiters(prodList[i].details.toLowerCase()));\n                    }\n                    if (prodList[i].position) {\n                        merch.push('evar116=' + this.stripProductDelimiters(prodList[i].position));\n                    }\n                    if (prodList[i].publicationTitle) {\n                        merch.push('evar129=' + this.stripProductDelimiters(prodList[i].publicationTitle));\n                    }\n                    if (prodList[i].specialIssueTitle) {\n                        merch.push('evar130=' + this.stripProductDelimiters(prodList[i].specialIssueTitle));\n                    }\n                    if (prodList[i].specialIssueNumber) {\n                        merch.push('evar131=' + this.stripProductDelimiters(prodList[i].specialIssueNumber));\n                    }\n                    if (prodList[i].referenceModuleTitle) {\n                        merch.push('evar139=' + this.stripProductDelimiters(prodList[i].referenceModuleTitle));\n                    }\n                    if (prodList[i].referenceModuleISBN) {\n                        merch.push('evar140=' + this.stripProductDelimiters(prodList[i].referenceModuleISBN));\n                    }\n                    if (prodList[i].volumeTitle) {\n                        merch.push('evar132=' + this.stripProductDelimiters(prodList[i].volumeTitle));\n                    }\n                    if (prodList[i].publicationSection) {\n                        merch.push('evar133=' + this.stripProductDelimiters(prodList[i].publicationSection));\n                    }\n                    if (prodList[i].publicationSpecialty) {\n                        merch.push('evar134=' + this.stripProductDelimiters(prodList[i].publicationSpecialty));\n                    }\n                    if (prodList[i].issn) {\n                        merch.push('evar135=' + this.stripProductDelimiters(prodList[i].issn));\n                    }\n                    if (prodList[i].id2) {\n                        merch.push('evar159=' + this.stripProductDelimiters(prodList[i].id2));\n                    }\n                    if (prodList[i].id3) {\n                        merch.push('evar160=' + this.stripProductDelimiters(prodList[i].id3));\n                    }\n                    if (prodList[i].provider) {\n                        merch.push('evar164=' + this.stripProductDelimiters(prodList[i].provider));\n                    }\n                    if (prodList[i].citationStyle) {\n                        merch.push('evar170=' + this.stripProductDelimiters(prodList[i].citationStyle));\n                    }\n\n                    var biblio = this.getBibliographicInfo(prodList[i]);\n                    if (biblio) {\n                        merch.push('evar28=' + biblio);\n                    }\n\n                    if (prodList[i].turnawayId) {\n                        pageData.eventList.push('product turnaway');\n                    }\n\n                    var price = prodList[i].price || '', qty = prodList[i].quantity || '', evts = [];\n                    if (price && qty) {\n                        qty = parseInt(qty || '1');\n                        price = parseFloat(price || '0');\n                        price = (price * qty).toFixed(2);\n\n                        if (window.eventData && eventData.eventName && eventData.eventName == 'cartAdd') {\n                            evts.push('event20=' + price);\n                        }\n                    }\n\n                    var type = window.pageData && pageData.page && pageData.page.type ? pageData.page.type : '', evt = window.eventData && eventData.eventName ? eventData.eventName : '';\n                    if (type.match(/^CP\\-/gi) !== null && (!evt || evt == 'newPage' || evt == 'contentView')) {\n                        evts.push('event181=1');\n                    }\n                    if (evt == 'contentDownload' || type.match(/^CP\\-DL/gi) !== null) {\n                        evts.push('event182=1');\n                    }\n                    if (evt == 'contentDownloadRequest') {\n                        evts.push('event319=1');\n                    }\n                    if (evt == 'contentExport') {\n                        evts.push('event184=1');\n                    }\n                    if (this.eventFires('recommendationViews')) {\n                        evts.push('event264=1');\n                    }\n\n                    if(prodList[i].datapoints) {\n                        evts.push('event239=' + prodList[i].datapoints);\n                    }\n                    if(prodList[i].documents) {\n                        evts.push('event240=' + prodList[i].documents);\n                    }\n                    if(prodList[i].size) {\n                        evts.push('event335=' + prodList[i].size);\n                        evts.push('event336=1')\n                    }\n\n                    if(evt == 'genAIContentUpdated') {\n                        evts.push('event51=1');\n                    }\n\n                    prods.push([\n                        ''\t\t\t\t\t// empty category\n                        ,prodList[i].id\t\t// id\n                        ,qty\t\t\t\t// qty\n                        ,price\t\t\t\t// price\n                        ,evts.join('|')\t\t// events\n                        ,merch.join('|')\t// merchandising eVars\n                    ].join(';'));\n                }\n            }\n        }\n\n        return prods.join(',');\n    }\n    ,eventFires: function(eventName) {\n      var evt = window.eventData && eventData.eventName ? eventData.eventName : '';\n      if(evt == eventName) {\n        return true;\n      }\n      // initial pageload and new pages\n      if((!window.eventData || evt == 'newPage') && window.pageData && window.pageData.trackEvents) {\n        var tEvents = window.pageData.trackEvents;\n        for(var i=0; i<tEvents.length; i++) {\n          if(tEvents[i] == eventName) {\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n\n    ,md5: function(s){function L(k,d){return(k<<d)|(k>>>(32-d))}function K(G,k){var I,d,F,H,x;F=(G&2147483648);H=(k&2147483648);I=(G&1073741824);d=(k&1073741824);x=(G&1073741823)+(k&1073741823);if(I&d){return(x^2147483648^F^H)}if(I|d){if(x&1073741824){return(x^3221225472^F^H)}else{return(x^1073741824^F^H)}}else{return(x^F^H)}}function r(d,F,k){return(d&F)|((~d)&k)}function q(d,F,k){return(d&k)|(F&(~k))}function p(d,F,k){return(d^F^k)}function n(d,F,k){return(F^(d|(~k)))}function u(G,F,aa,Z,k,H,I){G=K(G,K(K(r(F,aa,Z),k),I));return K(L(G,H),F)}function f(G,F,aa,Z,k,H,I){G=K(G,K(K(q(F,aa,Z),k),I));return K(L(G,H),F)}function D(G,F,aa,Z,k,H,I){G=K(G,K(K(p(F,aa,Z),k),I));return K(L(G,H),F)}function t(G,F,aa,Z,k,H,I){G=K(G,K(K(n(F,aa,Z),k),I));return K(L(G,H),F)}function e(G){var Z;var F=G.length;var x=F+8;var k=(x-(x%64))/64;var I=(k+1)*16;var aa=Array(I-1);var d=0;var H=0;while(H<F){Z=(H-(H%4))/4;d=(H%4)*8;aa[Z]=(aa[Z]| (G.charCodeAt(H)<<d));H++}Z=(H-(H%4))/4;d=(H%4)*8;aa[Z]=aa[Z]|(128<<d);aa[I-2]=F<<3;aa[I-1]=F>>>29;return aa}function B(x){var k=\"\",F=\"\",G,d;for(d=0;d<=3;d++){G=(x>>>(d*8))&255;F=\"0\"+G.toString(16);k=k+F.substr(F.length-2,2)}return k}function J(k){k=k.replace(/rn/g,\"n\");var d=\"\";for(var F=0;F<k.length;F++){var x=k.charCodeAt(F);if(x<128){d+=String.fromCharCode(x)}else{if((x>127)&&(x<2048)){d+=String.fromCharCode((x>>6)|192);d+=String.fromCharCode((x&63)|128)}else{d+=String.fromCharCode((x>>12)|224);d+=String.fromCharCode(((x>>6)&63)|128);d+=String.fromCharCode((x&63)|128)}}}return d}var C=Array();var P,h,E,v,g,Y,X,W,V;var S=7,Q=12,N=17,M=22;var A=5,z=9,y=14,w=20;var o=4,m=11,l=16,j=23;var U=6,T=10,R=15,O=21;s=J(s);C=e(s);Y=1732584193;X=4023233417;W=2562383102;V=271733878;for(P=0;P<C.length;P+=16){h=Y;E=X;v=W;g=V;Y=u(Y,X,W,V,C[P+0],S,3614090360);V=u(V,Y,X,W,C[P+1],Q,3905402710);W=u(W,V,Y,X,C[P+2],N,606105819);X=u(X,W,V,Y,C[P+3],M,3250441966);Y=u(Y,X,W,V,C[P+4],S,4118548399);V=u(V,Y,X,W,C[P+5],Q,1200080426);W=u(W,V,Y,X,C[P+6],N,2821735955);X=u(X,W,V,Y,C[P+7],M,4249261313);Y=u(Y,X,W,V,C[P+8],S,1770035416);V=u(V,Y,X,W,C[P+9],Q,2336552879);W=u(W,V,Y,X,C[P+10],N,4294925233);X=u(X,W,V,Y,C[P+11],M,2304563134);Y=u(Y,X,W,V,C[P+12],S,1804603682);V=u(V,Y,X,W,C[P+13],Q,4254626195);W=u(W,V,Y,X,C[P+14],N,2792965006);X=u(X,W,V,Y,C[P+15],M,1236535329);Y=f(Y,X,W,V,C[P+1],A,4129170786);V=f(V,Y,X,W,C[P+6],z,3225465664);W=f(W,V,Y,X,C[P+11],y,643717713);X=f(X,W,V,Y,C[P+0],w,3921069994);Y=f(Y,X,W,V,C[P+5],A,3593408605);V=f(V,Y,X,W,C[P+10],z,38016083);W=f(W,V,Y,X,C[P+15],y,3634488961);X=f(X,W,V,Y,C[P+4],w,3889429448);Y=f(Y,X,W,V,C[P+9],A,568446438);V=f(V,Y,X,W,C[P+14],z,3275163606);W=f(W,V,Y,X,C[P+3],y,4107603335);X=f(X,W,V,Y,C[P+8],w,1163531501);Y=f(Y,X,W,V,C[P+13],A,2850285829);V=f(V,Y,X,W,C[P+2],z,4243563512);W=f(W,V,Y,X,C[P+7],y,1735328473);X=f(X,W,V,Y,C[P+12],w,2368359562);Y=D(Y,X,W,V,C[P+5],o,4294588738);V=D(V,Y,X,W,C[P+8],m,2272392833);W=D(W,V,Y,X,C[P+11],l,1839030562);X=D(X,W,V,Y,C[P+14],j,4259657740);Y=D(Y,X,W,V,C[P+1],o,2763975236);V=D(V,Y,X,W,C[P+4],m,1272893353);W=D(W,V,Y,X,C[P+7],l,4139469664);X=D(X,W,V,Y,C[P+10],j,3200236656);Y=D(Y,X,W,V,C[P+13],o,681279174);V=D(V,Y,X,W,C[P+0],m,3936430074);W=D(W,V,Y,X,C[P+3],l,3572445317);X=D(X,W,V,Y,C[P+6],j,76029189);Y=D(Y,X,W,V,C[P+9],o,3654602809);V=D(V,Y,X,W,C[P+12],m,3873151461);W=D(W,V,Y,X,C[P+15],l,530742520);X=D(X,W,V,Y,C[P+2],j,3299628645);Y=t(Y,X,W,V,C[P+0],U,4096336452);V=t(V,Y,X,W,C[P+7],T,1126891415);W=t(W,V,Y,X,C[P+14],R,2878612391);X=t(X,W,V,Y,C[P+5],O,4237533241);Y=t(Y,X,W,V,C[P+12],U,1700485571);V=t(V,Y,X,W,C[P+3],T,2399980690);W=t(W,V,Y,X,C[P+10],R,4293915773);X=t(X,W,V,Y,C[P+1],O,2240044497);Y=t(Y,X,W,V,C[P+8],U,1873313359);V=t(V,Y,X,W,C[P+15],T,4264355552);W=t(W,V,Y,X,C[P+6],R,2734768916);X=t(X,W,V,Y,C[P+13],O,1309151649);Y=t(Y,X,W,V,C[P+4],U,4149444226);V=t(V,Y,X,W,C[P+11],T,3174756917);W=t(W,V,Y,X,C[P+2],R,718787259);X=t(X,W,V,Y,C[P+9],O,3951481745);Y=K(Y,h);X=K(X,E);W=K(W,v);V=K(V,g)}var i=B(Y)+B(X)+B(W)+B(V);return i.toLowerCase()}\n    ,stripProductDelimiters: function(val) {\n        if (val) {\n            return val.replace(/\\;|\\||\\,/gi, '-');\n        }\n    }\n\n    ,setCookie: function(name, value, seconds, domain) {\n        domain = document.location.hostname;\n        var expires = '';\n        var expiresNow = '';\n        var date = new Date();\n        date.setTime(date.getTime() + (-1 * 1000));\n        expiresNow = \"; expires=\" + date.toGMTString();\n\n        if (typeof(seconds) != 'undefined') {\n            date.setTime(date.getTime() + (seconds * 1000));\n            expires = '; expires=' + date.toGMTString();\n        }\n\n        var type = typeof(value);\n        type = type.toLowerCase();\n        if (type != 'undefined' && type != 'string') {\n            value = JSON.stringify(value);\n        }\n\n        // fix scoping issues\n        // keep writing the old cookie, but make it expire\n        document.cookie = name + '=' + value + expiresNow + '; path=/';\n\n        // now just set the right one\n        document.cookie = name + '=' + value + expires + '; path=/; domain=' + domain;\n    }\n\n    ,getCookie: function(name) {\n        name = name + '=';\n        var carray = document.cookie.split(';'), value;\n\n        for (var i=0; i<carray.length; i++) {\n            var c = carray[i];\n            while (c.charAt(0) == ' ') {\n                c = c.substring(1, c.length);\n            }\n            if (c.indexOf(name) == 0) {\n                value = c.substring(name.length, c.length);\n                try {\n                    value = JSON.parse(value);\n                } catch(ex) {}\n\n                return value;\n            }\n        }\n\n        return null;\n    }\n\n    ,deleteCookie: function(name) {\n        this.setCookie(name, '', -1);\n        this.setCookie(name, '', -1, document.location.hostname);\n    }\n\n    ,mapAdobeVars: function(s) {\n        var vars = {\n            pageName\t\t: 'Page - Analytics Pagename'\n            ,channel\t\t: 'Page - Section Name'\n            ,campaign\t\t: 'Campaign - ID'\n            ,currencyCode\t: 'Page - Currency Code'\n            ,purchaseID\t\t: 'Order - ID'\n            ,prop1\t\t\t: 'Visitor - Account ID'\n            ,prop2\t\t\t: 'Page - Product Name'\n            ,prop4\t\t\t: 'Page - Type'\n            ,prop6\t\t\t: 'Search - Type'\n            ,prop7\t\t\t: 'Search - Facet List'\n            ,prop8\t\t\t: 'Search - Feature Used'\n            ,prop12\t\t\t: 'Visitor - User ID'\n            ,prop13\t\t\t: 'Search - Sort Type'\n            ,prop14\t\t\t: 'Page - Load Time'\n            ,prop15         : 'Support - Topic Name'\n            ,prop16\t\t\t: 'Page - Business Unit'\n            ,prop21\t\t\t: 'Search - Criteria'\n            ,prop24\t\t\t: 'Page - Language'\n            ,prop25\t\t\t: 'Page - Product Feature'\n            ,prop28         : 'Support - Search Criteria'\n            ,prop30\t\t\t: 'Visitor - IP Address'\n            ,prop33         : 'Page - Product Application Version'\n            ,prop34         : 'Page - Website Extensions'\n            ,prop60\t\t\t: 'Search - Data Form Criteria'\n            ,prop63\t\t\t: 'Page - Extended Page Name'\n            ,prop65         : 'Page - Online State'\n            ,prop67         : 'Research Networks'\n            ,prop40: 'Page - UX Properties'\n\n            ,eVar3\t\t\t: 'Search - Total Results'\n            ,eVar7\t\t\t: 'Visitor - Account Name'\n            ,eVar15\t\t\t: 'Event - Search Results Click Position'\n            ,eVar19\t\t\t: 'Search - Advanced Criteria'\n            ,eVar21\t\t\t: 'Promo - Clicked ID'\n            ,eVar22\t\t\t: 'Page - Test ID'\n            ,eVar47\t\t\t: 'Page - Test Subject ID'\n            ,eVar27\t\t\t: 'Event - AutoSuggest Search Data'\n            ,eVar157\t\t: 'Event - AutoSuggest Search Typed Term'\n            ,eVar156\t\t: 'Event - AutoSuggest Search Selected Term'\n            ,eVar162\t\t: 'Event - AutoSuggest Search Category'\n            ,eVar163\t\t: 'Event - AutoSuggest Search Details'\n            ,eVar33\t\t\t: 'Visitor - Access Type'\n            ,eVar34\t\t\t: 'Order - Promo Code'\n            ,eVar39\t\t\t: 'Order - Payment Method'\n            ,eVar41\t\t\t: 'Visitor - Industry'\n            ,eVar42\t\t\t: 'Visitor - SIS ID'\n            ,eVar43\t\t\t: 'Page - Error Type'\n            ,eVar44\t\t\t: 'Event - Updated User Fields'\n            ,eVar48\t\t\t: 'Email - Recipient ID'\n            ,eVar51\t\t\t: 'Email - Message ID'\n            ,eVar52\t\t\t: 'Visitor - Department ID'\n            ,eVar53\t\t\t: 'Visitor - Department Name'\n            ,eVar60\t\t\t: 'Search - Within Content Criteria'\n            ,eVar61\t\t\t: 'Search - Within Results Criteria'\n            ,eVar62\t\t\t: 'Search - Result Types'\n            ,eVar74\t\t\t: 'Page - Journal Info'\n            ,eVar59\t\t\t: 'Page - Journal Publisher'\n            ,eVar76\t\t\t: 'Email - Broadlog ID'\n            ,eVar78\t\t\t: 'Visitor - Details'\n            ,eVar80         : 'Visitor - Usage Path Info'\n            ,eVar102\t\t: 'Form - Name'\n            ,eVar103        : 'Event - Conversion Driver'\n            ,eVar105        : 'Search - Current Page'\n            ,eVar106        : 'Visitor - App Session ID'\n            ,eVar107        : 'Page - Secondary Product Name'\n            ,eVar117        : 'Search - Database'\n            ,eVar126        : 'Page - Environment'\n            ,eVar141        : 'Search - Criteria Original'\n            ,eVar143        : 'Page - Tabs'\n            ,eVar161        : 'Search - Channel'\n            ,eVar169        : 'Search - Facet Operation'\n            ,eVar173        : 'Search - Details'\n            ,eVar174        : 'Campaign - Spredfast ID'\n            ,eVar175        : 'Visitor - TMX Device ID'\n            ,eVar176        : 'Visitor - TMX Request ID'\n            ,eVar148        : 'Visitor - Platform Name'\n            ,eVar149        : 'Visitor - Platform ID'\n            ,eVar152        : 'Visitor - Product ID'\n            ,eVar153        : 'Visitor - Superaccount ID'\n            ,eVar154        : 'Visitor - Superaccount Name'\n            ,eVar177        : 'Page - Context Domain'\n            ,eVar189    : 'Page - Experimentation User Id'\n            ,eVar190    : 'Page - Identity User'\n            ,eVar199    : 'Page - ID+ Parameters'\n\n            ,list2\t\t\t: 'Page - Widget Names'\n            ,list3\t\t\t: 'Promo - IDs'\n        };\n\n        for (var i in vars) {\n            s[i] = s[i] ? s[i] : _satellite.getVar(vars[i]);\n        }\n    }\n};\n\n// async support fallback\n(function(w) {\n\tvar eventBuffer = [];\n\tif(w.appData) {\n\t\tif(Array.isArray(w.appData)) {\n\t\t\teventBuffer = w.appData;\n\t\t} else {\n\t\t\tconsole.error('Elsevier DataLayer \"window.appData\" must be specified as array');\n\t\t\treturn;\n\t\t}\n    }\n\n\tw.appData = [];\n\n\tvar oldPush = w.appData.push;\n\n\tvar appDataPush = function() {\n        oldPush.apply(w.appData, arguments);\n        for(var i=0; i<arguments.length; i++) {\n            var data = arguments[i];\n            if(data.event) {\n                if(data.event == 'pageLoad') {\n                    w.pageDataTracker.trackPageLoad(data);\n                } else {\n                    w.pageDataTracker.trackEvent(data.event, data);\n                }\n            }\n        }\n\t};\n\n\tw.appData.push = appDataPush;\n\tfor(var i=0; i<eventBuffer.length; i++) {\n\t    var data = eventBuffer[i];\n\t    w.appData.push(data);\n\t}\n})(window);\n",
language:"javascript"}}]},{id:"RL279433bf4a484bc7bbd239f1db2c14e2",name:"Event - Purchase Complete",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"purchaseComplete"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){if(window.eventData&&window.eventData.order){var a=window.eventData.order;t.purchaseID=a.id,t.eVar34=a.promoCode,t.eVar39=a.paymentMethod,t.linkTrackVars=t.apl(t.linkTrackVars,"purchaseID",",",2),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar14",",",2),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar34",",",2),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar39",",",2),window.eventData.page&&window.eventData.page.currencyCode&&(t.currencyCode=window.eventData.page.currencyCode,t.linkTrackVars=t.apl(t.linkTrackVars,"currencyCode",",",2))}}},trackerProperties:{events:[{name:"purchase"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"",linkType:"o"}}]},{id:"RL2c07c37920084855aba22d6a8cb21eee",name:"Event - Conversion Driver Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"conversionDriverClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){t.eVar103=_satellite.getVar("Event - Conversion Driver"),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar103",",",2),t.eVar110="D=pageName",t.linkTrackVars=t.apl(t.linkTrackVars,"eVar110",",",2)}}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"conversion driver click",linkType:"o"}}]},{id:"RL3554c6a697ce4f4798156c321b226622",name:"Event - Widget Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"widgetClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){t.list2=_satellite.getVar("Page - Widget Names"),t.linkTrackVars=t.apl(t.linkTrackVars,"list2",",",2)}},trackerProperties:{events:[{name:"event179"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"widget click",linkType:"o"}}]},{id:"RL3a6cadc12dd04d5dada96d0d0b2ea4ed",name:"Event - Search Feature Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"searchFeatureClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar18",type:"value",value:"%Search - Feature Used%"}],props:[{name:"prop8",type:"value",value:"%Search - Feature Used%"}],events:[{name:"event10"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"search feature click",linkType:"o"}}]},{id:"RL3c26f9d51ed3423796fad0795172bca5",name:"Event - Edit Alert",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"editAlert"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar97",type:"value",value:"%Event - Alert Type%"},{name:"eVar1",type:"value",value:"%Search - Criteria%"}],props:[{name:"prop21",type:"value",value:"%Search - Criteria%"}],events:[{name:"event235"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"edit alert",linkType:"o"}}]},{id:"RL3eb352ba566b48438f1a414a1aff9b92",name:"Event - Save to List",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"saveToList"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event48"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"save to list",linkType:"o"}}]},{id:"RL43266e7673c440dbaee5cc1fba7c55ea",name:"Event - Form Error",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"formError"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar43",type:"value",value:"%Form - Error Type%"}],events:[{name:"event26"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"form error",linkType:"o"}}]},{id:"RL43b94f9f61dd49c686c0a8d4a158191f",name:"Event - CTA Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"ctaClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(){window.eventData&&eventData.cta&&eventData.cta.ids&&eventData.cta.ids.length>0&&(window.clickedPromoId=eventData.cta.ids[0])}},trackerProperties:{events:[{name:"event22"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"cta click",linkType:"o"}}]},{id:"RL477c5f510d31411ab31110d40517bba6",name:"Event - Search Within Content",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"searchWithinContent"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar60",type:"value",value:"%Search - Within Content Criteria%"}],events:[{name:"event75"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"search within content",linkType:"o"}}]},{id:"RL47d6dd8ac6074ab9b17b4ef3cf2a070c",name:"Event - Navigation Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"navigationClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar12",type:"value",value:"%Event - Navigation Link Name%"}],props:[{name:"prop26",type:"value",value:"%Event - Navigation Link Name%"}],events:[{name:"event45"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"navigation click",linkType:"o"}}]},{id:"RL48b800486cd34347997acc42b6e95ff2",name:"Event - New Page",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"newPage"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{pageName:"%Page - Analytics Pagename%"}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"page"}}]},{id:"RL4ae63feca0d94e159f863f91b4959beb",name:"Event - Content View",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"contentView"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event5"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"content view",linkType:"o"}}]},{id:"RL4d507167b3654a218cabe475f0ca8b21",name:"Event - Edit Alert Start",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"editAlertStart"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar97",type:"value",value:"%Event - Alert Type%"},{name:"eVar1",type:"value",value:"%Search - Criteria%"}],props:[{name:"prop21",type:"value",value:"%Search - Criteria%"}],events:[{name:"event236"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"edit alert start",linkType:"o"}}]},{id:"RL58216b18acc54943b8f64ee707feb9b0",name:"Event - Export",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"rowsExported"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar69",type:"value",value:"%Event - Rows Exported%"}],events:[{name:"event39"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"rows exported",linkType:"o"}}]},{id:"RL58f9b17393ea4fe89422ebc555b500f0",name:"Event - Saved Search",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"saveSearch"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"saved search",linkType:"o"}}]},{id:"RL5ab0c7c532794c75a0a0f66da1163e42",name:"Event - Login Start",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"loginStart"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event141"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"login start",linkType:"o"}}]},{id:"RL5d542e957a5b4d368dc06f57775cd4b0",name:"Event - Save Alert Start",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"saveAlertStart"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar97",type:"value",value:"%Event - Alert Type%"},{name:"eVar1",type:"value",value:"%Search - Criteria%"}],props:[{name:"prop21",type:"value",value:"%Search - Criteria%"}],events:[{name:"event234"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"save alert start",linkType:"o"}}]},{id:"RL5d7219dfb1a64345914ae399df07680d",name:"Event - Content Link Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"contentLinkClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{props:[{name:"prop23",type:"value",value:"%Event - Action Name%"}],events:[{name:"event87"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"content-specific link click",linkType:"o"}}]},{id:"RL63bfd5128c33464eb6d37878e7ca3fbf",name:"websiteBot",events:[{modulePath:"core/src/lib/events/customCode.js",settings:{source:function(){exFlag=!/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-||o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substr(0,4))?"":"e",sessionStorage.getItem("aamm")||sessionStorage.setItem("aamm","n"+exFlag),document.addEventListener("mousemove",(function e(){document.removeEventListener("mousemove",e,!1),sessionStorage.setItem("aamm","y"+exFlag)}))}},ruleOrder:50}],conditions:[],actions:[]},{id:"RL66ba8469ca46466a900f1871b2963de9",name:"Event - Content Share",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"contentShare"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar30",type:"value",value:"%Event - Share Platform%"}],events:[{name:"event11"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"content shared",linkType:"o"}}]},{id:"RL6a9a4e0401db4553a3702681fa99d3b6",name:"Event - Content Share Start",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"contentShareStart"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event206"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"content share start",linkType:"o"}}]},{id:"RL6cd9f144fd834488845f9bb20e27bf15",name:"Event - Content Download",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"contentDownload"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){t.list2=_satellite.getVar("Page - Widget Names"),t.linkTrackVars=t.apl(t.linkTrackVars,"list2",",",2)}},trackerProperties:{events:[{name:"event19"},{name:"event182"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"content download",linkType:"o"}}]},{id:"RL6e6eea7df7ad4752800e54197235a134",name:"Event - Content Tab Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"contentTabClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{props:[{name:"prop20",type:"value",value:"%Event - Tab Name%"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"",linkType:"o"}}]},{id:"RL79f16b8eb05b4c1fb64a680a7f50ee89",name:"Event - LinkOut",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"linkOut"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){t.list2=_satellite.getVar("Page - Widget Names"),t.linkTrackVars=t.apl(t.linkTrackVars,"list2",",",2)}},trackerProperties:{eVars:[{name:"eVar37",type:"value",value:"%Event - LinkOut Destination%"},{name:"eVar23",type:"value",value:"%Event - LinkOut Referring Product%"},{name:"eVar49",type:"value",value:"%Event - LinkOut Referring Product%"}],events:[{name:"event25"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"content link-out",linkType:"e"}}]},{id:"RL801b2c84d3764f8abe51ba2fea1f4e63",name:"Event - Delete Alert",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"deleteAlert"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar97",type:"value",value:"%Event - Alert Type%"},{name:"eVar1",type:"value",value:"%Search - Criteria%"}],props:[{name:"prop21",type:"value",value:"%Search - Criteria%"}],events:[{name:"event237"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"delete alert",linkType:"o"}}]},{id:"RL83dc58a9338041ec94aebb8446b89555",name:"3rd-pcd",events:[{modulePath:"core/src/lib/events/customCode.js",settings:{source:function(){window.addEventListener("message",(function(e){try{0==e.data.indexOf("---destpub-to-parent---canSetThirdPartyCookies")&&sessionStorage.setItem("_pageDataTracker_tpcf",e.data.split("|")[1])}catch(e){}}))}},ruleOrder:50}],conditions:[],actions:[]},{id:"RL8a8e673a9c6b4d8484315cbd074c4d84",name:"Event - AA Conversion Driver Click",events:[{modulePath:"core/src/lib/events/click.js",settings:{elementSelector:"[data-aa-conversiondriver]",bubbleFireIfChildFired:!1},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/clearVariables.js",settings:{}},{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){t.eVar103=this.getAttribute("data-aa-conversiondriver"),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar103",",",2),t.eVar110="D=pageName",t.linkTrackVars=t.apl(t.linkTrackVars,"eVar110",",",2),window.eventData={}}},trackerProperties:{}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"conversion driver click",linkType:"o"}}]},{id:"RL920ba4e969da4f5780dd5e1c110240b7",name:"Event - GenAI Closed",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"genAIClosed"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event347"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"genai closed",linkType:"o"}}]},{id:"RL99508f8fb3514787a846eda572546d03",name:"Event - Survey Submission",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"SurveySubmission"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar55",type:"value",value:"%Event - Survey Comment%"},{name:"eVar56",type:"alias",value:"prop43"}],props:[{name:"prop43",type:"value",value:"%Event - Survey Meta Data%"}],events:[{name:"event323"},{name:"event322",value:"%Event - Survey Score%"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"survey submission",linkType:"o"}}]},{id:"RL9c15b75edf9b49b6bbe2c8063207c4db",name:"Event - Content Export",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"contentExport"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar69",type:"value",value:"%Event - Rows Exported%"}],events:[{name:"event39"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"content export",linkType:"o"}}]},{id:"RL9d5671546f5d4ceaae1f8f6b1253386c",name:"eventDispatcher",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"eventDispatcher"},ruleOrder:50}],conditions:[],actions:[{modulePath:"core/src/lib/actions/customCode.js",settings:{source:"https://assets.adobedtm.com/4a848ae9611a/032db4f73473/93571d51095c/RCa16d232f95a944c0aabdea6621a2ef94-source.min.js",language:"javascript",isExternal:!0}}]},{id:"RLa630ea3ce6bb46ac92ebac6beea15c10",name:"Event - Campaign Referral Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"campaignReferralClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{props:[{name:"prop55",type:"value",value:"%Event - Link Referral Name%"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"campaign referral click",linkType:"o"}}]},{id:"RLa713d35eecc247afb0093d20f9ad58e0",name:"Event - Search Results Updated",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"searchResultsUpdated"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{pageName:"%Page - Analytics Pagename%"}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"page"}}]},{id:"RLa7487c2c22fd46a9933433fcb14ac68a",name:"Event - NPS Submission",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"NPSSubmission"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar55",type:"value",value:"%Event - NPS Comment%"}],events:[{name:"event323"},{name:"event322",value:"%Event - NPS Score%"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"nps submission",linkType:"o"}}]},{id:"RLa7bb9996873f4574be9e313cd387d082",name:"Event - User Profile Update",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"userProfileUpdate"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar44",type:"value",value:"%Event - Updated User Fields%"}],events:[{name:"event17"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"user profile update",linkType:"o"}}]},{id:"RLadf95e414619450db90bc0403545f846",name:"Event - Delete Alert Start",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"deleteAlertStart"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar97",type:"value",value:"%Event - Alert Type%"},{name:"eVar1",type:"value",value:"%Search - Criteria%"}],props:[{name:"prop21",type:"value",value:"%Search - Criteria%"}],events:[{name:"event238"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"delete alert start",linkType:"o"}}]},{id:"RLb1400f79bc6e4bb1a4ced0cd0475278e",name:"Event - Form Submit",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"formSubmit"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"form submit",linkType:"o"}}]},{id:"RLb1502ca8c14549a6914d42e022c2d767",name:"Event - CTA Impression",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"ctaImpression"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){if(t.list3=_satellite.getVar("Promo - IDs"),t.list3){for(var a=t.list3.split("|"),n=0;n<a.length;n++)a[n]=t.productPrefix(a[n]);t.list3=a.join("|"),t.linkTrackVars=t.apl(t.linkTrackVars,"list3",",",2)}}},trackerProperties:{events:[{name:"event21"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"cta impression",linkType:"o"}}]},{id:"RLb38274a29ace4be0a6130520ac7b5143",name:"Event - Content Interaction",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"contentInteraction"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{props:[{name:"prop53",type:"value",value:"%Event - Action Name%"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"content interaction",linkType:"o"}}]},{id:"RLb7c2ca0266384d189c8b87a90e5e79c4",name:"Event - Remove From My List",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"removeFromMyList"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event192"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"",linkType:"o"}}]},{id:"RLbc47031479fa4453a20a66514699f006",name:"Event - AA Button Click",events:[{modulePath:"core/src/lib/events/click.js",settings:{elementSelector:"[data-aa-button]",bubbleFireIfChildFired:!1},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/clearVariables.js",settings:{}},{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){t.eVar124=this.getAttribute("data-aa-button"),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar124",",",2),window.eventData={},window.s_objectID=window.s_objectID?window.s_objectID:"test"}},trackerProperties:{events:[{name:"event204"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"AA Button Click",linkType:"o"}}]},{id:"RLc4f76f1fe6ac4e5a98da82ece8b5a5ad",name:"Event - Search Results Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"searchResultsClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar15",type:"value",value:"%Event - Search Results Click Position%"}],events:[{name:"event37"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"search results click",linkType:"o"}}]},{id:"RLc8949324f45247198af8e61a641cba4a",name:"Event - Search Start",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"searchStart"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){window.eventData&&eventData.search&&eventData.search.type&&(t.prop6=window.eventData.search.type,t.linkTrackVars=t.apl(t.linkTrackVars,"prop6",",",2),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar2",",",2))}},trackerProperties:{events:[{name:"event211"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"search start",linkType:"o"}}]},{id:"RLcadd7f871cfe49f6b811cf8273e6b8f3",name:"Event - Cart Remove Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"cartRemove"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"scRemove"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"cart remove",linkType:"o"}}]},{id:"RLcba7bd4fbd714ec99f80a67867e767a9",name:"Event - Copy to Clipboard",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"copyToClipboard"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event210"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"copy to clipboard",linkType:"o"}}]},{id:"RLda8989e564ce49f8aa3213141c3d11ee",name:"Event - Button Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"buttonClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){t.eVar124=_satellite.getVar("Event - Button Type"),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar124",",",2)}},trackerProperties:{events:[{name:"event204"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"button click",linkType:"o"}}]},{id:"RLdcc45fca40ac48cea475aa04d82780da",name:"Event - Saved Alert",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"saveAlert"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{eVars:[{name:"eVar97",type:"value",value:"%Event - Alert Type%"},{name:"eVar1",type:"value",value:"%Search - Criteria%"}],props:[{name:"prop21",type:"value",value:"%Search - Criteria%"}],events:[{name:"event9"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"saved search",linkType:"o"}}]},{id:"RLeb3ca01b5f884168b194bd13e59aaa72",name:"Event - pageLoad",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"pageLoad"},ruleOrder:100}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){pageDataTracker.mapAdobeVars(t)}},trackerProperties:{}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"page"}}]},{id:"RLec9b554c217140fab183f1950d240428",name:"Event - Navigation Link Click",events:[{modulePath:"core/src/lib/events/click.js",settings:{elementSelector:"a[data-nav-name], a[data-sc-nav-name]",bubbleFireIfChildFired:!1},ruleOrder:50}],conditions:[],actions:[{modulePath:"core/src/lib/actions/customCode.js",settings:{source:"https://assets.adobedtm.com/4a848ae9611a/032db4f73473/93571d51095c/RCa5da170839744983875a4aa919b6b78c-source.min.js",language:"javascript",isExternal:!0}}]},{id:"RLeca77f554d4d41549e532937624d5b6d",name:"Event - Promo Link Click",events:[{modulePath:"core/src/lib/events/click.js",settings:{elementSelector:"a[data-sc-promo-id]",bubbleFireIfChildFired:!1},ruleOrder:50}],conditions:[],actions:[{modulePath:"core/src/lib/actions/customCode.js",settings:{source:"https://assets.adobedtm.com/4a848ae9611a/032db4f73473/93571d51095c/RCa83ca2e2d2ff4507b907a0f4dd9e75f3-source.min.js",language:"javascript",isExternal:!0}}]},{id:"RLf417d413667d4c0cadefdda8d0cfd2fd",name:"Event - GenAI Content Updated",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"genAIContentUpdated"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){if(eventData&&eventData.genAI){var a=eventData.genAI;a.resultWordCount&&(t.linkTrackEvents=t.apl(t.linkTrackEvents,"event340",",",1),t.linkTrackEvents=t.apl(t.linkTrackEvents,"event341",",",1),t.events=t.apl(t.events,"event340="+a.resultWordCount,",",1),t.events=t.apl(t.events,"event341",",",1)),a.citationsCount&&(t.linkTrackEvents=t.apl(t.linkTrackEvents,"event342",",",1),t.linkTrackEvents=t.apl(t.linkTrackEvents,"event343",",",1),t.events=t.apl(t.events,"event342="+a.citationsCount,",",1),t.events=t.apl(t.events,"event343",",",1)),a.refinementsOfferedCount&&(t.linkTrackEvents=t.apl(t.linkTrackEvents,"event344",",",1),t.linkTrackEvents=t.apl(t.linkTrackEvents,"event345",",",1),t.events=t.apl(t.events,"event344="+a.refinementsOfferedCount,",",1),t.events=t.apl(t.events,"event345",",",1))}}},trackerProperties:{eVars:[{name:"eVar57",type:"value",value:"%Event - GenAI Input%"},{name:"eVar58",type:"value",value:"%Event - GenAI Details%"}],props:[{name:"prop45",type:"value",value:"%Event - GenAI Answer Details%"}],events:[{name:"event346"},{name:"event51"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"genai content updated",linkType:"o"}}]},{id:"RLf4bb246ac03c40dbb5aadc509a1cde11",name:"Event - Cart Add Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"cartAdd"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"scAdd"},{name:"scOpen"},{name:"event20"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"cart add",linkType:"o"}}]},{id:"RLf69d32ab56cf4cf985d095086dd307fd",name:"Event - Remove From History",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"removeFromHistory"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event242"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"remove from history",linkType:"o"}}]},{id:"RLf71763f7b5aa4cda9cc9fa8868cbdc43",name:"Event - Logout Click",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"logoutClick"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{trackerProperties:{events:[{name:"event180"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"logout click",linkType:"o"}}]},{id:"RLf88965ff231d4ae9873a714d94b0695d",name:"Event - Form View",events:[{modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"formView"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"form submit",linkType:"o"}}]},{id:"RLfaddb90703a848cc9518d2a88403405a",name:"Event - Auto Suggest Term Clicked",events:[{
modulePath:"core/src/lib/events/directCall.js",settings:{identifier:"autoSuggestTermClicked"},ruleOrder:50}],conditions:[],actions:[{modulePath:"adobe-analytics/src/lib/actions/setVariables.js",settings:{customSetup:{source:function(e,t){window.eventData&&eventData.search&&(t.eVar27="letterct:"+(eventData.search.suggestedLetterCount||"none")+"|resultct:"+(eventData.search.suggestedResultCount||"none")+"|clickpos:"+(eventData.search.suggestedClickPosition||"none"),t.eVar157=eventData.search.typedTerm||"",t.eVar156=eventData.search.selectedTerm||"",t.eVar162=eventData.search.autoSuggestCategory||"",t.eVar163=eventData.search.autoSuggestDetails||"",t.linkTrackVars=t.apl(t.linkTrackVars,"eVar27",",",2),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar157",",",2),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar156",",",2),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar162",",",2),t.linkTrackVars=t.apl(t.linkTrackVars,"eVar163",",",2))}},trackerProperties:{events:[{name:"event233"}]}}},{modulePath:"adobe-analytics/src/lib/actions/sendBeacon.js",settings:{type:"link",linkName:"auto suggest term clicked",linkType:"o"}}]}]};var _satellite=function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function t(e){return"[object Object]"===Object.prototype.toString.call(e)}function a(e){var a,n;return!1!==t(e)&&(void 0===(a=e.constructor)||!1!==t(n=a.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf"))}function n(e){return"string"==typeof e&&-1!==e.indexOf("[")&&-1!==e.indexOf("]")}function r(e){return e.substr(0,e.indexOf("["))}function i(e,t,a){if(e.length&&dt(t)){var s=e[0];if(1!==e.length){var o=e.slice(1);if(!n(s))return i(o,t[s],a);var c=t[s=r(s)];Array.isArray(c)&&c.forEach((function(e){return i(o,e,a)}))}else t.hasOwnProperty(s)&&"string"==typeof t[s]&&(t[s]=a(t[s]))}}if(window.atob){var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o=document,c=Object.assign,l=window,u=l,d=function(e,t,a,n){var r,i=Boolean(t&&Array.isArray(a)),s=Boolean(i&&e),o=document.createElement("a");if(i){var c=function(){var e=new Error("Unable to find the Library Embed Code for Dynamic Host Resolution.");throw e.code="dynamic_host_resolver_constructor_error",e};if(e&&(/^((https?:)?\/\/).+/.test(e)||c(),/^\/\/.+/.test(e)?o.href=u.location.protocol+e:o.href=e),o.hostname||c(),-1===a.indexOf(o.hostname)){var l=new Error("This library is not authorized for this domain. Please contact your CSM for more information.");throw l.code="dynamic_host_not_allowed",l}}var d=function(){if(null!=r)return r;if(s){var e=o.host;/:80$/.test(e)?e=e.replace(":80",""):/:80\/$/.test(e)?e=e.replace(":80/",""):/:443$/.test(e)?e=e.replace(":443",""):/:443\/$/.test(e)&&(e=e.replace(":443/","")),r=o.protocol+"//"+e}else r="";return r},p=function(e){return s&&"string"==typeof e?[d(),"/"===e.charAt(0)?e.slice(1):e].join("/"):e},g={getTurbineHost:d,decorateWithDynamicHost:p,get isDynamicEnforced(){return i}};return u&&n.onDebugChanged((function(e){e?u.dynamicHostResolver=g:delete u.dynamicHostResolver})),g},p=function(e){var t=[];return e.forEach((function(e){e.events&&e.events.forEach((function(a){t.push({rule:e,event:a})}))})),t.sort((function(e,t){return e.event.ruleOrder-t.event.ruleOrder}))},g="debug",f=function(e,t){var a=function(){return"true"===e.getItem(g)},n=function(t){e.setItem(g,t)},r=[],i=function(e){r.push(e)};return t.outputEnabled=a(),{onDebugChanged:i,getDebugEnabled:a,setDebugEnabled:function(e){a()!==e&&(n(e),t.outputEnabled=e,r.forEach((function(t){t(e)})))}}},m="Module did not export a function.",v=function(e,t,a){return function(n,r,i){i=i||[];var s=e.getModuleExports(n.modulePath);if("function"!=typeof s)throw new Error(m);var o=e.getModuleDefinition(n.modulePath),c=n.settings||{};!n.hasTransformedFilePaths&&o.filePaths&&(a(c,o.filePaths,n.modulePath),n.hasTransformedFilePaths=!0);var l=t(c,r);return s.bind(null,l).apply(null,i)}},h=function(e){return"string"==typeof e?e.replace(/\s+/g," ").trim():e},b={LOG:"log",INFO:"info",DEBUG:"debug",WARN:"warn",ERROR:"error"},k="\ud83d\ude80",y=10===parseInt((/msie (\d+)/.exec(navigator.userAgent.toLowerCase())||[])[1])?"[Launch]":k,D=!1,C=function(e){if(D&&window.console){var t=Array.prototype.slice.call(arguments,1);t.unshift(y),e!==b.DEBUG||window.console[e]||(e=b.INFO),window.console[e].apply(window.console,t)}},w=C.bind(null,b.LOG),T=C.bind(null,b.INFO),S=C.bind(null,b.DEBUG),P=C.bind(null,b.WARN),V=C.bind(null,b.ERROR),_=function(){var e=D;D=!0,C.apply(null,Array.prototype.concat(b.WARN,Array.prototype.slice.call(arguments))),e||(D=!1)},E={log:w,info:T,debug:S,warn:P,error:V,deprecation:_,get outputEnabled(){return D},set outputEnabled(e){D=e},createPrefixedLogger:function(e){var t="["+e+"]";return{log:w.bind(null,t),info:T.bind(null,t),debug:S.bind(null,t),warn:P.bind(null,t),error:V.bind(null,t)}}},I=l,A="com.adobe.reactor.",O=function(e,t){var a=A+(t||"");return{getItem:function(t){try{return I[e].getItem(a+t)}catch(e){return null}},setItem:function(t,n){try{return I[e].setItem(a+t,n),!0}catch(e){return!1}}}},L=O,j="dataElements.",x=L("sessionStorage",j),M=L("localStorage",j),N={PAGEVIEW:"pageview",SESSION:"session",VISITOR:"visitor"},R={},F=function(e){var t;try{t=JSON.stringify(e)}catch(e){}return t},U=h,B=E,H={setValue:function(e,t,a){var n;switch(t){case N.PAGEVIEW:return void(R[e]=a);case N.SESSION:return void((n=F(a))&&x.setItem(e,n));case N.VISITOR:return void((n=F(a))&&M.setItem(e,n))}},getValue:function(e,t){var a;switch(t){case N.PAGEVIEW:return R.hasOwnProperty(e)?R[e]:null;case N.SESSION:return null===(a=x.getItem(e))?a:JSON.parse(a);case N.VISITOR:return null===(a=M.getItem(e))?a:JSON.parse(a)}}},W=function(e,t,a,n){return"Failed to execute data element module "+e.modulePath+" for data element "+t+". "+a+(n?"\n"+n:"")},Y=function(e,t,a,n,r){return function(i,s){var o=t(i);if(!o)return n?"":void 0;var c,l,u=o.storageDuration;try{c=e.getModuleExports(o.modulePath),l=e.getModuleDefinition(o.modulePath)}catch(e){return void B.error(W(o,i,e.message,e.stack))}if("function"==typeof c){var d,p=o.settings||{};!o.hasTransformedFilePaths&&l.filePaths&&(r(p,l.filePaths,o.modulePath),o.hasTransformedFilePaths=!0);try{d=c(a(p,s),s)}catch(e){return void B.error(W(o,i,e.message,e.stack))}return u&&(null!=d?H.setValue(i,u,d):d=H.getValue(i,u)),null==d&&null!=o.defaultValue&&(d=o.defaultValue),"string"==typeof d&&(o.cleanText&&(d=U(d)),o.forceLowerCase&&(d=d.toLowerCase())),d}B.error(W(o,i,"Module did not export a function."))}},G=h,X={text:function(e){return e.textContent},cleanText:function(e){return G(e.textContent)}},q=function(e,t,a){for(var n,r=e,i=0,s=t.length;i<s;i++){if(null==r)return;var o=t[i];if(a&&"@"===o.charAt(0)){var c=o.slice(1);r=X[c](r)}else if(r.getAttribute&&(n=o.match(/^getAttribute\((.+)\)$/))){var l=n[1];r=r.getAttribute(l)}else r=r[o]}return r},z=function(e,t,a){return function(n,r){var i;if(t(n))i=a(n,r);else{var s=n.split("."),o=s.shift();"this"===o?r&&(i=q(r.element,s,!0)):"event"===o?r&&(i=q(r,s)):"target"===o?r&&(i=q(r.target,s)):i=q(e[o],s)}return i}},J=function(e,t){return function(a){var n=a.split(".")[0];return Boolean(t(a)||"this"===n||"event"===n||"target"===n||e.hasOwnProperty(n))}},K=function(e,t,a){var n={exports:{}};return e.call(n.exports,n,n.exports,t,a),n.exports},$=K,Q=E,Z=function(){var e={},t=function(t){var a=e[t];if(!a)throw new Error("Module "+t+" not found.");return a},a=function(){Object.keys(e).forEach((function(e){try{n(e)}catch(a){var t="Error initializing module "+e+". "+a.message+(a.stack?"\n"+a.stack:"");Q.error(t)}}))},n=function(e){var a=t(e);return a.hasOwnProperty("exports")||(a.exports=$(a.definition.script,a.require,a.turbine)),a.exports};return{registerModule:function(t,a,n,r,i){var s={definition:a,extensionName:n,require:r,turbine:i};s.require=r,e[t]=s},hydrateCache:a,getModuleExports:n,getModuleDefinition:function(e){return t(e).definition},getModuleExtensionName:function(e){return t(e).extensionName}}},ee=E,te=!1,ae=function(e){return function(t,a){var n=e._monitors;n&&(te||(ee.warn("The _satellite._monitors API may change at any time and should only be used for debugging."),te=!0),n.forEach((function(e){e[t]&&e[t](a)})))}},ne=E,re=function(e,t,a){var n,r,i,s,o=[],c=function(n,r,i){if(!e(r))return n;o.push(r);var s=t(r,i);return o.pop(),null==s&&a?"":s};return n=function(e,t){var a=/^%([^%]+)%$/.exec(e);return a?c(e,a[1],t):e.replace(/%(.+?)%/g,(function(e,a){return c(e,a,t)}))},r=function(e,t){for(var a={},n=Object.keys(e),r=0;r<n.length;r++){var i=n[r],o=e[i];a[i]=s(o,t)}return a},i=function(e,t){for(var a=[],n=0,r=e.length;n<r;n++)a.push(s(e[n],t));return a},s=function(e,t){return"string"==typeof e?n(e,t):Array.isArray(e)?i(e,t):"object"==typeof e&&null!==e?r(e,t):e},function(e,t){return o.length>10?(ne.error("Data element circular reference detected: "+o.join(" -> ")),e):s(e,t)}},ie=function(e){return function(){if("string"==typeof arguments[0])e[arguments[0]]=arguments[1];else if(arguments[0]){var t=arguments[0];for(var a in t)e[a]=t[a]}}},se="undefined"!=typeof window&&window.Promise||void 0!==s&&s.Promise,oe=se,ce=function(e,t,a){return function(n,r,i,s){return s.then((function(){var s,o=n.delayNext;return new oe((function(t,a){var r=e(n,i,[i]);if(!o)return t();var c=n.timeout,l=new oe((function(e,t){s=setTimeout((function(){t(new Error("A timeout occurred because the action took longer than "+c/1e3+" seconds to complete. "))}),c)}));oe.race([r,l]).then(t,a)})).catch((function(e){return clearTimeout(s),e=t(e),a(n,r,e),oe.reject(e)})).then((function(){clearTimeout(s)}))}))}},le=se,ue=function(e,t,a,n,r){return function(i,s,o,c){return c.then((function(){var c;return new le((function(t,a){var n=e(i,o,[o]),r=i.timeout,s=new le((function(e,t){c=setTimeout((function(){t(new Error("A timeout occurred because the condition took longer than "+r/1e3+" seconds to complete. "))}),r)}));le.race([n,s]).then(t,a)})).catch((function(e){return clearTimeout(c),e=t(e),n(i,s,e),le.reject(e)})).then((function(e){if(clearTimeout(c),!a(i,e))return r(i,s),le.reject()}))}))}},de=se.resolve(),pe=function(e,t,a){return function(n,r){return n.conditions&&n.conditions.forEach((function(t){de=e(t,n,r,de)})),n.actions&&n.actions.forEach((function(e){de=t(e,n,r,de)})),de=(de=de.then((function(){a(n)}))).catch((function(){}))}},ge=function(e){return Boolean(e&&"object"==typeof e&&"function"==typeof e.then)},fe=function(e,t,a,n){return function(r,i){var s;if(r.conditions)for(var o=0;o<r.conditions.length;o++){s=r.conditions[o];try{var c=e(s,i,[i]);if(ge(c))throw new Error("Rule component sequencing must be enabled on the property for this condition to function properly.");if(!t(s,c))return a(s,r),!1}catch(e){return n(s,r,e),!1}}return!0}},me=function(e,t){return function(a,n){e(a,n)&&t(a,n)}},ve=function(e){return function(t){var a=e.getModuleDefinition(t.modulePath);return a&&a.displayName||t.modulePath}},he=function(e){return function(t){var a=t.rule,n=t.event,r=e.getModuleDefinition(n.modulePath).name;return{$type:e.getModuleExtensionName(n.modulePath)+"."+r,$rule:{id:a.id,name:a.name}}}},be=function(e,t,a,n,r,i){return function(s,o){var c=o.rule,l=o.event;l.settings=l.settings||{};try{var u=r(o);t(l,null,[function(t){var n=a(u,t);s((function(){e(n,c)}))}])}catch(e){i.error(n(l,c,e))}}},ke=function(e,t,a,n){return function(r,i,s){var o=t(r);a.error(e(o,i.name,s)),n("ruleActionFailed",{rule:i,action:r})}},ye=function(e,t,a,n){return function(r,i,s){var o=t(r);a.error(e(o,i.name,s)),n("ruleConditionFailed",{rule:i,condition:r})}},De=function(e,t,a){return function(n,r){var i=e(n);t.log('Condition "'+i+'" for rule "'+r.name+'" was not met.'),a("ruleConditionFailed",{rule:r,condition:n})}},Ce=function(e,t){return function(a){e.log('Rule "'+a.name+'" fired.'),t("ruleCompleted",{rule:a})}},we=function(e,t,a){return function(n,r){var i;if(n.actions)for(var s=0;s<n.actions.length;s++){i=n.actions[s];try{e(i,r,[r])}catch(e){return void t(i,n,e)}}a(n)}},Te=function(e,t,a,n){return function(r,i){n("ruleTriggered",{rule:i}),e?a(i,r):t(i,r)}},Se=function(e,t,a){return'Failed to execute "'+e+'" for "'+t+'" rule. '+a.message+(a.stack?"\n"+a.stack:"")},Pe=function(e,t){return t&&!e.negate||!t&&e.negate},Ve=[],_e=!1,Ee=function(e){_e?e():Ve.push(e)},Ie=function(e,t,a){e(t).forEach((function(e){a(Ee,e)})),_e=!0,Ve.forEach((function(e){e()})),Ve=[]},Ae=function(e){if(e||(e=new Error("The extension triggered an error, but no error information was provided.")),!(e instanceof Error)){var t="object"==typeof e?JSON.stringify(e):String(e);e=new Error(t)}return e},Oe={};Object.defineProperty(Oe,"__esModule",{value:!0}),Oe.isPlainObject=a;var Le,je=E,xe=c,{isPlainObject:Me}=Oe,Ne=function(e,t){return Me(t=t||{})?t=xe({},t,e):xe(t,e),t.hasOwnProperty("type")||Object.defineProperty(t,"type",{get:function(){return je.deprecation("Accessing event.type in Adobe Launch has been deprecated and will be removed soon. Please use event.$type instead."),t.$type}}),t},Re=function(e,t){return function(a,n){var r=e[a];if(r){var i=r.modules;if(i)for(var s=Object.keys(i),o=0;o<s.length;o++){var c=s[o],l=i[c];if(l.shared&&l.name===n)return t.getModuleExports(c)}}}},Fe=function(e,t){return function(){return t?e(t):{}}},Ue=function(e,t,a){return function(n){if(a){var r=n.split(".");r.splice(r.length-1||1,0,"min"),n=r.join(".")}return e(t)+n}},Be=".js",He=function(e){return e.substr(0,e.lastIndexOf("/"))},We=function(e,t){return-1!==e.indexOf(t,e.length-t.length)},Ye=function(e,t){We(t,Be)||(t+=Be);var a=t.split("/"),n=He(e).split("/");return a.forEach((function(e){e&&"."!==e&&(".."===e?n.length&&n.pop():n.push(e))})),n.join("/")},Ge={exports:{}};Le=function(){function e(){for(var e=0,t={};e<arguments.length;e++){var a=arguments[e];for(var n in a)t[n]=a[n]}return t}function t(e){return e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent)}function a(n){function r(){}function i(t,a,i){if("undefined"!=typeof document){"number"==typeof(i=e({path:"/"},r.defaults,i)).expires&&(i.expires=new Date(1*new Date+864e5*i.expires)),i.expires=i.expires?i.expires.toUTCString():"";try{var s=JSON.stringify(a);/^[\{\[]/.test(s)&&(a=s)}catch(e){}a=n.write?n.write(a,t):encodeURIComponent(String(a)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),t=encodeURIComponent(String(t)).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/[\(\)]/g,escape);var o="";for(var c in i)i[c]&&(o+="; "+c,!0!==i[c]&&(o+="="+i[c].split(";")[0]));return document.cookie=t+"="+a+o}}function s(e,a){if("undefined"!=typeof document){for(var r={},i=document.cookie?document.cookie.split("; "):[],s=0;s<i.length;s++){var o=i[s].split("="),c=o.slice(1).join("=");a||'"'!==c.charAt(0)||(c=c.slice(1,-1));try{var l=t(o[0]);if(c=(n.read||n)(c,l)||t(c),a)try{c=JSON.parse(c)}catch(e){}if(r[l]=c,e===l)break}catch(e){}}return e?r[e]:r}}return r.set=i,r.get=function(e){return s(e,!1)},r.getJSON=function(e){return s(e,!0)},r.remove=function(t,a){i(t,"",e(a,{expires:-1}))},r.defaults={},r.withConverter=a,r}return a((function(){}))},Ge.exports=Le();var Xe=Ge.exports,qe={get:Xe.get,set:Xe.set,remove:Xe.remove},ze=se,Je=function(e,t){return new ze((function(a,n){t.onload=function(){a(t)},t.onerror=function(){n(new Error("Failed to load script "+e))}}))},Ke=function(e){var t={};if(!e||"string"!=typeof e)return t;var a=e.trim().replace(/^[?#&]/,""),n=new URLSearchParams(a),r=n.keys();do{var i=r.next(),s=i.value;if(s){var o=n.getAll(s);1===o.length?t[s]=o[0]:t[s]=o}}while(!1===i.done);return t},$e=function(e){var t="{{space}}",a=new URLSearchParams;return Object.keys(e).forEach((function(n){var r=e[n];"string"==typeof e[n]?r=r.replace(/ /g,t):["object","undefined"].includes(typeof r)&&!Array.isArray(r)&&(r=""),Array.isArray(r)?r.forEach((function(e){a.append(n,e)})):a.append(n,r)})),a.toString().replace(new RegExp(encodeURIComponent(t),"g"),"%20")},Qe="@adobe/reactor-",Ze={cookie:qe,document:o,"load-script":function(e){var t=document.createElement("script");t.src=e,t.async=!0;var a=Je(e,t);return document.getElementsByTagName("head")[0].appendChild(t),a},"object-assign":c,promise:se,"query-string":{parse:function(e){return Ke(e)},stringify:function(e){return $e(e)}},window:l},et=function(e){return function(t){if(0===t.indexOf(Qe)){var a=t.substr(Qe.length),n=Ze[a];if(n)return n}if(0===t.indexOf("./")||0===t.indexOf("../"))return e(t);throw new Error('Cannot resolve module "'+t+'".')}},tt=Re,at=Fe,nt=Ue,rt=E,it=Ye,st=et,ot=function(e,t,a,n,r,i,s){var o=e.extensions,c=e.buildInfo,l=e.environment,u=e.property.settings;if(o){var d=tt(o,t);Object.keys(o).forEach((function(p){var g=o[p],f=g.settings;Array.isArray(g.filePaths)&&(f=i(f,g.filePaths));var m=at(n,f);if(g.modules){var v=rt.createPrefixedLogger(g.displayName),h=nt(s,g.hostedLibFilesBaseUrl,c.minified),b={buildInfo:c,environment:l,property:{name:e.property.name,id:e.property.id},getDataElementValue:r,getExtensionSettings:m,getHostedLibFileUrl:h,getSharedModule:d,logger:v,propertySettings:u,replaceTokens:n,onDebugChanged:a.onDebugChanged,get debugEnabled(){return a.getDebugEnabled()}};Object.keys(g.modules).forEach((function(e){var a=g.modules[e],n=st((function(a){var n=it(e,a);return t.getModuleExports(n)}));t.registerModule(e,a,p,n,b)}))}})),t.hydrateCache()}return t},ct=qe,lt=E,ut=function(e,t,a,n,r){var i=lt.createPrefixedLogger("Custom Script");e.track=function(e){lt.log('"'+e+'" does not match any direct call identifiers.')},e.getVisitorId=function(){return null},e.property={name:t.property.name,id:t.property.id},e.company=t.company,e.buildInfo=t.buildInfo,e.environment=t.environment,e.logger=i,e.notify=function(e,t){switch(lt.deprecation("_satellite.notify is deprecated. Please use the `_satellite.logger` API."),t){case 3:i.info(e);break;case 4:i.warn(e);break;case 5:i.error(e);break;default:i.log(e)}},e.getVar=n,e.setVar=r,e.setCookie=function(e,t,a){var n="",r={};a&&(n=", { expires: "+a+" }",r.expires=a);var i='_satellite.setCookie is deprecated. Please use _satellite.cookie.set("'+e+'", "'+t+'"'+n+").";lt.deprecation(i),ct.set(e,t,r)},e.readCookie=function(e){return lt.deprecation('_satellite.readCookie is deprecated. Please use _satellite.cookie.get("'+e+'").'),ct.get(e)},e.removeCookie=function(e){lt.deprecation('_satellite.removeCookie is deprecated. Please use _satellite.cookie.remove("'+e+'").'),ct.remove(e)},e.cookie=ct,e.pageBottom=function(){},e.setDebug=a;var s=!1;Object.defineProperty(e,"_container",{get:function(){return s||(lt.warn("_satellite._container may change at any time and should only be used for debugging."),s=!0),t}})},{isPlainObject:dt}=Oe,pt=o,gt=c,ft=d,mt=p,vt=f,ht=v,bt=Y,kt=z,yt=J,Dt=Z,Ct=ae,wt=re,Tt=ie,St=ce,Pt=ue,Vt=pe,_t=fe,Et=me,It=ve,At=he,Ot=be,Lt=ke,jt=ye,xt=De,Mt=Ce,Nt=we,Rt=Te,Ft=Se,Ut=Pe,Bt=Ie,Ht=Ae,Wt=Ne,Yt=O,Gt=ot,Xt=ut,qt=function(e,t){return function(a,n,r){return e&&dt(a)&&Object.keys(a).length&&Array.isArray(n)&&n.length?(n.forEach((function(e){Boolean(null!=r&&/^core\/.*actions.*\/customCode\.js$/.test(r))&&"source"===e&&!a.isExternal||i(e.split("."),a,t)})),a):a}},zt=E,Jt=window._satellite;if(Jt&&!window.__satelliteLoaded){window.__satelliteLoaded=!0;var Kt=Jt.container;delete Jt.container;var $t=gt({},Kt.buildInfo);Object.defineProperty($t,"environment",{get:function(){return zt.deprecation("container.buildInfo.environment is deprecated.Please use `container.environment.stage` instead"),Kt.environment.stage}}),Kt.buildInfo=$t;var Qt,Zt=vt(Yt("localStorage"),zt),ea="";pt.currentScript&&pt.currentScript.getAttribute("src")&&(ea=pt.currentScript.getAttribute("src"));try{Qt=ft(ea,Boolean(Kt.company.dynamicCdnEnabled),Kt.company.cdnAllowList,Zt)}catch(e){throw zt.warn("Please review the following error:"),e}var ta,aa=qt(Qt.isDynamicEnforced,Qt.decorateWithDynamicHost),na=Dt(),ra=Kt.property.settings.undefinedVarsReturnEmpty,ia=Kt.property.settings.ruleComponentSequencingEnabled,sa=Kt.dataElements||{},oa=function(e){return sa[e]},ca=function(){return ta.apply(null,arguments)},la=bt(na,oa,ca,ra,aa),ua={},da=Tt(ua),pa=yt(ua,oa),ga=kt(ua,oa,la);ta=wt(pa,ga,ra),Xt(Jt,Kt,Zt.setDebugEnabled,ga,da),Gt(Kt,na,Zt,ta,la,aa,Qt.decorateWithDynamicHost);var fa=Ct(Jt),ma=ht(na,ta,aa),va=It(na),ha=xt(va,zt,fa),ba=jt(Ft,va,zt,fa),ka=Lt(Ft,va,zt,fa),ya=Mt(zt,fa),Da=Ot(Rt(ia,Et(_t(ma,Ut,ha,ba),Nt(ma,ka,ya)),Vt(Pt(ma,Ht,Ut,ba,ha),St(ma,Ht,ka),ya),fa),ma,Wt,Ft,At(na),zt);Bt(mt,Kt.rules||[],Da)}return e(Jt)}console.warn("Adobe Launch is unsupported in IE 9 and below.")}();