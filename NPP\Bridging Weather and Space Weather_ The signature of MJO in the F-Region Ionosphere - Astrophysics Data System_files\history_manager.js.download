define(["js/components/generic_module","js/mixins/dependon","js/mixins/hardened","js/components/pubsub_key"],function(e,t,i,n){e=e.extend({initialize:function(){this._history=[]},activate:function(e){this.setBeeHive(e);e=this.getPubSub();e.subscribe(e.NAVIGATE,_.bind(this.recordNav,this))},recordNav:function(){this._history.push([].slice.apply(arguments))},getCurrentNav:function(){return this._history[this._history.length-1]},getPreviousNav:function(){return this._history[this._history.length-2]},hardenedInterface:{getPreviousNav:"",getCurrentNav:""}});return _.extend(e.prototype,t.BeeHive,i),e});
//# sourceMappingURL=history_manager.js.map