function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var r;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(r="Object"===(r={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}define(["js/components/generic_module","js/mixins/dependon","js/mixins/hardened","js/components/api_feedback","hotkeys"],function(e,t,r,n,o){function a(e){return(Array.isArray(e)?e:[e]).map(function(e){return"".concat("alt+shift","+").concat(e)}).join(", ")}var i=[{hotkey:a("a"),event:"search",description:"Focus on search bar"},{hotkey:a("left"),event:"prev",description:"Previous results page"},{hotkey:a("right"),event:"next",description:"Next results page"},{hotkey:a("down"),event:"item-next",description:"Focus on next result entry"},{hotkey:a("up"),event:"item-prev",description:"Focus on previous result entry"},{hotkey:a("s"),event:"item-select",description:"Select currently focused result entry"},{hotkey:a("`"),event:"show-help",description:"Show help dialog"}],e=e.extend({initialize:function(){},createEvent:function(t){var r=this.getPubSub();return function(e){r.publish(r.CUSTOM_EVENT,t,e)}},activate:function(e){var r=this,e=(this.setBeeHive(e),i.forEach(function(e){var t=e.hotkey;o(t,r.createEvent("hotkey/".concat(e.event)))}),this.getPubSub());e.subscribe(e.CUSTOM_EVENT,function(e){"hotkey/show-help"===e&&r.showHelpModal()})},getHotkeys:function(){return i},showHelpModal:function(){var e=this.getPubSub();e.publish(e.FEEDBACK,new n({code:n.CODES.ALERT,msg:this.getModalMessage(),title:"Hotkeys",modal:!0}))},getModalMessage:function(){return['<dl style="display: flex; flex-flow: row wrap;">'].concat(_toConsumableArray(i.map(function(e){var t=e.hotkey,e=e.description,t=t.split(", ").map(function(e){return"<code>".concat(e,"</code>")}).join(", ");return'\n            <dt style="flex-basis: 40%; text-align: right; padding-right: 10px">'.concat(t,'</dt>\n            <dd style="flex-basis: 60%">').concat(e,"</dd>\n          ")})),["</dl>"]).join("")},hardenedInterface:{}});return _.extend(e.prototype,t.BeeHive,r),e});
//# sourceMappingURL=hotkeys_controller.js.map