define(["underscore","js/components/persistent_storage"],function(e,t){var n=t.extend({activate:function(e){},onAppBootstrapped:function(){var e=this.getBeeHive().getObject("DynamicConfig");if(!e)throw new Error("DynamicConfig is missing");e=e.namespace;e||(console.warn("Application namespace not set; persistent storage will be created without it"),e=""),this._store=this._createStore(e)}});return function(){return new n({name:"storage-service"})}});
//# sourceMappingURL=storage.js.map