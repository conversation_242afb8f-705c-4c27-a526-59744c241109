define(["underscore","backbone","js/components/api_request","js/components/api_targets","js/components/generic_module","js/mixins/dependon","js/mixins/hardened","js/components/api_feedback","js/mixins/api_access"],function(u,e,c,d,t,s,n,i,o){var r=e.Model.extend({defaults:function(){return{isOrcidModeOn:!1,perPage:void 0}}}),a=e.Model.extend({defaults:function(){return{user:void 0,link_server:void 0,minAuthorPerResult:4,externalLinkAction:"Open in new tab",defaultDatabase:[{name:"Physics",value:!1},{name:"Astronomy",value:!1},{name:"General",value:!1}],defaultExportFormat:"BibTeX",defaultHideSidebars:"Show",customFormats:[],recentQueries:[]}}}),e=t.extend({initialize:function(e){this.localStorageModel=new r,this.listenTo(this.localStorageModel,"change",this.broadcastUserDataChange),this.userModel=new a,this.listenTo(this.userModel,"change:user",this.broadcastUserChange),this.listenTo(this.userModel,"change",function(e){"user"!==e&&this.broadcastUserDataChange.apply(this,arguments)}),u.bindAll(this,"completeLogIn","completeLogOut"),this.base_url=this.test?"location.origin":location.origin,this.buildAdditionalParameters()},activate:function(e){this.setBeeHive(e);this.getPubSub().subscribe(this.getPubSub().APP_STARTING,function(){var t=e.getService("OrcidApi"),s=e.getObject("User");null!==t.authData&&t.checkAccessOrcidApiAccess().fail(function(e){e=e&&e.status;e&&"401"===e&&(t.signOut(),s.setOrcidMode(0))})});var t=e.getService("PersistentStorage");t&&(t=t.get("UserPreferences"))&&this.localStorageModel.set(t)},_persistModel:function(){var e=this.getBeeHive().getService("PersistentStorage");e&&e.set("UserPreferences",this.localStorageModel.toJSON())},setLocalStorage:function(e){this.localStorageModel.set(e),this._persistModel()},getLocalStorage:function(e){return this.localStorageModel.toJSON()},addToRecentQueries:function(e){try{var t=this.userModel.get("recentQueries");10<=t.length&&t.pop(),t.unshift(e.clone().toJSON()),this.userModel.set("recentQueries",t),this.setUserData({recentQueries:t})}catch(e){t=[]}return t},getRecentQueries:function(){return this.userModel.get("recentQueries")||[]},setOrcidMode:function(e){this.localStorageModel.set("isOrcidModeOn",e),this._persistModel()},isOrcidModeOn:function(){return this.localStorageModel.get("isOrcidModeOn")},broadcastUserDataChange:function(e){this.getPubSub().publish(this.getPubSub().USER_ANNOUNCEMENT,this.USER_INFO_CHANGE,e.changed)},broadcastUserChange:function(){var e=this.userModel.get("user")?this.USER_SIGNED_IN:this.USER_SIGNED_OUT;this.getPubSub().publish(this.getPubSub().USER_ANNOUNCEMENT,e,this.userModel.get("user")),this.redirectIfNecessary()},buildAdditionalParameters:function(){this.additionalParameters={}},handleFailedPOST:function(e,t,s,n){this.getPubSub();e=e.responseJSON&&e.responseJSON.error?e.responseJSON.error:e.responseText||"error unknown";console.error("POST request failed for endpoint: ["+n+"]",e)},handleFailedGET:function(e,t,s,n){this.getPubSub();e=e.responseJSON&&e.responseJSON.error?e.responseJSON.error:e.responseText||"error unknown";console.error("GET request failed for endpoint: ["+n+"]",e)},fetchData:function(e){return this.composeRequest({target:e,method:"GET"})},postData:function(e,t){return this.additionalParameters[e]&&u.extend(t,this.additionalParameters[e]),this.composeRequest({target:e,method:"POST",data:t})},putData:function(e,t){return this.additionalParameters[e]&&u.extend(t,this.additionalParameters[e]),this.composeRequest({target:e,method:"PUT",data:t})},sendRequestWithNewCSRF:function(e){e=u.bind(e,this),this.getBeeHive().getObject("CSRFManager").getCSRF().done(e)},composeRequest:function(e){var t,s=e.target,n=e.method,i=(e=e.data||{}).csrf,e=u.omit(e,"csrf"),o=d[s],r=this,a=$.Deferred();return t={target:o,options:{context:this,type:n,data:u.isEmpty(e)?void 0:JSON.stringify(e),contentType:"application/json",done:function(){a.resolve.apply(a,arguments)},fail:function(){var e="GET"==n?r.handleFailedGET:r.handleFailedPOST,t=[].slice.apply(arguments);t.push(s),e.apply(r,t),a.reject.apply(a,arguments)}}},i?this.sendRequestWithNewCSRF(function(e){t.options.headers={"X-CSRFToken":e},this.getBeeHive().getService("Api").request(new c(t))}):this.getBeeHive().getService("Api").request(new c(t)),a.promise()},redirectIfNecessary:function(){var e,t=this.getPubSub();"AuthenticationPage"===this.getBeeHive().getObject("MasterPageManager").currentChild&&this.isLoggedIn()?(e=(e=this.getBeeHive().getService("HistoryManager").getPreviousNav())||"index-page",t.publish.apply(t,[t.NAVIGATE].concat(e))):"SettingsPage"!==this.getBeeHive().getObject("MasterPageManager").currentChild||this.isLoggedIn()||t.publish(t.NAVIGATE,"authentication-page")},setUser:function(e){this.userModel.set("user",e),this.isLoggedIn()&&this.completeLogIn()},completeLogIn:function(){var t=this;this.fetchData("USER_DATA").done(function(e){t.userModel.set(e)})},completeLogOut:function(){this.userModel.clear()},deleteAccount:function(){var e=this;return this.composeRequest({target:"USER",method:"DELETE",data:{csrf:!0}}).done(function(){e.getApiAccess({reconnect:!0}).done(function(){e.completeLogOut()})})},changeEmail:function(e){var t=this,s=e.email;return e=u.extend(u.pick(e,["email","password"]),{csrf:!0}),this.postData("CHANGE_EMAIL",e).done(function(){t.getPubSub().subscribeOnce(t.getPubSub().NAVIGATE,function(){setTimeout(function(){var e="<p>Please check <b>"+s+"</b> for further instructions</p><p>(If you don't see the email, please <b>check your spam folder</b>)</p>";t.getPubSub().publish(t.getPubSub().ALERT,new i({code:0,msg:e,type:"success",title:"Success",modal:!0}))},100)}),t.getPubSub().publish(t.getPubSub().NAVIGATE,"index-page")})},changePassword:function(e){return e=u.extend(e,{csrf:!0}),this.postData("CHANGE_PASSWORD",e)},getToken:function(){return this.fetchData("TOKEN")},generateToken:function(){return this.putData("TOKEN",{csrf:!0})},getUserData:function(){return this.userModel.toJSON()},setUserData:function(e){var t=this;return this.postData("USER_DATA",e).done(function(e){t.userModel.set(e)})},getUserName:function(){return this.userModel.get("user")},isLoggedIn:function(){return!!this.userModel.get("user")},setMyADSData:function(e){return this.postData("USER_DATA",e)},getOpenURLConfig:function(){return this.getSiteConfig("link_servers")},getSiteConfig:function(e){var t=$.Deferred();e=new c({target:e?d.SITE_CONFIGURATION+"/"+e:d.SITE_CONFIGURATION,options:{type:"GET",done:function(e){t.resolve(e)},fail:function(e){t.reject(e)}}});return this.getBeeHive().getService("Api").request(e),t.promise()},hardenedInterface:{setUser:"set email into user",isLoggedIn:"whether the user is logged in",getUserName:"get the user's email before the @",setLocalStorage:"sets an object in to user's local storage",getLocalStorage:"gives you a json object for user's local storage",isOrcidModeOn:"figure out if user has Orcid mode activated",setOrcidMode:"set orcid ui on or off",getOpenURLConfig:"get list of openurl endpoints",getUserData:"myads data",setUserData:"POST user data to myads endpoint",getRecentQueries:"get the 10 most recent queries as array",addToRecentQueries:"add a query to the set of recent queries",generateToken:"PUT to token endpoint to make a new token",getToken:"GET from token endpoint",deleteAccount:"POST to delete account endpoint",changePassword:"POST to change password endpoint",changeEmail:"POST to change email endpoint",setMyADSData:"",USER_SIGNED_IN:"constant",USER_SIGNED_OUT:"constant",USER_INFO_CHANGE:"constant"}});return u.extend(e.prototype,n,s.BeeHive,s.App,o,{USER_SIGNED_IN:"user_signed_in",USER_SIGNED_OUT:"user_signed_out",USER_INFO_CHANGE:"user_info_change"}),e});
//# sourceMappingURL=user.js.map