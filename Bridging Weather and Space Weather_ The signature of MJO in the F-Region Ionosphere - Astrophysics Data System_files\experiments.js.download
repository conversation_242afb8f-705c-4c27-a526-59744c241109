define(["underscore","jquery","js/components/generic_module","js/mixins/dependon","analytics","js/components/pubsub_events"],function(e,i,n,t,s,a){n=n.extend({initialize:function(){this.isRunning=!1},activate:function(i,n){this.setApp(n),this.setBeeHive(i);n=this.getPubSub();window.gtag||(window.gtag=function(){e.isArray(window.dataLayer)&&window.dataLayer.push(arguments)},window.gtag("event","optimize.callback",{callback:function(i,n){console.log("Experiment with ID: "+n+" is on variant: "+i)}})),n.subscribe(n.APP_BOOTSTRAPPED,e.bind(this.onAppStarted,this))},subscribe:function(i,n){var e=this.getPubSub();a[i]&&e.subscribe(a[i],n)},subscribeOnce:function(i,n){var e=this.getPubSub();a[i]&&e.subscribeOnce(a[i],n)},onAppStarted:function(){this.toggleOptimize()},toggleOptimize:function(){window.dataLayer?(this.isRunning?window.dataLayer.push({event:"optimize.deactivate"}):window.dataLayer.push({event:"optimize.activate"}),this.isRunning=!this.isRunning):console.warn("Optimize is not available, we are not running any experiment")}});return e.extend(n.prototype,t.BeeHive,t.App),n});
//# sourceMappingURL=experiments.js.map