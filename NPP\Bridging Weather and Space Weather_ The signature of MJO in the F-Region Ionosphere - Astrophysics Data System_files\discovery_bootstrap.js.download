define(["underscore","backbone","js/components/api_query","js/components/api_request","js/components/pubsub_events","hbs","js/components/api_targets"],function(u,l,a,s,e,t,r){function d(){function i(e){if(window.__UNSAFE_BBB_APP_INSTANCE__)try{return window.__UNSAFE_BBB_APP_INSTANCE__.getBeeHive().getService("Navigator").router.navigate(e.data.url,{trigger:!0,replace:!1}),!1}catch(e){console.error(e.message)}else console.error("cannot find application object")}var t=new RegExp("^#?(/?("+["classic-form","paper-form","index","search","execute-query","abs","user","orcid-instructions","public-libraries"].join("|")+").*/?)?$","i"),n=u.memoize(function(e){return!!t.test(e)&&e.replace(/^\/?#\/?/,"/")}),a=!1;$(document).on("mousedown focus","a",function(e){if(l.history&&l.history.options&&l.history.options.pushState){var t=$(this);if(!t.data().dontHandle){var o=n(t.attr("href"));if(o){var r=t.attr("href");if(t.attr("href",o),e.metaKey||e.ctrlKey)a=!0;else{if(!a||"focusin"!==e.type)return a=!1,t.one("click",{url:o},i),t.one("mouseup blur",function(){t.attr("href",r)}),!1;a=!1}}}}})}return{configure:function(){var e,t,o=this.getObject("DynamicConfig");o&&(t=(e=this.getBeeHive()).getService("Api"),o.root&&(t.url=o.root+"/"+t.url,this.root=o.root),void 0!==o.debug&&(e.debug=o.debug,this.getController("QueryMediator").debug=o.debug),o.apiRoot&&(t.url=o.apiRoot),o.version&&(t.clientVersion=o.version),t.modifyRequestOptions=function(e,t){-1==r._doesntNeedCredentials.indexOf(t.get("target"))&&(e.xhrFields={withCredentials:!0})},t=e.getService("OrcidApi"),o.orcidProxy&&(t.orcidProxyUri=location.origin+o.orcidProxy),this.bootstrapUrls=o.bootstrapUrls,o.useCache)&&this.triggerMethodOnAll("activateCache")},bootstrap:function(){var t,o,r,i,e=this.getBeeHive(),n=(this.getObject("DynamicConfig"),$.Deferred()),e=e.getService("PersistentStorage").get("appConfig");return e&&e.expires_at&&e.expires_at>Math.floor(Date.now()/1e3)?n.resolve(e).promise():(t=this.getBeeHive().getService("Api"),this.bootstrapUrls?(o=this.bootstrapUrls.length,r={},i={done:function(e){o--,u.extend(r,e),o<=0&&n.resolve(r)},fail:function(){--o<=0&&n.resolve(r)},type:"GET",timeout:9999},u.each(this.bootstrapUrls,function(e){-1<e.indexOf("http")?(i.u=e,t.request(new s({query:new a,target:""}),i)):(delete i.u,t.request(new s({query:new a,target:e}),i))}),setTimeout(function(){"resolved"!==n.state()&&n.reject(new Error("Timed out while loading modules"))},1e4)):setTimeout(function(){n.resolve({})},1),n)},reload:function(e){if(!(-1<location.search.indexOf("debug")))return location.search&&-1<location.search.indexOf("bbbRedirect=1")?this.redirect(e):void(location.search=location.search?location.search+"&bbbRedirect=1":"bbbRedirect=1");console.warn("Debug stop, normally would reload to: "+e)},redirect:function(e){this.router&&(location.pathname=this.router.root+e),location.href=location.protocol+"//"+location.hostname+":"+location.port+location.pathname.substring(0,location.pathname.lastIndexOf("/"))+"/"+e},start:function(o){function e(e){throw new Error("Ooops. Check you config! There is no "+e+" component @#!")}var r=$.Deferred(),i=this,n=this.getBeeHive(),a=(n.getService("Api"),this.getObject("DynamicConfig")),s=(this.getBeeHive().getObject("AppStorage").setConfig(a),i.getBeeHive().Services.get("Navigator")),c=(s||e("services.Navigator"),i.getObject("MasterPageManager"));return c||e("objects.MasterPageManager"),c.assemble(i).done(function(){var e=$("div#body-template-container"),t=(window.__PRERENDERED&&(t=$("div#content-container"),$(t.selector,c.view.el).html(t.html())),e.html(c.view.el),i.router=new o,i.router.activate(n.getHardenedInstance()),s.start(i),s.router=i.router,-1<location.search.indexOf("pushstate=false")),e=u.defaults({pushState:!t&&void 0},a&&a.routerConf);l.history.start(e),d(),r.resolve()}),r.promise()}}});
//# sourceMappingURL=discovery_bootstrap.js.map