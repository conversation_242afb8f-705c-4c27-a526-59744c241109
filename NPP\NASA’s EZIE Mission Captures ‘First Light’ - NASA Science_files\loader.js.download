!function(){"use strict";var o;o=window.wp.hooks,window.wpParselyHooks=(0,o.createHooks)(),function(){var o=function(){var o;return null===(o=window.wpParselyHooks)||void 0===o?void 0:o.doAction("wpParselyOnLoad")},n=function(){var o;return null===(o=window.wpParselyHooks)||void 0===o?void 0:o.doAction("wpParselyOnReady")};if("object"==typeof window.PARSELY){if("function"!=typeof window.PARSELY.onload)window.PARSELY.onload=o;else{var e=window.PARSELY.onload;window.PARSELY.onload=function(){e&&e(),o()}}if("function"!=typeof window.PARSELY.onReady)window.PARSELY.onReady=n;else{var t=window.PARSELY.onReady;window.PARSELY.onReady=function(){t&&t(),n()}}}else window.PARSELY={onload:o,onReady:n};!0===window.wpParselyDisableAutotrack&&(window.PARSELY.autotrack=!1)}(),function(){window.wp.i18n;var o,n,e;!function(o){o.Minutes10="10m",o.Hour="1h",o.Hours2="2h",o.Hours4="4h",o.Hours24="24h",o.Days7="7d",o.Days30="30d"}(o||(o={})),function(o){o.Views="views",o.AvgEngaged="avg_engaged"}(n||(n={})),function(o){o.Author="author",o.Section="section",o.Tag="tag"}(e||(e={}));var t;void 0!==window.wpParselySiteId&&(null===(t=window.wpParselyHooks)||void 0===t||t.addAction("wpParselyOnLoad","wpParsely",(function(){return o=this,n=void 0,t=function(){var o,n,e,t;return function(o,n){var e,t,i,r={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(u){return function(c){if(e)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(r=0)),r;)try{if(e=1,t&&(i=2&c[0]?t.return:c[0]?t.throw||((i=t.return)&&i.call(t),0):t.next)&&!(i=i.call(t,c[1])).done)return i;switch(t=0,i&&(c=[2&c[0],i.value]),c[0]){case 0:case 1:i=c;break;case 4:return r.label++,{value:c[1],done:!1};case 5:r.label++,t=c[1],c=[0];continue;case 7:c=r.ops.pop(),r.trys.pop();continue;default:if(!((i=(i=r.trys).length>0&&i[i.length-1])||6!==c[0]&&2!==c[0])){r=0;continue}if(3===c[0]&&(!i||c[1]>i[0]&&c[1]<i[3])){r.label=c[1];break}if(6===c[0]&&r.label<i[1]){r.label=i[1],i=c;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(c);break}i[2]&&r.ops.pop(),r.trys.pop();continue}c=n.call(o,r)}catch(o){c=[6,o],t=0}finally{e=i=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}(this,(function(i){return o=null===(t=null===(e=window.PARSELY)||void 0===e?void 0:e.config)||void 0===t?void 0:t.parsely_site_uuid,window.wpParselySiteId&&o?(n="".concat("https://api.parsely.com/v2","/profile?apikey=").concat(encodeURIComponent(window.wpParselySiteId),"&uuid=").concat(encodeURIComponent(o),"&url=").concat(encodeURIComponent(window.location.href)),[2,fetch(n)]):[2]}))},new((e=void 0)||(e=Promise))((function(i,r){function a(o){try{u(t.next(o))}catch(o){r(o)}}function c(o){try{u(t.throw(o))}catch(o){r(o)}}function u(o){var n;o.done?i(o.value):(n=o.value,n instanceof e?n:new e((function(o){o(n)}))).then(a,c)}u((t=t.apply(o,n||[])).next())}));var o,n,e,t})))}()}();