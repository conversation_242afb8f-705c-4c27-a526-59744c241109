define(["underscore","js/components/generic_module","js/mixins/dependon","persist-js","module"],function(s,e,t,r,i){var n=i.config().namespace||"",i=e.extend({constructor:function(e){this._store=this.createStore(n+((e=e||{}).name||""))},createStore:function(e){return this._createStore(e)},_createStore:function(t){var t=new r.Store(t,{about:"This is bumblebee persistent storage",defer:!0}),e=t.get("#keys");if(e)try{e=JSON.parse(e),s.isObject(e)||t.set("#keys","{}")}catch(e){t.set("#keys","{}")}else t.set("#keys","{}");return t},set:function(e,t){this._check<PERSON>ey(e),s.isString(t)||(t=JSON.stringify(t)),this._store.set(e,t),this._setKey(e)},get:function(t){this._checkKey(t);t=this._store.get(t);if(!t)return t;try{return JSON.parse(t)}catch(e){return t}},remove:function(e){this._checkKey(e),this._store.remove(e),this._delKey(e)},clear:function(){for(var e in this.get("#keys"))this._store.remove(e);this._store.set("#keys","{}")},keys:function(){return JSON.parse(this._store.get("#keys"))},_setKey:function(e){var t=this.keys()||{};t[e]=1,this._store.set("#keys",JSON.stringify(t))},_delKey:function(e){var t=this.keys()||{};delete t[e],this._store.set("#keys",JSON.stringify(t))},_checkKey:function(e){if(!s.isString(e))throw new Error("key must be string, received: "+e)}});return s.extend(i.prototype,t.BeeHive),i});
//# sourceMappingURL=persistent_storage.js.map