(window.webpackJsonp=window.webpackJsonp||[]).push([[7],{307:function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return c}));var s=n(8),o=n.n(s),l=n(9),a=n.n(l),i=n(7),c=function(){return a()((function BulkDownload(){var e=this;o()(this,BulkDownload),this.classList={hidden:"hidden",error:"error",eventDisabled:"event-disabled",show:"js--show",loader:"js--loader",jsHidden:"js--hidden",aheadOfPrint:"aheadOfPrint",sectionHeader:"section__header",issueItem:"issue-item",moved:"js--moved",focusCycleWrapper:"js--focusCycleWrapper"},this.selectors={section:".bulk-download",wrappers:".bulkDownloadWrapper",modal:".bulk-download-modal",modalHeader:".modal__header",modalActiveBody:".js--show[data-wizard-box]",modalActiveFooter:".js--show[data-wizard-footer]",maxPdfElement:"[data-max-pdf]",hasPdfElement:'[data-has-pdf="true"]',bulkDownloadOption:".bulk-download-option",pdfDownloadTargetOption:'.bulk-download-option[data-target="#pdfDownload"]',bulkDownloadInput:".bulkDownloadInput",bulkWizard:".bulk-wizard",exportCitationsSubmitButton:".export-citations__submit",pdfDownloadSubmitButton:".pdfDownload__submit",exportCitationsForm:".exportCitationsForm",exportCitationInner:".exportCitationInner",focusableElements:'a, button, input:not([type="hidden"]), textarea, select, details, [tabindex]:not([tabindex="-1"])',focusOrderElement:"[data-focus-order]",bulkFocusableEl:".js--bulkFocusableEl",anchorTags:".bulk-download [href='#'],.modal__footer [href='#']",bulkCheckbox:".bulk-content input[type='checkbox']",cancelBtn:".modal__footer-left a",closeBtn:".bulk-download-modal .modal__header .close",nextBtn:".exportCitationsForm",lastOption:".exportCitationInner .bulk-content:last-child input"},this.elements={section:null,wrappers:null,modal:null,maxPdfElement:null,hasPdfElement:null,bulkDownloadOption:null,pdfDownloadTargetOption:null,bulkDownloadInput:null,bulkWizard:null,exportCitationsSubmitButton:null,pdfDownloadSubmitButton:null,exportCitationsForm:null,currentWizardBox:null,currentWizardFooter:null,exportCitationInner:null,focusableList:null,anchorTags:null,bulkCheckbox:null,cancelBtn:null,closeBtn:null,nextBtn:null},this.clearModalContent=function(){return e.elements.exportCitationInner.forEach((function(e){return e.innerHTML=""}))},this.optionKeydownHandler=function(){var t;e.elements.bulkCheckbox=document.querySelectorAll(e.selectors.bulkCheckbox),null===(t=e.elements.bulkCheckbox)||void 0===t||t.forEach((function(e){e.addEventListener("keydown",(function(t){13===t.keyCode&&(t.preventDefault(),e.click())}))}))},this.downloadOptionHandler=function(t){var n=t.currentTarget.dataset.target,s=e.elements.modal.querySelector(n);s.setAttribute("tabindex","-1");var o,l="",a="",i="",c="",r="";switch(n){case"#exportCitation":e.isPdf=!1,o=s.querySelector(".exportCitationWrapper");break;case"#pdfDownload":e.isPdf=!0,o=s.querySelector(".pdfDownloadWrapper")}e.pdfAccessNum=0;var d=o.querySelector(".bulk-content");e.elements.wrappers.length&&!d&&(e.articleNum=0,e.elements.wrappers.forEach((function(t){if(t.classList.contains(e.classList.aheadOfPrint)){Array.from(t.children).forEach((function(t){if(t.classList.contains(e.classList.sectionHeader)){var n=t.textContent;i+='<h5 class="bulk-content__header">'.concat(n,"</h5>")}else t.classList.contains(e.classList.issueItem)&&(a=e.doiRender(t.querySelector(".bulkDownloadInput"),a),i+=a,a="")}))}else{var n=t.querySelector(".section__header");l=n?'<h5 class="bulk-content__header">'.concat(n.textContent,"</h5>"):"",a="",t.querySelectorAll(".bulkDownloadInput").forEach((function(t){return a=e.doiRender(t,a)})),i+='\n                        <div class="bulk-content">\n                            '.concat(l,"\n                            ").concat(a,"\n                        </div>")}})),e.isPdf&&(e.pdfAccessNum<e.articleNum?e.articleNum<=e.maxPdfItems?(e.isPdfAccessNote=!0,r="\n                            <div>\n                                <div>You only have access to download <b>".concat(e.pdfAccessNum,"</b> of the <b>").concat(e.articleNum,"</b> ").concat(e.contentTypeText," in this list.</div>\n                            </div>")):e.articleNum>e.maxPdfItems&&(e.isPdfAccessNote=!0,r="\n                            <div>\n                                <div><b>This issue contains a large number of articles.</b> Please select up to ".concat(e.maxPdfItems," items for download.</div>\n                                <div>You only have access to download <b>").concat(e.pdfAccessNum,"</b> of the <b>").concat(e.articleNum,"</b> ").concat(e.contentTypeText," in this list.</div>\n                            </div>")):e.pdfAccessNum>=e.articleNum&&e.pdfAccessNum>e.maxPdfItems&&(e.isPdfAccessNote=!0,r="\n                            <div>\n                                <div><b>This issue contains a large number of articles.</b> Please select up to ".concat(e.maxPdfItems," items for download.</div>\n                            </div>")),e.isPdfAccessNote&&(r='\n                        <div class="pdfAccessNote">\n                            <i aria-hidden="true" class="icon-info2"></i>\n                            '.concat(r,"\n                        </div>")),e.isPdfAccessNote=!1),c+=r,c+=i,o.children.length||(o.innerHTML=c,e.focusCycle(!0)),s.querySelector(".selection-count__details").innerHTML="0 of ".concat(e.articleNum," ").concat(e.contentTypeText),e.optionKeydownHandler())},this.bulkInputHandler=function(t){if("bulkDownload"===t.target.name){var n=t.target,s=n.closest(".modal__dialog"),o=s.querySelectorAll('[name="'.concat(n.name,'"]:checked')),l=s.querySelector(".pdfMaxLimit"),a=s.querySelector(".selection-count"),i=a.querySelector(".selection-count__icon"),c=a.querySelector(".selection-count__reached"),r=a.querySelector(".selection-count__details"),d=n.closest(".pdfDownloadWrapper")?s.querySelector(".pdfDownload__submit"):s.querySelector(".exportCitationsForm");o.length>=1?(d.classList.contains(e.classList.loader)||("A"===d.tagName&&d.classList.remove(e.classList.eventDisabled),d.removeAttribute("disabled")),n.closest(".pdfDownloadWrapper")?(l&&l.parentElement.removeChild(l),o.length>e.maxPdfItems?(n.checked=!1,n.closest(".bulkDownloadCheckbox").insertAdjacentHTML("afterend",'<div class="pdfMaxLimit"><b>Item limit reached.</b> Please deselect another choice.</div>'),a.classList.add(e.classList.error),r.innerHTML="<b>".concat(o.length-1," of ").concat(e.articleNum,"</b> ").concat(e.contentTypeText)):o.length===e.maxPdfItems?(a.classList.add(e.classList.error),c.classList.remove(e.classList.hidden)):o.length<e.maxPdfItems&&(c.classList.add(e.classList.hidden),i.classList.add(e.classList.hidden),a.classList.remove(e.classList.error))):(a.classList.remove(e.classList.error),i.classList.add(e.classList.hidden))):(d.classList.contains(e.classList.loader)||("A"===d.tagName&&d.classList.add(e.classList.eventDisabled),d.setAttribute("disabled","false")),a.classList.add(e.classList.error),i.classList.remove(e.classList.hidden)),e.focusCycle(!1),r&&(r.innerHTML="<b>".concat(o.length," of ").concat(e.articleNum,"</b> ").concat(e.contentTypeText))}},this.wizardHandler=function(t){var n=t.currentTarget.dataset.wizard,s=e.elements.modal.querySelector('[data-wizard-box="'.concat(n,'"]')),o=e.elements.modal.querySelector('[data-wizard-footer="'.concat(n,'"]'));e.elements.currentWizardBox.classList.remove(e.classList.show),s.classList.add(e.classList.show),e.elements.currentWizardFooter.classList.remove(e.classList.show),o.classList.add(e.classList.show),e.elements.currentWizardBox=s,e.elements.currentWizardFooter=o,e.focusCycle(!0)},this.collectContent=function(e,t){var n,s=t.querySelector(".collectContent"),o=t.querySelector(".collectContentNum"),l=0;s.textContent="",o.textContent="",e.forEach((function(e){var t='<input type="hidden" name="doi" value="'.concat(e.value,'"/>');s.insertAdjacentHTML("beforeend",t),l++})),n=l>1?"s":"",o.innerHTML="<p>You have chosen to export <span>".concat(l,"</span> citation").concat(n,".</p>")},this.doiRender=function(t,n){var s=t.nextElementSibling.firstElementChild||t.nextElementSibling.nextElementSibling.firstElementChild,o=(null==s?void 0:s.querySelectorAll(".MathJax , math , .fallback__mathEquation").length)>0,l=(null==s?void 0:s.querySelectorAll(".smallCaps").length)>0,a=t.querySelector('[type="hidden"]'),i="true"===a.dataset.hasPdf,c=t.querySelector(".hidden").textContent.trim(),r="",d=!1,u="";switch(u=o||l?null==s?void 0:s.innerHTML:null==s?void 0:s.textContent,c){case"full":c="full access",r="full-access";break;case"free":c="free access",r="free-access";break;case"oa":c="open access",r="open-access";break;case"freeToRead":c="Free to Read",r="free-to-read-access",d=!0;break;case"limited":c="token access",r="token-access",!1,d=!0;break;default:c="",d=!0}return n+='\n            <div class="bulk-content__article">\n            '.concat(""!==c?'\n                <div class="bulk-content__access-type '.concat(r,'">\n                    <i aria-hidden="true" class="icon-icon-lock_open"></i>\n                    <span class="small">').concat(c,"</span>\n                </div>"):"",'\n                <div class="bulk-content__details">\n                    <div class="input-group bulkDownloadCheckbox').concat(!d&&i||!e.isPdf?"":" disabled-content",'">\n                        <label class="checkbox--primary">\n                            <input title="title" type="checkbox" name="bulkDownload" value="').concat(a.value,'" ').concat(!d&&i||!e.isPdf?"":"disabled",'>\n                            <span class="label-txt">\n                                <div class="bulk-content__title">').concat(u,"</div>\n                            </span>\n                        </label>\n                    </div>\n                </div>\n            </div>"),i&&!d&&e.pdfAccessNum++,e.articleNum++,n},this.collectContentHandler=function(t){var n=e.elements.modal.querySelector(t.currentTarget.dataset.wrapper),s=e.elements.modal.querySelector(t.currentTarget.dataset.form),o=n.querySelectorAll('[name="bulkDownload"]:checked');e.collectContent(o,s)},this.exportCitationsSubmitButton=function(t){t.preventDefault();var n=t.currentTarget;e.elements.modal.querySelector(n.dataset.form).submit()},this.pdfDownloadSubmit=function(t){var n=t.currentTarget,s=n.querySelector("span"),o=e.elements.section.querySelector('[data-target="#pdfDownload"]').dataset.ajax,l=n.closest(".modal__dialog").querySelectorAll('[name="bulkDownload"]:checked'),a=n.previousElementSibling.querySelector(".selection-count__error"),i="",c=0;t.preventDefault(),a.classList.add(e.classList.hidden),n.setAttribute("disabled",!0),n.classList.add(e.classList.loader),n.classList.add(e.classList.eventDisabled),s.textContent="Preparing download",l.forEach((function(e){0===c&&(i+="?"),i+="doi=".concat(e.value,"&"),c++})),i=o+i.slice(0,-1),fetch(i).then((function(e){return e})).then((function(t){switch(t.headers.get("download-status")){case"full_access":var o=t.headers.get("content-disposition").replace("attachment; filename=","");t.blob().then((function(e){var t=document.createElement("a"),n=window.URL.createObjectURL(e);t.href=n,t.download=o,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(n)}));break;default:e.downloadButtonErrorReset(a,t.headers.get("download-status"))}e.resetDownloadButton(n,s)})).catch((function(e){console.log(e)}))},this.resetDownloadButton=function(t,n){t.removeAttribute("disabled"),t.classList.remove(e.classList.loader),t.classList.remove(e.classList.eventDisabled),n.textContent="Download (.zip)"},this.downloadButtonErrorReset=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s="";switch(n){case"partial_access":s="You do not have a required license. Some publications cannot be downloaded.";break;case"no_access":s="You do not have the required license. No publication will be downloaded.";break;default:s="Something went wrong, Please try again later."}t.classList.remove(e.classList.hidden),t.textContent=s},this.focusCycle=function(t){e.elements.modal.querySelectorAll(e.selectors.focusOrderElement).forEach((function(e){return e.removeAttribute("data-focus-order")})),e.elements.focusableList=e.focusableElements,e.elements.focusableList.forEach((function(t,n){t.dataset.focusOrder=n,t.addEventListener("focusout",e.focusCycleHandler)})),[e.elements.cancelBtn,e.elements.nextBtn,e.elements.closeBtn,e.elements.exportCitationsSubmitButton].forEach((function(t){return t.addEventListener("keydown",e.focusTrapHandler)}))},this.focusTrapHandler=function(t){if(t.shiftKey||t.keyCode!==i.a.TAB||(t.target===e.elements.cancelBtn&&e.elements.nextBtn.disabled||t.target===e.elements.nextBtn||t.target===e.elements.exportCitationsSubmitButton?(t.preventDefault(),e.elements.closeBtn.focus()):t.target===e.elements.cancelBtn&&(t.preventDefault(),e.elements.nextBtn.focus())),t.shiftKey&&t.keyCode===i.a.TAB)if(t.target===e.elements.closeBtn&&e.elements.nextBtn.disabled||t.target===e.elements.nextBtn)t.preventDefault(),e.elements.cancelBtn.focus();else if(t.target===e.elements.closeBtn)t.preventDefault(),e.elements.nextBtn.closest(".js--show")&&e.elements.nextBtn.focus(),e.elements.exportCitationsSubmitButton.closest(".js--show")&&e.elements.exportCitationsSubmitButton.focus();else if(t.target===e.elements.cancelBtn){var n;t.preventDefault(),null===(n=document.querySelector(e.selectors.lastOption))||void 0===n||n.focus()}},this.focusCycleHandler=function(t){t.stopImmediatePropagation();var n=t.target.dataset.focusOrder;if(t.relatedTarget&&!t.relatedTarget.closest(e.selectors.modal))switch(n){case"0":e.elements.focusableList[e.elements.focusableList.length-1].focus();break;case"".concat(e.elements.focusableList.length-1):e.elements.focusableList[0].focus()}},this.init()}),[{key:"setElements",value:function setElements(){this.elements.wrappers=document.querySelectorAll(this.selectors.wrappers),this.elements.modal=document.querySelector(this.selectors.modal),this.elements.maxPdfElement=this.elements.section.querySelector(this.selectors.maxPdfElement),this.elements.hasPdfElement=document.querySelectorAll(this.selectors.hasPdfElement),this.elements.bulkDownloadOption=document.querySelectorAll(this.selectors.bulkDownloadOption),this.elements.pdfDownloadTargetOption=document.querySelector(this.selectors.pdfDownloadTargetOption),this.elements.bulkDownloadInput=document.querySelectorAll(this.selectors.bulkDownloadInput),this.elements.bulkWizard=this.elements.modal.querySelectorAll(this.selectors.bulkWizard),this.elements.exportCitationsSubmitButton=this.elements.modal.querySelector(this.selectors.exportCitationsSubmitButton),this.elements.pdfDownloadSubmitButton=this.elements.modal.querySelector(this.selectors.pdfDownloadSubmitButton),this.elements.exportCitationsForm=this.elements.modal.querySelector(this.selectors.exportCitationsForm),this.elements.currentWizardBox=this.elements.modal.querySelector(".".concat(this.classList.show,"[data-wizard-box]")),this.elements.currentWizardFooter=this.elements.modal.querySelector(".".concat(this.classList.show,"[data-wizard-footer]")),this.elements.exportCitationInner=this.elements.modal.querySelectorAll(this.selectors.exportCitationInner),this.elements.anchorTags=document.querySelectorAll(this.selectors.anchorTags),this.elements.bulkCheckbox=document.querySelectorAll(this.selectors.bulkCheckbox),this.elements.cancelBtn=document.querySelector(this.selectors.cancelBtn),this.elements.closeBtn=document.querySelector(this.selectors.closeBtn),this.elements.nextBtn=document.querySelector(this.selectors.nextBtn)}},{key:"init",value:function init(){var e=document.querySelector(".bulk-download");e&&(this.elements.section=e,this.removeOldModal(),this.setElements(),this.initConfig(),this.onReady(),this.eventListeners())}},{key:"removeOldModal",value:function removeOldModal(){var e;null===(e=document.querySelector("".concat(this.selectors.modal,".").concat(this.classList.moved)))||void 0===e||e.remove()}},{key:"setContentTypeText",value:function setContentTypeText(){switch(this.contentType){case"articles":this.contentTypeText="articles";break;case"articlesAndChapters":this.contentTypeText="articles/chapters"}}},{key:"initConfig",value:function initConfig(){this.contentType=this.elements.section.dataset.contentType||"",this.contentTypeText="",this.isPdf="",this.maxPdfItems=this.elements.maxPdfElement?parseInt(this.elements.maxPdfElement.dataset.maxPdf):0,this.articleNum=0,this.pdfAccessNum=0,this.isPdfAccessNote=!1,this.setContentTypeText()}},{key:"onReady",value:function onReady(){var e=this;this.elements.modal.classList.contains(this.classList.moved)||(document.body.appendChild(this.elements.modal),this.elements.modal.classList.add(this.classList.moved)),this.clearModalContent(),this.elements.hasPdfElement.length&&this.elements.hasPdfElement.forEach((function(t){var n=t.nextElementSibling.innerHTML.indexOf("full"),s=t.nextElementSibling.innerHTML.indexOf("free"),o=t.nextElementSibling.innerHTML.indexOf("oa");n||s||o||e.elements.pdfDownloadTargetOption.classList.remove(e.classList.hidden)})),this.elements.bulkDownloadInput.length&&this.elements.section.classList.remove(this.classList.jsHidden)}},{key:"eventListeners",value:function eventListeners(){var e,t=this;this.elements.bulkDownloadOption.forEach((function(e){return e.addEventListener("click",t.downloadOptionHandler)})),this.elements.modal.addEventListener("change",this.bulkInputHandler),this.elements.bulkWizard.forEach((function(e){return e.addEventListener("click",t.wizardHandler)})),this.elements.exportCitationsSubmitButton.addEventListener("click",this.exportCitationsSubmitButton),this.elements.exportCitationsForm.addEventListener("click",this.collectContentHandler),null===(e=this.elements.anchorTags)||void 0===e||e.forEach((function(e){e.addEventListener("click",(function(e){return e.preventDefault(),!1}))})),this.elements.pdfDownloadSubmitButton&&this.elements.pdfDownloadSubmitButton.addEventListener("click",this.pdfDownloadSubmit)}},{key:"focusableElements",get:function get(){var e=this,t=[];return this.elements.modal.querySelectorAll("".concat(this.selectors.modalHeader,", .").concat(this.classList.show,"[data-wizard-footer]")).forEach((function(n){n.querySelectorAll(e.selectors.focusableElements).forEach((function(e){e.disabled||t.push(e)}))})),t}}])}()}}]);
//# sourceMappingURL=bulkDownload-a36aeecd12f7ccc7e5a8.js.map