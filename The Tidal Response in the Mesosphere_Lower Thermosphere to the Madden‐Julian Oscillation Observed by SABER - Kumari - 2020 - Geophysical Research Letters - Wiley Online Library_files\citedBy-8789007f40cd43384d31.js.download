(window.webpackJsonp=window.webpackJsonp||[]).push([[8],{193:function(t,e){self.fetch||(self.fetch=function(t,e){return e=e||{},new Promise((function(n,o){var i=new XMLHttpRequest,s=[],r=[],c={},l=function a(){return{ok:2==(i.status/100|0),statusText:i.statusText,status:i.status,url:i.responseURL,text:function text(){return Promise.resolve(i.responseText)},json:function json(){return Promise.resolve(i.responseText).then(JSON.parse)},blob:function blob(){return Promise.resolve(new Blob([i.response]))},clone:l,headers:{keys:function keys(){return s},entries:function entries(){return r},get:function get(t){return c[t.toLowerCase()]},has:function has(t){return t.toLowerCase()in c}}}};for(var d in i.open(e.method||"get",t,!0),i.onload=function(){i.getAllResponseHeaders().replace(/^(.*?):[^\S\n]*([\s\S]*?)$/gm,(function(t,e,n){s.push(e=e.toLowerCase()),r.push([e,n]),c[e]=c[e]?c[e]+","+n:n})),n(l())},i.onerror=o,i.withCredentials="include"==e.credentials,e.headers)i.setRequestHeader(d,e.headers[d]);i.send(e.body||null)}))})},394:function(t,e,n){"use strict";n.r(e);var o;n(193),n(55);o={selectors:{list:".cited-by__list",contentListController:".article-section__citedBy .accordion__control",contentList:".cited-by__content",sciteMetricsDropzone:".sciteMetricsDropzone",seeMoreWrapper:".seeMoreWrapper",loadingContentSpinner:".loading-content"},classes:{seeMore:"cited-by__see-more"},content:{loadingSpinner:'<li class="loading-content" tabindex="0"><span role="alert" aria-label="loading citations"><span aria-hidden="true">loading citations</span></span><div class="loading"></div></li>',citedByListContent:'<ul class="rlist cited-by__list"></ul><div class="cited-by__alert"></div>',citationsLoadedAlert:'<div class="sr-only" tabindex="-1" role="alert" aria-label="Citations loaded"></div>'},list:null,sciteMetricsDropzone:null,isTriggered:!1,init:function init(){o.list=document.querySelector(o.selectors.list),o.contentListController=o.convertToArray(document.querySelectorAll(o.selectors.contentListController)),o.contentList=document.querySelector(o.selectors.contentList),o.sciteMetricsDropzone=o.contentList.querySelector(o.selectors.sciteMetricsDropzone),o.control()},control:function control(){var t;null===(t=o.list)||void 0===t||t.addEventListener("click",o.seeMoreCitation),o.citationFirstCall()},citationFirstCall:function citationFirstCall(){o.contentList&&!o.list&&o.sciteMetricsDropzone&&""!==o.contentList.dataset.source&&o.contentListController.length&&["click","keydown"].forEach((function(t){o.contentListController.forEach((function(e){return e.addEventListener(t,o.buildInitContent)}))}))},buildInitContent:function buildInitContent(t){if((13===(t.keyCode?t.keyCode:t.which)||"click"===t.type)&&!o.isTriggered){o.contentList.insertAdjacentHTML("beforeend",o.content.citedByListContent),o.list=null===o.list&&o.contentList.querySelector(".cited-by__list");var e="".concat(o.contentList.dataset.source,"&ajaxRequest=true");o.list.innerHTML=o.content.loadingSpinner;var n=o.list.querySelector(o.selectors.loadingContentSpinner);setTimeout((function(){return n.focus()}),350),fetch(e).then((function(t){return t.json()})).then((function(t){o.buildCitationHeading(t),o.buildLoadedContent(t,n,o.list),o.init(),o.additionalFetchControls()})),o.isTriggered=!0}},buildCitationHeading:function buildCitationHeading(t){var e='\n                <h3>\n                    <span class="bald-text">'.concat(t.citedByTotalTimesLabel,"</span>\n                    <span>").concat(t.item.citedCount,"</span>\n                </h3>\n            ");o.sciteMetricsDropzone?o.sciteMetricsDropzone.insertAdjacentHTML("afterend",e):o.contentList.insertAdjacentHTML("afterbegin",e)},buildLoadedContent:function buildLoadedContent(t,e,n){if(t.item.citations.length&&t.item.citations.forEach((function(e){var o="",i="crossref"===e.source?t.registry.crossrefUrl+e.doi:"/doi/".concat(e.doi),s="crossref"===e.source?t.crossrefLinkLabel:t.internalLinkLabel;e.contributors&&e.contributors.forEach((function(t){o+='<span class="hlFld-ContribAuthor">'.concat(t.firstName," ").concat(t.surname,", </span>")}));var r='\n                        <li class="citedByEntry">\n                            <span class="entryAuthor">'.concat(o,"</span><span>").concat(e.itemTitle,"</span>").concat(e.parentTitle?'<span class="seriesTitle">, '.concat(e.parentTitle,"</span>"):"",'<span class="doi">, ').concat(e.doi,"</span>").concat(e.volume&&"0"!==e.volume?'<span class="volume">, <b>'.concat(e.volume,"</b></span>"):"").concat(e.issue&&"0"!==e.issue?'<span class="issue">, '.concat(e.issue,"</span>"):"").concat(e.firstPage?'<span class="page-range">, ('.concat(e.firstPage).concat(e.lastPage?"-".concat(e.lastPage):"",")</span>"):"").concat(e.year?'<span class="pub-date">, ('.concat(e.year,")</span>"):"",'<span>.</span>\n                            <div class="extra-links">\n                                <a href="').concat(i,'" class="visitable">').concat(s,"</a>\n                            </div>\n                        </li>");n.insertAdjacentHTML("beforeend",r)})),t.seeMoreLink){var i='<li class="citedByEntry seeMoreWrapper"><a href="javascript:void(0)" data-source="'.concat(t.seeMoreLink,'" class="cited-by__see-more" role="button">See more</a></li>');n.insertAdjacentHTML("beforeend",i)}setTimeout((function(){o.additionalContentBuild(e,n)}),500)},additionalContentBuild:function additionalContentBuild(t,e){var n=t.nextElementSibling.children[0],i=e.nextElementSibling,s=o.list.querySelector(o.selectors.seeMoreWrapper),r=o.list.querySelector(o.selectors.loadingContentSpinner);n.tabIndex=0,n.focus(),n.addEventListener("blur",o.removeTabIndexAttribute),o.list.removeChild(r),o.list.removeChild(s),i.innerHTML=o.content.citationsLoadedAlert},seeMoreCitation:function seeMoreCitation(t){if(t.target.classList.contains(o.classes.seeMore)){var e=t.target.closest("li"),n="".concat(t.target.dataset.source,"&ajaxRequest=true");e.insertAdjacentHTML("afterend",o.content.loadingSpinner);var i=o.list.querySelector(o.selectors.loadingContentSpinner);fetch(n).then((function(t){return t.json()})).then((function(t){o.buildLoadedContent(t,i,o.list),o.additionalSeeMoreControls()}))}},convertToArray:function convertToArray(t){return Array.prototype.slice.call(t)},removeTabIndexAttribute:function removeTabIndexAttribute(t){t.target.removeAttribute("tabindex")},additionalControls:function additionalControls(){},additionalFetchControls:function additionalFetchControls(){},additionalSeeMoreControls:function additionalSeeMoreControls(){}},UX.citedBy=o;var i=n(68);!function(){var t={selectors:{article:"article",citedBy:".cited-by",accordionControl:".accordion__control ",accordionControlNotOpened:".accordion__control:not(.js--open)",list:".cited-by__list",contentListController:".article-section__citedBy .accordion__control",contentList:".cited-by__content",sciteMetricsDropzone:".sciteMetricsDropzone",seeMoreWrapper:".seeMoreWrapper",loadingContentSpinner:".loading-content"},classes:{seeMore:"cited-by__see-more"},content:{loadingSpinner:'<li class="loading-content" tabindex="0"><span role="alert" aria-label="loading citations"><span aria-hidden="true">loading citations</span></span><div class="loading"></div></li>',citedByListContent:'<ul class="rlist cited-by__list"></ul><div class="cited-by__alert"></div>',citationsLoadedAlert:'<div class="sr-only" tabindex="-1" role="alert" aria-label="Citations loaded"></div>'},article:null,citedByEl:null,accordionControl:null,isGetFTREnabled:!1,isGetFTRiRetractionsEnabled:!1,getFTRUrl:null,getFTRCount:null,getFTRInstance:null,isCrossrefEnabled:!1,list:null,sciteMetricsDropzone:null,isTriggered:!1,isFirstRequest:!1,init:function init(){var e;t.article=document.querySelector(t.selectors.article),t.citedByEl=document.querySelector(t.selectors.citedBy),t.accordionControl=t.citedByEl.querySelector(t.selectors.accordionControl),t.accordionControlNotOpened=t.citedByEl.querySelector(t.selectors.accordionControlNotOpened),t.isGetFTRCitedByEnabled=t.accordionControl.getAttribute("data-getftr-citedby-enabled"),t.isGetFTRiRetractionsEnabled=null===(e=t.article)||void 0===e?void 0:e.getAttribute("data-getftr-retractions-enabled"),t.list=document.querySelector(t.selectors.list),t.contentListController=t.convertToArray(document.querySelectorAll(t.selectors.contentListController)),t.contentList=document.querySelector(t.selectors.contentList),t.sciteMetricsDropzone=t.contentList.querySelector(t.selectors.sciteMetricsDropzone),t.control()},control:function control(){var e,n;null===(e=t.list)||void 0===e||e.addEventListener("click",t.seeMoreCitation),t.citationFirstCall(),null===(n=t.accordionControlNotOpened)||void 0===n||n.addEventListener("click",t.additionalFetchControls)},citationFirstCall:function citationFirstCall(){t.contentList&&!t.list&&t.sciteMetricsDropzone&&""!==t.contentList.dataset.source&&t.contentListController.length&&["click","keydown"].forEach((function(e){t.contentListController.forEach((function(n){return n.addEventListener(e,t.buildInitContent)}))}))},buildInitContent:function buildInitContent(e){if((13===(e.keyCode?e.keyCode:e.which)||"click"===e.type)&&!t.isTriggered){t.contentList.insertAdjacentHTML("beforeend",t.content.citedByListContent),t.list=null===t.list&&t.contentList.querySelector(".cited-by__list");var n="".concat(t.contentList.dataset.source,"&ajaxRequest=true");t.list.innerHTML=t.content.loadingSpinner;var o=t.list.querySelector(t.selectors.loadingContentSpinner);setTimeout((function(){return o.focus()}),350),fetch(n).then((function(t){return t.json()})).then((function(e){t.buildCitationHeading(e),t.buildLoadedContent(e,o,t.list),t.init(),t.additionalFetchControls(e),t.mathJaxHandler()})),t.isTriggered=!0}},buildCitationHeading:function buildCitationHeading(e){var n='\n                <h3>\n                    <span class="bald-text">'.concat(e.citedByTotalTimesLabel,"</span>\n                    <span>").concat(e.item.citedCount,"</span>\n                </h3>\n            ");t.sciteMetricsDropzone?t.sciteMetricsDropzone.insertAdjacentHTML("afterend",n):t.contentList.insertAdjacentHTML("afterbegin",n)},buildLoadedContent:function buildLoadedContent(e,n,o){if(e.item.citations.length&&e.item.citations.forEach((function(n){var i="",s="crossref"===n.source?e.registry.crossrefUrl+n.doi:"/doi/".concat(n.doi),r="crossref"===n.source?e.crossrefLinkLabel:e.internalLinkLabel;n.contributors&&n.contributors.forEach((function(t){i+='<span class="hlFld-ContribAuthor">'.concat(t.firstName," ").concat(t.surname,", </span>")}));var c='\n                        <li class="citedByEntry">\n                            <span class="entryAuthor">'.concat(i,"</span><span>").concat(n.itemTitle,"</span>").concat(n.parentTitle?'<span class="seriesTitle">, '.concat(n.parentTitle,"</span>"):"",'<span class="doi">, ').concat(n.doi,"</span>").concat(n.volume&&"0"!==n.volume?'<span class="volume">, <b>'.concat(n.volume,"</b></span>"):"").concat(n.issue&&"0"!==n.issue?'<span class="issue">, '.concat(n.issue,"</span>"):"").concat(n.firstPage?'<span class="page-range">, ('.concat(n.firstPage).concat(n.lastPage?"-".concat(n.lastPage):"",")</span>"):"").concat(n.year?'<span class="pub-date">, ('.concat(n.year,")</span>"):"",'<span>.</span>\n                        <div class="extra-links ').concat(t.isGetFTRCitedByEnabled?"getFTR":"",'">\n                            ').concat(t.isGetFTRCitedByEnabled?'<span class="hidden data-doi">'.concat(n.doi,'</span><div class="getFTR__content getFTR__placeholder"></div>'):'<a href="'.concat(s,'" class="visitable">').concat(r,"</a>"),"\n                          \n                        </div>\n                    </li>");o.insertAdjacentHTML("beforeend",c)})),e.seeMoreLink){var i='<li class="citedByEntry seeMoreWrapper"><a href="javascript:void(0)" data-source="'.concat(e.seeMoreLink,'" class="cited-by__see-more" role="button">See more</a></li>');o.insertAdjacentHTML("beforeend",i)}setTimeout((function(){t.additionalContentBuild(n,o)}),500)},additionalContentBuild:function additionalContentBuild(e,n){var o=e.nextElementSibling.children[0],i=n.nextElementSibling;o.tabIndex=0,o.focus(),i.innerHTML=t.content.citationsLoadedAlert,o.addEventListener("blur",t.removeTabIndexAttribute),n.removeChild(e)},seeMoreCitation:function seeMoreCitation(e){if(e.target.classList.contains(t.classes.seeMore)){var n=e.target.closest("li"),o="".concat(e.target.dataset.source,"&ajaxRequest=true");n.insertAdjacentHTML("afterend",t.content.loadingSpinner);var i=t.list.querySelector(t.selectors.loadingContentSpinner),s=t.list.querySelector(t.selectors.seeMoreWrapper);i.focus(),t.list.removeChild(s),fetch(o).then((function(t){return t.json()})).then((function(e){t.buildLoadedContent(e,i,t.list),t.additionalSeeMoreControls()}))}},convertToArray:function convertToArray(t){return Array.prototype.slice.call(t)},removeTabIndexAttribute:function removeTabIndexAttribute(t){t.target.removeAttribute("tabindex")},additionalControls:function additionalControls(){},additionalFetchControls:function additionalFetchControls(){t.isFirstRequest=!0,t.isGetFTRCitedByEnabled&&t.isFirstRequest&&(t.isFirstRequest=!1,t.getFTRInstance=new i.a,t.getFTRInstance.articleCitedBy(),t.isGetFTRiRetractionsEnabled&&t.getFTRInstance.handleDoiUpdates(!0))},additionalSeeMoreControls:function additionalSeeMoreControls(){t.isGetFTRCitedByEnabled&&(window.scrollBy(0,10),t.isGetFTRiRetractionsEnabled&&t.getFTRInstance.handleDoiUpdates(!0,!0))},mathJaxHandler:function mathJaxHandler(){(t.citedByEl.innerHTML.includes("mathrm")||t.citedByEl.innerHTML.includes("textrm"))&&MathJax.typesetPromise([t.citedByEl])}};UX.citedBy=t}()}}]);
//# sourceMappingURL=citedBy-8789007f40cd43384d31.js.map