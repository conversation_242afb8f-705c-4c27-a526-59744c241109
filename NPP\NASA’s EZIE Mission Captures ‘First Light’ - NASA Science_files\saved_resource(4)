(t=>{t.fn.textareaCount=function(i,r){var o,l=t(this),c=0,u=i.maxCharacterSize,s=0,f=0,h={};function d(r){for(var a=0,e=0;e<r.length;e++)"\n"===r.charAt(e)&&a++;return a}function p(){return-1!==navigator.appVersion.toLowerCase().indexOf("win")}function g(r){return(r+" ").replace(/^[^A-Za-z0-9]+/gi,"").replace(/[^A-Za-z0-9]+/gi," ").split(" ")}function m(r){return r.length-1}function a(){var r,a,e,t=l.val(),n=("function"==typeof i.charCounter?i.charCounter:h[i.charCounter])(t);return 0<i.maxCharacterSize?(r=d(t=i.truncate&&n>=i.maxCharacterSize?t.substring(0,i.maxCharacterSize):t),a=i.maxCharacterSize,p()&&(a=i.maxCharacterSize-r),i.truncate&&a<n&&(e=this.scrollTop,l.val(t.substring(0,a)),this.scrollTop=e),o.removeClass(i.warningStyle+" "+i.errorStyle),a-n<=i.warningNumber&&o.addClass(i.warningStyle),a-n<0&&o.addClass(i.errorStyle),c=n,p()&&(c=n+r),f=m(g(l.val())),s=u-c):(r=d(t),c=n,p()&&(c=n+r),f=m(g(l.val()))),e=(e=(e=i.displayFormat).replace("#input",c)).replace("#words",f),e=0<u?(e=e.replace("#max",u)).replace("#left",s):e}function e(){o.html(a()),void 0!==r&&r.call(this,{input:c,max:u,left:s,words:f})}h.standard=function(r){return r.length},h.twitter=function(r){var a=Array(23).join("*"),e=new RegExp("(https?://)?([a-z0-9+!*(),;?&=$_.-]+(:[a-z0-9+!*(),;?&=$_.-]+)?@)?([a-z0-9-.]*)\\.(travel|museum|[a-z]{2,4})(:[0-9]{2,5})?(/([a-z0-9+$_-]\\.?)+)*/?(\\?[a-z+&$_.-][a-z0-9;:@&%=+/$_.-]*)?(#[a-z_.-][a-z0-9+$_.-]*)?","gi");return r.replace(e,a).length},i=t.extend({maxCharacterSize:-1,truncate:!0,charCounter:"standard",originalStyle:"originalTextareaInfo",warningStyle:"warningTextareaInfo",errorStyle:"errorTextareaInfo",warningNumber:20,displayFormat:"#input characters | #words words"},i),t("<div class='charleft'>&nbsp;</div>").insertAfter(l),(o=l.next(".charleft")).addClass(i.originalStyle),e(),l.bind("keyup",function(){e()}).bind("mouseover paste",function(){setTimeout(function(){e()},10)})}})(jQuery);;
!function($){var escape=/["\\\x00-\x1f\x7f-\x9f]/g,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},hasOwn=Object.prototype.hasOwnProperty;$.toJSON="object"==typeof JSON&&JSON.stringify?JSON.stringify:function(t){if(null===t)return"null";var e,r,n,o,i,f,u=$.type(t);if("undefined"!==u){if("number"===u||"boolean"===u)return String(t);if("string"===u)return $.quoteString(t);if("function"==typeof t.toJSON)return $.toJSON(t.toJSON());if("date"===u)return i=t.getUTCMonth()+1,f=t.getUTCDate(),'"'+t.getUTCFullYear()+"-"+(i=i<10?"0"+i:i)+"-"+(f=f<10?"0"+f:f)+"T"+(i=(i=t.getUTCHours())<10?"0"+i:i)+":"+(f=(f=t.getUTCMinutes())<10?"0"+f:f)+":"+(i=(i=t.getUTCSeconds())<10?"0"+i:i)+"."+(f=(f=(f=t.getUTCMilliseconds())<100?"0"+f:f)<10?"0"+f:f)+'Z"';if(e=[],$.isArray(t)){for(r=0;r<t.length;r++)e.push($.toJSON(t[r])||"null");return"["+e.join(",")+"]"}if("object"==typeof t){for(r in t)if(hasOwn.call(t,r)){if("number"===(u=typeof r))n='"'+r+'"';else{if("string"!==u)continue;n=$.quoteString(r)}"function"!==(u=typeof t[r])&&"undefined"!==u&&(o=$.toJSON(t[r]),e.push(n+":"+o))}return"{"+e.join(",")+"}"}}},$.evalJSON="object"==typeof JSON&&JSON.parse?JSON.parse:function(str){return eval("("+str+")")},$.secureEvalJSON="object"==typeof JSON&&JSON.parse?JSON.parse:function(str){var filtered=str.replace(/\\["\\\/bfnrtu]/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"");if(/^[\],:{}\s]*$/.test(filtered))return eval("("+str+")");throw new SyntaxError("Error parsing JSON, source is not valid.")},$.quoteString=function(t){return t.match(escape)?'"'+t.replace(escape,function(t){var e=meta[t];return"string"==typeof e?e:(e=t.charCodeAt(),"\\u00"+Math.floor(e/16).toString(16)+(e%16).toString(16))})+'"':'"'+t+'"'}}(jQuery);;
