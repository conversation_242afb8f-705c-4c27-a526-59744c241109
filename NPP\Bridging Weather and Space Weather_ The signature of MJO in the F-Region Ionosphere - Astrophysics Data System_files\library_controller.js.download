define(["backbone","js/components/generic_module","js/mixins/hardened","js/components/api_targets","js/components/api_request","js/components/api_feedback","js/components/api_query","js/mixins/dependon","utils"],function(n,e,t,l,p,o,h,i,r){var a=n.Model.extend({defaults:function(){return{num_documents:0,date_last_modified:void 0,permission:void 0,description:"",public:!1,num_users:1,owner:void 0,date_created:void 0,id:void 0,title:""}}}),s=n.Collection.extend({model:a}),a=e.extend({initialize:function(){this.collection=new s},activate:function(e){var t=this,r=(this.setBeeHive(e.getHardenedInstance()),this.getBeeHive().getService("PubSub"));r.subscribe(r.INVITING_REQUEST,_.bind(this.updateCurrentQuery,this)),r.subscribe(r.USER_ANNOUNCEMENT,_.bind(this.handleUserAnnouncement,this)),r.subscribe(r.CUSTOM_EVENT,function(e){"invalidate-library-metadata"===e&&(t._metadataLoaded=!1)}),_.each(["change","add","reset","remove"],function(i){this.listenTo(this.collection,i,function(e,t){"change"==i&&e instanceof n.Model?(r.publish(r.LIBRARY_CHANGE,this.collection.toJSON(),{ev:i,id:e.id}),delete this._libraryBibcodeCache[e.id]):r.publish(r.LIBRARY_CHANGE,this.collection.toJSON(),{ev:i})})},this)},handleUserAnnouncement:function(e){"user_signed_in"==e?this._fetchAllMetadata():"user_signed_out"==e&&this.collection.reset({})},updateCurrentQuery:function(e){this._currentQuery=e},composeRequest:function(e,t,i){var r=(i=i||{}).data||void 0,n=$.Deferred();return e=new p({target:e,options:{context:this,type:t,data:JSON.stringify(r),contentType:"application/json",done:function(){var e=[_.extend(arguments[0],i.extraArguments),[].slice(arguments,1)];n.resolve.apply(void 0,e)},fail:function(e){var t=this.getPubSub();t.publish(t.CUSTOM_EVENT,"libraries:request:fail",e),n.reject.apply(void 0,arguments)}}}),this.getBeeHive().getService("Api").request(e),n.promise()},_executeApiRequest:function(e){var e=new p({target:l.SEARCH,query:e}),t=$.Deferred(),i=this.getPubSub();return i.subscribeOnce(i.DELIVERING_RESPONSE,_.bind(function(e){t.resolve(e)}),this),i.publish(i.EXECUTE_REQUEST,e),t.promise()},_getBibcodes:function(e){var t=$.Deferred(),i=this;if("all"==e.bibcodes){var r=function(){c.set("start",o),this._executeApiRequest(c).done(function(e){e=_.map(e.get("response.docs"),function(e){return e.bibcode});[].push.apply(s,e),(o+=a)<n?r.call(i):t.resolve(s)})},n=e.limit||2e3,o=0,a=100,s=[],c=this._currentQuery.clone();c.unlock(),c.set("rows",100),c.set("fl","bibcode"),r.call(this)}else if("selected"==e.bibcodes){var d=this.getBeeHive().getObject("AppStorage").getSelectedPapers();t.resolve(d)}else{if(!_.isArray(e.bibcodes))throw new Error("should we add all bibcodes or only selected ones?");t.resolve(e.bibcodes)}return t.promise()},_fetchAllMetadata:function(){var t=this,e=l.LIBRARIES;return this.composeRequest(e,"GET").done(function(e){t._metadataLoaded=!0,t.collection.reset(e.libraries)})},getLibraryMetadata:function(t){var i,r,e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];return t&&!this.collection.get(t)?this.fetchLibraryMetadata(t,e):(i=$.Deferred(),(r=this)._metadataLoaded?(e=(t?r.collection.get(t):r.collection).toJSON(),i.resolve(e)):this._fetchAllMetadata().done(function(e){setTimeout(function(){var e=(t?r.collection.get(t):r.collection).toJSON();i.resolve(e)},1)}),i.promise())},fetchLibraryMetadata:function(t){var i,r=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],n=this;if(t)return i=$.Deferred(),this.composeRequest(l.LIBRARIES+"/"+t).done(function(e){i.resolve(e.metadata),r&&n.collection.add(e.metadata,{merge:!0})}).fail(function(e){i.reject(e),n.getPubSub().publish(n.getPubSub().NAVIGATE,"404",{xhr:e,message:"Cannot find library ID: <strong>".concat(t,"</strong>")})}),i.promise();throw new Error("need to provide a library id")},_libraryBibcodeCache:{},getLibraryBibcodes:function(t){var i,r,n,o,a,s=$.Deferred(),c=this,d=1e4;return this._libraryBibcodeCache[t]?s.resolve(this._libraryBibcodeCache[t]):(i=d,r=0,n=100,o=[],a=l.LIBRARIES+"/"+t,c=this,b.call(this)),s.promise();function u(e){i=e.solr.response.numFound>d?d:e.solr.response.numFound;e=_.pluck(e.solr.response.docs,"bibcode");[].push.apply(o,e),(r+=n)<i?b():(c._libraryBibcodeCache[t]=o,s.resolve(o))}function b(){var e=new h,e=(e.set("rows",100),e.set("fl","bibcode"),e.set("start",r),new p({target:a,query:e,options:{context:this,contentType:"application/x-www-form-urlencoded",done:u,fail:this.handleError}}));c.getBeeHive().getService("Api").request(e)}},createLibrary:function(e){var t=this,i=l.LIBRARIES;return this.composeRequest(i,"POST",{data:e}).done(function(){t._fetchAllMetadata()})},deleteLibrary:function(t,i){var r=this,e=l.DOCUMENTS+"/"+t;return this.composeRequest(e,"DELETE").done(function(){r.collection.remove(t),r.getBeeHive().getService("PubSub").publish(r.getBeeHive().getService("PubSub").NAVIGATE,"AllLibrariesWidget","libraries");var e="Library <b>"+i+"</b> was successfully deleted";r.getBeeHive().getService("PubSub").publish(r.getBeeHive().getService("PubSub").ALERT,new o({code:0,msg:e,type:"success"}))}).fail(function(e){e=JSON.parse(e.responseText).error,e="Library <b>"+i+"</b> could not be deleted : ("+e+")";r.getBeeHive().getService("PubSub").publish(r.getBeeHive().getService("PubSub").ALERT,new o({code:0,msg:e,type:"danger"}))})},updateLibraryContents:function(t,e){var i=this,e={data:e,extraArguments:{numBibcodesRequested:e.bibcode.length}},r=l.DOCUMENTS+"/"+t;return this.composeRequest(r,"POST",e).done(function(){i.fetchLibraryMetadata(t)}).fail(function(e){e=JSON.parse(e.responseText).error,e="Library <b>"+i.collection.get(t).title+"</b> could not be updated: ("+e+")";i.getBeeHive().getService("PubSub").publish(i.getBeeHive().getService("PubSub").ALERT,new o({code:0,msg:e,type:"danger"}))})},performLibraryOperation:function(e,t){if(!t)throw new Error("must provide options object with action and set of secondary libraries (if necessary)");var i={},r=t.action&&t.action.toLowerCase(),n=t.libraries,t=t.name,o=/^(union|intersection|difference)$/.test(r),a=/^copy$/.test(r),s=/^empty$/.test(r);if(!_.isString(r)||!o&&!a&&!s)throw new Error(r+" is not one of the defined actions");if(!_.isString(e))throw new Error("must pass library ID as first parameter");if(o&&!_.isArray(n))throw new Error("libraries must be an array");if(a&&(!_.isArray(n)||1!==n.length))throw new Error("for copy action, libraries must have exactly 1 entry");n=_.unique(n),_.extend(i,{action:r}),o?_.extend(i,{libraries:n,name:t}):a&&_.extend(i,{libraries:n});s=l.LIBRARIES+"/operations/"+e;return this.composeRequest(s,"POST",{data:i})},transferOwnership:function(e,t){if(!t||!_.isString(t))throw"new owner email address must be a string";if(e&&_.isString(e))return e=l.LIBRARY_TRANSFER+"/"+e,this.composeRequest(e,"POST",{data:{email:t}});throw"library Id must be a string"},updateLibraryMetadata:function(t,e){var i=this,r=l.DOCUMENTS+"/"+t;return this.composeRequest(r,"PUT",{data:e}).done(function(e){i.collection.get(t).set(e)}).fail(function(e){e=JSON.parse(e.responseText).error,e="Library <b>"+i.collection.get(t).get("name")+"</b> could not be updated: ("+e+")";i.getBeeHive().getService("PubSub").publish(i.getBeeHive().getService("PubSub").ALERT,new o({code:0,msg:e,type:"danger"}))})},addBibcodesToLib:function(t){var i=this;return this._getBibcodes(t).then(function(e){return i.updateLibraryContents(t.library,{bibcode:e,action:"add"}).fail(function(){var e="Library <b>"+i.collection.get(t.library).title+"</b> could not be updated";i.getBeeHive().getService("PubSub").publish(i.getBeeHive().getService("PubSub").ALERT,new o({code:0,msg:e,type:"danger"}))})})},createLibAndAddBibcodes:function(t){var i=this;return this._getBibcodes(t).then(function(e){if(e)return t.bibcode=e,i.createLibrary(t).fail(function(){var e="Library <b>"+name+"</b> could not be created";i.getBeeHive().getService("PubSub").publish(i.getBeeHive().getService("PubSub").ALERT,new o({code:0,msg:e,type:"danger"}))});throw new Error("Solr returned no bibcodes, can't put them in the new library")})},importLibraries:function(e){var t,i=$.Deferred(),r=this;if("classic"===e)t=l.LIBRARY_IMPORT_CLASSIC_TO_BBB;else{if("twopointoh"!==e)return void console.error("didn't recognize library endpoint! should be one of 'classic' or 'twopointoh' ");t=l.LIBRARY_IMPORT_ADS2_TO_BBB}return this.getBeeHive().getService("Api").request(new p({target:t,options:{done:function(e){i.resolve.apply(void 0,[].slice.apply(arguments)),r._fetchAllMetadata()},fail:function(e){i.reject.apply(void 0,[].slice.apply(arguments))}}})),i.promise()},hardenedInterface:{getLibraryMetadata:"returns json list of libraries, optional lib id as param",createLibrary:"createLibrary",createLibAndAddBibcodes:"createLibAndAddBibcodes",addBibcodesToLib:"addBibcodesToLib",deleteLibrary:"deleteLibrary",updateLibraryContents:"updateLibraryContents",updateLibraryMetadata:"updateLibraryMetadata",importLibraries:"importLibraries",transferOwnership:"transferOwnership",getLibraryBibcodes:"getLibraryBibcodes",performLibraryOperation:"performLibraryOperation"}});return _.extend(a.prototype,t,i.BeeHive),a});
//# sourceMappingURL=library_controller.js.map