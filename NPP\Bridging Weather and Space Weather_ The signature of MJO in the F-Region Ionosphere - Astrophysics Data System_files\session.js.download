define(["backbone","js/components/api_request","js/components/api_targets","js/mixins/hardened","js/components/generic_module","js/mixins/dependon","js/components/api_query","js/components/user","js/components/api_feedback","js/mixins/api_access","utils"],function(e,i,r,s,t,n,o,a,c,u,d){function l(e,s){return JSON.stringify(_.pick(e,g[s]))}var p=e.Model.extend({defaults:function(){return{resetPasswordToken:void 0}}}),g={login:["email","password"],register:["given_name","family_name","email","password1","password2","g-recaptcha-response"],resetPassword1:["g-recaptcha-response"],resetPassword2:["password1","password2"]},e=t.extend({initialize:function(e){e=e||{};this.model=new p,this.test=!!e.test||void 0,_.bindAll(this,["loginSuccess","loginFail","registerSuccess","registerFail","resetPassword1Success","resetPassword1Fail","resetPassword2Success","resetPassword2Fail"])},activate:function(e){this.setBeeHive(e)},login:function(s){var t=$.Deferred(),n=this;return this.sendRequestWithNewCSRF(function(e){e=new i({target:r.LOGIN,query:new o({}),options:{type:"POST",data:l(s,"login"),contentType:"application/json",headers:{"X-CSRFToken":e},done:function(){t.resolve.apply(t,arguments),n.loginSuccess.apply(n,arguments)},fail:function(){t.reject.apply(t,arguments),n.loginFail.apply(n,arguments)},beforeSend:function(e,s){e.session=this}}});return this.getBeeHive().getService("Api").request(e)}),t.promise()},sendRequestWithNewCSRF:function(e){e=_.bind(e,this),this.getBeeHive().getObject("CSRFManager").getCSRF().done(e)},logout:function(){this.sendRequestWithNewCSRF(function(e){e=new i({target:r.LOGOUT,query:new o({}),options:{context:this,type:"POST",headers:{"X-CSRFToken":e},contentType:"application/json",fail:this.logoutSuccess,done:this.logoutSuccess}});return this.getBeeHive().getService("Api").request(e)})},register:function(s){this.sendRequestWithNewCSRF(function(e){e=new i({target:r.USER,query:new o({}),options:{type:"POST",data:l(s,"register"),contentType:"application/json",headers:{"X-CSRFToken":e},done:this.registerSuccess,fail:this.registerFail}});return this.getBeeHive().getService("Api").request(e)})},resetPassword1:function(s){var t=s.email,s=_.omit(s,"email");this.sendRequestWithNewCSRF(function(e){e=new i({target:r.RESET_PASSWORD+"/"+t,query:new o({}),options:{type:"POST",data:l(s,"resetPassword1"),headers:{"X-CSRFToken":e},contentType:"application/json",done:this.resetPassword1Success,fail:this.resetPassword1Fail}});return this.getBeeHive().getService("Api").request(e)})},resetPassword2:function(s){this.sendRequestWithNewCSRF(function(e){e=new i({target:r.RESET_PASSWORD+"/"+this.model.get("resetPasswordToken"),query:new o({}),options:{type:"PUT",data:l(s,"resetPassword2"),contentType:"application/json",headers:{"X-CSRFToken":e},done:this.resetPassword2Success,fail:this.resetPassword2Fail}});return this.getBeeHive().getService("Api").request(e)})},resendVerificationEmail:function(s){var t=this;this.sendRequestWithNewCSRF(function(e){e=new i({target:r.RESEND_VERIFY.replace("{email}",s),query:new o({}),options:{type:"PUT",headers:{"X-CSRFToken":e},done:function(){var e=t.getPubSub();e.publish(e.USER_ANNOUNCEMENT,"resend_verification_email_success")},fail:function(e){var s=t.getPubSub(),e=d.extractErrorMessageFromAjax(e,"error unknown"),e="Resending verification email was unsuccessful (".concat(e,")");s.publish(s.ALERT,new c({code:0,msg:e,type:"danger",fade:!0})),s.publish(s.USER_ANNOUNCEMENT,"resend_verification_email_fail",e)}}});return this.getBeeHive().getService("Api").request(e)})},setChangeToken:function(e){this.model.set("resetPasswordToken",e)},loginSuccess:function(e,s,t){this.getApiAccess({reconnect:!0}).done(function(){})},loginFail:function(e,s,t){var n=this.getPubSub(),e=d.extractErrorMessageFromAjax(e,"error unknown"),e="Login was unsuccessful (".concat(e,")");n.publish(n.ALERT,new c({code:0,msg:e,type:"danger",fade:!0})),n.publish(n.USER_ANNOUNCEMENT,"login_fail",e)},logoutSuccess:function(e,s,t){var n=this;this.getApiAccess({reconnect:!0}).done(function(){n.getBeeHive().getObject("User").completeLogOut()})},registerSuccess:function(e,s,t){var n=this.getPubSub();n.publish(n.USER_ANNOUNCEMENT,"register_success")},registerFail:function(e,s,t){var n=this.getPubSub(),e=d.extractErrorMessageFromAjax(e,"error unknown"),e="Registration was unsuccessful (".concat(e,")");n.publish(n.ALERT,new c({code:0,msg:e,type:"danger",fade:!0})),n.publish(n.USER_ANNOUNCEMENT,"register_fail",e)},resetPassword1Success:function(e,s,t){var n=this.getPubSub();n.publish(n.USER_ANNOUNCEMENT,"reset_password_1_success")},resetPassword1Fail:function(e,s,t){var n=this.getPubSub(),e=d.extractErrorMessageFromAjax(e,"error unknown"),e="password reset step 1 was unsuccessful (".concat(e,")");n.publish(n.ALERT,new c({code:0,msg:e,type:"danger",fade:!0})),n.publish(n.USER_ANNOUNCEMENT,"reset_password_1_fail",e)},resetPassword2Success:function(e,s,t){var n=this.getApiAccess({reconnect:!0}),i=this.getPubSub();n.done(function(){i.publish(i.USER_ANNOUNCEMENT,"reset_password_2_success");i.publish(i.NAVIGATE,"index-page"),i.publish(i.ALERT,new c({code:0,msg:"Your password has been successfully reset!",type:"success",modal:!0}))}),n.fail(function(e){e=d.extractErrorMessageFromAjax(e,"error unknown"),e="Your password was not successfully reset. Please try to follow the link from the email you received again.\n\n(".concat(e,")");i.publish(i.USER_ANNOUNCEMENT,"reset_password_2_fail",e),i.publish(i.ALERT,new c({code:0,msg:e,type:"danger",modal:!0}))})},resetPassword2Fail:function(e,s,t){var n=this.getPubSub(),e=d.extractErrorMessageFromAjax(e,"error unknown"),e="password reset step 2 was unsuccessful (".concat(e,")");n.publish(n.ALERT,new c({code:0,msg:e,type:"danger",fade:!0})),n.publish(n.USER_ANNOUNCEMENT,"reset_password_2_fail",e)},hardenedInterface:{login:"log the user in",logout:"log the user out",register:"registers a new user",resetPassword1:"sends an email to account",resetPassword2:"updates the password",setChangeToken:"the router stores the token to reset password here",resendVerificationEmail:"resends the verification email"}});return _.extend(e.prototype,n.BeeHive,s,u),e});
//# sourceMappingURL=session.js.map