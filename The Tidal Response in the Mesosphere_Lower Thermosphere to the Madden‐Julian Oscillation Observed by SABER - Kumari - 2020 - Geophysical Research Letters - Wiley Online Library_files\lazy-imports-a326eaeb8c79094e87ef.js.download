(window.webpackJsonp=window.webpackJsonp||[]).push([[27],{202:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(8),o=n.n(i),s=n(9),r=n.n(s),a=(n(203),function(){return r()((function TableOfContent(){o()(this,TableOfContent),this.classList={hidden:"hidden"},this.selectors={menuBookTab:".menuBook--tab",authorBio:"#authorBio",bookReviews:"#bookReviews",abstractPreviewZoom:".abstract-preview__zoom",abstractLastFocus:"[data-lastFocus]",readCubeSharing:".readCube-sharing",shareCtrl:".share__ctrl",shareAccessCtrl:".share-access__ctrl",dropBlockHolder:".dropBlock__holder",tocAbstractPreview:".table-of-content .abstract-preview"},this.elements={menuBookTab:null,abstractPreviewZoom:null,readCubeSharing:null,shareCtrl:null,tocAbstractPreview:null},this.checkMenuItem=function(e){if(!document.querySelector("".concat(e,"-pane"))){var t=UX.utils.convertToArray(document.querySelectorAll(e));t.length&&t.forEach((function(e){(null==e?void 0:e.parentElement).removeChild(e)}))}},this.setElements(),this.eventListeners(),this.onReady(),this.onLoad()}),[{key:"setElements",value:function setElements(){this.elements.menuBookTab=document.querySelector(this.selectors.menuBookTab),this.elements.abstractPreviewZoom=UX.utils.convertToArray(document.querySelectorAll(this.selectors.abstractPreviewZoom)),this.elements.readCubeSharing=document.querySelector(this.selectors.readCubeSharing),this.elements.shareCtrl=document.querySelector(this.selectors.shareCtrl),this.elements.tocAbstractPreview=UX.utils.convertToArray(document.querySelectorAll(this.selectors.tocAbstractPreview))}},{key:"eventListeners",value:function eventListeners(){UX.fancybox&&(UX.fancybox(this.elements.abstractPreviewZoom),document.addEventListener("keydown",UX.fancyboxClose))}},{key:"onReady",value:function onReady(){(this.checkMenuItem(this.selectors.authorBio),this.checkMenuItem(this.selectors.bookReviews),this.elements.menuBookTab)&&this.elements.menuBookTab.querySelector("li:first-child a").click();if(this.elements.readCubeSharing&&this.elements.shareCtrl){var e=this.elements.shareCtrl.parentElement,t=null==e?void 0:e.querySelector(this.selectors.shareAccessCtrl),n=null==e?void 0:e.querySelector(this.selectors.dropBlockHolder);this.elements.shareCtrl.classList.remove(this.classList.remove),null==t||t.classList.add(this.classList.remove),null==n||n.setAttribute("aria-labelledby","share__ctrl")}}},{key:"onLoad",value:function onLoad(){this.elements.tocAbstractPreview.length&&UX.utils.changeImageWidthOnLoad(this.selectors.tocAbstractPreview,1.75),UX.utils.fallbackImageViewer()}}])}());UX.tocJs.truncate=function(){try{UX.tocJs.$loa.not("[data-truncate='none']").truncate({lines:2,type:"list",seeMoreLink:!0,seeMoreText:"See all authors",seeLessText:"See fewer authors",lessLinkEllipsis:!0})}catch(e){return null}}},205:function(e,t,n){"use strict";n(212);!function(e){var t=null,n=null;e.stickyElements.selectors.header=".pageHeader",e.test.isIE()&&(t=new MutationObserver((function(e){e.forEach((function(e){"absolute"===$(e.target).css("position")&&$(e.target).css("top","0")}))})),(n=document.querySelector(".article-row-right"))&&t.observe(n,{attributes:!0,attributeFilter:["style"]}))}(UX)},206:function(e,t,n){"use strict";var i,o=n(0),s=n(7),r=n(11),a=(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}),__decorate=function(e,t,n,i){var o,s=arguments.length,r=s<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(r=(s<3?o(r):s>3?o(t,n,r):o(t,n))||r);return s>3&&r&&Object.defineProperty(t,n,r),r},__metadata=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},l=function(){function Configs(){}return Configs.prototype.notePopup=function(e){return'<button class="footNoteClose footNote__cycleElement" aria-label="close button">\n                    <i class="icon-tools_close" aria-hidden="true"></i>\n                </button>\n                <div class="footNotePopup__item footNote__cycleElement">'+e+'</div>\n                <span class="chevronDown"></span>'},Configs=__decorate([Object(o.j)()],Configs)}(),c=function(e){function Texts(){return null!==e&&e.apply(this,arguments)||this}return a(Texts,e),Texts}(o.d),d=function(e){function ClassList(){var t=null!==e&&e.apply(this,arguments)||this;return t.accordionControl=".accordion__control",t.jsOpen="js--open",t.citedByArticleSection=".article-section__citedBy",t}return a(ClassList,e),ClassList}(o.d),u=function(e){function Selectors(){var t=null!==e&&e.apply(this,arguments)||this;return t.supportInfo=new o.e("a[href$='#support-information-section']","document"),t.supportInfoControl=new o.e(".article-section__supporting a.accordion__control","document"),t.openResearch=new o.e("a[href$='#open-research-section']","document"),t.openResearchControl=new o.e(".article-section__open-research a.accordion__control","document"),t.links=new o.e('[data-db-target-of=sections] a, .scrollableLink, .scrollableLinks a, .comment-link a, .PdfLink a[href="#accessDenialLayout"], .article-section__full a[href*="#"], [href="#citedby-section"]',"document"),t}return a(Selectors,e),Selectors}(o.f),h=function(e){function Elements(){var t=null!==e&&e.apply(this,arguments)||this;return t.links=[],t}return a(Elements,e),Elements}(o.b),f=function(){function GoToSection(e){this.wrapper=e,this.isTablet=!1}return GoToSection.prototype.initialize=function(){this.elements.initialize(this.wrapper),this.addEventListeners()},GoToSection.prototype.isTabletOn=function(){this.isTablet=!0},GoToSection.prototype.isTabletOff=function(){this.isTablet=!1},GoToSection.prototype.addEventListeners=function(){var e=this;this.domUtils.addEventListener(this.elements.supportInfo,"click",(function(){return e.elements.supportInfoControl.click()})),this.domUtils.addEventListener(this.elements.openResearch,"click",(function(){return e.elements.openResearchControl.click()})),this.domUtils.addEventListener(this.elements.links,"click",(function(t){return e.flowControl(t)})),this.domUtils.addEventListener(this.elements.links,"keydown",(function(t){return e.flowControl(t)})),this.domUtils.addEventListener(document,"keydown",(function(t){return e.escapeKeyHandler(t)}))},GoToSection.prototype.flowControl=function(e){var t=this;if("keydown"!==e.type||e.keyCode===s.a.RETURN){var n=e.currentTarget;n.classList.contains("open-figure-link")||n.classList.contains("bibLink")||n.classList.contains("tab-link")||n.closest(".accordion__control")||n.closest(".figure__toggle-link")||(e.preventDefault(),e.stopPropagation(),this.domUtils.getElements("html , body").forEach((function(e){return e.classList.remove("lock-screen")})),n.classList.contains("noteLink")?(this.openFootNote(n),window.addEventListener("resize",(function(){t.domUtils.getElements(".footNotePopup").length&&t.openFootNote(n)}))):n.classList.contains("backToText")?this.domUtils.getElement(n.getAttribute("href")).focus():n.classList.contains("goToText")||n.closest(".article-header__references-container")||n.getAttribute("href").indexOf("#accessDenialLayout")>-1?(this.scrollToTarget(n,!0),this.domUtils.getElement(n.getAttribute("href")).focus()):(this.scrollToTarget(n,!0),window.UX.dropBlock.on.hide(),this.isTablet&&(this.domUtils.getElement("[data-db-target-of=sections]").click(),this.domUtils.getElement(".w-slide__back").click())),n.closest(".sections__drop.js--open")&&window.UX.dropBlock.on.hide())}},GoToSection.prototype.escapeKeyHandler=function(e){"Escape"===e.key&&this.closeFootNote()},GoToSection.prototype.scrollToTarget=function(e,t){var n=t?this.domUtils.getElementById(e.hash.slice(1)):e,i=null==(n=n||this.domUtils.getElement('[name="'+e.hash.slice(1)+'"]'))?void 0:n.closest(this.classList.accordionControl);if(i&&(null==n?void 0:n.closest(this.classList.citedByArticleSection))){var o=null==n?void 0:n.closest(this.classList.citedByArticleSection),s=this.domUtils.getElement(this.classList.accordionControl,o);s.classList.contains(this.classList.jsOpen)||s.click()}else i&&!i.classList.contains(this.classList.jsOpen)&&i.click();n?(n.scrollIntoView({behavior:"smooth",block:"start"}),n.setAttribute("tabindex","0"),setTimeout((function(){n.focus()}),750),"a"!==n.tagName&&"button"!==n.tagName&&n.setAttribute("tabindex","-1")):this.redirectToExternalLink(e)},GoToSection.prototype.redirectToExternalLink=function(e){window.location.assign(e.href)},GoToSection.prototype.openFootNote=function(e){var t=this;this.closeFootNote(),this.elements.noteLink=e;var n=this.createFootNote(e),i=n.noteContainer,o=n.chevronDown,s=n.noteClose,r=this.isNodeInsideNode(e,"TBODY");r instanceof HTMLElement?r.appendChild(i):document.body.appendChild(i),window.UX.utils.focusCycleInitiator(i),i.querySelector(".footNotePopup__item").focus({preventScroll:!0});var a=i.offsetHeight,l=0,c=0,d=0,u=0,h=0;r instanceof HTMLElement?(l=e.offsetTop,c=e.offsetLeft,d=e.offsetLeft-r.offsetLeft,u=r.offsetWidth,h=r.offsetHeight,i.classList.add("insideTable")):(l=e.getBoundingClientRect().top+document.documentElement.scrollTop,c=e.getBoundingClientRect().left,d=e.offsetLeft,u=e.parentElement.offsetWidth),this.isNodeInsideNode(e,"THEAD")?(o.classList.add("inverse"),i.style.top=l+35+"px",i.classList.add("insideHead")):i.style.top=l-a-5+"px",u/2>d?(i.style.left=c+2+"px",o.classList.add("left")):(i.style.left=c-i.offsetWidth+22+"px",o.classList.add("right")),h/2>l&&(o.classList.add("inverse"),i.style.top=l+35+"px"),s.addEventListener("click",(function(){return t.closeFootNote()}))},GoToSection.prototype.closeFootNote=function(){var e,t,n,i;(this.elements.noteLink||this.domUtils.getElement(".footNotePopup"))&&(null===(e=this.elements.noteLink)||void 0===e||e.setAttribute("aria-expanded","false"),null===(t=this.elements.noteLink)||void 0===t||t.removeAttribute("aria-controls"),null===(n=this.domUtils.getElement(".footNotePopup"))||void 0===n||n.remove(),null===(i=this.elements.noteLink)||void 0===i||i.focus(),this.elements.noteLink=null)},GoToSection.prototype.createFootNote=function(e){var t=document.createElement("div");t.classList.add("footNotePopup");var n=e.getAttribute("data-noteid"),i=e.getAttribute("id"),o=e.getAttribute("href").substring(1),s=null==n?void 0:n.split(" ");if(s.length>1){var r=document.createElement("div");s.forEach((function(e){var t=document.querySelector("li#"+e).cloneNode(!0);null==r||r.appendChild(t)})),t.innerHTML=this.configs.notePopup(r.innerHTML)}else{var a=document.querySelector("li#"+n).innerHTML;t.innerHTML=this.configs.notePopup(a)}return e.setAttribute("aria-expanded","true"),e.setAttribute("aria-controls",o),t.setAttribute("id",o),t.setAttribute("aria-labelledby",i),{noteContainer:t,chevronDown:this.domUtils.getElement(".chevronDown",t),noteClose:this.domUtils.getElement(".footNoteClose",t)}},GoToSection.prototype.isNodeInsideNode=function(e,t){return e.tagName===t?e:e.parentNode!==document.body&&this.isNodeInsideNode(e.parentNode,t)},__decorate([Object(o.i)(),__metadata("design:type",l)],GoToSection.prototype,"configs",void 0),__decorate([Object(r.a)("md"),__metadata("design:type",Object)],GoToSection.prototype,"isTablet",void 0),GoToSection=__decorate([Object(o.c)(u,h,d,c),__metadata("design:paramtypes",[HTMLElement])],GoToSection)}();t.a=f},228:function(e,t,n){"use strict";n(229);var i=UX.dropBlock;i.setFocus=function(){},i.additionalOnShow=function(){var e=-1!==navigator.userAgent.toLowerCase().indexOf("safari");i.$target&&!e&&!UX.dropBlock.isMobile&&i.$target.attr("tabindex","-1")},i.additionalOnHide=function(){i.$target&&!UX.dropBlock.isMobile&&i.$target.removeAttr("tabindex")}},230:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(8),o=n.n(i),s=n(9),r=n.n(s),a=function(){return r()((function Citation(){var e=this;o()(this,Citation),this.selectors={authorName:".author-name",authorInfo:".author-info",authorJsOpen:".author-name.accordion-tabbed__control.js--open",editorName:".editor__name"},this.elements={authorsNames:null,authorsInfos:null,authorJsOpen:null,editorNames:null},this.variables={viewPortWidth:0,isDesktop:!0},this.init=function(){e.variables.viewPortWidth=window.innerWidth,e.setElements(),e.checkIfDesktop(),e.checkAuthorInfo()},this.checkAuthorInfo=function(){var t,n;(e.elements.authorJsOpen&&e.elements.authorJsOpen.click(),e.variables.isDesktop)||(null===(t=e.elements.authorsNames)||void 0===t||t.forEach((function(t){t.addEventListener("click",(function(n){var i,o=null==t||null===(i=t.parentElement)||void 0===i?void 0:i.querySelector(".author-info"),s=t.offsetTop+t.offsetHeight+2;null==o||o.setAttribute("style","top: ".concat(s,"px")),n.x<e.variables.viewPortWidth/2&&(null==o||o.classList.add("left-zero"))}))})),null===(n=e.elements.editorNames)||void 0===n||n.forEach((function(t){t.addEventListener("click",(function(n){var i;n.preventDefault();var o=null==t||null===(i=t.parentElement)||void 0===i?void 0:i.querySelector(".editor__info");n.x>e.variables.viewPortWidth/2?null==o||o.classList.add("right-zero"):null==o||o.classList.remove("right-zero")}))})))},this.init(),this.eventListeners()}),[{key:"setElements",value:function setElements(){this.elements.authorsInfos=document.querySelectorAll(this.selectors.authorInfo),this.elements.authorsNames=document.querySelectorAll(this.selectors.authorName),this.elements.authorJsOpen=document.querySelector(this.selectors.authorJsOpen),this.elements.editorNames=document.querySelectorAll(this.selectors.editorName)}},{key:"eventListeners",value:function eventListeners(){window.addEventListener("resize",this.init)}},{key:"checkIfDesktop",value:function checkIfDesktop(){this.variables.viewPortWidth<=992&&(this.variables.isDesktop=!1)}}])}()},231:function(e,t,n){"use strict";var i,o=n(0),s=(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}),__decorate=function(e,t,n,i){var o,s=arguments.length,r=s<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(r=(s<3?o(r):s>3?o(t,n,r):o(t,n))||r);return s>3&&r&&Object.defineProperty(t,n,r),r},r=function(e){function Texts(){return null!==e&&e.apply(this,arguments)||this}return s(Texts,e),Texts}(o.d),a=function(e){function ClassList(){return null!==e&&e.apply(this,arguments)||this}return s(ClassList,e),ClassList}(o.d),l=function(e){function Selectors(){var t=null!==e&&e.apply(this,arguments)||this;return t.ERPForm=".ERP-acceptance form",t.submitBtn=".ERP-acceptance .agree-submit",t.checkBoxTandC=".ERP-acceptance .ERP-TandC",t.checkboxContainer=".ERP-acceptance .checkbox--primary",t.errorContainer=".ERP-acceptance .checkbox--primary span",t.redirectUriInput='.ERP-acceptance input[name="redirectUri"]',t.cancelTandC="#cancelTandC",t}return s(Selectors,e),Selectors}(o.f),c=function(e){function Elements(){var t=null!==e&&e.apply(this,arguments)||this;return t.checkBoxTandC=null,t.checkboxContainers=null,t}return s(Elements,e),Elements}(o.b),d=function(){function ERPAcceptance(){}return ERPAcceptance.prototype.initialize=function(){this.setElements(),this.setRedirectUri(),this.addEventListeners()},ERPAcceptance.prototype.setElements=function(){this.elements.ERPForm=this.domUtils.getElement(this.selectors.ERPForm),this.elements.submitBtn=this.domUtils.getElement(this.selectors.submitBtn),this.elements.checkBoxTandC=this.domUtils.getElements(this.selectors.checkBoxTandC),this.elements.checkboxContainers=this.domUtils.getElements(this.selectors.checkboxContainer)},ERPAcceptance.prototype.addEventListeners=function(){var e,t,n=this;null===(e=this.elements.submitBtn)||void 0===e||e.addEventListener("click",(function(e){e.preventDefault(),n.handleSubmit()})),null===(t=this.elements.checkBoxTandC)||void 0===t||t.forEach((function(e){e.addEventListener("change",(function(e){n.checkBoxesChecker(e)}))}))},ERPAcceptance.prototype.setRedirectUri=function(){var e=this.findGetParameter();if(e){var t=document.querySelector(this.selectors.redirectUriInput),n=document.querySelector(this.selectors.cancelTandC);null==t||t.setAttribute("value",e),null==n||n.setAttribute("href","/action/doLogout?redirectUri="+e)}},ERPAcceptance.prototype.checkBoxesChecker=function(e){var t,n=e.target.parentElement;if(n){var i=n.querySelector(".invalid-feedback");t=n.querySelector("span"),n.classList.remove("error"),null==i||i.remove()}t&&t.classList.remove("error")},ERPAcceptance.prototype.handleSubmit=function(){var e,t,n=null===(e=this.elements.ERPForm)||void 0===e?void 0:e.querySelectorAll('input[type="checkbox"]:not(:checked, .optional)');n&&0===n.length?null===(t=this.elements.ERPForm)||void 0===t||t.submit():null==n||n.forEach((function(e){var t=e.parentElement,n=null==t?void 0:t.querySelector("span");if(t&&!t.querySelector(".invalid-feedback")){var i=document.createElement("p");i.classList.add("invalid-feedback"),i.textContent="You must accept to continue.",null==n||n.classList.add("error"),t.classList.add("error"),t.appendChild(i)}}))},ERPAcceptance.prototype.findGetParameter=function(){var e=window.location.search.split("redirectUri=");return(null==e?void 0:e.length)>1&&decodeURIComponent(e[1])},ERPAcceptance=__decorate([Object(o.c)(l,c,a,r)],ERPAcceptance)}();t.a=d},232:function(e,t,n){"use strict";n(233);var i=n(60);UX.accordion.hideDropBlock=!1,UX.accordion.additionalControls=function(){$(".accordion__control").on("keydown touchend",(function(e){if("keydown"===e.type){if(e.keyCode&&"Enter"!==e.keyCode&&32!==e.keyCode)return;32===e.keyCode&&e.preventDefault(),UX.accordion.on.toggle.single($(this))}}));var e=document.querySelector(".article-section__citedBy");if(e){var t=e.dataset.source;e.querySelector(".accordion__control").addEventListener("click",(function getCitedByContent(){var n=null,o=e.querySelectorAll('.sciteMetricsDropzone, [data-pb-dropzone="sciteMetricsDropzone"]'),s=e.querySelector(".citedByContentInner");o.length&&(n=setInterval((function toolTipChecker(){e.querySelector(".scite-metrics .tooltip")&&(UX.tooltip?UX.tooltip.reinitialize():UX.tooltip=(new i.a).initialize(),clearInterval(n))}),500)),s&&""===s.textContent&&(s.innerHTML='<div class="loading-content"><span>loading citations</span><div class="loading"></div></div>',fetch(t).then((function(e){return e.text()})).then((function(e){s.innerHTML=e})))}),{once:!0})}$(".accordion").on("click",'[data-type="ajax"]',(function(){var e=$(this).parents(".accordion").find("[data-ajax-content]"),t=e.data("ajax-content");e.hasClass("js--loader")||(e.addClass("js--loader").html('<div class="accordion__loading"></div>'),$.ajax(t,{success:function success(t){e.html(t)}}))})),$(document).on("keydown",".subjects-container .accordion-tabbed__control",(function(e){"Space"!==e.code&&"Enter"!==e.code||(e.preventDefault(),UX.accordion.on.toggle.tabbed($(this)))})),$('.accordion__control.corrections-label[data-slide-target=".corrections-body"]').on("click",(function(e){if(UX.loader.isMobile){e.stopImmediatePropagation(),e.preventDefault();var t=$(this);UX.accordion.on.toggle.single(t)}}))},UX.accordion.on.toggle.single=function(e){var t=e.parent().next(".accordion__content").length?e.parent().next(".accordion__content"):e.next(".accordion__content"),n=e.closest(".expandable-accordion"),i=!("true"===e.attr("aria-expanded"));if(void 0!==e.attr("data-content-target")){var o=e.attr("data-content-target");t=$(o)}t.stop(!0).slideToggle(200,(function(){e.parents().hasClass("article-accordion")&&($(".article-row-left").height("auto"),UX.accordion.isStickyBody=i)})),e.toggleClass("js--open"),e.attr("aria-expanded",i),n.length&&UX.accordion.on.checkExpandAll(e,i,n)}},235:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(8),o=n.n(i),s=n(9),r=n.n(s),a=function(){return r()((function Registration(){var e=this;o()(this,Registration),this.selectors={errMsg:".registration span.message, .registration label.error span , .registration .captcha-form .error ",registrationSubmitted:".registration-completed",registrationCompleted:"[registrationCompleted = true]"},this.elements={errMsg:null,registrationSubmitted:null,registrationCompleted:null},this.variables={navigationEntries:window.performance.getEntriesByType("navigation")},this.errMsgsHandler=function(){var t=[];e.elements.errMsg.forEach((function(e){var n,i,o=null===(n=e.closest("label"))||void 0===n?void 0:n.textContent,s=null===(i=e.textContent)||void 0===i?void 0:i.replaceAll("-","").trim(),r="".concat(o||""," ").concat(s).replaceAll("*","");t.push(r)}));var n=t.toString().replaceAll(","," ");if(UX.utils.screenReaderSpeak("Your registration cannot be completed. ".concat(n)),window.adobeDataLayer){var i=window.performance.getEntriesByType("navigation");i.length>0&&"navigate"===i[0].type&&UX.adobeDigitalData.registrationEvents.error(t.toString())}},this.RegistrationEvents=function(){window.adobeDataLayer&&e.elements.registrationSubmitted&&UX.adobeDigitalData.registrationEvents.submitted(),window.adobeDataLayer&&e.elements.registrationCompleted&&UX.adobeDigitalData.registrationEvents.completed()},this.init()}),[{key:"init",value:function init(){this.setElements(),this.eventListeners(),this.RegistrationEvents()}},{key:"setElements",value:function setElements(){this.elements.errMsg=document.querySelectorAll(this.selectors.errMsg),this.elements.registrationSubmitted=document.querySelector(this.selectors.registrationSubmitted),this.elements.registrationCompleted=document.querySelector(this.selectors.registrationCompleted)}},{key:"eventListeners",value:function eventListeners(){this.elements.errMsg.length&&window.addEventListener("load",this.errMsgsHandler)}}])}()},236:function(e,t){var n,i;n=document.querySelector(".citation-content"),i=document.querySelector("mjx-container"),n&&(i?UX.utils.fallbackImageViewer():UX.utils.mutationObserver("mjx-container",{childList:!0,subtree:!0},UX.utils.fallbackImageViewer,null,!0))},237:function(e,t){var n;n={init:function init(){n.control()},control:function control(){UX.resizeImages(".multi-search"),n.hideMoreLinks(),n.imageResize(),UX.fancybox&&n.fancybox()},imageResize:function imageResize(){var e=document.querySelector(".multi-search");if(e){var t=UX.utils.convertToArray(null==e?void 0:e.querySelectorAll(".abstract-preview img")),imageResize=function imageResize(e){return UX.resizeImages(e.target.parentElement)};t.length&&t.forEach((function(e){UX.resizeImages(e.parentElement),e.addEventListener("load",imageResize)}))}},fancybox:function fancybox(){var e=UX.utils.convertToArray(document.querySelectorAll(".abstract-preview__zoom"));UX.fancybox(e),document.addEventListener("keydown",UX.fancyboxClose)},hideMoreLinks:function hideMoreLinks(){var e=$(".multi-search"),t=e.find(".multi-search__list"),n=e.find(".multi-search--empty"),i=e.find(".multi-search__no-result"),o=$(".multi-search__more-links");(t.hasClass("hideMoreLinks")||i.length)&&o.length&&o.remove(),n.length&&(n.closest(".multi-search").prev(".multi-search__header").remove(),o.remove())}},UX.multiSearch=n},238:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(8),o=n.n(i),s=n(9),r=n.n(s),a=function(){return r()((function Shibboleth(){var e=this;o()(this,Shibboleth),this.classList={active:"js--active",loading:"js--loading-wrapper",updateInstitutionEntity:"ghaith"},this.content={loadingEL:'<div class="js--loading-wrapper" role="alert"><div class="js--loading"></div><div class="sr-only">loading institution list</div></div>',contentLoadedEL:'<div role="alert">institution list loaded</div>'},this.selectors={institutions:".institutions",institution:".institution-list",federationSelect:".federation-select",federationSelectContent:".federation-select__content",federationSelectContentLi:"li[data-index-target]",loading:".js--loading-wrapper",federationAlert:".federation-alert"},this.elements={institutions:null,federationSelect:null,federationSelectContent:null,federationSelectContentLi:null,federationAlert:null},this.selectChangeHandlers=function(t){var n=t.target.selectedIndex,i=e.elements.federationSelectContent.querySelector('li[data-index-target="'.concat(n,'"]')),o=null==i?void 0:i.querySelector(e.selectors.institution),s=e.elements.federationSelectContent.querySelector("li.js--active[data-index-target]"),r=e.findGetParameter("redirectUri");if(null==s||s.classList.remove(e.classList.active),i.classList.add(e.classList.active),!o&&"0"!==i.dataset.indexTarget){e.showLoading();var a="".concat("/pb/widgets/shibboleth/getFederationInstitutions?federation=").concat(encodeURIComponent(t.target.value)).concat(null!==r?"&redirectUrl=".concat(r):"");fetch(a).then((function(e){return e.json()})).then((function(t){i.insertAdjacentHTML("beforeend",e.createInstitutionsList(t)),e.hideLoading()}))}},this.updateInstitutionEntity=function(t){if(t.target.classList.contains(e.classList.updateInstitutionEntity)||t.target.parentElement.classList.contains(e.classList.updateInstitutionEntity)){t.preventDefault();var n=t.target.classList.contains(e.classList.updateInstitutionEntity)?t.target:t.target.parentElement,i=JSON.parse(n.dataset.updateentity),o=n.dataset.link;SeamlessAccessService.updateEntity(i).then((function(e){void 0!==o&&(window.location.href=o)})).catch((function(e){void 0!==o&&(console.log(e),window.location.href=o)}))}},this.createInstitutionsList=function(e){var t="",n=0;for(n=0;n<e.length;n++){var i=e[n],o={entityID:i.entityId,title:i.name},s=JSON.stringify(o).replace(/'/g,"&apos;");t+='\n                <li data-value="'.concat(i.name,'">\n                    <a class="ghaith" data-entityid="').concat(i.entityId,'" data-name="').concat(i.entityId,"\" data-updateEntity='").concat(s,"' data-link=\"").concat(i.url,'" href="javascript:void(0)">\n                        ').concat(i.name,"\n                    </a>\n                </li>\n            ")}return'<ul id="institution-'.concat(n,'" class="institution-list">').concat(t,"</ul>")},this.findGetParameter=function(e){var t=null,n=[];return location.search.substr(1).split("&").forEach((function(i){(n=i.split("="))[0]===e&&(t=n[1])})),t},this.showLoading=function(){return e.elements.institutions.insertAdjacentHTML("afterbegin",e.content.loadingEL)},this.hideLoading=function(){var t=e.elements.institutions.querySelector(e.selectors.loading);t&&(t.remove(),e.elements.federationAlert.innerHTML=e.content.contentLoadedEL,setTimeout((function(){e.elements.federationAlert.innerHTML=""}),3e3))},this.elements.sction=document.querySelector(".shibboleth"),this.setElements(),this.eventListeners(),this.onReady()}),[{key:"setElements",value:function setElements(){this.elements.institutions=this.elements.sction.querySelector(this.selectors.institutions),this.elements.federationSelect=this.elements.sction.querySelector(this.selectors.federationSelect),this.elements.federationSelectContent=this.elements.sction.querySelector(this.selectors.federationSelectContent),this.elements.federationSelectContentLi=this.elements.federationSelectContent.querySelectorAll(this.selectors.federationSelectContentLi),this.elements.federationAlert=this.elements.sction.querySelector(this.selectors.federationAlert)}},{key:"onReady",value:function onReady(){}},{key:"eventListeners",value:function eventListeners(){this.elements.federationSelect.addEventListener("change",this.selectChangeHandlers),this.elements.federationSelectContent.addEventListener("click",this.updateInstitutionEntity)}}])}()},239:function(e,t){$(document).ready((function(){var e="",t="",n="",i="",o="",s="";$(".addresses").length&&(e=$(".addresses").closest("#personalInformationForm").find(".form-btn"),t=$(".addresses").closest("#personalInformationForm").find(".g-recaptcha"),n=$(".addresses").closest("#personalInformationForm").find(".infoBox .error"),i=$(".switch-address").val(),o=$(".mailing-list-toggle"),s=$(".mailing-list-wrapper"),o.prop("checked")&&s.addClass("js--open"),o.on("change",(function(){o.prop("checked")?s.addClass("js--open"):s.removeClass("js--open")})),!i||n.length?(e.show(),t.removeClass("hidden")):(e.hide(),t.addClass("hidden")),$(".switch-address").on("change",(function(){$(".switch-address").val()?(e.hide(),t.addClass("hidden")):(e.show(),t.removeClass("hidden"))})),$(".edit-address").on("click",(function(){$(".switch-address").val()?(e.show(),t.removeClass("hidden")):(e.hide(),t.addClass("hidden"))})))}));var n=function showErrorBox(){var e=document.querySelectorAll(".label.error");if(e.length>0){var t=document.querySelector("fieldset .infoBox"),n=document.createElement("ul"),i=[];n.setAttribute("id","errorSummaryBox"),e.forEach((function(e){var t=((null==e?void 0:e.querySelector("label").innerText)+(null==e?void 0:e.querySelector(".message").innerText)).replace(/\*/g,""),o=null==e?void 0:e.nextElementSibling.getAttribute("id"),s=document.createElement("li");null==i||i.push(o),s.setAttribute("tabindex","0"),s.innerText=t,n.append(s)})),null==t||t.append(n),null==t||t.setAttribute("tabindex","0"),null==t||t.focus(),function errorFocusController(e,t){var n,i=document.getElementById("personalInformationForm"),o=[],s=document.querySelector(".g-recaptcha iframe"),r=null==i?void 0:i.querySelector(".actions a.linkBtn"),a=null==i?void 0:i.querySelector(".actions input[type=submit]"),l=null===(n=document.getElementById("address"))||void 0===n?void 0:n.querySelectorAll('input[id^="address"], select[id^="address"]:not([disabled="disabled"])'),c=function focusController(e,t){null==e||e.forEach((function(e){var n=null==e?void 0:e.previousElementSibling;n&&n.classList.contains("label")&&(e.setAttribute("tabindex",t&&!n.classList.contains("error")||n.classList.contains("error")?"0":"-1"),n.classList.contains("error")&&o.push(e))})),null==s||s.setAttribute("tabindex","0")};c(l),null==o||o.push(s,r,a);var d=null==t?void 0:t.querySelector("li:last-child"),u=function handleTabKey(e){if("Tab"===e.key){var t;e.preventDefault();var n=(null==o?void 0:o.findIndex((function(e){return document.activeElement===e})))+(e.shiftKey?-1:1);n<0?n=(null==o?void 0:o.length)-1:n>=(null==o?void 0:o.length)&&(n=0),null===(t=o[n])||void 0===t||t.focus()}else"Tab"!==e.key&&(c(l,1),document.removeEventListener("keydown",u))};null==d||d.addEventListener("focus",(function(){document.addEventListener("keydown",u)}))}(0,n)}};$(window).load((function(){var e=$(".addresses-accordion .accordion__control"),t=document.getElementById("address");$(".accordion__content #address .label").hasClass("error")&&UX.accordion.on.toggle.single(e),t&&n()}))},240:function(e,t){$(document).ready((function(){var e,t="",n="";$('.raaAccountInfo input[name="marketable.value"]').on("change",(function(e){$('input[name="marketable.value"]').attr("checked",!1),$(this).attr("checked",!0),t=$(this).parents(".marketable")[0].outerHTML,n=$(".raaAccountInfo").find("form"),n.find(".formDropZone").append('<div class="hidden">'+t+"</div>")})),$("#institutionList").change((function(){window.location.href=$(this).val()})),(e=$("#pane-series")).length>0&&function paginate(e){var t=e.find(".table-responsive"),n=t.find("tbody tr"),i="",o=0,s=null;n.length>4&&(t.addClass("js-pages"),i='<div class="pagination js-pager">\n            <ul class="rlist--inline pagination__list">\n                 <li><a href="#" data-target-page="1" title="1" class="active">1</a></li>\n',t.append(i),o=1,n.each((function(e){var n=parseInt(e/4)+1;$(this).attr("data-page",n),n>o&&(s='<li><a href="#" data-target-page="'+n+'" title="'+n+'">'+n+"</a></li>",t.find(".js-pager ul").append(s),o=n)})),UX.pager.$pages=$(".js-pages"),UX.pager.$pager=$(".js-pager"),UX.pager.init())}(e);var i=document.querySelector(".raaAccountInfo .infoBox");i&&(i.setAttribute("tabindex","-1"),i.focus())}))},243:function(e,t,n){"use strict";n(244);var i=n(60);UX.slide.vPort="screen-md",UX.slide.on.destroyJcf=function(e){},UX.slide.on.rebuildJcf=function(e){},UX.slide.on.hide=function(e){var t=$("body"),n=$(e.target).closest(".w-slide").length,i=$.Event("slide:close");if($(document).trigger(i,[UX.slide]),!i.isDefaultPrevented()){if(void 0!==e&&$(".w-slide").hasClass("js--open")&&(n||(UX.slide.back=!0)),UX.slide.back){UX.slide.addTransition(),UX.slide.$slideinfo.hide().removeClass("js--open"),UX.slide.nested=!0,0===UX.slide.activeSlide?(UX.slide.nested=!1,UX.slide.$contents=UX.slide.$elements.children(".w-slide__content")):UX.slide.$contents=$(".nested--"+UX.slide.activeSlide),UX.slide.on.destroyJcf(UX.slide.$contents);var o=$(UX.slide.$contents.attr("original-target"));o||(o=t);var s=UX.slide.$elements.find(".loi").length>0;if(UX.slide.on.beforeReturnSlideContent(),s){var r=$(".dropBlock-loi__holder");r.append(UX.slide.$contents.html()),r.find(".loi__list").each((function(){UX.loi.on.recalculate($(this))}))}else{if(UX.slide.$contents.find("[class*=nested--]").length&&UX.slide.$contents.find("[class*=nested--]").remove(),"self"===UX.slide.clone)if(UX.slide.nested||UX.slide.multi){var a=$(".returnNestedDataSlideHere");a.after(UX.slide.$contents.html()),a.remove()}else{var l=$(".returnDataSlideHere");l.after(UX.slide.$contents.html()),l.remove()}else UX.slide.nested||UX.slide.multi?($(".returnNestedDataSlideHere",o).after(UX.slide.$contents.html()),$(".returnNestedDataSlideHere",o).remove()):($(".returnDataSlideHere",o).after(UX.slide.$contents.html()),$(".returnDataSlideHere",o).remove());UX.slide.on.rebuildJcf(o)}if(UX.slide.oldInfo.splice(UX.slide.activeSlide,1),UX.slide.$slideinfo.html(UX.slide.oldInfo[UX.slide.activeSlide-1]),UX.slide.nested)UX.slide.activeSlide--,UX.slide.on._hideSlide(),UX.slide.multi&&UX.slide.$contents.remove(),UX.slide.$contents=$(".nested--"+UX.slide.activeSlide),0===UX.slide.activeSlide&&(UX.slide.$contents=UX.slide.$elements.children(".w-slide__content"));else{UX.slide.activeSlide=0,$("body, html").removeClass("lock-screen"),t.removeClass("lock-screen slide-is-active").removeAttr("data-active");var c=$(".coolBar");c.length&&c.removeClass("slide"),UX.slide.on._hideSlide(),UX.slide.$elements.removeClass("js--open")}if(UX.slide.$slideinfo.show().addClass("js--open"),UX.slide.backLabel="back",UX.slide.setBackLabel(),UX.slide.on.additionalHide(e),UX.slide.$elements.hasClass("w-slide__pill")){for(;UX.slide.activeSlide>0;)UX.slide.activeSlide--,UX.slide.on.hide(e);void 0!==e&&($(e.target).hasClass("w-slide__back")||$(e.currentTarget).hasClass("w-slide__back"))&&0===UX.slide.activeSlide&&UX.slide.on.off(e)}n||void 0!==e&&$(e.target).closest(".pill-list").length||UX.slide.on.off(e),UX.slide.removeTransition()}"undefined"!=typeof googletag&&void 0!==googletag.pubads&&googletag.pubads().refresh();var d=$.Event("slide:closed");$(document).trigger(d,[UX.slide])}},UX.slide.displaySlideHtmlToPage=function(){UX.slide.$slide='\n        <div class="w-slide">\n            <div class="w-slide_head">\n                <a href="javascript:void(0)" class="w-slide__back">\n                    <i class=" icon-arrow_l" aria-hidden="true"></i>\n                    <span>'.concat(UX.slide.backLabel,'</span>\n                </a>\n                <span class="w-slide__title"></span>\n            </div>\n            <div class="w-slide__content"></div>\n        </div>\n    '),UX.slide.$elements=$(UX.slide.$slide);var e=$("body"),t=$("main");t.length>0?t.append(UX.slide.$elements):e.append(UX.slide.$elements)},UX.slide.control=function(){var e=$("body");e.on("click",".w-slide__btn",(function(e){if(!e.currentTarget.getAttribute("href").includes("/")){UX.slide.isMobile&&$(this).addClass("slide-active");var t=$(this);if(UX.slide.isQueue&&UX.slide.isMobile)return e.stopImmediatePropagation(),$(this).off("click"),!1;if(UX.slide.isQueue=!0,$(this).hasClass("disable"))return e.stopPropagation(),void e.preventDefault();$(this).hasClass("slide-active")&&(e.stopPropagation(),e.preventDefault(),UX.slide.addTransition(),UX.slide.$toggle=$(this),UX.slide.$target=$(UX.slide.$toggle.data("slide-target")),UX.slide.back=!1,t.closest("li").addClass("js--slide").siblings().removeClass("js--slide"),t.is("[id*=pane-pcw]")&&UX.loader.isArticleTabEmpty($(this))?(t.closest("[data-db-target-of]").addClass("js--loading").focus(),setTimeout((function(){t.closest('[data-db-target-of="article"]').removeClass("js--loading"),$(UX.loader.target).removeClass("empty"),UX.slide.on.show(e)}),3e3)):(UX.slide.on.show(e),document.querySelector(".tooltip")&&(UX.tooltip?UX.tooltip.reinitialize():UX.tooltip=(new i.a).initialize())))}})),e.on("click",".w-slide__back, .w-slide__hide",(function(e){if(!e.target.closest(".sections__drop.rlist.separator")){var t=$(this);$(".js--slide").length&&!t.hasClass("js--bib")?(!$('[data-db-target-for="article"]').hasClass("js--open")&&$('[data-db-target-for="article"]').trigger("click"),setTimeout((function(){$(".js--slide").find("a").eq(0).focus()}),250)):$(".js--scrollTab").length&&setTimeout((function(){$(".js--scrollTab").focus()}),230),t.removeClass("js--bib"),UX.slide.back=!0,UX.slide.on.hide(e)}}))},UX.slide.on.additionalShowSlide=function(){var e=$(".js--scrollTab").attr("href"),t=$("".concat(e));if(UX.tab.isMobile&&UX.tab.isMobile[0]){var n=$(".w-slide__content");UX.tab.animate.scroll(n,t)}}},247:function(e,t,n){"use strict";var i;n(213);(i=UX.sticko).get.fixedpageElementsHeight=function(){var e=$("header, .pageHeader").outerHeight();return e||(e=0),e},i.set.height=function(e,t){var n=window.pageYOffset+$(window).outerHeight(),o=$(".page-footer").outerHeight(),s=$(document).outerHeight()-o,r={};n>=s?(e=n-s,i.newHeight=t-e,r={height:i.newHeight}):r={height:t},i.apply(i.$child,r);var a=i.get.fixedpageElementsHeight();$(".article-row-left").outerHeight()<$(window).outerHeight()&&(UX.accordion.isArticleAccordion||$(".article-row-left").height($(window).outerHeight()-a)),$(".dynamic-sticko").length&&$(".dynamic-sticko").each((function(){var t=$(this);t.css({position:"static","padding-bottom":""});var i=$(window).scrollTop()+a,o=$(window).outerHeight()-a,r=o;t.find(".tab__nav").length&&(r-=t.find(".tab__nav").outerHeight()),n>=s?(e=n-s,t.css("height",o-e),$(".sticko__child").css("height",r-e)):(t.css("height",o),$(".sticko__child").css("height",r)),i>=t.offset().top&&($(".sticko__parent").css("padding-bottom",o),t.css({position:"fixed",top:a}))}))}},248:function(e,t,n){"use strict";var i;n(196);i={sections:null,holder:$(".coolBar--sections .coolBar__drop"),vPort:"screen-sm",isMobile:!1,coolbarElement:null,headerHeight:0,isStickyBar:!1,lock:!0,eventPage:document.querySelector(".event__content"),init:function init(){i.vPort="screen-md",i.coolbarElement=$(".coolBar"),i.isStickyBar=i.coolbarElement.hasClass("stickybar"),i.get.sections(),i.fill.sections(),i.eventPage||i.control(),i.addtionalControls()},control:function control(){$(".journal-home").length&&(i.vPort="screen-md"),$(document).on(i.vPort+"-on",(function(){$(".coolBar").addClass("coolBar--res"),i.isMobile=!0,$(".stickybar.coolBar--res,.coolBar.coolBar--res").length>0&&$(".content>article.container").addClass("article--res")})),$(document).on(i.vPort+"-off",(function(){$(".coolBar").removeClass("coolBar--res"),$(".content article.container").removeClass("article--res"),$(document).trigger("eventSetContentPadding"),i.isMobile=!1,void 0!==UX.controller&&UX.controller.check()}))},addtionalControls:function addtionalControls(){var e=document.querySelector(".coolBar__section #sections_Pop ul");e&&e.classList.remove("w-slide--list")},get:{sections:function sections(){i.sections=$('.article__content .article-section:not([style="display: none;"]) .section__title')}},fill:{sections:function sections(){if($(".coolBar--sections").length&&UX.coolbar.sections.length>1){$(".coolBar--sections").removeClass("hidden");var e=$('<ul class="rlist w-slide--list"/>').appendTo(UX.coolbar.holder);UX.coolbar.sections.each((function(){var t=$(this).clone(),n=$("<li/>").appendTo(e),i=t.attr("id"),o=$('<a class="w-slide__hide"/>').attr("href","#"+i).appendTo(n).on("mousedown",(function(e){e.preventDefault()}));t.find(".section-footNote").remove(),t[0].querySelector("math")?$("<span/>").html(t.html()).appendTo(o):$("<span/>").text(t.text()).appendTo(o)}))}else $(".coolBar--sections").remove();$(".authorArticleInfoCon").length&&$(".coolBar--sections .coolBar__drop").append('<li role="menuitem"><a class="w-slide__hide" href="#_ac_authorArticleInfoCon" target="_self"><span>Author and Article Information</span></a></li>')}}},UX.coolbar=i},249:function(e,t,n){"use strict";var i;n(196);i={vPort:"screen-md",stickybarElement:$(".coolBar"),topPostition:0,lock:!0,init:function init(){i.control()},control:function control(){$(document).on(i.vPort+"-on",(function(){i.sticky.position()})),$(document).on(i.vPort+"-off",(function(){i.sticky.position()}))},headerHeight:function headerHeight(){$(".pageHeader").height()},sticky:{position:function position(){i.stickybarElement.css({top:i.headerHeight()})}}},UX.setStickeyTop=i},250:function(e,t,n){"use strict";n(251),n(213);UX.sticko.ads=function(){UX.sticko.$element.closest(".sticko-ads").length&&UX.sticko.$element.css({right:($(document).width()-$(".container").width())/2-320,left:"auto"});var e=$(".sticko-ads").find(".sticko__md"),t=$(".pageHeader").outerHeight(),n=0,i=$(".stickybar__wrapper");i.length&&(n=i.outerHeight());var o=n+t;e.css("top",o)}},252:function(e,t,n){"use strict";var __assign=function(){return(__assign=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},i=function i(){var e,t,n,i,o,s=this;this.publicDir="/specs/products/acropolis/pericles/releasedAssets/public",this.config={chtml:{mathmlSpacing:!0,scale:.85},options:{enableEnrichment:!0,a11y:{speech:!0,braille:!0},menuOptions:{settings:{assistiveMml:!0,collapsible:!1,explorer:!0}}}},this.startupConfig='{\n        pageReady: function () {\n            return MathJax.startup.defaultPageReady().then(() => {\n                document.dispatchEvent(new Event("mathjax-initilized"))\n            });\n          }\n    }',this.elements={searchResult:document.querySelector("#search-result"),noteListEles:null===(e=document.querySelector(".noteList-container .noteList"))||void 0===e?void 0:e.children,head:document.head,mathJaxSrc:document.createElement("script"),mathJaxConfig:document.createElement("script"),mathJaxStartup:document.createElement("script"),versionFlag:null===(t=document.querySelector('meta[name="mathjax-version"]'))||void 0===t?void 0:t.getAttribute("content"),lazyLoadFlag:null===(n=document.querySelector('meta[name="mathjax-lazy-load"]'))||void 0===n?void 0:n.getAttribute("content"),enableFlag:null===(i=document.querySelector('meta[name="mathjax-enabled"]'))||void 0===i?void 0:i.getAttribute("content")},this.initilize=function(){try{window.opera?(s.elements.mathJaxConfig.innerHTML="window.MathJax = "+JSON.stringify(s.config),s.elements.mathJaxStartup.innerHTML="window.MathJax.startup = "+s.startupConfig):(s.elements.mathJaxConfig.text="window.MathJax = "+JSON.stringify(s.config),s.elements.mathJaxStartup.text="window.MathJax.startup = "+s.startupConfig),s.elements.head.appendChild(s.elements.mathJaxConfig),s.elements.head.appendChild(s.elements.mathJaxStartup),s.elements.mathJaxSrc.type="text/javascript",s.elements.mathJaxSrc.defer=!0,s.elements.mathJaxSrc.src=s.src,s.elements.head.appendChild(s.elements.mathJaxSrc)}catch(e){console.error("something went wrong when importing MathJax :",e)}},"4"===this.elements.versionFlag?(this.src=this.publicDir+"/mathjax4-511ffcc670c4d93d695bf67802ba7aad/tex-mml-chtml.js",this.config=__assign(__assign({},this.config),{chtml:__assign(__assign({},this.config.chtml),{fontURL:this.publicDir+"/mathjax-fonts-31e8ac2eab6e3c56af62ee2c890d123c"})})):this.src=this.publicDir+"/mathjax3-d7fcb9c491ef5b1034c519769bcb10e3/es5/tex-mml-chtml.js","true"!==this.elements.lazyLoadFlag||null!==this.elements.searchResult||(null===(o=this.elements.noteListEles)||void 0===o?void 0:o.length)||(this.config=__assign(__assign({},this.config),{loader:__assign(__assign({},this.config.loader),{load:["ui/lazy"]})})),"true"===this.elements.enableFlag&&this.initilize()};t.a=i}}]);
//# sourceMappingURL=lazy-imports-a326eaeb8c79094e87ef.js.map