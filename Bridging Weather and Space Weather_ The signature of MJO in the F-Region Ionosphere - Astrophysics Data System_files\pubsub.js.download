define(["backbone","underscore","js/mixins/hardened","pubsub_service_impl","js/components/pubsub_events"],function(e,r,n,t,c){t=t.extend({hardenedInterface:{subscribe:"register callback",unsubscribe:"deregister callback",publish:"send data to the queue",getPubSubKey:"get secret key"}});return r.extend(t.prototype,n,{getHardenedInstance:function(e){e=r.clone(e||this.hardenedInterface);var n={key:this.getPubSubKey()},t=this,e=(e.publish=function(){t.publish.apply(t,[n.key].concat(r.toArray(arguments)))},e.subscribe=function(){t.subscribe.apply(t,[n.key].concat(r.toArray(arguments)))},e.unsubscribe=function(){t.unsubscribe.apply(t,[n.key].concat(r.toArray(arguments)))},e.subscribeOnce=function(){t.subscribeOnce.apply(t,[n.key].concat(r.toArray(arguments)))},e.getCurrentPubSubKey=function(){return n.key},this._getHardenedInstance(e,this));return r.extend(e,c),e}}),r.extend(t.prototype,c),t});
//# sourceMappingURL=pubsub.js.map