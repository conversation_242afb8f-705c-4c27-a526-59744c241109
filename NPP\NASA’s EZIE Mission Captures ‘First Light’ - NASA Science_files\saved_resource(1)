/*! jQuery v3.7.1 | (c) OpenJS Foundation and other contributors | jquery.org/license */
!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(ie,e){"use strict";var oe=[],r=Object.getPrototypeOf,ae=oe.slice,g=oe.flat?function(e){return oe.flat.call(e)}:function(e){return oe.concat.apply([],e)},s=oe.push,se=oe.indexOf,n={},i=n.toString,ue=n.hasOwnProperty,o=ue.toString,a=o.call(Object),le={},v=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},y=function(e){return null!=e&&e===e.window},C=ie.document,u={type:!0,src:!0,nonce:!0,noModule:!0};function m(e,t,n){var r,i,o=(n=n||C).createElement("script");if(o.text=e,t)for(r in u)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function x(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?n[i.call(e)]||"object":typeof e}var t="3.7.1",l=/HTML$/i,ce=function(e,t){return new ce.fn.init(e,t)};function c(e){var t=!!e&&"length"in e&&e.length,n=x(e);return!v(e)&&!y(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}function fe(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}ce.fn=ce.prototype={jquery:t,constructor:ce,length:0,toArray:function(){return ae.call(this)},get:function(e){return null==e?ae.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=ce.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return ce.each(this,e)},map:function(n){return this.pushStack(ce.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(ae.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(ce.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(ce.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(0<=n&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:s,sort:oe.sort,splice:oe.splice},ce.extend=ce.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||v(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],"__proto__"!==t&&a!==r&&(l&&r&&(ce.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||ce.isPlainObject(n)?n:{},i=!1,a[t]=ce.extend(l,o,r)):void 0!==r&&(a[t]=r));return a},ce.extend({expando:"jQuery"+(t+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==i.call(e))&&(!(t=r(e))||"function"==typeof(n=ue.call(t,"constructor")&&t.constructor)&&o.call(n)===a)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){m(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(c(e)){for(n=e.length;r<n;r++)if(!1===t.call(e[r],r,e[r]))break}else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)while(t=e[r++])n+=ce.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(c(Object(e))?ce.merge(n,"string"==typeof e?[e]:e):s.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:se.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!l.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!==a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(c(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return g(a)},guid:1,support:le}),"function"==typeof Symbol&&(ce.fn[Symbol.iterator]=oe[Symbol.iterator]),ce.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){n["[object "+t+"]"]=t.toLowerCase()});var pe=oe.pop,de=oe.sort,he=oe.splice,ge="[\\x20\\t\\r\\n\\f]",ve=new RegExp("^"+ge+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ge+"+$","g");ce.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var f=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function p(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}ce.escapeSelector=function(e){return(e+"").replace(f,p)};var ye=C,me=s;!function(){var e,b,w,o,a,T,r,C,d,i,k=me,S=ce.expando,E=0,n=0,s=W(),c=W(),u=W(),h=W(),l=function(e,t){return e===t&&(a=!0),0},f="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",t="(?:\\\\[\\da-fA-F]{1,6}"+ge+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",p="\\["+ge+"*("+t+")(?:"+ge+"*([*^$|!~]?=)"+ge+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+t+"))|)"+ge+"*\\]",g=":("+t+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+p+")*)|.*)\\)|)",v=new RegExp(ge+"+","g"),y=new RegExp("^"+ge+"*,"+ge+"*"),m=new RegExp("^"+ge+"*([>+~]|"+ge+")"+ge+"*"),x=new RegExp(ge+"|>"),j=new RegExp(g),A=new RegExp("^"+t+"$"),D={ID:new RegExp("^#("+t+")"),CLASS:new RegExp("^\\.("+t+")"),TAG:new RegExp("^("+t+"|[*])"),ATTR:new RegExp("^"+p),PSEUDO:new RegExp("^"+g),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ge+"*(even|odd|(([+-]|)(\\d*)n|)"+ge+"*(?:([+-]|)"+ge+"*(\\d+)|))"+ge+"*\\)|)","i"),bool:new RegExp("^(?:"+f+")$","i"),needsContext:new RegExp("^"+ge+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ge+"*((?:-\\d)?\\d*)"+ge+"*\\)|)(?=[^-]|$)","i")},N=/^(?:input|select|textarea|button)$/i,q=/^h\d$/i,L=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,H=/[+~]/,O=new RegExp("\\\\[\\da-fA-F]{1,6}"+ge+"?|\\\\([^\\r\\n\\f])","g"),P=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},M=function(){V()},R=J(function(e){return!0===e.disabled&&fe(e,"fieldset")},{dir:"parentNode",next:"legend"});try{k.apply(oe=ae.call(ye.childNodes),ye.childNodes),oe[ye.childNodes.length].nodeType}catch(e){k={apply:function(e,t){me.apply(e,ae.call(t))},call:function(e){me.apply(e,ae.call(arguments,1))}}}function I(t,e,n,r){var i,o,a,s,u,l,c,f=e&&e.ownerDocument,p=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==p&&9!==p&&11!==p)return n;if(!r&&(V(e),e=e||T,C)){if(11!==p&&(u=L.exec(t)))if(i=u[1]){if(9===p){if(!(a=e.getElementById(i)))return n;if(a.id===i)return k.call(n,a),n}else if(f&&(a=f.getElementById(i))&&I.contains(e,a)&&a.id===i)return k.call(n,a),n}else{if(u[2])return k.apply(n,e.getElementsByTagName(t)),n;if((i=u[3])&&e.getElementsByClassName)return k.apply(n,e.getElementsByClassName(i)),n}if(!(h[t+" "]||d&&d.test(t))){if(c=t,f=e,1===p&&(x.test(t)||m.test(t))){(f=H.test(t)&&U(e.parentNode)||e)==e&&le.scope||((s=e.getAttribute("id"))?s=ce.escapeSelector(s):e.setAttribute("id",s=S)),o=(l=Y(t)).length;while(o--)l[o]=(s?"#"+s:":scope")+" "+Q(l[o]);c=l.join(",")}try{return k.apply(n,f.querySelectorAll(c)),n}catch(e){h(t,!0)}finally{s===S&&e.removeAttribute("id")}}}return re(t.replace(ve,"$1"),e,n,r)}function W(){var r=[];return function e(t,n){return r.push(t+" ")>b.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function F(e){return e[S]=!0,e}function $(e){var t=T.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function B(t){return function(e){return fe(e,"input")&&e.type===t}}function _(t){return function(e){return(fe(e,"input")||fe(e,"button"))&&e.type===t}}function z(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&R(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function X(a){return F(function(o){return o=+o,F(function(e,t){var n,r=a([],e.length,o),i=r.length;while(i--)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function U(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}function V(e){var t,n=e?e.ownerDocument||e:ye;return n!=T&&9===n.nodeType&&n.documentElement&&(r=(T=n).documentElement,C=!ce.isXMLDoc(T),i=r.matches||r.webkitMatchesSelector||r.msMatchesSelector,r.msMatchesSelector&&ye!=T&&(t=T.defaultView)&&t.top!==t&&t.addEventListener("unload",M),le.getById=$(function(e){return r.appendChild(e).id=ce.expando,!T.getElementsByName||!T.getElementsByName(ce.expando).length}),le.disconnectedMatch=$(function(e){return i.call(e,"*")}),le.scope=$(function(){return T.querySelectorAll(":scope")}),le.cssHas=$(function(){try{return T.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}}),le.getById?(b.filter.ID=function(e){var t=e.replace(O,P);return function(e){return e.getAttribute("id")===t}},b.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&C){var n=t.getElementById(e);return n?[n]:[]}}):(b.filter.ID=function(e){var n=e.replace(O,P);return function(e){var t="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return t&&t.value===n}},b.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&C){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];i=t.getElementsByName(e),r=0;while(o=i[r++])if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),b.find.TAG=function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},b.find.CLASS=function(e,t){if("undefined"!=typeof t.getElementsByClassName&&C)return t.getElementsByClassName(e)},d=[],$(function(e){var t;r.appendChild(e).innerHTML="<a id='"+S+"' href='' disabled='disabled'></a><select id='"+S+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||d.push("\\["+ge+"*(?:value|"+f+")"),e.querySelectorAll("[id~="+S+"-]").length||d.push("~="),e.querySelectorAll("a#"+S+"+*").length||d.push(".#.+[+~]"),e.querySelectorAll(":checked").length||d.push(":checked"),(t=T.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),r.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&d.push(":enabled",":disabled"),(t=T.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||d.push("\\["+ge+"*name"+ge+"*="+ge+"*(?:''|\"\")")}),le.cssHas||d.push(":has"),d=d.length&&new RegExp(d.join("|")),l=function(e,t){if(e===t)return a=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!le.sortDetached&&t.compareDocumentPosition(e)===n?e===T||e.ownerDocument==ye&&I.contains(ye,e)?-1:t===T||t.ownerDocument==ye&&I.contains(ye,t)?1:o?se.call(o,e)-se.call(o,t):0:4&n?-1:1)}),T}for(e in I.matches=function(e,t){return I(e,null,null,t)},I.matchesSelector=function(e,t){if(V(e),C&&!h[t+" "]&&(!d||!d.test(t)))try{var n=i.call(e,t);if(n||le.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){h(t,!0)}return 0<I(t,T,null,[e]).length},I.contains=function(e,t){return(e.ownerDocument||e)!=T&&V(e),ce.contains(e,t)},I.attr=function(e,t){(e.ownerDocument||e)!=T&&V(e);var n=b.attrHandle[t.toLowerCase()],r=n&&ue.call(b.attrHandle,t.toLowerCase())?n(e,t,!C):void 0;return void 0!==r?r:e.getAttribute(t)},I.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ce.uniqueSort=function(e){var t,n=[],r=0,i=0;if(a=!le.sortStable,o=!le.sortStable&&ae.call(e,0),de.call(e,l),a){while(t=e[i++])t===e[i]&&(r=n.push(i));while(r--)he.call(e,n[r],1)}return o=null,e},ce.fn.uniqueSort=function(){return this.pushStack(ce.uniqueSort(ae.apply(this)))},(b=ce.expr={cacheLength:50,createPseudo:F,match:D,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(O,P),e[3]=(e[3]||e[4]||e[5]||"").replace(O,P),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||I.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&I.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return D.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&j.test(n)&&(t=Y(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(O,P).toLowerCase();return"*"===e?function(){return!0}:function(e){return fe(e,t)}},CLASS:function(e){var t=s[e+" "];return t||(t=new RegExp("(^|"+ge+")"+e+"("+ge+"|$)"))&&s(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(n,r,i){return function(e){var t=I.attr(e,n);return null==t?"!="===r:!r||(t+="","="===r?t===i:"!="===r?t!==i:"^="===r?i&&0===t.indexOf(i):"*="===r?i&&-1<t.indexOf(i):"$="===r?i&&t.slice(-i.length)===i:"~="===r?-1<(" "+t.replace(v," ")+" ").indexOf(i):"|="===r&&(t===i||t.slice(0,i.length+1)===i+"-"))}},CHILD:function(d,e,t,h,g){var v="nth"!==d.slice(0,3),y="last"!==d.slice(-4),m="of-type"===e;return 1===h&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,u=v!==y?"nextSibling":"previousSibling",l=e.parentNode,c=m&&e.nodeName.toLowerCase(),f=!n&&!m,p=!1;if(l){if(v){while(u){o=e;while(o=o[u])if(m?fe(o,c):1===o.nodeType)return!1;s=u="only"===d&&!s&&"nextSibling"}return!0}if(s=[y?l.firstChild:l.lastChild],y&&f){p=(a=(r=(i=l[S]||(l[S]={}))[d]||[])[0]===E&&r[1])&&r[2],o=a&&l.childNodes[a];while(o=++a&&o&&o[u]||(p=a=0)||s.pop())if(1===o.nodeType&&++p&&o===e){i[d]=[E,a,p];break}}else if(f&&(p=a=(r=(i=e[S]||(e[S]={}))[d]||[])[0]===E&&r[1]),!1===p)while(o=++a&&o&&o[u]||(p=a=0)||s.pop())if((m?fe(o,c):1===o.nodeType)&&++p&&(f&&((i=o[S]||(o[S]={}))[d]=[E,p]),o===e))break;return(p-=g)===h||p%h==0&&0<=p/h}}},PSEUDO:function(e,o){var t,a=b.pseudos[e]||b.setFilters[e.toLowerCase()]||I.error("unsupported pseudo: "+e);return a[S]?a(o):1<a.length?(t=[e,e,"",o],b.setFilters.hasOwnProperty(e.toLowerCase())?F(function(e,t){var n,r=a(e,o),i=r.length;while(i--)e[n=se.call(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:F(function(e){var r=[],i=[],s=ne(e.replace(ve,"$1"));return s[S]?F(function(e,t,n,r){var i,o=s(e,null,r,[]),a=e.length;while(a--)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:F(function(t){return function(e){return 0<I(t,e).length}}),contains:F(function(t){return t=t.replace(O,P),function(e){return-1<(e.textContent||ce.text(e)).indexOf(t)}}),lang:F(function(n){return A.test(n||"")||I.error("unsupported lang: "+n),n=n.replace(O,P).toLowerCase(),function(e){var t;do{if(t=C?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=ie.location&&ie.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===r},focus:function(e){return e===function(){try{return T.activeElement}catch(e){}}()&&T.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:z(!1),disabled:z(!0),checked:function(e){return fe(e,"input")&&!!e.checked||fe(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return q.test(e.nodeName)},input:function(e){return N.test(e.nodeName)},button:function(e){return fe(e,"input")&&"button"===e.type||fe(e,"button")},text:function(e){var t;return fe(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:X(function(){return[0]}),last:X(function(e,t){return[t-1]}),eq:X(function(e,t,n){return[n<0?n+t:n]}),even:X(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:X(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:X(function(e,t,n){var r;for(r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:X(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[e]=B(e);for(e in{submit:!0,reset:!0})b.pseudos[e]=_(e);function G(){}function Y(e,t){var n,r,i,o,a,s,u,l=c[e+" "];if(l)return t?0:l.slice(0);a=e,s=[],u=b.preFilter;while(a){for(o in n&&!(r=y.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=m.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(ve," ")}),a=a.slice(n.length)),b.filter)!(r=D[o].exec(a))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?I.error(e):c(e,s).slice(0)}function Q(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function J(a,e,t){var s=e.dir,u=e.next,l=u||s,c=t&&"parentNode"===l,f=n++;return e.first?function(e,t,n){while(e=e[s])if(1===e.nodeType||c)return a(e,t,n);return!1}:function(e,t,n){var r,i,o=[E,f];if(n){while(e=e[s])if((1===e.nodeType||c)&&a(e,t,n))return!0}else while(e=e[s])if(1===e.nodeType||c)if(i=e[S]||(e[S]={}),u&&fe(e,u))e=e[s]||e;else{if((r=i[l])&&r[0]===E&&r[1]===f)return o[2]=r[2];if((i[l]=o)[2]=a(e,t,n))return!0}return!1}}function K(i){return 1<i.length?function(e,t,n){var r=i.length;while(r--)if(!i[r](e,t,n))return!1;return!0}:i[0]}function Z(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function ee(d,h,g,v,y,e){return v&&!v[S]&&(v=ee(v)),y&&!y[S]&&(y=ee(y,e)),F(function(e,t,n,r){var i,o,a,s,u=[],l=[],c=t.length,f=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)I(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),p=!d||!e&&h?f:Z(f,u,d,n,r);if(g?g(p,s=y||(e?d:c||v)?[]:t,n,r):s=p,v){i=Z(s,l),v(i,[],n,r),o=i.length;while(o--)(a=i[o])&&(s[l[o]]=!(p[l[o]]=a))}if(e){if(y||d){if(y){i=[],o=s.length;while(o--)(a=s[o])&&i.push(p[o]=a);y(null,s=[],i,r)}o=s.length;while(o--)(a=s[o])&&-1<(i=y?se.call(e,a):u[o])&&(e[i]=!(t[i]=a))}}else s=Z(s===t?s.splice(c,s.length):s),y?y(null,t,s,r):k.apply(t,s)})}function te(e){for(var i,t,n,r=e.length,o=b.relative[e[0].type],a=o||b.relative[" "],s=o?1:0,u=J(function(e){return e===i},a,!0),l=J(function(e){return-1<se.call(i,e)},a,!0),c=[function(e,t,n){var r=!o&&(n||t!=w)||((i=t).nodeType?u(e,t,n):l(e,t,n));return i=null,r}];s<r;s++)if(t=b.relative[e[s].type])c=[J(K(c),t)];else{if((t=b.filter[e[s].type].apply(null,e[s].matches))[S]){for(n=++s;n<r;n++)if(b.relative[e[n].type])break;return ee(1<s&&K(c),1<s&&Q(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(ve,"$1"),t,s<n&&te(e.slice(s,n)),n<r&&te(e=e.slice(n)),n<r&&Q(e))}c.push(t)}return K(c)}function ne(e,t){var n,v,y,m,x,r,i=[],o=[],a=u[e+" "];if(!a){t||(t=Y(e)),n=t.length;while(n--)(a=te(t[n]))[S]?i.push(a):o.push(a);(a=u(e,(v=o,m=0<(y=i).length,x=0<v.length,r=function(e,t,n,r,i){var o,a,s,u=0,l="0",c=e&&[],f=[],p=w,d=e||x&&b.find.TAG("*",i),h=E+=null==p?1:Math.random()||.1,g=d.length;for(i&&(w=t==T||t||i);l!==g&&null!=(o=d[l]);l++){if(x&&o){a=0,t||o.ownerDocument==T||(V(o),n=!C);while(s=v[a++])if(s(o,t||T,n)){k.call(r,o);break}i&&(E=h)}m&&((o=!s&&o)&&u--,e&&c.push(o))}if(u+=l,m&&l!==u){a=0;while(s=y[a++])s(c,f,t,n);if(e){if(0<u)while(l--)c[l]||f[l]||(f[l]=pe.call(r));f=Z(f)}k.apply(r,f),i&&!e&&0<f.length&&1<u+y.length&&ce.uniqueSort(r)}return i&&(E=h,w=p),c},m?F(r):r))).selector=e}return a}function re(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,c=!r&&Y(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&C&&b.relative[o[1].type]){if(!(t=(b.find.ID(a.matches[0].replace(O,P),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}i=D.needsContext.test(e)?0:o.length;while(i--){if(a=o[i],b.relative[s=a.type])break;if((u=b.find[s])&&(r=u(a.matches[0].replace(O,P),H.test(o[0].type)&&U(t.parentNode)||t))){if(o.splice(i,1),!(e=r.length&&Q(o)))return k.apply(n,r),n;break}}}return(l||ne(e,c))(r,t,!C,n,!t||H.test(e)&&U(t.parentNode)||t),n}G.prototype=b.filters=b.pseudos,b.setFilters=new G,le.sortStable=S.split("").sort(l).join("")===S,V(),le.sortDetached=$(function(e){return 1&e.compareDocumentPosition(T.createElement("fieldset"))}),ce.find=I,ce.expr[":"]=ce.expr.pseudos,ce.unique=ce.uniqueSort,I.compile=ne,I.select=re,I.setDocument=V,I.tokenize=Y,I.escape=ce.escapeSelector,I.getText=ce.text,I.isXML=ce.isXMLDoc,I.selectors=ce.expr,I.support=ce.support,I.uniqueSort=ce.uniqueSort}();var d=function(e,t,n){var r=[],i=void 0!==n;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&ce(e).is(n))break;r.push(e)}return r},h=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},b=ce.expr.match.needsContext,w=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function T(e,n,r){return v(n)?ce.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?ce.grep(e,function(e){return e===n!==r}):"string"!=typeof n?ce.grep(e,function(e){return-1<se.call(n,e)!==r}):ce.filter(n,e,r)}ce.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?ce.find.matchesSelector(r,e)?[r]:[]:ce.find.matches(e,ce.grep(t,function(e){return 1===e.nodeType}))},ce.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(ce(e).filter(function(){for(t=0;t<r;t++)if(ce.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)ce.find(e,i[t],n);return 1<r?ce.uniqueSort(n):n},filter:function(e){return this.pushStack(T(this,e||[],!1))},not:function(e){return this.pushStack(T(this,e||[],!0))},is:function(e){return!!T(this,"string"==typeof e&&b.test(e)?ce(e):e||[],!1).length}});var k,S=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(ce.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||k,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:S.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof ce?t[0]:t,ce.merge(this,ce.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:C,!0)),w.test(r[1])&&ce.isPlainObject(t))for(r in t)v(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=C.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):e(ce):ce.makeArray(e,this)}).prototype=ce.fn,k=ce(C);var E=/^(?:parents|prev(?:Until|All))/,j={children:!0,contents:!0,next:!0,prev:!0};function A(e,t){while((e=e[t])&&1!==e.nodeType);return e}ce.fn.extend({has:function(e){var t=ce(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(ce.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&ce(e);if(!b.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&ce.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?ce.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?se.call(ce(e),this[0]):se.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(ce.uniqueSort(ce.merge(this.get(),ce(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),ce.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return d(e,"parentNode")},parentsUntil:function(e,t,n){return d(e,"parentNode",n)},next:function(e){return A(e,"nextSibling")},prev:function(e){return A(e,"previousSibling")},nextAll:function(e){return d(e,"nextSibling")},prevAll:function(e){return d(e,"previousSibling")},nextUntil:function(e,t,n){return d(e,"nextSibling",n)},prevUntil:function(e,t,n){return d(e,"previousSibling",n)},siblings:function(e){return h((e.parentNode||{}).firstChild,e)},children:function(e){return h(e.firstChild)},contents:function(e){return null!=e.contentDocument&&r(e.contentDocument)?e.contentDocument:(fe(e,"template")&&(e=e.content||e),ce.merge([],e.childNodes))}},function(r,i){ce.fn[r]=function(e,t){var n=ce.map(this,i,e);return"Until"!==r.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=ce.filter(t,n)),1<this.length&&(j[r]||ce.uniqueSort(n),E.test(r)&&n.reverse()),this.pushStack(n)}});var D=/[^\x20\t\r\n\f]+/g;function N(e){return e}function q(e){throw e}function L(e,t,n,r){var i;try{e&&v(i=e.promise)?i.call(e).done(t).fail(n):e&&v(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}ce.Callbacks=function(r){var e,n;r="string"==typeof r?(e=r,n={},ce.each(e.match(D)||[],function(e,t){n[t]=!0}),n):ce.extend({},r);var i,t,o,a,s=[],u=[],l=-1,c=function(){for(a=a||r.once,o=i=!0;u.length;l=-1){t=u.shift();while(++l<s.length)!1===s[l].apply(t[0],t[1])&&r.stopOnFalse&&(l=s.length,t=!1)}r.memory||(t=!1),i=!1,a&&(s=t?[]:"")},f={add:function(){return s&&(t&&!i&&(l=s.length-1,u.push(t)),function n(e){ce.each(e,function(e,t){v(t)?r.unique&&f.has(t)||s.push(t):t&&t.length&&"string"!==x(t)&&n(t)})}(arguments),t&&!i&&c()),this},remove:function(){return ce.each(arguments,function(e,t){var n;while(-1<(n=ce.inArray(t,s,n)))s.splice(n,1),n<=l&&l--}),this},has:function(e){return e?-1<ce.inArray(e,s):0<s.length},empty:function(){return s&&(s=[]),this},disable:function(){return a=u=[],s=t="",this},disabled:function(){return!s},lock:function(){return a=u=[],t||i||(s=t=""),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],u.push(t),i||c()),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!o}};return f},ce.extend({Deferred:function(e){var o=[["notify","progress",ce.Callbacks("memory"),ce.Callbacks("memory"),2],["resolve","done",ce.Callbacks("once memory"),ce.Callbacks("once memory"),0,"resolved"],["reject","fail",ce.Callbacks("once memory"),ce.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},"catch":function(e){return a.then(null,e)},pipe:function(){var i=arguments;return ce.Deferred(function(r){ce.each(o,function(e,t){var n=v(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&v(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var u=0;function l(i,o,a,s){return function(){var n=this,r=arguments,e=function(){var e,t;if(!(i<u)){if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,v(t)?s?t.call(e,l(u,o,N,s),l(u,o,q,s)):(u++,t.call(e,l(u,o,N,s),l(u,o,q,s),l(u,o,N,o.notifyWith))):(a!==N&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}},t=s?e:function(){try{e()}catch(e){ce.Deferred.exceptionHook&&ce.Deferred.exceptionHook(e,t.error),u<=i+1&&(a!==q&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(ce.Deferred.getErrorHook?t.error=ce.Deferred.getErrorHook():ce.Deferred.getStackHook&&(t.error=ce.Deferred.getStackHook()),ie.setTimeout(t))}}return ce.Deferred(function(e){o[0][3].add(l(0,e,v(r)?r:N,e.notifyWith)),o[1][3].add(l(0,e,v(t)?t:N)),o[2][3].add(l(0,e,v(n)?n:q))}).promise()},promise:function(e){return null!=e?ce.extend(e,a):a}},s={};return ce.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){var n=arguments.length,t=n,r=Array(t),i=ae.call(arguments),o=ce.Deferred(),a=function(t){return function(e){r[t]=this,i[t]=1<arguments.length?ae.call(arguments):e,--n||o.resolveWith(r,i)}};if(n<=1&&(L(e,o.done(a(t)).resolve,o.reject,!n),"pending"===o.state()||v(i[t]&&i[t].then)))return o.then();while(t--)L(i[t],a(t),o.reject);return o.promise()}});var H=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;ce.Deferred.exceptionHook=function(e,t){ie.console&&ie.console.warn&&e&&H.test(e.name)&&ie.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},ce.readyException=function(e){ie.setTimeout(function(){throw e})};var O=ce.Deferred();function P(){C.removeEventListener("DOMContentLoaded",P),ie.removeEventListener("load",P),ce.ready()}ce.fn.ready=function(e){return O.then(e)["catch"](function(e){ce.readyException(e)}),this},ce.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--ce.readyWait:ce.isReady)||(ce.isReady=!0)!==e&&0<--ce.readyWait||O.resolveWith(C,[ce])}}),ce.ready.then=O.then,"complete"===C.readyState||"loading"!==C.readyState&&!C.documentElement.doScroll?ie.setTimeout(ce.ready):(C.addEventListener("DOMContentLoaded",P),ie.addEventListener("load",P));var M=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===x(n))for(s in i=!0,n)M(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,v(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(ce(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},R=/^-ms-/,I=/-([a-z])/g;function W(e,t){return t.toUpperCase()}function F(e){return e.replace(R,"ms-").replace(I,W)}var $=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function B(){this.expando=ce.expando+B.uid++}B.uid=1,B.prototype={cache:function(e){var t=e[this.expando];return t||(t={},$(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[F(t)]=n;else for(r in t)i[F(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][F(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(F):(t=F(t))in r?[t]:t.match(D)||[]).length;while(n--)delete r[t[n]]}(void 0===t||ce.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!ce.isEmptyObject(t)}};var _=new B,z=new B,X=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,U=/[A-Z]/g;function V(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(U,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:X.test(i)?JSON.parse(i):i)}catch(e){}z.set(e,t,n)}else n=void 0;return n}ce.extend({hasData:function(e){return z.hasData(e)||_.hasData(e)},data:function(e,t,n){return z.access(e,t,n)},removeData:function(e,t){z.remove(e,t)},_data:function(e,t,n){return _.access(e,t,n)},_removeData:function(e,t){_.remove(e,t)}}),ce.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0===n){if(this.length&&(i=z.get(o),1===o.nodeType&&!_.get(o,"hasDataAttrs"))){t=a.length;while(t--)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=F(r.slice(5)),V(o,r,i[r]));_.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof n?this.each(function(){z.set(this,n)}):M(this,function(e){var t;if(o&&void 0===e)return void 0!==(t=z.get(o,n))?t:void 0!==(t=V(o,n))?t:void 0;this.each(function(){z.set(this,n,e)})},null,e,1<arguments.length,null,!0)},removeData:function(e){return this.each(function(){z.remove(this,e)})}}),ce.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=_.get(e,t),n&&(!r||Array.isArray(n)?r=_.access(e,t,ce.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=ce.queue(e,t),r=n.length,i=n.shift(),o=ce._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){ce.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return _.get(e,n)||_.access(e,n,{empty:ce.Callbacks("once memory").add(function(){_.remove(e,[t+"queue",n])})})}}),ce.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?ce.queue(this[0],t):void 0===n?this:this.each(function(){var e=ce.queue(this,t,n);ce._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&ce.dequeue(this,t)})},dequeue:function(e){return this.each(function(){ce.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=ce.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};"string"!=typeof e&&(t=e,e=void 0),e=e||"fx";while(a--)(n=_.get(o[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var G=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Y=new RegExp("^(?:([+-])=|)("+G+")([a-z%]*)$","i"),Q=["Top","Right","Bottom","Left"],J=C.documentElement,K=function(e){return ce.contains(e.ownerDocument,e)},Z={composed:!0};J.getRootNode&&(K=function(e){return ce.contains(e.ownerDocument,e)||e.getRootNode(Z)===e.ownerDocument});var ee=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&K(e)&&"none"===ce.css(e,"display")};function te(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return ce.css(e,t,"")},u=s(),l=n&&n[3]||(ce.cssNumber[t]?"":"px"),c=e.nodeType&&(ce.cssNumber[t]||"px"!==l&&+u)&&Y.exec(ce.css(e,t));if(c&&c[3]!==l){u/=2,l=l||c[3],c=+u||1;while(a--)ce.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,ce.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var ne={};function re(e,t){for(var n,r,i,o,a,s,u,l=[],c=0,f=e.length;c<f;c++)(r=e[c]).style&&(n=r.style.display,t?("none"===n&&(l[c]=_.get(r,"display")||null,l[c]||(r.style.display="")),""===r.style.display&&ee(r)&&(l[c]=(u=a=o=void 0,a=(i=r).ownerDocument,s=i.nodeName,(u=ne[s])||(o=a.body.appendChild(a.createElement(s)),u=ce.css(o,"display"),o.parentNode.removeChild(o),"none"===u&&(u="block"),ne[s]=u)))):"none"!==n&&(l[c]="none",_.set(r,"display",n)));for(c=0;c<f;c++)null!=l[c]&&(e[c].style.display=l[c]);return e}ce.fn.extend({show:function(){return re(this,!0)},hide:function(){return re(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ee(this)?ce(this).show():ce(this).hide()})}});var xe,be,we=/^(?:checkbox|radio)$/i,Te=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Ce=/^$|^module$|\/(?:java|ecma)script/i;xe=C.createDocumentFragment().appendChild(C.createElement("div")),(be=C.createElement("input")).setAttribute("type","radio"),be.setAttribute("checked","checked"),be.setAttribute("name","t"),xe.appendChild(be),le.checkClone=xe.cloneNode(!0).cloneNode(!0).lastChild.checked,xe.innerHTML="<textarea>x</textarea>",le.noCloneChecked=!!xe.cloneNode(!0).lastChild.defaultValue,xe.innerHTML="<option></option>",le.option=!!xe.lastChild;var ke={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Se(e,t){var n;return n="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&fe(e,t)?ce.merge([e],n):n}function Ee(e,t){for(var n=0,r=e.length;n<r;n++)_.set(e[n],"globalEval",!t||_.get(t[n],"globalEval"))}ke.tbody=ke.tfoot=ke.colgroup=ke.caption=ke.thead,ke.th=ke.td,le.option||(ke.optgroup=ke.option=[1,"<select multiple='multiple'>","</select>"]);var je=/<|&#?\w+;/;function Ae(e,t,n,r,i){for(var o,a,s,u,l,c,f=t.createDocumentFragment(),p=[],d=0,h=e.length;d<h;d++)if((o=e[d])||0===o)if("object"===x(o))ce.merge(p,o.nodeType?[o]:o);else if(je.test(o)){a=a||f.appendChild(t.createElement("div")),s=(Te.exec(o)||["",""])[1].toLowerCase(),u=ke[s]||ke._default,a.innerHTML=u[1]+ce.htmlPrefilter(o)+u[2],c=u[0];while(c--)a=a.lastChild;ce.merge(p,a.childNodes),(a=f.firstChild).textContent=""}else p.push(t.createTextNode(o));f.textContent="",d=0;while(o=p[d++])if(r&&-1<ce.inArray(o,r))i&&i.push(o);else if(l=K(o),a=Se(f.appendChild(o),"script"),l&&Ee(a),n){c=0;while(o=a[c++])Ce.test(o.type||"")&&n.push(o)}return f}var De=/^([^.]*)(?:\.(.+)|)/;function Ne(){return!0}function qe(){return!1}function Le(e,t,n,r,i,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)Le(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=qe;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return ce().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=ce.guid++)),e.each(function(){ce.event.add(this,t,i,r,n)})}function He(e,r,t){t?(_.set(e,r,!1),ce.event.add(e,r,{namespace:!1,handler:function(e){var t,n=_.get(this,r);if(1&e.isTrigger&&this[r]){if(n)(ce.event.special[r]||{}).delegateType&&e.stopPropagation();else if(n=ae.call(arguments),_.set(this,r,n),this[r](),t=_.get(this,r),_.set(this,r,!1),n!==t)return e.stopImmediatePropagation(),e.preventDefault(),t}else n&&(_.set(this,r,ce.event.trigger(n[0],n.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Ne)}})):void 0===_.get(e,r)&&ce.event.add(e,r,Ne)}ce.event={global:{},add:function(t,e,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=_.get(t);if($(t)){n.handler&&(n=(o=n).handler,i=o.selector),i&&ce.find.matchesSelector(J,i),n.guid||(n.guid=ce.guid++),(u=v.events)||(u=v.events=Object.create(null)),(a=v.handle)||(a=v.handle=function(e){return"undefined"!=typeof ce&&ce.event.triggered!==e.type?ce.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(D)||[""]).length;while(l--)d=g=(s=De.exec(e[l])||[])[1],h=(s[2]||"").split(".").sort(),d&&(f=ce.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=ce.event.special[d]||{},c=ce.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&ce.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=u[d])||((p=u[d]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(t,r,h,a)||t.addEventListener&&t.addEventListener(d,a)),f.add&&(f.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),ce.event.global[d]=!0)}},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=_.hasData(e)&&_.get(e);if(v&&(u=v.events)){l=(t=(t||"").match(D)||[""]).length;while(l--)if(d=g=(s=De.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),d){f=ce.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;while(o--)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||ce.removeEvent(e,d,v.handle),delete u[d])}else for(d in u)ce.event.remove(e,d+t[l],n,r,!0);ce.isEmptyObject(u)&&_.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=new Array(arguments.length),u=ce.event.fix(e),l=(_.get(this,"events")||Object.create(null))[u.type]||[],c=ce.event.special[u.type]||{};for(s[0]=u,t=1;t<arguments.length;t++)s[t]=arguments[t];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){a=ce.event.handlers.call(this,u,l),t=0;while((i=a[t++])&&!u.isPropagationStopped()){u.currentTarget=i.elem,n=0;while((o=i.handlers[n++])&&!u.isImmediatePropagationStopped())u.rnamespace&&!1!==o.namespace&&!u.rnamespace.test(o.namespace)||(u.handleObj=o,u.data=o.data,void 0!==(r=((ce.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s))&&!1===(u.result=r)&&(u.preventDefault(),u.stopPropagation()))}return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&1<=e.button))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<ce(i,this).index(l):ce.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(t,e){Object.defineProperty(ce.Event.prototype,t,{enumerable:!0,configurable:!0,get:v(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[ce.expando]?e:new ce.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return we.test(t.type)&&t.click&&fe(t,"input")&&He(t,"click",!0),!1},trigger:function(e){var t=this||e;return we.test(t.type)&&t.click&&fe(t,"input")&&He(t,"click"),!0},_default:function(e){var t=e.target;return we.test(t.type)&&t.click&&fe(t,"input")&&_.get(t,"click")||fe(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},ce.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},ce.Event=function(e,t){if(!(this instanceof ce.Event))return new ce.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Ne:qe,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&ce.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[ce.expando]=!0},ce.Event.prototype={constructor:ce.Event,isDefaultPrevented:qe,isPropagationStopped:qe,isImmediatePropagationStopped:qe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ne,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ne,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ne,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},ce.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},ce.event.addProp),ce.each({focus:"focusin",blur:"focusout"},function(r,i){function o(e){if(C.documentMode){var t=_.get(this,"handle"),n=ce.event.fix(e);n.type="focusin"===e.type?"focus":"blur",n.isSimulated=!0,t(e),n.target===n.currentTarget&&t(n)}else ce.event.simulate(i,e.target,ce.event.fix(e))}ce.event.special[r]={setup:function(){var e;if(He(this,r,!0),!C.documentMode)return!1;(e=_.get(this,i))||this.addEventListener(i,o),_.set(this,i,(e||0)+1)},trigger:function(){return He(this,r),!0},teardown:function(){var e;if(!C.documentMode)return!1;(e=_.get(this,i)-1)?_.set(this,i,e):(this.removeEventListener(i,o),_.remove(this,i))},_default:function(e){return _.get(e.target,r)},delegateType:i},ce.event.special[i]={setup:function(){var e=this.ownerDocument||this.document||this,t=C.documentMode?this:e,n=_.get(t,i);n||(C.documentMode?this.addEventListener(i,o):e.addEventListener(r,o,!0)),_.set(t,i,(n||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=C.documentMode?this:e,n=_.get(t,i)-1;n?_.set(t,i,n):(C.documentMode?this.removeEventListener(i,o):e.removeEventListener(r,o,!0),_.remove(t,i))}}}),ce.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){ce.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||ce.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),ce.fn.extend({on:function(e,t,n,r){return Le(this,e,t,n,r)},one:function(e,t,n,r){return Le(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,ce(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=qe),this.each(function(){ce.event.remove(this,e,n,t)})}});var Oe=/<script|<style|<link/i,Pe=/checked\s*(?:[^=]|=\s*.checked.)/i,Me=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Re(e,t){return fe(e,"table")&&fe(11!==t.nodeType?t:t.firstChild,"tr")&&ce(e).children("tbody")[0]||e}function Ie(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function We(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Fe(e,t){var n,r,i,o,a,s;if(1===t.nodeType){if(_.hasData(e)&&(s=_.get(e).events))for(i in _.remove(t,"handle events"),s)for(n=0,r=s[i].length;n<r;n++)ce.event.add(t,i,s[i][n]);z.hasData(e)&&(o=z.access(e),a=ce.extend({},o),z.set(t,a))}}function $e(n,r,i,o){r=g(r);var e,t,a,s,u,l,c=0,f=n.length,p=f-1,d=r[0],h=v(d);if(h||1<f&&"string"==typeof d&&!le.checkClone&&Pe.test(d))return n.each(function(e){var t=n.eq(e);h&&(r[0]=d.call(this,e,t.html())),$e(t,r,i,o)});if(f&&(t=(e=Ae(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=ce.map(Se(e,"script"),Ie)).length;c<f;c++)u=e,c!==p&&(u=ce.clone(u,!0,!0),s&&ce.merge(a,Se(u,"script"))),i.call(n[c],u,c);if(s)for(l=a[a.length-1].ownerDocument,ce.map(a,We),c=0;c<s;c++)u=a[c],Ce.test(u.type||"")&&!_.access(u,"globalEval")&&ce.contains(l,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?ce._evalUrl&&!u.noModule&&ce._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},l):m(u.textContent.replace(Me,""),u,l))}return n}function Be(e,t,n){for(var r,i=t?ce.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||ce.cleanData(Se(r)),r.parentNode&&(n&&K(r)&&Ee(Se(r,"script")),r.parentNode.removeChild(r));return e}ce.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,u,l,c=e.cloneNode(!0),f=K(e);if(!(le.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||ce.isXMLDoc(e)))for(a=Se(c),r=0,i=(o=Se(e)).length;r<i;r++)s=o[r],u=a[r],void 0,"input"===(l=u.nodeName.toLowerCase())&&we.test(s.type)?u.checked=s.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=s.defaultValue);if(t)if(n)for(o=o||Se(e),a=a||Se(c),r=0,i=o.length;r<i;r++)Fe(o[r],a[r]);else Fe(e,c);return 0<(a=Se(c,"script")).length&&Ee(a,!f&&Se(e,"script")),c},cleanData:function(e){for(var t,n,r,i=ce.event.special,o=0;void 0!==(n=e[o]);o++)if($(n)){if(t=n[_.expando]){if(t.events)for(r in t.events)i[r]?ce.event.remove(n,r):ce.removeEvent(n,r,t.handle);n[_.expando]=void 0}n[z.expando]&&(n[z.expando]=void 0)}}}),ce.fn.extend({detach:function(e){return Be(this,e,!0)},remove:function(e){return Be(this,e)},text:function(e){return M(this,function(e){return void 0===e?ce.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return $e(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Re(this,e).appendChild(e)})},prepend:function(){return $e(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Re(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return $e(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return $e(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(ce.cleanData(Se(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return ce.clone(this,e,t)})},html:function(e){return M(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Oe.test(e)&&!ke[(Te.exec(e)||["",""])[1].toLowerCase()]){e=ce.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(ce.cleanData(Se(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return $e(this,arguments,function(e){var t=this.parentNode;ce.inArray(this,n)<0&&(ce.cleanData(Se(this)),t&&t.replaceChild(e,this))},n)}}),ce.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){ce.fn[e]=function(e){for(var t,n=[],r=ce(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),ce(r[o])[a](t),s.apply(n,t.get());return this.pushStack(n)}});var _e=new RegExp("^("+G+")(?!px)[a-z%]+$","i"),ze=/^--/,Xe=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=ie),t.getComputedStyle(e)},Ue=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},Ve=new RegExp(Q.join("|"),"i");function Ge(e,t,n){var r,i,o,a,s=ze.test(t),u=e.style;return(n=n||Xe(e))&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(ve,"$1")||void 0),""!==a||K(e)||(a=ce.style(e,t)),!le.pixelBoxStyles()&&_e.test(a)&&Ve.test(t)&&(r=u.width,i=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=a,a=n.width,u.width=r,u.minWidth=i,u.maxWidth=o)),void 0!==a?a+"":a}function Ye(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(l){u.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",l.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",J.appendChild(u).appendChild(l);var e=ie.getComputedStyle(l);n="1%"!==e.top,s=12===t(e.marginLeft),l.style.right="60%",o=36===t(e.right),r=36===t(e.width),l.style.position="absolute",i=12===t(l.offsetWidth/3),J.removeChild(u),l=null}}function t(e){return Math.round(parseFloat(e))}var n,r,i,o,a,s,u=C.createElement("div"),l=C.createElement("div");l.style&&(l.style.backgroundClip="content-box",l.cloneNode(!0).style.backgroundClip="",le.clearCloneStyle="content-box"===l.style.backgroundClip,ce.extend(le,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),o},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),s},scrollboxSize:function(){return e(),i},reliableTrDimensions:function(){var e,t,n,r;return null==a&&(e=C.createElement("table"),t=C.createElement("tr"),n=C.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",J.appendChild(e).appendChild(t).appendChild(n),r=ie.getComputedStyle(t),a=parseInt(r.height,10)+parseInt(r.borderTopWidth,10)+parseInt(r.borderBottomWidth,10)===t.offsetHeight,J.removeChild(e)),a}}))}();var Qe=["Webkit","Moz","ms"],Je=C.createElement("div").style,Ke={};function Ze(e){var t=ce.cssProps[e]||Ke[e];return t||(e in Je?e:Ke[e]=function(e){var t=e[0].toUpperCase()+e.slice(1),n=Qe.length;while(n--)if((e=Qe[n]+t)in Je)return e}(e)||e)}var et=/^(none|table(?!-c[ea]).+)/,tt={position:"absolute",visibility:"hidden",display:"block"},nt={letterSpacing:"0",fontWeight:"400"};function rt(e,t,n){var r=Y.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function it(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0,l=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=ce.css(e,n+Q[a],!0,i)),r?("content"===n&&(u-=ce.css(e,"padding"+Q[a],!0,i)),"margin"!==n&&(u-=ce.css(e,"border"+Q[a]+"Width",!0,i))):(u+=ce.css(e,"padding"+Q[a],!0,i),"padding"!==n?u+=ce.css(e,"border"+Q[a]+"Width",!0,i):s+=ce.css(e,"border"+Q[a]+"Width",!0,i));return!r&&0<=o&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u+l}function ot(e,t,n){var r=Xe(e),i=(!le.boxSizingReliable()||n)&&"border-box"===ce.css(e,"boxSizing",!1,r),o=i,a=Ge(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(_e.test(a)){if(!n)return a;a="auto"}return(!le.boxSizingReliable()&&i||!le.reliableTrDimensions()&&fe(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===ce.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===ce.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+it(e,t,n||(i?"border":"content"),o,r,a)+"px"}function at(e,t,n,r,i){return new at.prototype.init(e,t,n,r,i)}ce.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ge(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=F(t),u=ze.test(t),l=e.style;if(u||(t=Ze(s)),a=ce.cssHooks[t]||ce.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];"string"===(o=typeof n)&&(i=Y.exec(n))&&i[1]&&(n=te(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=i&&i[3]||(ce.cssNumber[s]?"":"px")),le.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o,a,s=F(t);return ze.test(t)||(t=Ze(s)),(a=ce.cssHooks[t]||ce.cssHooks[s])&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=Ge(e,t,r)),"normal"===i&&t in nt&&(i=nt[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),ce.each(["height","width"],function(e,u){ce.cssHooks[u]={get:function(e,t,n){if(t)return!et.test(ce.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ot(e,u,n):Ue(e,tt,function(){return ot(e,u,n)})},set:function(e,t,n){var r,i=Xe(e),o=!le.scrollboxSize()&&"absolute"===i.position,a=(o||n)&&"border-box"===ce.css(e,"boxSizing",!1,i),s=n?it(e,u,n,a,i):0;return a&&o&&(s-=Math.ceil(e["offset"+u[0].toUpperCase()+u.slice(1)]-parseFloat(i[u])-it(e,u,"border",!1,i)-.5)),s&&(r=Y.exec(t))&&"px"!==(r[3]||"px")&&(e.style[u]=t,t=ce.css(e,u)),rt(0,t,s)}}}),ce.cssHooks.marginLeft=Ye(le.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Ge(e,"marginLeft"))||e.getBoundingClientRect().left-Ue(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),ce.each({margin:"",padding:"",border:"Width"},function(i,o){ce.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+Q[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(ce.cssHooks[i+o].set=rt)}),ce.fn.extend({css:function(e,t){return M(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Xe(e),i=t.length;a<i;a++)o[t[a]]=ce.css(e,t[a],!1,r);return o}return void 0!==n?ce.style(e,t,n):ce.css(e,t)},e,t,1<arguments.length)}}),((ce.Tween=at).prototype={constructor:at,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||ce.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(ce.cssNumber[n]?"":"px")},cur:function(){var e=at.propHooks[this.prop];return e&&e.get?e.get(this):at.propHooks._default.get(this)},run:function(e){var t,n=at.propHooks[this.prop];return this.options.duration?this.pos=t=ce.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):at.propHooks._default.set(this),this}}).init.prototype=at.prototype,(at.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=ce.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){ce.fx.step[e.prop]?ce.fx.step[e.prop](e):1!==e.elem.nodeType||!ce.cssHooks[e.prop]&&null==e.elem.style[Ze(e.prop)]?e.elem[e.prop]=e.now:ce.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=at.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},ce.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},ce.fx=at.prototype.init,ce.fx.step={};var st,ut,lt,ct,ft=/^(?:toggle|show|hide)$/,pt=/queueHooks$/;function dt(){ut&&(!1===C.hidden&&ie.requestAnimationFrame?ie.requestAnimationFrame(dt):ie.setTimeout(dt,ce.fx.interval),ce.fx.tick())}function ht(){return ie.setTimeout(function(){st=void 0}),st=Date.now()}function gt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=Q[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function vt(e,t,n){for(var r,i=(yt.tweeners[t]||[]).concat(yt.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function yt(o,e,t){var n,a,r=0,i=yt.prefilters.length,s=ce.Deferred().always(function(){delete u.elem}),u=function(){if(a)return!1;for(var e=st||ht(),t=Math.max(0,l.startTime+l.duration-e),n=1-(t/l.duration||0),r=0,i=l.tweens.length;r<i;r++)l.tweens[r].run(n);return s.notifyWith(o,[l,n,t]),n<1&&i?t:(i||s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l]),!1)},l=s.promise({elem:o,props:ce.extend({},e),opts:ce.extend(!0,{specialEasing:{},easing:ce.easing._default},t),originalProperties:e,originalOptions:t,startTime:st||ht(),duration:t.duration,tweens:[],createTween:function(e,t){var n=ce.Tween(o,l.opts,e,t,l.opts.specialEasing[e]||l.opts.easing);return l.tweens.push(n),n},stop:function(e){var t=0,n=e?l.tweens.length:0;if(a)return this;for(a=!0;t<n;t++)l.tweens[t].run(1);return e?(s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l,e])):s.rejectWith(o,[l,e]),this}}),c=l.props;for(!function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=F(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=ce.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);r<i;r++)if(n=yt.prefilters[r].call(l,o,c,l.opts))return v(n.stop)&&(ce._queueHooks(l.elem,l.opts.queue).stop=n.stop.bind(n)),n;return ce.map(c,vt,l),v(l.opts.start)&&l.opts.start.call(o,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),ce.fx.timer(ce.extend(u,{elem:o,anim:l,queue:l.opts.queue})),l}ce.Animation=ce.extend(yt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return te(n.elem,e,Y.exec(t),n),n}]},tweener:function(e,t){v(e)?(t=e,e=["*"]):e=e.match(D);for(var n,r=0,i=e.length;r<i;r++)n=e[r],yt.tweeners[n]=yt.tweeners[n]||[],yt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,u,l,c,f="width"in t||"height"in t,p=this,d={},h=e.style,g=e.nodeType&&ee(e),v=_.get(e,"fxshow");for(r in n.queue||(null==(a=ce._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always(function(){p.always(function(){a.unqueued--,ce.queue(e,"fx").length||a.empty.fire()})})),t)if(i=t[r],ft.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!v||void 0===v[r])continue;g=!0}d[r]=v&&v[r]||ce.style(e,r)}if((u=!ce.isEmptyObject(t))||!ce.isEmptyObject(d))for(r in f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=v&&v.display)&&(l=_.get(e,"display")),"none"===(c=ce.css(e,"display"))&&(l?c=l:(re([e],!0),l=e.style.display||l,c=ce.css(e,"display"),re([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===ce.css(e,"float")&&(u||(p.done(function(){h.display=l}),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1,d)u||(v?"hidden"in v&&(g=v.hidden):v=_.access(e,"fxshow",{display:l}),o&&(v.hidden=!g),g&&re([e],!0),p.done(function(){for(r in g||re([e]),_.remove(e,"fxshow"),d)ce.style(e,r,d[r])})),u=vt(g?v[r]:0,r,p),r in v||(v[r]=u.start,g&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?yt.prefilters.unshift(e):yt.prefilters.push(e)}}),ce.speed=function(e,t,n){var r=e&&"object"==typeof e?ce.extend({},e):{complete:n||!n&&t||v(e)&&e,duration:e,easing:n&&t||t&&!v(t)&&t};return ce.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in ce.fx.speeds?r.duration=ce.fx.speeds[r.duration]:r.duration=ce.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){v(r.old)&&r.old.call(this),r.queue&&ce.dequeue(this,r.queue)},r},ce.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ee).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){var i=ce.isEmptyObject(t),o=ce.speed(e,n,r),a=function(){var e=yt(this,ce.extend({},t),o);(i||_.get(this,"finish"))&&e.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(i,e,o){var a=function(e){var t=e.stop;delete e.stop,t(o)};return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=ce.timers,r=_.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&pt.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||ce.dequeue(this,i)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=_.get(this),n=t[a+"queue"],r=t[a+"queueHooks"],i=ce.timers,o=n?n.length:0;for(t.finish=!0,ce.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),ce.each(["toggle","show","hide"],function(e,r){var i=ce.fn[r];ce.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(gt(r,!0),e,t,n)}}),ce.each({slideDown:gt("show"),slideUp:gt("hide"),slideToggle:gt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){ce.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),ce.timers=[],ce.fx.tick=function(){var e,t=0,n=ce.timers;for(st=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||ce.fx.stop(),st=void 0},ce.fx.timer=function(e){ce.timers.push(e),ce.fx.start()},ce.fx.interval=13,ce.fx.start=function(){ut||(ut=!0,dt())},ce.fx.stop=function(){ut=null},ce.fx.speeds={slow:600,fast:200,_default:400},ce.fn.delay=function(r,e){return r=ce.fx&&ce.fx.speeds[r]||r,e=e||"fx",this.queue(e,function(e,t){var n=ie.setTimeout(e,r);t.stop=function(){ie.clearTimeout(n)}})},lt=C.createElement("input"),ct=C.createElement("select").appendChild(C.createElement("option")),lt.type="checkbox",le.checkOn=""!==lt.value,le.optSelected=ct.selected,(lt=C.createElement("input")).value="t",lt.type="radio",le.radioValue="t"===lt.value;var mt,xt=ce.expr.attrHandle;ce.fn.extend({attr:function(e,t){return M(this,ce.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){ce.removeAttr(this,e)})}}),ce.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?ce.prop(e,t,n):(1===o&&ce.isXMLDoc(e)||(i=ce.attrHooks[t.toLowerCase()]||(ce.expr.match.bool.test(t)?mt:void 0)),void 0!==n?null===n?void ce.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=ce.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!le.radioValue&&"radio"===t&&fe(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(D);if(i&&1===e.nodeType)while(n=i[r++])e.removeAttribute(n)}}),mt={set:function(e,t,n){return!1===t?ce.removeAttr(e,n):e.setAttribute(n,n),n}},ce.each(ce.expr.match.bool.source.match(/\w+/g),function(e,t){var a=xt[t]||ce.find.attr;xt[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=xt[o],xt[o]=r,r=null!=a(e,t,n)?o:null,xt[o]=i),r}});var bt=/^(?:input|select|textarea|button)$/i,wt=/^(?:a|area)$/i;function Tt(e){return(e.match(D)||[]).join(" ")}function Ct(e){return e.getAttribute&&e.getAttribute("class")||""}function kt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(D)||[]}ce.fn.extend({prop:function(e,t){return M(this,ce.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[ce.propFix[e]||e]})}}),ce.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&ce.isXMLDoc(e)||(t=ce.propFix[t]||t,i=ce.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=ce.find.attr(e,"tabindex");return t?parseInt(t,10):bt.test(e.nodeName)||wt.test(e.nodeName)&&e.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),le.optSelected||(ce.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),ce.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){ce.propFix[this.toLowerCase()]=this}),ce.fn.extend({addClass:function(t){var e,n,r,i,o,a;return v(t)?this.each(function(e){ce(this).addClass(t.call(this,e,Ct(this)))}):(e=kt(t)).length?this.each(function(){if(r=Ct(this),n=1===this.nodeType&&" "+Tt(r)+" "){for(o=0;o<e.length;o++)i=e[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");a=Tt(n),r!==a&&this.setAttribute("class",a)}}):this},removeClass:function(t){var e,n,r,i,o,a;return v(t)?this.each(function(e){ce(this).removeClass(t.call(this,e,Ct(this)))}):arguments.length?(e=kt(t)).length?this.each(function(){if(r=Ct(this),n=1===this.nodeType&&" "+Tt(r)+" "){for(o=0;o<e.length;o++){i=e[o];while(-1<n.indexOf(" "+i+" "))n=n.replace(" "+i+" "," ")}a=Tt(n),r!==a&&this.setAttribute("class",a)}}):this:this.attr("class","")},toggleClass:function(t,n){var e,r,i,o,a=typeof t,s="string"===a||Array.isArray(t);return v(t)?this.each(function(e){ce(this).toggleClass(t.call(this,e,Ct(this),n),n)}):"boolean"==typeof n&&s?n?this.addClass(t):this.removeClass(t):(e=kt(t),this.each(function(){if(s)for(o=ce(this),i=0;i<e.length;i++)r=e[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==t&&"boolean"!==a||((r=Ct(this))&&_.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===t?"":_.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,r=0;t=" "+e+" ";while(n=this[r++])if(1===n.nodeType&&-1<(" "+Tt(Ct(n))+" ").indexOf(t))return!0;return!1}});var St=/\r/g;ce.fn.extend({val:function(n){var r,e,i,t=this[0];return arguments.length?(i=v(n),this.each(function(e){var t;1===this.nodeType&&(null==(t=i?n.call(this,e,ce(this).val()):n)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=ce.map(t,function(e){return null==e?"":e+""})),(r=ce.valHooks[this.type]||ce.valHooks[this.nodeName.toLowerCase()])&&"set"in r&&void 0!==r.set(this,t,"value")||(this.value=t))})):t?(r=ce.valHooks[t.type]||ce.valHooks[t.nodeName.toLowerCase()])&&"get"in r&&void 0!==(e=r.get(t,"value"))?e:"string"==typeof(e=t.value)?e.replace(St,""):null==e?"":e:void 0}}),ce.extend({valHooks:{option:{get:function(e){var t=ce.find.attr(e,"value");return null!=t?t:Tt(ce.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],u=a?o+1:i.length;for(r=o<0?u:a?o:0;r<u;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!fe(n.parentNode,"optgroup"))){if(t=ce(n).val(),a)return t;s.push(t)}return s},set:function(e,t){var n,r,i=e.options,o=ce.makeArray(t),a=i.length;while(a--)((r=i[a]).selected=-1<ce.inArray(ce.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),ce.each(["radio","checkbox"],function(){ce.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<ce.inArray(ce(e).val(),t)}},le.checkOn||(ce.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var Et=ie.location,jt={guid:Date.now()},At=/\?/;ce.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new ie.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||ce.error("Invalid XML: "+(n?ce.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t};var Dt=/^(?:focusinfocus|focusoutblur)$/,Nt=function(e){e.stopPropagation()};ce.extend(ce.event,{trigger:function(e,t,n,r){var i,o,a,s,u,l,c,f,p=[n||C],d=ue.call(e,"type")?e.type:e,h=ue.call(e,"namespace")?e.namespace.split("."):[];if(o=f=a=n=n||C,3!==n.nodeType&&8!==n.nodeType&&!Dt.test(d+ce.event.triggered)&&(-1<d.indexOf(".")&&(d=(h=d.split(".")).shift(),h.sort()),u=d.indexOf(":")<0&&"on"+d,(e=e[ce.expando]?e:new ce.Event(d,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=h.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:ce.makeArray(t,[e]),c=ce.event.special[d]||{},r||!c.trigger||!1!==c.trigger.apply(n,t))){if(!r&&!c.noBubble&&!y(n)){for(s=c.delegateType||d,Dt.test(s+d)||(o=o.parentNode);o;o=o.parentNode)p.push(o),a=o;a===(n.ownerDocument||C)&&p.push(a.defaultView||a.parentWindow||ie)}i=0;while((o=p[i++])&&!e.isPropagationStopped())f=o,e.type=1<i?s:c.bindType||d,(l=(_.get(o,"events")||Object.create(null))[e.type]&&_.get(o,"handle"))&&l.apply(o,t),(l=u&&o[u])&&l.apply&&$(o)&&(e.result=l.apply(o,t),!1===e.result&&e.preventDefault());return e.type=d,r||e.isDefaultPrevented()||c._default&&!1!==c._default.apply(p.pop(),t)||!$(n)||u&&v(n[d])&&!y(n)&&((a=n[u])&&(n[u]=null),ce.event.triggered=d,e.isPropagationStopped()&&f.addEventListener(d,Nt),n[d](),e.isPropagationStopped()&&f.removeEventListener(d,Nt),ce.event.triggered=void 0,a&&(n[u]=a)),e.result}},simulate:function(e,t,n){var r=ce.extend(new ce.Event,n,{type:e,isSimulated:!0});ce.event.trigger(r,null,t)}}),ce.fn.extend({trigger:function(e,t){return this.each(function(){ce.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return ce.event.trigger(e,t,n,!0)}});var qt=/\[\]$/,Lt=/\r?\n/g,Ht=/^(?:submit|button|image|reset|file)$/i,Ot=/^(?:input|select|textarea|keygen)/i;function Pt(n,e,r,i){var t;if(Array.isArray(e))ce.each(e,function(e,t){r||qt.test(n)?i(n,t):Pt(n+"["+("object"==typeof t&&null!=t?e:"")+"]",t,r,i)});else if(r||"object"!==x(e))i(n,e);else for(t in e)Pt(n+"["+t+"]",e[t],r,i)}ce.param=function(e,t){var n,r=[],i=function(e,t){var n=v(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!ce.isPlainObject(e))ce.each(e,function(){i(this.name,this.value)});else for(n in e)Pt(n,e[n],t,i);return r.join("&")},ce.fn.extend({serialize:function(){return ce.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=ce.prop(this,"elements");return e?ce.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!ce(this).is(":disabled")&&Ot.test(this.nodeName)&&!Ht.test(e)&&(this.checked||!we.test(e))}).map(function(e,t){var n=ce(this).val();return null==n?null:Array.isArray(n)?ce.map(n,function(e){return{name:t.name,value:e.replace(Lt,"\r\n")}}):{name:t.name,value:n.replace(Lt,"\r\n")}}).get()}});var Mt=/%20/g,Rt=/#.*$/,It=/([?&])_=[^&]*/,Wt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ft=/^(?:GET|HEAD)$/,$t=/^\/\//,Bt={},_t={},zt="*/".concat("*"),Xt=C.createElement("a");function Ut(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(D)||[];if(v(t))while(n=i[r++])"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Vt(t,i,o,a){var s={},u=t===_t;function l(e){var r;return s[e]=!0,ce.each(t[e]||[],function(e,t){var n=t(i,o,a);return"string"!=typeof n||u||s[n]?u?!(r=n):void 0:(i.dataTypes.unshift(n),l(n),!1)}),r}return l(i.dataTypes[0])||!s["*"]&&l("*")}function Gt(e,t){var n,r,i=ce.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&ce.extend(!0,e,r),e}Xt.href=Et.href,ce.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Et.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Et.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":zt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":ce.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Gt(Gt(e,ce.ajaxSettings),t):Gt(ce.ajaxSettings,e)},ajaxPrefilter:Ut(Bt),ajaxTransport:Ut(_t),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var c,f,p,n,d,r,h,g,i,o,v=ce.ajaxSetup({},t),y=v.context||v,m=v.context&&(y.nodeType||y.jquery)?ce(y):ce.event,x=ce.Deferred(),b=ce.Callbacks("once memory"),w=v.statusCode||{},a={},s={},u="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(h){if(!n){n={};while(t=Wt.exec(p))n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2])}t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return h?p:null},setRequestHeader:function(e,t){return null==h&&(e=s[e.toLowerCase()]=s[e.toLowerCase()]||e,a[e]=t),this},overrideMimeType:function(e){return null==h&&(v.mimeType=e),this},statusCode:function(e){var t;if(e)if(h)T.always(e[T.status]);else for(t in e)w[t]=[w[t],e[t]];return this},abort:function(e){var t=e||u;return c&&c.abort(t),l(0,t),this}};if(x.promise(T),v.url=((e||v.url||Et.href)+"").replace($t,Et.protocol+"//"),v.type=t.method||t.type||v.method||v.type,v.dataTypes=(v.dataType||"*").toLowerCase().match(D)||[""],null==v.crossDomain){r=C.createElement("a");try{r.href=v.url,r.href=r.href,v.crossDomain=Xt.protocol+"//"+Xt.host!=r.protocol+"//"+r.host}catch(e){v.crossDomain=!0}}if(v.data&&v.processData&&"string"!=typeof v.data&&(v.data=ce.param(v.data,v.traditional)),Vt(Bt,v,t,T),h)return T;for(i in(g=ce.event&&v.global)&&0==ce.active++&&ce.event.trigger("ajaxStart"),v.type=v.type.toUpperCase(),v.hasContent=!Ft.test(v.type),f=v.url.replace(Rt,""),v.hasContent?v.data&&v.processData&&0===(v.contentType||"").indexOf("application/x-www-form-urlencoded")&&(v.data=v.data.replace(Mt,"+")):(o=v.url.slice(f.length),v.data&&(v.processData||"string"==typeof v.data)&&(f+=(At.test(f)?"&":"?")+v.data,delete v.data),!1===v.cache&&(f=f.replace(It,"$1"),o=(At.test(f)?"&":"?")+"_="+jt.guid+++o),v.url=f+o),v.ifModified&&(ce.lastModified[f]&&T.setRequestHeader("If-Modified-Since",ce.lastModified[f]),ce.etag[f]&&T.setRequestHeader("If-None-Match",ce.etag[f])),(v.data&&v.hasContent&&!1!==v.contentType||t.contentType)&&T.setRequestHeader("Content-Type",v.contentType),T.setRequestHeader("Accept",v.dataTypes[0]&&v.accepts[v.dataTypes[0]]?v.accepts[v.dataTypes[0]]+("*"!==v.dataTypes[0]?", "+zt+"; q=0.01":""):v.accepts["*"]),v.headers)T.setRequestHeader(i,v.headers[i]);if(v.beforeSend&&(!1===v.beforeSend.call(y,T,v)||h))return T.abort();if(u="abort",b.add(v.complete),T.done(v.success),T.fail(v.error),c=Vt(_t,v,t,T)){if(T.readyState=1,g&&m.trigger("ajaxSend",[T,v]),h)return T;v.async&&0<v.timeout&&(d=ie.setTimeout(function(){T.abort("timeout")},v.timeout));try{h=!1,c.send(a,l)}catch(e){if(h)throw e;l(-1,e)}}else l(-1,"No Transport");function l(e,t,n,r){var i,o,a,s,u,l=t;h||(h=!0,d&&ie.clearTimeout(d),c=void 0,p=r||"",T.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(s=function(e,t,n){var r,i,o,a,s=e.contents,u=e.dataTypes;while("*"===u[0])u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}(v,T,n)),!i&&-1<ce.inArray("script",v.dataTypes)&&ce.inArray("json",v.dataTypes)<0&&(v.converters["text script"]=function(){}),s=function(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];o=c.shift();while(o)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o]))for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(v,s,T,i),i?(v.ifModified&&((u=T.getResponseHeader("Last-Modified"))&&(ce.lastModified[f]=u),(u=T.getResponseHeader("etag"))&&(ce.etag[f]=u)),204===e||"HEAD"===v.type?l="nocontent":304===e?l="notmodified":(l=s.state,o=s.data,i=!(a=s.error))):(a=l,!e&&l||(l="error",e<0&&(e=0))),T.status=e,T.statusText=(t||l)+"",i?x.resolveWith(y,[o,l,T]):x.rejectWith(y,[T,l,a]),T.statusCode(w),w=void 0,g&&m.trigger(i?"ajaxSuccess":"ajaxError",[T,v,i?o:a]),b.fireWith(y,[T,l]),g&&(m.trigger("ajaxComplete",[T,v]),--ce.active||ce.event.trigger("ajaxStop")))}return T},getJSON:function(e,t,n){return ce.get(e,t,n,"json")},getScript:function(e,t){return ce.get(e,void 0,t,"script")}}),ce.each(["get","post"],function(e,i){ce[i]=function(e,t,n,r){return v(t)&&(r=r||n,n=t,t=void 0),ce.ajax(ce.extend({url:e,type:i,dataType:r,data:t,success:n},ce.isPlainObject(e)&&e))}}),ce.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),ce._evalUrl=function(e,t,n){return ce.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){ce.globalEval(e,t,n)}})},ce.fn.extend({wrapAll:function(e){var t;return this[0]&&(v(e)&&(e=e.call(this[0])),t=ce(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){var e=this;while(e.firstElementChild)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return v(n)?this.each(function(e){ce(this).wrapInner(n.call(this,e))}):this.each(function(){var e=ce(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=v(t);return this.each(function(e){ce(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){ce(this).replaceWith(this.childNodes)}),this}}),ce.expr.pseudos.hidden=function(e){return!ce.expr.pseudos.visible(e)},ce.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},ce.ajaxSettings.xhr=function(){try{return new ie.XMLHttpRequest}catch(e){}};var Yt={0:200,1223:204},Qt=ce.ajaxSettings.xhr();le.cors=!!Qt&&"withCredentials"in Qt,le.ajax=Qt=!!Qt,ce.ajaxTransport(function(i){var o,a;if(le.cors||Qt&&!i.crossDomain)return{send:function(e,t){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===e?r.abort():"error"===e?"number"!=typeof r.status?t(0,"error"):t(r.status,r.statusText):t(Yt[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),a=r.onerror=r.ontimeout=o("error"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&ie.setTimeout(function(){o&&a()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),ce.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),ce.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return ce.globalEval(e),e}}}),ce.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),ce.ajaxTransport("script",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=ce("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),C.head.appendChild(r[0])},abort:function(){i&&i()}}});var Jt,Kt=[],Zt=/(=)\?(?=&|$)|\?\?/;ce.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Kt.pop()||ce.expando+"_"+jt.guid++;return this[e]=!0,e}}),ce.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,a=!1!==e.jsonp&&(Zt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Zt.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=v(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Zt,"$1"+r):!1!==e.jsonp&&(e.url+=(At.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||ce.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=ie[r],ie[r]=function(){o=arguments},n.always(function(){void 0===i?ce(ie).removeProp(r):ie[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,Kt.push(r)),o&&v(i)&&i(o[0]),o=i=void 0}),"script"}),le.createHTMLDocument=((Jt=C.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Jt.childNodes.length),ce.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(le.createHTMLDocument?((r=(t=C.implementation.createHTMLDocument("")).createElement("base")).href=C.location.href,t.head.appendChild(r)):t=C),o=!n&&[],(i=w.exec(e))?[t.createElement(i[1])]:(i=Ae([e],t,o),o&&o.length&&ce(o).remove(),ce.merge([],i.childNodes)));var r,i,o},ce.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return-1<s&&(r=Tt(e.slice(s)),e=e.slice(0,s)),v(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),0<a.length&&ce.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?ce("<div>").append(ce.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},ce.expr.pseudos.animated=function(t){return ce.grep(ce.timers,function(e){return t===e.elem}).length},ce.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l=ce.css(e,"position"),c=ce(e),f={};"static"===l&&(e.style.position="relative"),s=c.offset(),o=ce.css(e,"top"),u=ce.css(e,"left"),("absolute"===l||"fixed"===l)&&-1<(o+u).indexOf("auto")?(a=(r=c.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),v(t)&&(t=t.call(e,n,ce.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+i),"using"in t?t.using.call(e,f):c.css(f)}},ce.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){ce.offset.setOffset(this,t,e)});var e,n,r=this[0];return r?r.getClientRects().length?(e=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===ce.css(r,"position"))t=r.getBoundingClientRect();else{t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;while(e&&(e===n.body||e===n.documentElement)&&"static"===ce.css(e,"position"))e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=ce(e).offset()).top+=ce.css(e,"borderTopWidth",!0),i.left+=ce.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-ce.css(r,"marginTop",!0),left:t.left-i.left-ce.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){var e=this.offsetParent;while(e&&"static"===ce.css(e,"position"))e=e.offsetParent;return e||J})}}),ce.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;ce.fn[t]=function(e){return M(this,function(e,t,n){var r;if(y(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n)return r?r[i]:e[t];r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n},t,e,arguments.length)}}),ce.each(["top","left"],function(e,n){ce.cssHooks[n]=Ye(le.pixelPosition,function(e,t){if(t)return t=Ge(e,n),_e.test(t)?ce(e).position()[n]+"px":t})}),ce.each({Height:"height",Width:"width"},function(a,s){ce.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){ce.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return M(this,function(e,t,n){var r;return y(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?ce.css(e,t,i):ce.style(e,t,n,i)},s,n?e:void 0,n)}})}),ce.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){ce.fn[t]=function(e){return this.on(t,e)}}),ce.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),ce.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){ce.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var en=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;ce.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),v(e))return r=ae.call(arguments,2),(i=function(){return e.apply(t||this,r.concat(ae.call(arguments)))}).guid=e.guid=e.guid||ce.guid++,i},ce.holdReady=function(e){e?ce.readyWait++:ce.ready(!0)},ce.isArray=Array.isArray,ce.parseJSON=JSON.parse,ce.nodeName=fe,ce.isFunction=v,ce.isWindow=y,ce.camelCase=F,ce.type=x,ce.now=Date.now,ce.isNumeric=function(e){var t=ce.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},ce.trim=function(e){return null==e?"":(e+"").replace(en,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return ce});var tn=ie.jQuery,nn=ie.$;return ce.noConflict=function(e){return ie.$===ce&&(ie.$=nn),e&&ie.jQuery===ce&&(ie.jQuery=tn),ce},"undefined"==typeof e&&(ie.jQuery=ie.$=ce),ce});
jQuery.noConflict();;
/*! jQuery Migrate v3.4.1 | (c) OpenJS Foundation and other contributors | jquery.org/license */
"undefined"==typeof jQuery.migrateMute&&(jQuery.migrateMute=!0),function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e,window)}):"object"==typeof module&&module.exports?module.exports=t(require("jquery"),window):t(jQuery,window)}(function(s,n){"use strict";function e(e){return 0<=function(e,t){for(var r=/^(\d+)\.(\d+)\.(\d+)/,n=r.exec(e)||[],o=r.exec(t)||[],a=1;a<=3;a++){if(+o[a]<+n[a])return 1;if(+n[a]<+o[a])return-1}return 0}(s.fn.jquery,e)}s.migrateVersion="3.4.1";var t=Object.create(null);s.migrateDisablePatches=function(){for(var e=0;e<arguments.length;e++)t[arguments[e]]=!0},s.migrateEnablePatches=function(){for(var e=0;e<arguments.length;e++)delete t[arguments[e]]},s.migrateIsPatchEnabled=function(e){return!t[e]},n.console&&n.console.log&&(s&&e("3.0.0")&&!e("5.0.0")||n.console.log("JQMIGRATE: jQuery 3.x-4.x REQUIRED"),s.migrateWarnings&&n.console.log("JQMIGRATE: Migrate plugin loaded multiple times"),n.console.log("JQMIGRATE: Migrate is installed"+(s.migrateMute?"":" with logging active")+", version "+s.migrateVersion));var o={};function u(e,t){var r=n.console;!s.migrateIsPatchEnabled(e)||s.migrateDeduplicateWarnings&&o[t]||(o[t]=!0,s.migrateWarnings.push(t+" ["+e+"]"),r&&r.warn&&!s.migrateMute&&(r.warn("JQMIGRATE: "+t),s.migrateTrace&&r.trace&&r.trace()))}function r(e,t,r,n,o){Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){return u(n,o),r},set:function(e){u(n,o),r=e}})}function a(e,t,r,n,o){var a=e[t];e[t]=function(){return o&&u(n,o),(s.migrateIsPatchEnabled(n)?r:a||s.noop).apply(this,arguments)}}function c(e,t,r,n,o){if(!o)throw new Error("No warning message provided");return a(e,t,r,n,o),0}function i(e,t,r,n){return a(e,t,r,n),0}s.migrateDeduplicateWarnings=!0,s.migrateWarnings=[],void 0===s.migrateTrace&&(s.migrateTrace=!0),s.migrateReset=function(){o={},s.migrateWarnings.length=0},"BackCompat"===n.document.compatMode&&u("quirks","jQuery is not compatible with Quirks Mode");var d,l,p,f={},m=s.fn.init,y=s.find,h=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/,g=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/g,v=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;for(d in i(s.fn,"init",function(e){var t=Array.prototype.slice.call(arguments);return s.migrateIsPatchEnabled("selector-empty-id")&&"string"==typeof e&&"#"===e&&(u("selector-empty-id","jQuery( '#' ) is not a valid selector"),t[0]=[]),m.apply(this,t)},"selector-empty-id"),s.fn.init.prototype=s.fn,i(s,"find",function(t){var r=Array.prototype.slice.call(arguments);if("string"==typeof t&&h.test(t))try{n.document.querySelector(t)}catch(e){t=t.replace(g,function(e,t,r,n){return"["+t+r+'"'+n+'"]'});try{n.document.querySelector(t),u("selector-hash","Attribute selector with '#' must be quoted: "+r[0]),r[0]=t}catch(e){u("selector-hash","Attribute selector with '#' was not fixed: "+r[0])}}return y.apply(this,r)},"selector-hash"),y)Object.prototype.hasOwnProperty.call(y,d)&&(s.find[d]=y[d]);c(s.fn,"size",function(){return this.length},"size","jQuery.fn.size() is deprecated and removed; use the .length property"),c(s,"parseJSON",function(){return JSON.parse.apply(null,arguments)},"parseJSON","jQuery.parseJSON is deprecated; use JSON.parse"),c(s,"holdReady",s.holdReady,"holdReady","jQuery.holdReady is deprecated"),c(s,"unique",s.uniqueSort,"unique","jQuery.unique is deprecated; use jQuery.uniqueSort"),r(s.expr,"filters",s.expr.pseudos,"expr-pre-pseudos","jQuery.expr.filters is deprecated; use jQuery.expr.pseudos"),r(s.expr,":",s.expr.pseudos,"expr-pre-pseudos","jQuery.expr[':'] is deprecated; use jQuery.expr.pseudos"),e("3.1.1")&&c(s,"trim",function(e){return null==e?"":(e+"").replace(v,"$1")},"trim","jQuery.trim is deprecated; use String.prototype.trim"),e("3.2.0")&&(c(s,"nodeName",function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},"nodeName","jQuery.nodeName is deprecated"),c(s,"isArray",Array.isArray,"isArray","jQuery.isArray is deprecated; use Array.isArray")),e("3.3.0")&&(c(s,"isNumeric",function(e){var t=typeof e;return("number"==t||"string"==t)&&!isNaN(e-parseFloat(e))},"isNumeric","jQuery.isNumeric() is deprecated"),s.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){f["[object "+t+"]"]=t.toLowerCase()}),c(s,"type",function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?f[Object.prototype.toString.call(e)]||"object":typeof e},"type","jQuery.type is deprecated"),c(s,"isFunction",function(e){return"function"==typeof e},"isFunction","jQuery.isFunction() is deprecated"),c(s,"isWindow",function(e){return null!=e&&e===e.window},"isWindow","jQuery.isWindow() is deprecated")),s.ajax&&(l=s.ajax,p=/(=)\?(?=&|$)|\?\?/,i(s,"ajax",function(){var e=l.apply(this,arguments);return e.promise&&(c(e,"success",e.done,"jqXHR-methods","jQXHR.success is deprecated and removed"),c(e,"error",e.fail,"jqXHR-methods","jQXHR.error is deprecated and removed"),c(e,"complete",e.always,"jqXHR-methods","jQXHR.complete is deprecated and removed")),e},"jqXHR-methods"),e("4.0.0")||s.ajaxPrefilter("+json",function(e){!1!==e.jsonp&&(p.test(e.url)||"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&p.test(e.data))&&u("jsonp-promotion","JSON-to-JSONP auto-promotion is deprecated")}));var j=s.fn.removeAttr,b=s.fn.toggleClass,w=/\S+/g;function x(e){return e.replace(/-([a-z])/g,function(e,t){return t.toUpperCase()})}i(s.fn,"removeAttr",function(e){var r=this,n=!1;return s.each(e.match(w),function(e,t){s.expr.match.bool.test(t)&&r.each(function(){if(!1!==s(this).prop(t))return!(n=!0)}),n&&(u("removeAttr-bool","jQuery.fn.removeAttr no longer sets boolean properties: "+t),r.prop(t,!1))}),j.apply(this,arguments)},"removeAttr-bool"),i(s.fn,"toggleClass",function(t){return void 0!==t&&"boolean"!=typeof t?b.apply(this,arguments):(u("toggleClass-bool","jQuery.fn.toggleClass( boolean ) is deprecated"),this.each(function(){var e=this.getAttribute&&this.getAttribute("class")||"";e&&s.data(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==t&&s.data(this,"__className__")||"")}))},"toggleClass-bool");var Q,A,R=!1,C=/^[a-z]/,N=/^(?:Border(?:Top|Right|Bottom|Left)?(?:Width|)|(?:Margin|Padding)?(?:Top|Right|Bottom|Left)?|(?:Min|Max)?(?:Width|Height))$/;s.swap&&s.each(["height","width","reliableMarginRight"],function(e,t){var r=s.cssHooks[t]&&s.cssHooks[t].get;r&&(s.cssHooks[t].get=function(){var e;return R=!0,e=r.apply(this,arguments),R=!1,e})}),i(s,"swap",function(e,t,r,n){var o,a,i={};for(a in R||u("swap","jQuery.swap() is undocumented and deprecated"),t)i[a]=e.style[a],e.style[a]=t[a];for(a in o=r.apply(e,n||[]),t)e.style[a]=i[a];return o},"swap"),e("3.4.0")&&"undefined"!=typeof Proxy&&(s.cssProps=new Proxy(s.cssProps||{},{set:function(){return u("cssProps","jQuery.cssProps is deprecated"),Reflect.set.apply(this,arguments)}})),e("4.0.0")?(A={animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},"undefined"!=typeof Proxy?s.cssNumber=new Proxy(A,{get:function(){return u("css-number","jQuery.cssNumber is deprecated"),Reflect.get.apply(this,arguments)},set:function(){return u("css-number","jQuery.cssNumber is deprecated"),Reflect.set.apply(this,arguments)}}):s.cssNumber=A):A=s.cssNumber,Q=s.fn.css,i(s.fn,"css",function(e,t){var r,n,o=this;return e&&"object"==typeof e&&!Array.isArray(e)?(s.each(e,function(e,t){s.fn.css.call(o,e,t)}),this):("number"==typeof t&&(r=x(e),n=r,C.test(n)&&N.test(n[0].toUpperCase()+n.slice(1))||A[r]||u("css-number",'Number-typed values are deprecated for jQuery.fn.css( "'+e+'", value )')),Q.apply(this,arguments))},"css-number");var S,P,k,H,E=s.data;i(s,"data",function(e,t,r){var n,o,a;if(t&&"object"==typeof t&&2===arguments.length){for(a in n=s.hasData(e)&&E.call(this,e),o={},t)a!==x(a)?(u("data-camelCase","jQuery.data() always sets/gets camelCased names: "+a),n[a]=t[a]):o[a]=t[a];return E.call(this,e,o),t}return t&&"string"==typeof t&&t!==x(t)&&(n=s.hasData(e)&&E.call(this,e))&&t in n?(u("data-camelCase","jQuery.data() always sets/gets camelCased names: "+t),2<arguments.length&&(n[t]=r),n[t]):E.apply(this,arguments)},"data-camelCase"),s.fx&&(k=s.Tween.prototype.run,H=function(e){return e},i(s.Tween.prototype,"run",function(){1<s.easing[this.easing].length&&(u("easing-one-arg","'jQuery.easing."+this.easing.toString()+"' should use only one argument"),s.easing[this.easing]=H),k.apply(this,arguments)},"easing-one-arg"),S=s.fx.interval,P="jQuery.fx.interval is deprecated",n.requestAnimationFrame&&Object.defineProperty(s.fx,"interval",{configurable:!0,enumerable:!0,get:function(){return n.document.hidden||u("fx-interval",P),s.migrateIsPatchEnabled("fx-interval")&&void 0===S?13:S},set:function(e){u("fx-interval",P),S=e}}));var M=s.fn.load,q=s.event.add,O=s.event.fix;s.event.props=[],s.event.fixHooks={},r(s.event.props,"concat",s.event.props.concat,"event-old-patch","jQuery.event.props.concat() is deprecated and removed"),i(s.event,"fix",function(e){var t,r=e.type,n=this.fixHooks[r],o=s.event.props;if(o.length){u("event-old-patch","jQuery.event.props are deprecated and removed: "+o.join());while(o.length)s.event.addProp(o.pop())}if(n&&!n._migrated_&&(n._migrated_=!0,u("event-old-patch","jQuery.event.fixHooks are deprecated and removed: "+r),(o=n.props)&&o.length))while(o.length)s.event.addProp(o.pop());return t=O.call(this,e),n&&n.filter?n.filter(t,e):t},"event-old-patch"),i(s.event,"add",function(e,t){return e===n&&"load"===t&&"complete"===n.document.readyState&&u("load-after-event","jQuery(window).on('load'...) called after load event occurred"),q.apply(this,arguments)},"load-after-event"),s.each(["load","unload","error"],function(e,t){i(s.fn,t,function(){var e=Array.prototype.slice.call(arguments,0);return"load"===t&&"string"==typeof e[0]?M.apply(this,e):(u("shorthand-removed-v3","jQuery.fn."+t+"() is deprecated"),e.splice(0,0,t),arguments.length?this.on.apply(this,e):(this.triggerHandler.apply(this,e),this))},"shorthand-removed-v3")}),s.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,r){c(s.fn,r,function(e,t){return 0<arguments.length?this.on(r,null,e,t):this.trigger(r)},"shorthand-deprecated-v3","jQuery.fn."+r+"() event shorthand is deprecated")}),s(function(){s(n.document).triggerHandler("ready")}),s.event.special.ready={setup:function(){this===n.document&&u("ready-event","'ready' event is deprecated")}},c(s.fn,"bind",function(e,t,r){return this.on(e,null,t,r)},"pre-on-methods","jQuery.fn.bind() is deprecated"),c(s.fn,"unbind",function(e,t){return this.off(e,null,t)},"pre-on-methods","jQuery.fn.unbind() is deprecated"),c(s.fn,"delegate",function(e,t,r,n){return this.on(t,e,r,n)},"pre-on-methods","jQuery.fn.delegate() is deprecated"),c(s.fn,"undelegate",function(e,t,r){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",r)},"pre-on-methods","jQuery.fn.undelegate() is deprecated"),c(s.fn,"hover",function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)},"pre-on-methods","jQuery.fn.hover() is deprecated");function T(e){var t=n.document.implementation.createHTMLDocument("");return t.body.innerHTML=e,t.body&&t.body.innerHTML}var F=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi;s.UNSAFE_restoreLegacyHtmlPrefilter=function(){s.migrateEnablePatches("self-closed-tags")},i(s,"htmlPrefilter",function(e){var t,r;return(r=(t=e).replace(F,"<$1></$2>"))!==t&&T(t)!==T(r)&&u("self-closed-tags","HTML tags must be properly nested and closed: "+t),e.replace(F,"<$1></$2>")},"self-closed-tags"),s.migrateDisablePatches("self-closed-tags");var D,W,_,I=s.fn.offset;return i(s.fn,"offset",function(){var e=this[0];return!e||e.nodeType&&e.getBoundingClientRect?I.apply(this,arguments):(u("offset-valid-elem","jQuery.fn.offset() requires a valid DOM element"),arguments.length?this:void 0)},"offset-valid-elem"),s.ajax&&(D=s.param,i(s,"param",function(e,t){var r=s.ajaxSettings&&s.ajaxSettings.traditional;return void 0===t&&r&&(u("param-ajax-traditional","jQuery.param() no longer uses jQuery.ajaxSettings.traditional"),t=r),D.call(this,e,t)},"param-ajax-traditional")),c(s.fn,"andSelf",s.fn.addBack,"andSelf","jQuery.fn.andSelf() is deprecated and removed, use jQuery.fn.addBack()"),s.Deferred&&(W=s.Deferred,_=[["resolve","done",s.Callbacks("once memory"),s.Callbacks("once memory"),"resolved"],["reject","fail",s.Callbacks("once memory"),s.Callbacks("once memory"),"rejected"],["notify","progress",s.Callbacks("memory"),s.Callbacks("memory")]],i(s,"Deferred",function(e){var a=W(),i=a.promise();function t(){var o=arguments;return s.Deferred(function(n){s.each(_,function(e,t){var r="function"==typeof o[e]&&o[e];a[t[1]](function(){var e=r&&r.apply(this,arguments);e&&"function"==typeof e.promise?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[t[0]+"With"](this===i?n.promise():this,r?[e]:arguments)})}),o=null}).promise()}return c(a,"pipe",t,"deferred-pipe","deferred.pipe() is deprecated"),c(i,"pipe",t,"deferred-pipe","deferred.pipe() is deprecated"),e&&e.call(a,a),a},"deferred-pipe"),s.Deferred.exceptionHook=W.exceptionHook),s});
;
/**
 * Random Popup Display for WP Popup Maker
 * 
 * This script randomly determines whether to display a popup when a user
 * is browsing the site. It uses session cookies to track whether the
 * decision has already been made for the current session.
 */
(function($, document) {
    if (typeof popupData !== 'undefined') {
    var popupId = popupData.popupId; // popup ID
    var randomChance = popupData.randomChance; // % chance to show the popup
    var sessionCookieName = 'pum_random_display_decision';
    
    // Check if we've already made a decision for this session
    function getSessionDecision() {
        if ($.pm_cookie === undefined) {
            return null;
        }
        
        var decision = $.pm_cookie(sessionCookieName) || null;
        return decision;
    }
    
    // Set the session decision
    function setSessionDecision(show) {
        if ($.pm_cookie === undefined) {
            return;
        }
        
        // Use empty cookie time parameter for session cookie
        $.pm_cookie(sessionCookieName, show ? 'show' : 'hide', '', '/');
    }
    
    // Make a random decision whether to show the popup
    function makeRandomDecision() {
        return Math.random() < randomChance;
    }
    
    // Handle the popup display logic
    function handlePopupDisplay() {
        var decision = getSessionDecision();
        
        // If we haven't made a decision yet, make one and save it
        if (decision === null) {
            decision = makeRandomDecision() ? 'show' : 'hide';
            setSessionDecision(decision === 'show');
        }
        
        // Get the popup by ID
        var $popup = $('#pum-' + popupId);
        
        if (!$popup.length) {
            return;
        }
        
        // If decision is to hide, prevent the popup from opening
        if (decision === 'hide') {
            $popup.addClass('preventOpen');
            
            // Also handle any triggers that might try to open it
            $(document).on('pumBeforeOpen', '#pum-' + popupId, function(event) {
                event.preventDefault();
                return false;
            });
        } 
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        handlePopupDisplay();
    });
}
    
}(jQuery, document));;
jQuery( ($) => {

	// If we're on mobile, add the "global-nav__mobile" class to the global nav.
	const isMobile = window.matchMedia("only screen and (max-width: 760px)").matches;
	if ( isMobile ) {
		$( '#global-navigation' ).addClass( 'global-nav__mobile' );
	}

	// We need checks later to see which menu is open based on screen size.
	let mobileMenu  = window.matchMedia("only screen and (max-width: 800px)").matches;
	let tabletMenu  = window.matchMedia("only screen and (min-width: 801px) and (max-width: 1023px)").matches;
	let desktopMenu = window.matchMedia("only screen and (min-width: 1024px)").matches;

	// On window resize, check if we're on mobile or not and add the class if we are.
	// On window resize update our menu booleans.
	$( window ).on('resize', function() {
		const isMobile = window.matchMedia("only screen and (max-width: 760px)").matches;
		mobileMenu = window.matchMedia("only screen and (max-width: 800px)").matches;
		tabletMenu = window.matchMedia("only screen and (min-width: 801px) and (max-width: 1023px)").matches;
		desktopMenu = window.matchMedia("only screen and (min-width: 1024px)").matches;
		if ( isMobile ) {
			$( '#global-navigation' ).addClass( 'global-nav__mobile' );
		} else {
			$( '#global-navigation' ).removeClass( 'global-nav__mobile' );
		}
	});

	// Upon clicking the "#global-nav-trigger", add the "hidden" attribute on the "#global-navigation" element if we have it, or remove it if we don't.
	$( '.hds-explore-nav-trigger' ).on( 'click', function() {

		// Get the button within the clicked element.
		const hdsExploreButton = this.querySelector( 'button' );

		// If the "aria-expanded" attribute is false, open the explore nav.
		if ( 'false' === hdsExploreButton.getAttribute( 'aria-expanded' ) ) {
			setupTabFlow( 'desktopTrigger' );
			openExploreNav();
		} else {
			closeExploreNav();
		}
	} );

	// Upon clicking .hds-global-menu-toggle, check if aria-expanded is true or false and open or close the menu accordingly.
	$( '#global-navigation-trigger--mobile' ).on( 'click', function( e ) {
		e.preventDefault();
		e.stopPropagation();

		// We don't need to get the button here because it's on the clicked element.
		const hdsGlobalMenuButton = e.currentTarget;

		// If the "aria-expanded" attribute is false, open the explore nav.
		if ( 'false' === hdsGlobalMenuButton.getAttribute( 'aria-expanded' ) ) {
			setupTabFlow( 'mobileTrigger' );
			openExploreNav();
		} else {
			closeExploreNav();
		}
	} );

	// Open the explore nav.
	function openExploreNav() {
		// Open the global navigation.
		$( '#global-navigation' ).attr( 'hidden', false );
		$( '#global-navigation' ).attr( 'aria-expanded', 'true' );

		// aria-expand the hds-explore-nav-trigger button.
		$( '.hds-explore-nav-trigger button' ).attr( 'aria-expanded', 'true' );
		$( '.hds-global-menu-toggle' ).attr( 'aria-expanded', 'true' );

		// Add or remove the hds global menu toggle class.
		$( '.hds-global-menu-toggle' ).addClass( 'menu-toggle-active' );
		$( '.hds-global-menu-toggle' ).attr( 'data-menu-action', 'open' );

		// Add a class to the body to prevent scrolling.
		$( 'html' ).addClass( 'html-fixed' );

		if ( ! mobileMenu ) {
			initHomeTabFlow();
		}
	}

	function closeExploreNav() {
		$( '#global-navigation' ).attr( 'hidden', 'hidden' );
		$( '#global-navigation' ).attr( 'aria-expanded', 'false' );

		// aria-expand the hds-explore-nav-trigger button.
		$( '.hds-explore-nav-trigger button' ).attr( 'aria-expanded', 'false' );
		$( '.hds-global-menu-toggle' ).attr( 'aria-expanded', 'false' );

		// Add or remove the hds global menu toggle class.
		$( '.hds-global-menu-toggle' ).removeClass( 'menu-toggle-active' );
		$( '.hds-global-menu-toggle' ).attr( 'data-menu-action', 'closed' );

		// Remove the body class.
		$( 'html' ).removeClass( 'html-fixed' );
	}

	// On click of ".global-nav__primary .usa-nav__submenu-item", log "Global Nav Clicked".
	$( '.global-nav__primary-list .hds-global-menu-item-trigger' ).on( 'click', function(e) {
		// Prevent default and stop propagation.
		e.preventDefault();
		e.stopPropagation();

		// Get the "submenu-id" of the clicked element.
		const submenuId = $( this ).attr( 'submenu-id' );

		// If the submenu ID is 'home', do the home actions.
		if ( 'global-nav-home' === submenuId ) {

			// Show the global nav home submenu.
			$( '.global-nav__home' ).attr( "hidden", false );

			// Hide the other columns.
			$( '.global-nav__secondary, .global-nav__tertiary' ).attr( 'hidden', 'hidden' );
			$( '.global-nav__secondary, .global-nav__tertiary' ).removeClass( 'global-nav__open' );

			// Add a global-nav__open class to the global-nav__home element.
			$( '.global-nav__home' ).addClass( 'global-nav__open' );

			// Remove "usa-current" class from all ".usa-nav__submenu-item" and add it to the clicked element.
			$( '.global-nav__primary-list .hds-global-menu-item-trigger' ).removeClass( 'usa-current' );
			$( this ).addClass( 'usa-current' );

			// If the clicked element has a parent of global-nav__primary, open the primary menu.
			if ( $( this ).parents( '.global-nav__primary' ).length ) {
				$( '.global-nav__primary, .global-nav__secondary' ).addClass( 'global-nav__open' );
			} else {
				$( '.global-nav__primary, .global-nav__secondary' ).removeClass( 'global-nav__open' );
			}

			// Remove active classes from primary and secondary submenu.
			$( '.global-nav__primary-submenu' ).removeClass( 'usa-active' );
			$( '.global-nav__secondary-submenu' ).removeClass( 'usa-active' );

			// Set tab flow for Desktop / Tablet
			// Mobile never displays this menu.
			if ( ! mobileMenu ) {
				const thisLink = $( this ).find('a').first();
				const firstLink = $( '.global-nav__home' ).find('a').first();
				const lastLink = $( '.global-nav__home' ).find('a').last();
				const nextLink = $( this ).next().find('a').first();

				setTabIntoSubmenu( thisLink, firstLink );
				getTabReturnPath( thisLink, firstLink );
				setTabNextPath( nextLink, lastLink );
			}

		} else {

			// Hide the global nav home submenu.
			$( '.global-nav__home' ).attr( 'hidden', 'hidden' );

			// Remove the hidden class from the secondary and tertiary columns.
			$( '.global-nav__secondary, .global-nav__tertiary' ).attr( 'hidden', false );

			// Remove "usa-current" class from all ".usa-nav__submenu-item" and add it to the clicked element.
			$( '.global-nav__primary-list .hds-global-menu-item-trigger' ).removeClass( 'usa-current' );
			$( this ).addClass( 'usa-current' );

			// Remove the "usa-active" class from all "global_nav__secondary" elements and add it to the matching "submenu-id".
			$( '.global-nav__secondary-submenu' ).removeClass( 'usa-active' );
			$( '.global-nav__secondary-submenu[submenu-id="' + submenuId + '"]' ).addClass( 'usa-active' );

			// Do the same thing with the tertiary submenu.
			$( '.global-nav__tertiary-submenu' ).removeClass( 'usa-active' );
			$( '.global-nav__tertiary-submenu[submenu-id="' + submenuId + '"]' ).addClass( 'usa-active' );

			// If the clicked element has a parent of global-nav__primary, open the primary menu.
			if ( $( this ).parents( '.global-nav__primary' ).length ) {
				$( '.global-nav__primary, .global-nav__secondary' ).addClass( 'global-nav__open' );
			} else {
				$( '.global-nav__primary, .global-nav__secondary' ).removeClass( 'global-nav__open' );
			}

			// Set tab flow for Desktop / Tablet / Mobile
			const thisLink = $( this ).find('a').first();

			if ( ! mobileMenu ) {
				const firstLink = $( '.global-nav__secondary-submenu[submenu-id="' + submenuId + '"]' ).find('a').first();
				const nextLink = $( this ).next().find('a').first();
				const lastLink = $( '.global-nav__tertiary-submenu[submenu-id="' + submenuId + '"]' ).find('a').last();

				setTabIntoSubmenu( thisLink, firstLink );
				getTabReturnPath( thisLink, firstLink );
				setTabNextPath( nextLink, lastLink );
			}

			if ( mobileMenu ) {
				const backButton = $( '.global-nav__back' ).first();
				const lastSecondaryLink = $( '.global-nav__secondary-submenu[submenu-id="' + submenuId + '"]' ).find('a').last();
				setupBackButton( thisLink, backButton );
				setupLoopMenu( backButton, lastSecondaryLink );
			}
		}
	} );

	//Mark all scrollers as passive
    jQuery.event.special.touchstart = {
        setup: function( _, ns, handle ) {
            this.addEventListener("touchstart", handle, { passive: !ns.includes("noPreventDefault") });
        }
    };
    jQuery.event.special.touchmove = {
        setup: function( _, ns, handle ) {
            this.addEventListener("touchmove", handle, { passive: !ns.includes("noPreventDefault") });
        }
    };

	// Add a click event listener to the document
	$(document).click(function(e) {

		// Check if the clicked element is a button or a submenu
		if ( ! $(e.target).parents('.hds-nav__inner').length ) {

			// Close all nav menus
			closeNavSubMenus();
		}

		// Check if the clicked element is a button or a submenu
		if ( ! $(e.target).parents('.global-nav__primary-wrapper, .hds-global-menu-toggle, .hds-explore-nav-trigger, #global-navigation').length ) {

			// Close the explore menu.
			closeExploreNav();
		}

	});

	// On click of ".global-nav__primary .usa-nav__submenu-item", log "Global Nav Clicked".
	// Add a click event listener to each menu item with a submenu
	$('.hds-nav-has-submenu button').click(function(e) {
		// Prevent the default behavior of clicking a link
		e.preventDefault();
	  
		const thisButton = this;
		const thisSubmenu = this.parentElement.querySelector('.hds-nav__submenu');

		// Check if the menu is open.
		const isOpen = thisButton.getAttribute( 'aria-expanded' ) == 'true';

		// close all nav menus
		closeNavSubMenus();
	  
		if ( ! isOpen ) {
		  thisButton.setAttribute('aria-expanded', "true");
		  thisSubmenu.removeAttribute('hidden');
		}
	  
	});
	  
	function closeNavSubMenus() {
		let subMenulis = document.querySelectorAll('.hds-nav-has-submenu');

		subMenulis.forEach(subMenu => {

			const linkButton = subMenu.querySelector('button');
			const subMenuLi = subMenu.querySelector('.hds-nav__submenu');

			// If there is a button.
			if (linkButton) {
				linkButton.setAttribute('aria-expanded', "false");

				// Adding this check so the console doesn't throw errors when it doesn't exist.
				if (subMenuLi) {
					subMenuLi.setAttribute('hidden', "true");
				}
			}
		});
	}

	// Handling Search Panel
	// Upon clicking hds-search-panel-mobile-trigger, add the "hds-active" class to .hds-search-panel-mobile.
	$('.hds-search-panel-mobile-trigger').on('click', function(e) {
		// Prevent default.
		e.preventDefault();

		$('.hds-search-panel-mobile').removeClass('hds-inactive');
		$('.hds-search-panel-mobile').addClass('hds-active');

		// Wait until after the animation.
		setTimeout(function(){
			$('html').addClass('html-fixed');
			// Focus on the search input. after the animation.
			$('#search-field-en-small--mobile').focus();
		}, 401);
	});
  
	// Upon clicking .hds-search-panel-bar-close-icon, remove the "hds-active" class from the search panel.
	$('.hds-search-panel-bar-close-icon').on('click', function(e) {
		// Prevent default.
		e.preventDefault();

		$('.hds-search-panel-mobile').removeClass('hds-active');
		$('.hds-search-panel-mobile').addClass('hds-inactive');

		// Remove the html-fixed class from the html.
		$('html').removeClass('html-fixed');

		// Wait until after the animation.
		setTimeout(function() {
			$('.hds-search-panel-mobile').removeClass('hds-inactive');
			// Focus on the search icon. after the animation.
			$('.hds-search-panel-mobile-trigger').focus();
		}, 401);
	});


	// Function to control direction flow when expand menu is opened.
	function setupTabFlow(trigger) {

		if ( 'desktopTrigger' === trigger) {
			$( '#global-navigation-trigger' ).on( 'keydown', desktopTabExplore );
			$( '.hds-global-menu-item' ).on( 'keydown', desktopTabFlow );
			desktopSetupSkipToContent();
		}

		if ( 'mobileTrigger' === trigger) {
			$( '#global-navigation-trigger--mobile' ).on( 'keydown', mobileTabExplore );
			$( '.hds-global-menu-item' ).on( 'keydown', mobileTabFlow );
			mobileSetupSkipToContent();
		}
	}

	/* Functions that need to update continuously. */

	// Desktop
	$( '#search-field-en-small--desktop' ).on( 'keydown', desktopGoToLastItem );
	// Mobile

	$( '#mobile-header-logo' ).on( 'keydown', mobileGoToLastItem );

	// override HomeLink on mobile only.
	if ( mobileMenu ) {
		overrideHomeLink();
	}


	/* .on() callback functions */

	// removes global-nav__open on all menus and sets focus to the clickedItem.
	function handleBackButton(e, clickedItem) {
		e.stopPropagation();
		e.preventDefault();

		$( '.global-nav__primary, .global-nav__secondary' ).removeClass( 'global-nav__open' );

		// Wait until after the animation to set focus.
		setTimeout(function() {
			clickedItem.focus()
		}, 401);
	}

	// Go from the desktop search to the last menu item.
	function desktopGoToLastItem(e) {
		let flag = false;
		const desktopExpandButton = $( '#global-navigation-trigger' ).attr( 'aria-expanded' );

		if ( 'true' === desktopExpandButton ) {;
			if ( 'Tab' === e.key ) {
				if ( e.shiftKey ) {
					const menuItems = $( '.hds-global-menu-item-trigger' ).not( '.hds-global-menu-item-mobile' );
					const lastItem = menuItems[ menuItems.length - 1 ];

					$( lastItem ).find('a').first().focus();
					lastItem.focus();
					flag = true;
				}
			}
		}

		if ( flag ) {
			e.stopPropagation();
			e.preventDefault();
		}
	}

	// Go from mobile Logo to last item in menu, only if expanded.
	function mobileGoToLastItem(e) {
		let flag = false;
		const mobileExpandButton = $( '#global-navigation-trigger--mobile' ).attr( 'aria-expanded' );

		if ( 'true' === mobileExpandButton ) {
			if ( 'Tab' === e.key ) {
				switch (e.shiftKey) {
					case true:
						let menuItems = $( '.hds-global-menu-item' );

						if ( ! mobileMenu ) {
							menuItems = $( '.hds-global-menu-item-trigger' );
						}

						const lastItem = menuItems[ menuItems.length - 1 ];
						$( lastItem ).find('a').first().focus();
						flag = true;
						break;
					default:
						break;
				}
			}
		}

		if ( flag ) {
			e.stopPropagation();
			e.preventDefault();
		}
	}

	// If tab is pressed while on the explore menu.
	function desktopTabExplore(e) {
		let flag = false;

		// Skip if the menu is not expanded.
		if ( 'true' === e.currentTarget.getAttribute( 'aria-expanded' ) ) {
			// Skip if tab is not pressed.
			if ( 'Tab' === e.key ) {
				// get all menu items that expand out. And note the first item.
				const menuItems = $( '.hds-global-menu-item-trigger' );
				const firstItem = menuItems[0];
				// Skip if shift is being held.
				if ( ! e.shiftKey ) {
					// Set focus to first expanded item a tag.
					$( firstItem ).find('a').first().focus();
					flag = true;
				}
			}
		}

		if ( flag ) {
			e.stopPropagation();
            e.preventDefault();
        }
	}

	// If tab is pressed while on the menu icon.
	function mobileTabExplore(e) {
		let flag = false;

		// Skip if the menu is not open.
		if ( 'true' === e.currentTarget.getAttribute( 'aria-expanded' ) ) {
			// Skip is 'Tab' is not being pushed.
			if ( 'Tab' === e.key ) {
				// get all menu items that expand out. And note hte first item.
				const menuItems = $( '.hds-global-menu-item' );
				const firstItem = menuItems[0];

				if ( ! e.shiftKey ) {
					// Set focus to first expanded item a tag.
					$( firstItem ).find('a').first().focus();
					flag = true;
				}
			}
		}

		if ( flag ) {
			e.stopPropagation();
            e.preventDefault();
        }
	}

	// Desktop: if tab or shift tab is pressed on menu items.
	function desktopTabFlow(e) {
		let flag = false;
		const currItem = e.currentTarget;

		const menuItems = $( '.hds-global-menu-item-trigger' ).not( '.hds-global-menu-item-mobile' );
		const firstItem = menuItems[0];
		const lastItem = menuItems[ menuItems.length - 1 ];
		const nextItem = $( '#search-field-en-small--desktop' );

		if ( 'Tab' === e.key ) {
			switch ( currItem ) {
				case firstItem:
					// if reverse go to open / close
					if ( e.shiftKey ) {
						let menuExpander = $( '#global-navigation-trigger' ) ;
						// Check for mobile width
						if ( tabletMenu ) {
							menuExpander = $( '#global-navigation-trigger--mobile' );
						}

						menuExpander.focus();
						flag = true;
					}
					break;
				case lastItem:
					if ( ! e.shiftKey ) {
						// go to next header item (the logo).
						nextItem.focus();
						flag = true;
					}
					break;
				default:
					break;
			}
		}

		if ( flag ) {
			e.stopPropagation();
			e.preventDefault();
		}
	}

	// Mobile: if tab or shift tab is pressed on menu items.
	function mobileTabFlow(e) {
		let flag = false;
		const currItem = e.currentTarget;
		let menuItems = $( '.hds-global-menu-item' );

		if ( ! mobileMenu ) {
			menuItems = $( '.hds-global-menu-item-trigger' );
		}

		const firstItem = menuItems[0];
		const lastItem = menuItems[ menuItems.length - 1 ];
		const nextItem = $( '#mobile-header-logo' );

		if ( 'Tab' === e.key ) {
			switch ( currItem ) {
				case firstItem:
					// if reverse go to open / close
					if ( e.shiftKey ) {
						let menuExpander = $( '#global-navigation-trigger' ) ;
						// Check for mobile width
						if ( mobileMenu || tabletMenu ) {
							menuExpander = $( '#global-navigation-trigger--mobile' );
						}

						menuExpander.focus();
						flag = true;
					}
					break;
				case lastItem:
					if ( ! e.shiftKey ) {
						// go to next header item (the logo).
						nextItem.focus();
						flag = true;
					}
					break;
				default:
					break;
			}
		}

		if ( flag ) {
			e.stopPropagation();
			e.preventDefault();
		}
	}

	// Closes the explorer Nav when at the end of the Header and Pushing 'Tab'
	function mobileSkipToContent(e) {
		const mobileExpandButton = $( '#global-navigation-trigger--mobile' ).attr( 'aria-expanded' );

		if ( 'true' === mobileExpandButton ) {
			if ( 'Tab' === e.key ) {
				if ( ! e.shiftKey ) {
					closeExploreNav();
				}
			}
		}
	}

	// Closes the explorer Nav when at the end of the Header and pushing 'Tab'
	function skipToContent(e) {
		const desktopExpandButton = $( '#global-navigation-trigger' ).attr( 'aria-expanded' );

		if ( 'true' === desktopExpandButton ) {
			if ( 'Tab' === e.key ) {
				if ( ! e.shiftKey ) {
					closeExploreNav();
				}
			}
		}
	}

	/* Helper Functions */

	// If you are on the mobile search trigger and you hit tab
	// close the explore menu and continue to the content.
	function mobileSetupSkipToContent() {
		const searchButton = $( '.hds-search-panel-mobile-trigger' );
		$( searchButton ).on( 'keydown', mobileSkipToContent );
	}

	// At the end of the header menu close the explore menu
	// and go to the first tab in the content.
	function desktopSetupSkipToContent() {
		const lastHeaderItem = $( '.usa-nav' ).find('a').last();
		$( lastHeaderItem ).on( 'keydown', skipToContent );
	}

	// setup Home button to goto '/'
	function overrideHomeLink() {
		const homeButton = $( '.hds-global-menu-item-trigger[submenu-id="global-nav-home"]' );
		homeButton.off('click');
	}

	// setup backButton to go back to the clickedItem.
	function setupBackButton( clickedItem, backButton) {
		backButton.on( 'click', function(e) {
			handleBackButton(e, clickedItem);
		} );

		// Wait until animation is over to set new focus.
		setTimeout(function() {
			backButton.focus();
		}, 401);
	}

	// Keep focus in the current opened menu.
	function setupLoopMenu( firstLink, lastLink ) {

		firstLink.on( 'keydown', function(e) {
			let flag = false;
			if ( 'Tab' === e.key ) {
				if ( e.shiftKey ) {
					lastLink.focus();
					flag = true;
				}
			}

			if ( flag ) {
				e.stopPropagation();
				e.preventDefault();
			}
		});

		lastLink.on( 'keydown', function(e) {
			let flag = false;
			if ( 'Tab' === e.key ) {
				if ( ! e.shiftKey ) {
					firstLink.focus();
					flag = true;
				}
			}

			if ( flag ) {
				e.stopPropagation();
				e.preventDefault();
			}
		});
	}

	// Sets the tab ordering for the 'Home (Highlights)'
	// called when explore menu opens.
	function initHomeTabFlow() {
		const priMenu = $( '.hds-global-menu-primary' );
		const activeLiItem = priMenu.find('.usa-current');
		const submenuId = activeLiItem.attr( 'submenu-id' );
		const isHighlightsActive = 'global-nav-home' === submenuId ? true : false;

		if ( isHighlightsActive ) {
			const firstLink = $( '.global-nav__home' ).find('a').first();
			const lastLink = $( '.global-nav__home' ).find('a').last();
			const nextLink = activeLiItem.next().find('a').first();
			const clickedItem = activeLiItem.find('a').first();
			setTabIntoSubmenu( clickedItem, firstLink );
			getTabReturnPath( clickedItem, firstLink );
			setTabNextPath( nextLink, lastLink );
		}
	}

	// Sets tab order into the block.
	function setTabIntoSubmenu( clickedItem, firstItem ) {
		$( clickedItem ).on( 'keydown', function(e) {
			let flag = false;
			const doTab = clickedItem.parent().hasClass( 'usa-current' );
			if ( doTab ) {
				if ( 'Tab' === e.key ) {
					if ( ! e.shiftKey ) {
						$( firstItem ).focus();
						flag = true;
					}

					if ( flag ) {
						e.stopPropagation();
						e.preventDefault();
					}
				}
			}
		});
	}

	// Sets the tab from the firstItem to the clickedItem
	function getTabReturnPath( clickedItem, firstItem ) {
		$( firstItem ).on( 'keydown', function(e) {
			let flag = false;

			if ( 'Tab' === e.key ) {
				if ( e.shiftKey ) {
					$( clickedItem ).focus();
					flag = true;
				}
			}

			if ( flag ) {
				e.stopPropagation();
				e.preventDefault();
			}
		});
	}

	// Sets the tab from the lastItem to the nextItem.
	function setTabNextPath( nextItem, lastItem ) {
		// If next item is blank set it to
		// desktop: search Bar
		// mobile: header logo
		if ( 0 === nextItem.length ) {
			nextItem = $( '#search-field-en-small--desktop' );

			if ( ! desktopMenu ) {
				nextItem = $( '#mobile-header-logo' );
			}
		}

		$( lastItem ).on( 'keydown', function(e) {
			let flag = false;

			if ( 'Tab' === e.key ) {
				if ( ! e.shiftKey ) {
					$( nextItem ).focus();
					flag = true;
				}
			}

			if ( flag ) {
				e.stopPropagation();
				e.preventDefault();
			}
		});
	}

	/** JSON Endpoint: Is Live */
    $.getJSON("https://plus.nasa.gov/wp-json/nasaplus/v1/live" )
    .done(function(json) {
        if( true == json) {
            $('body').addClass('is-live');
        }
    })
    .fail(function(jqxhr, textStatus, error) {
        console.log(error);
    });

	/** Image Fix to the Blog migration CPT */
	$(document).ready(function() {
		$('.nasa-blog-template-default .alignnone img, .nasa-blog-template-default .alignleft img, .nasa-blog-template-default .alignright img, .nasa-blog-template-default .aligncenter img, .blogs-migration-template-default figure.aligncenter img, .blogs-migration-template-default figure.alignnone img, .blogs-migration-template-default figure.alignleft img, .blogs-migration-template-default figure.alignright img').each(function(i, item) {
			//The image
			let img = $(this);

			// If the image loads normally
			if (this.complete) {
				//Get the width of the image
				let w = img.width();
				//Apply the width of the image to the wrapping figure element
				if (w > 0) {
					$(item).closest('figure').css('width', w + 'px');
				}
			} else {
				//The document load was too soon and didn't complete, wait for the image...
				img.on('load', function() {
					//Get the width of the image
					let w = img.width();
					//Apply the width of the image to the wrapping figure element
					if (w > 0) {
						$(item).closest('figure').css('width', w + 'px');
					}
				});
			}

		});
		//Targets images with no figure element - adds editorial class
		$('.nasa-blog-template-default .single-blog-content img:not(figure img), .blogs-migration-template-default .single-blog-content img:not(figure img)').each(function(i, item) {
			//The image
			let img = $(this);
			//Wrap the image
			$(img).wrap('<div class="editorial-img-wrap"></div>');
		});
		//Remove the disable styles button 
		$('#edac-highlight-disable-styles').remove();
		
	});

} );
;
jQuery(($) => {
	$('.hds-toggle').click(function() {
	  $(this).toggleClass('hds-toggle-open');
	  $(this).siblings('.hds-toggle-content.hds-toggle-content-mobile').toggle(300);
	});
  });;
var luxon=function(t){"use strict";function j(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!=typeof t||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"==typeof t?t:String(t)}(r.key),r)}}function o(t,e,n){e&&j(t.prototype,e),n&&j(t,n),Object.defineProperty(t,"prototype",{writable:!1})}function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n,r=arguments[e];for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function i(t,e){t.prototype=Object.create(e.prototype),z(t.prototype.constructor=t,e)}function A(t){return(A=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function z(t,e){return(z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function q(t,e,n){return(q=function(){if("undefined"!=typeof Reflect&&Reflect.construct&&!Reflect.construct.sham){if("function"==typeof Proxy)return 1;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),1}catch(t){}}}()?Reflect.construct.bind():function(t,e,n){var r=[null];r.push.apply(r,e);e=new(Function.bind.apply(t,r));return n&&z(e,n.prototype),e}).apply(null,arguments)}function _(t){var n="function"==typeof Map?new Map:void 0;return function(t){if(null===t||-1===Function.toString.call(t).indexOf("[native code]"))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(t))return n.get(t);n.set(t,e)}function e(){return q(t,arguments,A(this).constructor)}return e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),z(e,t)}(t)}function U(t,e){if(null==t)return{};for(var n,r={},i=Object.keys(t),o=0;o<i.length;o++)n=i[o],0<=e.indexOf(n)||(r[n]=t[n]);return r}function P(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function R(t,e){var n,r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){var n;if(t)return"string"==typeof t?P(t,e):"Map"===(n="Object"===(n=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?P(t,e):void 0}(t))||e&&t&&"number"==typeof t.length)return r&&(t=r),n=0,function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e=function(t){function e(){return t.apply(this,arguments)||this}return i(e,t),e}(_(Error)),H=function(e){function t(t){return e.call(this,"Invalid DateTime: "+t.toMessage())||this}return i(t,e),t}(e),W=function(e){function t(t){return e.call(this,"Invalid Interval: "+t.toMessage())||this}return i(t,e),t}(e),J=function(e){function t(t){return e.call(this,"Invalid Duration: "+t.toMessage())||this}return i(t,e),t}(e),Y=function(t){function e(){return t.apply(this,arguments)||this}return i(e,t),e}(e),G=function(e){function t(t){return e.call(this,"Invalid unit "+t)||this}return i(t,e),t}(e),u=function(t){function e(){return t.apply(this,arguments)||this}return i(e,t),e}(e),n=function(t){function e(){return t.call(this,"Zone is an abstract class")||this}return i(e,t),e}(e),e="numeric",r="short",a="long",$={year:e,month:e,day:e},B={year:e,month:r,day:e},Q={year:e,month:r,day:e,weekday:r},K={year:e,month:a,day:e},X={year:e,month:a,day:e,weekday:a},tt={hour:e,minute:e},et={hour:e,minute:e,second:e},nt={hour:e,minute:e,second:e,timeZoneName:r},rt={hour:e,minute:e,second:e,timeZoneName:a},it={hour:e,minute:e,hourCycle:"h23"},ot={hour:e,minute:e,second:e,hourCycle:"h23"},at={hour:e,minute:e,second:e,hourCycle:"h23",timeZoneName:r},ut={hour:e,minute:e,second:e,hourCycle:"h23",timeZoneName:a},st={year:e,month:e,day:e,hour:e,minute:e},ct={year:e,month:e,day:e,hour:e,minute:e,second:e},lt={year:e,month:r,day:e,hour:e,minute:e},ft={year:e,month:r,day:e,hour:e,minute:e,second:e},dt={year:e,month:r,day:e,weekday:r,hour:e,minute:e},ht={year:e,month:a,day:e,hour:e,minute:e,timeZoneName:r},mt={year:e,month:a,day:e,hour:e,minute:e,second:e,timeZoneName:r},yt={year:e,month:a,day:e,weekday:a,hour:e,minute:e,timeZoneName:a},vt={year:e,month:a,day:e,weekday:a,hour:e,minute:e,second:e,timeZoneName:a},c=function(){function t(){}var e=t.prototype;return e.offsetName=function(t,e){throw new n},e.formatOffset=function(t,e){throw new n},e.offset=function(t){throw new n},e.equals=function(t){throw new n},o(t,[{key:"type",get:function(){throw new n}},{key:"name",get:function(){throw new n}},{key:"ianaName",get:function(){return this.name}},{key:"isUniversal",get:function(){throw new n}},{key:"isValid",get:function(){throw new n}}]),t}(),pt=null,gt=function(t){function e(){return t.apply(this,arguments)||this}i(e,t);var n=e.prototype;return n.offsetName=function(t,e){return te(t,e.format,e.locale)},n.formatOffset=function(t,e){return ie(this.offset(t),e)},n.offset=function(t){return-new Date(t).getTimezoneOffset()},n.equals=function(t){return"system"===t.type},o(e,[{key:"type",get:function(){return"system"}},{key:"name",get:function(){return(new Intl.DateTimeFormat).resolvedOptions().timeZone}},{key:"isUniversal",get:function(){return!1}},{key:"isValid",get:function(){return!0}}],[{key:"instance",get:function(){return pt=null===pt?new e:pt}}]),e}(c),wt={};var kt={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};var bt={},f=function(n){function r(t){var e=n.call(this)||this;return e.zoneName=t,e.valid=r.isValidZone(t),e}i(r,n),r.create=function(t){return bt[t]||(bt[t]=new r(t)),bt[t]},r.resetCache=function(){bt={},wt={}},r.isValidSpecifier=function(t){return this.isValidZone(t)},r.isValidZone=function(t){if(!t)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:t}).format(),!0}catch(t){return!1}};var t=r.prototype;return t.offsetName=function(t,e){return te(t,e.format,e.locale,this.name)},t.formatOffset=function(t,e){return ie(this.offset(t),e)},t.offset=function(t){var e,n,r,i,o,a,u,s,t=new Date(t);return isNaN(t)?NaN:(i=this.name,wt[i]||(wt[i]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:i,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),a=(i=(i=wt[i]).formatToParts?function(t,e){for(var n=t.formatToParts(e),r=[],i=0;i<n.length;i++){var o=n[i],a=o.type,o=o.value,u=kt[a];"era"===a?r[u]=o:b(u)||(r[u]=parseInt(o,10))}return r}(i,t):(o=t,i=(i=i).format(o).replace(/\u200E/g,""),i=(o=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(i))[1],a=o[2],[o[3],i,a,o[4],o[5],o[6],o[7]]))[0],o=i[1],e=i[2],n=i[3],u=i[4],r=i[5],i=i[6],u=24===u?0:u,s=(t=+t)%1e3,(Qt({year:a="BC"===n?1-Math.abs(a):a,month:o,day:e,hour:u,minute:r,second:i,millisecond:0})-(t-=0<=s?s:1e3+s))/6e4)},t.equals=function(t){return"iana"===t.type&&t.name===this.name},o(r,[{key:"type",get:function(){return"iana"}},{key:"name",get:function(){return this.zoneName}},{key:"isUniversal",get:function(){return!1}},{key:"isValid",get:function(){return this.valid}}]),r}(c),Tt=["base"],St=["padTo","floor"],Ot={};var Mt={};function Nt(t,e){void 0===e&&(e={});var n=JSON.stringify([t,e]),r=Mt[n];return r||(r=new Intl.DateTimeFormat(t,e),Mt[n]=r),r}var Dt={};var Et={};var Vt=null;function It(t,e,n,r,i){t=t.listingMode(n);return"error"===t?null:("en"===t?r:i)(e)}var xt=function(){function t(t,e,n){this.padTo=n.padTo||0,this.floor=n.floor||!1,n.padTo,n.floor;var r=U(n,St);(!e||0<Object.keys(r).length)&&(e=s({useGrouping:!1},n),0<n.padTo&&(e.minimumIntegerDigits=n.padTo),this.inf=(r=t,void 0===(n=e)&&(n={}),t=JSON.stringify([r,n]),(e=Dt[t])||(e=new Intl.NumberFormat(r,n),Dt[t]=e),e))}return t.prototype.format=function(t){var e;return this.inf?(e=this.floor?Math.floor(t):t,this.inf.format(e)):l(this.floor?Math.floor(t):Yt(t,3),this.padTo)},t}(),Ct=function(){function t(t,e,n){this.opts=n;var n=this.originalZone=void 0,r=(this.opts.timeZone?this.dt=t:"fixed"===t.zone.type?(r=0<=(r=t.offset/60*-1)?"Etc/GMT+"+r:"Etc/GMT"+r,0!==t.offset&&f.create(r).valid?(n=r,this.dt=t):(n="UTC",this.dt=0===t.offset?t:t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone)):"system"===t.zone.type?this.dt=t:"iana"===t.zone.type?n=(this.dt=t).zone.name:(this.dt=t.setZone(n="UTC").plus({minutes:t.offset}),this.originalZone=t.zone),s({},this.opts));r.timeZone=r.timeZone||n,this.dtf=Nt(e,r)}var e=t.prototype;return e.format=function(){return this.originalZone?this.formatToParts().map(function(t){return t.value}).join(""):this.dtf.format(this.dt.toJSDate())},e.formatToParts=function(){var e=this,t=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?t.map(function(t){return"timeZoneName"===t.type?s({},t,{value:e.originalZone.offsetName(e.dt.ts,{locale:e.dt.locale,format:e.opts.timeZoneName})}):t}):t},e.resolvedOptions=function(){return this.dtf.resolvedOptions()},t}(),Zt=function(){function t(t,e,n){var r;this.opts=s({style:"long"},n),!e&&Ht()&&(this.rtf=(e=t,(n=t=void 0===(t=n)?{}:t).base,n=U(n=t,Tt),n=JSON.stringify([e,n]),(r=Et[n])||(r=new Intl.RelativeTimeFormat(e,t),Et[n]=r),r))}var e=t.prototype;return e.format=function(t,e){if(this.rtf)return this.rtf.format(t,e);var n=e,e=t,t=this.opts.numeric,r="long"!==this.opts.style,i=(void 0===t&&(t="always"),void 0===r&&(r=!1),{years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]}),o=-1===["hours","minutes","seconds"].indexOf(n);if("auto"===t&&o){var a="days"===n;switch(e){case 1:return a?"tomorrow":"next "+i[n][0];case-1:return a?"yesterday":"last "+i[n][0];case 0:return a?"today":"this "+i[n][0]}}var t=Object.is(e,-0)||e<0,e=1===(o=Math.abs(e)),u=i[n],r=r?!e&&u[2]||u[1]:e?i[n][0]:n;return t?o+" "+r+" ago":"in "+o+" "+r},e.formatToParts=function(t,e){return this.rtf?this.rtf.formatToParts(t,e):[]},t}(),g=function(){function i(t,e,n,r){var t=function(e){var n=e.indexOf("-x-");if(-1===(n=(e=-1!==n?e.substring(0,n):e).indexOf("-u-")))return[e];try{r=Nt(e).resolvedOptions(),i=e}catch(t){var e=e.substring(0,n),r=Nt(e).resolvedOptions(),i=e}return[i,(n=r).numberingSystem,n.calendar]}(t),i=t[0],o=t[1],t=t[2];this.locale=i,this.numberingSystem=e||o||null,this.outputCalendar=n||t||null,this.intl=(i=this.locale,e=this.numberingSystem,((o=this.outputCalendar)||e)&&(i.includes("-u-")||(i+="-u"),o&&(i+="-ca-"+o),e)&&(i+="-nu-"+e),i),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=r,this.fastNumbersCached=null}i.fromOpts=function(t){return i.create(t.locale,t.numberingSystem,t.outputCalendar,t.defaultToEN)},i.create=function(t,e,n,r){void 0===r&&(r=!1);t=t||k.defaultLocale;return new i(t||(r?"en-US":Vt=Vt||(new Intl.DateTimeFormat).resolvedOptions().locale),e||k.defaultNumberingSystem,n||k.defaultOutputCalendar,t)},i.resetCache=function(){Vt=null,Mt={},Dt={},Et={}},i.fromObject=function(t){var t=void 0===t?{}:t,e=t.locale,n=t.numberingSystem,t=t.outputCalendar;return i.create(e,n,t)};var t=i.prototype;return t.listingMode=function(){var t=this.isEnglish(),e=!(null!==this.numberingSystem&&"latn"!==this.numberingSystem||null!==this.outputCalendar&&"gregory"!==this.outputCalendar);return t&&e?"en":"intl"},t.clone=function(t){return t&&0!==Object.getOwnPropertyNames(t).length?i.create(t.locale||this.specifiedLocale,t.numberingSystem||this.numberingSystem,t.outputCalendar||this.outputCalendar,t.defaultToEN||!1):this},t.redefaultToEN=function(t){return this.clone(s({},t=void 0===t?{}:t,{defaultToEN:!0}))},t.redefaultToSystem=function(t){return this.clone(s({},t=void 0===t?{}:t,{defaultToEN:!1}))},t.months=function(n,r,t){var i=this;return void 0===r&&(r=!1),It(this,n,t=void 0===t?!0:t,ce,function(){var e=r?{month:n,day:"numeric"}:{month:n},t=r?"format":"standalone";return i.monthsCache[t][n]||(i.monthsCache[t][n]=function(t){for(var e=[],n=1;n<=12;n++){var r=L.utc(2016,n,1);e.push(t(r))}return e}(function(t){return i.extract(t,e,"month")})),i.monthsCache[t][n]})},t.weekdays=function(n,r,t){var i=this;return void 0===r&&(r=!1),It(this,n,t=void 0===t?!0:t,he,function(){var e=r?{weekday:n,year:"numeric",month:"long",day:"numeric"}:{weekday:n},t=r?"format":"standalone";return i.weekdaysCache[t][n]||(i.weekdaysCache[t][n]=function(t){for(var e=[],n=1;n<=7;n++){var r=L.utc(2016,11,13+n);e.push(t(r))}return e}(function(t){return i.extract(t,e,"weekday")})),i.weekdaysCache[t][n]})},t.meridiems=function(t){var n=this;return It(this,void 0,t=void 0===t?!0:t,function(){return me},function(){var e;return n.meridiemCache||(e={hour:"numeric",hourCycle:"h12"},n.meridiemCache=[L.utc(2016,11,13,9),L.utc(2016,11,13,19)].map(function(t){return n.extract(t,e,"dayperiod")})),n.meridiemCache})},t.eras=function(t,e){var n=this;return It(this,t,e=void 0===e?!0:e,ge,function(){var e={era:t};return n.eraCache[t]||(n.eraCache[t]=[L.utc(-40,1,1),L.utc(2017,1,1)].map(function(t){return n.extract(t,e,"era")})),n.eraCache[t]})},t.extract=function(t,e,n){t=this.dtFormatter(t,e).formatToParts().find(function(t){return t.type.toLowerCase()===n});return t?t.value:null},t.numberFormatter=function(t){return new xt(this.intl,(t=void 0===t?{}:t).forceSimple||this.fastNumbers,t)},t.dtFormatter=function(t,e){return new Ct(t,this.intl,e=void 0===e?{}:e)},t.relFormatter=function(t){return void 0===t&&(t={}),new Zt(this.intl,this.isEnglish(),t)},t.listFormatter=function(t){return void 0===t&&(t={}),e=this.intl,void 0===(t=t)&&(t={}),n=JSON.stringify([e,t]),(r=Ot[n])||(r=new Intl.ListFormat(e,t),Ot[n]=r),r;var e,n,r},t.isEnglish=function(){return"en"===this.locale||"en-us"===this.locale.toLowerCase()||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")},t.equals=function(t){return this.locale===t.locale&&this.numberingSystem===t.numberingSystem&&this.outputCalendar===t.outputCalendar},o(i,[{key:"fastNumbers",get:function(){var t;return null==this.fastNumbersCached&&(this.fastNumbersCached=(!(t=this).numberingSystem||"latn"===t.numberingSystem)&&("latn"===t.numberingSystem||!t.locale||t.locale.startsWith("en")||"latn"===new Intl.DateTimeFormat(t.intl).resolvedOptions().numberingSystem)),this.fastNumbersCached}}]),i}(),Ft=null,d=function(n){function e(t){var e=n.call(this)||this;return e.fixed=t,e}i(e,n),e.instance=function(t){return 0===t?e.utcInstance:new e(t)},e.parseSpecifier=function(t){if(t){t=t.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new e(ee(t[1],t[2]))}return null};var t=e.prototype;return t.offsetName=function(){return this.name},t.formatOffset=function(t,e){return ie(this.fixed,e)},t.offset=function(){return this.fixed},t.equals=function(t){return"fixed"===t.type&&t.fixed===this.fixed},o(e,[{key:"type",get:function(){return"fixed"}},{key:"name",get:function(){return 0===this.fixed?"UTC":"UTC"+ie(this.fixed,"narrow")}},{key:"ianaName",get:function(){return 0===this.fixed?"Etc/UTC":"Etc/GMT"+ie(-this.fixed,"narrow")}},{key:"isUniversal",get:function(){return!0}},{key:"isValid",get:function(){return!0}}],[{key:"utcInstance",get:function(){return Ft=null===Ft?new e(0):Ft}}]),e}(c),Lt=function(n){function t(t){var e=n.call(this)||this;return e.zoneName=t,e}i(t,n);var e=t.prototype;return e.offsetName=function(){return null},e.formatOffset=function(){return""},e.offset=function(){return NaN},e.equals=function(){return!1},o(t,[{key:"type",get:function(){return"invalid"}},{key:"name",get:function(){return this.zoneName}},{key:"isUniversal",get:function(){return!1}},{key:"isValid",get:function(){return!1}}]),t}(c);function w(t,e){var n;return b(t)||null===t?e:t instanceof c?t:"string"==typeof t?"default"===(n=t.toLowerCase())?e:"local"===n||"system"===n?gt.instance:"utc"===n||"gmt"===n?d.utcInstance:d.parseSpecifier(n)||f.create(t):v(t)?d.instance(t):"object"==typeof t&&t.offset&&"number"==typeof t.offset?t:new Lt(t)}var jt,At=function(){return Date.now()},zt="system",qt=null,_t=null,Ut=null,Pt=60,k=function(){function t(){}return t.resetCaches=function(){g.resetCache(),f.resetCache()},o(t,null,[{key:"now",get:function(){return At},set:function(t){At=t}},{key:"defaultZone",get:function(){return w(zt,gt.instance)},set:function(t){zt=t}},{key:"defaultLocale",get:function(){return qt},set:function(t){qt=t}},{key:"defaultNumberingSystem",get:function(){return _t},set:function(t){_t=t}},{key:"defaultOutputCalendar",get:function(){return Ut},set:function(t){Ut=t}},{key:"twoDigitCutoffYear",get:function(){return Pt},set:function(t){Pt=t%100}},{key:"throwOnInvalid",get:function(){return jt},set:function(t){jt=t}}]),t}();function b(t){return void 0===t}function v(t){return"number"==typeof t}function Rt(t){return"number"==typeof t&&t%1==0}function Ht(){try{return"undefined"!=typeof Intl&&!!Intl.RelativeTimeFormat}catch(t){return!1}}function Wt(t,n,r){if(0!==t.length)return t.reduce(function(t,e){e=[n(e),e];return t&&r(t[0],e[0])===t[0]?t:e},null)[1]}function h(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function T(t,e,n){return Rt(t)&&e<=t&&t<=n}function l(t,e){void 0===e&&(e=2);t=t<0?"-"+(""+-t).padStart(e,"0"):(""+t).padStart(e,"0");return t}function m(t){if(!b(t)&&null!==t&&""!==t)return parseInt(t,10)}function y(t){if(!b(t)&&null!==t&&""!==t)return parseFloat(t)}function Jt(t){if(!b(t)&&null!==t&&""!==t)return t=1e3*parseFloat("0."+t),Math.floor(t)}function Yt(t,e,n){void 0===n&&(n=!1);e=Math.pow(10,e);return(n?Math.trunc:Math.round)(t*e)/e}function Gt(t){return t%4==0&&(t%100!=0||t%400==0)}function $t(t){return Gt(t)?366:365}function Bt(t,e){var n,r=(r=e-1)-(n=12)*Math.floor(r/n)+1;return 2==r?Gt(t+(e-r)/12)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][r-1]}function Qt(t){var e=Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,t.second,t.millisecond);return t.year<100&&0<=t.year&&(e=new Date(e)).setUTCFullYear(t.year,t.month-1,t.day),+e}function Kt(t){var e=(t+Math.floor(t/4)-Math.floor(t/100)+Math.floor(t/400))%7,t=t-1,t=(t+Math.floor(t/4)-Math.floor(t/100)+Math.floor(t/400))%7;return 4==e||3==t?53:52}function Xt(t){return 99<t?t:t>k.twoDigitCutoffYear?1900+t:2e3+t}function te(t,e,n,r){void 0===r&&(r=null);var t=new Date(t),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"},r=(r&&(i.timeZone=r),s({timeZoneName:e},i)),e=new Intl.DateTimeFormat(n,r).formatToParts(t).find(function(t){return"timezonename"===t.type.toLowerCase()});return e?e.value:null}function ee(t,e){t=parseInt(t,10),Number.isNaN(t)&&(t=0),e=parseInt(e,10)||0;return 60*t+(t<0||Object.is(t,-0)?-e:e)}function ne(t){var e=Number(t);if("boolean"==typeof t||""===t||Number.isNaN(e))throw new u("Invalid unit value "+t);return e}function re(t,e){var n,r,i={};for(n in t)h(t,n)&&null!=(r=t[n])&&(i[e(n)]=ne(r));return i}function ie(t,e){var n=Math.trunc(Math.abs(t/60)),r=Math.trunc(Math.abs(t%60)),i=0<=t?"+":"-";switch(e){case"short":return i+l(n,2)+":"+l(r,2);case"narrow":return i+n+(0<r?":"+r:"");case"techie":return i+l(n,2)+l(r,2);default:throw new RangeError("Value format "+e+" is out of range for property format")}}function oe(t){return n=t,["hour","minute","second","millisecond"].reduce(function(t,e){return t[e]=n[e],t},{});var n}var ae=["January","February","March","April","May","June","July","August","September","October","November","December"],ue=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],se=["J","F","M","A","M","J","J","A","S","O","N","D"];function ce(t){switch(t){case"narrow":return[].concat(se);case"short":return[].concat(ue);case"long":return[].concat(ae);case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var le=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],fe=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],de=["M","T","W","T","F","S","S"];function he(t){switch(t){case"narrow":return[].concat(de);case"short":return[].concat(fe);case"long":return[].concat(le);case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var me=["AM","PM"],ye=["Before Christ","Anno Domini"],ve=["BC","AD"],pe=["B","A"];function ge(t){switch(t){case"narrow":return[].concat(pe);case"short":return[].concat(ve);case"long":return[].concat(ye);default:return null}}function we(t,e){for(var n="",r=R(t);!(i=r()).done;){var i=i.value;i.literal?n+=i.val:n+=e(i.val)}return n}var ke={D:$,DD:B,DDD:K,DDDD:X,t:tt,tt:et,ttt:nt,tttt:rt,T:it,TT:ot,TTT:at,TTTT:ut,f:st,ff:lt,fff:ht,ffff:yt,F:ct,FF:ft,FFF:mt,FFFF:vt},S=function(){function d(t,e){this.opts=e,this.loc=t,this.systemLoc=null}d.create=function(t,e){return new d(t,e=void 0===e?{}:e)},d.parseFormat=function(t){for(var e=null,n="",r=!1,i=[],o=0;o<t.length;o++){var a=t.charAt(o);"'"===a?(0<n.length&&i.push({literal:r||/^\s+$/.test(n),val:n}),e=null,n="",r=!r):r||a===e?n+=a:(0<n.length&&i.push({literal:/^\s+$/.test(n),val:n}),e=n=a)}return 0<n.length&&i.push({literal:r||/^\s+$/.test(n),val:n}),i},d.macroTokenToFormatOpts=function(t){return ke[t]};var t=d.prototype;return t.formatWithSystemDefault=function(t,e){return null===this.systemLoc&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(t,s({},this.opts,e)).format()},t.formatDateTime=function(t,e){return this.loc.dtFormatter(t,s({},this.opts,e=void 0===e?{}:e)).format()},t.formatDateTimeParts=function(t,e){return this.loc.dtFormatter(t,s({},this.opts,e=void 0===e?{}:e)).formatToParts()},t.formatInterval=function(t,e){return this.loc.dtFormatter(t.start,s({},this.opts,e=void 0===e?{}:e)).dtf.formatRange(t.start.toJSDate(),t.end.toJSDate())},t.resolvedOptions=function(t,e){return this.loc.dtFormatter(t,s({},this.opts,e=void 0===e?{}:e)).resolvedOptions()},t.num=function(t,e){var n;return void 0===e&&(e=0),this.opts.forceSimple?l(t,e):(n=s({},this.opts),0<e&&(n.padTo=e),this.loc.numberFormatter(n).format(t))},t.formatDateTimeFromString=function(r,t){var n=this,i="en"===this.loc.listingMode(),e=this.loc.outputCalendar&&"gregory"!==this.loc.outputCalendar,o=function(t,e){return n.loc.extract(r,t,e)},a=function(t){return r.isOffsetFixed&&0===r.offset&&t.allowZ?"Z":r.isValid?r.zone.formatOffset(r.ts,t.format):""},u=function(){return i?me[r.hour<12?0:1]:o({hour:"numeric",hourCycle:"h12"},"dayperiod")},s=function(t,e){return i?(n=r,ce(t)[n.month-1]):o(e?{month:t}:{month:t,day:"numeric"},"month");var n},c=function(t,e){return i?(n=r,he(t)[n.weekday-1]):o(e?{weekday:t}:{weekday:t,month:"long",day:"numeric"},"weekday");var n},l=function(t){var e=d.macroTokenToFormatOpts(t);return e?n.formatWithSystemDefault(r,e):t},f=function(t){return i?(e=r,ge(t)[e.year<0?0:1]):o({era:t},"era");var e};return we(d.parseFormat(t),function(t){switch(t){case"S":return n.num(r.millisecond);case"u":case"SSS":return n.num(r.millisecond,3);case"s":return n.num(r.second);case"ss":return n.num(r.second,2);case"uu":return n.num(Math.floor(r.millisecond/10),2);case"uuu":return n.num(Math.floor(r.millisecond/100));case"m":return n.num(r.minute);case"mm":return n.num(r.minute,2);case"h":return n.num(r.hour%12==0?12:r.hour%12);case"hh":return n.num(r.hour%12==0?12:r.hour%12,2);case"H":return n.num(r.hour);case"HH":return n.num(r.hour,2);case"Z":return a({format:"narrow",allowZ:n.opts.allowZ});case"ZZ":return a({format:"short",allowZ:n.opts.allowZ});case"ZZZ":return a({format:"techie",allowZ:n.opts.allowZ});case"ZZZZ":return r.zone.offsetName(r.ts,{format:"short",locale:n.loc.locale});case"ZZZZZ":return r.zone.offsetName(r.ts,{format:"long",locale:n.loc.locale});case"z":return r.zoneName;case"a":return u();case"d":return e?o({day:"numeric"},"day"):n.num(r.day);case"dd":return e?o({day:"2-digit"},"day"):n.num(r.day,2);case"c":return n.num(r.weekday);case"ccc":return c("short",!0);case"cccc":return c("long",!0);case"ccccc":return c("narrow",!0);case"E":return n.num(r.weekday);case"EEE":return c("short",!1);case"EEEE":return c("long",!1);case"EEEEE":return c("narrow",!1);case"L":return e?o({month:"numeric",day:"numeric"},"month"):n.num(r.month);case"LL":return e?o({month:"2-digit",day:"numeric"},"month"):n.num(r.month,2);case"LLL":return s("short",!0);case"LLLL":return s("long",!0);case"LLLLL":return s("narrow",!0);case"M":return e?o({month:"numeric"},"month"):n.num(r.month);case"MM":return e?o({month:"2-digit"},"month"):n.num(r.month,2);case"MMM":return s("short",!1);case"MMMM":return s("long",!1);case"MMMMM":return s("narrow",!1);case"y":return e?o({year:"numeric"},"year"):n.num(r.year);case"yy":return e?o({year:"2-digit"},"year"):n.num(r.year.toString().slice(-2),2);case"yyyy":return e?o({year:"numeric"},"year"):n.num(r.year,4);case"yyyyyy":return e?o({year:"numeric"},"year"):n.num(r.year,6);case"G":return f("short");case"GG":return f("long");case"GGGGG":return f("narrow");case"kk":return n.num(r.weekYear.toString().slice(-2),2);case"kkkk":return n.num(r.weekYear,4);case"W":return n.num(r.weekNumber);case"WW":return n.num(r.weekNumber,2);case"o":return n.num(r.ordinal);case"ooo":return n.num(r.ordinal,3);case"q":return n.num(r.quarter);case"qq":return n.num(r.quarter,2);case"X":return n.num(Math.floor(r.ts/1e3));case"x":return n.num(r.ts);default:return l(t)}})},t.formatDurationFromString=function(t,e){var n,r=this,i=function(t){switch(t[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},e=d.parseFormat(e),o=e.reduce(function(t,e){var n=e.literal,e=e.val;return n?t:t.concat(e)},[]),t=t.shiftTo.apply(t,o.map(i).filter(function(t){return t}));return we(e,(n=t,function(t){var e=i(t);return e?r.num(n.get(e),t.length):t}))},d}(),O=function(){function t(t,e){this.reason=t,this.explanation=e}return t.prototype.toMessage=function(){return this.explanation?this.reason+": "+this.explanation:this.reason},t}(),r=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function p(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e.reduce(function(t,e){return t+e.source},"");return RegExp("^"+r+"$")}function M(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(o){return e.reduce(function(t,e){var n=t[0],r=t[1],t=t[2],e=e(o,t),t=e[0],i=e[1],e=e[2];return[s({},n,t),i||r,e]},[{},null,1]).slice(0,2)}}function N(t){if(null!=t){for(var e=arguments.length,n=new Array(1<e?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];for(var i=0,o=n;i<o.length;i++){var a=o[i],u=a[0],a=a[1],u=u.exec(t);if(u)return a(u)}}return[null,null]}function be(){for(var t=arguments.length,i=new Array(t),e=0;e<t;e++)i[e]=arguments[e];return function(t,e){for(var n={},r=0;r<i.length;r++)n[i[r]]=m(t[e+r]);return[n,null,e+r]}}var e=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,a=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,Te=RegExp(a.source+("(?:"+e.source+"?(?:\\[("+r.source+")\\])?)?")),D=RegExp("(?:T"+Te.source+")?"),Se=be("weekYear","weekNumber","weekDay"),Oe=be("year","ordinal"),e=RegExp(a.source+" ?(?:"+e.source+"|("+r.source+"))?"),r=RegExp("(?: "+e.source+")?");function Me(t,e,n){t=t[e];return b(t)?n:m(t)}function Ne(t,e){return[{hours:Me(t,e,0),minutes:Me(t,e+1,0),seconds:Me(t,e+2,0),milliseconds:Jt(t[e+3])},null,e+4]}function De(t,e){var n=!t[e]&&!t[e+1],t=ee(t[e+1],t[e+2]);return[{},n?null:d.instance(t),e+3]}function Ee(t,e){return[{},t[e]?f.create(t[e]):null,e+1]}var Ve=RegExp("^T?"+a.source+"$"),Ie=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function xe(t){function e(t,e){return void 0===e&&(e=!1),void 0!==t&&(e||t&&l)?-t:t}var n=t[0],r=t[1],i=t[2],o=t[3],a=t[4],u=t[5],s=t[6],c=t[7],t=t[8],l="-"===n[0],n=c&&"-"===c[0];return[{years:e(y(r)),months:e(y(i)),weeks:e(y(o)),days:e(y(a)),hours:e(y(u)),minutes:e(y(s)),seconds:e(y(c),"-0"===c),milliseconds:e(Jt(t),n)}]}var Ce={GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function Ze(t,e,n,r,i,o,a){e={year:2===e.length?Xt(m(e)):m(e),month:ue.indexOf(n)+1,day:m(r),hour:m(i),minute:m(o)};return a&&(e.second=m(a)),t&&(e.weekday=3<t.length?le.indexOf(t)+1:fe.indexOf(t)+1),e}var Fe=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function Le(t){var e=t[1],n=t[2],r=t[3],i=t[4],o=t[5],a=t[6],u=t[7],s=t[8],c=t[9],l=t[10],t=t[11],e=Ze(e,i,r,n,o,a,u),i=s?Ce[s]:c?0:ee(l,t);return[e,new d(i)]}var je=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,Ae=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,ze=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function qe(t){var e=t[1],n=t[2],r=t[3];return[Ze(e,t[4],r,n,t[5],t[6],t[7]),d.utcInstance]}function _e(t){var e=t[1],n=t[2],r=t[3],i=t[4],o=t[5],a=t[6];return[Ze(e,t[7],n,r,i,o,a),d.utcInstance]}var Ue=p(/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,D),Pe=p(/(\d{4})-?W(\d\d)(?:-?(\d))?/,D),Re=p(/(\d{4})-?(\d{3})/,D),He=p(Te),We=M(function(t,e){return[{year:Me(t,e),month:Me(t,e+1,1),day:Me(t,e+2,1)},null,e+3]},Ne,De,Ee),Je=M(Se,Ne,De,Ee),Ye=M(Oe,Ne,De,Ee),Ge=M(Ne,De,Ee);var $e=M(Ne);var Be=p(/(\d{4})-(\d\d)-(\d\d)/,r),Qe=p(e),Ke=M(Ne,De,Ee);var a={weeks:{days:7,hours:168,minutes:10080,seconds:604800,milliseconds:6048e5},days:{hours:24,minutes:1440,seconds:86400,milliseconds:864e5},hours:{minutes:60,seconds:3600,milliseconds:36e5},minutes:{seconds:60,milliseconds:6e4},seconds:{milliseconds:1e3}},Xe=s({years:{quarters:4,months:12,weeks:52,days:365,hours:8760,minutes:525600,seconds:31536e3,milliseconds:31536e6},quarters:{months:3,weeks:13,days:91,hours:2184,minutes:131040,seconds:7862400,milliseconds:78624e5},months:{weeks:4,days:30,hours:720,minutes:43200,seconds:2592e3,milliseconds:2592e6}},a),D=365.2425,Te=30.436875,tn=s({years:{quarters:4,months:12,weeks:D/7,days:D,hours:24*D,minutes:525949.2,seconds:525949.2*60,milliseconds:525949.2*60*1e3},quarters:{months:3,weeks:D/28,days:D/4,hours:24*D/4,minutes:131487.3,seconds:525949.2*60/4,milliseconds:7889237999.999999},months:{weeks:Te/7,days:Te,hours:24*Te,minutes:43829.1,seconds:2629746,milliseconds:2629746e3}},a),E=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],en=E.slice(0).reverse();function V(t,e,n){n={values:(n=void 0===n?!1:n)?e.values:s({},t.values,e.values||{}),loc:t.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||t.conversionAccuracy,matrix:e.matrix||t.matrix};return new I(n)}function nn(t,e,n,r,i){var t=t[i][n],o=e[n]/t,a=!(Math.sign(o)===Math.sign(r[i]))&&0!==r[i]&&Math.abs(o)<=1?(a=o)<0?Math.floor(a):Math.ceil(a):Math.trunc(o);r[i]+=a,e[n]-=a*t}var I=function(){function y(t){var e="longterm"===t.conversionAccuracy||!1,n=e?tn:Xe;t.matrix&&(n=t.matrix),this.values=t.values,this.loc=t.loc||g.create(),this.conversionAccuracy=e?"longterm":"casual",this.invalid=t.invalid||null,this.matrix=n,this.isLuxonDuration=!0}y.fromMillis=function(t,e){return y.fromObject({milliseconds:t},e)},y.fromObject=function(t,e){if(void 0===e&&(e={}),null==t||"object"!=typeof t)throw new u("Duration.fromObject: argument expected to be an object, got "+(null===t?"null":typeof t));return new y({values:re(t,y.normalizeUnit),loc:g.fromObject(e),conversionAccuracy:e.conversionAccuracy,matrix:e.matrix})},y.fromDurationLike=function(t){if(v(t))return y.fromMillis(t);if(y.isDuration(t))return t;if("object"==typeof t)return y.fromObject(t);throw new u("Unknown duration argument "+t+" of type "+typeof t)},y.fromISO=function(t,e){var n=N(t,[Ie,xe])[0];return n?y.fromObject(n,e):y.invalid("unparsable",'the input "'+t+"\" can't be parsed as ISO 8601")},y.fromISOTime=function(t,e){var n=N(t,[Ve,$e])[0];return n?y.fromObject(n,e):y.invalid("unparsable",'the input "'+t+"\" can't be parsed as ISO 8601")},y.invalid=function(t,e){if(void 0===e&&(e=null),!t)throw new u("need to specify a reason the Duration is invalid");t=t instanceof O?t:new O(t,e);if(k.throwOnInvalid)throw new J(t);return new y({invalid:t})},y.normalizeUnit=function(t){var e={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[t&&t.toLowerCase()];if(e)return e;throw new G(t)},y.isDuration=function(t){return t&&t.isLuxonDuration||!1};var t=y.prototype;return t.toFormat=function(t,e){e=s({},e=void 0===e?{}:e,{floor:!1!==e.round&&!1!==e.floor});return this.isValid?S.create(this.loc,e).formatDurationFromString(this,t):"Invalid Duration"},t.toHuman=function(n){var r=this,t=(void 0===n&&(n={}),E.map(function(t){var e=r.values[t];return b(e)?null:r.loc.numberFormatter(s({style:"unit",unitDisplay:"long"},n,{unit:t.slice(0,-1)})).format(e)}).filter(function(t){return t}));return this.loc.listFormatter(s({type:"conjunction",style:n.listStyle||"narrow"},n)).format(t)},t.toObject=function(){return this.isValid?s({},this.values):{}},t.toISO=function(){var t;return this.isValid?(t="P",0!==this.years&&(t+=this.years+"Y"),0===this.months&&0===this.quarters||(t+=this.months+3*this.quarters+"M"),0!==this.weeks&&(t+=this.weeks+"W"),0!==this.days&&(t+=this.days+"D"),0===this.hours&&0===this.minutes&&0===this.seconds&&0===this.milliseconds||(t+="T"),0!==this.hours&&(t+=this.hours+"H"),0!==this.minutes&&(t+=this.minutes+"M"),0===this.seconds&&0===this.milliseconds||(t+=Yt(this.seconds+this.milliseconds/1e3,3)+"S"),"P"===t&&(t+="T0S"),t):null},t.toISOTime=function(t){if(void 0===t&&(t={}),!this.isValid)return null;var e=this.toMillis();if(e<0||864e5<=e)return null;t=s({suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended"},t);var e=this.shiftTo("hours","minutes","seconds","milliseconds"),n="basic"===t.format?"hhmm":"hh:mm",e=(t.suppressSeconds&&0===e.seconds&&0===e.milliseconds||(n+="basic"===t.format?"ss":":ss",t.suppressMilliseconds&&0===e.milliseconds)||(n+=".SSS"),e.toFormat(n));return e=t.includePrefix?"T"+e:e},t.toJSON=function(){return this.toISO()},t.toString=function(){return this.toISO()},t.toMillis=function(){return this.as("milliseconds")},t.valueOf=function(){return this.toMillis()},t.plus=function(t){if(!this.isValid)return this;for(var e=y.fromDurationLike(t),n={},r=0,i=E;r<i.length;r++){var o=i[r];(h(e.values,o)||h(this.values,o))&&(n[o]=e.get(o)+this.get(o))}return V(this,{values:n},!0)},t.minus=function(t){return this.isValid?(t=y.fromDurationLike(t),this.plus(t.negate())):this},t.mapUnits=function(t){if(!this.isValid)return this;for(var e={},n=0,r=Object.keys(this.values);n<r.length;n++){var i=r[n];e[i]=ne(t(this.values[i],i))}return V(this,{values:e},!0)},t.get=function(t){return this[y.normalizeUnit(t)]},t.set=function(t){return this.isValid?V(this,{values:s({},this.values,re(t,y.normalizeUnit))}):this},t.reconfigure=function(t){var t=void 0===t?{}:t,e=t.locale,n=t.numberingSystem,r=t.conversionAccuracy,t=t.matrix,e=this.loc.clone({locale:e,numberingSystem:n});return V(this,{loc:e,matrix:t,conversionAccuracy:r})},t.as=function(t){return this.isValid?this.shiftTo(t).get(t):NaN},t.normalize=function(){var t,n,r;return this.isValid?(t=this.toObject(),n=this.matrix,r=t,en.reduce(function(t,e){return b(r[e])?t:(t&&nn(n,r,t,r,e),e)},null),V(this,{values:t},!0)):this},t.rescale=function(){var t;return this.isValid?(t=function(t){for(var e={},n=0,r=Object.entries(t);n<r.length;n++){var i=r[n],o=i[0],i=i[1];0!==i&&(e[o]=i)}return e}(this.normalize().shiftToAll().toObject()),V(this,{values:t},!0)):this},t.shiftTo=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(!this.isValid)return this;if(0===e.length)return this;for(var r,e=e.map(function(t){return y.normalizeUnit(t)}),i={},o={},a=this.toObject(),u=0,s=E;u<s.length;u++){var c=s[u];if(0<=e.indexOf(c)){var l,f=c,d=0;for(l in o)d+=this.matrix[l][c]*o[l],o[l]=0;v(a[c])&&(d+=a[c]);var h,m=Math.trunc(d);for(h in o[c]=(1e3*d-1e3*(i[c]=m))/1e3,a)E.indexOf(h)>E.indexOf(c)&&nn(this.matrix,a,h,i,c)}else v(a[c])&&(o[c]=a[c])}for(r in o)0!==o[r]&&(i[f]+=r===f?o[r]:o[r]/this.matrix[f][r]);return V(this,{values:i},!0).normalize()},t.shiftToAll=function(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this},t.negate=function(){if(!this.isValid)return this;for(var t={},e=0,n=Object.keys(this.values);e<n.length;e++){var r=n[e];t[r]=0===this.values[r]?0:-this.values[r]}return V(this,{values:t},!0)},t.equals=function(t){if(!this.isValid||!t.isValid)return!1;if(!this.loc.equals(t.loc))return!1;for(var e,n=0,r=E;n<r.length;n++){var i=r[n];if(e=this.values[i],i=t.values[i],!(void 0===e||0===e?void 0===i||0===i:e===i))return!1}return!0},o(y,[{key:"locale",get:function(){return this.isValid?this.loc.locale:null}},{key:"numberingSystem",get:function(){return this.isValid?this.loc.numberingSystem:null}},{key:"years",get:function(){return this.isValid?this.values.years||0:NaN}},{key:"quarters",get:function(){return this.isValid?this.values.quarters||0:NaN}},{key:"months",get:function(){return this.isValid?this.values.months||0:NaN}},{key:"weeks",get:function(){return this.isValid?this.values.weeks||0:NaN}},{key:"days",get:function(){return this.isValid?this.values.days||0:NaN}},{key:"hours",get:function(){return this.isValid?this.values.hours||0:NaN}},{key:"minutes",get:function(){return this.isValid?this.values.minutes||0:NaN}},{key:"seconds",get:function(){return this.isValid?this.values.seconds||0:NaN}},{key:"milliseconds",get:function(){return this.isValid?this.values.milliseconds||0:NaN}},{key:"isValid",get:function(){return null===this.invalid}},{key:"invalidReason",get:function(){return this.invalid?this.invalid.reason:null}},{key:"invalidExplanation",get:function(){return this.invalid?this.invalid.explanation:null}}]),y}(),rn="Invalid Interval";var on=function(){function c(t){this.s=t.start,this.e=t.end,this.invalid=t.invalid||null,this.isLuxonInterval=!0}c.invalid=function(t,e){if(void 0===e&&(e=null),!t)throw new u("need to specify a reason the Interval is invalid");t=t instanceof O?t:new O(t,e);if(k.throwOnInvalid)throw new W(t);return new c({invalid:t})},c.fromDateTimes=function(t,e){var n,t=ir(t),e=ir(e),r=(n=e,(r=t)&&r.isValid?n&&n.isValid?n<r?on.invalid("end before start","The end of an interval must be after its start, but you had start="+r.toISO()+" and end="+n.toISO()):null:on.invalid("missing or invalid end"):on.invalid("missing or invalid start"));return null==r?new c({start:t,end:e}):r},c.after=function(t,e){e=I.fromDurationLike(e),t=ir(t);return c.fromDateTimes(t,t.plus(e))},c.before=function(t,e){e=I.fromDurationLike(e),t=ir(t);return c.fromDateTimes(t.minus(e),t)},c.fromISO=function(t,e){var n,r,i,o=(t||"").split("/",2),a=o[0],u=o[1];if(a&&u){try{s=(n=L.fromISO(a,e)).isValid}catch(u){s=!1}try{i=(r=L.fromISO(u,e)).isValid}catch(u){i=!1}if(s&&i)return c.fromDateTimes(n,r);if(s){o=I.fromISO(u,e);if(o.isValid)return c.after(n,o)}else if(i){var s=I.fromISO(a,e);if(s.isValid)return c.before(r,s)}}return c.invalid("unparsable",'the input "'+t+"\" can't be parsed as ISO 8601")},c.isInterval=function(t){return t&&t.isLuxonInterval||!1};var t=c.prototype;return t.length=function(t){return void 0===t&&(t="milliseconds"),this.isValid?this.toDuration.apply(this,[t]).get(t):NaN},t.count=function(t){var e,n;return void 0===t&&(t="milliseconds"),this.isValid?(e=this.start.startOf(t),n=this.end.startOf(t),Math.floor(n.diff(e,t).get(t))+(n.valueOf()!==this.end.valueOf())):NaN},t.hasSame=function(t){return!!this.isValid&&(this.isEmpty()||this.e.minus(1).hasSame(this.s,t))},t.isEmpty=function(){return this.s.valueOf()===this.e.valueOf()},t.isAfter=function(t){return!!this.isValid&&this.s>t},t.isBefore=function(t){return!!this.isValid&&this.e<=t},t.contains=function(t){return!!this.isValid&&this.s<=t&&this.e>t},t.set=function(t){var t=void 0===t?{}:t,e=t.start,t=t.end;return this.isValid?c.fromDateTimes(e||this.s,t||this.e):this},t.splitAt=function(){var e=this;if(!this.isValid)return[];for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];for(var i=n.map(ir).filter(function(t){return e.contains(t)}).sort(),o=[],a=this.s,u=0;a<this.e;){var s=i[u]||this.e,s=+s>+this.e?this.e:s;o.push(c.fromDateTimes(a,s)),a=s,u+=1}return o},t.splitBy=function(t){var e=I.fromDurationLike(t);if(!this.isValid||!e.isValid||0===e.as("milliseconds"))return[];for(var n=this.s,r=1,i=[];n<this.e;){var o=this.start.plus(e.mapUnits(function(t){return t*r})),o=+o>+this.e?this.e:o;i.push(c.fromDateTimes(n,o)),n=o,r+=1}return i},t.divideEqually=function(t){return this.isValid?this.splitBy(this.length()/t).slice(0,t):[]},t.overlaps=function(t){return this.e>t.s&&this.s<t.e},t.abutsStart=function(t){return!!this.isValid&&+this.e==+t.s},t.abutsEnd=function(t){return!!this.isValid&&+t.e==+this.s},t.engulfs=function(t){return!!this.isValid&&this.s<=t.s&&this.e>=t.e},t.equals=function(t){return!(!this.isValid||!t.isValid)&&this.s.equals(t.s)&&this.e.equals(t.e)},t.intersection=function(t){var e;return this.isValid?(e=(this.s>t.s?this:t).s,(t=(this.e<t.e?this:t).e)<=e?null:c.fromDateTimes(e,t)):this},t.union=function(t){var e;return this.isValid?(e=(this.s<t.s?this:t).s,t=(this.e>t.e?this:t).e,c.fromDateTimes(e,t)):this},c.merge=function(t){var t=t.sort(function(t,e){return t.s-e.s}).reduce(function(t,e){var n=t[0],t=t[1];return t?t.overlaps(e)||t.abutsStart(e)?[n,t.union(e)]:[n.concat([t]),e]:[n,e]},[[],null]),e=t[0],t=t[1];return t&&e.push(t),e},c.xor=function(t){for(var e,n=null,r=0,i=[],t=t.map(function(t){return[{time:t.s,type:"s"},{time:t.e,type:"e"}]}),o=R((e=Array.prototype).concat.apply(e,t).sort(function(t,e){return t.time-e.time}));!(a=o()).done;)var a=a.value,n=1===(r+="s"===a.type?1:-1)?a.time:(n&&+n!=+a.time&&i.push(c.fromDateTimes(n,a.time)),null);return c.merge(i)},t.difference=function(){for(var e=this,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return c.xor([this].concat(n)).map(function(t){return e.intersection(t)}).filter(function(t){return t&&!t.isEmpty()})},t.toString=function(){return this.isValid?"["+this.s.toISO()+" – "+this.e.toISO()+")":rn},t.toLocaleString=function(t,e){return void 0===t&&(t=$),void 0===e&&(e={}),this.isValid?S.create(this.s.loc.clone(e),t).formatInterval(this):rn},t.toISO=function(t){return this.isValid?this.s.toISO(t)+"/"+this.e.toISO(t):rn},t.toISODate=function(){return this.isValid?this.s.toISODate()+"/"+this.e.toISODate():rn},t.toISOTime=function(t){return this.isValid?this.s.toISOTime(t)+"/"+this.e.toISOTime(t):rn},t.toFormat=function(t,e){e=(void 0===e?{}:e).separator,e=void 0===e?" – ":e;return this.isValid?""+this.s.toFormat(t)+e+this.e.toFormat(t):rn},t.toDuration=function(t,e){return this.isValid?this.e.diff(this.s,t,e):I.invalid(this.invalidReason)},t.mapEndpoints=function(t){return c.fromDateTimes(t(this.s),t(this.e))},o(c,[{key:"start",get:function(){return this.isValid?this.s:null}},{key:"end",get:function(){return this.isValid?this.e:null}},{key:"isValid",get:function(){return null===this.invalidReason}},{key:"invalidReason",get:function(){return this.invalid?this.invalid.reason:null}},{key:"invalidExplanation",get:function(){return this.invalid?this.invalid.explanation:null}}]),c}(),an=function(){function t(){}return t.hasDST=function(t){void 0===t&&(t=k.defaultZone);var e=L.now().setZone(t).set({month:12});return!t.isUniversal&&e.offset!==e.set({month:6}).offset},t.isValidIANAZone=function(t){return f.isValidZone(t)},t.normalizeZone=function(t){return w(t,k.defaultZone)},t.months=function(t,e){void 0===t&&(t="long");var e=void 0===e?{}:e,n=e.locale,r=e.numberingSystem,i=e.locObj,i=void 0===i?null:i,e=e.outputCalendar;return(i||g.create(void 0===n?null:n,void 0===r?null:r,void 0===e?"gregory":e)).months(t)},t.monthsFormat=function(t,e){void 0===t&&(t="long");var e=void 0===e?{}:e,n=e.locale,r=e.numberingSystem,i=e.locObj,i=void 0===i?null:i,e=e.outputCalendar;return(i||g.create(void 0===n?null:n,void 0===r?null:r,void 0===e?"gregory":e)).months(t,!0)},t.weekdays=function(t,e){void 0===t&&(t="long");var e=void 0===e?{}:e,n=e.locale,r=e.numberingSystem,e=e.locObj;return((void 0===e?null:e)||g.create(void 0===n?null:n,void 0===r?null:r,null)).weekdays(t)},t.weekdaysFormat=function(t,e){void 0===t&&(t="long");var e=void 0===e?{}:e,n=e.locale,r=e.numberingSystem,e=e.locObj;return((void 0===e?null:e)||g.create(void 0===n?null:n,void 0===r?null:r,null)).weekdays(t,!0)},t.meridiems=function(t){t=(void 0===t?{}:t).locale;return g.create(void 0===t?null:t).meridiems()},t.eras=function(t,e){void 0===t&&(t="short");e=(void 0===e?{}:e).locale;return g.create(void 0===e?null:e,null,"gregory").eras(t)},t.features=function(){return{relative:Ht()}},t}();function un(t,e){function n(t){return t.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf()}e=n(e)-n(t);return Math.floor(I.fromMillis(e).as("days"))}function sn(t,e,n,r){var t=function(t,e,n){for(var r,i,o={},a=t,u=0,s=[["years",function(t,e){return e.year-t.year}],["quarters",function(t,e){return e.quarter-t.quarter+4*(e.year-t.year)}],["months",function(t,e){return e.month-t.month+12*(e.year-t.year)}],["weeks",function(t,e){t=un(t,e);return(t-t%7)/7}],["days",un]];u<s.length;u++){var c=s[u],l=c[0],c=c[1];0<=n.indexOf(l)&&(o[r=l]=c(t,e),t=e<(i=a.plus(o))?(o[l]--,a.plus(o)):i)}return[t,o,i,r]}(t,e,n),i=t[0],o=t[1],a=t[2],t=t[3],u=e-i,n=n.filter(function(t){return 0<=["hours","minutes","seconds","milliseconds"].indexOf(t)}),e=(0===n.length&&(a=a<e?i.plus(((e={})[t]=1,e)):a)!==i&&(o[t]=(o[t]||0)+u/(a-i)),I.fromObject(o,r));return 0<n.length?(t=I.fromMillis(u,r)).shiftTo.apply(t,n).plus(e):e}var cn={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},ln={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},fn=cn.hanidec.replace(/[\[|\]]/g,"").split("");function x(t,e){t=t.numberingSystem;return void 0===e&&(e=""),new RegExp(""+cn[t||"latn"]+e)}var dn="missing Intl.DateTimeFormat.formatToParts support";function C(t,e){return void 0===e&&(e=function(t){return t}),{regex:t,deser:function(t){t=t[0];return e(function(t){var e=parseInt(t,10);if(isNaN(e)){for(var e="",n=0;n<t.length;n++){var r=t.charCodeAt(n);if(-1!==t[n].search(cn.hanidec))e+=fn.indexOf(t[n]);else for(var i in ln){var i=ln[i],o=i[0],i=i[1];o<=r&&r<=i&&(e+=r-o)}}return parseInt(e,10)}return e}(t))}}}var hn="[ "+String.fromCharCode(160)+"]",mn=new RegExp(hn,"g");function yn(t){return t.replace(/\./g,"\\.?").replace(mn,hn)}function vn(t){return t.replace(/\./g,"").replace(mn," ").toLowerCase()}function Z(n,r){return null===n?null:{regex:RegExp(n.map(yn).join("|")),deser:function(t){var e=t[0];return n.findIndex(function(t){return vn(e)===vn(t)})+r}}}function pn(t,e){return{regex:t,deser:function(t){return ee(t[1],t[2])},groups:e}}function gn(t){return{regex:t,deser:function(t){return t[0]}}}function wn(e,n){function r(t){return{regex:RegExp(t.val.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")),deser:function(t){return t[0]},literal:!0}}var i=x(n),o=x(n,"{2}"),a=x(n,"{3}"),u=x(n,"{4}"),s=x(n,"{6}"),c=x(n,"{1,2}"),l=x(n,"{1,3}"),f=x(n,"{1,6}"),d=x(n,"{1,9}"),h=x(n,"{2,4}"),m=x(n,"{4,6}"),t=function(t){if(e.literal)return r(t);switch(t.val){case"G":return Z(n.eras("short",!1),0);case"GG":return Z(n.eras("long",!1),0);case"y":return C(f);case"yy":return C(h,Xt);case"yyyy":return C(u);case"yyyyy":return C(m);case"yyyyyy":return C(s);case"M":return C(c);case"MM":return C(o);case"MMM":return Z(n.months("short",!0,!1),1);case"MMMM":return Z(n.months("long",!0,!1),1);case"L":return C(c);case"LL":return C(o);case"LLL":return Z(n.months("short",!1,!1),1);case"LLLL":return Z(n.months("long",!1,!1),1);case"d":return C(c);case"dd":return C(o);case"o":return C(l);case"ooo":return C(a);case"HH":return C(o);case"H":return C(c);case"hh":return C(o);case"h":return C(c);case"mm":return C(o);case"m":case"q":return C(c);case"qq":return C(o);case"s":return C(c);case"ss":return C(o);case"S":return C(l);case"SSS":return C(a);case"u":return gn(d);case"uu":return gn(c);case"uuu":return C(i);case"a":return Z(n.meridiems(),0);case"kkkk":return C(u);case"kk":return C(h,Xt);case"W":return C(c);case"WW":return C(o);case"E":case"c":return C(i);case"EEE":return Z(n.weekdays("short",!1,!1),1);case"EEEE":return Z(n.weekdays("long",!1,!1),1);case"ccc":return Z(n.weekdays("short",!0,!1),1);case"cccc":return Z(n.weekdays("long",!0,!1),1);case"Z":case"ZZ":return pn(new RegExp("([+-]"+c.source+")(?::("+o.source+"))?"),2);case"ZZZ":return pn(new RegExp("([+-]"+c.source+")("+o.source+")?"),2);case"z":return gn(/[a-z_+-/]{1,256}?/i);case" ":return gn(/[^\S\n\r]/);default:return r(t)}}(e)||{invalidReason:dn};return t.token=e,t}var kn={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour:{numeric:"h","2-digit":"hh"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};var bn=null;function Tn(t,n){var e;return(e=Array.prototype).concat.apply(e,t.map(function(t){return e=n,(t=t).literal||null==(e=On(S.macroTokenToFormatOpts(t.val),e))||e.includes(void 0)?t:e;var e}))}function Sn(e,t,n){var n=Tn(S.parseFormat(n),e),r=n.map(function(t){return wn(t,e)}),i=r.find(function(t){return t.invalidReason});if(i)return{input:t,tokens:n,invalidReason:i.invalidReason};var o,r=["^"+(i=r).map(function(t){return t.regex}).reduce(function(t,e){return t+"("+e.source+")"},"")+"$",i],i=r[1],r=RegExp(r[0],"i"),i=function(t,e,n){var r=t.match(e);if(r){var i,o,a,u={},s=1;for(i in n)h(n,i)&&(a=(o=n[i]).groups?o.groups+1:1,!o.literal&&o.token&&(u[o.token.val[0]]=o.deser(r.slice(s,s+a))),s+=a);return[r,u]}return[r,{}]}(t,r,i),a=i[0],i=i[1],u=i?(u=null,b((o=i).z)||(u=f.create(o.z)),b(o.Z)||(u=u||new d(o.Z),s=o.Z),b(o.q)||(o.M=3*(o.q-1)+1),b(o.h)||(o.h<12&&1===o.a?o.h+=12:12===o.h&&0===o.a&&(o.h=0)),0===o.G&&o.y&&(o.y=-o.y),b(o.u)||(o.S=Jt(o.u)),[Object.keys(o).reduce(function(t,e){var n=function(t){switch(t){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}}(e);return n&&(t[n]=o[e]),t},{}),u,s]):[null,null,void 0],s=u[0],c=u[1],l=u[2];if(h(i,"a")&&h(i,"H"))throw new Y("Can't include meridiem when specifying 24-hour format");return{input:t,tokens:n,regex:r,rawMatches:a,matches:i,result:s,zone:c,specificOffset:l}}function On(i,t){return i?S.create(t,i).formatDateTimeParts(bn=bn||L.fromMillis(1555555555555)).map(function(t){return e=i,r=(t=t).type,t=t.value,"literal"===r?{literal:!(n=/^\s+$/.test(t)),val:n?" ":t}:(n=e[r],(t="object"==typeof(t=kn[r])?t[n]:t)?{literal:!1,val:t}:void 0);var e,n,r}):null}var Mn=[0,31,59,90,120,151,181,212,243,273,304,334],Nn=[0,31,60,91,121,152,182,213,244,274,305,335];function F(t,e){return new O("unit out of range","you specified "+e+" (of type "+typeof e+") as a "+t+", which is invalid")}function Dn(t,e,n){e=new Date(Date.UTC(t,e-1,n)),t<100&&0<=t&&e.setUTCFullYear(e.getUTCFullYear()-1900),n=e.getUTCDay();return 0===n?7:n}function En(t,e,n){return n+(Gt(t)?Nn:Mn)[e-1]}function Vn(t,e){var t=Gt(t)?Nn:Mn,n=t.findIndex(function(t){return t<e});return{month:n+1,day:e-t[n]}}function In(t){var e,n=t.year,r=t.month,i=t.day,o=En(n,r,i),r=Dn(n,r,i),i=Math.floor((o-r+10)/7);return i<1?i=Kt(e=n-1):i>Kt(n)?(e=n+1,i=1):e=n,s({weekYear:e,weekNumber:i,weekday:r},oe(t))}function xn(t){var e,n=t.weekYear,r=t.weekNumber,i=t.weekday,o=Dn(n,1,4),a=$t(n),r=7*r+i-o-3,i=(r<1?r+=$t(e=n-1):a<r?(e=n+1,r-=$t(n)):e=n,Vn(e,r));return s({year:e,month:i.month,day:i.day},oe(t))}function Cn(t){var e=t.year;return s({year:e,ordinal:En(e,t.month,t.day)},oe(t))}function Zn(t){var e=t.year,n=Vn(e,t.ordinal);return s({year:e,month:n.month,day:n.day},oe(t))}function Fn(t){var e=Rt(t.year),n=T(t.month,1,12),r=T(t.day,1,Bt(t.year,t.month));return e?n?!r&&F("day",t.day):F("month",t.month):F("year",t.year)}function Ln(t){var e=t.hour,n=t.minute,r=t.second,t=t.millisecond,i=T(e,0,23)||24===e&&0===n&&0===r&&0===t,o=T(n,0,59),a=T(r,0,59),u=T(t,0,999);return i?o?a?!u&&F("millisecond",t):F("second",r):F("minute",n):F("hour",e)}var jn="Invalid DateTime";function An(t){return new O("unsupported zone",'the zone "'+t.name+'" is not supported')}function zn(t){return null===t.weekData&&(t.weekData=In(t.c)),t.weekData}function qn(t,e){t={ts:t.ts,zone:t.zone,c:t.c,o:t.o,loc:t.loc,invalid:t.invalid};return new L(s({},t,e,{old:t}))}function _n(t,e,n){var r=t-60*e*1e3,i=n.offset(r);return e===i?[r,e]:i===(n=n.offset(r-=60*(i-e)*1e3))?[r,i]:[t-60*Math.min(i,n)*1e3,Math.max(i,n)]}function Un(t,e){t+=60*e*1e3;e=new Date(t);return{year:e.getUTCFullYear(),month:e.getUTCMonth()+1,day:e.getUTCDate(),hour:e.getUTCHours(),minute:e.getUTCMinutes(),second:e.getUTCSeconds(),millisecond:e.getUTCMilliseconds()}}function Pn(t,e,n){return _n(Qt(t),e,n)}function Rn(t,e){var n=t.o,r=t.c.year+Math.trunc(e.years),i=t.c.month+Math.trunc(e.months)+3*Math.trunc(e.quarters),r=s({},t.c,{year:r,month:i,day:Math.min(t.c.day,Bt(r,i))+Math.trunc(e.days)+7*Math.trunc(e.weeks)}),i=I.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),e=_n(Qt(r),n,t.zone),r=e[0],n=e[1];return 0!==i&&(n=t.zone.offset(r+=i)),{ts:r,o:n}}function Hn(t,e,n,r,i,o){var a=n.setZone,u=n.zone;return t&&0!==Object.keys(t).length||e?(t=L.fromObject(t,s({},n,{zone:e||u,specificOffset:o})),a?t:t.setZone(u)):L.invalid(new O("unparsable",'the input "'+i+"\" can't be parsed as "+r))}function Wn(t,e,n){return void 0===n&&(n=!0),t.isValid?S.create(g.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(t,e):null}function Jn(t,e){var n=9999<t.c.year||t.c.year<0,r="";return n&&0<=t.c.year&&(r+="+"),r+=l(t.c.year,n?6:4),r=e?(r=(r+="-")+l(t.c.month)+"-")+l(t.c.day):(r+=l(t.c.month))+l(t.c.day)}function Yn(t,e,n,r,i,o){var a=l(t.c.hour);return e?(a=(a+=":")+l(t.c.minute),0===t.c.second&&n||(a+=":")):a+=l(t.c.minute),0===t.c.second&&n||(a+=l(t.c.second),0===t.c.millisecond&&r)||(a=(a+=".")+l(t.c.millisecond,3)),i&&(t.isOffsetFixed&&0===t.offset&&!o?a+="Z":a=t.o<0?(a=(a+="-")+l(Math.trunc(-t.o/60))+":")+l(Math.trunc(-t.o%60)):(a=(a+="+")+l(Math.trunc(t.o/60))+":")+l(Math.trunc(t.o%60))),o&&(a+="["+t.zone.ianaName+"]"),a}var Gn={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},$n={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Bn={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Qn=["year","month","day","hour","minute","second","millisecond"],Kn=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],Xn=["year","ordinal","hour","minute","second","millisecond"];function tr(t){var e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[t.toLowerCase()];if(e)return e;throw new G(t)}function er(t,e){var n=w(e.zone,k.defaultZone),e=g.fromObject(e),r=k.now();if(b(t.year))s=r;else{for(var i=0,o=Qn;i<o.length;i++){var a=o[i];b(t[a])&&(t[a]=Gn[a])}var u=Fn(t)||Ln(t);if(u)return L.invalid(u);var u=Pn(t,n.offset(r),n),s=u[0],u=u[1]}return new L({ts:s,zone:n,loc:e,o:u})}function nr(e,n,r){function t(t,e){return t=Yt(t,o||r.calendary?0:2,!0),n.loc.clone(r).relFormatter(r).format(t,e)}function i(t){return r.calendary?n.hasSame(e,t)?0:n.startOf(t).diff(e.startOf(t),t).get(t):n.diff(e,t).get(t)}var o=!!b(r.round)||r.round;if(r.unit)return t(i(r.unit),r.unit);for(var a=R(r.units);!(u=a()).done;){var u=u.value,s=i(u);if(1<=Math.abs(s))return t(s,u)}return t(n<e?-0:0,r.units[r.units.length-1])}function rr(t){var e={},t=0<t.length&&"object"==typeof t[t.length-1]?(e=t[t.length-1],Array.from(t).slice(0,t.length-1)):Array.from(t);return[e,t]}var L=function(){function p(t){var e,n=t.zone||k.defaultZone,r=t.invalid||(Number.isNaN(t.ts)?new O("invalid input"):null)||(n.isValid?null:An(n)),i=(this.ts=b(t.ts)?k.now():t.ts,null),o=null;r||(o=t.old&&t.old.ts===this.ts&&t.old.zone.equals(n)?(i=(e=[t.old.c,t.old.o])[0],e[1]):(e=n.offset(this.ts),i=Un(this.ts,e),i=(r=Number.isNaN(i.year)?new O("invalid input"):null)?null:i,r?null:e)),this._zone=n,this.loc=t.loc||g.create(),this.invalid=r,this.weekData=null,this.c=i,this.o=o,this.isLuxonDateTime=!0}p.now=function(){return new p({})},p.local=function(){var t=rr(arguments),e=t[0],t=t[1];return er({year:t[0],month:t[1],day:t[2],hour:t[3],minute:t[4],second:t[5],millisecond:t[6]},e)},p.utc=function(){var t=rr(arguments),e=t[0],t=t[1],n=t[0],r=t[1],i=t[2],o=t[3],a=t[4],u=t[5],t=t[6];return e.zone=d.utcInstance,er({year:n,month:r,day:i,hour:o,minute:a,second:u,millisecond:t},e)},p.fromJSDate=function(t,e){void 0===e&&(e={});var n,t="[object Date]"===Object.prototype.toString.call(t)?t.valueOf():NaN;return Number.isNaN(t)?p.invalid("invalid input"):(n=w(e.zone,k.defaultZone)).isValid?new p({ts:t,zone:n,loc:g.fromObject(e)}):p.invalid(An(n))},p.fromMillis=function(t,e){if(void 0===e&&(e={}),v(t))return t<-864e13||864e13<t?p.invalid("Timestamp out of range"):new p({ts:t,zone:w(e.zone,k.defaultZone),loc:g.fromObject(e)});throw new u("fromMillis requires a numerical input, but received a "+typeof t+" with value "+t)},p.fromSeconds=function(t,e){if(void 0===e&&(e={}),v(t))return new p({ts:1e3*t,zone:w(e.zone,k.defaultZone),loc:g.fromObject(e)});throw new u("fromSeconds requires a numerical input")},p.fromObject=function(t,e){t=t||{};var n=w((e=void 0===e?{}:e).zone,k.defaultZone);if(!n.isValid)return p.invalid(An(n));var r=k.now(),i=b(e.specificOffset)?n.offset(r):e.specificOffset,o=re(t,tr),a=!b(o.ordinal),u=!b(o.year),s=!b(o.month)||!b(o.day),u=u||s,c=o.weekYear||o.weekNumber,e=g.fromObject(e);if((u||a)&&c)throw new Y("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(s&&a)throw new Y("Can't mix ordinal dates with month/day");for(var l,s=c||o.weekday&&!u,f=Un(r,i),d=(s?(v=Kn,l=$n,f=In(f)):a?(v=Xn,l=Bn,f=Cn(f)):(v=Qn,l=Gn),!1),h=R(v);!(m=h()).done;){var m=m.value;b(o[m])?o[m]=(d?l:f)[m]:d=!0}var y,v,c=(s?(r=Rt((c=o).weekYear),v=T(c.weekNumber,1,Kt(c.weekYear)),y=T(c.weekday,1,7),r?v?!y&&F("weekday",c.weekday):F("week",c.week):F("weekYear",c.weekYear)):a?(v=Rt((r=o).year),y=T(r.ordinal,1,$t(r.year)),v?!y&&F("ordinal",r.ordinal):F("year",r.year)):Fn(o))||Ln(o);return c?p.invalid(c):(r=new p({ts:(v=Pn(s?xn(o):a?Zn(o):o,i,n))[0],zone:n,o:v[1],loc:e}),o.weekday&&u&&t.weekday!==r.weekday?p.invalid("mismatched weekday","you can't specify both a weekday of "+o.weekday+" and a date of "+r.toISO()):r)},p.fromISO=function(t,e){void 0===e&&(e={});var n=N(t,[Ue,We],[Pe,Je],[Re,Ye],[He,Ge]);return Hn(n[0],n[1],e,"ISO 8601",t)},p.fromRFC2822=function(t,e){void 0===e&&(e={});var n=N(t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim(),[Fe,Le]);return Hn(n[0],n[1],e,"RFC 2822",t)},p.fromHTTP=function(t,e){void 0===e&&(e={});t=N(t,[je,qe],[Ae,qe],[ze,_e]);return Hn(t[0],t[1],e,"HTTP",e)},p.fromFormat=function(t,e,n){if(void 0===n&&(n={}),b(t)||b(e))throw new u("fromFormat requires an input string and a format");var r=n,i=r.locale,r=r.numberingSystem,i=g.fromOpts({locale:void 0===i?null:i,numberingSystem:void 0===r?null:r,defaultToEN:!0}),i=[(r=Sn(r=i,t,e)).result,r.zone,r.specificOffset,r.invalidReason],r=i[0],o=i[1],a=i[2],i=i[3];return i?p.invalid(i):Hn(r,o,n,"format "+e,t,a)},p.fromString=function(t,e,n){return p.fromFormat(t,e,n=void 0===n?{}:n)},p.fromSQL=function(t,e){void 0===e&&(e={});var n=N(t,[Be,We],[Qe,Ke]);return Hn(n[0],n[1],e,"SQL",t)},p.invalid=function(t,e){if(void 0===e&&(e=null),!t)throw new u("need to specify a reason the DateTime is invalid");t=t instanceof O?t:new O(t,e);if(k.throwOnInvalid)throw new H(t);return new p({invalid:t})},p.isDateTime=function(t){return t&&t.isLuxonDateTime||!1},p.parseFormatForOpts=function(t,e){t=On(t,g.fromObject(e=void 0===e?{}:e));return t?t.map(function(t){return t?t.val:null}).join(""):null},p.expandFormat=function(t,e){return void 0===e&&(e={}),Tn(S.parseFormat(t),g.fromObject(e)).map(function(t){return t.val}).join("")};var t=p.prototype;return t.get=function(t){return this[t]},t.resolvedLocaleOptions=function(t){t=S.create(this.loc.clone(t=void 0===t?{}:t),t).resolvedOptions(this);return{locale:t.locale,numberingSystem:t.numberingSystem,outputCalendar:t.calendar}},t.toUTC=function(t,e){return void 0===e&&(e={}),this.setZone(d.instance(t=void 0===t?0:t),e)},t.toLocal=function(){return this.setZone(k.defaultZone)},t.setZone=function(t,e){var n,e=void 0===e?{}:e,r=e.keepLocalTime,r=void 0!==r&&r,e=e.keepCalendarTime,e=void 0!==e&&e;return(t=w(t,k.defaultZone)).equals(this.zone)?this:t.isValid?(n=this.ts,(r||e)&&(r=t.offset(this.ts),n=Pn(this.toObject(),r,t)[0]),qn(this,{ts:n,zone:t})):p.invalid(An(t))},t.reconfigure=function(t){var t=void 0===t?{}:t,e=t.locale,n=t.numberingSystem,t=t.outputCalendar,e=this.loc.clone({locale:e,numberingSystem:n,outputCalendar:t});return qn(this,{loc:e})},t.setLocale=function(t){return this.reconfigure({locale:t})},t.set=function(t){if(!this.isValid)return this;var e,t=re(t,tr),n=!b(t.weekYear)||!b(t.weekNumber)||!b(t.weekday),r=!b(t.ordinal),i=!b(t.year),o=!b(t.month)||!b(t.day),a=t.weekYear||t.weekNumber;if((i||o||r)&&a)throw new Y("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(o&&r)throw new Y("Can't mix ordinal dates with month/day");n?e=xn(s({},In(this.c),t)):b(t.ordinal)?(e=s({},this.toObject(),t),b(t.day)&&(e.day=Math.min(Bt(e.year,e.month),e.day))):e=Zn(s({},Cn(this.c),t));i=Pn(e,this.o,this.zone);return qn(this,{ts:i[0],o:i[1]})},t.plus=function(t){return this.isValid?qn(this,Rn(this,I.fromDurationLike(t))):this},t.minus=function(t){return this.isValid?qn(this,Rn(this,I.fromDurationLike(t).negate())):this},t.startOf=function(t){if(!this.isValid)return this;var e={},t=I.normalizeUnit(t);switch(t){case"years":e.month=1;case"quarters":case"months":e.day=1;case"weeks":case"days":e.hour=0;case"hours":e.minute=0;case"minutes":e.second=0;case"seconds":e.millisecond=0}return"weeks"===t&&(e.weekday=1),"quarters"===t&&(t=Math.ceil(this.month/3),e.month=3*(t-1)+1),this.set(e)},t.endOf=function(t){var e;return this.isValid?this.plus(((e={})[t]=1,e)).startOf(t).minus(1):this},t.toFormat=function(t,e){return void 0===e&&(e={}),this.isValid?S.create(this.loc.redefaultToEN(e)).formatDateTimeFromString(this,t):jn},t.toLocaleString=function(t,e){return void 0===t&&(t=$),void 0===e&&(e={}),this.isValid?S.create(this.loc.clone(e),t).formatDateTime(this):jn},t.toLocaleParts=function(t){return void 0===t&&(t={}),this.isValid?S.create(this.loc.clone(t),t).formatDateTimeParts(this):[]},t.toISO=function(t){var e,t=void 0===t?{}:t,n=t.format,r=t.suppressSeconds,r=void 0!==r&&r,i=t.suppressMilliseconds,i=void 0!==i&&i,o=t.includeOffset,o=void 0===o||o,t=t.extendedZone,t=void 0!==t&&t;return this.isValid?(e=Jn(this,n="extended"===(void 0===n?"extended":n)),(e+="T")+Yn(this,n,r,i,o,t)):null},t.toISODate=function(t){t=(void 0===t?{}:t).format;return this.isValid?Jn(this,"extended"===(void 0===t?"extended":t)):null},t.toISOWeekDate=function(){return Wn(this,"kkkk-'W'WW-c")},t.toISOTime=function(t){var t=void 0===t?{}:t,e=t.suppressMilliseconds,n=t.suppressSeconds,r=t.includeOffset,i=t.includePrefix,o=t.extendedZone,t=t.format;return this.isValid?(void 0!==i&&i?"T":"")+Yn(this,"extended"===(void 0===t?"extended":t),void 0!==n&&n,void 0!==e&&e,void 0===r||r,void 0!==o&&o):null},t.toRFC2822=function(){return Wn(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)},t.toHTTP=function(){return Wn(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")},t.toSQLDate=function(){return this.isValid?Jn(this,!0):null},t.toSQLTime=function(t){var t=void 0===t?{}:t,e=t.includeOffset,e=void 0===e||e,n=t.includeZone,n=void 0!==n&&n,t=t.includeOffsetSpace,r="HH:mm:ss.SSS";return(n||e)&&((void 0===t||t)&&(r+=" "),n?r+="z":e&&(r+="ZZ")),Wn(this,r,!0)},t.toSQL=function(t){return void 0===t&&(t={}),this.isValid?this.toSQLDate()+" "+this.toSQLTime(t):null},t.toString=function(){return this.isValid?this.toISO():jn},t.valueOf=function(){return this.toMillis()},t.toMillis=function(){return this.isValid?this.ts:NaN},t.toSeconds=function(){return this.isValid?this.ts/1e3:NaN},t.toUnixInteger=function(){return this.isValid?Math.floor(this.ts/1e3):NaN},t.toJSON=function(){return this.toISO()},t.toBSON=function(){return this.toJSDate()},t.toObject=function(t){var e;return void 0===t&&(t={}),this.isValid?(e=s({},this.c),t.includeConfig&&(e.outputCalendar=this.outputCalendar,e.numberingSystem=this.loc.numberingSystem,e.locale=this.loc.locale),e):{}},t.toJSDate=function(){return new Date(this.isValid?this.ts:NaN)},t.diff=function(t,e,n){var r;return void 0===e&&(e="milliseconds"),void 0===n&&(n={}),this.isValid&&t.isValid?(n=s({locale:this.locale,numberingSystem:this.numberingSystem},n),e=e,e=(Array.isArray(e)?e:[e]).map(I.normalizeUnit),t=sn((r=t.valueOf()>this.valueOf())?this:t,r?t:this,e,n),r?t.negate():t):I.invalid("created by diffing an invalid DateTime")},t.diffNow=function(t,e){return void 0===t&&(t="milliseconds"),void 0===e&&(e={}),this.diff(p.now(),t,e)},t.until=function(t){return this.isValid?on.fromDateTimes(this,t):this},t.hasSame=function(t,e){var n;return!!this.isValid&&(n=t.valueOf(),(t=this.setZone(t.zone,{keepLocalTime:!0})).startOf(e)<=n)&&n<=t.endOf(e)},t.equals=function(t){return this.isValid&&t.isValid&&this.valueOf()===t.valueOf()&&this.zone.equals(t.zone)&&this.loc.equals(t.loc)},t.toRelative=function(t){var e,n,r,i;return this.isValid?(e=(t=void 0===t?{}:t).base||p.fromObject({},{zone:this.zone}),n=t.padding?this<e?-t.padding:t.padding:0,r=["years","months","days","hours","minutes","seconds"],i=t.unit,Array.isArray(t.unit)&&(r=t.unit,i=void 0),nr(e,this.plus(n),s({},t,{numeric:"always",units:r,unit:i}))):null},t.toRelativeCalendar=function(t){return void 0===t&&(t={}),this.isValid?nr(t.base||p.fromObject({},{zone:this.zone}),this,s({},t,{numeric:"auto",units:["years","months","days"],calendary:!0})):null},p.min=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.every(p.isDateTime))return Wt(e,function(t){return t.valueOf()},Math.min);throw new u("min requires all arguments be DateTimes")},p.max=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.every(p.isDateTime))return Wt(e,function(t){return t.valueOf()},Math.max);throw new u("max requires all arguments be DateTimes")},p.fromFormatExplain=function(t,e,n){var n=n=void 0===n?{}:n,r=n.locale,n=n.numberingSystem;return Sn(g.fromOpts({locale:void 0===r?null:r,numberingSystem:void 0===n?null:n,defaultToEN:!0}),t,e)},p.fromStringExplain=function(t,e,n){return p.fromFormatExplain(t,e,n=void 0===n?{}:n)},o(p,[{key:"isValid",get:function(){return null===this.invalid}},{key:"invalidReason",get:function(){return this.invalid?this.invalid.reason:null}},{key:"invalidExplanation",get:function(){return this.invalid?this.invalid.explanation:null}},{key:"locale",get:function(){return this.isValid?this.loc.locale:null}},{key:"numberingSystem",get:function(){return this.isValid?this.loc.numberingSystem:null}},{key:"outputCalendar",get:function(){return this.isValid?this.loc.outputCalendar:null}},{key:"zone",get:function(){return this._zone}},{key:"zoneName",get:function(){return this.isValid?this.zone.name:null}},{key:"year",get:function(){return this.isValid?this.c.year:NaN}},{key:"quarter",get:function(){return this.isValid?Math.ceil(this.c.month/3):NaN}},{key:"month",get:function(){return this.isValid?this.c.month:NaN}},{key:"day",get:function(){return this.isValid?this.c.day:NaN}},{key:"hour",get:function(){return this.isValid?this.c.hour:NaN}},{key:"minute",get:function(){return this.isValid?this.c.minute:NaN}},{key:"second",get:function(){return this.isValid?this.c.second:NaN}},{key:"millisecond",get:function(){return this.isValid?this.c.millisecond:NaN}},{key:"weekYear",get:function(){return this.isValid?zn(this).weekYear:NaN}},{key:"weekNumber",get:function(){return this.isValid?zn(this).weekNumber:NaN}},{key:"weekday",get:function(){return this.isValid?zn(this).weekday:NaN}},{key:"ordinal",get:function(){return this.isValid?Cn(this.c).ordinal:NaN}},{key:"monthShort",get:function(){return this.isValid?an.months("short",{locObj:this.loc})[this.month-1]:null}},{key:"monthLong",get:function(){return this.isValid?an.months("long",{locObj:this.loc})[this.month-1]:null}},{key:"weekdayShort",get:function(){return this.isValid?an.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}},{key:"weekdayLong",get:function(){return this.isValid?an.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}},{key:"offset",get:function(){return this.isValid?+this.o:NaN}},{key:"offsetNameShort",get:function(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}},{key:"offsetNameLong",get:function(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}},{key:"isOffsetFixed",get:function(){return this.isValid?this.zone.isUniversal:null}},{key:"isInDST",get:function(){return!this.isOffsetFixed&&(this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset)}},{key:"isInLeapYear",get:function(){return Gt(this.year)}},{key:"daysInMonth",get:function(){return Bt(this.year,this.month)}},{key:"daysInYear",get:function(){return this.isValid?$t(this.year):NaN}},{key:"weeksInWeekYear",get:function(){return this.isValid?Kt(this.weekYear):NaN}}],[{key:"DATE_SHORT",get:function(){return $}},{key:"DATE_MED",get:function(){return B}},{key:"DATE_MED_WITH_WEEKDAY",get:function(){return Q}},{key:"DATE_FULL",get:function(){return K}},{key:"DATE_HUGE",get:function(){return X}},{key:"TIME_SIMPLE",get:function(){return tt}},{key:"TIME_WITH_SECONDS",get:function(){return et}},{key:"TIME_WITH_SHORT_OFFSET",get:function(){return nt}},{key:"TIME_WITH_LONG_OFFSET",get:function(){return rt}},{key:"TIME_24_SIMPLE",get:function(){return it}},{key:"TIME_24_WITH_SECONDS",get:function(){return ot}},{key:"TIME_24_WITH_SHORT_OFFSET",get:function(){return at}},{key:"TIME_24_WITH_LONG_OFFSET",get:function(){return ut}},{key:"DATETIME_SHORT",get:function(){return st}},{key:"DATETIME_SHORT_WITH_SECONDS",get:function(){return ct}},{key:"DATETIME_MED",get:function(){return lt}},{key:"DATETIME_MED_WITH_SECONDS",get:function(){return ft}},{key:"DATETIME_MED_WITH_WEEKDAY",get:function(){return dt}},{key:"DATETIME_FULL",get:function(){return ht}},{key:"DATETIME_FULL_WITH_SECONDS",get:function(){return mt}},{key:"DATETIME_HUGE",get:function(){return yt}},{key:"DATETIME_HUGE_WITH_SECONDS",get:function(){return vt}}]),p}();function ir(t){if(L.isDateTime(t))return t;if(t&&t.valueOf&&v(t.valueOf()))return L.fromJSDate(t);if(t&&"object"==typeof t)return L.fromObject(t);throw new u("Unknown datetime argument: "+t+", of type "+typeof t)}return t.DateTime=L,t.Duration=I,t.FixedOffsetZone=d,t.IANAZone=f,t.Info=an,t.Interval=on,t.InvalidZone=Lt,t.Settings=k,t.SystemZone=gt,t.VERSION="3.3.0",t.Zone=c,Object.defineProperty(t,"__esModule",{value:!0}),t}({});;
/**
 * JQuery Wrapper
 */
(function($) {
    /**
    * Timezone Conversion
    */

    function setCookie(cname, cvalue, exdays) {
        const d = new Date();
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
        let expires = "expires=" + d.toUTCString();
        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    }

    function deleteCookie(cname) {
        const d = new Date();
        d.setTime(d.getTime() - 3600);
        let expires = "expires=" + d.toUTCString();
        document.cookie = cname + "=;" + expires + ";path=/";
    }

    function resetCookie( selector_value ) {
        deleteCookie('timezoneset_cookie');
        setCookie('timezoneset_cookie', selector_value, 30);
    }

    $(document).ready(function(){

        const DateTime = luxon.DateTime;
        let timezoneString = "America/New_York";
        
        // find all event dates on page
        const eventdates = $('.localized-date');
        
        // timezone selector and reset button
        const timezoneSelect = $('.timezone-select');
        const reset = $('#reset-timezone');
        
        // change the timezone when changing select
        // make it persist
        // show the reset button
        if( timezoneSelect.length ) {
            timezoneSelect.on('change', function() {
                timezoneString = $(this).val();
                updateTimezone( timezoneString );
                resetCookie( timezoneString );
                reset.addClass('show');
            });
        }
        
        // reset back to default
        // clear cookie
        // hide reset buttion
        if( reset.length ) {
            reset.on('click', function(){
                timezoneSelect.val('America/New_York');
                timezoneString = timezoneSelect.val();
                updateTimezone( timezoneString );
                deleteCookie('timezoneset_cookie');
                $(this).removeClass('show'); 
            });
        }

        // search for all relevant times on the page
        function updateTimezone( timezoneString ) {
            if(eventdates.length) {
                eventdates.each(function() {
                    let starttimestamp = Number($(this).attr('data-event-timestamp-start'));
                    let endtimestamp = Number($(this).attr('data-event-timestamp-end'));
                    let currentDateTimeStart = DateTime.fromSeconds( starttimestamp, { zone: timezoneString });
                    let currentDateTimeEnd = DateTime.fromSeconds( endtimestamp, { zone: timezoneString });
                    let startDate = currentDateTimeStart.hasSame(DateTime.now(), "day") ? 'Today' : currentDateTimeStart.toFormat('MMMM d, y');
                    let startTime = currentDateTimeStart.toFormat('t');
                    let endDate = currentDateTimeEnd.hasSame(DateTime.now(), "day") ? 'Today' : currentDateTimeEnd.toFormat('MMMM d, y');
                    let endTime = currentDateTimeEnd.toFormat('t');

                    if(startDate === endDate) {
                        endDate = '';
                    }
        
                    $(this).find('.start-date').text(startDate + ' ');
                    $(this).find('.start-time').text(startTime);
                    $(this).find('.end-date').text(endDate + ' ');
                    $(this).find('.end-time').text(endTime);
                }); 
            }           
        }

        
    });

})( jQuery );;
/**
* Social Share
* Vanilla JS
*/

document.addEventListener( 'DOMContentLoaded', function() {
    var btn = document.getElementById( 'social-share-button' );
    var popover = document.getElementById( 'social-share-popover' );
    var calbtn = document.getElementById( 'calendar-share-button' );
    var calpopover = document.getElementById( 'calendar-share-popover' );
    var copy = document.getElementById('copy-url');
    var copiedUrl = document.getElementById('copy-to-clipboard');
    var label = document.getElementById('alert-label');
    var html = document.body.parentNode;

    if( btn && popover ) {
        const outsideClickListener = event => {
            if (!popover.contains(event.target) && !btn.contains(event.target)) { 
                popover.classList.remove('open');
                if( calpopover ) {
                    calpopover.classList.remove('open');
                }
                removeClickListener();
            }
        }
        const removeClickListener = () => {
            document.removeEventListener('click', outsideClickListener);
        }
        btn.addEventListener('click', function(){
            popover.classList.toggle('open');
            document.addEventListener('click', outsideClickListener);
            if( calpopover ) {
                calpopover.classList.remove('open');
            }
        });

        

        
        if( copy && label ) {
            // Clipboard only works when content is served over SSL
            if (window.isSecureContext && navigator.clipboard) {
                copy.addEventListener( 'click', function(){
                    navigator.clipboard.writeText(copiedUrl.innerText).then(() => {
                        /* Text copied to clipboard */
                        label.innerText = "Copied successfully!";
                        label.classList.remove('error');
                    },() => {
                        /* Rejected - text failed to copy to the clipboard */
                        label.innerText = "Failed to copy URL";
                        label.classList.add('error');
                    });
                })
            } else {
                popover.classList.add('no-ssl');
            }
        }    
    }
    
    if( calbtn && calpopover ) {
        const outsideClickListener = event => {
            if (!calpopover.contains(event.target) && !calbtn.contains(event.target)) { 
                calpopover.classList.remove('open');
                if( popover ) {
                    popover.classList.remove('open');
                }
                removeClickListener();
            }
        }
        const removeClickListener = () => {
            document.removeEventListener('click', outsideClickListener);
        }
        calbtn.addEventListener( 'click', function(){
            calpopover.classList.toggle('open');
            document.addEventListener('click', outsideClickListener);
            if( popover ) {
                popover.classList.remove('open');
            }
        });
    }    
});;
(function( $ ){

	// Initialize the show more behavior for the status update buttons.
	const initStatusUpdateShowMore = () => {
	  const showMoreButtons = $('.status-update-show-more').not('.show-more-init');

	  $(showMoreButtons).each((idx, ele) => {

		// Prevent repeate init.
		$(ele).addClass('show-more-init');

		// Add show/hide click event.
		$(ele).on("click", (event) => {
		  event.preventDefault();
		  console.log(event.currentTarget);
		  const content = $(`#${$(event.currentTarget).data('target')}`);

		  $(content).each((idx, target) => {
			const isShowMore = $(event.currentTarget).hasClass('status-update-show-more');

			if ( isShowMore === true ) {
			  $(target).addClass('show-full-status-update');
			  $(event.currentTarget).removeClass('status-update-show-more');
			  $(event.currentTarget).addClass('status-update-show-less');
			  $(event.currentTarget).text("Show Less");
			} else {
			  $(target).removeClass('show-full-status-update');
			  $(event.currentTarget).removeClass('status-update-show-less');
			  $(event.currentTarget).addClass('status-update-show-more');
			  $(event.currentTarget).text("Show More");
			}
		  });
		});

	  });
	};


	// Observe changes in the .status-update-posts-list DOM
	const singleStatusUpdateBody = document.querySelector('.status-update-posts-list');

	if (singleStatusUpdateBody) {
	  const observer = new MutationObserver((mutationsList, observer) => {
		for (const mutation of mutationsList) {
		  if (mutation.type === 'childList') {
			initStatusUpdateShowMore();
		  }
		}
	  });

	  // Start observing the target node for configured mutations
	  observer.observe(singleStatusUpdateBody, { childList: true, subtree: true });
	}

	$( document ).ready(() => {
	  initStatusUpdateShowMore();
	});

  })(jQuery);
;
(function( $ ) {

    let postTimestamp = blogContentEndpointData.post_date;
    let blogSlug = blogContentEndpointData.blog_name;
	let postNotIn = blogContentEndpointData.post_id;
    let previousFetchedPosts = [];
    let storedPostsToDisplay = [];
    let offset = 0;
    let numposts = 2;
    let intervalStarted = false; // Flag to ensure interval starts only once

    // Function to fetch data from REST API endpoint
    function fetchNewBlogPostsFromAPI( blogSlug, timestamp, offset, numposts ) {
        // Endpoint URL
        let endpoint = `${blogContentEndpointData.new_rest_url}?blog=${blogSlug}&since=${timestamp}&type=status&offset=${offset}&numposts=${numposts}&post_not_in=${postNotIn}`;
        $.ajax({
            url: endpoint,
            method: 'GET',
            dataType: 'json',
            success: function(data) {
                // Check if there are new results
                if (data && data.length > 0) {
                    // Filter out posts that have already been fetched
                    let newPosts = data.filter(post => !previousFetchedPosts.includes(post));

                    // Update previousFetchedPostIDs with new post IDs
                    previousFetchedPosts = previousFetchedPosts.concat(newPosts);

                    // Prepend new posts if there are any
                    if (newPosts.length > 0) {
                        $('.hds-blog-load-newer-content-wrap').remove();
                        newerUpdatesButton(newPosts);
                    }
                } else {
                    if ( !intervalStarted ) { // Check if the interval has already been started
                        // If we get no data back, start the interval to check for new content every 2 minutes.
                        startFetchInterval();
                        intervalStarted = true; // Set the flag to true after starting the interval
                    }
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Error fetching data:', errorThrown);
            }
        });
    }

    // Function to show a "Newer Updates" button on the page
    function newerUpdatesButton( newPosts ) {
        // Set the new posts to our stored array, so we can hold on to them until the user clicks the button.
        storedPostsToDisplay = storedPostsToDisplay.concat(newPosts);

        // Create a div element to wrap the button
        let buttonContainer = $('<div>', {
            class: 'hds-blog-load-newer-content-wrap desktop:grid-col-10'
        });

        // Create a button element using jQuery
        let button = $('<button>', {
            text: `Load Newer Updates`,
            class: 'hds-button bg-spacesuit-white border-0 margin-auto',
            tabindex: '0'
        });

        // Append the button to the div container
        buttonContainer.append(button); // phpcs:ignore -- we're creating the element just above.

        setTimeout(() => {
            // Append the button container and make sure the loader is removed cause why not.
            $('.single-blog-loader').remove();
            $('.status-update-posts-list').prepend(buttonContainer); // phpcs:ignore -- we're creating the elements.
        }, 1000);

        // Add click event listener to handle button action
        button.on('click', function() {
            $(this).parent().remove();

            $('.status-update-posts-list').prepend('<div class="single-blog-loader desktop:grid-col-10"><img src="/wp-content/client-mu-plugins/uswds-framework/uswds/img/loader.svg" /></div>');

            setTimeout(() => {
                $('.single-blog-loader').remove();
                $('.status-update-posts-list').prepend(newPosts.reverse());
                offset += storedPostsToDisplay.length;
                storedPostsToDisplay = [];
                fetchNewBlogPostsFromAPI( blogSlug, postTimestamp, offset, numposts );
            }, 1000);
        });
    }

    function startFetchInterval() {
        setInterval(function() {
            fetchNewBlogPostsFromAPI( blogSlug, postTimestamp, offset, numposts );
        }, 12000); // Change to 12000 for easy testing.
    }

    // Initialize the new and old fetches on page load.
    $( document ).ready(() => {
        fetchNewBlogPostsFromAPI( blogSlug, postTimestamp, offset, numposts );
    });

})(jQuery);
;
(function( $ ){

	let timestamp = blogContentEndpointData.post_date;
	let blogSlug = blogContentEndpointData.blog_name;
	let offset = 2;
	let numposts = 2;

  // Initialize the blog alert indicator.
  // When a new blog is posted in the system, this code will change the page content to reflect the chagne.
  // Function to fetch data from REST API endpoint
  function fetchOlderBlogPostsFromAPI( blogSlug, offset ) {
    // Endpoint URL
    let endpoint = `${blogContentEndpointData.old_rest_url}?blog=${blogSlug}&since=${timestamp}&type=status&offset=${offset}&numposts=${numposts}`;
	$.ajax({
		url: endpoint,
		method: 'GET',
		dataType: 'json',
		success: function(data) {
			// Check if there are new results
			if (data && data.length > 0) {
				olderUpdatesButton( data );
			}
		},
		error: function(jqXHR, textStatus, errorThrown) {
			console.error('Error fetching data:', errorThrown);
		}
	});
  }

  // Function to show a "Newer Updates" button on the page
  function olderUpdatesButton( newPosts ) {

	// Create a div element to wrap the button
	let buttonContainer = $('<div>', {
		class: 'hds-blog-load-older-content-wrap desktop:grid-col-10',
	});

	// Create a button element using jQuery
	let button = $('<button>', {
		text: `Load Older Updates`,
		class: 'hds-button bg-spacesuit-white border-0 margin-auto',
		tabindex: '0',
	});

	// Append the button to the div container
	buttonContainer.append(button); // phpcs:ignore -- we're creating the element just above.

	// Append the button to a container or document body
	setTimeout(() => {
		// Append the button container and make sure the loader is removed cause why not.
		$('.single-blog-loader').remove();
		$('.status-update-posts-list').append(buttonContainer);
	  }, 1000);

	// Add click event listener to handle button action
	button.on('click', function() {

		$(this).parent().remove();

		$('.status-update-posts-list').append('<div class="single-blog-loader loading-older desktop:grid-col-10"><img src="/wp-content/client-mu-plugins/uswds-framework/uswds/img/loader.svg" /></div>');

		setTimeout(() => {
			$('.single-blog-loader').remove()
			$('.status-update-posts-list').append(newPosts); // phpcs:ignore -- we're creating the elements.
		}, 1000);

		offset += 2;
		fetchOlderBlogPostsFromAPI( blogSlug, offset );
	});
  }

  fetchOlderBlogPostsFromAPI( blogSlug, offset );

})(jQuery);
;
!function(){var t={192:function(t,e,n){var r=n(541)();t.exports=r;try{regeneratorRuntime=r}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}},541:function(t,e,n){var r=n(998).default;function o(){"use strict";t.exports=o=function(){return n},t.exports.__esModule=!0,t.exports.default=t.exports;var e,n={},i=Object.prototype,a=i.hasOwnProperty,c=Object.defineProperty||function(t,e,n){t[e]=n.value},u="function"==typeof Symbol?Symbol:{},l=u.iterator||"@@iterator",s=u.asyncIterator||"@@asyncIterator",f=u.toStringTag||"@@toStringTag";function d(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(e){d=function(t,e,n){return t[e]=n}}function p(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),a=new C(r||[]);return c(i,"_invoke",{value:T(t,n,a)}),i}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}n.wrap=p;var v="suspendedStart",g="suspendedYield",m="executing",y="completed",w={};function b(){}function x(){}function O(){}var S={};d(S,l,(function(){return this}));var A=Object.getPrototypeOf,j=A&&A(A(P([])));j&&j!==i&&a.call(j,l)&&(S=j);var E=O.prototype=b.prototype=Object.create(S);function k(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){function n(o,i,c,u){var l=h(t[o],t,i);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==r(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,c,u)}),(function(t){n("throw",t,c,u)})):e.resolve(f).then((function(t){s.value=t,c(s)}),(function(t){return n("throw",t,c,u)}))}u(l.arg)}var o;c(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}})}function T(t,n,r){var o=v;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=z(c,r);if(u){if(u===w)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===v)throw o=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=m;var l=h(t,n,r);if("normal"===l.type){if(o=r.done?y:g,l.arg===w)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(o=y,r.method="throw",r.arg=l.arg)}}}function z(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,z(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),w;var i=h(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,w;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,w):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,w)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function I(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[l];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(a.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(r(t)+" is not iterable")}return x.prototype=O,c(E,"constructor",{value:O,configurable:!0}),c(O,"constructor",{value:x,configurable:!0}),x.displayName=d(O,f,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===x||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,O):(t.__proto__=O,d(t,f,"GeneratorFunction")),t.prototype=Object.create(E),t},n.awrap=function(t){return{__await:t}},k(_.prototype),d(_.prototype,s,(function(){return this})),n.AsyncIterator=_,n.async=function(t,e,r,o,i){void 0===i&&(i=Promise);var a=new _(p(t,e,r,o),i);return n.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(E),d(E,f,"Generator"),d(E,l,(function(){return this})),d(E,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},n.values=P,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(I),!t)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(r,o){return c.type="throw",c.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var u=a.call(i,"catchLoc"),l=a.call(i,"finallyLoc");if(u&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,w):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),w},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),I(n),w}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;I(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),w}},n}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports},998:function(t){function e(n){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(n)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},function(){"use strict";var t={};n.r(t),n.d(t,{run:function(){return Vt},runGroup:function(){return Wt}});var e={};n.r(e),n.d(e,{getScroller:function(){return ae},lock:function(){return ce},unlock:function(){return ue}});var r={};n.r(r),n.d(r,{reInitChildren:function(){return Ie}});var o={};n.r(o),n.d(o,{down:function(){return Ne},up:function(){return $e}});var i={};n.r(i),n.d(i,{elVisibleHeight:function(){return Je},elements:function(){return Ue},height:function(){return We},width:function(){return Ve}});var a={};n.r(a),n.d(a,{clear:function(){return mn},get:function(){return vn},put:function(){return hn},remove:function(){return gn}});var c={};n.r(c),n.d(c,{clear:function(){return xn},get:function(){return wn},put:function(){return yn},remove:function(){return bn}});var u={};n.r(u),n.d(u,{get:function(){return On},remove:function(){return An},set:function(){return Sn}});var l={};function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function f(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,c=[],u=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(c.push(r.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return s(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[],n=t.length;n--;e.unshift(t[n]));return e}function p(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)}function h(){return d((arguments.length>0&&void 0!==arguments[0]?arguments[0]:document).querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')).filter((function(t){return p(t)}))}function v(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:function(){};if(n&&e){if(27===t.keyCode)return e.focus(),void r();if(9===t.keyCode){var o=h(n),i=o[0],a=o[o.length-1];t.shiftKey?document.activeElement===i&&(a.focus(),t.preventDefault()):document.activeElement===a&&(i.focus(),t.preventDefault())}}}function g(t,e){Object.keys(e).forEach((function(n){return t.setAttribute(n,e[n])}))}n.r(l),n.d(l,{addAsyncFilter:function(){return on},addFilter:function(){return an},animate:function(){return t},applyBrowserClasses:function(){return oe},arrayDiff:function(){return L},arrayEquals:function(){return I},arrayToInt:function(){return C},aspectRatioToPadding:function(){return P},bodyLock:function(){return e},browsers:function(){return re},checkNotificationPromise:function(){return pn},clipboard:function(){return le},cloneDeep:function(){return N},consoleError:function(){return j},consoleInfo:function(){return E},consoleLog:function(){return k},consoleWarn:function(){return _},convertElements:function(){return d},cookieStorage:function(){return u},debounce:function(){return Be},deepMerge:function(){return Y},delay:function(){return Q},delegate:function(){return Ke},dragHorizontal:function(){return fe},escapeHtml:function(){return tt},escapeScripts:function(){return et},filter:function(){return rn},filterObject:function(){return X},findNestedObject:function(){return K},fnvHash:function(){return it},focusLoop:function(){return v},getAttachmentImageUrl:function(){return nt},getChildren:function(){return de},getClosest:function(){return pe},getConfig:function(){return rt},getCoords:function(){return he},getFocusable:function(){return h},getHiddenHeight:function(){return ve},getNode:function(){return me},getNodes:function(){return ge},getValidLocale:function(){return ot},hasClassFromArray:function(){return ye},hasScrollbar:function(){return we},insertAfter:function(){return be},insertBefore:function(){return xe},isEmptyObject:function(){return at},isEqual:function(){return ut},isExternalLink:function(){return Oe},isFileLink:function(){return Se},isFormDirty:function(){return Ae},isFunction:function(){return H},isImageLink:function(){return je},isJestTest:function(){return A},isJson:function(){return lt},isNumber:function(){return st},isObject:function(){return z},isRtl:function(){return Ee},localStorage:function(){return a},matchesOrContainedInSelectors:function(){return ke},mimicFn:function(){return At},normalizeUrl:function(){return jt},objectAssign:function(){return Et},objectToAttributes:function(){return Ft},objectToFormData:function(){return kt},openNewTab:function(){return _e},parseSocial:function(){return It},parseUrl:function(){return Ct},popup:function(){return Te},queryToJson:function(){return Ht},ready:function(){return un},removeClassThatContains:function(){return ze},removeFilter:function(){return cn},resize:function(){return ln},runOnce:function(){return dn},saferHtml:function(){return Pt},sanitizeLocale:function(){return Mt},sessionStorage:function(){return c},setAttributes:function(){return g},shouldLoadChunk:function(){return Le},simpleBar:function(){return r},slide:function(){return o},slugify:function(){return Rt},spacerClasses:function(){return qe},speak:function(){return S},sprintf:function(){return $t},trigger:function(){return se},uniqueId:function(){return qt},updateQueryVar:function(){return Ut},viewport:function(){return i},visible:function(){return p},vsprintf:function(){return Dt}});var m={containers:[]},y={previousMessage:""},w=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"polite",e=document.createElement("div");g(e,{"aria-live":t,"aria-relevant":"additions text","aria-atomic":"true",style:"position: absolute; margin: -1px; padding: 0; height: 1px; width: 1px; overflow: hidden; clip: rect(1px, 1px, 1px, 1px); -webkit-clip-path: inset(50%); clip-path: inset(50%); border: 0; word-wrap: normal !important;"}),document.body.appendChild(e),m.containers.push(e)},b=function(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/<[^<>]+>/g," ");return y.previousMessage===t&&(t+=" "),y.previousMessage=t,t},x=function(){return m.containers.forEach((function(t){return t.textContent=""}))},O=function(){m.containers.length||(w("assertive"),w("polite"))};function S(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"polite";O(),x();var n=m.containers.filter((function(t){return t.getAttribute("aria-live")===e}))[0];n&&(n.textContent=b(t))}function A(){return!!window.__TEST__}function j(){window.console&&A()}function E(){}function k(){}function _(){window.console&&A()}function T(t){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(t)}function z(t){return!(!t||"object"!==T(t)||Array.isArray(t))}var L=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=function(t){if(null!==n){if("function"==typeof n)return n(t);if(z(t))return t[n]}return t},o=new Set(t.map(r)),i=new Set(e.map(r));return{added:e.filter((function(t){return!o.has(r(t))})),removed:t.filter((function(t){return!i.has(r(t))}))}};function I(t,e){return Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every((function(t,n){return t===e[n]}))}var C=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).map((function(t){return parseInt(t,10)}))};function P(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").split(":");return parseFloat((t[1]/t[0]*100).toFixed(5))}function M(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return R(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?R(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,o=function(){};return{s:o,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){c=!0,i=t},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw i}}}}function R(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var F=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new WeakMap;if(null===t||"object"!==T(t))return t;if(e.has(t))return e.get(t);if(t instanceof Date)return new Date(t);if(Array.isArray(t)){var n=[];e.set(t,n);for(var r=0;r<t.length;r++)n[r]=F(t[r],e);return n}if(t instanceof Map){var o=new Map;return e.set(t,o),t.forEach((function(t,n){o.set(n,F(t,e))})),o}if(t instanceof Set){var i=new Set;return e.set(t,i),t.forEach((function(t){i.add(F(t,e))})),i}if(t instanceof RegExp)return new RegExp(t);if(ArrayBuffer.isView(t))return new t.constructor(t.buffer.slice(0));if(t instanceof Object){var a=Object.create(Object.getPrototypeOf(t));e.set(t,a);var c,u=M(Reflect.ownKeys(t));try{for(u.s();!(c=u.n()).done;){var l=c.value;a[l]=F(t[l],e)}}catch(t){u.e(t)}finally{u.f()}return a}return t},N=function(t){return F(t)},$="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103,D=function(t){var e=Object.prototype.toString.call(t);return"[object RegExp]"===e||"[object Date]"===e||function(t){return t.$$typeof===$}(t)};function Z(t){return function(t){return!!t&&"object"===T(t)}(t)&&!D(t)}function H(t){return t&&"[object Function]"==={}.toString.call(t)}function q(t,e){return!1!==e.clone&&e.isMergeableObject(t)?G((n=t,Array.isArray(n)?[]:{}),t,e):t;var n}function U(t,e,n){return t.concat(e).map((function(t){return q(t,n)}))}function V(t,e,n){var r=t.slice();return e.forEach((function(e,o){void 0===r[o]?r[o]=n.cloneUnlessOtherwiseSpecified(e,n):n.isMergeableObject(e)?r[o]=G(t[o],e,n):-1===t.indexOf(e)&&r.push(e)})),r}function W(t){return Object.keys(t).concat(function(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter((function(e){return t.propertyIsEnumerable(e)})):[]}(t))}function J(t,e){try{return e in t}catch(t){return!1}}function B(t,e,n){var r={};return n.isMergeableObject(t)&&W(t).forEach((function(e){r[e]=q(t[e],n)})),W(e).forEach((function(o){(function(t,e){return J(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))})(t,o)||(J(t,o)&&n.isMergeableObject(e[o])?r[o]=function(t,e){if(!e.customMerge)return G;var n=e.customMerge(t);return"function"==typeof n?n:G}(o,n)(t[o],e[o],n):r[o]=q(e[o],n))})),r}function G(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};n.arrayMerge=function(t){var e=U;return"combine"===t.arrayMerge?e=V:H(t.arrayMerge)&&(e=t.arrayMerge),e}(n),n.isMergeableObject=n.isMergeableObject||Z,n.cloneUnlessOtherwiseSpecified=q;var r=Array.isArray(e);return r===Array.isArray(t)?r?n.arrayMerge(t,e,n):B(t,e,n):q(e,n)}G.all=function(t,e){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce((function(t,n){return G(t,n,e)}),{})};var Y=G,X=function(t,e){var n=Object.entries(t).filter(e);return Object.fromEntries(n)};function K(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=function(t){if("object"===T(t))for(var o in t)if(Object.prototype.hasOwnProperty.call(t,o)){if(o===e&&t[o]===n)return t;var i=r(t[o]);if(i)return i}return null};return r(t)}function Q(){var t,e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,o=[];function i(t,n){e=window.setTimeout((function(){if(e=null,t(),o.length){var n=o.shift();i(n.fn,n.t)}}),n)}return t={delay:function(n,r){return o.length||e?o.push({fn:n,t:r}):i(n,r),t},cancel:function(){return window.clearTimeout(e),o=[],t}},t.delay(n,r)}function tt(){return String(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")}function et(){return String(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"")}var nt=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"full";if(!t||"object"!==T(t))return"";var r=e||n;return t.sizes&&t.sizes[r]&&t.sizes[r].url?t.sizes[r].url:r!==n&&t.sizes&&t.sizes[n]&&t.sizes[n].url?t.sizes[n].url:t.url||""};function rt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e&&t[e]?t[e]:t}var ot=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";try{if(!t)throw new Error("Locale not provided");var n=new Intl.Locale(t),r=new Intl.DateTimeFormat(t).resolvedOptions().locale;if(new Intl.Locale(r).language!==n.language)throw new Error("Unsupported locale: ".concat(t));return r}catch(t){return e}},it=function(t){for(var e=String(t),n=14695981039346656037n,r=0;r<e.length;r++){n^=BigInt(e.charCodeAt(r)),n*=1099511628211n,n&=18446744073709551615n}return n.toString(16).padStart(16,"0")};function at(t){for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return JSON.stringify(t)===JSON.stringify({})}var ct=function(t,e){if(t===e)return!0;if(null==t||"object"!==T(t)||null==e||"object"!==T(e))return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(var o=0,i=n;o<i.length;o++){var a=i[o];if(!r.includes(a)||!ct(t[a],e[a]))return!1}return!0},ut=ct;function lt(t){if(null===t)return!1;try{JSON.parse(t)}catch(t){return!1}return!0}var st=function(t){return!isNaN(parseFloat(t))&&isFinite(t)};function ft(t){var e=function(t,e){if("object"!=T(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=T(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==T(e)?e:e+""}function dt(t,e,n){return(e=ft(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function pt(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return ht(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ht(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,o=function(){};return{s:o,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){c=!0,i=t},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw i}}}}function ht(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function vt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function gt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?vt(Object(n),!0).forEach((function(e){dt(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):vt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var mt=function(t,e,n,r){if("length"!==n&&"prototype"!==n&&"arguments"!==n&&"caller"!==n){var o=Object.getOwnPropertyDescriptor(t,n),i=Object.getOwnPropertyDescriptor(e,n);!yt(o,i)&&r||Object.defineProperty(t,n,i)}},yt=function(t,e){return void 0===t||t.configurable||t.writable===e.writable&&t.enumerable===e.enumerable&&t.configurable===e.configurable&&(t.writable||t.value===e.value)},wt=function(t,e){var n=Object.getPrototypeOf(e);n!==Object.getPrototypeOf(t)&&Object.setPrototypeOf(t,n)},bt=function(t,e){return"/* Wrapped ".concat(t,"*/\n").concat(e)},xt=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),Ot=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),St=function(t,e,n){var r=""===n?"":"with ".concat(n.trim(),"() "),o=bt.bind(null,r,e.toString());Object.defineProperty(o,"name",Ot),Object.defineProperty(t,"toString",gt(gt({},xt),{},{value:o}))};function At(t,e){var n,r=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).ignoreNonConfigurable,o=void 0!==r&&r,i=t.name,a=pt(Reflect.ownKeys(e));try{for(a.s();!(n=a.n()).done;){var c=n.value;mt(t,e,c,o)}}catch(t){a.e(t)}finally{a.f()}return wt(t,e),St(t,e,i),t}function jt(t){if(!t)return"";var e=t.trim();return""===e?"":/^https?:\/\//i.test(e)?e:e.startsWith("//")?"https:".concat(e):"https://".concat(e)}function Et(){for(var t={},e=0;e<arguments.length;e+=1)for(var n=arguments[e],r=Object.keys(n),o=0;o<r.length;o+=1)t[r[o]]=n[r[o]];return t}var kt=function(t,e,n){var r=new window.FormData;return function t(e,o){if(!function(t){return Array.isArray(n)&&n.some((function(e){return e===t}))}(o))if(o=o||"",e instanceof window.File)r.append(o,e);else if(Array.isArray(e))for(var i=0;i<e.length;i++)t(e[i],o+"["+i+"]");else if("object"===T(e)&&e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t(e[a],""===o?a:o+"["+a+"]");else null!=e&&r.append(o,e)}(t,e),r};function _t(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return Tt(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Tt(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,o=function(){};return{s:o,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){c=!0,i=t},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw i}}}}function Tt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var zt={calendly:{name:"Calendly",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?calendly\.com\/([a-zA-Z0-9_-]+(?:\/[a-zA-Z0-9_-]+)?)(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^[a-zA-Z0-9_-]+(?:\/[a-zA-Z0-9_-]+)?$/,urlTemplate:function(t){return"https://calendly.com/".concat(t)},normalizeIdentifier:function(t){return t.toLowerCase()}},youtube:{name:"YouTube",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?youtube\.com\/(@[a-zA-Z0-9_.-]+)(?:\/?(?:\?.+)?)?$/i,/^(?:https?:\/\/)?(?:www\.)?youtube\.com\/(channel\/UC[a-zA-Z0-9_-]+)(?:\/?(?:\?.+)?)?$/i,/^(?:https?:\/\/)?(?:www\.)?youtube\.com\/(c\/[a-zA-Z0-9_.-]+)(?:\/?(?:\?.+)?)?$/i,/^(?:https?:\/\/)?(?:www\.)?youtube\.com\/(user\/[a-zA-Z0-9_.-]+)(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^@?[a-zA-Z0-9_.-]+$/,urlTemplate:function(t){return"https://youtube.com/".concat(t)},normalizeIdentifier:function(t){return t.startsWith("@")||t.startsWith("channel/")||t.startsWith("c/")||t.startsWith("user/")||/^UC/.test(t)?t:"@"+t}},wordpress:{name:"WordPress",urlRegexes:[/^(?:https?:\/\/)?profiles\.wordpress\.org\/([a-zA-Z0-9_.-]+)\/?(?:\/.*)?$/i],handleValidationRegex:/^[a-zA-Z0-9_.-]+$/,urlTemplate:function(t){return"https://profiles.wordpress.org/".concat(t.toLowerCase(),"/")},normalizeIdentifier:function(t){return t.toLowerCase()}},xitter:{name:"X",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?(?:twitter|x)\.com\/([a-zA-Z0-9_]{1,15})(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^[a-zA-Z0-9_]{1,15}$/,urlTemplate:function(t){return"https://x.com/".concat(t)},normalizeIdentifier:function(t){return t.replace(/^@/,"")}},facebook:{name:"Facebook",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?(?:facebook|fb)\.com\/(?:profile\.php\?id=)?(\d+)(?:&.+|\/?)$/i,/^(?:https?:\/\/)?(?:www\.)?(?:facebook|fb)\.com\/(?!pages\/|groups\/|events\/|photo(?:s|\.php)?|permalink\.php|story\.php|watch\/?|live\/?|video(?:s|\.php)?|media\/?|messages\/|gaming\/|notes\/|sharer(?:\.php)?|login\.php|help\/|legal\/|marketplace\/|ads\/|posts\/|hashtag\/)([a-zA-Z0-9._-]+)(?:\/?(?:\?.*)?)?$/i],handleValidationRegex:/^(?:[a-zA-Z0-9._-]+|\d+)$/,urlTemplate:function(t,e){return/^\d+$/.test(t)&&e&&/profile\.php\?id=/.test(e)?"https://facebook.com/profile.php?id=".concat(t):"https://facebook.com/".concat(t)},normalizeIdentifier:function(t){return t.replace(/^@/,"")}},bluesky:{name:"Bluesky",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?bsky\.app\/profile\/([a-zA-Z0-9.-]+[a-zA-Z0-9])(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^[a-zA-Z0-9.-]+[a-zA-Z0-9]$/,urlTemplate:function(t){return"https://bsky.app/profile/".concat(t)},normalizeIdentifier:function(t){return t.replace(/^@/,"")},finalizeIdentifier:function(t,e){return e&&t&&!t.includes(".")?"".concat(t,".bsky.social"):t}},tiktok:{name:"TikTok",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?tiktok\.com\/@([a-zA-Z0-9_.]+)(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^[a-zA-Z0-9_.]+$/,urlTemplate:function(t){return"https://tiktok.com/@".concat(t)},normalizeIdentifier:function(t){return t.replace(/^@/,"")}},whatsapp:{name:"WhatsApp",urlRegexes:[/^(?:https?:\/\/)?(?:wa\.me\/|api\.whatsapp\.com\/send\/?\?phone=)(\+?\d+[\d\s()-]*\d)(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^\+?\d+[\d\s()-]*\d$/,urlTemplate:function(t){return"https://wa.me/".concat(t.replace(/\D/g,""))},normalizeIdentifier:function(t){return t.replace(/\D/g,"")}},threads:{name:"Threads",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?threads\.net\/@([a-zA-Z0-9_.]+)(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^[a-zA-Z0-9_.]+$/,urlTemplate:function(t){return"https://threads.net/@".concat(t)},normalizeIdentifier:function(t){return t.replace(/^@/,"")}},linkedin:{name:"LinkedIn",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?linkedin\.com\/in\/([a-zA-Z0-9_-]+)(?:\/?(?:\?.+)?)?$/i,/^(?:https?:\/\/)?(?:www\.)?linkedin\.com\/company\/([a-zA-Z0-9_-]+)(?:\/?(?:\?.+)?)?$/i,/^(?:https?:\/\/)?(?:www\.)?linkedin\.com\/school\/([a-zA-Z0-9_-]+)(?:\/?(?:\?.+)?)?$/i,/^(?:https?:\/\/)?(?:www\.)?linkedin\.com\/showcase\/([a-zA-Z0-9_-]+)(?:\/?(?:\?.+)?)?$/i,/^(?:https?:\/\/)?(?:www\.)?linkedin\.com\/pub\/([a-zA-Z0-9_-]+(?:-[a-zA-Z0-9_-]+)*)(?:\/[a-zA-Z0-9]+){0,3}\/?(?:\?.+)?$/i],handleValidationRegex:/^[a-zA-Z0-9_-]+$/,urlTemplate:function(t,e){var n=t.split("/")[0];if(e){if(e.includes("/company/"))return"https://linkedin.com/company/".concat(n);if(e.includes("/school/"))return"https://linkedin.com/school/".concat(n);if(e.includes("/showcase/"))return"https://linkedin.com/showcase/".concat(n);if(e.includes("/pub/"))return"https://linkedin.com/pub/".concat(n)}return"https://linkedin.com/in/".concat(n)},normalizeIdentifier:function(t){return t.replace(/^@/,"")}},savvycal:{name:"SavvyCal",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?savvycal\.com\/([a-zA-Z0-9_-]+)(?:\/[a-zA-Z0-9_-]+)?(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^[a-zA-Z0-9_-]+$/,urlTemplate:function(t){return"https://savvycal.com/".concat(t.split("/")[0])},normalizeIdentifier:function(t){return t.replace(/^@/,"")}},github:{name:"GitHub",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?github\.com\/([a-zA-Z0-9_-]+)(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^[a-zA-Z0-9_-]+$/,urlTemplate:function(t){return"https://github.com/".concat(t)},normalizeIdentifier:function(t){return t.replace(/^@/,"")}},instagram:{name:"Instagram",urlRegexes:[/^(?:https?:\/\/)?(?:www\.)?instagram\.com\/([a-zA-Z0-9_.]+)(?:\/?(?:\?.+)?)?$/i],handleValidationRegex:/^[a-zA-Z0-9_.]+$/,urlTemplate:function(t){return"https://instagram.com/".concat(t)},normalizeIdentifier:function(t){return t.replace(/^@/,"")}}},Lt=Object.freeze(["calendly","youtube","wordpress","xitter","facebook","bluesky","tiktok","whatsapp","threads","linkedin","savvycal","github","instagram"]);function It(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n={url:"",identifier:"",platform:"",valid:!1};if(!t||"string"!=typeof t)return n;var r=t.trim();if(!r)return n;var o,i=e?e.toLowerCase():"",a=_t(Lt);try{for(a.s();!(o=a.n()).done;){var c,u=o.value,l=zt[u],s=_t(l.urlRegexes);try{for(s.s();!(c=s.n()).done;){var f=c.value,d=r.match(f);if(d&&d[1]){var p=d[1];return n.identifier=l.normalizeIdentifier(p),l.finalizeIdentifier&&(n.identifier=l.finalizeIdentifier(n.identifier,!1)),n.url=l.urlTemplate(n.identifier,r),n.platform=u,n.valid=!0,n}}}catch(t){s.e(t)}finally{s.f()}}}catch(t){a.e(t)}finally{a.f()}if(i&&Lt.includes(i)){var h=zt[i],v=h.normalizeIdentifier(r);if(h.handleValidationRegex&&h.handleValidationRegex.test(v))return n.identifier=v,h.finalizeIdentifier&&(n.identifier=h.finalizeIdentifier(n.identifier,!0)),n.url=h.urlTemplate(n.identifier,null),n.platform=i,n.valid=!0,n}return n}function Ct(t,e){for(var n,r=["source","scheme","authority","userInfo","user","pass","host","port","relative","path","directory","file","query","fragment"],o={},i=o["phpjs.parse_url.mode"]&&o["phpjs.parse_url.mode"].local_value||"php",a={php:/^(?:([^:\/?#]+):)?(?:\/\/()(?:(?:()(?:([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?))?()(?:(()(?:(?:[^?#\/]*\/)*)()(?:[^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/\/?)?((?:(([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/},c=a[i].exec(t),u={},l=14;l--;)c[l]&&(u[r[l]]=c[l]);return e?u[e.replace("PHP_URL_","").toLowerCase()]:("php"!==i&&(n=o["phpjs.parse_url.queryKey"]&&o["phpjs.parse_url.queryKey"].local_value||"queryKey",a=/(?:^|&)([^&=]*)=?([^&]*)/g,u[n]={},(u[r[12]]||"").replace(a,(function(t,e,r){e&&(u[n][e]=r)}))),u.source=null,u)}function Pt(t){for(var e=t[0],n=1;n<arguments.length;n++){e+=String(arguments[n]).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),e+=t[n]}return e}var Mt=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/_/g,"-")};function Rt(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").toString().normalize("NFKD").toLowerCase().trim().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/-$/g,"")}function Ft(t){var e=[];return Object.entries(t).forEach((function(t){var n=f(t,2),r=n[0],o=n[1];if(o.length||"alt"===r)if(Array.isArray(o)){var i=o.filter((function(t){return t}));e.push("".concat(r,'="').concat(i.join(" "),'"'))}else e.push("".concat(r,'="').concat(o,'"'))})),e.join(" ")}var Nt={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function $t(t){return function(t,e){var n,r,o,i,a,c,u,l,s,f=1,d=t.length,p="";for(r=0;r<d;r++)if("string"==typeof t[r])p+=t[r];else if("object"===T(t[r])){if((i=t[r]).keys)for(n=e[f],o=0;o<i.keys.length;o++){if(null==n)throw new Error($t('[sprintf] Cannot access property "%s" of undefined value "%s"',i.keys[o],i.keys[o-1]));n=n[i.keys[o]]}else n=i.param_no?e[i.param_no]:e[f++];if(Nt.not_type.test(i.type)&&Nt.not_primitive.test(i.type)&&n instanceof Function&&(n=n()),Nt.numeric_arg.test(i.type)&&"number"!=typeof n&&isNaN(n))throw new TypeError($t("[sprintf] expecting number but found %T",n));switch(Nt.number.test(i.type)&&(l=n>=0),i.type){case"b":n=parseInt(n,10).toString(2);break;case"c":n=String.fromCharCode(parseInt(n,10));break;case"d":case"i":n=parseInt(n,10);break;case"j":n=JSON.stringify(n,null,i.width?parseInt(i.width):0);break;case"e":n=i.precision?parseFloat(n).toExponential(i.precision):parseFloat(n).toExponential();break;case"f":n=i.precision?parseFloat(n).toFixed(i.precision):parseFloat(n);break;case"g":n=i.precision?String(Number(n.toPrecision(i.precision))):parseFloat(n);break;case"o":n=(parseInt(n,10)>>>0).toString(8);break;case"s":n=String(n),n=i.precision?n.substring(0,i.precision):n;break;case"t":n=String(!!n),n=i.precision?n.substring(0,i.precision):n;break;case"T":n=Object.prototype.toString.call(n).slice(8,-1).toLowerCase(),n=i.precision?n.substring(0,i.precision):n;break;case"u":n=parseInt(n,10)>>>0;break;case"v":n=n.valueOf(),n=i.precision?n.substring(0,i.precision):n;break;case"x":n=(parseInt(n,10)>>>0).toString(16);break;case"X":n=(parseInt(n,10)>>>0).toString(16).toUpperCase()}Nt.json.test(i.type)?p+=n:(!Nt.number.test(i.type)||l&&!i.sign?s="":(s=l?"+":"-",n=n.toString().replace(Nt.sign,"")),c=i.pad_char?"0"===i.pad_char?"0":i.pad_char.charAt(1):" ",u=i.width-(s+n).length,a=i.width&&u>0?c.repeat(u):"",p+=i.align?s+n+a:"0"===c?s+a+n:a+s+n)}return p}(function(t){if(Zt[t])return Zt[t];var e,n=t,r=[],o=0;for(;n;){if(null!==(e=Nt.text.exec(n)))r.push(e[0]);else if(null!==(e=Nt.modulo.exec(n)))r.push("%");else{if(null===(e=Nt.placeholder.exec(n)))throw new SyntaxError("[sprintf] unexpected placeholder");if(e[2]){o|=1;var i=[],a=e[2],c=[];if(null===(c=Nt.key.exec(a)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(i.push(c[1]);""!==(a=a.substring(c[0].length));)if(null!==(c=Nt.key_access.exec(a)))i.push(c[1]);else{if(null===(c=Nt.index_access.exec(a)))throw new SyntaxError("[sprintf] failed to parse named argument key");i.push(c[1])}e[2]=i}else o|=2;if(3===o)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");r.push({placeholder:e[0],param_no:e[1],keys:e[2],sign:e[3],pad_char:e[4],align:e[5],width:e[6],precision:e[7],type:e[8]})}n=n.substring(e[0].length)}return Zt[t]=r}(t),arguments)}function Dt(t,e){return $t.apply(null,[t].concat(e||[]))}var Zt=Object.create(null);var Ht=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=t.length?t:window.location.search.slice(1),n=e.length?e.split("&"):[],r={},o=[];return n.forEach((function(t){o=t.split("="),r[o[0]]=decodeURIComponent(o[1]||"")})),JSON.parse(JSON.stringify(r))};function qt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"id";return"".concat(t.length?"".concat(t,"-"):"").concat(Math.random().toString(36).substr(2,9))}function Ut(t,e){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:window.location.href).split("#"),r=n[1]?"#".concat(n[1]):"",o=n[0].split("?"),i=o[0],a=o[1],c=void 0!==a?a.split("&"):[],u=!1;return c.forEach((function(n,r){n.startsWith("".concat(t,"="))&&(u=!0,e?c[r]="".concat(t,"=").concat(e):c.splice(r,1))})),!u&&e&&(c[c.length]="".concat(t,"=").concat(e)),"".concat(i).concat("?").concat(c.join("&")).concat(r)}var Vt=function(){var t,e,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r){var i=o.onAnimateInit,a=void 0===i?function(){}:i,c=o.onAnimateStart,u=void 0===c?function(){}:c,l=o.onAnimateEnd,s=void 0===l?function(){}:l,f=o.delay,d=void 0===f?(null===(t=r.dataset)||void 0===t?void 0:t.animationDelay)||0:f,p=o.duration,h=void 0===p?(null===(e=r.dataset)||void 0===e?void 0:e.animationDuration)||400:p,v=o.easing,g=void 0===v?(null===(n=r.dataset)||void 0===n?void 0:n.animationEasing)||"linear":v,m=function(t,e){var n,r,o,i,a,c={},u={},l=e.distanceFrom,s=void 0===l?(null===(n=t.dataset)||void 0===n?void 0:n.translateDistanceFrom)||"20px":l,f=e.distanceTo,d=void 0===f?(null===(r=t.dataset)||void 0===r?void 0:r.translateDistanceTo)||"0px":f,p=e.opacityFrom,h=void 0===p?null===(o=t.dataset)||void 0===o?void 0:o.translateOpacityFrom:p,v=e.opacityTo,g=void 0===v?null===(i=t.dataset)||void 0===i?void 0:i.translateOpacityTo:v,m=e.types;return(void 0===m?(null===(a=t.dataset)||void 0===a?void 0:a.animationTypes)||"":m).split(" ").forEach((function(t){"fadeIn"===t&&(c.opacity=h||0,u.opacity=g||1),"fadeOut"===t&&(c.opacity=h||1,u.opacity=g||0),"translateY"===t&&(c.transform="translateY(".concat(s,")"),u.transform="translateY(".concat(d,")"))})),[c,u]}(r,o);a(),setTimeout((function(){u(),requestAnimationFrame((function(){r.animate(m,{duration:Number(h),easing:g}).onfinish=function(){!function(t,e){var n,r,o,i=e.distanceTo,a=void 0===i?(null===(n=t.dataset)||void 0===n?void 0:n.translateDistanceTo)||"0px":i,c=e.opacityTo,u=void 0===c?null===(r=t.dataset)||void 0===r?void 0:r.translateOpacityTo:c,l=e.types;(void 0===l?(null===(o=t.dataset)||void 0===o?void 0:o.animationTypes)||"":l).split(" ").forEach((function(e){"fadeIn"===e&&(t.style.opacity=u||"1",t.setAttribute("aria-hidden","false")),"fadeOut"===e&&(t.style.opacity=u||"0",t.setAttribute("aria-hidden","true")),"translateY"===e&&(t.style.transform="translateY(".concat(a,")"))}))}(r,o),s()}}))}),d)}},Wt=function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach((function(t){var e=t.target,n=t.options;Vt(e,n)}))},Jt=/(android)/i.test(window.navigator.userAgent),Bt=!!window.chrome,Gt="undefined"!=typeof InstallTrigger,Yt=document.documentMode||!1,Xt=!Yt&&!!window.StyleMedia,Kt=!!window.navigator.userAgent.match(/(iPod|iPhone|iPad)/i),Qt=!!window.navigator.userAgent.match(/(iPod|iPhone)/i),te=!!window.opera||window.navigator.userAgent.indexOf(" OPR/")>=0,ee=Object.prototype.toString.call(window.HTMLElement).indexOf("Constructor")>0||!Bt&&!te&&"undefined"!==window.webkitAudioContext,ne=window.navigator.platform;function re(){return{android:Jt,chrome:Bt,edge:Xt,firefox:Gt,ie:Yt,ios:Kt,iosMobile:Qt,opera:te,safari:ee,os:ne}}function oe(){var t=re(),e=document.body.classList;t.android?e.add("device-android"):t.ios&&e.add("device-ios"),t.edge?e.add("browser-edge"):t.chrome?e.add("browser-chrome"):t.firefox?e.add("browser-firefox"):t.ie?e.add("browser-ie"):t.opera?e.add("browser-opera"):t.safari&&e.add("browser-safari")}var ie=0,ae=function(){var t=re();return t.ie||t.firefox||t.chrome&&!t.edge?document.documentElement:document.body},ce=function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],e=ae(),n=document.body.style;ie=e.scrollTop,n.overflowY="scroll",n.position="fixed",n.width="100%",t&&(n.marginTop="-".concat(ie,"px"))},ue=function(){var t=ae(),e=document.body.style;e.overflowY="",e.position="static",e.marginTop="0px",e.width="",t.scrollTop=ie};function le(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(window.clipboardData&&window.clipboardData.setData)return window.clipboardData.setData("Text",t);if(document.queryCommandSupported&&document.queryCommandSupported("copy")){var e=document.createElement("textarea");e.textContent=t,e.style.position="fixed",document.body.appendChild(e),e.select();try{return document.execCommand("copy")}catch(t){return _("Copy to clipboard failed.",t),!1}finally{document.body.removeChild(e)}}}function se(){var t,e=Et({data:{},el:document,event:"",native:!0},arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});if(e.native)(t=document.createEvent("HTMLEvents")).initEvent(e.event,!0,!1);else try{t=new window.CustomEvent(e.event,{detail:e.data})}catch(n){(t=document.createEvent("CustomEvent")).initCustomEvent(e.event,!0,!0,e.data)}e.el.dispatchEvent(t)}function fe(t){var e={isDown:!1,moveEventTriggered:!1,startX:0,scrollLeft:0};t.addEventListener("mousedown",(function(n){e.isDown=!0,t.classList.add("drag-horizontal--active"),e.startX=n.pageX-t.offsetLeft,e.scrollLeft=t.scrollLeft})),t.addEventListener("mouseleave",(function(){e.isDown=!1,t.classList.remove("drag-horizontal--active")})),t.addEventListener("mouseup",(function(){e.isDown=!1,t.classList.remove("drag-horizontal--active"),se({event:"gform-utils/horizontal-drag-ended",native:!1}),e.moveEventTriggered=!1})),t.addEventListener("mousemove",(function(n){if(e.isDown){n.preventDefault();var r=3*(n.pageX-t.offsetLeft-e.startX);t.scrollLeft=e.scrollLeft-r,e.moveEventTriggered||(se({event:"gform-utils/horizontal-drag-started",native:!1}),e.moveEventTriggered=!0)}}))}function de(t){for(var e=[],n=t.children.length;n--;)8!==t.children[n].nodeType&&e.unshift(t.children[n]);return e}function pe(t,e){var n,r;for(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"].some((function(t){return"function"==typeof document.body[t]&&(n=t,!0)}));t;){if((r=t.parentElement)&&r[n](e))return r;t=r}return null}function he(t){var e=t.getBoundingClientRect(),n=document.body,r=document.documentElement,o=window.pageYOffset||r.scrollTop||n.scrollTop,i=window.pageXOffset||r.scrollLeft||n.scrollLeft,a=r.clientTop||n.clientTop||0,c=r.clientLeft||n.clientLeft||0,u=e.top+o-a,l=e.left+i-c;return{top:Math.round(u),left:Math.round(l),bottom:Math.round(e.bottom)}}function ve(t){var e=t.clientWidth,n=t;n.style.visibility="hidden",n.style.height="auto",n.style.maxHeight="none",n.style.position="fixed",n.style.width="".concat(e,"px");var r=n.offsetHeight;return n.style.visibility="",n.style.height="",n.style.maxHeight="",n.style.width="",n.style.position="",n.style.zIndex="",r}function ge(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:document,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3]?t:'[data-js="'.concat(t,'"]'),o=n.querySelectorAll(r);return e&&(o=d(o)),o}function me(){var t=ge(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",!1,arguments.length>1&&void 0!==arguments[1]?arguments[1]:document,arguments.length>2&&void 0!==arguments[2]&&arguments[2]);return t.length>0?t[0]:null}function ye(t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]).some((function(r){return t.classList.contains("".concat(e).concat(r).concat(n))}))}function we(t){return{vertical:t.scrollHeight>t.clientHeight,horizontal:t.scrollWidth>t.clientWidth}}function be(t,e){e.parentNode.insertBefore(t,e.nextElementSibling)}function xe(t,e){e.parentNode.insertBefore(t,e)}function Oe(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").match(/^([^:/?#]+:)?(?:\/\/([^/?#]*))?([^?#]+)?(\?[^#]*)?(#.*)?/);return"string"==typeof t[1]&&t[1].length>0&&t[1].toLowerCase()!==window.location.protocol||"string"==typeof t[2]&&t[2].length>0&&t[2].replace(new RegExp(":(".concat({"http:":80,"https:":443}[window.location.protocol],")?$")),"")!==window.location.host}function Se(){return-1!==(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").split("/").pop().indexOf(".")}function Ae(){var t;if(!window.gforms_original_json||!window.UpdateFormObject)return!1;window.UpdateFormObject();var e="1"===(null===(t=window)||void 0===t||null===(t=t.gf_legacy)||void 0===t?void 0:t.is_legacy),n=JSON.parse(JSON.stringify(JSON.parse(window.gforms_original_json))),r=JSON.parse(JSON.stringify(window.form));return e&&(n.fields.forEach((function(t,e){delete n.fields[e].layoutGroupId})),r.fields.forEach((function(t,e){delete r.fields[e].layoutGroupId}))),JSON.stringify(n)!==JSON.stringify(r)}function je(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").split(".").pop().toLowerCase().match(/(jpg|jpeg|png|gif|svg)/g);return t&&t.length>0||!1}function Ee(){var t=document.createElement("div");document.body.appendChild(t);var e="rtl"===window.getComputedStyle(t,null).getPropertyValue("direction");return document.body.removeChild(t),e}function ke(t,e){for(var n=0;n<e.length;n++)for(var r=document.querySelectorAll(e[n]),o=0;o<r.length;o++)if(t===r[o]||r[o].contains(t))return!0;return!1}function _e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=document.createElement("a");e.href=t,e.target="_blank",document.body.appendChild(e),e.click(),e.remove()}function Te(){var t=Et({event:null,url:"",center:!0,name:"_blank",specs:{menubar:0,scrollbars:0,status:1,titlebar:1,toolbar:0,top:100,left:100,width:500,height:300}},arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});if(t.event&&(t.event.preventDefault(),t.url.length||(t.url=t.event.currentTarget.href)),t.url.length){t.center&&(t.specs.top=window.screen.height/2-t.specs.height/2,t.specs.left=window.screen.width/2-t.specs.width/2);var e=[];Object.entries(t.specs).forEach((function(t){var n=f(t,2),r=n[0],o=n[1],i="".concat(r,"=").concat(o);e.push(i)})),window.open(t.url,t.name,e.join())}}function ze(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=0;n<t.classList.length;n++)-1!==t.classList.item(n).indexOf(e)&&t.classList.remove(t.classList.item(n))}function Le(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return document.querySelectorAll("[data-load-chunk-".concat(t,"]")).length>0}var Ie=function(t){var e,n=(null===(e=window)||void 0===e?void 0:e.SimpleBar)||{};n.instances&&t&&ge("[data-simplebar]",!0,t,!0).forEach((function(t){var e;return null!==(e=n.instances.get(t))&&void 0!==e?e:new n(t)}))},Ce=25,Pe=[],Me=function(t){return t<.2074?-3.8716*t*t*t+6.137*t*t+.4*t:1.1317*(t-1)*(t-1)*(t-1)-.1975*(t-1)*(t-1)+1},Re=function(t){Pe[t]||(Pe[t]={up:null,down:null})},Fe=function(t){Pe[t].up&&(window.cancelAnimationFrame(Pe[t].up),Pe[t].up=null),Pe[t].down&&(window.cancelAnimationFrame(Pe[t].down),Pe[t].down=null)},Ne=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:400,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=t.offsetHeight,i=ve(t),a=null;t.style.maxHeight="0",Re(e),Fe(e);var c=function(u){a||(a=u);var l=u-a,s=Me(l/n)*(i-o)+o;t.style.maxHeight="".concat(s,"px"),l<n?Pe[e].down=window.requestAnimationFrame(c):(Pe[e].down=null,t.style.maxHeight="none",r&&r())};setTimeout((function(){Pe[e].down=window.requestAnimationFrame(c)}),Ce)},$e=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:400,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=t.offsetHeight,i=null;t.style.maxHeight="".concat(o,"px"),Re(e),Fe(e);var a=function(c){i||(i=c);var u=c-i,l=Me(u/n)*(0-o)+o;t.style.maxHeight="".concat(l,"px"),u<n?Pe[e].up=window.requestAnimationFrame(a):(Pe[e].up=null,t.style.maxHeight="0",r&&r())};setTimeout((function(){Pe[e].up=window.requestAnimationFrame(a)}),Ce)};function De(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Ze(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?De(Object(n),!0).forEach((function(e){dt(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):De(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var He=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gform-spacing",r={};return!t||"string"!=typeof t&&"number"!=typeof t&&!Array.isArray(t)||Array.isArray(t)&&!t.length?r:"string"==typeof t||"number"==typeof t?(r["".concat(n,"--").concat(e,"bottom-").concat(t)]=!0,r):1===t.length?(["top","right","bottom","left"].forEach((function(o){r["".concat(n,"--").concat(e).concat(o,"-").concat(t[0])]=!0})),r):2===t.length?(["top","bottom"].forEach((function(o){r["".concat(n,"--").concat(e).concat(o,"-").concat(t[0])]=!0})),["right","left"].forEach((function(o){r["".concat(n,"--").concat(e).concat(o,"-").concat(t[1])]=!0})),r):3===t.length?(r["".concat(n,"--").concat(e,"top-").concat(t[0])]=!0,["right","left"].forEach((function(o){r["".concat(n,"--").concat(e).concat(o,"-").concat(t[1])]=!0})),r["gform-spacing--".concat(e,"bottom-").concat(t[2])]=!0,r):4===t.length?(r["".concat(n,"--").concat(e,"top-").concat(t[0])]=!0,r["".concat(n,"--").concat(e,"right-").concat(t[1])]=!0,r["".concat(n,"--").concat(e,"bottom-").concat(t[2])]=!0,r["".concat(n,"--").concat(e,"left-").concat(t[3])]=!0,r):r};function qe(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"gform-spacing",n={};return!t||"string"!=typeof t&&"number"!=typeof t&&!Array.isArray(t)&&("object"!==T(t)||Array.isArray(t))||Array.isArray(t)&&!t.length?n:(n[e]=!0,"string"==typeof t||"number"==typeof t||Array.isArray(t)?Ze(Ze({},n),He(t,"",e)):["","md","lg"].reduce((function(n,r){return Object.prototype.hasOwnProperty.call(t,r)?Ze(Ze({},n),He(t[r],r?"".concat(r,"-"):"",e)):n}),n))}var Ue=function(){var t="undefined"!=typeof window&&window,e="undefined"!=typeof document&&document;return{docElem:e&&e.documentElement,win:t}},Ve=function(){var t=Ue(),e=t.docElem,n=t.win,r=e.clientWidth,o=n.innerWidth;return r<o?o:r},We=function(){var t=Ue(),e=t.docElem,n=t.win,r=e.clientHeight,o=n.innerHeight;return r<o?o:r},Je=function(t){var e=t.offsetHeight,n=We(),r=t.getBoundingClientRect(),o=r.bottom,i=r.top;return Math.max(0,i>0?Math.min(e,n-i):Math.min(o,n))};function Be(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("function"!=typeof t)throw new TypeError("Expected the first argument to be a function, got `".concat(T(t),"`"));var n,r,o,i=e.wait,a=void 0===i?0:i,c=e.maxWait,u=void 0===c?Number.Infinity:c,l=e.before,s=void 0!==l&&l,f=e.after,d=void 0===f||f;if(!s&&!d)throw new Error("Both `before` and `after` are false, function wouldn't be called.");var p=function(){for(var e=arguments.length,i=new Array(e),c=0;c<e;c++)i[c]=arguments[c];var l=this,f=s&&!n;return clearTimeout(n),n=setTimeout((function(){n=void 0,r&&(clearTimeout(r),r=void 0),d&&(o=t.apply(l,i))}),a),u>0&&u!==Number.Infinity&&!r&&(r=setTimeout((function(){r=void 0,n&&(clearTimeout(n),n=void 0),d&&(o=t.apply(l,i))}),u)),f&&(o=t.apply(l,i)),o};return At(p,t),p.cancel=function(){n&&(clearTimeout(n),n=void 0),r&&(clearTimeout(r),r=void 0)},p}if("undefined"!=typeof Element&&!Element.prototype.matches){var Ge=Element.prototype;Ge.matches=Ge.matchesSelector||Ge.mozMatchesSelector||Ge.msMatchesSelector||Ge.oMatchesSelector||Ge.webkitMatchesSelector}function Ye(t,e,n,r,o){var i=Xe.apply(this,arguments);return t.addEventListener(n,i,o),{destroy:function(){t.removeEventListener(n,i,o)}}}function Xe(t,e,n,r){return function(n){n.delegateTarget=function(t,e){for(;t&&9!==t.nodeType;){if("function"==typeof t.matches&&t.matches(e))return t;t=t.parentNode}}(n.target,e),n.delegateTarget&&r.call(t,n)}}var Ke=function(t,e,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];return"function"==typeof t.addEventListener?Ye.apply(null,arguments):"function"==typeof n?Ye.bind(null,document).apply(null,arguments):("string"==typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,(function(t){return Ye(t,e,n,r,o)})))};function Qe(t,e,n,r,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void n(t)}c.done?e(u):Promise.resolve(u).then(r,o)}var tn=n(192),en=n.n(tn);window.gform=window.gform||{},window.gform.instances=window.gform.instances||{},window.gform.instances.filters=window.gform.instances.filters||[];var nn=window.gform.instances.filters,rn=function(){var t,e=(t=en().mark((function t(){var e,n,r,o,i=arguments;return en().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e=Et({data:{},event:""},i.length>0&&void 0!==i[0]?i[0]:{}),void 0===nn[e.event]){t.next=18;break}(n=nn[e.event]).sort((function(t,e){return t.priority-e.priority})),r=0;case 6:if(!(r<n.length)){t.next=18;break}if(!(o=n[r]).isAsync){t.next=14;break}return t.next=11,o.callable(e.data);case 11:e.data=t.sent,t.next=15;break;case 14:e.data=o.callable(e.data);case 15:r++,t.next=6;break;case 18:return t.abrupt("return",e.data);case 19:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,o){var i=t.apply(e,n);function a(t){Qe(i,r,o,a,c,"next",t)}function c(t){Qe(i,r,o,a,c,"throw",t)}a(void 0)}))});return function(){return e.apply(this,arguments)}}(),on=function(t,e){an(t,e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,!0)},an=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];void 0===nn[t]&&(nn[t]=[]);var o=t+"_"+nn[t].length;nn[t].push({tag:o,callable:e,priority:n,isAsync:r})},cn=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(void 0!==nn[t])for(var r=nn[t],o=r.length-1;o>=0;o--)null!==n&&n!==r[o].tag||null!==e&&parseInt(r[o].priority)!==parseInt(e)||r.splice(o,1)};function un(t){"loading"!==document.readyState?t():document.addEventListener?document.addEventListener("DOMContentLoaded",t):document.attachEvent("onreadystatechange",(function(){"loading"!==document.readyState&&t()}))}function ln(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200;!(arguments.length>2&&void 0!==arguments[2])||arguments[2]?window.addEventListener("resize",Be(t,{wait:e})):window.removeEventListener("resize",Be(t,{wait:e}))}var sn={},fn=function(t){for(var e=String(t),n=0,r=0,o=e.length;r<o;r++){n=(n<<5)-n+e.charCodeAt(r),n|=0}return"orf_"+n},dn=function(t){var e=fn(t);return void 0===sn[e]&&(sn[e]=!1),function(){sn[e]||(sn[e]=!0,t.apply(this,arguments))}};function pn(){try{window.Notification.requestPermission().then()}catch(t){return!1}return!0}var hn=function(t,e){window.localStorage.setItem(t,e)},vn=function(t){return window.localStorage.getItem(t)},gn=function(t){return window.localStorage.removeItem(t)},mn=function(){window.localStorage.clear()},yn=function(t,e){window.sessionStorage.setItem(t,e)},wn=function(t){return window.sessionStorage.getItem(t)},bn=function(t){return window.sessionStorage.removeItem(t)},xn=function(){window.sessionStorage.clear()},On=function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=document.cookie.split(";"),n=0;n<e.length;n++){var r=e[n].split("=");if(t===r[0].trim())return decodeURIComponent(r[1])}return null},Sn=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,o="",i=e;if(n&&!isNaN(Number(n))){var a=new Date;a.setTime(a.getTime()+24*Number(n)*60*60*1e3),o=" expires="+a.toUTCString()}if(r){var c=On(t);i=""!==c&&null!==c?c+","+e:e}document.cookie=encodeURIComponent(t)+"="+encodeURIComponent(i)+";"+o},An=function(t){Sn(t,"",-1)};!function(){var t=window.gformComponentNamespace||"gform";window[t]=window[t]||{},window[t].utils=window[t].utils||{};var e=window[t].utils;Object.entries(l).forEach((function(t){var n=f(t,2),r=n[0],o=n[1];e[r]=o}))}()}()}();;
