define(["underscore","backbone","react","react-dom","react-redux","analytics","js/components/api_query","js/widgets/base/base_widget","js/mixins/link_generator_mixin","./redux/configure-store","./redux/modules/api","./redux/modules/ui","./containers/app"],function(s,e,t,i,r,n,o,a,c,u,d,l,p){var h=e.View.extend({initialize:function(e){s.assign(this,e)},render:function(){return i.render(t.createElement(r.Provider,{store:this.store},t.createElement(p,null)),this.el),this},destroy:function(){i.unmountComponentAtNode(this.el)}}),e=a.extend({initialize:function(){this.store=u(this),this.view=new h({store:this.store})},defaultQueryArguments:{fl:"bibcode,data,doctype,doi,esources,first_author,genre,isbn,issn,issue,page,property,pub,title,volume,year,links_data,publisher"},activate:function(e){var i=this.store.dispatch,r=this,e=(this.setBeeHive(e),this.activateWidget(),this.getPubSub());e.subscribe(e.DISPLAY_DOCUMENTS,function(e){var t=r.store.getState().api.query;e&&s.isFunction(e.toJSON)?(e=e.toJSON(),s.isEqual(t,e)||(i(l.reset()),i(d.displayDocuments(e)))):i(l.setError("did not receive query"))}),e.subscribe(e.DELIVERING_RESPONSE,function(e){e&&s.isFunction(e.toJSON)?i(d.processResponse(e.toJSON())):i(l.setError("did not receive response from server"))}),this.attachGeneralHandler(this.onApiFeedback),this._updateLinkServer()},_updateLinkServer:function(){var e=this.store.dispatch,t=this.getBeeHive();s.isPlainObject(t)&&(t=t.getObject("User"),s.isPlainObject(t))&&t.getUserData&&(t=t.getUserData(),s.isString(t.link_server))&&e(d.setLinkServer(t.link_server))},dispatchRequest:function(e){e=new o(e);a.prototype.dispatchRequest.call(this,e)},emitAnalytics:function(e,t){n("send","event","interaction","".concat("ftl"===e?"full-text":"data","-link-followed"),{link_followed:t})},onApiFeedback:function(e){var t=this.store.dispatch;s.isPlainObject(e.error)&&t(l.setError(e.error))}});return s.extend(e.prototype,c),e});
//# sourceMappingURL=widget.jsx.js.map