define(["underscore","../modules/api","../modules/ui"],function(c,e,t){var e=e.actions,u=e.QUERY_PROVIDED,o=e.RECEIVED_RESPONSE,i=e.CURRENT_QUERY_UPDATED,s=e.FETCH_DATA,a=e.FETCHING_DATA,n=e.SEND_ANALYTICS,e=t.actions,l=e.SET_LOADING,p=e.SET_NO_RESULTS,d=e.SET_HAS_ERROR,f=e.SET_FULL_TEXT_SOURCES,y=e.SET_DATA_PRODUCTS;return function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(t){return r.map(function(e){return c.partial(e,t)})}}(function(r,e){var n=e.dispatch;return function(t){return function(e){t(e),e.type===u&&(e=e.result,n({type:l,result:!0}),n({type:i,result:e}),c.isPlainObject(e)?(e=e.q,c.isArray(e)&&0<e.length?/^(identifier|bibcode):/.test(e[0])?(e=e[0].replace(/^(identifier|bibcode):/,""),n({type:s,result:e}),r.trigger("page-manager-event","widget-ready",{isActive:!0})):n({type:d,result:"unable to parse bibcode from query"}):n({type:d,result:"did not receive a bibcode in query"})):n({type:d,result:"query is not a plain object"}))}}},function(i,e){var s=e.dispatch,a=e.getState;return function(u){return function(e){if(u(e),e.type===o){i._updateLinkServer();var t=a().api.linkServer,e=e.result;if(c.isPlainObject(e)){var r,e=e.response&&e.response.docs;if(c.isArray(e)&&0<e.length){c.isString(t)&&(e[0].link_server=t);try{r=i.parseResourcesData(e[0])}catch(e){return s({type:d,result:"unable to parse resource data"})}0<r.fullTextSources.length&&(t=r.fullTextSources,n={},0!==t.length&&t.forEach(function(e){n[e.shortName]||(n[e.shortName]=[]),n[e.shortName].push(e)}),s({type:f,result:n})),0<r.dataProducts.length&&s({type:y,result:r.dataProducts}),0===r.dataProducts.length&&0===r.fullTextSources.length&&s({type:p,result:!0}),s({type:l,result:!1})}else s({type:d,result:"did not receive docs"})}else s({type:d,result:"did not receive docs"})}var n}}},function(r,e){var n=e.dispatch;return function(t){return function(e){t(e),e.type===s&&(e={q:"identifier:".concat(e.result)},n({type:a,result:e}),n({type:l,result:!0}),r.dispatchRequest(e))}}},function(r,e){e.dispatch;return function(t){return function(e){t(e),e.type===n&&r.emitAnalytics(e.source,e.result)}}})});
//# sourceMappingURL=api.js.map