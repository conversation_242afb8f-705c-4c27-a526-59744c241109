// For license information, see `https://assets.adobedtm.com/extensions/EP31dbb9c60e404ba1aa6e746d49be6f29/AppMeasurement_Module_ActivityMap.js`.
function AppMeasurement_Module_ActivityMap(e){function t(){var e=s.pageYOffset+(s.innerHeight||0);e&&e>+f&&(f=e)}function n(){if(l.scrollReachSelector){var t=e.d.querySelector&&e.d.querySelector(l.scrollReachSelector);t?(f=t.scrollTop||0,t.addEventListener("scroll",(function(){var e;(e=t&&t.scrollTop+t.clientHeight||0)>f&&(f=e)}))):0<g--&&setTimeout(n,1e3)}}function r(e,t){var n,r,i;if(e&&t&&(n=l.c[t]||(l.c[t]=t.split(","))))for(i=0;i<n.length&&(r=n[i++]);)if(-1<e.indexOf(r))return null;return e}function i(t,n,r,i,a){var c,o;if((t.dataset&&(o=t.dataset[n])||t.getAttribute&&((o=t.getAttribute("data-"+r))||(o=t.getAttribute(r))))&&(c=o),!c&&e.useForcedLinkTracking&&a){var l;if(t=t.onclick?""+t.onclick:"",n="",i&&t&&0<=(r=t.indexOf(i))){for(r+=i.length;r<t.length;)if(o=t.charAt(r++),0<="'\"".indexOf(o)){l=o;break}for(var s=!1;r<t.length&&l&&(o=t.charAt(r),s||o!==l);)"\\"===o?s=!0:(n+=o,s=!1),r++}(l=n)&&(e.w[i]=l)}return c||a&&e.w[i]}function a(e,t,n){var i;return(i=l[t](e,n))&&r(o(i),l[t+"Exclusions"])}function c(e,t,n){var r;if(e&&!(1===(r=e.nodeType)&&(r=e.nodeName)&&(r=r.toUpperCase())&&p[r])&&(1===e.nodeType&&(r=e.nodeValue)&&(t[t.length]=r),n.a||n.t||n.s||!e.getAttribute||((r=e.getAttribute("alt"))?n.a=r:(r=e.getAttribute("title"))?n.t=r:"IMG"==(""+e.nodeName).toUpperCase()&&(r=e.getAttribute("src")||e.src)&&(n.s=r)),(r=e.childNodes)&&r.length))for(e=0;e<r.length;e++)c(r[e],t,n)}function o(e){if(null==e||null==e)return e;try{return e.replace(RegExp("^[\\s\\n\\f\\r\\t\t-\r \xa0\u1680\u180e\u2000-\u200a\u2028\u2029\u205f\u3000\ufeff]+","mg"),"").replace(RegExp("[\\s\\n\\f\\r\\t\t-\r \xa0\u1680\u180e\u2000-\u200a\u2028\u2029\u205f\u3000\ufeff]+$","mg"),"").replace(RegExp("[\\s\\n\\f\\r\\t\t-\r \xa0\u1680\u180e\u2000-\u200a\u2028\u2029\u205f\u3000\ufeff]{1,}","mg")," ").substring(0,254)}catch(e){}}var l=this;l.s=e;var s=window;s.s_c_in||(s.s_c_il=[],s.s_c_in=0),l._il=s.s_c_il,l._in=s.s_c_in,l._il[l._in]=l,s.s_c_in++,l._c="s_m";var u,f=0,g=60;l.c={};var p={SCRIPT:1,STYLE:1,LINK:1,CANVAS:1};l._g=function(){var t,n,r,i=e.contextData,c=e.linkObject;(t=e.pageName||e.pageURL)&&(n=a(c,"link",e.linkName))&&(r=a(c,"region"))&&(i["a.activitymap.page"]=t.substring(0,255),i["a.activitymap.link"]=128<n.length?n.substring(0,128):n,i["a.activitymap.region"]=127<r.length?r.substring(0,127):r,0<f&&(i["a.activitymap.xy"]=10*Math.floor(f/10)),i["a.activitymap.pageIDType"]=e.pageName?1:0)},l._d=function(){l.trackScrollReach&&!u&&(l.scrollReachSelector?n():(t(),s.addEventListener&&s.addEventListener("scroll",t,!1)),u=!0)},l.link=function(e,t){var n;if(t)n=r(o(t),l.linkExclusions);else if((n=e)&&!(n=i(e,"sObjectId","s-object-id","s_objectID",1))){var a,s;(s=r(o(e.innerText||e.textContent),l.linkExclusions))||(c(e,a=[],n={a:void 0,t:void 0,s:void 0}),(s=r(o(a.join(""))))||(s=r(o(n.a?n.a:n.t?n.t:n.s?n.s:void 0)))||!(a=(a=e.tagName)&&a.toUpperCase?a.toUpperCase():"")||("INPUT"==a||"SUBMIT"==a&&e.value?s=r(o(e.value)):"IMAGE"==a&&e.src&&(s=r(o(e.src))))),n=s}return n},l.region=function(e){for(var t,n=l.regionIDAttribute||"id";e&&(e=e.parentNode);){if(t=i(e,n,n,n))return t;if("BODY"==e.nodeName)return"BODY"}}}