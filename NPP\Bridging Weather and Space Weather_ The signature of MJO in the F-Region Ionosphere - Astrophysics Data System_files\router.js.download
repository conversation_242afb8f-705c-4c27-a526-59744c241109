define(["underscore","jquery","backbone","js/components/api_query","js/mixins/dependon","js/components/api_feedback","js/components/api_request","js/components/api_targets","js/mixins/api_access","js/components/api_query_updater"],function(r,e,i,a,t,s,o,u,n,c){i=i.Router.extend({initialize:function(e){this.queryUpdater=new c("Router")},execute:function(e,i){i=i.filter(function(e){return void 0!==e});r.isFunction(e)&&e.apply(this,i)},activate:function(e){if(this.setBeeHive(e),!this.hasPubSub())throw new Error("Ooops! Who configured this #@$%! There is no PubSub service!")},routerNavigate:function(e,i){i=i||{};this.getPubSub().publish(this.getPubSub().NAVIGATE,e,i)},routes:{"/":"index","":"index","classic-form(/)":"classicForm","paper-form(/)":"paperForm","index/(:query)":"index","search/(:query)(/)(:widgetName)":"search","search(/)(?:query)":"search","execute-query/(:query)":"executeQuery","feedback/(:subview)":"feedbackPage","abs/*path":"view","user/orcid*(:subView)":"orcidPage","user/account(/)(:subView)":"authenticationPage","user/account/verify/(:subView)/(:token)":"routeToVerifyPage","user/settings(/)(:subView)(/)":"settingsPage","user/libraries(/)(:id)(/)(:subView)(/)(:subData)(/)":"librariesPage","user/home(/)":"homePage","orcid-instructions(/)":"orcidInstructions","public-libraries/(:id)(/)":"publicLibraryPage","*invalidRoute":"noPageFound"},index:function(e){this.routerNavigate("index-page")},classicForm:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";this.routerNavigate("ClassicSearchForm",{query:(new a).load(e)})},paperForm:function(){this.routerNavigate("PaperSearchForm")},feedbackPage:function(e){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",t="";try{i=i.replace("&",encodeURIComponent("&")),t=new URLSearchParams(i).get("bibcode")}catch(e){t=""}this.routerNavigate("ShowFeedback",{subview:e,href:"#feedback/".concat(e),bibcode:t})},search:function(i,e){if(i)try{var t=(new a).load(i);this.routerNavigate("search-page",{q:t,page:e&&"show-"+e,replace:!0})}catch(e){return console.error("Error parsing query from a string: ",i,e),this.getPubSub().publish(this.getPubSub().NAVIGATE,"index-page"),this.getPubSub().publish(this.getPubSub().BIG_FIRE,new s({code:s.CODES.CANNOT_ROUTE,reason:"Cannot parse query",query:i})),this.getPubSub().publish(this.getPubSub().ALERT,new s({code:s.CODES.ALERT,msg:"unable parse query",type:"danger",modal:!0}))}else this.getPubSub().publish(this.getPubSub().NAVIGATE,"index-page",{replace:!0})},executeQuery:function(e){this.getPubSub().publish(this.getPubSub().NAVIGATE,"execute-query",e)},view:function(e){if(!e)return this.routerNavigate("404");var i,e=e.split("/"),e=(e[e.length-1].match(/^(abstract|citations|references|coreads|similar|toc|graphics|metrics|exportcitation)$/)&&(t=e.pop()),e.join("/")),t=t?(i="Show"+t[0].toUpperCase()+t.slice(1),"#abs/"+encodeURIComponent(e)+"/"+t):(i="ShowAbstract","#abs/"+encodeURIComponent(e)+"/abstract");this.routerNavigate(i,{href:t,bibcode:e})},routeToVerifyPage:function(e,i){this.getPubSub().publish(this.getPubSub().NAVIGATE,"user-action",{subView:e,token:i})},orcidPage:function(){this.getPubSub().publish(this.getPubSub().NAVIGATE,"orcid-page")},orcidInstructions:function(){this.getPubSub().publish(this.getPubSub().NAVIGATE,"orcid-instructions")},authenticationPage:function(e){if(e&&!r.contains(["login","register","reset-password-1","reset-password-2","resend-verification-email"],e))throw new Error("that isn't a subview that the authentication page knows about");this.routerNavigate("authentication-page",{subView:e})},settingsPage:function(e){r.contains(["token","password","email","delete"],e)?this.routerNavigate("UserSettings",{subView:e}):r.contains(["librarylink","orcid","application","export"],e)?this.routerNavigate("UserPreferences",{subView:e}):r.contains(["libraryimport"],e)?this.routerNavigate("LibraryImport"):r.contains(["myads"],e)?this.routerNavigate("MyAdsDashboard"):this.routerNavigate("UserPreferences",{subView:void 0})},librariesPage:function(e,i,t){if(e){if("actions"===e)return this.routerNavigate("LibraryActionsWidget","libraries");i=i||"library";if(r.contains(["library","admin"],i))this.routerNavigate("IndividualLibraryWidget",{subView:i,id:e});else{if(!r.contains(["export","metrics","visualization"],i))throw new Error("did not recognize subview for library view");"library-export"==(i="library-"+i)?this.routerNavigate(i,{subView:t||"bibtex",id:e}):"library-metrics"==i&&this.routerNavigate(i,{id:e})}}else this.routerNavigate("AllLibrariesWidget","libraries")},publicLibraryPage:function(e){this.getPubSub().publish(this.getPubSub().NAVIGATE,"IndividualLibraryWidget",{id:e,publicView:!0,subView:"library"})},homePage:function(e){this.routerNavigate("home-page",{subView:e})},noPageFound:function(){this.routerNavigate("404")},_extractParameters:function(e,i){return r.map(e.exec(i).slice(1),function(e){return/q\=/.test(e)?e:e&&decodeURIComponent(e)})}});return r.extend(i.prototype,t.BeeHive,n),i});
//# sourceMappingURL=router.js.map