(()=>{var n={891:function(n){var e;e=()=>(()=>{var n={684:function(n){"undefined"!=typeof self&&self,n.exports=function(n){var e={};function t(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return n[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}return t.m=n,t.c=e,t.d=function(n,e,r){t.o(n,e)||Object.defineProperty(n,e,{enumerable:!0,get:r})},t.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},t.t=function(n,e){if(1&e&&(n=t(n)),8&e)return n;if(4&e&&"object"==typeof n&&n&&n.__esModule)return n;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:n}),2&e&&"string"!=typeof n)for(var o in n)t.d(r,o,function(e){return n[e]}.bind(null,o));return r},t.n=function(n){var e=n&&n.__esModule?function(){return n.default}:function(){return n};return t.d(e,"a",e),e},t.o=function(n,e){return{}.hasOwnProperty.call(n,e)},t.p="",t(t.s=0)}([function(n,e,t){"use strict";t.r(e);var r={};function o(n){return"[object RegExp]"==={}.toString.call(n)}t.r(r),t.d(r,"WeakMap",function(){return N});var i="file:",a="about:",c="iframe",u="popup",s="Call was rejected by callee.\r\n";function f(n){return void 0===n&&(n=window),n.location.protocol===a}function d(n){if(n)try{if(n.parent&&n.parent!==n)return n.parent}catch(n){}}function l(n){if(n&&!d(n))try{return n.opener}catch(n){}}function h(n){try{return!0}catch(n){}return!1}function w(n){var e=(n=n||window).location;if(!e)throw new Error("Can not read window location");var t=e.protocol;if(!t)throw new Error("Can not read window protocol");if(t===i)return i+"//";if(t===a){var r=d(n);return r&&h()?w(r):a+"//"}var o=e.host;if(!o)throw new Error("Can not read window host");return t+"//"+o}function p(n){var e=w(n=n||window);return e&&n.mockDomain&&0===n.mockDomain.indexOf("mock:")?n.mockDomain:e}function v(n){if(!function(n){try{if(n===window)return!0}catch(n){}try{var e=Object.getOwnPropertyDescriptor(n,"location");if(e&&!1===e.enumerable)return!1}catch(n){}try{if(f(n)&&h())return!0}catch(n){}try{if(w(n)===w(window))return!0}catch(n){}return!1}(n))return!1;try{if(n===window)return!0;if(f(n)&&h())return!0;if(p(window)===p(n))return!0}catch(n){}return!1}function y(n,e){if(!n||!e)return!1;var t=d(e);return t?t===n:-1!==function(n){var e=[];try{for(;n.parent!==n;)e.push(n.parent),n=n.parent}catch(n){}return e}(e).indexOf(n)}function m(n){var e,t,r=[];try{e=n.frames}catch(t){e=n}try{t=e.length}catch(n){}if(0===t)return r;if(t){for(var o=0;o<t;o++){var i=void 0;try{i=e[o]}catch(n){continue}r.push(i)}return r}for(var a=0;a<100;a++){var c=void 0;try{c=e[a]}catch(n){return r}if(!c)return r;r.push(c)}return r}var g=[],_=[];function b(n,e){void 0===e&&(e=!0);try{if(n===window)return!1}catch(n){return!0}try{if(!n)return!0}catch(n){return!0}try{if(n.closed)return!0}catch(n){return!n||n.message!==s}if(e&&v(n))try{if(n.mockclosed)return!0}catch(n){}try{if(!n.parent||!n.top)return!0}catch(n){}var t=function(n,e){for(var t=0;t<n.length;t++)try{if(n[t]===e)return t}catch(n){}return-1}(g,n);if(-1!==t){var r=_[t];if(r&&function(n){if(!n.contentWindow)return!0;if(!n.parentNode)return!0;var e=n.ownerDocument;return!(!e||!e.documentElement||e.documentElement.contains(n))}(r))return!0}return!1}function x(n){return l(n=n||window)||d(n)||void 0}function k(n,e){if("string"==typeof n){if("string"==typeof e)return"*"===n||e===n;if(o(e))return!1;if(Array.isArray(e))return!1}return o(n)?o(e)?n.toString()===e.toString():!Array.isArray(e)&&Boolean(e.match(n)):!!Array.isArray(n)&&(Array.isArray(e)?JSON.stringify(n)===JSON.stringify(e):!o(e)&&n.some(function(n){return k(n,e)}))}function j(n){try{if(n===window)return!0}catch(n){if(n&&n.message===s)return!0}try{if("[object Window]"==={}.toString.call(n))return!0}catch(n){if(n&&n.message===s)return!0}try{if(window.Window&&n instanceof window.Window)return!0}catch(n){if(n&&n.message===s)return!0}try{if(n&&n.self===n)return!0}catch(n){if(n&&n.message===s)return!0}try{if(n&&n.parent===n)return!0}catch(n){if(n&&n.message===s)return!0}try{if(n&&n.top===n)return!0}catch(n){if(n&&n.message===s)return!0}return!1}function E(n){try{if(!n)return!1;if("undefined"!=typeof Promise&&n instanceof Promise)return!0;if("undefined"!=typeof window&&window.Window&&n instanceof window.Window)return!1;if("undefined"!=typeof window&&window.constructor&&n instanceof window.constructor)return!1;var e={}.toString;if(e){var t=e.call(n);if("[object Window]"===t||"[object global]"===t||"[object DOMWindow]"===t)return!1}if("function"==typeof n.then)return!0}catch(n){return!1}return!1}var S,W=[],O=[],P=0;function A(){if(!P&&S){var n=S;S=null,n.resolve()}}function C(){P+=1}function D(){P-=1,A()}var I=function(){function n(n){var e=this;if(this.resolved=void 0,this.rejected=void 0,this.errorHandled=void 0,this.value=void 0,this.error=void 0,this.handlers=void 0,this.dispatching=void 0,this.stack=void 0,this.resolved=!1,this.rejected=!1,this.errorHandled=!1,this.handlers=[],n){var t,r,o=!1,i=!1,a=!1;C();try{n(function(n){a?e.resolve(n):(o=!0,t=n)},function(n){a?e.reject(n):(i=!0,r=n)})}catch(n){return D(),void this.reject(n)}D(),a=!0,o?this.resolve(t):i&&this.reject(r)}}var e=n.prototype;return e.resolve=function(n){if(this.resolved||this.rejected)return this;if(E(n))throw new Error("Can not resolve promise with another promise");return this.resolved=!0,this.value=n,this.dispatch(),this},e.reject=function(n){var e=this;if(this.resolved||this.rejected)return this;if(E(n))throw new Error("Can not reject promise with another promise");if(!n){var t=n&&"function"==typeof n.toString?n.toString():{}.toString.call(n);n=new Error("Expected reject to be called with Error, got "+t)}return this.rejected=!0,this.error=n,this.errorHandled||setTimeout(function(){e.errorHandled||function(n,e){if(-1===W.indexOf(n)){W.push(n),setTimeout(function(){throw n},1);for(var t=0;t<O.length;t++)O[t](n,e)}}(n,e)},1),this.dispatch(),this},e.asyncReject=function(n){return this.errorHandled=!0,this.reject(n),this},e.dispatch=function(){var e=this,t=this.resolved,r=this.rejected,o=this.handlers;if(!this.dispatching&&(t||r)){this.dispatching=!0,C();for(var i=function(i){var a=o[i],c=a.onSuccess,u=a.onError,s=a.promise,f=void 0;if(t)try{f=c?c(e.value):e.value}catch(n){return s.reject(n),"continue"}else if(r){if(!u)return s.reject(e.error),"continue";try{f=u(e.error)}catch(n){return s.reject(n),"continue"}}f instanceof n&&(f.resolved||f.rejected)?(f.resolved?s.resolve(f.value):s.reject(f.error),f.errorHandled=!0):E(f)?f instanceof n&&(f.resolved||f.rejected)?f.resolved?s.resolve(f.value):s.reject(f.error):f.then(function(n){s.resolve(n)},function(n){s.reject(n)}):s.resolve(f)},a=0;a<o.length;a++)i(a);o.length=0,this.dispatching=!1,D()}},e.then=function(e,t){if(e&&"function"!=typeof e&&!e.call)throw new Error("Promise.then expected a function for success handler");if(t&&"function"!=typeof t&&!t.call)throw new Error("Promise.then expected a function for error handler");var r=new n;return this.handlers.push({promise:r,onSuccess:e,onError:t}),this.errorHandled=!0,this.dispatch(),r},e.catch=function(n){return this.then(void 0,n)},e.finally=function(e){if(e&&"function"!=typeof e&&!e.call)throw new Error("Promise.finally expected a function");return this.then(function(t){return n.try(e).then(function(){return t})},function(t){return n.try(e).then(function(){throw t})})},e.timeout=function(n,e){var t=this;if(this.resolved||this.rejected)return this;var r=setTimeout(function(){t.resolved||t.rejected||t.reject(e||new Error("Promise timed out after "+n+"ms"))},n);return this.then(function(n){return clearTimeout(r),n})},e.toPromise=function(){if("undefined"==typeof Promise)throw new TypeError("Could not find Promise");return Promise.resolve(this)},n.resolve=function(e){return e instanceof n?e:E(e)?new n(function(n,t){return e.then(n,t)}):(new n).resolve(e)},n.reject=function(e){return(new n).reject(e)},n.asyncReject=function(e){return(new n).asyncReject(e)},n.all=function(e){var t=new n,r=e.length,o=[];if(!r)return t.resolve(o),t;for(var i=function(i){var a=e[i];if(a instanceof n){if(a.resolved)return o[i]=a.value,r-=1,"continue"}else if(!E(a))return o[i]=a,r-=1,"continue";n.resolve(a).then(function(n){o[i]=n,0==(r-=1)&&t.resolve(o)},function(n){t.reject(n)})},a=0;a<e.length;a++)i(a);return 0===r&&t.resolve(o),t},n.hash=function(e){var t={};return n.all(Object.keys(e).map(function(r){return n.resolve(e[r]).then(function(n){t[r]=n})})).then(function(){return t})},n.map=function(e,t){return n.all(e.map(t))},n.onPossiblyUnhandledException=function(n){return function(n){return O.push(n),{cancel:function(){O.splice(O.indexOf(n),1)}}}(n)},n.try=function(e,t,r){if(e&&"function"!=typeof e&&!e.call)throw new Error("Promise.try expected a function");var o;C();try{o=e.apply(t,r||[])}catch(e){return D(),n.reject(e)}return D(),n.resolve(o)},n.delay=function(e){return new n(function(n){setTimeout(n,e)})},n.isPromise=function(e){return!!(e&&e instanceof n)||E(e)},n.flush=function(){return e=S=S||new n,A(),e;var e},n}();function M(){return(M=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n}).apply(this,arguments)}function z(n,e){for(var t=0;t<n.length;t++)try{if(n[t]===e)return t}catch(n){}return-1}var T,R=Object.defineProperty,q=Date.now()%1e9,N=function(){function n(){if(this.name=void 0,this.weakmap=void 0,this.keys=void 0,this.values=void 0,q+=1,this.name="__weakmap_"+(1e9*Math.random()>>>0)+"__"+q,function(){if("undefined"==typeof WeakMap)return!1;if(void 0===Object.freeze)return!1;try{var n=new WeakMap,e={};return Object.freeze(e),n.set(e,"__testvalue__"),"__testvalue__"===n.get(e)}catch(n){return!1}}())try{this.weakmap=new WeakMap}catch(n){}this.keys=[],this.values=[]}var e=n.prototype;return e._cleanupClosedWindows=function(){for(var n=this.weakmap,e=this.keys,t=0;t<e.length;t++){var r=e[t];if(j(r)&&b(r)){if(n)try{n.delete(r)}catch(n){}e.splice(t,1),this.values.splice(t,1),t-=1}}},e.isSafeToReadWrite=function(n){return!j(n)},e.set=function(n,e){if(!n)throw new Error("WeakMap expected key");var t=this.weakmap;if(t)try{t.set(n,e)}catch(n){delete this.weakmap}if(this.isSafeToReadWrite(n)){var r=this.name,o=n[r];o&&o[0]===n?o[1]=e:R(n,r,{value:[n,e],writable:!0})}else{this._cleanupClosedWindows();var i=this.keys,a=this.values,c=z(i,n);-1===c?(i.push(n),a.push(e)):a[c]=e}},e.get=function(n){if(!n)throw new Error("WeakMap expected key");var e=this.weakmap;if(e)try{if(e.has(n))return e.get(n)}catch(n){delete this.weakmap}if(!this.isSafeToReadWrite(n)){this._cleanupClosedWindows();var t=z(this.keys,n);if(-1===t)return;return this.values[t]}var r=n[this.name];if(r&&r[0]===n)return r[1]},e.delete=function(n){if(!n)throw new Error("WeakMap expected key");var e=this.weakmap;if(e)try{e.delete(n)}catch(n){delete this.weakmap}if(this.isSafeToReadWrite(n)){var t=n[this.name];t&&t[0]===n&&(t[0]=t[1]=void 0)}else{this._cleanupClosedWindows();var r=this.keys,o=z(r,n);-1!==o&&(r.splice(o,1),this.values.splice(o,1))}},e.has=function(n){if(!n)throw new Error("WeakMap expected key");var e=this.weakmap;if(e)try{if(e.has(n))return!0}catch(n){delete this.weakmap}if(this.isSafeToReadWrite(n)){var t=n[this.name];return!(!t||t[0]!==n)}return this._cleanupClosedWindows(),-1!==z(this.keys,n)},e.getOrSet=function(n,e){if(this.has(n))return this.get(n);var t=e();return this.set(n,t),t},n}();function L(){return"xxxxxxxxxx".replace(/./g,function(){return"0123456789abcdef".charAt(Math.floor(16*Math.random()))})+"_"+function(n){if("function"==typeof btoa)return btoa(n);if("undefined"!=typeof Buffer)return Buffer.from(n,"utf8").toString("base64");throw new Error("Can not find window.btoa or Buffer")}((new Date).toISOString().slice(11,19).replace("T",".")).replace(/[^a-zA-Z0-9]/g,"").toLowerCase()}function F(n){var e={};function t(){for(var t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];var i=function(n){try{return JSON.stringify([].slice.call(n),function(n,e){return"function"==typeof e?"memoize["+function(n){if(T=T||new N,null==n||"object"!=typeof n&&"function"!=typeof n)throw new Error("Invalid object");var e=T.get(n);return e||(e=typeof n+":"+L(),T.set(n,e)),e}(e)+"]":e})}catch(n){throw new Error("Arguments not serializable -- can not be used to memoize")}}(r);return e.hasOwnProperty(i)||(e[i]=n.apply(this,arguments).finally(function(){delete e[i]})),e[i]}return t.reset=function(){e={}},t}function H(){}function J(n,e){if(void 0===e&&(e=1),e>=3)return"stringifyError stack overflow";try{if(!n)return"<unknown error: "+{}.toString.call(n)+">";if("string"==typeof n)return n;if(n instanceof Error){var t=n&&n.stack,r=n&&n.message;if(t&&r)return-1!==t.indexOf(r)?t:r+"\n"+t;if(t)return t;if(r)return r}return"function"==typeof n.toString?n.toString():{}.toString.call(n)}catch(n){return"Error while stringifying error: "+J(n,e+1)}}function U(n){return"string"==typeof n?n:n&&"function"==typeof n.toString?n.toString():{}.toString.call(n)}function B(n){return"[object RegExp]"==={}.toString.call(n)}function G(n,e,t){if(n.hasOwnProperty(e))return n[e];var r=t();return n[e]=r,r}Object.create(Error.prototype);var K="postrobot_method",Q="postrobot_hello",Y="*",Z="cross_domain_zalgo_promise",V="cross_domain_function",X="cross_domain_window";function $(n){return void 0===n&&(n=window),n!==window?n.__post_robot_10_0_14__:n.__post_robot_10_0_14__=n.__post_robot_10_0_14__||{}}var nn=function(){return{}};function en(n,e){return void 0===n&&(n="store"),void 0===e&&(e=nn),G($(),n,function(){var n=e();return{has:function(e){return n.hasOwnProperty(e)},get:function(e,t){return n.hasOwnProperty(e)?n[e]:t},set:function(e,t){return n[e]=t,t},del:function(e){delete n[e]},getOrSet:function(e,t){return G(n,e,t)},reset:function(){n=e()},keys:function(){return Object.keys(n)}}})}var tn=function(){};function rn(){var n=$();return n.WINDOW_WILDCARD=n.WINDOW_WILDCARD||new tn,n.WINDOW_WILDCARD}function on(n,e){return void 0===n&&(n="store"),void 0===e&&(e=nn),en("windowStore").getOrSet(n,function(){var t=new N,r=function(n){return t.getOrSet(n,e)};return{has:function(e){return r(e).hasOwnProperty(n)},get:function(e,t){var o=r(e);return o.hasOwnProperty(n)?o[n]:t},set:function(e,t){return r(e)[n]=t,t},del:function(e){delete r(e)[n]},getOrSet:function(e,t){return G(r(e),n,t)}}})}function an(){return en("instance").getOrSet("instanceID",L)}function cn(n){return on("helloPromises").getOrSet(n,function(){return new I})}function un(n,e){return(0,e.send)(n,Q,{instanceID:an()},{domain:Y,timeout:-1}).then(function(e){var t=e.origin,r=e.data.instanceID;return cn(n).resolve({win:n,domain:t}),{win:n,domain:t,instanceID:r}})}function sn(n,e){var t=e.send;return on("windowInstanceIDPromises").getOrSet(n,function(){return un(n,{send:t}).then(function(n){return n.instanceID})})}function fn(n){on("knownWindows").set(n,!0)}var dn,ln="function",hn="error",wn="promise",pn="regex",vn="date",yn="array",mn="object",gn="string",_n="number",bn="boolean",xn="null",kn="undefined";function jn(n){return"object"==typeof n&&null!==n&&"string"==typeof n.__type__}function En(n){return void 0===n?kn:null===n?xn:Array.isArray(n)?yn:"function"==typeof n?ln:"object"==typeof n?n instanceof Error?hn:"function"==typeof n.then?wn:"[object RegExp]"==={}.toString.call(n)?pn:"[object Date]"==={}.toString.call(n)?vn:mn:"string"==typeof n?gn:"number"==typeof n?_n:"boolean"==typeof n?bn:void 0}function Sn(n,e){return{__type__:n,__val__:e}}var Wn,On=((dn={})[ln]=function(){},dn[hn]=function(n){return Sn(hn,{message:n.message,stack:n.stack,code:n.code})},dn[wn]=function(){},dn[pn]=function(n){return Sn(pn,n.source)},dn[vn]=function(n){return Sn(vn,n.toJSON())},dn[yn]=function(n){return n},dn[mn]=function(n){return n},dn[gn]=function(n){return n},dn[_n]=function(n){return n},dn[bn]=function(n){return n},dn[xn]=function(n){return n},dn),Pn={},An=((Wn={})[ln]=function(){throw new Error("Function serialization is not implemented; nothing to deserialize")},Wn[hn]=function(n){var e=n.stack,t=n.code,r=new Error(n.message);return r.code=t,r.stack=e+"\n\n"+r.stack,r},Wn[wn]=function(){throw new Error("Promise serialization is not implemented; nothing to deserialize")},Wn[pn]=function(n){return new RegExp(n)},Wn[vn]=function(n){return new Date(n)},Wn[yn]=function(n){return n},Wn[mn]=function(n){return n},Wn[gn]=function(n){return n},Wn[_n]=function(n){return n},Wn[bn]=function(n){return n},Wn[xn]=function(n){return n},Wn),Cn={};function Dn(){for(var n=en("idToProxyWindow"),e=0,t=n.keys();e<t.length;e++){var r=t[e];n.get(r).shouldClean()&&n.del(r)}}function In(n,e,t){var r,o=t.send;return{id:n,type:l(e)?u:c,getInstanceID:F(function(){return sn(e,{send:o})}),close:function(){return I.try(function(){e.close()})},getName:function(){return I.try(function(){if(!b(e))return r})},focus:function(){return I.try(function(){e.focus()})},isClosed:function(){return I.try(function(){return b(e)})},setLocation:function(n){return I.try(function(){if(v(e))try{if(e.location&&"function"==typeof e.location.replace)return void e.location.replace(n)}catch(n){}e.location=n})},setName:function(n){return I.try(function(){(e=function(n){if(!v(n))throw new Error("Expected window to be same domain");return n}(e)).name=n,e.frameElement&&e.frameElement.setAttribute("name",n),r=n})}}}new I(function(n){if(window.document&&window.document.body)return n(window.document.body);var e=setInterval(function(){if(window.document&&window.document.body)return clearInterval(e),n(window.document.body)},10)});var Mn=function(){function n(n,e,t){var r=t.send;this.isProxyWindow=!0,this.serializedWindow=void 0,this.actualWindow=void 0,this.actualWindowPromise=void 0,this.send=void 0,this.name=void 0,this.serializedWindow=n,this.actualWindowPromise=new I,this.send=r,e&&this.setWindow(e)}var e=n.prototype;return e.getType=function(){return this.serializedWindow.type},e.isPopup=function(){return this.getType()===u},e.isIframe=function(){return this.getType()===c},e.setLocation=function(n){var e=this;return this.serializedWindow.setLocation(n).then(function(){return e})},e.setName=function(n){var e=this;return this.serializedWindow.setName(n).then(function(){return e})},e.close=function(){var n=this;return this.serializedWindow.close().then(function(){return n})},e.focus=function(){var n=this;return I.try(function(){return I.all([n.isPopup()&&n.serializedWindow.getName().then(function(n){n&&window.open("",n)}),n.serializedWindow.focus()])}).then(function(){return n})},e.isClosed=function(){return this.serializedWindow.isClosed()},e.getWindow=function(){return this.actualWindow},e.setWindow=function(n){this.actualWindow=n,this.serializedWindow=In(this.serializedWindow.id,n,{send:this.send}),this.actualWindowPromise.resolve(n)},e.awaitWindow=function(){return this.actualWindowPromise},e.matchWindow=function(n){var e=this;return I.try(function(){return e.actualWindow?n===e.actualWindow:I.all([e.getInstanceID(),sn(n,{send:e.send})]).then(function(t){var r=t[0]===t[1];return r&&e.setWindow(n),r})})},e.unwrap=function(){return this.actualWindow||this},e.getInstanceID=function(){return this.serializedWindow.getInstanceID()},e.serialize=function(){return this.serializedWindow},e.shouldClean=function(){return this.actualWindow&&b(this.actualWindow)},n.unwrap=function(e){return n.isProxyWindow(e)?e.unwrap():e},n.serialize=function(e,t){var r=t.send;return Dn(),n.toProxyWindow(e,{send:r}).serialize()},n.deserialize=function(e,t){var r=t.on,o=t.send;return Dn(),en("idToProxyWindow").getOrSet(e.id,function(){return new n(e,null,{on:r,send:o})})},n.isProxyWindow=function(n){return Boolean(n&&!j(n)&&n.isProxyWindow)},n.toProxyWindow=function(e,t){var r=t.send;if(Dn(),n.isProxyWindow(e))return e;var o=e;return on("winToProxyWindow").getOrSet(e,function(){var e=L(),t=new n(In(e,o,{send:r}),o,{send:r});return en("idToProxyWindow").set(e,t)})},n}();function zn(n,e,t,r,o){var i=on("methodStore"),a=en("proxyWindowMethods");Mn.isProxyWindow(r)?a.set(n,{val:e,name:t,domain:o,source:r}):(a.del(n),i.getOrSet(r,function(){return{}})[n]={domain:o,name:t,val:e,source:r})}function Tn(n,e){var t=on("methodStore"),r=en("proxyWindowMethods");return t.getOrSet(n,function(){return{}})[e]||r.get(e)}function Rn(n,e,t,r,o){var i;i=o.on,en("builtinListeners").getOrSet("functionCalls",function(){return i(K,{domain:Y},function(n){var e=n.source,t=n.origin,r=n.data,o=r.id,i=r.name,a=Tn(e,o);if(!a)throw new Error("Could not find method '"+r.name+"' with id: "+r.id+" in "+p(window));var c=a.source,u=a.domain,s=a.val;return I.try(function(){if(!k(u,t))throw new Error("Method '"+r.name+"' domain "+JSON.stringify(B(a.domain)?a.domain.source:a.domain)+" does not match origin "+t+" in "+p(window));if(Mn.isProxyWindow(c))return c.matchWindow(e).then(function(n){if(!n)throw new Error("Method call '"+r.name+"' failed - proxy window does not match source in "+p(window))})}).then(function(){return s.apply({source:e,origin:t},r.args)},function(n){return I.try(function(){if(s.onError)return s.onError(n)}).then(function(){throw n.stack&&(n.stack="Remote call to "+i+"()\n\n"+n.stack),n})}).then(function(n){return{result:n,id:o,name:i}})})});var a=t.__id__||L();n=Mn.unwrap(n);var c=t.__name__||t.name||r;return Mn.isProxyWindow(n)?(zn(a,t,c,n,e),n.awaitWindow().then(function(n){zn(a,t,c,n,e)})):zn(a,t,c,n,e),Sn(V,{id:a,name:c})}function qn(n,e,t,r){var o,i=r.on,a=r.send;return function(n,e){void 0===e&&(e=Pn);var t=JSON.stringify(n,function(n){var t=this[n];if(jn(this))return t;var r=En(t);if(!r)return t;var o=e[r]||On[r];return o?o(t,n):t});return void 0===t?kn:t}(t,((o={})[wn]=function(t,r){return function(n,e,t,r,o){return Sn(Z,{then:Rn(n,e,function(n,e){return t.then(n,e)},r,{on:o.on,send:o.send})})}(n,e,t,r,{on:i,send:a})},o[ln]=function(t,r){return Rn(n,e,t,r,{on:i,send:a})},o[mn]=function(n){return j(n)||Mn.isProxyWindow(n)?Sn(X,Mn.serialize(n,{send:a})):n},o))}function Nn(n,e,t,r){var o,i=r.on,a=r.send;return function(n,e){if(void 0===e&&(e=Cn),n!==kn)return JSON.parse(n,function(n,t){if(jn(this))return t;var r,o;if(jn(t)?(r=t.__type__,o=t.__val__):(r=En(t),o=t),!r)return o;var i=e[r]||An[r];return i?i(o,n):o})}(t,((o={})[Z]=function(n){return new I(n.then)},o[V]=function(t){return function(n,e,r,o){var i=t.id,a=t.name,c=o.send,u=function(t){function r(){var o=arguments;return Mn.toProxyWindow(n,{send:c}).awaitWindow().then(function(n){var u=Tn(n,i);if(u&&u.val!==r)return u.val.apply({source:window,origin:p()},o);var s={domain:e,fireAndForget:t.fireAndForget},f=[].slice.call(o);return c(n,K,{id:i,name:a,args:f},s).then(function(n){if(!t.fireAndForget)return n.data.result})}).catch(function(n){throw n})}return void 0===t&&(t={}),r.__name__=a,r.__origin__=e,r.__source__=n,r.__id__=i,r.origin=e,r},s=u();return s.fireAndForget=u({fireAndForget:!0}),s}(n,e,0,{on:i,send:a})},o[X]=function(n){return Mn.deserialize(n,{on:(e={on:i,send:a}).on,send:e.send});var e},o))}var Ln={};function Fn(n,e,t,r){var o,i=r.on,a=r.send;if(b(n))throw new Error("Window is closed");for(var c=qn(n,e,((o={}).__post_robot_10_0_14__=M({id:L(),origin:p(window)},t),o),{on:i,send:a}),u=Object.keys(Ln),s=[],f=0;f<u.length;f++){var d=u[f];try{Ln[d](n,c,e)}catch(n){s.push(n)}}if(s.length===u.length)throw new Error("All post-robot messaging strategies failed:\n\n"+s.map(J).join("\n\n"))}Ln.postrobot_post_message=function(n,e,t){(Array.isArray(t)?t:"string"==typeof t?[t]:[Y]).map(function(n){return 0===n.indexOf(i)?Y:n}).forEach(function(t){n.postMessage(e,t)})};var Hn,Jn="__domain_regex__";function Un(n){return en("responseListeners").get(n)}function Bn(n){en("responseListeners").del(n)}function Gn(n){return en("erroredResponseListeners").has(n)}function Kn(n){var e=n.name,t=n.win,r=n.domain,o=on("requestListeners");if(t===Y&&(t=null),r===Y&&(r=null),!e)throw new Error("Name required to get request listener");for(var i=0,a=[t,rn()];i<a.length;i++){var c=a[i];if(c){var u=o.get(c);if(u){var s=u[e];if(s){if(r&&"string"==typeof r){if(s[r])return s[r];if(s[Jn])for(var f=0,d=s[Jn];f<d.length;f++){var l=d[f],h=l.listener;if(k(l.regex,r))return h}}if(s[Y])return s[Y]}}}}}var Qn=((Hn={}).postrobot_message_request=function(n,e,t,r){var o=r.on,i=r.send,a=Kn({name:t.name,win:n,domain:e});function c(r,a,c){void 0===c&&(c={}),t.fireAndForget||b(n)||Fn(n,e,M({type:r,ack:a,hash:t.hash,name:t.name},c),{on:o,send:i})}return I.all([c("postrobot_message_ack"),I.try(function(){if(!a)throw new Error("No handler found for post message: "+t.name+" from "+e+" in "+window.location.protocol+"//"+window.location.host+window.location.pathname);if(!k(a.domain,e))throw new Error("Request origin "+e+" does not match domain "+a.domain.toString());return a.handler({source:n,origin:e,data:t.data})}).then(function(n){return c("postrobot_message_response","success",{data:n})},function(n){return c("postrobot_message_response","error",{error:n})})]).then(H).catch(function(n){if(a&&a.handleError)return a.handleError(n);throw n})},Hn.postrobot_message_ack=function(n,e,t){if(!Gn(t.hash)){var r=Un(t.hash);if(!r)throw new Error("No handler found for post message ack for message: "+t.name+" from "+e+" in "+window.location.protocol+"//"+window.location.host+window.location.pathname);if(!k(r.domain,e))throw new Error("Ack origin "+e+" does not match domain "+r.domain.toString());if(n!==r.win)throw new Error("Ack source does not match registered window");r.ack=!0}},Hn.postrobot_message_response=function(n,e,t){if(!Gn(t.hash)){var r,i=Un(t.hash);if(!i)throw new Error("No handler found for post message response for message: "+t.name+" from "+e+" in "+window.location.protocol+"//"+window.location.host+window.location.pathname);if(!k(i.domain,e))throw new Error("Response origin "+e+" does not match domain "+(r=i.domain,Array.isArray(r)?"("+r.join(" | ")+")":o(r)?"RegExp("+r.toString():r.toString()));if(n!==i.win)throw new Error("Response source does not match registered window");Bn(t.hash),"error"===t.ack?i.promise.reject(t.error):"success"===t.ack&&i.promise.resolve({source:n,origin:e,data:t.data})}},Hn);function Yn(n,e){var t=e.on,r=e.send,o=en("receivedMessages");if(!window||window.closed)throw new Error("Message recieved in closed window");try{if(!n.source)return}catch(n){return}var a=n.source,c=n.origin,u=function(n,e,t,r){var o,i=r.on,a=r.send;try{o=Nn(e,t,n,{on:i,send:a})}catch(n){return}if(o&&"object"==typeof o&&null!==o&&(o=o.__post_robot_10_0_14__)&&"object"==typeof o&&null!==o&&o.type&&"string"==typeof o.type&&Qn[o.type])return o}(n.data,a,c,{on:t,send:r});u&&(fn(a),o.has(u.id)||(o.set(u.id,!0),b(a)&&!u.fireAndForget||(0===u.origin.indexOf(i)&&(c=i+"//"),Qn[u.type](a,c,u,{on:t,send:r}))))}function Zn(n,e,t){if(!n)throw new Error("Expected name");if("function"==typeof e&&(t=e,e={}),!t)throw new Error("Expected handler");(e=e||{}).name=n,e.handler=t||e.handler;var r=e.window,o=e.domain,i=function n(e,t){var r=e.name,o=e.win,i=e.domain,a=on("requestListeners");if(!r||"string"!=typeof r)throw new Error("Name required to add request listener");if(Array.isArray(o)){for(var c=[],u=0,s=o;u<s.length;u++)c.push(n({name:r,domain:i,win:s[u]},t));return{cancel:function(){for(var n=0;n<c.length;n++)c[n].cancel()}}}if(Array.isArray(i)){for(var f=[],d=0,l=i;d<l.length;d++)f.push(n({name:r,win:o,domain:l[d]},t));return{cancel:function(){for(var n=0;n<f.length;n++)f[n].cancel()}}}var h=Kn({name:r,win:o,domain:i});if(o&&o!==Y||(o=rn()),i=i||Y,h)throw o&&i?new Error("Request listener already exists for "+r+" on domain "+i.toString()+" for "+(o===rn()?"wildcard":"specified")+" window"):o?new Error("Request listener already exists for "+r+" for "+(o===rn()?"wildcard":"specified")+" window"):i?new Error("Request listener already exists for "+r+" on domain "+i.toString()):new Error("Request listener already exists for "+r);var w,p,v=a.getOrSet(o,function(){return{}}),y=G(v,r,function(){return{}}),m=i.toString();return B(i)?(w=G(y,Jn,function(){return[]})).push(p={regex:i,listener:t}):y[m]=t,{cancel:function(){delete y[m],p&&(w.splice(w.indexOf(p,1)),w.length||delete y[Jn]),Object.keys(y).length||delete v[r],o&&!Object.keys(v).length&&a.del(o)}}}({name:n,win:r,domain:o},{handler:e.handler,handleError:e.errorHandler||function(n){throw n},window:r,domain:o||Y,name:n});return{cancel:function(){i.cancel()}}}function Vn(n,e,t){"function"==typeof(e=e||{})&&(t=e,e={});var r,o=new I;return e.errorHandler=function(n){r.cancel(),o.reject(n)},r=Zn(n,e,function(n){if(r.cancel(),o.resolve(n),t)return t(n)}),o.cancel=r.cancel,o}var Xn=function n(e,t,r,o){var i=(o=o||{}).domain||Y,a=o.timeout||-1,c=o.timeout||5e3,u=o.fireAndForget||!1;return I.try(function(){return function(n,e,t){if(!n)throw new Error("Expected name");if(t&&"string"!=typeof t&&!Array.isArray(t)&&!B(t))throw new TypeError("Expected domain to be a string, array, or regex");if(b(e))throw new Error("Target window is closed")}(t,e,i),function(n,e,t,r){var o=r.send;return I.try(function(){return function(n,e){var t=x(e);if(t)return t===n;if(e===n)return!1;if(function(n){if(n){try{if(n.top)return n.top}catch(n){}if(d(n)===n)return n;try{if(y(window,n)&&window.top)return window.top}catch(n){}try{if(y(n,window)&&window.top)return window.top}catch(n){}for(var e=0,t=function n(e){for(var t=[],r=0,o=m(e);r<o.length;r++){var i=o[r];t.push(i);for(var a=0,c=n(i);a<c.length;a++)t.push(c[a])}return t}(n);e<t.length;e++){var r=t[e];try{if(r.top)return r.top}catch(n){}if(d(r)===r)return r}}}(e)===e)return!1;for(var r=0,o=m(n);r<o.length;r++)if(o[r]===e)return!0;return!1}(window,n)?function(n,e,t){void 0===e&&(e=5e3),void 0===t&&(t="Window");var r=cn(n);return-1!==e&&(r=r.timeout(e,new Error(t+" did not load after "+e+"ms"))),r}(n,t):B(e)?un(n,{send:o}):{domain:e}}).then(function(n){return n.domain})}(e,i,c,{send:n})}).then(function(o){if(!k(i,o))throw new Error("Domain "+U(i)+" does not match "+U(o));i=o;var c,s,f=t===K&&r&&"string"==typeof r.name?r.name+"()":t,d=new I,l=t+"_"+L();if(!u){var h={name:t,win:e,domain:i,promise:d};!function(n,e){en("responseListeners").set(n,e)}(l,h);var w=on("requestPromises").getOrSet(e,function(){return[]});w.push(d),d.catch(function(){!function(n){en("erroredResponseListeners").set(n,!0)}(l),Bn(l)});var v=function(n){return on("knownWindows").get(n,!1)}(e)?1e4:2e3,y=a,m=v,g=y,_=(c=function(){return b(e)?d.reject(new Error("Window closed for "+t+" before "+(h.ack?"response":"ack"))):h.cancelled?d.reject(new Error("Response listener was cancelled for "+t)):(m=Math.max(m-500,0),-1!==g&&(g=Math.max(g-500,0)),h.ack||0!==m?0===g?d.reject(new Error("No response for postMessage "+f+" in "+p()+" in "+y+"ms")):void 0:d.reject(new Error("No ack for postMessage "+f+" in "+p()+" in "+v+"ms")))},function n(){s=setTimeout(function(){c(),n()},500)}(),{cancel:function(){clearTimeout(s)}});d.finally(function(){_.cancel(),w.splice(w.indexOf(d,1))}).catch(H)}return Fn(e,i,{type:"postrobot_message_request",hash:l,name:t,data:r,fireAndForget:u},{on:Zn,send:n}),u?d.resolve():d})};function $n(n,e,t){return qn(n,e,t,{on:Zn,send:Xn})}function ne(n,e,t){return Nn(n,e,t,{on:Zn,send:Xn})}function ee(n){return Mn.toProxyWindow(n,{send:Xn})}function te(){var n,e,t,r;$().initialized||($().initialized=!0,e=(n={on:Zn,send:Xn}).on,t=n.send,(r=$()).receiveMessage=r.receiveMessage||function(n){return Yn(n,{on:e,send:t})},function(n){var e=n.on,t=n.send;en().getOrSet("postMessageListener",function(){return(n=window).addEventListener("message",r=function(n){!function(n,e){var t=e.on,r=e.send,o=n.source||n.sourceElement,a=n.origin||n.originalEvent&&n.originalEvent.origin,c=n.data;if("null"===a&&(a=i+"//"),o){if(!a)throw new Error("Post message did not have origin domain");Yn({source:o,origin:a,data:c},{on:t,send:r})}}(n,{on:e,send:t})}),{cancel:function(){n.removeEventListener("message",r)}};var n,r})}({on:Zn,send:Xn}),function(n){var e=n.on,t=n.send;en("builtinListeners").getOrSet("helloListener",function(){var n=e(Q,{domain:Y},function(n){var e=n.source,t=n.origin;return cn(e).resolve({win:e,domain:t}),{instanceID:an()}}),r=x();return r&&un(r,{send:t}).catch(H),n})}({on:Zn,send:Xn}))}function re(){var n;!function(){for(var n=en("responseListeners"),e=0,t=n.keys();e<t.length;e++){var r=t[e],o=n.get(r);o&&(o.cancelled=!0),n.del(r)}}(),(n=en().get("postMessageListener"))&&n.cancel(),delete window.__post_robot_10_0_14__}function oe(n){for(var e=0,t=on("requestPromises").get(n,[]);e<t.length;e++)t[e].reject(new Error("Window cleaned up before response")).catch(H)}t.d(e,"bridge",function(){}),t.d(e,"Promise",function(){return I}),t.d(e,"TYPES",function(){return!0}),t.d(e,"ProxyWindow",function(){return Mn}),t.d(e,"setup",function(){return te}),t.d(e,"destroy",function(){return re}),t.d(e,"serializeMessage",function(){return $n}),t.d(e,"deserializeMessage",function(){return ne}),t.d(e,"toProxyWindow",function(){return ee}),t.d(e,"on",function(){return Zn}),t.d(e,"once",function(){return Vn}),t.d(e,"send",function(){return Xn}),t.d(e,"markWindowKnown",function(){return fn}),t.d(e,"cleanUpWindow",function(){return oe}),te()}])},702:(n,e,t)=>{n.exports=t(684),n.exports.default=n.exports}},e={};function t(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return n[r].call(i.exports,i,i.exports,t),i.exports}t.n=n=>{var e=n&&n.__esModule?()=>n.default:()=>n;return t.d(e,{a:e}),e},t.d=(n,e)=>{for(var r in e)t.o(e,r)&&!t.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:e[r]})},t.o=(n,e)=>Object.prototype.hasOwnProperty.call(n,e),t.r=n=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})};var r={};return(()=>{"use strict";t.r(r),t.d(r,{DiscoveryService:()=>E,PersistenceService:()=>a,ds_response_url:()=>j,json_mdq:()=>m,json_mdq_get:()=>_,json_mdq_get_sp:()=>b,json_mdq_pre_get:()=>g,json_mdq_search:()=>x,parse_qs:()=>k});var n=t(702),e=t.n(n);function o(n){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},o(n)}function i(n){var e=function(n){if("object"!=o(n)||!n)return n;var e=n[Symbol.toPrimitive];if(void 0!==e){var t=e.call(n,"string");if("object"!=o(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(n)}(n);return"symbol"==o(e)?e:e+""}var a=function(){return n=function n(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),this._url=e,this._selector=t.selector,this._frame=window.document.createElement("iframe"),this._frame.id="ps_"+Math.random().toString(36).replace(/[^a-z]+/g,"").substr(2,10),this._frame.src=e,this._init_iframe(this._selector),this.dst=this._frame.contentWindow||this._frame,this.apikey=t.apikey||void 0,delete t.apikey,this.opts=t},(t=[{key:"_init_iframe",value:function(n){void 0!==n?this.show_checkbox(n):this.hide_checkbox(void 0)}},{key:"_detach_checkbox",value:function(n){var e=n||this._selector;if(e){var t=window.document.querySelector(e);if(t)try{this._frame=t.removeChild(this._frame)}catch(n){console.log("Iframe not attached to: ".concat(e))}}}},{key:"hide_checkbox",value:function(n){try{return this._detach_checkbox(n),this._frame.style["content-visibility"]="hidden",this._frame.style.display="none",this._frame.style.position="absolute",this._frame.style.top="-999px",this._frame.style.left="-999px",this._frame.style.height="0px",this._frame.style.width="0px",this._frame.style.border="0px",window.document.body.appendChild(this._frame),this.dst=this._frame.contentWindow||this._frame,!0}catch(n){return console.log("Problem attaching hidden checkbox: ".concat(n)),!1}}},{key:"show_checkbox",value:function(n){try{var t=window.document.body.querySelector(n);return null!==t?(this._detach_checkbox("body"),this._frame.style["content-visibility"]="visible",this._frame.style.display="inline-block",this._frame.style.position="relative",this._frame.style.top="0px",this._frame.style.left="0px",this._frame.style.height="40px",this._frame.style.width="40px",this._frame.style.border="0px",this._frame.style["background-color"]="transparent",t.appendChild(this._frame),this.dst=this._frame.contentWindow||this._frame,e().send(this.dst,"init-checkbox").then(function(n){console.log("Handled init-checkbox message")}).catch(function(n){console.log("Error handling init-checkbox message: ".concat(n))}),!0):(console.log("Selector not found: ".concat(n)),!1)}catch(n){return console.log("Problem attaching checkbox: ".concat(n)),!1}}},{key:"update",value:function(n,t){return e().send(this.dst,"update",{context:n,entity:t,apikey:this.apikey})}},{key:"entities",value:function(n){var t=this;return e().send(t.dst,"entities",{context:n,apikey:t.apikey}).then(function(n){return n}).catch(function(r){return new Promise(function(r,o){var i=window.setTimeout(function(){o("Timeout waiting for initialized message: ".concat(3e4))},3e4);e().on("initialized",{window:t.dst},function(o){window.clearTimeout(i),r(e().send(t.dst,"entities",{context:n,apikey:t.apikey}))})})})}},{key:"remove",value:function(n,t){return e().send(this.dst,"remove",{context:n,entity_id:t,apikey:this.apikey})}},{key:"entity",value:function(n,t){return e().send(this.dst,"entity",{context:n,entity_id:t,apikey:this.apikey})}},{key:"has_storage_access",value:function(n){return e().send(this.dst,"has_storage_access",{context:n,apikey:this.apikey})}}])&&function(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,i(r.key),r)}}(n.prototype,t),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,t}(),c=0;function u(n,e){var t=(65535&n)+(65535&e);return(n>>16)+(e>>16)+(t>>16)<<16|65535&t}function s(n,e){return n<<e|n>>>32-e}function f(n,e,t,r){return n<20?e&t|~e&r:n<40?e^t^r:n<60?e&t|e&r|t&r:e^t^r}function d(n){return n<20?1518500249:n<40?1859775393:n<60?-1894007588:-899497514}function l(n){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},l(n)}function h(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}function w(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?h(Object(t),!0).forEach(function(e){p(n,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):h(Object(t)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(t,e))})}return n}function p(n,e,t){return(e=v(e))in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function v(n){var e=function(n){if("object"!=l(n)||!n)return n;var e=n[Symbol.toPrimitive];if(void 0!==e){var t=e.call(n,"string");if("object"!=l(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(n)}(n);return"symbol"==l(e)?e:e+""}function y(n){return"{sha1}"+function(n){return function(n){for(var e,t=c?"0123456789ABCDEF":"0123456789abcdef",r="",o=0;o<n.length;o++)e=n.charCodeAt(o),r+=t.charAt(e>>>4&15)+t.charAt(15&e);return r}(function(n){return function(n){for(var e="",t=0;t<32*n.length;t+=8)e+=String.fromCharCode(n[t>>5]>>>24-t%32&255);return e}(function(n,e){n[e>>5]|=128<<24-e%32,n[15+(e+64>>9<<4)]=e;for(var t=Array(80),r=1732584193,o=-271733879,i=-1732584194,a=271733878,c=-1009589776,l=0;l<n.length;l+=16){for(var h=r,w=o,p=i,v=a,y=c,m=0;m<80;m++){t[m]=m<16?n[l+m]:s(t[m-3]^t[m-8]^t[m-14]^t[m-16],1);var g=u(u(s(r,5),f(m,o,i,a)),u(u(c,t[m]),d(m)));c=a,a=i,i=s(o,30),o=r,r=g}r=u(r,h),o=u(o,w),i=u(i,p),a=u(a,v),c=u(c,y)}return Array(r,o,i,a,c)}(function(n){for(var e=Array(n.length>>2),t=0;t<e.length;t++)e[t]=0;for(t=0;t<8*n.length;t+=8)e[t>>5]|=(255&n.charCodeAt(t/8))<<24-t%32;return e}(n),8*n.length))}(function(n){for(var e,t,r="",o=-1;++o<n.length;)e=n.charCodeAt(o),t=o+1<n.length?n.charCodeAt(o+1):0,55296<=e&&e<=56319&&56320<=t&&t<=57343&&(e=65536+((1023&e)<<10)+(1023&t),o++),e<=127?r+=String.fromCharCode(e):e<=2047?r+=String.fromCharCode(192|e>>>6&31,128|63&e):e<=65535?r+=String.fromCharCode(224|e>>>12&15,128|e>>>6&63,128|63&e):e<=2097151&&(r+=String.fromCharCode(240|e>>>18&7,128|e>>>12&63,128|e>>>6&63,128|63&e));return r}(n)))}(n)}function m(n){return fetch(n,{method:"GET",headers:{Accept:"application/json"}}).then(function(e){if(404==e.status)throw new URIError("".concat(n,": not found"));return e}).then(function(n){var e=n.headers.get("content-type");if(e&&e.includes("application/json"))return n.json();throw new SyntaxError("MDQ didn't provide a JSON response")})}function g(n,e,t,r){var o=r+n+".json";return t&&e&&(o="".concat(o,"?entityID=").concat(encodeURIComponent(t),"&trustProfile=").concat(e)),m(o).then(function(n){return Array.isArray(n)&&n.length>0&&(n=n[0]),n})}function _(n,e,t,r){return g(n,e,t,r).catch(function(n){console.log(n)})}function b(n,e){return m(e+y(n)+".json").then(function(n){return"[object Array]"===Object.prototype.toString.call(n)&&(n=n[0]),n}).catch(function(n){console.log("ERROR getting SP md:",n)})}function x(n,e,t,r){var o=[];return o.push("q=".concat(n)),t&&r&&(o.push("entityID=".concat(encodeURIComponent(t))),o.push("trustProfile=".concat(r))),m("".concat(e,"?").concat(o.join("&")))}function k(n){var e={};return n.forEach(function(n){var t=n.split("=",2);2==t.length&&(e[t[0]]=decodeURIComponent(t[1].replace(/\+/g," ")))}),e}function j(n,e){e&&e.return||(e=w(w({},e),{},{return:"/"}));var t=e.return;if("/"===t&&(t=window.location.origin?window.location.origin+"/":window.location.protocol+"//"+window.location.host+"/"),void 0===t||!t.startsWith("http://")&&!t.startsWith("https://"))throw new Error("Invalid return query param: ".concat(t));var r=-1===t.indexOf("?")?"?":"&",o=e.returnIDParam,i=n.entity_id;return o||(o="entityID"),i&&(t+=r+o+"="+i),t}var E=function(){return n=function n(e,t,r){var o,i,c,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};!function(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),"string"==typeof r?(o=u.selector,i=u.entityID,c=u.trustProfile):"object"===l(r)?(o=r.selector,i=r.entityID,c=r.trustProfile,r="thiss.io"):r="thiss.io",this.mdq="function"==typeof e?e:function(n){return _(y(n),c,i,e)},this.mdq_sp=function(n){return b(n,e)},this.ps=t instanceof a?t:new a(t,{selector:o}),this.context=r},e=[{key:"with_items",value:function(n){var e=this;this.ps.entities(this.context).then(function(e){return n(e.data)}).then(function(n){n&&n.data&&n.data.forEach(function(n){this.ps.update(e.context,n)})})}},{key:"saml_discovery_response",value:function(n){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.do_saml_discovery_response(n,e).then(function(n){var e=Object.fromEntries(new URLSearchParams(window.location.search));return j(n.entity,e)}).then(function(n){window.top.location.href=n}).catch(function(n){console.log(n)})}},{key:"pin",value:function(n){return this.do_saml_discovery_response(n,!0)}},{key:"do_saml_discovery_response",value:function(n){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],t=this;return t.ps.entity(t.context,n).then(function(n){return n.data}).then(function(r){return void 0===r?t.mdq(n).then(function(n){if(e)return t.ps.update(t.context,n).then(function(n){return n.data});var r=Date.now(),o={entity:n,last_refresh:r,last_use:r};return Promise.resolve(o)}):Promise.resolve(r)}).catch(function(n){return console.log(n)})}},{key:"remove",value:function(n){return this.ps.remove(this.context,n)}}],e&&function(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,v(r.key),r)}}(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,e}()})(),r})(),n.exports=e()}},e={},t=function t(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return n[r].call(i.exports,i,i.exports,t),i.exports}(891);window.thiss=t})();

