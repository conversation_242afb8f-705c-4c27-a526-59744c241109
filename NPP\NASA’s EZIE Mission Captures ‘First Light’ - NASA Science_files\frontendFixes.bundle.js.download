(()=>{var e={450:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});const r=window.edac_frontend_fixes?.lang_and_dir||{enabled:!1},__WEBPACK_DEFAULT_EXPORT__=()=>{if(!r.enabled)return;const e=document.querySelector("html"),t=e.getAttribute("lang"),n=e.getAttribute("dir");t&&t===r.lang||e.setAttribute("lang",r.lang),n&&n===r.dir||e.setAttribute("dir",r.dir)}},606:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});const r=window.edac_frontend_fixes.meta_viewport_scalable||{enabled:!1},__WEBPACK_DEFAULT_EXPORT__=()=>{if(!r.enabled)return;const e=document.querySelector('meta[name="viewport"]');if(e){if(!e.content.match(/user\-scalable\s*=\s*(no|0)/i))return;e.remove()}const t=document.createElement("meta");t.name="viewport",t.content="width=device-width, initial-scale=1",document.head.appendChild(t)}},835:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});var r=n(428);const i=(0,r.__)("opens a new window","accessibility-checker");let o,a;const l=()=>{o=document.createElement("div"),o.setAttribute("role","tooltip"),o.classList.add("anww-tooltip"),Object.assign(o.style,{position:"absolute",background:"white",color:"#1e1e1e",fontSize:"16px",border:"1px solid black",padding:"5px 10px",zIndex:9999,display:"none",pointerEvents:"auto",boxShadow:"0px 4px 6px rgba(0,0,0,0.1)",maxWidth:"200px",whiteSpace:"nowrap"}),document.body.appendChild(o),document.addEventListener("click",(e=>{e.target.closest(".anww-tooltip, a[target='_blank']")||f()})),document.addEventListener("keydown",(e=>{"Escape"===e.key&&f()})),o.addEventListener("mouseenter",(()=>{clearTimeout(a)})),o.addEventListener("mouseleave",(()=>{f()}))},d=()=>{document.querySelectorAll(".edac-nww-external-link-icon").forEach((e=>e.remove())),document.querySelectorAll("a:not([data-nww-processed])").forEach((e=>{const t=e.getAttribute("onclick");if("_blank"===e.getAttribute("target"))return s(e),c(e),u(e),void e.setAttribute("data-nww-processed","true");if(t&&t.includes("window.open")){const n=t.match(/window\.open\([^,]+,\s*['"]([^'"]+)['"]/),r=n?n[1]:"";"_blank"!==r&&""!==r||(s(e),c(e),u(e),e.setAttribute("data-nww-processed","true"))}}))},s=e=>{const t=e.querySelector("h1, h2, h3, h4, h5, h6");if(t)return void t.insertAdjacentHTML("beforeend",'<i class="edac-nww-external-link-icon" aria-hidden="true"></i>');const n=e.querySelector(".elementor-button-content-wrapper");n?n.insertAdjacentHTML("beforeend",'<i class="edac-nww-external-link-icon elementor-button-link-content" aria-hidden="true"></i>'):e.insertAdjacentHTML("beforeend",'<i class="edac-nww-external-link-icon" aria-hidden="true"></i>')},c=e=>{let t="";if(e.hasAttribute("aria-label"))t=e.getAttribute("aria-label");else if(e.querySelector("img")){t=e.querySelector("img").getAttribute("alt")||""}else e.textContent&&(t=e.textContent.trim());t=t?`${t}, ${i}`:i,e.setAttribute("aria-label",t)},u=e=>{e.addEventListener("mouseenter",(t=>{_(e,t.pageX,t.pageY)})),e.addEventListener("focusin",(()=>{const t=e.getBoundingClientRect();_(e,t.left+window.scrollX,t.top+t.height+window.scrollY)})),e.addEventListener("mouseleave",b),e.addEventListener("focusout",b)},_=(e,t,n)=>{clearTimeout(a),o.textContent=i,o.style.display="block";const r=o.offsetWidth,l=o.offsetHeight;t+r+10>window.innerWidth&&(t-=r+20),n+l+10>window.innerHeight+window.scrollY&&(n-=l+20),o.style.top=`${n+10}px`,o.style.left=`${t+10}px`},b=()=>{a=setTimeout(f,300)},f=()=>{o.style.display="none"},__WEBPACK_DEFAULT_EXPORT__=()=>{l(),d(),document.addEventListener("facetwp-loaded",d)}},821:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__=()=>{document.querySelectorAll('a[target="_blank"]:not(.edac-allow-new-tab)').forEach((e=>{e.closest(".edac-allow-new-tab")||(e.removeAttribute("target"),e.classList.add("edac-removed-target-blank"))}))}},885:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});const r=window.edac_frontend_fixes?.remove_title_if_preferred_accessible_name||{enabled:!1},i=e=>{const t=e.getAttribute("alt");t&&""!==t?.trim()||e.setAttribute("alt",e.getAttribute("title")),l(e)},o=e=>{const t=e.innerText;t&&""!==t?.trim()||d(e)||e.setAttribute("aria-label",e.getAttribute("title")),l(e)},a=e=>{if(d(e))return void l(e);const t=e.labels?.[0]?.innerText,n=e.closest("label")?.innerText;t&&""!==t?.trim()||n&&""!==n?.trim()||e.setAttribute("aria-label",e.getAttribute("title")),l(e)},l=e=>{e.classList.add("edac-removed-title"),e.removeAttribute("title")},d=e=>{const t=e.getAttribute("aria-label"),n=e.getAttribute("aria-labelledby");return!!(t&&""!==t?.trim()||n&&""!==n?.trim())},__WEBPACK_DEFAULT_EXPORT__=()=>{if(!r.enabled)return;document.querySelectorAll("img[title], a[title], input[title], textarea[title], select[title], button[title]").forEach((e=>{if(""===e.getAttribute("title")?.trim())return;const t=e.tagName.toLowerCase();"img"===t?i(e):["a","button"].includes(t)?o(e):["input","textarea","select"].includes(t)&&a(e)}))}},695:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});var r=n(428);const i=()=>{const e=(e=>{const t=document.querySelectorAll("body a:not(.ab-item)");for(const n of t)if(!n.closest(e))return n;return null})("#wpadminbar");if(e&&e.href&&-1!==e.href.indexOf("#")){const t=e.href.split("#")[1];return!!document.getElementById(t)}return!1};const __WEBPACK_DEFAULT_EXPORT__=()=>{const e=document.getElementById("skip-link-template");if(!e)return;if(!window.edac_frontend_fixes.skip_link.targets)return;if(i())return;const t=window.edac_frontend_fixes.skip_link.targets.find((e=>document.querySelector(e)));t||console.log((0,r.__)("EDAC: Did not find a matching target ID on the page for the skip link.","accessibility-checker"));const n=e.content.cloneNode(!0);t?(n.querySelector(".edac-skip-link--content").href=t,function(e,t){const n=document.querySelector(e);n&&(-1===n.tabIndex&&n.setAttribute("tabindex","0"),t.querySelector(".edac-skip-link--content")?.addEventListener("click",(()=>{try{history.pushState({},"",e)}catch(e){console.error((0,r.__)("EDAC: Error updating history for skip link.","accessibility-checker"))}n.focus()}),{once:!0}))}(t,n)):n.querySelector(".edac-skip-link--content").remove(),document.body.prepend(n)}},845:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});const r=window.edac_frontend_fixes?.tabindex||{enabled:!1},__WEBPACK_DEFAULT_EXPORT__=()=>{if(!r.enabled)return;document.querySelectorAll("[tabindex]").forEach((e=>{"A"!==e.tagName||e.hasAttribute("href")&&"button"!==e.getAttribute("role")?"-1"!==e.getAttribute("tabindex")&&e.getAttribute("tabindex")>0&&(e.setAttribute("tabindex","0"),e.classList.add("edac-focusable-modified")):e.setAttribute("tabindex","0")}));document.querySelectorAll('div[role="button"]:not([tabindex]), a[role="button"]:not([tabindex]):not([href])').forEach((e=>{e.hasAttribute("tabindex")&&"-1"===e.getAttribute("tabindex")||(e.setAttribute("tabindex","0"),e.classList.add("edac-focusable"))}))}},818:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});const r=window.edac_frontend_fixes?.underline||{enabled:!1},__WEBPACK_DEFAULT_EXPORT__=()=>{if(!r.enabled)return;document.querySelectorAll(r.target).forEach((function(e){if(e.closest("nav")||e.closest('[role="navigation"]'))return;e.style.textDecoration="underline",e.setAttribute("data-original-outline",e.style.outlineWidth),e.setAttribute("data-original-offset",e.style.outlineOffset),e.setAttribute("data-original-color",e.style.outlineColor);const t=e.style.color;e.addEventListener("mouseenter",(function(){e.style.textDecoration="none"})),e.addEventListener("mouseleave",(function(){e.style.textDecoration="underline"})),e.addEventListener("focusin",(function(){let n="2px";"2px"===e.style.outlineWidth&&(n="4px"),e.style.outlineWidth=n,e.style.outlineColor=t,e.style.outlineOffset="2px"})),e.addEventListener("focusout",(function(){e.style.outlineWidth=e.getAttribute("data-original-outline"),e.style.outlineColor=e.getAttribute("data-original-color"),e.style.outlineOffset=e.getAttribute("data-original-offset")}))}))}},428:e=>{"use strict";e.exports=wp.i18n}},t={};function __webpack_require__(n){var r=t[n];if(void 0!==r)return r.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,__webpack_require__),i.exports}__webpack_require__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=(e,t)=>{for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};const n=window.edac_frontend_fixes||{};n?.skip_link?.enabled&&Promise.resolve().then(__webpack_require__.bind(__webpack_require__,695)).then((e=>{e.default()})),n?.lang_and_dir?.enabled&&Promise.resolve().then(__webpack_require__.bind(__webpack_require__,450)).then((e=>{e.default()})),n?.tabindex?.enabled&&Promise.resolve().then(__webpack_require__.bind(__webpack_require__,845)).then((e=>{e.default()})),n?.meta_viewport_scalable?.enabled&&Promise.resolve().then(__webpack_require__.bind(__webpack_require__,606)).then((e=>{e.default()})),n?.remove_title_if_preferred_accessible_name?.enabled&&Promise.resolve().then(__webpack_require__.bind(__webpack_require__,885)).then((e=>{e.default()})),n?.underline?.enabled&&Promise.resolve().then(__webpack_require__.bind(__webpack_require__,818)).then((e=>{e.default()})),n?.meta_viewport_scalable?.enabled&&Promise.resolve().then(__webpack_require__.bind(__webpack_require__,606)).then((e=>{e.default()})),n?.prevent_links_opening_new_windows?.enabled&&Promise.resolve().then(__webpack_require__.bind(__webpack_require__,821)).then((e=>{e.default()})),n?.new_window_warning?.enabled&&Promise.resolve().then(__webpack_require__.bind(__webpack_require__,835)).then((e=>{e.default()}))})();