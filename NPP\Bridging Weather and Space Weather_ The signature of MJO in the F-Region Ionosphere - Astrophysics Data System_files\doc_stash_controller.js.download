define(["backbone","js/components/generic_module","js/mixins/hardened","js/mixins/dependon"],function(e,s,t,n){s=s.extend({_docs:[],activate:function(e){this.setBeeHive(e.getHardenedInstance());e=this.getBeeHive().getService("PubSub");e.subscribe(e.START_SEARCH,_.bind(this.emptyStash,this))},stashDocs:function(e){this._docs.push.apply(this._docs,e)},getDocs:function(){return _.cloneDeep(this._docs)},emptyStash:function(){this._docs=[]},hardenedInterface:{stashDocs:"stash docs",getDocs:"getDocs"}});return _.extend(s.prototype,t,n.BeeHive),s});
//# sourceMappingURL=doc_stash_controller.js.map