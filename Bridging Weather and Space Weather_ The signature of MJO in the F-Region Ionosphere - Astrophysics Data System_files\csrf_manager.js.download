define(["backbone","js/components/generic_module","js/mixins/hardened","js/components/api_request","js/components/api_targets","js/mixins/dependon"],function(e,t,i,s,r,n){t=t.extend({activate:function(e){this.setBeeHive(e);e=this.getPubSub();_.bindAll(this,["resolvePromiseWithNewKey"]),e.subscribe(e.DELIVERING_RESPONSE,this.resolvePromiseWithNewKey)},getCSRF:function(){this.deferred=$.Deferred();var e=new s({target:r.CSRF}),t=this.getPubSub();return t.publish(t.EXECUTE_REQUEST,e),this.deferred.promise()},resolvePromiseWithNewKey:function(e){e=e.toJSON().csrf;this.deferred.resolve(e)},hardenedInterface:{getCSRF:"getCSRF"}});return _.extend(t.prototype,i,n.BeeHive),t});
//# sourceMappingURL=csrf_manager.js.map