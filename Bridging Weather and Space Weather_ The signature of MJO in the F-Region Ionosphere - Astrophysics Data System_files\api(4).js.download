define(["underscore","../modules/api","../modules/ui"],function(u,t,n){function c(t,e){return u.map(t,function(t){var n=(n=>{try{return 0<=n.indexOf("http")?decodeURIComponent(n):decodeURIComponent(n.slice(n.indexOf(":")+1)).replace(/^\//,"#")}catch(t){return n}})(t.url);return{rawUrl:t.url,url:n,circular:-1<n.indexOf(e,n.indexOf(":")),external:-1<n.indexOf("http"),name:t.title,id:u.uniqueId()}})}var t=t.actions,a=t.RECEIVED_RESPONSE,r=t.SEND_ANALYTICS,o=t.FALLBACK_ON_ERROR,t=n.actions,l=t.SET_LOADING,d=t.SET_HAS_ERROR,s=t.SET_ITEMS;return function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(n){return e.map(function(t){return u.partial(t,n)})}}(function(t,n){var r=n.dispatch,i=n.getState;return function(e){return function(t){var n;e(t),t.type===a&&(t=t.result,n=i().api.bibcode,u.isPlainObject(t)&&(t=t.links&&t.links.records,u.isArray(t))&&0<t.length?(r({type:l,result:!1}),r({type:s,result:c(t,n)})):r({type:d,result:"did not receive docs"}))}}},function(e,t){t.dispatch;return function(n){return function(t){n(t),t.type===r&&e.emitAnalytics(t.result)}}},function(t,n){var r=n.dispatch,i=n.getState;return function(e){return function(t){var n;e(t),t.type===o&&(t=i().api.bibcode,n=i().ui.items,u.isArray(n))&&0<n.length&&r({type:s,result:c(n.map(function(t){return{url:t.rawUrl,title:t.name}}),t)})}}})});
//# sourceMappingURL=api.js.map