#crossmark-widget {
  display: none;
}
#crossmark-widget .crossmark-reset {
  display: block;
  float: none;
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
}
#crossmark-widget .crossmark-overlay {
  position: fixed;
  top: 0;
  bottom: -60px;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100000;
  overflow: hidden;
}
#crossmark-widget .crossmark-popup {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100001;
}
#crossmark-widget .crossmark-popup__offset {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
}
#crossmark-widget .crossmark-popup__offset.is-ios {
  position: absolute;
}
@media (max-width: 479px) {
  #crossmark-widget .crossmark-popup__offset {
    top: 50%;
    left: 15px;
    right: 15px;
    width: auto;
    height: auto;
  }
}
@media (min-width: 992px) and (max-height: 710px) {
  #crossmark-widget .crossmark-popup__offset {
    top: 15px;
    bottom: 15px;
    width: auto;
    height: auto;
  }
}
@media (min-width: 768px) and (max-height: 650px) {
  #crossmark-widget .crossmark-popup__offset {
    top: 15px;
    bottom: 15px;
    width: auto;
    height: auto;
  }
}
@media (max-height: 590px) {
  #crossmark-widget .crossmark-popup__offset {
    top: 15px;
    bottom: 15px;
    width: auto;
    height: auto;
  }
}
#crossmark-widget .crossmark-popup__inner {
  position: relative;
  background: white;
  height: 680px;
  width: 768px;
  margin-top: -340px;
  margin-left: -384px;
  border-radius: 3px;
  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.25);
}
@media (max-width: 991px) {
  #crossmark-widget .crossmark-popup__inner {
    height: 620px;
    width: 560px;
    margin-top: -310px;
    margin-left: -280px;
  }
}
@media (max-width: 767px) {
  #crossmark-widget .crossmark-popup__inner {
    height: 560px;
    width: 440px;
    margin-top: -280px;
    margin-left: -220px;
  }
}
@media (max-width: 479px) {
  #crossmark-widget .crossmark-popup__inner {
    width: auto;
    margin-left: 0;
  }
}
@media (min-width: 992px) and (max-height: 710px) {
  #crossmark-widget .crossmark-popup__inner {
    margin-top: 0;
    height: 100%;
  }
}
@media (min-width: 768px) and (max-height: 650px) {
  #crossmark-widget .crossmark-popup__inner {
    margin-top: 0;
    height: 100%;
  }
}
@media (max-height: 590px) {
  #crossmark-widget .crossmark-popup__inner {
    margin-top: 0;
    height: 100%;
  }
}
#crossmark-widget .crossmark-popup__header {
  position: relative;
  z-index: 1;
  height: 53px;
}
#crossmark-widget .crossmark-popup__logo {
  float: left;
  height: 100%;
  padding: 8px;
}
#crossmark-widget .crossmark-popup__btn-close {
  float: right;
  height: 29px;
  width: 29px;
  margin: 12px;
  background: url('images/cross-black.svg') no-repeat;
  cursor: pointer;
}
#crossmark-widget .crossmark-popup__content-wrapper {
  position: absolute;
  top: 53px;
  bottom: 0;
  left: 0;
  right: 0;
}
#crossmark-widget .crossmark-popup__content {
  width: 100%;
  height: 100%;
}
