function _toConsumableArray(r){return _arrayWithoutHoles(r)||_iterableToArray(r)||_unsupportedIterableToArray(r)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(r,e){var a;if(r)return"string"==typeof r?_arrayLikeToArray(r,e):"Map"===(a="Object"===(a={}.toString.call(r).slice(8,-1))&&r.constructor?r.constructor.name:a)||"Set"===a?Array.from(r):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(r,e):void 0}function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}function _arrayWithoutHoles(r){if(Array.isArray(r))return _arrayLikeToArray(r)}function _arrayLikeToArray(r,e){(null==e||e>r.length)&&(e=r.length);for(var a=0,t=Array(e);a<e;a++)t[a]=r[a];return t}define(["redux","./modules/api","./modules/ui","./middleware/api","./middleware/ui"],function(t,o,n,i,u){var l=t.createStore,y=t.applyMiddleware,d=t.combineReducers;return function(r){var r=y.apply(t,[].concat(_toConsumableArray(u),_toConsumableArray(i(r)))),e=window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__||t.compose,a=d({api:o.reducer,ui:n.reducer});return l(a,e(r))}});
//# sourceMappingURL=configure-store.js.map