(window.webpackJsonp=window.webpackJsonp||[]).push([[58],{191:function(e,t){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector)},192:function(e,t,n){"use strict";n(191);Element.prototype.closest||(Element.prototype.closest=function(e){var t=this;if(!document.documentElement.contains(t))return null;do{if(t.matches(e))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return null})},196:function(e,t){var n;n={sections:null,holder:$(".coolBar--sections .coolBar__drop"),vPort:"screen-sm",isMobile:!1,coolbarElement:null,headerHeight:0,isStickyBar:!1,lock:!0,init:function init(){n.coolbarElement=$(".coolBar"),n.isStickyBar=n.coolbarElement.hasClass("stickybar"),n.get.sections(),n.fill.sections(),n.control(),n.addtionalControls()},control:function control(){$(".journal-home").length&&(n.vPort="screen-md"),$(document).on(n.vPort+"-on",(function(){$(".coolBar").addClass("coolBar--res"),n.isMobile=!0,$(".stickybar.coolBar--res,.coolBar.coolBar--res").length>0&&$(".content>article.container").addClass("article--res")})),$(document).on(n.vPort+"-off",(function(){$(".coolBar").removeClass("coolBar--res"),$(".content article.container").removeClass("article--res"),$(document).trigger("eventSetContentPadding"),n.isMobile=!1,void 0!==UX.controller&&UX.controller.check()}))},addtionalControls:function addtionalControls(){},get:{sections:function sections(){n.sections=$(".article__content .section__title")}},fill:{sections:function sections(){if($(".coolBar--sections").length&&n.sections.length>1){$(".coolBar--sections").removeClass("hidden");var e=$('<ul class="rlist w-slide--list"/>').appendTo(n.holder);n.sections.each((function(){var t=$(this),n=$("<li/>").attr("role","menuitem").appendTo(e),o=$('<a class="w-slide__hide"/>').attr("href","#"+t.attr("id")).appendTo(n);$("<span/>").append(t.html()).appendTo(o)}))}else $(".coolBar--sections").remove();$(".authorArticleInfoCon").length&&$(".coolBar--sections .coolBar__drop").append('<li role="menuitem"><a class="w-slide__hide" href="#_ac_authorArticleInfoCon" target="_self"><span>Author and Article Information</span></a></li>')}}},UX.coolbar=n},203:function(e,t){var n;n={$loa:null,vPort:"screen-xs",init:function init(){var e=$(".to-section");e.length?e.each((function(){var e=$(this).text(),t=$(this).attr("id");$('<li><a class="w-slide__hide" href="#'+t+'"><span>'+e+"</span></a></li>").appendTo(".sections__drop")})):$(".toc-go-section, .toc__section").remove(),n.$loa=$(".issue-item .loa, .search__item .meta__authors, .search__item .loa"),n.control(),n.additionalControl()},control:function control(){$(window).on("load orientationchange",(function(e){n.truncate()})),$(document).on(n.vPort+"-on",(function(){n.isMobile=!0,n.truncate()})),$(document).on(n.vPort+"-off",(function(){n.isMobile=!1,n.truncate()}))},additionalControl:function additionalControl(){},truncate:function truncate(){try{n.$loa.not("[data-truncate='none']").truncate({lines:2,type:"list",seeMoreLink:!0,seeMoreText:"See all authors",seeLessText:"See fewer authors"})}catch(e){console.error(e)}}},UX.tocJs=n},204:function(e,t,n){var o=n(93);e.exports=function _defineProperty(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},212:function(e,t,n){"use strict";var o=n(35),i=n(26),a=n(8),r=n.n(a),s=n(9),l=n.n(s),c=!1;if("undefined"!=typeof window&&window.getComputedStyle){var d=document.createElement("div");["","-webkit-","-moz-","-ms-"].some((function(e){try{d.style.position=e+"sticky"}catch(e){}return""!=d.style.position}))&&(c=!0)}else c=!0;var h=!1,u="undefined"!=typeof ShadowRoot,f={top:null,left:null},p=[];function extend(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])}function parseNumeric(e){return parseFloat(e)||0}function getDocOffsetTop(e){for(var t=0;e;)t+=e.offsetTop,e=e.offsetParent;return t}var m=function(){return l()((function Sticky(e){if(r()(this,Sticky),!(e instanceof HTMLElement))throw new Error("First argument must be HTMLElement");if(p.some((function(t){return t._node===e})))throw new Error("Stickyfill is already applied to this node");this._node=e,this._stickyMode=null,this._active=!1,p.push(this),this.refresh()}),[{key:"refresh",value:function refresh(){if(!c&&!this._removed){this._active&&this._deactivate();var e=this._node,t=getComputedStyle(e),n={position:t.position,top:t.top,display:t.display,marginTop:t.marginTop,marginBottom:t.marginBottom,marginLeft:t.marginLeft,marginRight:t.marginRight,cssFloat:t.cssFloat};if(!isNaN(parseFloat(n.top))&&"table-cell"!=n.display&&"none"!=n.display){this._active=!0;var o=e.style.position;"sticky"!=t.position&&"-webkit-sticky"!=t.position||(e.style.position="static");var i=e.parentNode,a=u&&i instanceof ShadowRoot?i.host:i,r=e.getBoundingClientRect(),s=a.getBoundingClientRect(),l=getComputedStyle(a);this._parent={node:a,styles:{position:a.style.position},offsetHeight:a.offsetHeight},this._offsetToWindow={left:r.left,right:document.documentElement.clientWidth-r.right},this._offsetToParent={top:r.top-s.top-parseNumeric(l.borderTopWidth),left:r.left-s.left-parseNumeric(l.borderLeftWidth),right:-r.right+s.right-parseNumeric(l.borderRightWidth)},this._styles={position:o,top:e.style.top,bottom:e.style.bottom,left:e.style.left,right:e.style.right,width:e.style.width,marginTop:e.style.marginTop,marginLeft:e.style.marginLeft,marginRight:e.style.marginRight};var d=parseNumeric(n.top);this._limits={start:r.top+window.pageYOffset-d,end:s.top+window.pageYOffset+a.offsetHeight-parseNumeric(l.borderBottomWidth)-e.offsetHeight-d-parseNumeric(n.marginBottom)};var h=l.position;"absolute"!=h&&"relative"!=h&&(a.style.position="relative"),this._recalcPosition();var f=this._clone={};f.node=document.createElement("div"),extend(f.node.style,{width:r.right-r.left+"px",height:r.bottom-r.top+"px",marginTop:n.marginTop,marginBottom:n.marginBottom,marginLeft:n.marginLeft,marginRight:n.marginRight,cssFloat:n.cssFloat,padding:0,border:0,borderSpacing:0,fontSize:"1em",position:"static"}),i.insertBefore(f.node,e),f.docOffsetTop=getDocOffsetTop(f.node)}}}},{key:"_recalcPosition",value:function _recalcPosition(){if(this._active&&!this._removed){var e=f.top<=this._limits.start?"start":f.top>=this._limits.end?"end":"middle";if(this._stickyMode!=e){switch(e){case"start":extend(this._node.style,{position:"absolute",left:this._offsetToParent.left+"px",right:this._offsetToParent.right+"px",top:this._offsetToParent.top+"px",bottom:"auto",width:"auto",marginLeft:0,marginRight:0,marginTop:0});break;case"middle":extend(this._node.style,{position:"fixed",left:this._offsetToWindow.left+"px",right:this._offsetToWindow.right+"px",top:this._styles.top,bottom:"auto",width:"auto",marginLeft:0,marginRight:0,marginTop:0});break;case"end":extend(this._node.style,{position:"absolute",left:this._offsetToParent.left+"px",right:this._offsetToParent.right+"px",top:"auto",bottom:0,width:"auto",marginLeft:0,marginRight:0})}this._stickyMode=e}}}},{key:"_fastCheck",value:function _fastCheck(){this._active&&!this._removed&&(Math.abs(getDocOffsetTop(this._clone.node)-this._clone.docOffsetTop)>1||Math.abs(this._parent.node.offsetHeight-this._parent.offsetHeight)>1)&&this.refresh()}},{key:"_deactivate",value:function _deactivate(){var e=this;this._active&&!this._removed&&(this._clone.node.parentNode.removeChild(this._clone.node),delete this._clone,extend(this._node.style,this._styles),delete this._styles,p.some((function(t){return t!==e&&t._parent&&t._parent.node===e._parent.node}))||extend(this._parent.node.style,this._parent.styles),delete this._parent,this._stickyMode=null,this._active=!1,delete this._offsetToWindow,delete this._offsetToParent,delete this._limits)}},{key:"remove",value:function remove(){var e=this;this._deactivate(),p.some((function(t,n){if(t._node===e._node)return p.splice(n,1),!0})),this._removed=!0}}])}(),g={stickies:p,Sticky:m,forceSticky:function forceSticky(){c=!1,init(),this.refreshAll()},addOne:function addOne(e){if(!(e instanceof HTMLElement)){if(!e.length||!e[0])return;e=e[0]}for(var t=0;t<p.length;t++)if(p[t]._node===e)return p[t];return new m(e)},add:function add(e){if(e instanceof HTMLElement&&(e=[e]),e.length){for(var t=[],n=function _loop(){var n=e[o];return n instanceof HTMLElement?p.some((function(e){if(e._node===n)return t.push(e),!0}))?0:void t.push(new m(n)):(t.push(void 0),0)},o=0;o<e.length;o++)n();return t}},refreshAll:function refreshAll(){p.forEach((function(e){return e.refresh()}))},removeOne:function removeOne(e){if(!(e instanceof HTMLElement)){if(!e.length||!e[0])return;e=e[0]}p.some((function(t){if(t._node===e)return t.remove(),!0}))},remove:function remove(e){if(e instanceof HTMLElement&&(e=[e]),e.length)for(var t=function _loop2(){var t=e[n];p.some((function(e){if(e._node===t)return e.remove(),!0}))},n=0;n<e.length;n++)t()},removeAll:function removeAll(){for(;p.length;)p[0].remove()}};function init(){var e,t,n;h||(h=!0,checkScroll(),window.addEventListener("scroll",checkScroll),window.addEventListener("resize",g.refreshAll),window.addEventListener("orientationchange",g.refreshAll),"hidden"in document?(t="hidden",n="visibilitychange"):"webkitHidden"in document&&(t="webkitHidden",n="webkitvisibilitychange"),n?(document[t]||startFastCheckTimer(),document.addEventListener(n,(function(){document[t]?function stopFastCheckTimer(){clearInterval(e)}():startFastCheckTimer()}))):startFastCheckTimer());function checkScroll(){window.pageXOffset!=f.left?(f.top=window.pageYOffset,f.left=window.pageXOffset,g.refreshAll()):window.pageYOffset!=f.top&&(f.top=window.pageYOffset,f.left=window.pageXOffset,p.forEach((function(e){return e._recalcPosition()})))}function startFastCheckTimer(){e=setInterval((function(){p.forEach((function(e){return e._fastCheck()}))}),500)}}c||init();var v=g;n(84),n(191),n(192);!function(e){e.stickyElements={selectors:{header:".header",scrollThenFix:".scrollThenFix",stickyElements:[".fixed-element"],dynamicHeader:[".pb-ad",".pb-las",".cookiePolicy-popup",".header__dropzone"],stickoParent:".sticko__parent"},header:null,isHeaderSticky:!0,dynamicHeaderHeight:!1,vPort:"screen-sm",isMobile:!1,init:function init(){this.header=document.querySelector(this.selectors.header),this.isHeaderSticky&&this.selectors.stickyElements.unshift(this.selectors.header),this._isDynamicHeaderHeight(),this.start(),this.responsive(),this.extra()},responsive:function responsive(){var e=this;$(document).on("smartResize",(function(){return e.start()})),$(document).on("".concat(this.vPort,"-on"),(function(){return e.isMobile=!0})),$(document).on("".concat(this.vPort,"-off"),(function(){return e.isMobile=!1}))},extra:function extra(){},start:function start(){var e=this;if(this.computeHeaderHeight(),this.selectors.stickyElements.length){var t=document.querySelectorAll(this.selectors.stickyElements.join(","));if(!t||!t.length)return;t.forEach((function(t){var n=t.matches(e.selectors.header);e.makeSticky(t,n)}))}},makeSticky:function makeSticky(e,t){this.setTopValue(e,t),this.postStickyActions(e,t),o.a&&v.addOne(e)},setTopValue:function setTopValue(e,t){this._canSetTopValue(e,t)&&(e.style.top=this.headerHeight+"px")},postStickyActions:function postStickyActions(e,t){this._has(e,this.selectors.stickoParent,!0)&&this.stickoActions(e),this._has(e,this.selectors.scrollThenFix)&&this.scrollThenFixActions(e,t)},stickoActions:function stickoActions(e){var t=e.querySelector(this.selectors.stickoParent)||e;this.isHeaderSticky?t.style.height="calc(100vh - ".concat(this.headerHeight,"px)"):t.style.height="100vh",document.addEventListener("change:auto-hide-bar",(function(){e.style.top="".concat(Object(i.a)(),"px")}))},scrollThenFixActions:function scrollThenFixActions(e,t){e.classList.add("fixed-element");var n=e.querySelector(this.selectors.scrollThenFix).offsetTop;n-=t?0:this.headerHeight,e.style.top="".concat(-1*n,"px")},_isDynamicHeaderHeight:function _isDynamicHeaderHeight(){if(!this.dynamicHeaderHeight)for(var e=0;e<this.selectors.dynamicHeader.length&&(this.dynamicHeaderHeight=this._has(this.header,this.selectors.dynamicHeader[e],!0),!this.dynamicHeaderHeight);e++);},computeHeaderHeight:function computeHeaderHeight(){this.headerHeight=Object(i.a)(this.selectors.header)},_has:function _has(e,t,n){return e&&(null!==e.querySelector(t)||n&&e.matches(t))},_canSetTopValue:function _canSetTopValue(e,t){return!t&&this.dynamicHeaderHeight&&null===e.closest(this.selectors.header)}}}(UX)},213:function(e,t,n){"use strict";var o,i,a=n(26);o=!1,i={$parent:$("body"),$elements:"",$element:"",$child:"",pOffset:"",pWidth:"",eTop:"auto",startPoint:"",newHeight:0,marginTopVal:0,marginBottomVal:0,vPort:"screen-sm",originalHeight:"",forceDisableMobileSticky:!1,stopOn:"never",scrollHandler:null,stickoOnLargeHight:!1,init:function init(){i.control(),i.launch(),i.ctrl.check()},launch:function launch(){i.$elements=$(".sticko, .sticko__md"),o&&i.$element.length?i.$elements.each((function(){$(this).attr("style",""),i.forceDisableMobileSticky&&$(this).find(".sticko__child").attr("style",""),$(this).hasClass("sticko__md")&&$(this).removeClass("js--sticko")})):i.$elements.each((function(){i.$element=$(this),i.$element.hasClass("sticko__md")&&o||(i.$element.closest(".sticko__parent").length?i.$parent=i.$element.closest(".sticko__parent"):i.$parent=$("body"),i.$element.find(".sticko__child").length&&(i.$child=i.$element.find(".sticko__child")),i.destroyOnLargeHight())||(i.$element.addClass("js--sticko"),i.$element.is("[data-stop-on]")?(i.stopOn=i.$element.data("stop-on"),0===$(i.stopOn).length&&(i.stopOn="never")):i.stopOn="never",i.define(),i.$child.length&&i.get.height(),i.ads(),i.$element.length&&i.$child.length&&(i.originalHeight=i.newHeight,i.set.height(0,i.originalHeight)),i.additionalLaunch())}))},control:function control(){$(document).on(i.vPort+"-on",(function(){o=!0})),$(document).on(i.vPort+"-off",(function(){o=!1})),$(document).on("smartResize",(function(){var e=i.get.fixedpageElementsHeight();$(window).scrollTop()+e>=$(".dynamic-sticko").offset().top&&i.$elements.each((function(){if(i.$element=$(this),i.$parent=i.$element.closest(".sticko__parent")||$("body"),i.$child=i.$element.find(".sticko__child"),i.pWidth=i.$parent.width(),!i.destroyOnLargeHight()){var t={width:i.pWidth,top:e+10};i.apply(i.$element,t)}}))}))},additionalLaunch:function additionalLaunch(){},define:function define(){i.pOffset=i.$parent.offset(),i.pLeft=i.pOffset.left+parseInt(i.$parent.css("padding-left")),i.pWidth=i.$parent.width();var e={width:i.pWidth,top:i.$element.css("top")};i.apply(i.$element,e)},apply:function apply(e,t){e.css(t)},ads:function ads(){},get:{height:function height(){i.pOffset=i.$parent.offset(),i.pWidth=i.$parent.width();var e=0;i.$element.children(":not(.sticko__child)").each((function(){e+=$(this).outerHeight()})),i.newHeight=$(window).outerHeight()-(e+i.pOffset.top),$("body").outerHeight()<=$(window).outerHeight()&&(i.newHeight=i.newHeight-$(".page-footer").outerHeight());var t={width:i.pWidth,height:i.newHeight,"overflow-y":"auto"};i.apply(i.$child,t)},fixedpageElementsHeight:function fixedpageElementsHeight(){var e=Object(a.a)();return $(".fixed-element").not("article .fixed-element").each((function(t){e+=$(this).outerHeight()})),e}},set:{height:function height(e,t){var n;i.$element.is("[data-stop-on]")?(i.stopOn=i.$element.data("stop-on"),0===$(i.stopOn).length&&(i.stopOn="never")):i.stopOn="never";var o=window.pageYOffset+$(window).outerHeight(),a=i.set.variables(),r="never"===i.stopOn?$(document).outerHeight()-$("footer").offset().top:$(document).outerHeight()-$(i.stopOn).offset().top,s=r===$(document).outerHeight()?$(document).outerHeight():$(document).outerHeight()-r-a;i.pWidth=i.$parent.width();var l={width:i.pWidth};o>=s?(e=o-s,i.newHeight=t-e-i.marginTopVal-i.marginBottomVal,l.height=i.newHeight):l.height=t-i.marginTopVal-i.marginBottomVal,i.apply(i.$child,l);var c=i.get.fixedpageElementsHeight();$(".article-row-left").outerHeight()<$(window).outerHeight()&&(UX.accordion.isStickyBody||$(".article-row-left").height($(window).outerHeight()-c));var d=0;if($("footer").length&&(d=$("footer").height()),!UX.accordion.isStickyBody&&$(".sticko__side-content").outerHeight()<$(window).outerHeight()-c-d&&$(".sticko__side-content").height($(window).outerHeight()-c-d),i.$element.hasClass("dynamic-sticko")){var h=i.$parent,u=h.offset().top-$(window).scrollTop(),f=$(window).scrollTop()+c,p=$(window).outerHeight()-c;h.css({position:"static",top:u}),h.find(".tab__nav").length&&(p-=h.find(".tab__nav").outerHeight()),n=o>=s?p-(e=o-s)-i.marginTopVal-i.marginBottomVal:p-i.marginTopVal-i.marginBottomVal,"never"!==i.stopOn&&(n="auto"),h.css("height",n),h.find(".sticko__child").css("height",n);var m=f+h.outerHeight()+i.marginTopVal;if(f+i.marginTopVal>=h.offset().top){var g=c;"never"!==i.stopOn&&m>=s&&(g-=m-s),h.css({position:"sticky",top:g+i.marginTopVal})}}},variables:function variables(){return 0}},ctrl:{check:function check(){$(window).on("resized",(function(){i.ctrl.resize()}))},resize:function resize(){i.launch()},scrolla:function scrolla(){i.scrollHandler||(i.scrollHandler=$(window).scroll((function(){o&&i.forceDisableMobileSticky||i.$elements.each((function(){i.$element=$(this),i.$parent=i.$element.closest(".sticko__parent")||$("body"),i.$child=i.$element.find(".sticko__child"),i.set.height(0,i.originalHeight)}))})))}},destroyOnLargeHight:function destroyOnLargeHight(){var e=i.get.fixedpageElementsHeight(),t=0;return UX.sticko.$child.children().each((function(){t+=$(this).outerHeight()})),i.$parent.hasClass("sticko-on-largeHeight")&&$(window).outerHeight()-e<t&&(i.$parent.attr("style",""),i.$element.removeClass("js--sticko").attr("style",""),i.$child.attr("style","")),i.$parent.hasClass("sticko-on-largeHeight")&&$(window).outerHeight()-e<t}},UX.sticko=i},225:function(e,t){UX.noFocusableSVG={init:function init(){if(UX.test.isIE)for(var e=document.getElementsByTagName("svg"),t=e.length;t--;)e[t].setAttribute("focusable",!1)}}},226:function(e,t){var n;n={getItem:function getItem(e){return e&&decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null},setItem:function setItem(e,t,n,o,i,a){if(!e||/^(?:expires|max\-age|path|domain|secure)$/i.test(e))return!1;var r="";if(n)switch(n.constructor){case Number:r=n===1/0?"; expires=Fri, 31 Dec 9999 23:59:59 GMT":"; max-age="+n;break;case String:r="; expires="+n;break;case Date:r="; expires="+n.toUTCString()}return document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+r+(i?"; domain="+i:"")+(o?"; path="+o:"")+(a?"; secure":""),!0},removeItem:function removeItem(e,t,n){return!!this.hasItem(e)&&(document.cookie=encodeURIComponent(e)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT"+(n?"; domain="+n:"")+(t?"; path="+t:""),!0)},hasItem:function hasItem(e){return!!e&&new RegExp("(?:^|;\\s*)"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(document.cookie)},keys:function keys(){for(var e=document.cookie.replace(/((?:^|\s*;)[^\=]+)(?=;|$)|^\s*|\s*(?:\=[^;]*)?(?:\1|$)/g,"").split(/\s*(?:\=[^;]*)?;\s*/),t=e.length,n=0;n<t;n++)e[n]=decodeURIComponent(e[n]);return e}},UX.cookies=n},227:function(e,t){var n,o,i,a;n=$(window),o=$("body"),i=$(document),a={init:function init(){a.control()},control:function control(){o.on("click",".header__dropzone-bookmark",(function(){a.add_bookmark()}))},add_bookmark:function add_bookmark(){if(n.sidebar&&n.sidebar.addPanel)n.sidebar.addPanel(i.title,n.location.href,"");else if(n.external&&"AddFavorite"in n.external)n.external.AddFavorite(location.href,i.title);else{if(n.opera&&n.print)return this.title=i.title,!0;alert("Press "+(-1!==navigator.userAgent.toLowerCase().indexOf("mac")?"Command/Cmd":"CTRL")+" + D to bookmark this page.")}}},UX.bookmark=a},229:function(e,t){var n,o,i;n=$(window),o=$("body"),i={$controllers:$("[data-db-target-for]"),$targets:null,$controller:null,$target:null,$parents:null,$parent:null,escape:27,$container:$(".container"),switchVal:null,$section:$(".dropBlock__holder"),vPort:"screen-sm",isMobile:!1,initialized:!1,direction:"left",pop:"down",switchClass:null,init:function init(){i.control()},control:function control(){i.initialized||(i.initialized=!0,$(document).on("click","[data-db-target-for]",(function(e){e.preventDefault(),e.stopPropagation();var t=$(this).hasClass("js--open"),n=$(this).is("[data-slide-target]"),o=$(this).data("db-sm-disable")||!1;if(i.$controller=$(this),i.find.target(),t)i.on.hide();else{if(i.isMobile&&(!1!==n||o))return;i.on.show(e)}})),$(document).on("click",".coolBar:not(.loi__banner) [data-db-target-of] a:not([data-slide-target])",(function(e){i.on.hide()})),n.on("resize",(function(){null!=i.$parent&&i.find.dimensions()})),$(document).on(i.vPort+"-on",(function(){i.isMobile=!0})),$(document).on(i.vPort+"-off",(function(){i.isMobile=!1})))},on:{show:function show(e){i.$controller.closest(".dropBlock__holder").length||void 0!==UX.controller&&UX.controller.check(e),i.$target&&(i.$controller.trigger("dropBlock:open",[i]),i.$target.addClass("js--open"),i.$controller.addClass("js--open"),o.attr("data-active","dropBlock"),i.$target.closest("[class*='sticko']").addClass("js--open"),i.switch.icon(),i.$target.find(".active :tabbable").first().focus(),i.$controller.attr({"aria-expanded":"true"}),i.$target.removeAttr("aria-hidden"),i.$target.find(".loi").length>0&&i.$target.find(".loi__list").each((function(){UX.loi.on.recalculate($(this))})),i.$controller.trigger("dropBlock:opened",[i])),i.additionalOnShow()},hide:function hide(){i.$target&&(i.$controller.trigger("dropBlock:close",[i]),i.$controller.removeClass("js--open"),i.$target.removeClass("js--open"),i.$target.attr("style")&&i.$target.removeAttr("style"),i.$target.closest("[class*='accordion-tabbed__tab']").removeClass("js--open"),i.$target.closest("[class*='sticko']").removeClass("js--open"),i.switch.back(),i.$controller.attr({"aria-expanded":"false"}),i.$target.attr({"aria-hidden":"true"}),i.$parentSection&&i.$parentSection.length?(i.$controller=$('[data-db-target-for="'+i.$parentSection.attr("data-db-target-of")+'"]'),i.find.target()):"dropBlock"===o.attr("data-active")&&(o.removeAttr("data-active"),$("body, html").removeClass("lock-screen")),i.$target&&i.$target.find("input:focus").blur(),i.$controller.trigger("dropBlock:closed",[i])),i.additionalOnHide()},escape:function escape(){null!=i.$target&&i.on.hide()}},find:{target:function target(){if($("[data-db-target-of]").length){i.$targets=$("[data-db-target-of]"),i.$target=$("[data-db-target-of="+i.$controller.attr("data-db-target-for")+"]"),i.$section=i.$target;var e=i.$section.parents(".dropBlock__holder");e.length?i.$parentSection=e:i.$parentSection=null,i.find.parent()}},parent:function parent(){i.$parent=null,i.$parents=$("[data-db-parent-of]"),i.$parents.length&&(i.$parents.each((function(){var e=i.$controller.attr("data-db-target-for");if(-1!==$(this).data("db-parent-of").split(",").indexOf(e))return i.$parent=$(this),!1})),null!=i.$parent&&(i.find.dimensions(),i.find.ifContaind()))},ifContaind:function ifContaind(){i.$parent.find(".container").length&&!i.$target.find(".container").length&&i.$target.wrapInner("<div class='container'></div>")},dimensions:function dimensions(){var e=i.$parent.outerHeight(),t=i.$parent.offset(),n=t.left,o=i.$parent.innerWidth(),a={},r=t;i.$container=$(".container"),i.$container.length&&(r=i.$container.offset());var s=r.left+i.$container.width(),l=o+n;if(i.$parent.find(".scrollThenFix").length&&(e=i.$parent.find(".scrollThenFix").outerHeight()),i.$parent.innerWidth()===i.$controller.innerWidth()&&(o=i.$parent.hasClass("no-auto-width")?"":"auto",i.isMobile&&(o=i.$parent.hasClass("no-auto-width")?"":"100%")),l>s)a={top:e,right:"0px",width:o};else{a={top:e,width:o};var c=i.$parent.data("db-direction")||i.direction;switch(c){case"left":case"right":a[c]="0px";break;case"center":var d=i.$target.innerWidth()/2-i.$parent.innerWidth();a.left="calc(-50% - ".concat(d,"px)")}}i.$target.css(a)}},switch:{icon:function icon(){switch(i.switchVal=i.$controller.attr("data-db-switch"),i.switchVal){case"none":break;case"rotate":i.$controller.find("i").addClass("js--rotated");break;default:void 0!==i.switchVal&&(i.switchClass=i.$controller.find("i").attr("class"),i.$controller.find("i").removeClass().addClass(i.switchVal))}},back:function back(){i.$controllers.each((function(){var e=$(this).attr("data-db-switch");switch(e){case"none":break;case"rotate":i.$controller.find("i").removeClass("js--rotated");break;default:void 0!==e&&"true"==$(this).attr("aria-expanded")&&i.$controller.find("i").removeClass(e).addClass(i.switchClass)}}))}},additionalOnShow:function additionalOnShow(){},additionalOnHide:function additionalOnHide(){}},UX.dropBlock=i},233:function(e,t,n){var o,i,a,r=n(1);o=$("body"),i=!1,(a={$elements:$(".accordion"),$tabbedElements:$(".accordion-tabbed"),$controllers:$(".accordion__control"),$tabbedControllers:$(".accordion-tabbed__control"),isMobile:!1,isStickyBody:!1,vPort:"screen-sm",$responsiveElements:$("[data-accordion-vport]"),accVport:[],$responsiveTarget:[],collapseTabs:!1,collapseAccordion:!0,defaultCollapsed:!0,hideAllBoolean:!0,openedAccordion:0,hideDropBlock:!0,init:function init(){a.initResponsive(),a.logic($(".accordion-tabbed")),a.control(),a.additionalControls()},additionalControls:function additionalControls(){},control:function control(){o.on("click",".accordion__control",(function(e){e.stopImmediatePropagation(),"dropBlock"===o.attr("data-active")&&a.hideDropBlock&&UX.dropBlock.on.hide();var t=$(this).is("[data-slide-target]");if(!($(this).hasClass("external")||"undefined"!==r(t)&&!1!==t&&i)){e.preventDefault();var n=$(this);a.on.toggle.single(n),$(this).trigger("accordion:controlClick")}})),o.on("click",".accordion-tabbed__control",(function(e){var t=$(this).is("[data-slide-target]");if(!($(this).hasClass("external")||"undefined"!==r(t)&&!1!==t&&i||a.preventAccordionBehavior($(this)))){e.preventDefault();var n=$(this);a.on.toggle.tabbed(n)}})),$(document).on(a.vPort+"-on",(function(){i=!0})),$(document).on(a.vPort+"-off",(function(){i=!1}))},logic:function logic(e){a.hideAllBoolean&&a.hideAll(),a.$tabbedElements=e,a.$tabbedElements.each((function(){var e=$(this),t=e.closest(".accordion");t.parent("[data-default-collapsed]").length&&(a.defaultCollapsed=t.parent().data("default-collapsed")),e.find(".accordion-tabbed__tab").each((function(e){var t=$(this);if(a.defaultCollapsed||"all"===a.defaultCollapsed){if(a.preventAccordionBehavior(t))return;a.collapseTabsByDefault(this)||e!==a.openedAccordion||t.hasClass("accordion__closed")?(t.removeClass("js--open"),t.find(".accordion-tabbed__control").attr("aria-expanded","false"),t.find(".accordion-tabbed__content").hide()):(t.addClass("js--open"),t.find(".accordion-tabbed__control").attr("aria-expanded","true"),t.find(".accordion-tabbed__content").show(),t.find(".accordion-lazy").length&&a.getAccordionContent(t.find(".accordion-lazy")))}else t.addClass("js--open"),t.find(".accordion-tabbed__control").attr("aria-expanded","true"),t.find(".accordion-tabbed__content").show()}))}))},collapseTabsByDefault:function collapseTabsByDefault(){return a.collapseTabs},additionalAjaxSuccess:function additionalAjaxSuccess(e){},getCustomAjaxUrl:function getCustomAjaxUrl(e){},buildAjaxUrl:function buildAjaxUrl(e){var t;if(void 0!==e.attr("data-custom-url")&&"true"===e.attr("data-custom-url"))t=a.getCustomAjaxUrl(e);else{var n=e.attr("data-widgetid"),o=e.attr("data-dropzoneid");t="/pb/widgets/accordion/loadTab?pbContext="+encodeURIComponent($("meta[name=pbContext]").attr("content"))+"&widgetId="+n+"&dropZoneId="+o}return t},getAccordionContent:function getAccordionContent(e){var t=a.buildAjaxUrl(e),n=e.parent();$.ajax({url:t,success:function success(t){e.closest(".accordion-tabbed__tab").attr("data-ajaxLoaded","true"),e.replaceWith(t),n.find(".accordion-tabbed").length&&a.logic(n.find(".accordion-tabbed")),n.find(".accordion__spinner").remove(),a.additionalAjaxSuccess(n)},error:function error(){}})},on:{toggle:{single:function single(e){var t,n=e.next(".accordion__content");if(void 0!==e.attr("data-content-target")){var o=e.attr("data-content-target");n=$(o)}n.slideToggle(200,(function(){e.parent().hasClass("article-accordion")&&($(".article-row-left").height("auto"),a.isStickyBody=t)})),e.toggleClass("js--open");var i=e.closest(".expandable-accordion");t=!("true"===e.attr("aria-expanded")),e.attr("aria-expanded",t);var r=$.Event("accordion:expandedSingle");$(document).trigger(r,[e,n]),i.length&&a.on.checkExpandAll(e,t,i)},tabbed:function tabbed(e){var t=e.closest(".accordion-tabbed__tab"),n=e.closest(".accordion-tabbed").children(".js--open"),o=!("true"===e.attr("aria-expanded"));e.attr("aria-expanded",o);var i=e.closest(".expandable-accordion");i.length&&a.on.checkExpandAll(e,o,i),t.hasClass("js--open")?t.find(".accordion-tabbed__content").slideUp(200,(function(){e.removeClass("js--open"),t.removeClass("js--open")})):((a.collapseAccordion||a.extraCollapseAccordionCheck(t))&&n.find(".accordion-tabbed__content").slideUp(200),t.find(".accordion-tabbed__content").slideDown(200,(function(){(a.collapseAccordion||a.extraCollapseAccordionCheck(t))&&n.removeClass("js--open"),t.addClass("js--open"),"true"!==t.attr("data-ajaxLoaded")&&t.find(".accordion-lazy").length&&a.getAccordionContent(t.find(".accordion-lazy")),a.on.expanded(e);var o=$.Event("accordion:expandedTabbed");$(document).trigger(o,[e])})))},all:function all(e){var t=e.closest(".expandable-accordion"),n=!("true"===e.attr("aria-expanded"));if(e.attr("aria-expanded",n),n){t.find(".accordion-tabbed__tab").addClass("js--open"),t.find(".accordion__control, .accordion-tabbed__control").addClass("js--open").attr("aria-expanded",n),t.find(".accordion__content, .accordion-tabbed__content").slideDown(200),e.text("Hide All");var o=t.find("[data-ajaxLoaded]");o.each((function(){"true"!==o.attr("[data-ajaxLoaded]")&&o.find(".accordion-lazy").length&&a.getAccordionContent(o.find(".accordion-lazy"))}))}else t.find(".accordion-tabbed__tab").removeClass("js--open"),t.find(".accordion__control, .accordion-tabbed__control").removeClass("js--open").attr("aria-expanded",n),t.find(".accordion__content, .accordion-tabbed__content").slideUp(200),e.text("Expand All")}},expanded:function expanded(e){},checkExpandAll:function checkExpandAll(e,t,n){var o;n.length&&(o=n.find(".expand-all")),o.length&&"true"===o.attr("aria-expanded")&&(o.attr("aria-expanded",t),t?o.text("Hide All"):o.text("Expand All"))}},hideAll:function hideAll(){a.$elements.each((function(){var e=$(this);e.find(".accordion__control").attr("aria-expanded","false"),e.find(".accordion__content").hasClass("js--open")?e.find(".accordion__content").hasClass("js--open")?e.find(".accordion__control").attr("aria-expanded","true"):e.find(".accordion__content").removeClass("js--open"):e.find(".accordion__content").hide()}))},initResponsive:function initResponsive(){a.$responsiveElements.length&&a.$responsiveElements.each((function(e){a.$responsiveTarget[e]=$(this),a.accVport[e]=$(this).data("accordion-vport"),a.setViewports(e)}))},extraCollapseAccordionCheck:function extraCollapseAccordionCheck(e){return a.collapseAccordion},setViewports:function setViewports(e){$(document).on(a.accVport[e]+"-on",(function(){var t=a.$responsiveTarget[e];t.data("accordion-vport")===a.accVport[e]&&(t.addClass("accordion-vport--res"),"with-arrow"===t.data("accordion-option")&&t.addClass("accordion-with-arrow"),a.logic(a.$responsiveTarget[e].find(".accordion-tabbed")))})),$(document).on(a.accVport[e]+"-off",(function(){var t=a.$responsiveTarget[e];t.data("accordion-vport")===a.accVport[e]&&(t.removeClass("accordion-vport--res accordion-with-arrow"),t.find(".accordion-tabbed__content").show())}))}}).on.toggle.single=function(e){var t,n=e.next(".accordion__content");if(void 0!==e.attr("data-content-target")){var o=e.attr("data-content-target");n=$(o)}n.slideToggle(200,(function(){e.parent().hasClass("article-accordion")&&($(".article-row-left").height("auto"),a.isStickyBody=t)})),e.toggleClass("js--open");var i=e.closest(".expandable-accordion");t=!("true"===e.attr("aria-expanded")),e.attr("aria-expanded",t);var r=$.Event("accordion:expandedSingle");$(document).trigger(r,[e,n]),i.length&&a.on.checkExpandAll(e,t,i)},a.preventAccordionBehavior=function(e){return!!e.parents("[data-accordion-vport]:not(.accordion-vport--res)").length},UX.accordion=a},234:function(e,t){var n,o,i;n=$(window),o=$("body"),i={element:".back-to-top",footerBackToTop:"footer .back-to-top",init:function init(){i.controller()},controller:function controller(){o.on("click",i.element,(function(e){e.preventDefault(),$("html, body").animate({scrollTop:0},600),$(document.body).find(":focusable:not(.sr-only-focusable)").first().focus()})),n.on("scroll",(function(){$(i.footerBackToTop).length&&($(this).scrollTop()>$("header").height()?$(i.footerBackToTop).addClass("back-to-top__is-visible"):$(i.footerBackToTop).removeClass("back-to-top__is-visible back-to-top__fade-out"),$(this).scrollTop()>400&&$(i.footerBackToTop).addClass("back-to-top__fade-out"),i.check.isfooterVisible()?$(i.footerBackToTop).addClass("back-to-top__static"):$(i.footerBackToTop).removeClass("back-to-top__static"))}))},check:{isfooterVisible:function isfooterVisible(){var e=$(window).scrollTop()+$(window).height();return $("footer").offset().top<=e}}},UX.backToTop=i},241:function(e,t){$(document).ready((function(){$(document).on("click","#indivLogin, #guestLogin",(function(e){e.preventDefault(),e.stopPropagation(),$(this).toggleClass("active"),$(".navigation-login-dropdown-container:not(.login-options)").toggleClass("hidden"),$(".search-open").is(":visible")&&toggleQuickSearch()})),$(document).on("click",(function(e){"indivLogin"===$(e.target).attr("id")||"guestLogin"===$(e.target).attr("id")||$(".navigation-login-dropdown-container").hasClass("hidden")||$(".navigation-login-dropdown-container").addClass("hidden")})),$("#accessTokenForm div.input-group input[type=text]").on("keyup input change",(function(){$("#accessTokenForm div.input-group #tokenAccess").val().length?$("#accessTokenForm input[type=submit]").removeAttr("disabled"):$("#accessTokenForm input[type=submit]").attr("disabled","disabled")})),$(".registration-popup input[type=submit]").attr("disabled","disabled"),$(".registration-popup input[type=text]").on("keyup input change",(function(){$(this).val().length?$(".registration-form input[type=submit]").removeAttr("disabled"):$(".registration-form input[type=submit]").attr("disabled","disabled")})),$("#check-if-exist")&&setTimeout((function(){$("#hidden-message").css("visibility","visible")}),3e4),$("#check-if-exist input[type=input]").on("keyup input change",(function(){$("#check-if-exist input[type=input]").val().length?$("#check-if-exist input[type=submit]").removeAttr("disabled"):$("#check-if-exist input[type=submit]").attr("disabled","disabled")}))})),window.loadInstitutionPage=function loadInstitutionPage(e){var t=$(e).val();location.href=t}},242:function(e,t){$(document).ready((function(){$(".registration__personal .registration__button").on("click",(function(e){e.preventDefault(),alert("Registration magic happens"),$(this).parents(".row").slideUp(200),$(this).parents(".row").next().find(".accordion__control").click()}))}))},244:function(e,t){var n,o;n=$("body"),o={$slide:null,$target:null,$contents:null,$slideinfo:null,$toggle:null,prev:null,isMobile:!1,vPort:"screen-sm",elementvPort:null,back:!1,nested:!1,multi:!1,activeSlide:0,oldInfo:[],off:null,clone:null,wait:!1,backLabel:"back",$elements:null,isQueue:!1,isRTL:!1,init:function init(){o.$slide='<div class="w-slide"><div class="w-slide_head"><a href="#" class="w-slide__back"><i class=" icon-arrow_l" aria-hidden="true"></i>'+o.backLabel+'</a><span class="w-slide__title"></span></span></div><div class="w-slide__content"></div></div>',o.$elements=$(o.$slide),o.displaySlideHtmlToPage(),o.$contents=o.$elements.find(".w-slide__content"),o.$slideinfo=o.$elements.find(".w-slide__title"),o.$back=o.$elements.find(".w-slide__back"),o.$toggle=$(".w-slide__btn"),o.elementvPort=o.vPort,o.vPort=[],o.customViewPorts(),o.$toggle.each((function(e){o.check.viewPort($(this),e),o.responsive(e)})),o.additionalInitialization(),o.control(),o.additionalControls(),"undefined"!=typeof facetDateChart&&facetDateChart.control(),UX.utils&&UX.utils.isRTL()&&(o.isRTL=!0)},displaySlideHtmlToPage:function displaySlideHtmlToPage(){var e=$("main");e.length>0?e.append(o.$elements):n.append(o.$elements)},additionalInitialization:function additionalInitialization(){},responsive:function responsive(e){$(document).on(o.vPort[e]+"-on",(function(){o.off=!1,o.isQueue=!1,$(".w-slide__btn").each((function(){$(this).data("ctrl-res")===o.vPort[e]&&$(this).addClass("slide-active")}))})),$(document).on(o.vPort[e]+"-off",(function(t){o.off=!0,o.on.off(t),o.isQueue=!1,$(".w-slide__btn").each((function(){$(this).data("ctrl-res")===o.vPort[e]&&$(this).removeClass("slide-active")}))})),$(document).on(o.elementvPort+"-on",(function(e){o.isMobile=!0,o.$elements.css("margin-top",0),$(".w-slide.js--open").length&&(o.back=!0,o.on.hide(e))})),$(document).on(o.elementvPort+"-off",(function(e){o.isMobile=!1,$(".w-slide.js--open").length&&(o.back=!0,o.on.hide(e))})),o.additionalResponsive()},additionalResponsive:function additionalResponsive(){},control:function control(){n.on("click",".w-slide__btn",(function(e){return o.isQueue&&o.isMobile?(e.stopImmediatePropagation(),$(this).off("click"),!1):(o.isQueue=!0,$(this).hasClass("disable")?(e.stopPropagation(),void e.preventDefault()):void($(this).hasClass("slide-active")&&(e.stopPropagation(),e.preventDefault(),o.addTransition(),o.$toggle=$(this),o.$target=$(o.$toggle.data("slide-target")),o.back=!1,$(this).is("[id*=pane-pcw]")&&UX.tab.check.empty($(this))?setTimeout((function(){o.on.show(e)}),3e3):o.on.show(e))))})),n.on("click",".w-slide__back, .w-slide__hide",(function(e){$(this).hasClass("w-slide__back")&&e.preventDefault(),o.back=!0,o.on.hide(e)}))},additionalControls:function additionalControls(){},addTransition:function addTransition(){$(".w-slide, .w-slide__content").css("transition","all 250ms")},removeTransition:function removeTransition(){$(".w-slide, .w-slide__content").css("transition","")},customViewPorts:function customViewPorts(){$(".journal-home").length&&(o.elementvPort="screen-md")},setBackLabel:function setBackLabel(){o.$back.html('<i class=" icon-arrow_l" aria-hidden="true"></i>'+o.backLabel)},on:{show:function show(e){var t;$(document).trigger("UX-detach"),"undefined"!=typeof jcf&&jcf.destroy($(".range-slider__range"));var i=$.Event("slide:open");if($(document).trigger(i,[o]),!i.isDefaultPrevented()){"undefined"!==n.attr("data-active")&&UX.controller.check(e),!o.$toggle.is("[data-full-screen]")||o.$toggle.data("[full-screen]")||o.isMobile?n.removeClass("w-slide__not-fullWidth"):n.addClass("w-slide__not-fullWidth"),o.nested=o.$toggle.parents(".w-slide__content").length>0,o.multi=o.$toggle.closest(".pill-list").length>0||o.$toggle.is('[data-slide-target*="#pill"]'),o.$toggle.closest(".w-slide__pill").length>0&&(o.nested=!1,o.multi=!0);var a=null===(t=e.currentTarget)||void 0===t?void 0:t.getAttribute("aria-haspopup");a&&o.$elements.attr("role","true"===a?"menu":a),o.on.additionalShow(),o.$slideinfo.hide().removeClass("js--open"),o.backLabel="back",o.setBackLabel(),o.nested&&o.on._nestSlide(),o.multi&&(0!==o.activeSlide&&(o.back=!0,o.on.hide(e)),o.activeSlide++,o.$elements.addClass("w-slide__pill").find(".w-slide__content").append('<div class="w-slide__content nested--'+o.activeSlide+'"></div>'),o.$contents=$(".nested--"+o.activeSlide)),o.$contents.attr("original-target",o.$toggle.data("slide-target")),o.$target.hasClass("tab__pane")&&(o.$target.closest(".tab__content").find(".tab__pane").removeClass("active"),o.$target.addClass("active"));var r=o.$toggle.text();o.$toggle.is("[data-label]")&&(r=o.$toggle.data("label")),(o.$toggle.hasClass("loa")||o.$toggle.parents(".loa.mobile-authors").length>0)&&(r="AUTHORS");var s=o.$target.children();o.on.destroyJcf(s),o.$toggle.is("[data-slide-clone]")?(o.clone=o.$toggle.data("slide-clone"),"self"===o.clone&&(s=o.$target)):o.clone="",o.oldInfo.push(r),o.$slideinfo.html(r),o.$target.find(".loi").length>0?(o.$contents.append(s.html()),$(".dropBlock-loi__holder .loi").remove(),o.$contents.find(".loi__list").each((function(){UX.loi.on.recalculate($(this))}))):("self"===o.clone?o.nested||o.multi?o.$target.after('<div class="returnNestedDataSlideHere"></div>'):o.$target.after('<div class="returnDataSlideHere"></div>'):o.nested||o.multi?o.$target.append('<div class="returnNestedDataSlideHere"></div>'):o.$target.append('<div class="returnDataSlideHere"></div>'),s.appendTo(o.$contents),o.on.rebuildJcf(o.$contents)),"undefined"!=typeof googletag&&void 0!==googletag.pubads&&googletag.pubads().refresh(),n.addClass("lock-screen slide-is-active").attr("data-active","slide"),o.$back.focus();var l=$(".coolBar");l.length&&l.addClass("slide");var c=$.Event("slide:opened");o.wait?(setTimeout((function(){o.on._showSlide(),o.on.slidePill(),$(document).trigger(c,[o])}),150),o.wait=!1):(o.on._showSlide(),$(document).trigger(c,[o])),$(document).trigger("UX-attach")}},_showSlide:function _showSlide(){var e=o.$elements;(o.nested||o.multi)&&(e=o.$contents),!o.$elements.hasClass("js--open")&&o.multi&&(e=$("[class*=nested--], .w-slide"));var t="-".concat("100%");o.isRTL&&(t="100%"),e.css("transform","translateX(".concat(t,")")).off().one("transitionend webkitTransitionEnd oTransitionEnd",(function(e){e.stopPropagation(),o.isQueue=!1,o.removeTransition()})),o.$slideinfo.show().addClass("js--open"),o.$elements.addClass("js--open"),o.on.additionalShowSlide()},_nestSlide:function _nestSlide(){o.activeSlide++,o.$contents.append('<div class="w-slide__content nested--'+o.activeSlide+'"></div>'),o.$contents=$(".nested--"+o.activeSlide)},additionalShow:function additionalShow(){},additionalShowSlide:function additionalShowSlide(){},slidePill:function slidePill(){},hide:function hide(e){var t=$(e.target).closest(".w-slide").length,i=$.Event("slide:close");if($(document).trigger(i,[o]),!i.isDefaultPrevented()){if(void 0!==e&&$(".w-slide").hasClass("js--open")&&(t||(o.back=!0)),o.back){o.addTransition(),o.$slideinfo.hide().removeClass("js--open"),o.nested=!0,0===o.activeSlide?(o.nested=!1,o.$contents=o.$elements.children(".w-slide__content")):o.$contents=$(".nested--"+o.activeSlide),o.on.destroyJcf(o.$contents);var a=$(o.$contents.attr("original-target"));a||(a=n);var r=o.$elements.find(".loi").length>0;if(o.on.beforeReturnSlideContent(),r){var s=$(".dropBlock-loi__holder");s.append(o.$contents.html()),s.find(".loi__list").each((function(){UX.loi.on.recalculate($(this))}))}else o.$contents.find("[class*=nested--]").length&&o.$contents.find("[class*=nested--]").remove(),o.on._returnDataSlide(a),o.on.rebuildJcf(a);if(o.oldInfo.splice(o.activeSlide,1),o.$slideinfo.html(o.oldInfo[o.activeSlide-1]),o.$toggle&&o.$toggle.focus(),o.nested)o.activeSlide--,o.on._hideSlide(),o.multi&&o.$contents.remove(),o.$contents=$(".nested--"+o.activeSlide),0===o.activeSlide&&(o.$contents=o.$elements.children(".w-slide__content"));else{o.activeSlide=0,$("body, html").removeClass("lock-screen"),n.removeClass("lock-screen slide-is-active").removeAttr("data-active");var l=$(".coolBar");l.length&&l.removeClass("slide"),o.on._hideSlide(),o.$elements.removeClass("js--open"),o.$elements.removeAttr("role")}if(o.$slideinfo.show().addClass("js--open"),o.backLabel="back",o.setBackLabel(),o.on.additionalHide(e),o.$elements.hasClass("w-slide__pill")){for(;o.activeSlide>0;)o.activeSlide--,o.on.hide(e);void 0!==e&&($(e.target).hasClass("w-slide__back")||$(e.currentTarget).hasClass("w-slide__back"))&&0===o.activeSlide&&o.on.off(e)}t||void 0!==e&&$(e.target).closest(".pill-list").length||o.on.off(e),o.removeTransition()}"undefined"!=typeof googletag&&void 0!==googletag.pubads&&googletag.pubads().refresh();var c=$.Event("slide:closed");$(document).trigger(c,[o])}},_hideSlide:function _hideSlide(){var e=o.$elements;o.nested&&(e=o.$contents),e.css("transform","translateX(0vw)").off().one("transitionend webkitTransitionEnd oTransitionEnd",(function(e){e.stopPropagation(),o.back&&(o.nested?$(this).remove():o.$contents.empty())}))},_returnDataSlide:function _returnDataSlide(e){var t=o.nested||o.multi?".returnNestedDataSlideHere":".returnDataSlideHere",n="self"===o.clone?$(t):$(t,e);o.$contents.children().appendTo(n.parent()),n.remove()},additionalHide:function additionalHide(e){},beforeReturnSlideContent:function beforeReturnSlideContent(){},off:function off(e){if(o.$slideinfo.hide().removeClass("js--open"),$(".pill-list li").removeClass("pill-active"),o.$elements.find(".loi").length>0){o.$contents=$(".loi").closest(".w-slide__content");var t=$(".dropBlock-loi__holder");t.append(o.$contents.html()),t.find(".loi__list").each((function(){UX.loi.on.recalculate($(this))}))}$("body, html").removeClass("lock-screen"),n.removeClass("lock-screen slide-is-active").removeAttr("data-active"),$(".coolBar").removeClass("slide"),o.$contents=o.$elements.children(".w-slide__content"),o.$elements.css("transform","translateX(0vw)").off().one("transitionend webkitTransitionEnd oTransitionEnd",(function(e){e.stopPropagation(),o.$elements.removeClass("js--open"),o.off&&($(".w-slide").removeClass("w-slide__pill"),o.$contents.empty())})),o.off=!1,o.on.additionalOff(e)},additionalOff:function additionalOff(e){},destroyJcf:function destroyJcf(e){"undefined"!=typeof jcf&&jcf.destroy(e.find(".jcf"))},rebuildJcf:function rebuildJcf(e){"undefined"!=typeof jcf&&jcf.replace(e.find(".jcf"))}},check:{viewPort:function viewPort(e,t){void 0!==e.data("ctrl-res")?o.vPort[t]=e.data("ctrl-res"):(e.data("ctrl-res",o.elementvPort),o.vPort[t]=o.elementvPort)}}},UX.slide=o},245:function(e,t){var n,o;n=$("body"),o={vPort:"screen-sm",elementvPort:null,isMobile:!1,$menuContainer:$(".responsive-menu-container"),leftSectionWidth:null,rightSectionWidth:null,maxMenuItemWidth:135,$moreDropsown:$(".responsive-menu-nav .menubar > .dropdown-more"),$menu:$(".responsive-menu-nav >.drawer__nav"),$menuWrapper:$(".responsive-menu-nav"),init:function init(){o.elementvPort=o.vPort,o.vPort=[],$(".responsive-menu-container").each((function(e){o.check.viewPort(e,$(this)),o.responsive(e)})),o.control(),o.on.build()},responsive:function responsive(e){$(document).on(o.vPort[e]+"-on",(function(){o.isMobile=!0,$(".responsive-menu-container").each((function(){$(this).data("ctrl-res")===o.vPort[e]&&$(this).addClass("js--disabled-responsive-menu")}))})),$(document).on(o.vPort[e]+"-off",(function(){o.isMobile=!1,$(".responsive-menu-container").each((function(){$(this).data("ctrl-res")===o.vPort[e]&&$(this).removeClass("js--disabled-responsive-menu")}))}))},control:function control(){$(window).on("resize",(function(){o.modifyMenu()})),n.on("touchend",".dropdown-more >a, .dropdown.menu-parent>a ",(function(e){$(this).parents(".dropdown-more").length>0&&!$(this).parent().hasClass(".dropdown-more")?$(".responsive-menu-nav .js--opened:not(.dropdown-more)").not($(this).parent()).removeClass("js--opened"):$(".responsive-menu-nav .js--opened").not($(this).parent()).removeClass("js--opened"),$(this).parent().toggleClass("js--opened"),$(this).parent().hasClass("js--opened")?$(this).parent().removeClass("js--forceClose"):$(this).parent().addClass("js--forceClose")})),n.click((function(e){$(".responsive-menu-nav .js--opened:not(:hover)").removeClass("js--opened")}))},on:{build:function build(){o.$menuWrapper.show(),o.modifyMenu(),setTimeout((function(){o.modifyMenu()}),800)}},modifyMenu:function modifyMenu(){$(".responsive-menu-container").each((function(){if(o.$menuContainer=$(this),o.$moreDropsown=o.$menuContainer.find(".dropdown-more"),o.$menuContainer.hasClass("js--disabled-responsive-menu"))o.$moreDropsown.hasClass("hidden")||(o.$moreDropsown.addClass("hidden"),o.$moreDropsown.before(o.$moreDropsown.find(".dropdown__menu").html()),o.$moreDropsown.find(".dropdown__menu").empty());else{o.leftSectionWidth=o.$menuContainer.find(".left-section").width(),o.rightSectionWidth=o.$menuContainer.find(".right-section").width(),o.$menu=o.$menuContainer.find(".drawer__nav"),o.$menuWrapper=o.$menuContainer.find(".responsive-menu-nav");var e=o.$menuContainer.width()-(o.leftSectionWidth+o.rightSectionWidth+35),t=o.$menu.width();if(e>0&&t>0)if(t>e)for(;t>e;){var n=o.$menu.find('.menubar > [role="menuitem"]:not(.dropdown-more)').last();if(!(n.length>0))break;o.$moreDropsown.removeClass("hidden"),o.$moreDropsown.find(">.dropdown__menu").prepend(n[0].outerHTML),n.remove(),t=o.$menu.width()}else if(e>t+o.maxMenuItemWidth&&!o.$moreDropsown.hasClass("hidden")){for(;e>t;){var i=o.$moreDropsown.find('.dropdown__menu [role="menuitem"]'),a=i.first();if(!(a.length>0))break;o.$moreDropsown.before(a[0].outerHTML),i.length<=1&&o.$moreDropsown.addClass("hidden"),a.remove(),t=o.$menu.width()}t>e&&o.modifyMenu()}}}))},check:{viewPort:function viewPort(e,t){void 0!==t.data("ctrl-res")||""===t.data("ctrl-res")?o.vPort[e]=t.data("ctrl-res"):(t.data("ctrl-res",o.elementvPort),o.vPort[e]=o.elementvPort)}}},UX.reponsiveMenu=o},246:function(e,t){var n,o;n=$("body"),o={init:function init(){o.control()},offset:function offset(e){var t=e.getBoundingClientRect(),n=window.pageXOffset||document.documentElement.scrollLeft,o=window.pageYOffset||document.documentElement.scrollTop;return{top:t.top+o,left:t.left+n}},adjustAnchor:function adjustAnchor(e){e.preventDefault();var t=$(":target"),n=o.get.fixedpageElementsHeight();t.length>0&&(e.stopPropagation(),t.eq(0).focus(),window.scrollTo(0,o.offset(t.get(0)).top-n))},control:function control(){$(window).on("hashchange load",o.adjustAnchor),n.on("click",'a.table-fn[href^="#"], a.ref.fn[href^="#"], .scroll-to-target a[href^="#"], .content a[href^="#"]',(function(e){window.location.hash===$(this).attr("href")&&o.adjustAnchor(e)})),n.on("click keydown",'a[href^="#"].sr-only-focusable',(function(e){e.keyCode&&13!==e.keyCode||window.location.hash===$(this).attr("href")&&o.adjustAnchor(e)})),n.on("click",".w-slide__back",(function(){$("[data-scroll-back-to]").length&&($(document).scrollTop($("[data-scroll-back-to]").data("scroll-back-to")),$("[data-scroll-back-to]").removeAttr("data-scroll-back-to"))}))},get:{fixedpageElementsHeight:function fixedpageElementsHeight(){var e=0,t=$("header.fixed, .pageHeader");t.length&&(e=t.outerHeight());var n=$(".scrollThenFix");n.length&&(e=n.outerHeight());var o=$(".coolBar.stickybar--sticky"),i=0;return o.length&&(0===(i=o.outerHeight())&&(i=o.find(".stickybar__wrapper").outerHeight()),e+=i),$("header .scrollThenFix, .coolBar").length&&$.each($("header .scrollThenFix, .coolBar"),(function(t,n){$(n).outerHeight()&&(e+=$(n).outerHeight())})),e}}},UX.scrollo=o},251:function(e,t){var n;n={init:function init(){$(".js__toggleAdForm").on("click",(function(e){e.preventDefault();var t="#"+$(this).data("toggle");$(t).slideToggle()}))}},UX.adplaceholder=n},402:function(e,t,n){"use strict";n.r(t);n(225);var o,i=n(35);o={html:'<div class="ie-warning"><div class="ie-warning__text">The site is best supported on modern browsers like Chrome, Safari, Firefox and Edge.</div><button class="btn ie-warning__btn">Got it</button></div>',parent:"header",selectors:{target:"header",content:".content",warning:".ie-warning",button:".ie-warning__btn"},targetEl:null,contentEl:null,init:function init(){var e=i.a;!sessionStorage.getItem("ieWarning")&&e&&e<12&&(this.targetEl=document.querySelector(this.selectors.target),this.showMessage())},showMessage:function showMessage(){o.targetEl.insertAdjacentHTML("afterbegin",this.html),document.querySelector(o.selectors.button).addEventListener("click",this.handlers.btnClickHandler),o.contentEl=document.querySelector(o.selectors.content),o.contentEl&&(o.contentEl.style.paddingTop=parseInt(window.getComputedStyle(o.contentEl).paddingTop)+document.querySelector(o.selectors.warning).clientHeight+"px")},removeMessage:function removeMessage(){sessionStorage&&sessionStorage.setItem("ieWarning","shown"),document.querySelector(o.selectors.button).removeEventListener("click",this.handlers.btnClickHandler);var e=document.querySelector(o.selectors.warning);o.contentEl&&(o.contentEl.style.paddingTop=parseInt(window.getComputedStyle(o.contentEl).paddingTop)-e.clientHeight+"px"),o.targetEl.removeChild(e)},handlers:{btnClickHandler:function btnClickHandler(e){o.removeMessage()}}},UX.ieWarning=o,UX.ieWarning.init();n(226),n(227);var a,r=n(0),s=(a=function(e,t){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}a(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}),__decorate=function(e,t,n,o){var i,a=arguments.length,r=a<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(r=(a<3?i(r):a>3?i(t,n,r):i(t,n))||r);return a>3&&r&&Object.defineProperty(t,n,r),r},__metadata=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},l=function(e){function Texts(){return null!==e&&e.apply(this,arguments)||this}return s(Texts,e),Texts}(r.d),c=function(e){function ClassList(){var t=null!==e&&e.apply(this,arguments)||this;return t.open="core-dropblock__content--open",t}return s(ClassList,e),ClassList}(r.d),d=function(e){function Selectors(){var t=null!==e&&e.apply(this,arguments)||this;return t.trigger=new r.e(".core-dropblock__trigger"),t.content=new r.e(".core-dropblock__content"),t}return s(Selectors,e),Selectors}(r.f),h=function(e){function Elements(){return null!==e&&e.apply(this,arguments)||this}return s(Elements,e),Elements}(r.b),u=function(){function CoreDropblock(e){this.wrapper=e}return CoreDropblock.prototype.initialize=function(){if(this.elements.initialize(this.wrapper),this.elements.trigger&&this.elements.content)return this.isOpen=!1,this.isTransitioning=!1,this.addEventListeners(),this},CoreDropblock.prototype.addEventListeners=function(){var e=this;this.domUtils.addEventListener(document,"click",(function(t){return e.handleOutsideClick(t)})),this.domUtils.addEventListener(this.elements.trigger,"click",(function(t){return e.toggleDropdown(t)})),this.domUtils.addEventListener(this.elements.trigger,"keydown",(function(t){return e.handleKeyDown(t)})),this.domUtils.addEventListener(this.elements.content,"transitionend",(function(){e.isTransitioning=!1}))},CoreDropblock.prototype.toggleDropdown=function(e){this.isTransitioning||(this.isTransitioning=!0,e.stopPropagation(),this.isOpen=!this.isOpen,this.updateDropdownState())},CoreDropblock.prototype.updateDropdownState=function(){if(this.isOpen){this.domUtils.addClasses(this.elements.content,this.classList.open),this.elements.trigger.setAttribute("aria-expanded","true"),this.elements.content.setAttribute("aria-hidden","false"),this.elements.content.style.height=this.elements.content.scrollHeight+"px";var e=this.domUtils.getElement("button, [href], input, select, textarea, [tabindex]:not([tabindex='-1'])",this.elements.content);null==e||e.focus()}else this.elements.content.style.height="0",this.domUtils.removeClasses(this.elements.content,this.classList.open),this.elements.trigger.setAttribute("aria-expanded","false"),this.elements.content.setAttribute("aria-hidden","true")},CoreDropblock.prototype.closeDropdown=function(){this.isOpen=!1,this.updateDropdownState()},CoreDropblock.prototype.handleOutsideClick=function(e){this.elements.content.contains(e.target)||this.elements.trigger.contains(e.target)||this.closeDropdown()},CoreDropblock.prototype.handleKeyDown=function(e){"Escape"===e.key&&this.isOpen?this.closeDropdown():"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),this.toggleDropdown(e))},CoreDropblock=__decorate([Object(r.c)(d,h,c,l),__metadata("design:paramtypes",[HTMLElement])],CoreDropblock)}(),f=(n(228),n(230)),p=n(231),m=(n(232),n(234),n(235)),g=(n(236),n(237),n(202)),v=n(238),_=(n(239),n(240),n(241),n(242),n(1)),b=n.n(_),y=n(204),w=n.n(y),k=n(8),C=n.n(k),S=n(9),T=n.n(S),E=n(2),x=n.n(E),H=n(24);function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){w()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var A="modal",D=".".concat("bs.modal"),j=x.a.fn[A],B={backdrop:!0,keyboard:!0,focus:!0,show:!0},O={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},P={HIDE:"hide".concat(D),HIDE_PREVENTED:"hidePrevented".concat(D),HIDDEN:"hidden".concat(D),SHOW:"show".concat(D),SHOWN:"shown".concat(D),FOCUSIN:"focusin".concat(D),RESIZE:"resize".concat(D),CLICK_DISMISS:"click.dismiss".concat(D),KEYDOWN_DISMISS:"keydown.dismiss".concat(D),MOUSEUP_DISMISS:"mouseup.dismiss".concat(D),MOUSEDOWN_DISMISS:"mousedown.dismiss".concat(D),CLICK_DATA_API:"click".concat(D).concat(".data-api")},I="modal-dialog-scrollable",M="modal-scrollbar-measure",L="modal-backdrop",N="modal-open",U="fade",R="in",W="modal-static",F=".modal-dialog",V=".modal-body",X='[data-toggle="modal"]',z='[data-dismiss="modal"]',q=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",K=".sticky-top",J=function(){function Modal(e,t){C()(this,Modal),this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(F),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}return T()(Modal,[{key:"toggle",value:function toggle(e){return this._isShown?this.hide():this.show(e)}},{key:"show",value:function show(e){var t=this;if(!this._isShown&&!this._isTransitioning){x()(this._element).hasClass(U)&&(this._isTransitioning=!0);var n=x.a.Event(P.SHOW,{relatedTarget:e});x()(this._element).trigger(n),this._isShown||n.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),x()(this._element).on(P.CLICK_DISMISS,z,(function(e){return t.hide(e)})),x()(this._dialog).on(P.MOUSEDOWN_DISMISS,(function(){x()(t._element).one(P.MOUSEUP_DISMISS,(function(e){x()(e.target).is(t._element)&&(t._ignoreBackdropClick=!0)}))})),this._showBackdrop((function(){return t._showElement(e)})))}}},{key:"hide",value:function hide(e){var t=this;if(e&&e.preventDefault(),this._isShown&&!this._isTransitioning){var n=x.a.Event(P.HIDE);if(x()(this._element).trigger(n),this._isShown&&!n.isDefaultPrevented()){this._isShown=!1;var o=x()(this._element).hasClass(U);if(o&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),x()(document).off(P.FOCUSIN),x()(this._element).removeClass(R),x()(this._element).off(P.CLICK_DISMISS),x()(this._dialog).off(P.MOUSEDOWN_DISMISS),o){var i=H.a.getTransitionDurationFromElement(this._element);x()(this._element).one(H.a.TRANSITION_END,(function(e){return t._hideModal(e)})).emulateTransitionEnd(i)}else this._hideModal()}}}},{key:"dispose",value:function dispose(){[window,this._element,this._dialog].forEach((function(e){return x()(e).off(D)})),x()(document).off(P.FOCUSIN),x.a.removeData(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null}},{key:"handleUpdate",value:function handleUpdate(){this._adjustDialog()}},{key:"_getConfig",value:function _getConfig(e){return e=_objectSpread(_objectSpread({},B),e),H.a.typeCheckConfig(A,e,O),e}},{key:"_triggerBackdropTransition",value:function _triggerBackdropTransition(){var e=this;if("static"===this._config.backdrop){var t=x.a.Event(P.HIDE_PREVENTED);if(x()(this._element).trigger(t),t.defaultPrevented)return;this._element.classList.add(W);var n=H.a.getTransitionDurationFromElement(this._element);x()(this._element).one(H.a.TRANSITION_END,(function(){e._element.classList.remove(W)})).emulateTransitionEnd(n),this._element.focus()}else this.hide()}},{key:"_showElement",value:function _showElement(e){var t=this,n=x()(this._element).hasClass(U),o=this._dialog?this._dialog.querySelector(V):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),x()(this._dialog).hasClass(I)&&o?o.scrollTop=0:this._element.scrollTop=0,n&&H.a.reflow(this._element),x()(this._element).addClass(R),this._config.focus&&this._enforceFocus();var i=x.a.Event(P.SHOWN,{relatedTarget:e}),a=function transitionComplete(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,x()(t._element).trigger(i)};if(n){var r=H.a.getTransitionDurationFromElement(this._dialog);x()(this._dialog).one(H.a.TRANSITION_END,a).emulateTransitionEnd(r)}else a()}},{key:"_enforceFocus",value:function _enforceFocus(){var e=this;x()(document).off(P.FOCUSIN).on(P.FOCUSIN,(function(t){document!==t.target&&e._element!==t.target&&0===x()(e._element).has(t.target).length&&e._element.focus()}))}},{key:"_setEscapeEvent",value:function _setEscapeEvent(){var e=this;this._isShown&&this._config.keyboard?x()(this._element).on(P.KEYDOWN_DISMISS,(function(t){27===t.which&&e._triggerBackdropTransition()})):this._isShown||x()(this._element).off(P.KEYDOWN_DISMISS)}},{key:"_setResizeEvent",value:function _setResizeEvent(){var e=this;this._isShown?x()(window).on(P.RESIZE,(function(t){return e.handleUpdate(t)})):x()(window).off(P.RESIZE)}},{key:"_hideModal",value:function _hideModal(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._isTransitioning=!1,this._showBackdrop((function(){x()(document.body).removeClass(N),e._resetAdjustments(),e._resetScrollbar(),x()(e._element).trigger(P.HIDDEN)}))}},{key:"_removeBackdrop",value:function _removeBackdrop(){this._backdrop&&(x()(this._backdrop).remove(),this._backdrop=null)}},{key:"_showBackdrop",value:function _showBackdrop(e){var t=this,n=x()(this._element).hasClass(U)?U:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=L,n&&this._backdrop.classList.add(n),x()(this._backdrop).appendTo(document.body),x()(this._element).on(P.CLICK_DISMISS,(function(e){t._ignoreBackdropClick?t._ignoreBackdropClick=!1:e.target===e.currentTarget&&t._triggerBackdropTransition()})),n&&H.a.reflow(this._backdrop),x()(this._backdrop).addClass(R),!e)return;if(!n)return void e();var o=H.a.getTransitionDurationFromElement(this._backdrop);x()(this._backdrop).one(H.a.TRANSITION_END,e).emulateTransitionEnd(o)}else if(!this._isShown&&this._backdrop){x()(this._backdrop).removeClass(R);var i=function callbackRemove(){t._removeBackdrop(),e&&e()};if(x()(this._element).hasClass(U)){var a=H.a.getTransitionDurationFromElement(this._backdrop);x()(this._backdrop).one(H.a.TRANSITION_END,i).emulateTransitionEnd(a)}else i()}else e&&e()}},{key:"_adjustDialog",value:function _adjustDialog(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft="".concat(this._scrollbarWidth,"px")),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight="".concat(this._scrollbarWidth,"px"))}},{key:"_resetAdjustments",value:function _resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}},{key:"_checkScrollbar",value:function _checkScrollbar(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=e.left+e.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()}},{key:"_setScrollbar",value:function _setScrollbar(){var e=this;if(this._isBodyOverflowing){var t=[].slice.call(document.querySelectorAll(q)),n=[].slice.call(document.querySelectorAll(K));x()(t).each((function(t,n){var o=n.style.paddingRight,i=x()(n).css("padding-right");x()(n).data("padding-right",o).css("padding-right","".concat(parseFloat(i)+e._scrollbarWidth,"px"))})),x()(n).each((function(t,n){var o=n.style.marginRight,i=x()(n).css("margin-right");x()(n).data("margin-right",o).css("margin-right","".concat(parseFloat(i)-e._scrollbarWidth,"px"))}));var o=document.body.style.paddingRight,i=x()(document.body).css("padding-right");x()(document.body).data("padding-right",o).css("padding-right","".concat(parseFloat(i)+this._scrollbarWidth,"px"))}x()(document.body).addClass(N)}},{key:"_resetScrollbar",value:function _resetScrollbar(){var e=[].slice.call(document.querySelectorAll(q));x()(e).each((function(e,t){var n=x()(t).data("padding-right");x()(t).removeData("padding-right"),t.style.paddingRight=n||""}));var t=[].slice.call(document.querySelectorAll("".concat(K)));x()(t).each((function(e,t){var n=x()(t).data("margin-right");void 0!==n&&x()(t).css("margin-right",n).removeData("margin-right")}));var n=x()(document.body).data("padding-right");x()(document.body).removeData("padding-right"),document.body.style.paddingRight=n||""}},{key:"_getScrollbarWidth",value:function _getScrollbarWidth(){var e=document.createElement("div");e.className=M,document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t}}],[{key:"VERSION",get:function get(){return"4.4.1"}},{key:"Default",get:function get(){return B}},{key:"_jQueryInterface",value:function _jQueryInterface(e,t){return this.each((function(){var n=x()(this).data("bs.modal"),o=_objectSpread(_objectSpread(_objectSpread({},B),x()(this).data()),"object"===b()(e)&&e?e:{});if(n||(n=new Modal(this,o),x()(this).data("bs.modal",n)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'.concat(e,'"'));n[e](t)}else o.show&&n.show(t)}))}}])}();x()(document).on(P.CLICK_DATA_API,X,(function(e){var t,n=this,o=H.a.getSelectorFromElement(this);o&&(t=document.querySelector(o));var i=x()(t).data("bs.modal")?"toggle":_objectSpread(_objectSpread({},x()(t).data()),x()(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var a=x()(t).one(P.SHOW,(function(e){e.isDefaultPrevented()||a.one(P.HIDDEN,(function(){x()(n).is(":visible")&&n.focus()}))}));J._jQueryInterface.call(x()(t),i,this)})),x.a.fn[A]=J._jQueryInterface,x.a.fn[A].Constructor=J,x.a.fn[A].noConflict=function(){return x.a.fn[A]=j,J._jQueryInterface};n(243),n(245);var Q,Y=function main(e){var t=Array.prototype.slice.call(document.getElementsByClassName("w-slide__btn")),n=Array.prototype.slice.call(document.getElementsByClassName("responsive-menu-nav"));try{t.length&&e.slide.init(t)}catch(e){console.error(e)}try{n.length&&e.reponsiveMenu.init(n)}catch(e){console.error(e)}};n(246);Q={headerSelector:".header",$primaryBar:$(".auto-hide-primary-bar"),$stickyBar:$(".auto-hide-secondary-bar"),previousTop:0,scrollDelta:5,scrollOffset:100,vPort:"screen-sm",isMobile:!1,previousScroll:"",disabled:!1,changeEvent:null,changeEventName:"change:auto-hide-bar",init:function init(){var e=this;i.a?this.$primaryBar.removeClass("fixed"):(this.header=document.querySelector(this.headerSelector),this.$primaryBar.addClass("auto-hide-bar fixed-element"),this.$stickyBar.addClass("secondary-bar--sticky fixed-element"),this.createEvents(),setTimeout((function(){e.$stickyBar.css("top",e.$primaryBar.height())}),200),this.control())},control:function control(){$(document).on(Q.vPort+"-on",(function(){Q.isMobile=!0})),$(document).on(Q.vPort+"-off",(function(){Q.isMobile=!1})),$(window).on("resize",(function(){Q.previousScroll=""})),$(window).on("scroll resize",(function(){Q.displayOnScroll()})),Q.additionalControls()},createEvents:function createEvents(){this.changeEvent=new CustomEvent(this.changeEventName,{},!1)},displayOnScroll:function displayOnScroll(){var e=this;if(Q.$stickyBar.length&&!1===Q.isMobile&&!Q.disabled){var t=$(window).scrollTop(),n=Q.$primaryBar.height()+Q.scrollOffset;Q.previousTop>=t&&t<n?Q.previousTop-t>Q.scrollDelta&&"up"!==Q.previousScroll&&(Q.$primaryBar.addClass("fixed-element"),this.header.style.removeProperty("transform"),Q.previousScroll="up",setTimeout((function(){document.dispatchEvent(e.changeEvent)}),100)):t>n&&"down"!==Q.previousScroll&&(Q.$primaryBar.removeClass("fixed-element"),this.header.style.setProperty("transform","translateY(-".concat(Q.$primaryBar.height(),"px)")),Q.hideOpenHeaderMenus(),Q.previousScroll="down",setTimeout((function(){document.dispatchEvent(e.changeEvent)}),100)),Q.previousTop=t}},additionalControls:function additionalControls(){},hideOpenHeaderMenus:function hideOpenHeaderMenus(){$(".navigation-login-dropdown-container").addClass("hidden");var e=$(".loginBar__dropBlock__holder.js--open");e.removeClass("js--open"),$("[data-db-target-for="+e.attr("data-db-target-of")+"]").removeClass("js--open");var t=$(".quick-search__dropBlock.js--open");t.removeClass("js--open"),$("[data-db-target-for="+t.attr("data-db-target-of")+"]").removeClass("js--open")}},UX.autoHideBar=Q;n(205),n(247),n(248);var G=n(206),Z=(n(249),n(250),function main(e){var t=Array.prototype.slice.call(document.querySelectorAll(".table-fn, .ref.fn, .scroll-to-target")),n=Array.prototype.slice.call(document.getElementsByClassName("auto-hide-primary-bar")),o=Array.prototype.slice.call(document.getElementsByClassName("sticko")),i=Array.prototype.slice.call(document.getElementsByClassName("coolBar")),a=Array.prototype.slice.call(document.querySelectorAll('.table-fn, .ref.fn, .scroll-to-target, a.sr-only-focusable, .article__content, [data-db-target-of=sections] a, .scrollableLink, .scrollableLinks a, .comment-link a, .PdfLink a[href="#accessDenialLayout"], .article-section__full a[href*="#"]')),r=Array.prototype.slice.call(document.getElementsByClassName("stickybar")),s=Array.prototype.slice.call(document.getElementsByClassName("js__toggleAdForm"));try{t.length&&e.scrollo.init(t)}catch(e){console.error(e)}try{n.length&&e.autoHideBar.init(n)}catch(e){console.error(e)}try{e.stickyElements.init()}catch(e){console.error(e)}try{o.length&&e.sticko.init(o)}catch(e){console.error(e)}try{i.length&&e.coolbar.init(i)}catch(e){console.error(e)}try{a.length&&(e.goToSection=new G.a(a).initialize())}catch(e){console.error(e)}try{r.length&&e.setStickeyTop.init(r)}catch(e){console.error(e)}try{s.length&&e.adplaceholder.init(s)}catch(e){console.error(e)}}),ee=n(252),te=function main(e){var t=Array.prototype.slice.call(document.querySelectorAll('article[data-enable-mathjax="true"] ,[data-mathJax] , .mathStatement , .inline-equation , math'));try{t.length&&(e.mathjax=new ee.a(t))}catch(e){console.error(e)}};t.default=function main(e){var t=Array.prototype.slice.call(document.getElementsByClassName("header__dropzone-bookmark")),n=Array.prototype.slice.call(document.getElementsByClassName("core-dropblock")),o=Array.prototype.slice.call(document.querySelectorAll("[data-db-target-for]")),i=Array.prototype.slice.call(document.getElementsByClassName("loa-authors-trunc")),a=Array.prototype.slice.call(document.getElementsByClassName("ERP-acceptance")),r=Array.prototype.slice.call(document.getElementsByClassName("accordion")),s=Array.prototype.slice.call(document.getElementsByClassName("back-to-top")),l=Array.prototype.slice.call(document.querySelectorAll(".registration,.registration-completed,[registrationCompleted = true]")),c=Array.prototype.slice.call(document.getElementsByClassName("multi-search")),d=Array.prototype.slice.call(document.getElementsByClassName("table-of-content")),h=Array.prototype.slice.call(document.getElementsByClassName("shibboleth"));try{e.noFocusableSVG.init()}catch(e){console.error(e)}try{t.length&&e.bookmark.init(t)}catch(e){console.error(e)}try{n.length&&(e.coreDropblock=n.map((function(e){return new u(e).initialize()})))}catch(e){console.error(e)}try{o.length&&e.dropBlock.init(o)}catch(e){console.error(e)}try{i.length&&(e.citation=new f.a(i))}catch(e){console.error(e)}try{a.length&&(e.ERPAcceptance=new p.a(a).initialize())}catch(e){console.error(e)}try{r.length&&e.accordion.init(r)}catch(e){console.error(e)}try{s.length&&e.backToTop.init(s)}catch(e){console.error(e)}try{l.length&&(e.registration=new m.a(l))}catch(e){console.error(e)}try{c.length&&e.multiSearch.init(c)}catch(e){console.error(e)}try{d.length&&(e.tableOfContent=new g.a(d))}catch(e){console.error(e)}try{h.length&&(e.shibboleth=new v.a(h))}catch(e){console.error(e)}Y(e),Z(e),te(e)}}}]);
//# sourceMappingURL=vendors~lazy-imports-c2a1dddd2e4b2fba3ea0.js.map