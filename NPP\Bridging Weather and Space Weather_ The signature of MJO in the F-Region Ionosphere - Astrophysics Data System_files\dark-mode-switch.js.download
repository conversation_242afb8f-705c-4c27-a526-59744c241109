define(["analytics"],function(n){var e,t,a="darkSwitch",d=function(t){try{localStorage.setItem(a,t)}catch(t){}},o=function(t){document.body.setAttribute("data-theme","dark"),e.classList.add("darkModeOn"),e.set<PERSON>ttribute("title","Turn off dark mode"),t&&d("on")},r=function(t){document.body.removeAttribute("data-theme"),e.classList.remove("darkModeOn"),e.setAttribute("title","Turn on dark mode"),t&&d("off")},i=function(t,e){n("send","event","uitheme",t,e)};(e=document.getElementById("darkSwitch"))&&(e.classList.remove("hidden"),((t=(()=>{try{return localStorage.getItem(a)}catch(t){return null}})())?"on"!==t?r:o:window.matchMedia("(prefers-color-scheme: dark)").matches?o:r)(!1),e.addEventListener("click",function(){e.classList.contains("darkModeOn")?(r(!0),i("appSetting","light")):(o(!0),i("appSetting","dark"))}))});
//# sourceMappingURL=dark-mode-switch.js.map