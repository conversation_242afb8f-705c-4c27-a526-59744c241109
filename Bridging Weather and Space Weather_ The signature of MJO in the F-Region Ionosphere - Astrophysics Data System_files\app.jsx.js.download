define(["underscore","react","prop-types"],function(r,c,e){function t(){return c.createElement("h3",{className:"s-right-col-widget-title"},"No Sources Found")}function a(){return c.createElement("div",{className:"text-center text-muted"},c.createElement("i",{className:"fa fa-spinner fa-spin fa-2x","aria-hidden":"true"}),c.createElement("span",{className:"sr-only"},"Loading"))}function n(e){var t=e.items,n=e.onClick;return c.createElement("div",{className:"resources__full__list"},c.createElement("div",{className:"resources__header__row"},c.createElement("i",{className:"fa fa-file-text-o","aria-hidden":"true"}),c.createElement("div",{className:"resources__header__title"},"full text sources")),r.map(t,function(a,e){return c.createElement("div",{className:"resources__content",key:e},c.createElement("div",{className:"resources__content__title"},e),c.createElement("div",{className:"resources__content__links"},r.map(a,function(e,t){return c.createElement("span",{key:e.type+t},c.createElement("a",{href:e.url,target:"_blank",rel:"noopener",onClick:function(){return n("ftl",e)},title:"".concat(e.description," ").concat(e.open?"OPEN ACCESS":"INSTITUTION"===e.type?"":"SIGN IN REQUIRED"),className:"resources__content__link ".concat(e.open?"unlock":"")},c.createElement("span",{className:"sr-only"},e.description),"PDF"===e.type&&c.createElement("i",{className:"fa fa-file-pdf-o","aria-hidden":"true"}),"HTML"===e.type&&c.createElement("i",{className:"fa fa-file-text","aria-hidden":"true"}),"SCAN"===e.type&&c.createElement("i",{className:"fa fa-file-image-o","aria-hidden":"true"}),"INSTITUTION"===e.type&&c.createElement("i",{className:"fa fa-university","aria-hidden":"true"})),t<a.length-1&&c.createElement("div",{className:"resources__content__link__separator"},"|"))})))}))}function s(e){var t=e.items,a=e.onClick;return c.createElement("div",{className:"resources__data__list"},c.createElement("div",{className:"resources__header__row"},c.createElement("i",{className:"fa fa-database"}),c.createElement("div",{className:"resources__header__title"},"data products")),c.createElement("div",{className:"resources__content"},r.map(t,function(e){return c.createElement("a",{key:e.name,href:e.url,target:"_blank",rel:"noopener",onClick:function(){return a("data",e)},title:e.description,className:"resources__content__link"},e.name," (",e.count,")")})))}function l(e){return c.createElement("div",null,e.loading&&c.createElement(a,null),e.noResults&&!e.loading&&c.createElement(t,null),!e.loading&&!e.hasError&&c.createElement(c.Fragment,null,c.createElement("div",{className:"resources__container"},!r.isEmpty(e.fullTextSources)&&c.createElement(n,{items:e.fullTextSources,onClick:e.onLinkClick})),c.createElement("div",{className:"resources__container"},!r.isEmpty(e.dataProducts)&&c.createElement(s,{items:e.dataProducts,onClick:e.onLinkClick}))))}n.propTypes={items:e.array,onClick:e.func},n.defaultProps={items:[],onClick:function(){}},s.propTypes={items:e.array,onClick:e.func},s.defaultProps={items:[],onClick:function(){}};return l.propTypes={loading:e.bool,noResults:e.bool,fullTextSources:e.array,dataProducts:e.array,onLinkClick:e.func,hasError:e.string},l.defaultProps={loading:!1,noResults:!1,fullTextSources:[],dataProducts:[],onLinkClick:function(){},hasError:null},l});
//# sourceMappingURL=app.jsx.js.map