// For license information, see `https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RC370d2f73bced496faf6bcde1a97ecc39-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RC370d2f73bced496faf6bcde1a97ecc39-source.min.js', "window.uetq=window.uetq||[],window.uetq.push(\"consent\",\"update\",{ad_storage:_satellite.getVar(\"CJS-DataElement-Return-Google-Osano-ad_stoarage-Consent-Status-For-Bing-UET-Consent-Mode\")});");