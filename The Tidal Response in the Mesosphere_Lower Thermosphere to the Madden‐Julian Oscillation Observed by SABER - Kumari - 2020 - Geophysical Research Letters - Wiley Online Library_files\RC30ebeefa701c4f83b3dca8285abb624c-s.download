// For license information, see `https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RC30ebeefa701c4f83b3dca8285abb624c-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RC30ebeefa701c4f83b3dca8285abb624c-source.min.js', "window.LogRocket&&LogRocket.track(\"pageView\",{pageTemplate:adobeDataLayer.getState(\"page.secondary-section\"),pageSubtype:adobeDataLayer.getState(\"page.tertiary-section\"),pageGroup:adobeDataLayer.getState(\"page.primary-section\"),website:_satellite.getVar(\"ACDL|Page|Site Hub\"),articleDoi:_satellite.getVar(\"ACDL|Content|Item Doi\"),pubTitle:_satellite.getVar(\"ACDL|Content|Series Title\")});");