function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}define(["backbone","underscore","js/components/generic_module","js/components/pubsub_key"],function(t,l,e,n){var h=/\s+/;return e.extend({className:"PubSub",initialize:function(t){this._issuedKeys={},this.strict=!0,this.handleErrors=!0,this._errors={},this.errWarningCount=10,l.extend(this,l.pick(t,["strict","handleErrors","errWarningCount"])),this.pubSubKey=n.newInstance({creator:{}}),this._issuedKeys[this.pubSubKey.getId()]=this.pubSubKey.getCreator(),this.running=!0,this.debug=!1},start:function(){this.publish(this.pubSubKey,this.OPENING_GATES),this.publish(this.pubSubKey,this.OPEN_FOR_BUSINESS),this.running=!0},destroy:function(){this.publish(this.pubSubKey,this.CLOSING_GATES),this.off(),this.publish(this.pubSubKey,this.CLOSED_FOR_BUSINESS),this.running=!1,this._issuedKeys={}},subscribe:function(t,e,r){if(!this.isRunning())throw new Error("PubSub has been closed, ignoring futher requests");if(this._checkKey(t,e,r),l.isUndefined(e))throw new Error("You tried to subscribe to undefined event. Error between chair and keyboard?");this.on(e,r,t),-1==e.indexOf(t.getId())&&this.on(e+t.getId(),r,t)},subscribeOnce:function(t,e,r){if(this._checkKey(t,e,r),l.isUndefined(e))throw new Error("You tried to subscribe to undefined event. Error between chair and keyboard?");this.once(e,r,t),-1==e.indexOf(t.getId())&&this.once(e+t.getId(),r,t)},unsubscribe:function(t,e,r){this._checkKey(t,e,r);var n=t;if(e&&r)this.off(e,r,n),this.off(e+t.getId(),r,n);else if(e||r)this.off(e,r,n),this.off(e+t.getId(),r,n);else{for(var i,s,o,u,h=l.keys(this._events),c=[],a=0,b=h.length;a<b;a++)if(e=h[a],i=this._events[e])for(u=0,o=i.length;u<o;u++)(s=i[u]).context===n&&c.push({name:e,event:s});l.each(c,function(t){this.off(t.name,t.event.callback,t.event.context)},this)}},publish:function(){if(this.isRunning()){this._checkKey(arguments[0]);var t=Array.prototype.slice.call(arguments,1);if(0==t.length||l.isUndefined(t[0]))throw new Error("You tried to trigger undefined event. Error between chair and keyboard?");return t.push(arguments[0]),this.handleErrors?this.triggerHandleErrors.apply(this,t):this.trigger.apply(this,t)}console.error("PubSub has been closed, ignoring futher requests")},getCurrentPubSubKey:function(){return this.pubSubKey},getPubSubKey:function(){var t=n.newInstance({creator:this.pubSubKey});if(this.strict){if(this._issuedKeys[t.getId()])throw Error("The key with id",t.getId(),"has been already registered!");this._issuedKeys[t.getId()]=t.getCreator()}return t},isStrict:function(){return this.strict},_checkKey:function(t,e,r){if(this.strict){if(l.isUndefined(t))throw new Error("Every request must be accompanied by PubSubKey");if(!(t instanceof n))throw new Error("Key must be instance of PubSubKey. (If you are trying to pass context, you can't do that. Instead, wrap your callback into: _.bind(callback, context))\nPerhaps the PubSub you are using is the non-protected version?");if(!this._issuedKeys.hasOwnProperty(t.getId()))throw new Error("Your key is not known to us, sorry, you can't use this queue.");if(this._issuedKeys[t.getId()]!==t.getCreator())throw new Error("Your key has wrong identity, sorry, you can't use this queue.")}},triggerHandleErrors:function(t){var e,r;return this._events&&((t,e,r,n)=>{if(r){if("object"===_typeof(r)){for(var i in r)t[e].apply(t,[i,r[i]].concat(n));return!1}if(h.test(r)){for(var s=r.split(h),o=0,u=s.length;o<u;o++)t[e].apply(t,[s[o]].concat(n));return!1}}return!0})(this,"trigger",t,e=Array.prototype.slice.call(arguments,1))&&(t=this._events[t],r=this._events.all,t&&this.triggerEvents(t,e),r)&&this.triggerEvents(r,arguments),this},triggerEvents:function(t,e){var r,n=-1,i=t.length,s=e[0],o=e[1],u=e[2];switch(e.length){case 0:for(;++n<i;)try{(r=t[n]).callback.call(r.ctx)}catch(t){this.handleCallbackError(t,r,e)}return;case 1:for(;++n<i;)try{(r=t[n]).callback.call(r.ctx,s)}catch(t){this.handleCallbackError(t,r,e)}return;case 2:for(;++n<i;)try{(r=t[n]).callback.call(r.ctx,s,o)}catch(t){this.handleCallbackError(t,r,e)}return;case 3:for(;++n<i;)try{(r=t[n]).callback.call(r.ctx,s,o,u)}catch(t){this.handleCallbackError(t,r,e)}return;default:for(;++n<i;)try{(r=t[n]).callback.apply(r.ctx,e)}catch(t){this.handleCallbackError(t,r,e)}}},handleCallbackError:function(t,e,r){if(console.warn("[PubSub] Error: ",e,r,t.message,t.stack),this.debug)throw t;console.warn(t.stack);var n=e.ctx.getId(),n=this._errors[n]=(this._errors[n]||0)+1;n%this.errWarningCount==0&&this.publish(this.pubSubKey,this.BIG_FIRE,n,t,e,r)},isRunning:function(){return this.running}})});
//# sourceMappingURL=default_pubsub.js.map