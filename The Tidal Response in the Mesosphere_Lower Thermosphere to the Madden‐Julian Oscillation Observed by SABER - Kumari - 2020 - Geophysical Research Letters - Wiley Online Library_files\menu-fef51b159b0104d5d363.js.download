(window.webpackJsonp=window.webpackJsonp||[]).push([[31],{302:function(e,t){function menubar(e,t){this.$id=$("#"+e),this.$rootItems=this.$id.children("li"),this.$items=this.$id.find(".menu-item"),this.$parents=this.$id.find(".menu-parent"),this.$allItems=this.$parents.add(this.$items),this.$activeItem=null,this.vmenu=t,this.bChildOpen=!1,this.keys={tab:9,enter:13,esc:27,space:32,left:37,up:38,right:39,down:40},this.bindHandlers()}menubar.prototype.bindHandlers=function(){var e=this;this.$allItems.keydown((function(t){return e.handleKeyDown($(this),t)})),this.$allItems.keypress((function(t){return e.handleKeyPress($(this),t)})),this.$allItems.focus((function(t){return e.handleFocus($(this),t)})),$(document).click((function(t){return e.handleDocumentClick(t)}))},menubar.prototype.handleFocus=function(e,t){if(null==this.$activeItem)this.$activeItem=e;else if(e[0]!==this.$activeItem[0])return!0;var n=this.$activeItem.parentsUntil("div").filter("li");if(this.$allItems.removeClass("menu-focus menu-focus-checked"),this.$activeItem.is(".checked")?this.$activeItem.addClass("menu-focus-checked"):this.$activeItem.addClass("menu-focus"),n.addClass("menu-focus"),1===this.vmenu)if(1===this.bChildOpen){e.parent().is("#menubar")&&"true"===e.attr("aria-haspopup")&&e.addClass("hover").children("ul").attr("aria-hidden","false")}else this.vmenu=!1;return!0},menubar.prototype.handleKeyDown=function(e,t){var n=e.parent();if(t.altKey||t.ctrlKey)return!0;switch(t.keyCode){case this.keys.tab:this.$id.find("ul").attr("aria-hidden","true"),this.$allItems.removeClass("menu-focus hover"),this.$activeItem=null,this.bChildOpen;break;case this.keys.esc:if(!(UX.menu.$toggle.length>0))return n.is("#menubar")?(e.removeClass("hover").children("ul").first().attr("aria-hidden","true"),e.focus()):(this.$activeItem=n.parent(),e.removeClass("hover"),this.bChildOpen=!1,this.$activeItem.focus(),n.attr("aria-hidden","true")),t.stopPropagation(),!1;UX.menu.on.hide(),UX.menu.$toggle.focus();case this.keys.enter:case this.keys.space:return e.parent().is("#menubar")?(e.addClass("hover").children("ul").first().attr("aria-hidden","false"),this.bChildOpen=!0,this.processMenuChoice(e),e.find("ul > li:first").find("a").focus()):(this.processMenuChoice(e),this.$allItems.removeClass("menu-hover menu-hover-checked hover"),this.$allItems.removeClass("menu-focus menu-focus-checked hover"),this.$id.find("ul").not("#menubar").attr("aria-hidden","true"),this.$activeItem=null),t.stopPropagation(),!1;case this.keys.left:return 1===this.vmenu&&n.is("#menubar")?this.$activeItem=this.moveUp(e):this.$activeItem=this.moveToPrevious(e),this.$activeItem.focus(),t.stopPropagation(),!1;case this.keys.right:return 1===this.vmenu&&n.is("#menubar")?this.$activeItem=this.moveDown(e):this.$activeItem=this.moveToNext(e),this.$activeItem.focus(),t.stopPropagation(),!1;case this.keys.up:return 1===this.vmenu&&n.is("#menubar")?this.$activeItem=this.moveToPrevious(e):this.$activeItem=this.moveUp(e),this.$activeItem.focus(),t.stopPropagation(),!1;case this.keys.down:return 1===this.vmenu&&n.is("#menubar")?this.$activeItem=this.moveToNext(e):this.$activeItem=this.moveDown(e),this.$activeItem.focus(),t.stopPropagation(),!1}return!0},menubar.prototype.moveToNext=function(e){var t,n=e.parent(),o=n.children("li"),i=o.length,s=o.index(e),a=null;if(n.is("#menubar"))a=i-1>s?e.next():o.first(),"true"===e.attr("aria-haspopup")&&(e.removeClass("hover"),"false"===(t=e.children("ul").first()).attr("aria-hidden")&&(t.attr("aria-hidden","true"),this.bChildOpen=!0)),e.removeClass("menu-focus"),"true"===a.attr("aria-haspopup")&&1===this.bChildOpen&&(a.addClass("hover"),(t=a.children("ul").first()).attr("aria-hidden","false"));else if("true"===e.attr("aria-haspopup"))e.addClass("hover"),a=(t=e.children("ul").first()).children("li").first(),t.attr("aria-hidden","false"),this.bChildOpen=!0;else{if(1===this.vmenu)return e;var r=null,l=null;(r=e.parentsUntil("div").filter("ul").not("#menubar")).attr("aria-hidden","true"),r.find("li").removeClass("menu-focus hover"),r.last().parent().removeClass("menu-focus hover"),l=r.last().parent(),"true"===(a=(s=this.$rootItems.index(l))<this.$rootItems.length-1?l.next():this.$rootItems.first()).attr("aria-haspopup")&&a.children("ul").length>0&&(a.addClass("hover"),t=a.children("ul").first(),a=t.children("li").first(),t.attr("aria-hidden","false"),this.bChildOpen=!0)}return a},menubar.prototype.moveToPrevious=function(e){var t,n=e.parent(),o=n.children("li"),i=(o.length,o.index(e)),s=null;if(n.is("#menubar"))s=i>0?e.prev():o.last(),"true"===e.attr("aria-haspopup")&&"false"===(t=e.removeClass("hover").children("ul").first()).attr("aria-hidden")&&(t.attr("aria-hidden","true"),this.bChildOpen=!0),e.removeClass("menu-focus hover"),"true"===s.attr("aria-haspopup")&&1===this.bChildOpen&&(s.addClass("hover"),(t=s.children("ul").first()).attr("aria-hidden","false"));else{var a=n.parent(),r=a.parent();1!==this.vmenu&&r.is("#menubar")?(n.attr("aria-hidden","true"),e.removeClass("menu-focus hover"),a.removeClass("menu-focus hover"),(s=(i=this.$rootItems.index(a))>0?a.prev():this.$rootItems.last()).addClass("menu-focus hover"),"true"===s.attr("aria-haspopup")&&(t=null,s.children("ul").length>0&&((t=s.addClass("hover").children("ul").first()).attr("aria-hidden","false"),this.bChildOpen=!0,s=t.children("li").first()))):(s=n.parent(),n.attr("aria-hidden","true"),e.removeClass("menu-focus hover"),s.removeClass("hover"),1===this.vmenu&&(this.bChildOpen=!1))}return s},menubar.prototype.moveDown=function(e,t){var n=e.parent(),o=n.children("li").not(".separator"),i=o.length,s=o.index(e),a=null,r=null;if(n.is("#menubar"))return"true"!==e.attr("aria-haspopup")?e:(a=(r=e.addClass("hover").children("ul").first()).children("li").first(),r.attr("aria-hidden","false"),this.bChildOpen=!0,a);if(t){var l=!1,c=s+1;for(c===i&&(c=0);c!==s;){if(o.eq(c).html().charAt(0).toLowerCase()===t){l=!0;break}(c+=1)===i&&(c=0)}return 1===l?(a=o.eq(c),e.removeClass("menu-focus menu-focus-checked"),a):e}return a=i-1>s?o.eq(s+1):o.first(),e.removeClass("menu-focus menu-focus-checked"),a},menubar.prototype.moveUp=function(e){var t=e.parent(),n=t.children("li").not(".separator"),o=(n.length,n.index(e)),i=null;return t.is("#menubar")?e:(i=o>0?n.eq(o-1):n.last(),e.removeClass("menu-focus menu-focus-checked"),i)},menubar.prototype.handleKeyPress=function(e,t){if(t.altKey||t.ctrlKey||t.shiftKey)return!0;switch(t.keyCode){case this.keys.tab:return!0;case this.keys.esc:case this.keys.enter:case this.keys.space:case this.keys.up:case this.keys.down:case this.keys.left:case this.keys.right:return t.stopPropagation(),!1;default:var n=String.fromCharCode(t.which);return this.$activeItem=this.moveDown(e,n),this.$activeItem.focus(),t.stopPropagation(),!1}return!0},menubar.prototype.handleDocumentClick=function(e){return this.$id.find("ul").not("#menubar").attr("aria-hidden","true"),this.$allItems.removeClass("menu-focus menu-focus-checked hover"),this.$activeItem=null,!0},menubar.prototype.processMenuChoice=function(e){var t=(e.parent().attr("id"),e.attr("id"),e.data("href"));if(null!=t)if(t.toLowerCase().indexOf("legacy.")>0){window.open(t,"_blank").focus()}else window.location.href=t;var n=e.find("a");null!=n&&(window.location.href=n[0].href)},window.menubar=menubar},391:function(e,t,n){"use strict";n.r(t);var o,i,s;n(302);o=$("body"),i=$(window),s={$toggle:$('[data-toggle="nav"]'),$target:null,revers:!1,lastItem:null,items:null,vPort:["screen-sm"],isMobile:!1,menuHeaderMainTitle:"",redirectPath:!1,displayedNestedItems:!1,nestedMenu:void 0,listPositionVars:{mainScrollingList:$("#menubar"),inTimeoutTiming:null,outTimeoutTiming:null,mainScrollingListHeight:!1,mobileTransitionTime:400,bodyTransitionPropertyName:"transform",bodyTransitionElapsedTime:.25},targetOffset:70,init:function init(){$(".main-nav").each((function(e){s.$target=$(this);var t=s.$target.find(".menu-header");s.menuHeaderMainTitle=t.text(),s.redirectPath=t.data("redirect-to"),s.check.viewPort(e),s.responsive(e)})),$(".main-nav__toggle__controller").attr("aria-expanded","false"),s.items=$(".main-nav").find("a, button, input"),s.control(),s.additionalControl()},responsive:function responsive(e){$(document).on(s.vPort[e]+"-on",(function(){s.isMobile=!0,$(".main-nav").each((function(){$(this).find(".drawer__nav").data("ctrl-res")===s.vPort[e]&&($(this).addClass("menu--res"),$(".main-nav__toggle__controller").addClass("main-nav__toggle__controller__res"),s.on.rebuild.responsive())}))})),$(document).on(s.vPort[e]+"-off",(function(){s.isMobile=!1,$(".main-nav").each((function(){$(this).find(".drawer__nav").data("ctrl-res")===s.vPort[e]&&($(this).removeClass("menu--res"),$(".main-nav__toggle__controller").removeClass("main-nav__toggle__controller__res"),s.on.rebuild.original())}))}))},control:function control(){o.on("click",'[data-toggle="nav"]',(function(e){e.preventDefault(),s.$toggle=$(this),s.$target=$("#"+s.$toggle.data("target")),s.items=s.$target.find("a, button, input"),s.items.each((function(e){e===s.items.length-1&&(s.lastItem=$(this))})),s.on.show(e)})),o.on("click",'.js--open[data-toggle="nav"]',(function(e){e.preventDefault(),s.on.hide(e)})),o.on("click",(function(e){$(e.target).closest(".dropdown__toggle").length||$("a.dropdown__toggle.hover").removeClass("hover"),$(e.target).hasClass("icon-arrow_r")||$(e.target).parents(".main-nav__toggle__controller")||$(e.target).parents(".main-nav.menu--res").length||!$('.lock-screen[data-active="menu"]').length||s.on.hide(e)})),o.on("click",'.main-nav a:not([data-toggle="nav"])',(function(e){var t=$(this).attr("href");if("#"===t)e.preventDefault();else if(t&&-1!==t.indexOf("#")){e.preventDefault();var n=t.split("#")[1],o=$("#"+n);s.on.hide(),o.length?$("html, body").animate({scrollTop:o.offset().top-s.targetOffset},50,(function(){s.on.hide()})):window.location.href=t}})),o.on("click touchend",'[data-toggle="dropdown"]',(function(e){e.preventDefault(),s.nestedMenu=$(this),s.on.nested(e)})),o.on("click",".menu-header",(function(e){e.preventDefault(),s.isMobile&&s.on.hideNested(!0),e.stopPropagation()})),o.on("keydown",'.drawer__nav, [data-toggle="nav"]',(function(e){s.isMobile&&9===(e.keyCode||e.which)&&(s.revers?s.on.tabRevers():s.on.tab())})),o.get(0).addEventListener("transitionend",s.on.listPosition.handleHeightSet),i.on("resize",s.on.listPosition.handleResize),i.on("keyup",(function(e){s.revers=!(!e.shiftKey||9!==e.keyCode),s.isMobile||(e.keyCode||e.which,s.on.tabDesktop(e,s.revers,e.keyCode),27===(e.keyCode||e.which)&&s.on.escDesktop(e))}))},additionalControl:function additionalControl(){},on:{show:function show(){void 0!==UX.controller&&UX.controller.check(),o.addClass("lock-screen").attr("data-active","menu"),s.$toggle.addClass("js--open"),s.$target.closest(".main-nav").css("top",i.scrollTop()),$(".coolBar--res").removeClass("trans").css("top",i.scrollTop()+$("header").height()),$(".loi__banner.loi--res").css("top",i.scrollTop()+$("header").height()),$(".main-nav__toggle__controller").attr("aria-expanded","true"),s.$target.attr("style","display: block !important"),s.$toggle.focus(),s.on.showAdditional()},showAdditional:function showAdditional(){},showNested:function showNested(){s.on.listPosition.start("in"),s.nestedMenu.next(".dropdown__menu").toggleClass("sub-menu__opened"),s.nestedMenu.parent().toggleClass("menu-parent__opened"),s.nestedMenu.find("i").toggleClass("opposite-arrow"),s.nestedMenu.parent().siblings("li").toggleClass("prev-items"),s.nestedMenu.toggleClass("prev-items"),$("[data-toggle='nav']").addClass("js--nested-open");var e=s.nestedMenu.html();$(".menu-header").length&&$(".menu-header").html(e),s.displayedNestedItems=!0,s.on.showNestedExtra()},showNestedExtra:function showNestedExtra(){},hideNested:function hideNested(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=$(".menu-header");void 0!==s.nestedMenu?(s.on.listPosition.start("out"),e=s.nestedMenu.parent().parent(),s.nestedMenu=e.parent().find(".dropdown__toggle"),e.find(".sub-menu__opened").removeClass("sub-menu__opened"),e.find(".menu-parent__opened").removeClass("menu-parent__opened"),e.find(".prev-items").removeClass("prev-items"),$("[data-toggle='nav']").removeClass("js--nested-open"),e.hasClass("sub-menu__opened")?n.find("span").html(s.nestedMenu.find("span").html()):s.redirectPath&&t&&!s.displayedNestedItems?document.location=s.redirectPath:(n.html(s.menuHeaderMainTitle),s.displayedNestedItems=!1)):s.nestedMenu=n,s.on.hideNestedExtra()},hideNestedExtra:function hideNestedExtra(){},hide:function hide(){s.isMobile&&s.on.hideNested(),s.$toggle.removeClass("js--open"),$(".main-nav, header").css("top",0),$(".coolBar--res").addClass("trans").css("top",$("header").height()),$(".loi__banner.loi--res").css("top",$("header").height()),o.removeAttr("data-active").removeClass("lock-screen"),$(".main-nav__toggle__controller").attr("aria-expanded","false"),s.on.listPosition.reset()},escape:function escape(){s.on.hide(),s.$toggle.focus()},tab:function tab(){s.$toggle.off(),s.lastItem.off().on("focusout",(function(){s.$toggle.focus()}))},tabRevers:function tabRevers(){s.lastItem.off(),s.$toggle.off().on("focusout",(function(){s.lastItem.focus()}))},tabDesktop:function tabDesktop(e,t,n){var o=$(e.target),i=o.closest(".dropdown__menu"),s=$(".dropdown__toggle");o.closest("ul").length||(s.removeClass("hover"),$(".dropdown").removeClass("js--open")),o.hasClass("dropdown__toggle")?(0===i.length||!i.hasClass("js--open")&&0===i.find(".js--open").length?(s.removeClass("hover"),$(".dropdown__menu").removeClass("js--open"),o.closest("a").addClass("hover"),o.siblings(".dropdown__menu").addClass("js--open")):(i.find(".hover").removeClass("hover"),i.find(".js--open").removeClass("js--open"),o.closest("a").addClass("hover"),o.siblings(".dropdown__menu").addClass("js--open")),13==n&&$(e.target).closest("a").addClass("hover")):o.closest("ul").find(".hover").removeClass("hover")},escDesktop:function escDesktop(e){$(e.target).closest(".dropdown__toggle").removeClass("hover")},rebuild:{responsive:function responsive(){s.on.hide()},original:function original(){s.on.hide(),s.$target.show()}},nested:function nested(e){s.isMobile&&s.on.showNested()},listPosition:{start:function start(e){if(s.nestedMenu&&e&&s.isMobile){var t=s.nestedMenu.first();if(t.closest("#menubar").length)switch(e){case"in":var n=t.closest(".dropdown__menu, #menubar"),o=t.next(".dropdown__menu");s.on.listPosition.handle({prev:n,next:o,parent:n,sub:o},"in");break;case"out":var i=t.hasClass("menu-header"),a=t.next(".dropdown__menu"),r=t.closest(".dropdown__menu, #menubar");s.on.listPosition.handle({prev:a,next:r,parent:r,sub:a},"out",i)}}},handle:function handle(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,o="in"===t?0:s.listPositionVars.mobileTransitionTime/3,i="in"===t&&s.listPositionVars.inTimeoutTiming?s.listPositionVars.inTimeoutTiming:"out"===t&&s.listPositionVars.outTimeoutTiming?s.listPositionVars.outTimeoutTiming:s.listPositionVars.mobileTransitionTime/3;if(s.on.listPosition.addDataToElem(e.prev),!0!==n&&(s.on.listPosition.handleScrollBarHide([e.prev,e.next]),e.next.length)){var a=s.on.listPosition.getLastPosition(e.next);setTimeout((function(){s.on.listPosition.scrollTo(e,t,a||0,o)}),i)}},addDataToElem:function addDataToElem(e){if(e.length){var t=e.get(0).getBoundingClientRect();e.attr("data-last-pos",JSON.stringify({bottom:Math.floor(t.bottom),scrollVal:Math.floor(e.first().scrollTop())}))}},getLastPosition:function getLastPosition(e){return e.attr("data-last-pos")&&JSON.parse(e.attr("data-last-pos")).scrollVal},handleScrollBarHide:function handleScrollBarHide(e){e.forEach((function(e){e.addClass("scrollbar-hidden"),e.attr("data-transitionend-listener-added")||(e.attr("data-transitionend-listener-added","true"),e.on("transitionend",(function(t){e.removeClass("scrollbar-hidden")})))}))},scrollTo:function scrollTo(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=s.listPositionVars.mainScrollingListHeight-(e.parent.innerHeight()-e.parent.height()),a=s.listPositionVars.mainScrollingListHeight-(e.sub.innerHeight()-e.sub.height());UX.utils.isSafari()&&(a-=180),"in"===t?(e.parent.css({"max-height":i,height:i,"overflow-y":"hidden"}).attr("data-list-pos-corrected","true"),e.sub.css({"max-height":a,height:a,"overflow-y":"auto"}).attr("data-list-pos-corrected","true"),e.parent.length&&e.parent.animate({scrollTop:0},{duration:Math.abs(n-e.parent.scrollTop())>125?0:o}),e.sub.animate({scrollTop:n},{duration:Math.abs(n-e.parent.scrollTop())>125?0:o})):"out"===t&&(e.parent.css({"max-height":i,height:i,"overflow-y":"auto"}),e.sub.css({"max-height":"",height:"","overflow-y":""}).removeAttr("data-list-pos-corrected"),e.parent.length&&e.parent.animate({scrollTop:n},{duration:Math.abs(n-e.parent.scrollTop())>125?0:o}))},handleResize:function handleResize(e){s.$toggle.hasClass("js--open")&&s.on.listPosition.handleHeightSet({},{skipCheck:!0})},handleHeightSet:function handleHeightSet(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(s.$toggle.hasClass("js--open")&&e.elapsedTime===s.listPositionVars.bodyTransitionElapsedTime&&e.propertyName===s.listPositionVars.bodyTransitionPropertyName||!0===t.skipCheck)&&(s.listPositionVars.mainScrollingListHeight=window.outerHeight-s.listPositionVars.mainScrollingList[0].getBoundingClientRect().top,s.listPositionVars.mainScrollingList.find('[data-list-pos-corrected="true"]').add(s.listPositionVars.mainScrollingList).css({height:s.listPositionVars.mainScrollingListHeight,"max-height":s.listPositionVars.mainScrollingListHeight}))},reset:function reset(){s.listPositionVars.mainScrollingList.find("[data-last-pos], [data-list-pos-corrected]").add(s.listPositionVars.mainScrollingList).removeAttr("data-last-pos data-list-pos-corrected").css({"max-height":"",height:"","overflow-y":""}).animate({scrollTop:0},0)}}},check:{viewPort:function viewPort(e){s.$target.find(".drawer__nav").attr("data-ctrl-res")&&(s.vPort[e]=s.$target.find(".drawer__nav").attr("data-ctrl-res"))}}},UX.menu=s,function(e){var t,n=$("body"),o=!1,i=document.querySelectorAll(".menu-item"),s=null,a=null,r=e.menu;r.init=function(){var e=$(".main-nav");e.each((function(e){r.$target=$(this),r.check.viewPort(e),r.responsive(e)})),r.items=e.find("a, button, input"),r.control(),r.additionalControl(),s.init();var t=document.querySelector('.main-nav a[href="#menuBook"]');null==t||t.remove(),null==i||i.forEach((function(e){var t=null==e?void 0:e.querySelector('a[href$="-pane"]');null==t||t.removeAttribute("aria-selected")}))},r.on.tabDesktop=function(){},r.on.tabReversDesktop=function(){},r.on.escDesktop=function(){},t='[data-toggle="dropdown"]:not(.expandable-list__toggle)',s={vPort:"screen-sm",init:function init(){s.prepare(),s.switchMobileFlag(),s.accessibility.control()},prepare:function prepare(){$("nav").data("flag-open",!1)},accessibility:{_flagReverseOrder:!1,control:function control(){var i=null,r=null,l=null,c=null,d=null,u=null,h=null,m=null,p=null,f=null;$(document).on("click",t,(function(e,t){o||(i=$(this),a=i.hasClass("js--open"),s.$controller=i,s.findTarget(),a&&!t?s.on.hide(e):s.on.show(e),s.$container.data("flag-open",!1),s.$container.data("flag-change-focus",!1),e.originalEvent||i.focus())})).on("focus",'[role="menuitem"]',(function(e,t){i=$(this),s.$container=$(this).closest("nav"),s.itemsCollection=s.$container.find('[role="menuitem"]'),r=i.closest("[role=menu]").attr("id"),s._menubarContext_itemsCollection=s.itemsCollection.filter("[role=menubar] > li > [role=menuitem]"),s._currentContext_isSub=s._menubarContext_itemsCollection.index(i)<0,s._currentContext_isSub&&$(this).closest(".hubpage-menu__nav").length?s._currentContext_itemsCollection=s.itemsCollection.filter("#"+r+"  li  [role=menuitem]"):s._currentContext_isSub?s._currentContext_itemsCollection=s.itemsCollection.filter("#"+r+" > li > [role=menuitem]"):s._currentContext_itemsCollection=s._menubarContext_itemsCollection,s._currentIndex=s._currentContext_itemsCollection.index(i),s.accessibility._flagReverseOrder="rtl"===i.css("direction")})).on("focus",t,(function(){})).on("keydown",'[role="menuitem"]',(function(n){var a={DOWN:40,END:35,HOME:36,LEFT:37,RIGHT:39,UP:38,RETURN:13,SPACE:32,TAB:9,ESCAPE:27};function isPrintableCharacter(e){return 1===e.length&&e.match(/\S/)}if(l=!0,Object.values(a).indexOf(n.keyCode)>-1||(l=!1),c=!1,d=!1,i=$(this),u=!1,h=i.is(t),i.parents("[role=menu]").length>1&&(u=!0),m=null,p=null,f=s._currentContext_isSub,o)switch(n.keyCode){case a.LEFT:case a.ESCAPE:d=!1,i.parents("li").last().find("[role=menuitem]").focus(),m="close",l=!0;break;case a.RETURN:case a.SPACE:m="open",d=!0;break;case a.TAB:l=!1;break;case a.UP:!h&&f?i.parents("li").last().find("[role=menuitem]").focus():(s._currentContext_itemsCollection=s._menubarContext_itemsCollection,s._currentIndex=e.menubar._menubarContext_itemsCollection.index(i.parents("li").last().find("[role=menuitem]"))),m="prev";break;case a.END:m="end";break;case a.HOME:m="home";break;case a.DOWN:!h&&f?i.parents("li").last().find("[role=menuitem]").focus():(s._currentContext_itemsCollection=s._menubarContext_itemsCollection,s._currentIndex=e.menubar._menubarContext_itemsCollection.index(i.parents("li").last().find("[role=menuitem]"))),m="next";break;case a.RIGHT:h&&!f&&(d=!0,m="open");break;default:isPrintableCharacter(n.key)&&(m="find",p=[n.key],l=!0)}else switch(n.keyCode){case a.RETURN:case a.SPACE:m="open",d=!0;break;case a.TAB:l=!1;break;case a.UP:m="prev";break;case a.LEFT:s._currentContext_itemsCollection=s._menubarContext_itemsCollection,s._currentIndex=e.menubar._menubarContext_itemsCollection.index(i.parents("li").last().find("[role=menuitem]")),m="prev",(!h&&f&&u||f&&u&&h)&&(m="close");break;case a.END:m="end";break;case a.HOME:m="home";break;case a.RIGHT:h&&f?(d=!0,m="open"):(s._currentContext_itemsCollection=s._menubarContext_itemsCollection,s._currentIndex=e.menubar._menubarContext_itemsCollection.index(i.parents("li").last().find("[role=menuitem]")),m="next"),f&&!h&&(c=!0,d=!0);break;case a.DOWN:h&&!f?(d=!0,m="open"):m="next";break;default:isPrintableCharacter(n.key)&&(m="find",p=[n.key],l=!0)}s.$container.data("flag-open",c),s.$container.data("flag-change-focus",d),m&&s.accessibility.menuitem(i,m,p),l&&(n.stopPropagation(),n.preventDefault())})),$("#menubar").on("focusout",(function(e,t){$(e.relatedTarget).parents().is($(this))||$(this).find(".dropdown__toggle.js--open.hover").removeClass("js--open hover").attr("tabindex","-1").next(".dropdown__menu.js--open").attr("aria-hidden","true").removeClass("js--open")})),n.off("touchend",'[data-toggle="dropdown"]')},menuitem:function menuitem(e,n,o){var i=s._currentContext_itemsCollection.length,a=s._currentIndex,r={next:function next(e){this.goTo(a+1)},prev:function prev(e){this.goTo(a-1)},find:function find(e,t){},home:function home(){this.goTo(0)},end:function end(){this.goTo(i-1)},goTo:function goTo(e){e<0&&(e=i-1),e>=i&&(e=0),$(".main-nav").find(".is--current").removeClass("is--current"),s._currentContext_itemsCollection.eq(e).focus().parent("li").addClass("is--current"),s.closeInactiveSubMenu(e)},open:function open(e){e.is(t)?e.trigger("click",!0):e[0].click(),e.next().find("li:first-child a").focus()},close:function close(e){e.closest("[role=menu]").prev().focus().click()}};if(this._flagReverseOrder)switch(n){case"prev":n="next";break;case"next":n="prev";break;case"home":n="end";break;case"end":n="home"}if("string"==typeof n){if(void 0===r[n])throw new TypeError('No method named "'+n+'" found.');r[n](e,o)}}},switchMobileFlag:function switchMobileFlag(){$(document).on(s.vPort+"-on",(function(){o=!0})),$(document).on(s.vPort+"-off",(function(){o=!1}))},closeInactiveSubMenu:function closeInactiveSubMenu(e){var t="main-nav";s._currentContext_itemsCollection.eq(e).parent().parent().parent().is(".hubpage-menu__nav")&&(t="hubpage-menu__nav"),(s._currentContext_itemsCollection.eq(e).parent().parent().is(".menubar")||s._currentContext_itemsCollection.eq(e).parent().parent().parent().is(".hubpage-menu__nav"))&&$("."+t).find(".dropdown__toggle.js--open.hover").length&&$("."+t).find(".dropdown__toggle.js--open.hover").removeClass("js--open hover").attr("tabindex","-1").next(".dropdown__menu.js--open").attr("aria-hidden","true").removeClass("js--open")},findTarget:function findTarget(){var e,t=$("#"+s.$controller.attr("data-target"));t.length||(t=$(s.$controller.attr("href"))),!s.$controller.attr("data-target")&&s.$controller.attr("data-slide-target")&&(t=$(s.$controller.attr("data-slide-target"))),s.$section=t,s.$container=s.$section.closest("nav"),(e=s.$section.parents('[role="menu"]')).length?s.$parentSection=e:s.$parentSection=null},on:{show:function show(t){if(s.$controller.closest("[role=menu]").length||void 0!==e.controller&&e.controller.check(),s.$section){var i,a,r;if(s.$section.addClass("js--open"),s.$controller.addClass("js--open"),s.$controller.addClass("hover"),n.attr("data-active","menubar"),o&&n.addClass("lock-screen"),s._currentContext_itemsCollection===s._menubarContext_itemsCollection)null===(i=s._menubarContext_itemsCollection)||void 0===i||i.attr("tabindex",-1),null===(a=s._menubarContext_itemsCollection)||void 0===a||a.eq(0).attr("tabindex",0),null===(r=s.$controller)||void 0===r||r.attr("tabindex",0);s.$container.data("flag-change-focus")&&setTimeout((function(){s.$section.find("[role=menuitem]").first().focus()}),15),s.$controller.attr({"aria-expanded":"true"}),s.$section.removeAttr("aria-hidden")}},hide:function hide(){var e=null;s.$section&&(s.$controller.removeClass("js--open"),s.$section.removeClass("js--open"),s.$controller.removeClass("hover"),s.$controller.attr({"aria-expanded":"false"}),s.$section.attr({"aria-hidden":"true"}),s.$parentSection&&s.$parentSection.length?((e=$("[data-target="+s.$parentSection.attr("id")+"]")).length||(e=$("[href=#"+s.$parentSection.attr("id")+"]")),s.$controller=e,s.findTarget()):"menubar"===n.attr("data-active")&&(n.removeAttr("data-active"),o&&n.removeClass("lock-screen")))}}},e.menubar=s,i.forEach((function(e){"none"===getComputedStyle(e).display&&e.remove()}))}(UX),UX.menu.additionalControl=function(){Array.from(document.querySelectorAll(".menu-parent")).map((function(e){var t;null===(t=e.querySelector("ul"))||void 0===t||t.addEventListener("mousedown",(function(e){e.preventDefault()}))})),UX.utils.menuRoleButton()},UX.menu.on.showAdditional=function(){"menu"===$("body").data("active")&&$("html").css("overflow","visible")}}}]);
//# sourceMappingURL=menu-fef51b159b0104d5d363.js.map