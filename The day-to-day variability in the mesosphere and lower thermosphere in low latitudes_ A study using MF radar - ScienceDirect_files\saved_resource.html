
<!-- saved from url=(0011)about:blank -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><script>window.__CF$cv$params={r:'984d21653d78f217',t:'MTc1ODgzMDY2NC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);</script><script src="https://www.sciencedirect.com/cdn-cgi/challenge-platform/scripts/jsd/main.js"></script><script>window.__CF$cv$params={r:'984d21653d78f217',t:'MTc1ODgzMDY2NC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);</script><script src="https://www.sciencedirect.com/cdn-cgi/challenge-platform/scripts/jsd/main.js"></script></head><body data-new-gr-c-s-check-loaded="14.1255.0" data-gr-ext-installed=""></body><grammarly-desktop-integration data-grammarly-shadow-root="true"><template shadowrootmode="open"><style>
      div.grammarly-desktop-integration {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select:none;
        user-select:none;
      }

      div.grammarly-desktop-integration:before {
        content: attr(data-content);
      }
    </style><div aria-label="grammarly-integration" role="group" tabindex="-1" class="grammarly-desktop-integration" data-content="{&quot;mode&quot;:&quot;full&quot;,&quot;isActive&quot;:true,&quot;isUserDisabled&quot;:false}"></div></template></grammarly-desktop-integration></html>