(function(){window._altmetric||(window._altmetric={}),window._altmetric["export"]=function(e,t){return window._altmetric[e]=t},window._altmetric.exports=function(e){var t,n,i;for(t in n=[],e)i=e[t],n.push(window._altmetric[t]=i);return n}}).call(this),window._altmetric.api_uri||(window._altmetric.api_uri="https://api.altmetric.com"),window._altmetric.api_version||(window._altmetric.api_version="v1"),window._altmetric.details_uri||(window._altmetric.details_uri="https://www.altmetric.com"),function(){var e;e=function(e){var t,n;return t=document.createElement("div"),n=document.createTextNode(e),t.appendChild(n),t.innerHTML},_altmetric.exports({encodeHTML:e})}.call(this),function(e){"function"==typeof e.define&&(e._altmetric_define=e.define,e.define=undefined),"object"==typeof e.exports&&(e._altmetric_exports=e.exports,e.exports=undefined)}(window),function webpackUniversalModuleDefinition(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Handlebars=t():e.Handlebars=t()}(this,function(){return function(n){function i(e){if(r[e])return r[e].exports;var t=r[e]={exports:{},id:e,loaded:!1};return n[e].call(t.exports,t,t.exports,i),t.loaded=!0,t.exports}var r={};return i.m=n,i.c=r,i.p="",i(0)}([function(e,t,n){"use strict";function i(){var t=new a.HandlebarsEnvironment;return u.extend(t,a),t.SafeString=l["default"],t.Exception=s["default"],t.Utils=u,t.escapeExpression=u.escapeExpression,t.VM=c,t.template=function(e){return c.template(e,t)},t}var r=n(1)["default"],o=n(2)["default"];t.__esModule=!0;var a=r(n(3)),l=o(n(36)),s=o(n(5)),u=r(n(4)),c=r(n(37)),d=o(n(43)),p=i();p.create=i,d["default"](p),p["default"]=p,t["default"]=p,e.exports=t["default"]},function(e,t){"use strict";t["default"]=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t},t.__esModule=!0},function(e,t){"use strict";t["default"]=function(e){return e&&e.__esModule?e:{"default":e}},t.__esModule=!0},function(e,t,n){"use strict";function i(e,t,n){this.helpers=e||{},this.partials=t||{},this.decorators=n||{},l.registerDefaultHelpers(this),s.registerDefaultDecorators(this)}var r=n(2)["default"];t.__esModule=!0,t.HandlebarsEnvironment=i;var o=n(4),a=r(n(5)),l=n(9),s=n(29),u=r(n(31)),c=n(32),d="4.7.7";t.VERSION=d;var p=8;t.COMPILER_REVISION=p;var h=7;t.LAST_COMPATIBLE_COMPILER_REVISION=h;var f={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};t.REVISION_CHANGES=f;var m="[object Object]";i.prototype={constructor:i,logger:u["default"],log:u["default"].log,registerHelper:function _(e,t){if(o.toString.call(e)===m){if(t)throw new a["default"]("Arg not supported with multiple helpers");o.extend(this.helpers,e)}else this.helpers[e]=t},unregisterHelper:function y(e){delete this.helpers[e]},registerPartial:function b(e,t){if(o.toString.call(e)===m)o.extend(this.partials,e);else{if(void 0===t)throw new a["default"]('Attempting to register a partial called "'+e+'" as undefined');this.partials[e]=t}},unregisterPartial:function v(e){delete this.partials[e]},registerDecorator:function w(e,t){if(o.toString.call(e)===m){if(t)throw new a["default"]("Arg not supported with multiple decorators");o.extend(this.decorators,e)}else this.decorators[e]=t},unregisterDecorator:function k(e){delete this.decorators[e]},resetLoggedPropertyAccesses:function x(){c.resetLoggedProperties()}};var g=u["default"].log;t.log=g,t.createFrame=o.createFrame,t.logger=u["default"]},function(e,t){"use strict";function n(e){return c[e]}function i(e){for(var t=1;t<arguments.length;t++)for(var n in arguments[t])Object.prototype.hasOwnProperty.call(arguments[t],n)&&(e[n]=arguments[t][n]);return e}function r(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1}function o(e){if("string"!=typeof e){if(e&&e.toHTML)return e.toHTML();if(null==e)return"";if(!e)return e+"";e=""+e}return p.test(e)?e.replace(d,n):e}function a(e){return!e&&0!==e||!(!m(e)||0!==e.length)}function l(e){var t=i({},e);return t._parent=e,t}function s(e,t){return e.path=t,e}function u(e,t){return(e?e+".":"")+t}t.__esModule=!0,t.extend=i,t.indexOf=r,t.escapeExpression=o,t.isEmpty=a,t.createFrame=l,t.blockParams=s,t.appendContextPath=u;var c={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},d=/[&<>"'`=]/g,p=/[&<>"'`=]/,h=Object.prototype.toString;t.toString=h;var f=function f(e){return"function"==typeof e};f(/x/)&&(t.isFunction=f=function(e){return"function"==typeof e&&"[object Function]"===h.call(e)}),t.isFunction=f;var m=Array.isArray||function(e){return!(!e||"object"!=typeof e)&&"[object Array]"===h.call(e)};t.isArray=m},function(e,t,n){"use strict";function c(e,t){var n=t&&t.loc,i=undefined,r=undefined,o=undefined,a=undefined;n&&(i=n.start.line,r=n.end.line,o=n.start.column,a=n.end.column,e+=" - "+i+":"+o);for(var l=Error.prototype.constructor.call(this,e),s=0;s<p.length;s++)this[p[s]]=l[p[s]];Error.captureStackTrace&&Error.captureStackTrace(this,c);try{n&&(this.lineNumber=i,this.endLineNumber=r,d?(Object.defineProperty(this,"column",{value:o,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:a,enumerable:!0})):(this.column=o,this.endColumn=a))}catch(u){}}var d=n(6)["default"];t.__esModule=!0;var p=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];c.prototype=new Error,t["default"]=c,e.exports=t["default"]},function(e,t,n){e.exports={"default":n(7),__esModule:!0}},function(e,t,n){var i=n(8);e.exports=function r(e,t,n){return i.setDesc(e,t,n)}},function(e){var t=Object;e.exports={create:t.create,getProto:t.getPrototypeOf,isEnum:{}.propertyIsEnumerable,getDesc:t.getOwnPropertyDescriptor,setDesc:t.defineProperty,setDescs:t.defineProperties,getKeys:t.keys,getNames:t.getOwnPropertyNames,getSymbols:t.getOwnPropertySymbols,each:[].forEach}},function(e,t,n){"use strict";function i(e){a["default"](e),l["default"](e),s["default"](e),u["default"](e),c["default"](e),d["default"](e),p["default"](e)}function r(e,t,n){e.helpers[t]&&(e.hooks[t]=e.helpers[t],n||delete e.helpers[t])}var o=n(2)["default"];t.__esModule=!0,t.registerDefaultHelpers=i,t.moveHelperToHooks=r;var a=o(n(10)),l=o(n(11)),s=o(n(24)),u=o(n(25)),c=o(n(26)),d=o(n(27)),p=o(n(28))},function(e,t,n){"use strict";t.__esModule=!0;var a=n(4);t["default"]=function(o){o.registerHelper("blockHelperMissing",function(e,t){var n=t.inverse,i=t.fn;if(!0===e)return i(this);if(!1===e||null==e)return n(this);if(a.isArray(e))return 0<e.length?(t.ids&&(t.ids=[t.name]),o.helpers.each(e,t)):n(this);if(t.data&&t.ids){var r=a.createFrame(t.data);r.contextPath=a.appendContextPath(t.data.contextPath,t.name),t={data:r}}return i(e,t)})},e.exports=t["default"]},function(t,n,i){(function(f){"use strict";var m=i(12)["default"],e=i(2)["default"];n.__esModule=!0;var g=i(4),_=e(i(5));n["default"]=function(e){e.registerHelper("each",function(i,e){function t(e,t,n){s&&(s.key=e,s.index=t,s.first=0===t,s.last=!!n,u&&(s.contextPath=u+e)),l+=r(i[e],{data:s,blockParams:g.blockParams([i[e],e],[u+e,null])})}if(!e)throw new _["default"]("Must pass iterator to #each");var n,r=e.fn,o=e.inverse,a=0,l="",s=undefined,u=undefined;if(e.data&&e.ids&&(u=g.appendContextPath(e.data.contextPath,e.ids[0])+"."),g.isFunction(i)&&(i=i.call(this)),e.data&&(s=g.createFrame(e.data)),i&&"object"==typeof i)if(g.isArray(i))for(var c=i.length;a<c;a++)a in i&&t(a,a,a===i.length-1);else if(f.Symbol&&i[f.Symbol.iterator]){for(var d=[],p=i[f.Symbol.iterator](),h=p.next();!h.done;h=p.next())d.push(h.value);for(c=(i=d).length;a<c;a++)t(a,a,a===i.length-1)}else n=undefined,m(i).forEach(function(e){n!==undefined&&t(n,a-1),n=e,a++}),n!==undefined&&t(n,a-1,!0);return 0===a&&(l=o(this)),l})},t.exports=n["default"]}).call(n,function(){return this}())},function(e,t,n){e.exports={"default":n(13),__esModule:!0}},function(e,t,n){n(14),e.exports=n(20).Object.keys},function(e,t,n){var i=n(15);n(17)("keys",function(t){return function n(e){return t(i(e))}})},function(e,t,n){var i=n(16);e.exports=function(e){return Object(i(e))}},function(e){e.exports=function(e){if(e==undefined)throw TypeError("Can't call method on  "+e);return e}},function(e,t,n){var r=n(18),o=n(20),a=n(23);e.exports=function(e,t){var n=(o.Object||{})[e]||Object[e],i={};i[e]=t(n),r(r.S+r.F*a(function(){n(1)}),"Object",i)}},function(e,t,n){var f=n(19),m=n(20),g=n(21),_="prototype",y=function(e,t,n){var i,r,o,a=e&y.F,l=e&y.G,s=e&y.S,u=e&y.P,c=e&y.B,d=e&y.W,p=l?m:m[t]||(m[t]={}),h=l?f:s?f[t]:(f[t]||{})[_];for(i in l&&(n=t),n)(r=!a&&h&&i in h)&&i in p||(o=r?h[i]:n[i],p[i]=l&&"function"!=typeof h[i]?n[i]:c&&r?g(o,f):d&&h[i]==o?function(t){var e=function(e){return this instanceof t?new t(e):t(e)};return e[_]=t[_],e}(o):u&&"function"==typeof o?g(Function.call,o):o,u&&((p[_]||(p[_]={}))[i]=o))};y.F=1,y.G=2,y.S=4,y.P=8,y.B=16,y.W=32,e.exports=y},function(e){var t=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)},function(e){var t=e.exports={version:"1.2.6"};"number"==typeof __e&&(__e=t)},function(e,t,n){var o=n(22);e.exports=function(i,r,e){if(o(i),r===undefined)return i;switch(e){case 1:return function(e){return i.call(r,e)};case 2:return function(e,t){return i.call(r,e,t)};case 3:return function(e,t,n){return i.call(r,e,t,n)}}return function(){return i.apply(r,arguments)}}},function(e){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e){e.exports=function(e){try{return!!e()}catch(t){return!0}}},function(e,t,n){"use strict";var i=n(2)["default"];t.__esModule=!0;var r=i(n(5));t["default"]=function(e){e.registerHelper("helperMissing",function(){if(1===arguments.length)return undefined;throw new r["default"]('Missing helper: "'+arguments[arguments.length-1].name+'"')})},e.exports=t["default"]},function(e,t,n){"use strict";var i=n(2)["default"];t.__esModule=!0;var r=n(4),o=i(n(5));t["default"]=function(n){n.registerHelper("if",function(e,t){if(2!=arguments.length)throw new o["default"]("#if requires exactly one argument");return r.isFunction(e)&&(e=e.call(this)),!t.hash.includeZero&&!e||r.isEmpty(e)?t.inverse(this):t.fn(this)}),n.registerHelper("unless",function(e,t){if(2!=arguments.length)throw new o["default"]("#unless requires exactly one argument");return n.helpers["if"].call(this,e,{fn:t.inverse,inverse:t.fn,hash:t.hash})})},e.exports=t["default"]},function(e,t){"use strict";t.__esModule=!0,t["default"]=function(r){r.registerHelper("log",function(){for(var e=[undefined],t=arguments[arguments.length-1],n=0;n<arguments.length-1;n++)e.push(arguments[n]);var i=1;null!=t.hash.level?i=t.hash.level:t.data&&null!=t.data.level&&(i=t.data.level),e[0]=i,r.log.apply(r,e)})},e.exports=t["default"]},function(e,t){"use strict";t.__esModule=!0,t["default"]=function(e){e.registerHelper("lookup",function(e,t,n){return e?n.lookupProperty(e,t):e})},e.exports=t["default"]},function(e,t,n){"use strict";var i=n(2)["default"];t.__esModule=!0;var r=n(4),o=i(n(5));t["default"]=function(e){e.registerHelper("with",function(e,t){if(2!=arguments.length)throw new o["default"]("#with requires exactly one argument");r.isFunction(e)&&(e=e.call(this));var n=t.fn;if(r.isEmpty(e))return t.inverse(this);var i=t.data;return t.data&&t.ids&&((i=r.createFrame(t.data)).contextPath=r.appendContextPath(t.data.contextPath,t.ids[0])),n(e,{data:i,blockParams:r.blockParams([e],[i&&i.contextPath])})})},e.exports=t["default"]},function(e,t,n){"use strict";function i(e){o["default"](e)}var r=n(2)["default"];t.__esModule=!0,t.registerDefaultDecorators=i;var o=r(n(30))},function(e,t,n){"use strict";t.__esModule=!0;var l=n(4);t["default"]=function(e){e.registerDecorator("inline",function(r,o,a,e){var t=r;return o.partials||(o.partials={},t=function(e,t){var n=a.partials;a.partials=l.extend({},n,o.partials);var i=r(e,t);return a.partials=n,i}),o.partials[e.args[0]]=e.fn,t})},e.exports=t["default"]},function(e,t,n){"use strict";t.__esModule=!0;var i=n(4),o={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function r(e){if("string"==typeof e){var t=i.indexOf(o.methodMap,e.toLowerCase());e=0<=t?t:parseInt(e,10)}return e},log:function a(e){if(e=o.lookupLevel(e),"undefined"!=typeof console&&o.lookupLevel(o.level)<=e){var t=o.methodMap[e];console[t]||(t="log");for(var n=arguments.length,i=Array(1<n?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];console[t].apply(console,i)}}};t["default"]=o,e.exports=t["default"]},function(e,t,n){"use strict";function i(e){var t=s(null);t.constructor=!1,t.__defineGetter__=!1,t.__defineSetter__=!1,t.__lookupGetter__=!1;var n=s(null);return n.__proto__=!1,{properties:{whitelist:d.createNewLookupObject(n,e.allowedProtoProperties),defaultValue:e.allowProtoPropertiesByDefault},methods:{whitelist:d.createNewLookupObject(t,e.allowedProtoMethods),defaultValue:e.allowProtoMethodsByDefault}}}function r(e,t,n){return o("function"==typeof e?t.methods:t.properties,n)}function o(e,t){return e.whitelist[t]!==undefined?!0===e.whitelist[t]:e.defaultValue!==undefined?e.defaultValue:(a(t),!1)}function a(e){!0!==h[e]&&(h[e]=!0,p.log("error",'Handlebars: Access has been denied to resolve the property "'+e+'" because it is not an "own property" of its parent.\nYou can add a runtime option to disable the check or this warning:\nSee https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details'))}function l(){u(h).forEach(function(e){delete h[e]})}var s=n(33)["default"],u=n(12)["default"],c=n(1)["default"];t.__esModule=!0,t.createProtoAccessControl=i,t.resultIsAllowed=r,t.resetLoggedProperties=l;var d=n(35),p=c(n(31)),h=s(null)},function(e,t,n){e.exports={"default":n(34),__esModule:!0}},function(e,t,n){var i=n(8);e.exports=function r(e,t){return i.create(e,t)}},function(e,t,n){"use strict";function i(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return o.extend.apply(undefined,[r(null)].concat(t))}var r=n(33)["default"];t.__esModule=!0,t.createNewLookupObject=i;var o=n(4)},function(e,t){"use strict";function n(e){this.string=e}t.__esModule=!0,n.prototype.toString=n.prototype.toHTML=function(){return""+this.string},t["default"]=n,e.exports=t["default"]},function(e,t,n){"use strict";function i(e){var t=e&&e[0]||1,n=p.COMPILER_REVISION;if(!(t>=p.LAST_COMPATIBLE_COMPILER_REVISION&&t<=p.COMPILER_REVISION)){if(t<p.LAST_COMPATIBLE_COMPILER_REVISION){var i=p.REVISION_CHANGES[n],r=p.REVISION_CHANGES[t];throw new w["default"]("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+i+") or downgrade your runtime to an older version ("+r+").")}throw new w["default"]("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+e[1]+").")}}function r(s,u){function e(e,t,n){n.hash&&(t=v.extend({},t,n.hash),n.ids&&(n.ids[0]=!0)),e=u.VM.resolvePartial.call(this,e,t,n);var i=v.extend({},n,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),r=u.VM.invokePartial.call(this,e,t,i);if(null==r&&u.compile&&(n.partials[n.name]=u.compile(e,s.compilerOptions,u),r=n.partials[n.name](t,i)),null==r)throw new w["default"]("The partial "+n.name+" could not be compiled when running in runtime-only mode");if(n.indent){for(var o=r.split("\n"),a=0,l=o.length;a<l&&(o[a]||a+1!==l);a++)o[a]=n.indent+o[a];r=o.join("\n")}return r}function l(e,t){function n(e){return""+s.main(c,e,c.helpers,c.partials,r,a,o)}var i=arguments.length<=1||t===undefined?{}:arguments[1],r=i.data;l._setup(i),!i.partial&&s.useData&&(r=g(e,r));var o=undefined,a=s.useBlockParams?[]:undefined;return s.useDepths&&(o=i.depths?e!=i.depths[0]?[e].concat(i.depths):i.depths:[e]),(n=_(s.main,n,c,i.depths||[],r,a))(e,i)}if(!u)throw new w["default"]("No environment passed to template");if(!s||!s.main)throw new w["default"]("Unknown template object: "+typeof s);s.main.decorator=s.main_d,u.VM.checkRevision(s.compiler);var i=s.compiler&&7===s.compiler[0],c={strict:function r(e,t,n){if(!(e&&t in e))throw new w["default"]('"'+t+'" not defined in '+e,{loc:n});return c.lookupProperty(e,t)},lookupProperty:function o(e,t){var n=e[t];return null==n?n:Object.prototype.hasOwnProperty.call(e,t)?n:x.resultIsAllowed(n,c.protoAccessControl,t)?n:undefined},lookup:function a(e,t){for(var n=e.length,i=0;i<n;i++){if(null!=(e[i]&&c.lookupProperty(e[i],t)))return e[i][t]}},lambda:function n(e,t){return"function"==typeof e?e.call(t):e},escapeExpression:v.escapeExpression,invokePartial:e,fn:function d(e){var t=s[e];return t.decorator=s[e+"_d"],t},programs:[],program:function p(e,t,n,i,r){var o=this.programs[e],a=this.fn(e);return t||r||i||n?o=m(this,e,a,t,n,i,r):o||(o=this.programs[e]=m(this,e,a)),o},data:function h(e,t){for(;e&&t--;)e=e._parent;return e},mergeIfNeeded:function f(e,t){var n=e||t;return e&&t&&e!==t&&(n=v.extend({},t,e)),n},nullContext:b({}),noop:u.VM.noop,compilerInfo:s.compiler};return l.isTop=!0,l._setup=function(e){if(e.partial)c.protoAccessControl=e.protoAccessControl,c.helpers=e.helpers,c.partials=e.partials,c.decorators=e.decorators,c.hooks=e.hooks;else{var t=v.extend({},u.helpers,e.helpers);y(t,c),c.helpers=t,s.usePartial&&(c.partials=c.mergeIfNeeded(e.partials,u.partials)),(s.usePartial||s.useDecorators)&&(c.decorators=v.extend({},u.decorators,e.decorators)),c.hooks={},c.protoAccessControl=x.createProtoAccessControl(e);var n=e.allowCallsToHelperMissing||i;k.moveHelperToHooks(c,"helperMissing",n),k.moveHelperToHooks(c,"blockHelperMissing",n)}},l._child=function(e,t,n,i){if(s.useBlockParams&&!n)throw new w["default"]("must pass block params");if(s.useDepths&&!i)throw new w["default"]("must pass parent depths");return m(c,e,s[e],t,0,n,i)},l}function m(r,e,o,a,t,l,s){function n(e,t){var n=arguments.length<=1||t===undefined?{}:arguments[1],i=s;return!s||e==s[0]||e===r.nullContext&&null===s[0]||(i=[e].concat(s)),o(r,e,r.helpers,r.partials,n.data||a,l&&[n.blockParams].concat(l),i)}return(n=_(o,n,r,s,a,l)).program=e,n.depth=s?s.length:0,n.blockParams=t||0,n}function o(e,t,n){return e?e.call||n.name||(n.name=e,e=n.partials[e]):e="@partial-block"===n.name?n.data["partial-block"]:n.partials[n.name],e}function a(e,t,n){var o=n.data&&n.data["partial-block"];n.partial=!0,n.ids&&(n.data.contextPath=n.ids[0]||n.data.contextPath);var a=undefined;if(n.fn&&n.fn!==l&&function(){n.data=p.createFrame(n.data);var i=n.fn;a=n.data["partial-block"]=function r(e,t){var n=arguments.length<=1||t===undefined?{}:arguments[1];return n.data=p.createFrame(n.data),n.data["partial-block"]=o,i(e,n)},i.partials&&(n.partials=v.extend({},n.partials,i.partials))}(),e===undefined&&a&&(e=a),e===undefined)throw new w["default"]("The partial "+n.name+" could not be found");if(e instanceof Function)return e(t,n)}function l(){return""}function g(e,t){return t&&"root"in t||((t=t?p.createFrame(t):{}).root=e),t}function _(e,t,n,i,r,o){if(e.decorator){var a={};t=e.decorator(t,a,n,i&&i[0],r,o,i),v.extend(t,a)}return t}function y(n,i){u(n).forEach(function(e){var t=n[e];n[e]=s(t,i)})}function s(e,t){var n=t.lookupProperty;return h.wrapHelper(e,function(e){return v.extend({lookupProperty:n},e)})}var b=n(38)["default"],u=n(12)["default"],c=n(1)["default"],d=n(2)["default"];t.__esModule=!0,t.checkRevision=i,t.template=r,t.wrapProgram=m,t.resolvePartial=o,t.invokePartial=a,t.noop=l;var v=c(n(4)),w=d(n(5)),p=n(3),k=n(9),h=n(42),x=n(32)},function(e,t,n){e.exports={"default":n(39),__esModule:!0}},function(e,t,n){n(40),e.exports=n(20).Object.seal},function(e,t,n){var i=n(41);n(17)("seal",function(t){return function n(e){return t&&i(e)?t(e):e}})},function(e){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t){"use strict";function n(t,n){if("function"!=typeof t)return t;var i=function i(){var e=arguments[arguments.length-1];return arguments[arguments.length-1]=n(e),t.apply(this,arguments)};return i}t.__esModule=!0,t.wrapHelper=n},function(e,t){(function(i){"use strict";t.__esModule=!0,t["default"]=function(e){var t=void 0!==i?i:window,n=t.Handlebars;e.noConflict=function(){return t.Handlebars===e&&(t.Handlebars=n),e}},e.exports=t["default"]}).call(t,function(){return this}())}])}),function(e){"function"==typeof e._altmetric_define&&(e.define=e._altmetric_define,e._altmetric_define=undefined),"object"==typeof e._altmetric_exports&&(e.exports=e._altmetric_exports,e._altmetric_exports=undefined)}(window),function(e,t,n){"undefined"!=typeof module&&module.exports?module.exports=n():t[e]=n()}("qwery",_altmetric,function(){function e(){this.c={}}function g(e){return Y.g(e)||Y.s(e,"(^|\\s+)"+e+"(\\s+|$)",1)}function f(e,t){for(var n=0,i=e.length;n<i;n++)t(e[n])}function a(e){for(var t=[],n=0,i=e.length;n<i;++n)d(e[n])?t=t.concat(e[n]):t[t.length]=e[n];return t}function l(e){for(var t=0,n=e.length,i=[];t<n;t++)i[t]=e[t];return i}function r(e){for(;(e=e.previousSibling)&&1!=e[S];);return e}function m(e){return e.match(V)}function _(e,t,n,i,r,o,a,l,s,u,c){var d,p,h,f,m;if(1!==this[S])return!1;if(t&&"*"!==t&&this[E]&&this[E].toLowerCase()!==t)return!1;if(n&&(p=n.match(O))&&p[1]!==this.id)return!1;if(n&&(m=n.match(M)))for(d=m.length;d--;)if(!g(m[d].slice(1)).test(this.className))return!1;if(s&&w.pseudos[s]&&!w.pseudos[s](this,c))return!1;if(i&&!a)for(h in f=this.attributes)if(Object.prototype.hasOwnProperty.call(f,h)&&(f[h].name||h)==r)return this;return!(i&&!y(o,ee(this,r)||"",a))&&this}function i(e){return Q.g(e)||Q.s(e,e.replace(j,"\\$1"))}function y(e,t,n){switch(e){case"=":return t==n;case"^=":return t.match(K.g("^="+n)||K.s("^="+n,"^"+i(n),1));case"$=":return t.match(K.g("$="+n)||K.s("$="+n,i(n)+"$",1));case"*=":return t.match(K.g(n)||K.s(n,i(n),1));case"~=":return t.match(K.g("~="+n)||K.s("~="+n,"(?:^|\\s+)"+i(n)+"(?:\\s+|$)",1));case"|=":return t.match(K.g("|="+n)||K.s("|="+n,"^"+i(n)+"(-|$)",1))}return 0}function u(e,t){var n,i,r,o,a,l,s,u=[],c=[],d=t,p=Z.g(e)||Z.s(e,e.split(W)),h=e.match(z);if(!p.length)return u;if(o=(p=p.slice(0)).pop(),p.length&&(r=p[p.length-1].match(N))&&(d=v(t,r[1])),!d)return u;for(l=m(o),n=0,i=(a=d!==t&&9!==d[S]&&h&&/^[+~]$/.test(h[h.length-1])?function(e){for(;d=d.nextSibling;)1==d[S]&&(!l[1]||l[1]==d[E].toLowerCase())&&(e[e.length]=d);return e}([]):d[P](l[1]||"*")).length;n<i;n++)(s=_.apply(a[n],l))&&(u[u.length]=s);return p.length?(f(u,function(e){b(e,p,h)&&(c[c.length]=e)}),c):u}function t(e,t,n){if(s(t))return e==t;if(d(t))return!!~a(t).indexOf(e);for(var i,r,o=t.split(",");t=o.pop();)if(i=Z.g(t)||Z.s(t,t.split(W)),r=t.match(z),i=i.slice(0),_.apply(e,m(i.pop()))&&(!i.length||b(e,i,r,n)))return!0;return!1}function b(e,i,r,t){function o(e,t,n){for(;n=X[r[t]](n,e);)if(s(n)&&_.apply(n,m(i[t]))){if(!t)return n;if(a=o(n,t-1,n))return a}}var a;return(a=o(e,i.length-1,e))&&(!t||J(a,t))}function s(e,t){return e&&"object"==typeof e&&(t=e[S])&&(1==t||9==t)}function c(e){var t,n,i=[];e:for(t=0;t<e.length;++t){for(n=0;n<i.length;++n)if(i[n]==e[t])continue e;i[i.length]=e[t]}return i}function d(e){return"object"==typeof e&&isFinite(e.length)}function o(e){return e?"string"==typeof e?w(e)[0]:!e[S]&&d(e)?e[0]:e:n}function v(e,t,n){return 9===e[S]?e.getElementById(t):e.ownerDocument&&((n=e.ownerDocument.getElementById(t))&&J(n,e)&&n||!J(e,e.ownerDocument)&&h('[id="'+t+'"]',e)[0])}function w(e,t){var n,i,r=o(t);if(!r||!e)return[];if(e===window||s(e))return!t||e!==window&&s(r)&&J(e,r)?[e]:[];if(e&&d(e))return a(e);if(n=e.match($)){if(n[1])return(i=v(r,n[1]))?[i]:[];if(n[2])return l(r[P](n[2]))}return h(e,r)}function p(i,r){return function(e){var t,n;L.test(e)?9!==i[S]&&((n=t=i.getAttribute("id"))||i.setAttribute("id",n="__qwerymeupscotty"),e='[id="'+n+'"]'+e,r(i.parentNode||i,e,!0),t||i.removeAttribute("id")):e.length&&r(i,e,!1)}}var h,n=document,k=n.documentElement,x="getElementsByClassName",P="getElementsByTagName",T="querySelectorAll",C="useNativeQSA",E="tagName",S="nodeType",O=/#([\w\-]+)/,M=/\.[\w\-]+/g,N=/^#([\w\-]+)$/,A=/^\.([\w\-]+)$/,I=/^([\w\-]+)$/,B=/^([\w]+)?\.([\w\-]+)$/,L=/(^|,)\s*[>~+]/,H=/^\s+|\s*([,\s\+\~>]|$)\s*/g,D=/[\s\>\+\~]/,R=/(?![\s\w\-\/\?\&\=\:\.\(\)\!,@#%<>\{\}\$\*\^'"]*\]|[\s\w\+\-]*\))/,j=/([.*+?\^=!:${}()|\[\]\/\\])/g,F=/^(\*|[a-z0-9]+)?(?:([\.\#]+[\w\-\.#]+)?)/,U=/\[([\w\-]+)(?:([\|\^\$\*\~]?\=)['"]?([ \w\-\/\?\&\=\:\.\(\)\!,@#%<>\{\}\$\*\^]+)["']?)?\]/,q=/:([\w\-]+)(\(['"]?([^()]+)['"]?\))?/,$=new RegExp(N.source+"|"+I.source+"|"+A.source),z=new RegExp("("+D.source+")"+R.source,"g"),W=new RegExp(D.source+R.source),V=new RegExp(F.source+"("+U.source+")?("+q.source+")?"),X={" ":function(e){return e&&e!==k&&e.parentNode},">":function(e,t){return e&&e.parentNode==t.parentNode&&e.parentNode},"~":function(e){return e&&e.previousSibling},"+":function(e,t,n,i){return!!e&&((n=r(e))&&(i=r(t))&&n==i&&n)}};e.prototype={g:function(e){return this.c[e]||undefined},s:function(e,t,n){return t=n?new RegExp(t):t,this.c[e]=t}};var G,Y=new e,Q=new e,K=new e,Z=new e,J="compareDocumentPosition"in k?function(e,t){return 16==(16&t.compareDocumentPosition(e))}:"contains"in k?function(e,t){return(t=9===t[S]||t==window?k:t)!==e&&t.contains(e)}:function(e,t){for(;e=e.parentNode;)if(e===t)return 1;return 0},ee=((G=n.createElement("p")).innerHTML='<a href="#x">x</a>')&&"#x"!=G.firstChild.getAttribute("href")?function(e,t){return"class"===t?e.className:"href"===t||"src"===t?e.getAttribute(t,2):e.getAttribute(t)}:function(e,t){return e.getAttribute(t)},te=(n[x],n.querySelector&&n[T]),ne=function(e,t){var n,i,r=[];try{return 9!==t[S]&&L.test(e)?(f(n=e.split(","),p(t,function(e,t){1==(i=e[T](t)).length?r[r.length]=i.item(0):i.length&&(r=r.concat(l(i)))})),1<n.length&&1<r.length?c(r):r):l(t[T](e))}catch(o){}return ie(e,t)},ie=function(e,i){var t,n,r,o,a,l,s=[];if(n=(e=e.replace(H,"$1")).match(B)){for(a=g(n[2]),t=i[P](n[1]||"*"),r=0,o=t.length;r<o;r++)a.test(t[r].className)&&(s[s.length]=t[r]);return s}return f(l=e.split(","),p(i,function(e,t,n){for(a=u(t,e),r=0,o=a.length;r<o;r++)(9===e[S]||n||J(a[r],i))&&(s[s.length]=a[r])})),1<l.length&&1<s.length?c(s):s},re=function(e){"undefined"!=typeof e[C]&&(h=e[C]&&te?ne:ie)};return re({useNativeQSA:!0}),w.configure=re,w.uniq=c,w.is=t,w.pseudos={},w}),function(e,t,n){"undefined"!=typeof module&&module.exports?module.exports=n():t[e]=n()}("bean",_altmetric,function(e,t){e=e||"bean",t=t||this;var d,p,h,u,n,i,r,o,a,l,s,c,f,m,g,_,y,b,v,w=window,k=t[e],x=/[^\.]*(?=\..*)\.|.*/,P=/\..*/,T="addEventListener",C="removeEventListener",E=document||{},S=E.documentElement||{},O=S[T],M=O?T:"attachEvent",N={},A=Array.prototype.slice,I=function(e,t){return e.split(t||" ")},B=function(e){return"string"==typeof e},L=function(e){return"function"==typeof e},H=function(e,t,n){for(n=0;n<t.length;n++)t[n]&&(e[t[n]]=1);return e}({},I("click dblclick mouseup mousedown contextmenu mousewheel mousemultiwheel DOMMouseScroll mouseover mouseout mousemove selectstart selectend keydown keypress keyup orientationchange focus blur change reset select submit load unload beforeunload resize move DOMContentLoaded readystatechange message error abort scroll "+(O?"show input invalid touchstart touchmove touchend touchcancel gesturestart gesturechange gestureend textinputreadystatechange pageshow pagehide popstate hashchange offline online afterprint beforeprint dragstart dragenter dragover dragleave drag drop dragend loadstart progress suspend emptied stalled loadmetadata loadeddata canplay canplaythrough playing waiting seeking seeked ended durationchange timeupdate play pause ratechange volumechange cuechange checking noupdate downloading cached updateready obsolete ":""))),D=(b="compareDocumentPosition"in S?function(e,t){return t.compareDocumentPosition&&16==(16&t.compareDocumentPosition(e))}:"contains"in S?function(e,t){return(t=9===t.nodeType||t===window?S:t)!==e&&t.contains(e)}:function(e,t){for(;e=e.parentNode;)if(e===t)return 1;return 0},{mouseenter:{base:"mouseover",condition:v=function(e){var t=e.relatedTarget;return t?t!==this&&"xul"!==t.prefix&&!/document/.test(this.toString())&&!b(t,this):null==t}},mouseleave:{base:"mouseout",condition:v},mousewheel:{base:/Firefox/.test(navigator.userAgent)?"DOMMouseScroll":"mousewheel"}}),R=(i=I("altKey attrChange attrName bubbles cancelable ctrlKey currentTarget detail eventPhase getModifierState isTrusted metaKey relatedNode relatedTarget shiftKey srcElement target timeStamp type view which propertyName"),r=i.concat(I("button buttons clientX clientY dataTransfer fromElement offsetX offsetY pageX pageY screenX screenY toElement")),o=r.concat(I("wheelDelta wheelDeltaX wheelDeltaY wheelDeltaZ axis")),a=i.concat(I("char charCode key keyCode keyIdentifier keyLocation location")),l=i.concat(I("data")),s=i.concat(I("touches targetTouches changedTouches scale rotation")),c=i.concat(I("data origin source")),f=i.concat(I("state")),m=/over|out/,g=[{reg:/key/i,fix:function(e,t){return t.keyCode=e.keyCode||e.which,a}},{reg:/click|mouse(?!(.*wheel|scroll))|menu|drag|drop/i,fix:function(e,t,n){return t.rightClick=3===e.which||2===e.button,t.pos={x:0,y:0},e.pageX||e.pageY?(t.clientX=e.pageX,t.clientY=e.pageY):(e.clientX||e.clientY)&&(t.clientX=e.clientX+E.body.scrollLeft+S.scrollLeft,t.clientY=e.clientY+E.body.scrollTop+S.scrollTop),m.test(n)&&(t.relatedTarget=e.relatedTarget||e[("mouseover"==n?"from":"to")+"Element"]),r}},{reg:/mouse.*(wheel|scroll)/i,fix:function(){return o}},{reg:/^text/i,fix:function(){return l}},{reg:/^touch|^gesture/i,fix:function(){return s}},{reg:/^message$/i,fix:function(){return c}},{reg:/^popstate$/i,fix:function(){return f}},{reg:/.*/,fix:function(){return i}}],_={},(y=function(e,t,n){if(arguments.length&&(e=e||((t.ownerDocument||t.document||t).parentWindow||w).event,this.originalEvent=e,this.isNative=n,this.isBean=!0,e)){var i,r,o,a,l,s=e.type,u=e.target||e.srcElement;if(this.target=u&&3===u.nodeType?u.parentNode:u,n){if(!(l=_[s]))for(i=0,r=g.length;i<r;i++)if(g[i].reg.test(s)){_[s]=l=g[i].fix;break}for(i=(a=l(e,this,s)).length;i--;)!((o=a[i])in this)&&o in e&&(this[o]=e[o])}}}).prototype.preventDefault=function(){this.originalEvent.preventDefault?this.originalEvent.preventDefault():this.originalEvent.returnValue=!1},y.prototype.stopPropagation=function(){this.originalEvent.stopPropagation?this.originalEvent.stopPropagation():this.originalEvent.cancelBubble=!0},y.prototype.stop=function(){this.preventDefault(),this.stopPropagation(),this.stopped=!0},y.prototype.stopImmediatePropagation=function(){this.originalEvent.stopImmediatePropagation&&this.originalEvent.stopImmediatePropagation(),this.isImmediatePropagationStopped=function(){return!0}},y.prototype.isImmediatePropagationStopped=function(){return this.originalEvent.isImmediatePropagationStopped&&this.originalEvent.isImmediatePropagationStopped()},y.prototype.clone=function(e){var t=new y(this,this.element,this.isNative);return t.currentTarget=e,t},y),j=function(e,t){return O||t||e!==E&&e!==w?e:S},F=(u=function(n,i,r,o){var a=function(e,t){return i.apply(n,o?A.call(t,e?0:1).concat(o):t)},l=function(e,t){return i.__beanDel?i.__beanDel.ft(e.target,n):t},e=r?function(e){var t=l(e,this);if(r.apply(t,arguments))return e&&(e.currentTarget=t),a(e,arguments)}:function(e){return i.__beanDel&&(e=e.clone(l(e))),a(e,arguments)};return e.__beanDel=i.__beanDel,e},(n=function(e,t,n,i,r,o,a){var l,s=D[t];"unload"==t&&(n=W(V,e,t,n,i)),s&&(s.condition&&(n=u(e,n,s.condition,o)),t=s.base||t),this.isNative=l=H[t]&&!!e[M],this.customType=!O&&!l&&t,this.element=e,this.type=t,this.original=i,this.namespaces=r,this.eventType=O||l?t:"propertychange",this.target=j(e,l),this[M]=!!this.target[M],this.root=a,this.handler=u(e,n,null,o)}).prototype.inNamespaces=function(e){var t,n,i=0;if(!e)return!0;if(!this.namespaces)return!1;for(t=e.length;t--;)for(n=this.namespaces.length;n--;)e[t]==this.namespaces[n]&&i++;return e.length===i},n.prototype.matches=function(e,t,n){return!(this.element!==e||t&&this.original!==t||n&&this.handler!==n)},n),U=(p={},h=function(e,t,n,i,r,o){var a=r?"r":"$";if(t&&"*"!=t){var l,s=0,u=p[a+t],c="*"==e;if(!u)return;for(l=u.length;s<l;s++)if((c||u[s].matches(e,n,i))&&!o(u[s],u,s,t))return}else for(var d in p)d.charAt(0)==a&&h(e,d.substr(1),n,i,r,o)},{has:function(e,t,n,i){var r,o=p[(i?"r":"$")+t];if(o)for(r=o.length;r--;)if(!o[r].root&&o[r].matches(e,n,null))return!0;return!1},get:function(e,t,n,i){var r=[];return h(e,t,n,null,i,function(e){return r.push(e)}),r},put:function(e){var t=!e.root&&!this.has(e.element,e.type,null,!1),n=(e.root?"r":"$")+e.type;return(p[n]||(p[n]=[])).push(e),t},del:function(e){h(e.element,e.type,null,e.handler,e.root,function(e,t,n){
return t.splice(n,1),e.removed=!0,0===t.length&&delete p[(e.root?"r":"$")+e.type],!1})},entries:function(){var e,t=[];for(e in p)"$"==e.charAt(0)&&(t=t.concat(p[e]));return t}}),q=function(e){d=arguments.length?e:E.querySelectorAll?function(e,t){return t.querySelectorAll(e)}:function(){throw new Error("Bean: No selector engine installed")}},$=function(e,t){if(O||!t||!e||e.propertyName=="_on"+t){var n=U.get(this,t||e.type,null,!1),i=n.length,r=0;for(e=new R(e,this,!0),t&&(e.type=t);r<i&&!e.isImmediatePropagationStopped();r++)n[r].removed||n[r].handler.call(this,e)}},z=O?function(e,t,n){e[n?T:C](t,$,!1)}:function(t,e,n,i){var r;n?(U.put(r=new F(t,i||e,function(e){$.call(t,e,i)},$,null,null,!0)),i&&null==t["_on"+i]&&(t["_on"+i]=0),r.target.attachEvent("on"+r.eventType,r.handler)):(r=U.get(t,i||e,$,!0)[0])&&(r.target.detachEvent("on"+r.eventType,r.handler),U.del(r))},W=function(e,t,n,i,r){return function(){i.apply(this,arguments),e(t,n,r)}},V=function(e,t,n,i){var r,o,a=t&&t.replace(P,""),l=U.get(e,a,null,!1),s={};for(r=0,o=l.length;r<o;r++)n&&l[r].original!==n||!l[r].inNamespaces(i)||(U.del(l[r]),!s[l[r].eventType]&&l[r][M]&&(s[l[r].eventType]={t:l[r].eventType,c:l[r].type}));for(r in s)U.has(e,s[r].t,null,!1)||z(e,s[r].t,!1,s[r].c)},X=function(r,n){var i=function(e,t){for(var n,i=B(r)?d(r,t):r;e&&e!==t;e=e.parentNode)for(n=i.length;n--;)if(i[n]===e)return e},e=function(e){var t=i(e.target,this);t&&n.apply(t,arguments)};return e.__beanDel={ft:i,selector:r},e},G=O?function(e,t,n){var i=E.createEvent(e?"HTMLEvents":"UIEvents");i[e?"initEvent":"initUIEvent"](t,!0,!0,w,1),n.dispatchEvent(i)}:function(e,t,n){n=j(n,e),e?n.fireEvent("on"+t,E.createEventObject()):n["_on"+t]++},Y=function(e,t,n){var i,r,o,a,l=B(t);if(l&&0<t.indexOf(" ")){for(a=(t=I(t)).length;a--;)Y(e,t[a],n);return e}if((r=l&&t.replace(P,""))&&D[r]&&(r=D[r].base),!t||l)(o=l&&t.replace(x,""))&&(o=I(o,".")),V(e,r,n,o);else if(L(t))V(e,null,t);else for(i in t)t.hasOwnProperty(i)&&Y(e,i,t[i]);return e},Q=function(e,t,n,i){var r,o,a,l,s,u,c;if(n!==undefined||"object"!=typeof t){for(L(n)?(s=A.call(arguments,3),i=r=n):(r=i,s=A.call(arguments,4),i=X(n,r,d)),a=I(t),this===N&&(i=W(Y,e,t,i,r)),l=a.length;l--;)c=U.put(u=new F(e,a[l].replace(P,""),i,r,I(a[l].replace(x,""),"."),s,!1)),u[M]&&c&&z(e,u.eventType,!0,u.customType);return e}for(o in t)t.hasOwnProperty(o)&&Q.call(this,e,o,t[o])},K=function(e,t,n,i){return Q.apply(null,B(n)?[e,n,t,i].concat(3<arguments.length?A.call(arguments,5):[]):A.call(arguments))},Z=function(){return Q.apply(N,arguments)},J=function(e,t,n){for(var i,r,o=U.get(t,n,null,!1),a=o.length,l=0;l<a;l++)o[l].original&&(i=[e,o[l].type],(r=o[l].handler.__beanDel)&&i.push(r.selector),i.push(o[l].original),Q.apply(null,i));return e},ee={on:Q,add:K,one:Z,off:Y,remove:Y,clone:J,fire:function(e,t,n){var i,r,o,a,l,s=I(t);for(i=s.length;i--;)if(t=s[i].replace(P,""),(a=s[i].replace(x,""))&&(a=I(a,".")),a||n||!e[M])for(l=U.get(e,t,null,!1),n=[!1].concat(n),r=0,o=l.length;r<o;r++)l[r].inNamespaces(a)&&l[r].handler.apply(e,n);else G(H[t],t,e);return e},Event:R,setSelectorEngine:q,noConflict:function(){return t[e]=k,this}};if(w.attachEvent){var te=function(){var e,t=U.entries();for(e in t)t[e].type&&"unload"!==t[e].type&&Y(t[e].element,t[e].type);w.detachEvent("onunload",te),w.CollectGarbage&&w.CollectGarbage()};w.attachEvent("onunload",te)}return q(),ee}),function(e,t,n){"undefined"!=typeof module&&module.exports?module.exports=n():t[e]=n()}("bonzo",_altmetric,function(){function a(e){return e&&e.nodeName&&(1==e.nodeType||11==e.nodeType)}function s(e,t,n){var i,r,o;if("string"==typeof e)return k.create(e);if(a(e)&&(e=[e]),n){for(o=[],i=0,r=e.length;i<r;i++)o[i]=y(t,e[i]);return o}return e}function n(e){return new RegExp("(^|\\s+)"+e+"(\\s+|$)")}function u(e,t,n,i){for(var r,o=0,a=e.length;o<a;o++)r=i?e.length-o-1:o,t.call(n||e[r],e[r],r,e);return e}function o(e,t,n){for(var i=0,r=e.length;i<r;i++)a(e[i])&&(o(e[i].childNodes,t,n),t.call(n||e[i],e[i],i,e));return e}function l(e){return e.replace(/-(.)/g,function(e,t){return t.toUpperCase()})}function c(e){return e?e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase():e}function d(e){e[V]("data-node-uid")||e[W]("data-node-uid",++U);var t=e[V]("data-node-uid");return F[t]||(F[t]={})}function t(e){var t=e[V]("data-node-uid");t&&delete F[t]}function p(e){var t;try{return null===e||e===undefined?undefined:"true"===e||"false"!==e&&("null"===e?null:(t=parseFloat(e))==e?t:e)}catch(n){}return undefined}function i(e,t,n){for(var i=0,r=e.length;i<r;++i)if(t.call(n||null,e[i],i,e))return!0;return!1}function h(e){return"transform"==e&&(e=G.transform)||/^transform-?[Oo]rigin$/.test(e)&&(e=G.transform+"Origin")||"float"==e&&(e=G.cssFloat),e?l(e):null}function r(e,t,i,r){var o=0,a=t||this,l=[];return u(s(J&&"string"==typeof e&&"<"!=e.charAt(0)?J(e):e),function(t,n){u(a,function(e){i(t,l[o++]=0<n?y(a,e):e)},null,r)},this,r),a.length=o,u(l,function(e){a[--o]=e},null,!r),a}function f(e,t,n){var i=k(e),r=i.css("position"),o=i.offset(),a="relative",l=r==a,s=[parseInt(i.css("left"),10),parseInt(i.css("top"),10)];"static"==r&&(i.css("position",a),r=a),isNaN(s[0])&&(s[0]=l?0:e.offsetLeft),isNaN(s[1])&&(s[1]=l?0:e.offsetTop),null!=t&&(e.style.left=t-o.left+s[0]+z),null!=n&&(e.style.top=n-o.top+s[1]+z)}function m(e,t){return"function"==typeof t?t(e):t}function g(e,t,n){var i=this[0];return i?null==e&&null==t?(b(i)?v():{x:i.scrollLeft,y:i.scrollTop})[n]:(b(i)?E.scrollTo(e,t):(null!=e&&(i.scrollLeft=e),null!=t&&(i.scrollTop=t)),this):this}function _(e){if(this.length=0,e){e="string"==typeof e||e.nodeType||"undefined"==typeof e.length?[e]:e,this.length=e.length;for(var t=0;t<e.length;t++)this[t]=e[t]}}function y(e,t){var n,i,r,o=t.cloneNode(!0);if(e.$&&"function"==typeof e.cloneEvents)for(e.$(o).cloneEvents(t),n=e.$(o).find("*"),i=e.$(t).find("*"),r=0;r<i.length;r++)e.$(n[r]).cloneEvents(i[r]);return o}function b(e){return e===E||/^(?:body|html)$/i.test(e.tagName)}function v(){return{x:E.pageXOffset||O.scrollLeft,y:E.pageYOffset||O.scrollTop}}function w(e){var t=document.createElement("script"),n=e.match(I);return t.src=n[1],t}function k(e){return new _(e)}var x,P,T,C,E=window,S=E.document,O=S.documentElement,M="parentNode",N=/^(checked|value|selected|disabled)$/i,A=/^(select|fieldset|table|tbody|tfoot|td|tr|colgroup)$/i,I=/\s*<script +src=['"]([^'"]+)['"]>/,e=["<table>","</table>",1],B=["<table><tbody><tr>","</tr></tbody></table>",3],L=["<select>","</select>",1],H=["_","",0,1],D={thead:e,tbody:e,tfoot:e,colgroup:e,caption:e,tr:["<table><tbody>","</tbody></table>",2],th:B,td:B,col:["<table><colgroup>","</colgroup></table>",2],fieldset:["<form>","</form>",1],legend:["<form><fieldset>","</fieldset></form>",2],option:L,optgroup:L,script:H,style:H,link:H,param:H,base:H},R=/^(checked|selected|disabled)$/,j=/msie/i.test(navigator.userAgent),F={},U=0,q=/^-?[\d\.]+$/,$=/^data-(.+)$/,z="px",W="setAttribute",V="getAttribute",X="getElementsByTagName",G=((C=S.createElement("p")).innerHTML='<a href="#x">x</a><table style="float:left;"></table>',{hrefExtended:"#x"!=C[X]("a")[0][V]("href"),autoTbody:0!==C[X]("tbody").length,computedStyle:S.defaultView&&S.defaultView.getComputedStyle,cssFloat:C[X]("table")[0].style.styleFloat?"styleFloat":"cssFloat",transform:function(){var e,t=["transform","webkitTransform","MozTransform","OTransform","msTransform"];for(e=0;e<t.length;e++)if(t[e]in C.style)return t[e]}(),classList:"classList"in C,opasity:"undefined"!=typeof S.createElement("a").style.opacity}),Y=/(^\s*|\s*$)/g,Q=/\s+/,K=String.prototype.toString,Z={lineHeight:1,zoom:1,zIndex:1,opacity:1,boxFlex:1,WebkitBoxFlex:1,MozBoxFlex:1},J=S.querySelectorAll&&function(e){return S.querySelectorAll(e)},ee=String.prototype.trim?function(e){return e.trim()}:function(e){return e.replace(Y,"")},te=G.computedStyle?function(e,t){var n=null,i=S.defaultView.getComputedStyle(e,"");return i&&(n=i[t]),e.style[t]||n}:j&&O.currentStyle?function(e,t){var n,i;if("opacity"!=t||G.opasity)return i=e.currentStyle?e.currentStyle[t]:null,e.style[t]||i;n=100;try{n=e.filters["DXImageTransform.Microsoft.Alpha"].opacity}catch(r){try{n=e.filters("alpha").opacity}catch(o){}}return n/100}:function(e,t){return e.style[t]};return G.classList?(x=function(e,t){return e.classList.contains(t)},P=function(e,t){e.classList.add(t)},T=function(e,t){e.classList.remove(t)}):(x=function(e,t){return n(t).test(e.className)},P=function(e,t){e.className=ee(e.className+" "+t)},T=function(e,t){e.className=ee(e.className.replace(n(t)," "))}),_.prototype={get:function(e){return this[e]||null},each:function(e,t){return u(this,e,t)},deepEach:function(e,t){return o(this,e,t)},map:function(e,t){var n,i,r=[];for(i=0;i<this.length;i++)n=e.call(this,this[i],i),t?t(n)&&r.push(n):r.push(n);return r},html:function(i,r){var o=r?O.textContent===undefined?"innerText":"textContent":"innerHTML",n=this,a=function(t,e){u(s(i,n,e),function(e){t.appendChild(e)})},e=function(e,t){try{if(r||"string"==typeof i&&!A.test(e.tagName))return e[o]=i}catch(n){}a(e,t)};return void 0!==i?this.empty().each(e):this[0]?this[0][o]:""},text:function(e){return this.html(e,!0)},append:function(n){var i=this;return this.each(function(t,e){u(s(n,i,e),function(e){t.appendChild(e)})})},prepend:function(i){var r=this;return this.each(function(t,e){var n=t.firstChild;u(s(i,r,e),function(e){t.insertBefore(e,n)})})},appendTo:function(e,t){return r.call(this,e,t,function(e,t){e.appendChild(t)})},prependTo:function(e,t){return r.call(this,e,t,function(e,t){e.insertBefore(t,e.firstChild)},1)},before:function(n){var i=this;return this.each(function(t,e){u(s(n,i,e),function(e){t[M].insertBefore(e,t)})})},after:function(n){var i=this;return this.each(function(t,e){u(s(n,i,e),function(e){t[M].insertBefore(e,t.nextSibling)},null,1)})},insertBefore:function(e,t){return r.call(this,e,t,function(e,t){e[M].insertBefore(t,e)})},insertAfter:function(e,t){return r.call(this,e,t,function(e,t){var n=e.nextSibling;n?e[M].insertBefore(t,n):e[M].appendChild(t)},1)},replaceWith:function(e){return k(s(e)).insertAfter(this),this.remove()},clone:function(e){var t,n,i=[];for(n=0,t=this.length;n<t;n++)i[n]=y(e||this,this[n]);return k(i)},addClass:function(e){return e=K.call(e).split(Q),this.each(function(t){u(e,function(e){e&&!x(t,m(t,e))&&P(t,m(t,e))})})},removeClass:function(e){return e=K.call(e).split(Q),this.each(function(t){u(e,function(e){e&&x(t,m(t,e))&&T(t,m(t,e))})})},hasClass:function(e){return e=K.call(e).split(Q),i(this,function(t){return i(e,function(e){return e&&x(t,e)})})},toggleClass:function(e,n){return e=K.call(e).split(Q),this.each(function(t){u(e,function(e){e&&(void 0!==n?n?!x(t,e)&&P(t,e):T(t,e):x(t,e)?T(t,e):P(t,e))})})},show:function(t){return t="string"==typeof t?t:"",this.each(function(e){e.style.display=t})},hide:function(){return this.each(function(e){e.style.display="none"})},toggle:function(t,n){return n="string"==typeof n?n:"","function"!=typeof t&&(t=null),this.each(function(e){e.style.display=e.offsetWidth||e.offsetHeight?"none":n,t&&t.call(e)})},first:function(){return k(this.length?this[0]:[])},last:function(){return k(this.length?this[this.length-1]:[])},next:function(){return this.related("nextSibling")},previous:function(){return this.related("previousSibling")},parent:function(){return this.related(M)},related:function(t){return k(this.map(function(e){for(e=e[t];e&&1!==e.nodeType;)e=e[t];return e||0},function(e){return e}))},focus:function(){return this.length&&this[0].focus(),this},blur:function(){return this.length&&this[0].blur(),this},css:function(e,t){function n(e,t,n){for(var i in o)if(o.hasOwnProperty(i)){n=o[i],(t=h(i))&&q.test(n)&&!(t in Z)&&(n+=z);try{e.style[t]=m(e,n)}catch(r){}}}var i,o=e;return t===undefined&&"string"==typeof e?(t=this[0])?t===S||t===E?(i=t===S?k.doc():k.viewport(),"width"==e?i.width:"height"==e?i.height:""):(e=h(e))?te(t,e):null:null:("string"==typeof e&&((o={})[e]=t),j&&o.opacity&&(o.filter="alpha(opacity="+100*o.opacity+")",o.zoom=e.zoom||1,delete o.opacity),this.each(n))},offset:function(t,n){if(t&&"object"==typeof t&&("number"==typeof t.top||"number"==typeof t.left))return this.each(function(e){f(e,t.left,t.top)});if("number"==typeof t||"number"==typeof n)return this.each(function(e){f(e,t,n)});if(!this[0])return{top:0,left:0,height:0,width:0};var e=this[0],i=e.ownerDocument.documentElement,r=e.getBoundingClientRect(),o=v(),a=e.offsetWidth,l=e.offsetHeight;return{top:r.top+o.y-Math.max(0,i&&i.clientTop,S.body.clientTop),left:r.left+o.x-Math.max(0,i&&i.clientLeft,S.body.clientLeft),height:l,width:a}},dim:function(){if(!this.length)return{height:0,width:0};var e,t,n=this[0],i=9==n.nodeType&&n.documentElement,r=i||!n.style||n.offsetWidth||n.offsetHeight?null:(e=this,t={position:n.style.position||"",visibility:n.style.visibility||"",display:n.style.display||""},e.first().css({position:"absolute",visibility:"hidden",display:"block"}),t),o=i?Math.max(n.body.scrollWidth,n.body.offsetWidth,i.scrollWidth,i.offsetWidth,i.clientWidth):n.offsetWidth,a=i?Math.max(n.body.scrollHeight,n.body.offsetHeight,i.scrollHeight,i.offsetHeight,i.clientHeight):n.offsetHeight;return r&&this.first().css(r),{height:a,width:o}},attr:function(t,n){var e,i=this[0];if("string"==typeof t||t instanceof String)return void 0===n?i?N.test(t)?!(!R.test(t)||"string"!=typeof i[t])||i[t]:"href"!=t&&"src"!=t||!G.hrefExtended?i[V](t):i[V](t,2):null:this.each(function(e){N.test(t)?e[t]=m(e,n):e[W](t,m(e,n))});for(e in t)t.hasOwnProperty(e)&&this.attr(e,t[e]);return this},removeAttr:function(t){return this.each(function(e){R.test(t)?e[t]=!1:e.removeAttribute(t)})},val:function(e){return"string"==typeof e?this.attr("value",e):this.length?this[0].value:null},data:function(t,n){var i,r,e=this[0];return void 0===n?e?(i=d(e),void 0===t?(u(e.attributes,function(e){(r=(""+e.name).match($))&&(i[l(r[1])]=p(e.value))}),i):("undefined"==typeof i[t]&&(i[t]=p(this.attr("data-"+c(t)))),i[t])):null:this.each(function(e){d(e)[t]=n})},remove:function(){return this.deepEach(t),this.detach()},empty:function(){return this.each(function(e){for(o(e.childNodes,t);e.firstChild;)e.removeChild(e.firstChild)})},detach:function(){return this.each(function(e){e[M]&&e[M].removeChild(e)})},scrollTop:function(e){return g.call(this,null,e,"y")},scrollLeft:function(e){return g.call(this,e,null,"x")}},k.setQueryEngine=function(e){J=e,delete k.setQueryEngine},k.aug=function(e,t){for(var n in e)e.hasOwnProperty(n)&&((t||_.prototype)[n]=e[n])},k.create=function(s){return"string"==typeof s&&""!==s?function(){if(I.test(s))return[w(s)];var e=s.match(/^\s*<([^\s>]+)/),t=S.createElement("div"),n=[],i=e?D[e[1].toLowerCase()]:null,r=i?i[2]+1:1,o=i&&i[3],a=M,l=G.autoTbody&&i&&"<table>"==i[0]&&!/<tbody/i.test(s);for(t.innerHTML=i?i[0]+s+i[1]:s;r--;)t=t.firstChild;for(o&&t&&1!==t.nodeType&&(t=t.nextSibling);e&&1!=t.nodeType||l&&(!t.tagName||"TBODY"==t.tagName)||n.push(t),t=t.nextSibling;);return u(n,function(e){e[a]&&e[a].removeChild(e)}),n}():a(s)?[s.cloneNode(!0)]:[]},k.doc=function(){var e=k.viewport();return{width:Math.max(S.body.scrollWidth,O.scrollWidth,e.width),height:Math.max(S.body.scrollHeight,O.scrollHeight,e.height)}},k.firstChild=function(e){for(var t,n=e.childNodes,i=0,r=n&&n.length||0;i<r;i++)1===n[i].nodeType&&(t=n[r=i]);return t},k.viewport=function(){return{width:j?O.clientWidth:self.innerWidth,height:j?O.clientHeight:self.innerHeight}},k.isAncestor="compareDocumentPosition"in O?function(e,t){return 16==(16&e.compareDocumentPosition(t))}:"contains"in O?function(e,t){return e!==t&&e.contains(t)}:function(e,t){for(;t=t[M];)if(t===e)return!0;return!1},k}),function(e,t,n){"undefined"!=typeof module&&module.exports?module.exports=n():e[t]=n()}(_altmetric,"bowser",function(){function a(n){function e(e){var t=n.match(e);return t&&1<t.length&&t[1]||""}function t(e){var t=n.match(e);return t&&1<t.length&&t[2]||""}function i(e){switch(e){case"NT":return"NT";case"XP":return"XP";case"NT 5.0":return"2000";case"NT 5.1":return"XP";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return undefined}}var r,o=e(/(ipod|iphone|ipad)/i).toLowerCase(),a=!/like android/i.test(n)&&/android/i.test(n),l=/nexus\s*[0-6]\s*/i.test(n),s=!l&&/nexus\s*[0-9]+/i.test(n),u=/CrOS/.test(n),c=/silk/i.test(n),d=/sailfish/i.test(n),p=/tizen/i.test(n),h=/(web|hpw)os/i.test(n),f=/windows phone/i.test(n),m=(/SamsungBrowser/i.test(n),!f&&/windows/i.test(n)),g=!o&&!c&&/macintosh/i.test(n),_=!a&&!d&&!p&&!h&&/linux/i.test(n),y=e(/edge\/(\d+(\.\d+)?)/i),b=e(/version\/(\d+(\.\d+)?)/i),v=/tablet/i.test(n)&&!/tablet pc/i.test(n),w=!v&&/[^-]mobi/i.test(n),k=/xbox/i.test(n);/opera/i.test(n)?r={name:"Opera",opera:T,version:b||e(/(?:opera|opr|opios)[\s\/](\d+(\.\d+)?)/i)}:/opr\/|opios/i.test(n)?r={name:"Opera",opera:T,version:e(/(?:opr|opios)[\s\/](\d+(\.\d+)?)/i)||b}:/SamsungBrowser/i.test(n)?r={name:"Samsung Internet for Android",samsungBrowser:T,version:b||e(/(?:SamsungBrowser)[\s\/](\d+(\.\d+)?)/i)}:/coast/i.test(n)?r={name:"Opera Coast",coast:T,version:b||e(/(?:coast)[\s\/](\d+(\.\d+)?)/i)}:/yabrowser/i.test(n)?r={name:"Yandex Browser",yandexbrowser:T,version:b||e(/(?:yabrowser)[\s\/](\d+(\.\d+)?)/i)}:/ucbrowser/i.test(n)?r={name:"UC Browser",ucbrowser:T,version:e(/(?:ucbrowser)[\s\/](\d+(?:\.\d+)+)/i)}:/mxios/i.test(n)?r={name:"Maxthon",maxthon:T,version:e(/(?:mxios)[\s\/](\d+(?:\.\d+)+)/i)}:/epiphany/i.test(n)?r={name:"Epiphany",epiphany:T,version:e(/(?:epiphany)[\s\/](\d+(?:\.\d+)+)/i)}:/puffin/i.test(n)?r={name:"Puffin",puffin:T,version:e(/(?:puffin)[\s\/](\d+(?:\.\d+)?)/i)}:/sleipnir/i.test(n)?r={name:"Sleipnir",sleipnir:T,version:e(/(?:sleipnir)[\s\/](\d+(?:\.\d+)+)/i)}:/k-meleon/i.test(n)?r={name:"K-Meleon",kMeleon:T,version:e(/(?:k-meleon)[\s\/](\d+(?:\.\d+)+)/i)}:f?(r={name:"Windows Phone",osname:"Windows Phone",windowsphone:T},y?(r.msedge=T,r.version=y):(r.msie=T,r.version=e(/iemobile\/(\d+(\.\d+)?)/i))):/msie|trident/i.test(n)?r={name:"Internet Explorer",msie:T,version:e(/(?:msie |rv:)(\d+(\.\d+)?)/i)}:u?r={name:"Chrome",osname:"Chrome OS",chromeos:T,chromeBook:T,chrome:T,version:e(/(?:chrome|crios|crmo)\/(\d+(\.\d+)?)/i)}:/chrome.+? edge/i.test(n)?r={name:"Microsoft Edge",msedge:T,version:y}:/vivaldi/i.test(n)?r={name:"Vivaldi",vivaldi:T,version:e(/vivaldi\/(\d+(\.\d+)?)/i)||b}:d?r={name:"Sailfish",osname:"Sailfish OS",sailfish:T,version:e(/sailfish\s?browser\/(\d+(\.\d+)?)/i)}:/seamonkey\//i.test(n)?r={name:"SeaMonkey",seamonkey:T,version:e(/seamonkey\/(\d+(\.\d+)?)/i)}:/firefox|iceweasel|fxios/i.test(n)?(r={name:"Firefox",firefox:T,version:e(/(?:firefox|iceweasel|fxios)[ \/](\d+(\.\d+)?)/i)},/\((mobile|tablet);[^\)]*rv:[\d\.]+\)/i.test(n)&&(r.firefoxos=T,r.osname="Firefox OS")):c?r={name:"Amazon Silk",silk:T,version:e(/silk\/(\d+(\.\d+)?)/i)}:/phantom/i.test(n)?r={name:"PhantomJS",phantom:T,version:e(/phantomjs\/(\d+(\.\d+)?)/i)}:/slimerjs/i.test(n)?r={name:"SlimerJS",slimer:T,version:e(/slimerjs\/(\d+(\.\d+)?)/i)}:/blackberry|\bbb\d+/i.test(n)||/rim\stablet/i.test(n)?r={name:"BlackBerry",osname:"BlackBerry OS",blackberry:T,version:b||e(/blackberry[\d]+\/(\d+(\.\d+)?)/i)}:h?(r={name:"WebOS",osname:"WebOS",webos:T,version:b||e(/w(?:eb)?osbrowser\/(\d+(\.\d+)?)/i)},/touchpad\//i.test(n)&&(r.touchpad=T)):/bada/i.test(n)?r={name:"Bada",osname:"Bada",bada:T,version:e(/dolfin\/(\d+(\.\d+)?)/i)}:p?r={name:"Tizen",osname:"Tizen",tizen:T,version:e(/(?:tizen\s?)?browser\/(\d+(\.\d+)?)/i)||b}:/qupzilla/i.test(n)?r={name:"QupZilla",qupzilla:T,version:e(/(?:qupzilla)[\s\/](\d+(?:\.\d+)+)/i)||b}:/chromium/i.test(n)?r={name:"Chromium",chromium:T,version:e(/(?:chromium)[\s\/](\d+(?:\.\d+)?)/i)||b}:/chrome|crios|crmo/i.test(n)?r={name:"Chrome",chrome:T,version:e(/(?:chrome|crios|crmo)\/(\d+(\.\d+)?)/i)}:a?r={name:"Android",version:b}:/safari|applewebkit/i.test(n)?(r={name:"Safari",safari:T},b&&(r.version=b)):o?(r={name:"iphone"==o?"iPhone":"ipad"==o?"iPad":"iPod"},b&&(r.version=b)):r=/googlebot/i.test(n)?{name:"Googlebot",googlebot:T,version:e(/googlebot\/(\d+(\.\d+))/i)||b}:{name:e(/^(.*)\/(.*) /),version:t(/^(.*)\/(.*) /)},!r.msedge&&/(apple)?webkit/i.test(n)?(/(apple)?webkit\/537\.36/i.test(n)?(r.name=r.name||"Blink",r.blink=T):(r.name=r.name||"Webkit",r.webkit=T),!r.version&&b&&(r.version=b)):!r.opera&&/gecko\//i.test(n)&&(r.name=r.name||"Gecko",r.gecko=T,r.version=r.version||e(/gecko\/(\d+(\.\d+)?)/i)),r.windowsphone||r.msedge||!a&&!r.silk?r.windowsphone||r.msedge||!o?g?(r.mac=T,r.osname="macOS"):k?(r.xbox=T,r.osname="Xbox"):m?(r.windows=T,r.osname="Windows"):_&&(r.linux=T,r.osname="Linux"):(r[o]=T,r.ios=T,r.osname="iOS"):(r.android=T,r.osname="Android");var x="";r.windows?x=i(e(/Windows ((NT|XP)( \d\d?.\d)?)/i)):r.windowsphone?x=e(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i):r.mac?x=(x=e(/Mac OS X (\d+([_\.\s]\d+)*)/i)).replace(/[_\s]/g,"."):o?x=(x=e(/os (\d+([_\s]\d+)*) like mac os x/i)).replace(/[_\s]/g,"."):a?x=e(/android[ \/-](\d+(\.\d+)*)/i):r.webos?x=e(/(?:web|hpw)os\/(\d+(\.\d+)*)/i):r.blackberry?x=e(/rim\stablet\sos\s(\d+(\.\d+)*)/i):r.bada?x=e(/bada\/(\d+(\.\d+)*)/i):r.tizen&&(x=e(/tizen[\/\s](\d+(\.\d+)*)/i)),x&&(r.osversion=x);var P=!r.windows&&x.split(".")[0];return v||s||"ipad"==o||a&&(3==P||4<=P&&!w)||r.silk?r.tablet=T:(w||"iphone"==o||"ipod"==o||a||l||r.blackberry||r.webos||r.bada)&&(r.mobile=T),r.msedge||r.msie&&10<=r.version||r.yandexbrowser&&15<=r.version||r.vivaldi&&1<=r.version||r.chrome&&20<=r.version||r.samsungBrowser&&4<=r.version||r.firefox&&20<=r.version||r.safari&&6<=r.version||r.opera&&10<=r.version||r.ios&&r.osversion&&6<=r.osversion.split(".")[0]||r.blackberry&&10.1<=r.version||r.chromium&&20<=r.version?r.a=T:r.msie&&r.version<10||r.chrome&&r.version<20||r.firefox&&r.version<20||r.safari&&r.version<6||r.opera&&r.version<10||r.ios&&r.osversion&&r.osversion.split(".")[0]<6||r.chromium&&r.version<20?r.c=T:r.x=T,r}function i(e){return e.split(".").length}function r(e,t){var n,i=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(n=0;n<e.length;n++)i.push(t(e[n]));return i}function l(e){for(var n=Math.max(i(e[0]),i(e[1])),t=r(e,function(e){var t=n-i(e);return r((e+=new Array(t+1).join(".0")).split("."),function(e){return new Array(20-e.length).join("0")+e}).reverse()});0<=--n;){if(t[0][n]>t[1][n])return 1;if(t[0][n]!==t[1][n])return-1;if(0===n)return 0}}function o(e,t,n){var i=s;"string"==typeof t&&(n=t,t=void 0),void 0===t&&(t=!1),n&&(i=a(n));var r=""+i.version;for(var o in e)if(e.hasOwnProperty(o)&&i[o]){if("string"!=typeof e[o])throw new Error("Browser version in the minVersion map should be a string: "+o+": "+String(e));return l([r,e[o]])<0}return t}function e(e,t,n){return!o(e,t,n)}var T=!0,s=a("undefined"!=typeof navigator&&navigator.userAgent||"");return s.test=function(e){for(var t=0;t<e.length;++t){var n=e[t];if("string"==typeof n&&n in s)return!0}return!1},s.isUnsupportedBrowser=o,s.compareVersions=l,s.check=e,s._detect=a,s}),function(){var e,t,n,i=[].slice;t=function(){var e;if(e=1<=arguments.length?i.call(arguments,0):[],"undefined"!=typeof console&&null!==console&&null!=console.error)try{return console.error.apply(console,e)}catch(t){return t,console.error(Array.prototype.slice.call(arguments))}},n=function(){1<=arguments.length&&i.call(arguments,0)},e=function(){1<=arguments.length&&i.call(arguments,0)},_altmetric.exports({log:n,errorLog:t,assert:e})}.call(this),function(){var e,t,n,i,r;n=function(e,t,n){var i,r,o;if(null===e||e===undefined)throw new TypeError("Object is null or undefined");if(r=0,o=e.length>>0,i=void 0,"function"!=typeof t)throw new TypeError("First argument is not callable");if(arguments.length<3){if(0===o)throw new TypeError("Array length is 0 and no second argument");i=e[0],r=1}else i=n;for(;r<o;)r in e&&(i=t.call(undefined,i,e[r],r,e),++r);return i},i=function(e){return n(e,function(e,t){return e[t[0]]=t[1],e},{})},t=function(e){return null!=Array.prototype.first?e.first():0<e.length?e[0]:void 0},r=function(e){var t,n,i,r,o,a;if(null!=Array.prototype.unique)return e.unique();for(i={},n=t=0,r=e.length;0<=r?t<=r:r<=t;n=0<=r?++t:--t)i[e[n]]=e[n];for(n in o=[],i)a=i[n],"undefined"!==n&&o.push(a);return o},e=function(e,t){var n,i,r,o;if(null!=Array.prototype.filter)return e.filter(t);for(r=[],n=0,i=e.length;n<i;n++)t(o=e[n])&&r.push(o);return r},_altmetric.exports({array_reduce:n,array_filter:e,array_to_dict:i,array_first:t,array_unique:r})}.call(this),String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")}),function(){var l,e,n,s=[].indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(t in this&&this[t]===e)return t;return-1};_altmetric.log,l=function(e){var t,n;return null!=e&&null!=(t=e.match(/(10\.\d{4,}\/[\S]+[^\.\s])/g))&&null!=(n=t[0])?n.trim():void 0},n=function(r){var i,o,a;return a=["citation_doi","dc.identifier","rft_id","dc.identifier.doi","dc.doi","prism.doi","eprints.official_url","dcterms.identifier","citation_handle_id"],i=function(){var e,t,n,i;for(i=[],e=0,t=r.length;e<t;e++)null!==(o=r[e]).name&&(n=o.name.toLowerCase(),0<=s.call(a,n))&&i.push(o);return i}(),function(){var e,t,n;for(n=[],e=0,t=i.length;e<t;e++)o=i[e],l(o.value)&&n.push(l(o.value));return n}()[0]},e=function(e){var i,t,r;return r=document.getElementsByTagName("meta"),t=function(){var e,t,n;for(n=[],e=0,t=r.length;e<t;e++)i=r[e],n.push({name:i.getAttribute("name"),value:i.getAttribute("content")});return n}(),n(t)||e},_altmetric.exports({findDOI:e,extractDOI:l,selectDOI:n})}.call(this),function(){var n,i,e;_altmetric.log,e=function(e){var t;if((t=null!=e?e.toUpperCase().replace(/-|\s/g,""):void 0)&&/^(97(8|9))?\d{9}(\d|X)$/.test(t))return 10===t.length?n(t):t},n=function(e){var t;if("string"==typeof e&&10===e.length)return t=(10-(38+3*(i(e[0])+i(e[2])+i(e[4])+i(e[6])+i(e[8]))+i(e[1])+i(e[3])+i(e[5])+i(e[7]))%10)%10,"978"+e.substring(0,9)+t},i=function(e){return parseInt(e,10)},_altmetric.exports({normalizeISBN:e})}.call(this),_altmetric.exports({sources:[{id:5,light_colour:"#FF0000",dark_colour:"#B60000",name:"News",code:"m",fetch_api_key:"news",tab_label:"News",tab_id:"msm",user_label_plural:"news outlets",user_label:"news outlet",post_types:["msm"],position:0,basic_api_key:"msm",legend_text:"Picked up by **COUNT** news outlet(s)",cited_counts:["cited_by_msm_count"],name_plural:"news stories",filter:"",request_id:"news"},{id:18,light_colour:"#5fb441",dark_colour:"#33741c",name:"Book reviews",code:"k",fetch_api_key:"book_reviews",tab_label:"Book reviews",tab_id:"book_reviews",user_label_plural:"book reviewers",user_label:"book reviewer",post_types:["book_review"],position:2,basic_api_key:"book_reviews",legend_text:"Reviewed in **COUNT** outlet(s)",cited_counts:["cited_by_book_reviews_count"],name_plural:"book reviews",filter:"",request_id:"book-reviews"},{id:2,light_colour:"#ffd140",dark_colour:"#e89500",name:"Blogs",code:"b",fetch_api_key:"blogs",tab_label:"Blogs",tab_id:"blogs",user_label_plural:"blogs",user_label:"blog",post_types:["blog"],position:3,basic_api_key:"feeds",legend_text:"Blogged by **COUNT**",cited_counts:["cited_by_feeds_count"],name_plural:"blog posts",filter:"",request_id:"blogs"},{id:16,light_colour:"#9f79f2",dark_colour:"#5b17e8",name:"Policy documents",code:"d",fetch_api_key:"policy",tab_label:"Policy documents",tab_id:"policy",user_label_plural:"policy sources",user_label:"policy source",post_types:["policy"],position:4,basic_api_key:"policies",legend_text:"Referenced in **COUNT** policy source(s)",cited_counts:["cited_by_policies_count"],name_plural:"policy documents",filter:"",request_id:"policy-documents"},{id:1,light_colour:"#74CFED",dark_colour:"#2F90B9",name:"X",code:"t",fetch_api_key:"twitter",tab_label:"X",tab_id:"twitter",user_label_plural:"X users",user_label:"X user",post_types:["tweet"],position:5,basic_api_key:"tweeters",legend_text:"Posted by **COUNT** X user(s)",cited_counts:["cited_by_tweeters_count"],name_plural:"tweets",filter:"",request_id:"twitter"},{id:19,light_colour:"#a6e8bc",dark_colour:"#7cad8d",name:"Syllabi",code:"y",fetch_api_key:"syllabi",tab_label:"Syllabi",tab_id:"syllabi",user_label_plural:"institutions with syllabi",user_label:"institution with syllabi",post_types:["syllabus"],position:6,basic_api_key:"syllabi",legend_text:"Referenced in **COUNT** syllabi",cited_counts:["cited_by_syllabi_count"],name_plural:"syllabi",filter:"",request_id:"syllabi"},{id:20,light_colour:"#f27700",dark_colour:"#d45029",name:"Patents",code:"a",fetch_api_key:"patent",tab_label:"Patents",tab_id:"patent",user_label_plural:"patents",user_label:"patent",post_types:["patent"],position:7,basic_api_key:"patents",legend_text:"Referenced in **COUNT** patents",cited_counts:["cited_by_patents_count"],name_plural:"patents",filter:"",request_id:"patents"},{id:14,light_colour:"#efefef",dark_colour:"#bdbdbd",name:"Peer reviews",code:"e",fetch_api_key:"peer_reviews",tab_label:"Peer reviews",tab_id:"peer_reviews",user_label_plural:"peer review sites",user_label:"peer review site",post_types:["peer_review"],position:8,basic_api_key:"peer_review_sites",legend_text:"Mentioned by **COUNT** peer review site(s)",cited_counts:["cited_by_peer_review_sites_count"],name_plural:"peer reviews",filter:"",request_id:"peer-reviews"},{id:15,light_colour:"#e1a400",dark_colour:"#df931b",name:"Weibo",code:"s",fetch_api_key:"weibo",tab_label:"Weibo",tab_id:"weibo",user_label_plural:"weibo users",user_label:"weibo user",post_types:["weibo"],position:9,basic_api_key:"weibo",legend_text:"Mentioned by **COUNT** weibo user(s)",cited_counts:["cited_by_weibo_count"],name_plural:"weibo posts",filter:"",request_id:"weibo"},{id:3,light_colour:"#2445bd",dark_colour:"#0b2ca4",name:"Facebook",code:"f",fetch_api_key:"facebook",tab_label:"Facebook",tab_id:"facebook",user_label_plural:"Facebook pages",user_label:"Facebook page",post_types:["fbwall"],position:10,basic_api_key:"fbwalls",legend_text:"On **COUNT** Facebook page(s)",cited_counts:["cited_by_fbwalls_count"],name_plural:"Facebook posts",filter:"",request_id:"facebook"},{id:13,light_colour:"#958899",dark_colour:"#3b2a3d",name:"Wikipedia",code:"w",fetch_api_key:"wikipedia",tab_label:"Wikipedia",tab_id:"wikipedia",user_label_plural:"Wikipedia pages",user_label:"Wikipedia page",post_types:["wikipedia"],position:11,basic_api_key:"wikipedia",legend_text:"Referenced in **COUNT** Wikipedia pages",cited_counts:["cited_by_wikipedia_count"],name_plural:"Wikipedia pages",filter:"",request_id:"wikipedia"},{id:4,light_colour:"#E065BB",dark_colour:"#912470",name:"Google+",code:"g",fetch_api_key:"googleplus",tab_label:"Google+",tab_id:"gplus",user_label_plural:"Google+ users",user_label:"Google+ user",post_types:["gplus"],position:12,basic_api_key:"gplus",legend_text:"Mentioned in **COUNT** Google+ post(s)",cited_counts:["cited_by_gplus_count"],name_plural:"Google+ posts",filter:"",request_id:"google"},{id:6,light_colour:"#1E90FF",dark_colour:"#00BFFF",name:"LinkedIn",code:"l",fetch_api_key:"linkedin",tab_label:"LinkedIn",tab_id:"linkedin",user_label_plural:"LinkedIn users",user_label:"LinkedIn user",post_types:["linkedin"],position:13,basic_api_key:"linkedin",legend_text:"Mentioned in **COUNT** LinkedIn forum(s)",cited_counts:["cited_by_linkedin_count"],name_plural:"LinkedIn posts",filter:"",request_id:"linkedin"},{id:7,light_colour:"#D5E8F0",dark_colour:"#B9DDEB",name:"Reddit",code:"r",fetch_api_key:"reddit",tab_label:"Reddit",tab_id:"reddit",user_label_plural:"Redditors",user_label:"Redditor",post_types:["rdt"],position:14,basic_api_key:"rdts",legend_text:"Reddited by **COUNT**",cited_counts:["cited_by_rdts_count"],name_plural:"Reddit posts",filter:"",request_id:"reddit"},{id:9,light_colour:"#cc6600",dark_colour:"#9c4e00",name:"Pinterest",code:"p",fetch_api_key:"pinterest",tab_label:"Pinterest",tab_id:"pinterest",user_label_plural:"Pinners",user_label:"Pinner",post_types:["pinterest"],position:15,basic_api_key:"pinners",legend_text:"Pinned by **COUNT** on Pinterest",cited_counts:["cited_by_pinners_count"],name_plural:"pins",filter:"",request_id:"pinterest"},{id:8,light_colour:"#F4006E",dark_colour:"#CB2D2D",name:"F1000",code:"1",fetch_api_key:"f1000",tab_label:"Research highlights",tab_id:"highlights",user_label_plural:"research highlight platforms",user_label:"research highlight platform",post_types:["f1000","rh"],position:16,basic_api_key:"rh",legend_text:"Highlighted by **COUNT** platform(s)",cited_counts:["cited_by_f1000_count","cited_by_rh_count"],name_plural:"Research highlight platforms",filter:"",request_id:"research-highlights"},{id:10,light_colour:"#DEDEDE",dark_colour:"#EFEFEF",name:"QnA",code:"q",fetch_api_key:"q&a",tab_label:"Q&A",tab_id:"qna",user_label_plural:"Q&A threads",user_label:"Q&A thread",post_types:["qna"],position:17,basic_api_key:"qna",legend_text:"Mentioned in **COUNT** Q&A thread(s)",cited_counts:[
"cited_by_forums_count","cited_by_qna_count","cited_by_qs_count"],name_plural:"Q&A threads",filter:"",request_id:"qa"},{id:11,light_colour:"#94DB5E",dark_colour:"#98C973",name:"Video",code:"v",fetch_api_key:"video",tab_label:"YouTube",tab_id:"videos",user_label_plural:"YouTube creators",user_label:"YouTube creator",post_types:["video"],position:18,basic_api_key:"videos",legend_text:"On **COUNT** video(s)",cited_counts:["cited_by_videos_count"],name_plural:"videos",filter:"",request_id:"video"},{id:12,light_colour:"#DEDEDE",dark_colour:"#EFEFEF",name:"Unknown",code:"?",fetch_api_key:null,tab_label:"Misc.",tab_id:"misc",user_label_plural:"misc. posts",user_label:"misc. post",post_types:["unknown","other"],position:19,basic_api_key:"",legend_text:"Mentioned in **COUNT** misc. posts",cited_counts:["cited_by_unknown_count"],name_plural:"misc. posts",filter:"",request_id:"misc"},{id:21,light_colour:"#A1E3E4",dark_colour:"#15BABC",name:"Clinical guidelines",code:"c",fetch_api_key:"guideline",tab_label:"Clinical guidelines",tab_id:"guideline",user_label_plural:"clinical guideline sources",user_label:"clinical guideline source",post_types:["guideline"],position:20,basic_api_key:"guidelines",legend_text:"Referenced in **COUNT** clinical guideline source(s)",cited_counts:["cited_by_guidelines_count"],name_plural:"clinical guidelines",filter:"",request_id:"guidelines"},{id:22,light_colour:"#70B1FF",dark_colour:"#1185FE",name:"Bluesky",code:"u",fetch_api_key:"bluesky",tab_label:"Bluesky",tab_id:"bluesky",user_label_plural:"Bluesky users",user_label:"Bluesky user",post_types:["bluesky"],position:21,basic_api_key:"bluesky",legend_text:"Referenced by **COUNT** Bluesky user(s)",cited_counts:["cited_by_bluesky_count"],name_plural:"Bluesky posts",filter:"",request_id:"bluesky"},{id:23,light_colour:"#E368EE",dark_colour:"#90089C",name:"Podcasts",code:"o",fetch_api_key:"podcasts",tab_label:"Podcasts",tab_id:"podcasts",user_label_plural:"podcasts",user_label:"podcast",post_types:["podcast"],position:22,basic_api_key:"podcasts",legend_text:"Picked up by **COUNT** podcasts",cited_counts:["cited_by_podcasts_count"],name_plural:"podcast episodes",filter:"",request_id:"podcasts"}]}),function(){return this.AltmetricTemplates||(this.AltmetricTemplates={}),this.AltmetricTemplates.image_or_text=Handlebars.template({1:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return'    <img alt="'+e.escapeExpression("function"==typeof(o=null!=(o=a(n,"alt_text")||(null!=t?a(t,"alt_text"):t))?o:e.hooks.helperMissing)?o.call(null!=t?t:e.nullContext||{},{name:"alt_text",hash:{},data:r,loc:{start:{line:2,column:14},end:{line:2,column:26}}}):o)+'" src="'+e.escapeExpression("function"==typeof(o=null!=(o=a(n,"img_src")||(null!=t?a(t,"img_src"):t))?o:e.hooks.helperMissing)?o.call(null!=t?t:e.nullContext||{},{name:"img_src",hash:{},data:r,loc:{start:{line:2,column:33},end:{line:2,column:44}}}):o)+'" width="'+e.escapeExpression("function"==typeof(o=null!=(o=a(n,"img_width")||(null!=t?a(t,"img_width"):t))?o:e.hooks.helperMissing)?o.call(null!=t?t:e.nullContext||{},{name:"img_width",hash:{},data:r,loc:{start:{line:2,column:53},end:{line:2,column:66}}}):o)+'" height="'+e.escapeExpression("function"==typeof(o=null!=(o=a(n,"img_height")||(null!=t?a(t,"img_height"):t))?o:e.hooks.helperMissing)?o.call(null!=t?t:e.nullContext||{},{name:"img_height",hash:{},data:r,loc:{start:{line:2,column:76},end:{line:2,column:90}}}):o)+'" style="border:0; margin:0; max-width: none;">\n'},3:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return'    <span class="_altmetric_score">'+e.escapeExpression("function"==typeof(o=null!=(o=a(n,"score")||(null!=t?a(t,"score"):t))?o:e.hooks.helperMissing)?o.call(null!=t?t:e.nullContext||{},{name:"score",hash:{},data:r,loc:{start:{line:4,column:35},end:{line:4,column:44}}}):o)+"</span>\n"},compiler:[8,">= 4.3.0"],main:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return null!=(o=a(n,"if").call(null!=t?t:e.nullContext||{},null!=t?a(t,"show_img"):t,{name:"if",hash:{},fn:e.program(1,r,0),inverse:e.program(3,r,0),data:r,loc:{start:{line:1,column:0},end:{line:5,column:7}}}))?o:""},useData:!0}),this.AltmetricTemplates.image_or_text}.call(this),function(){return this.AltmetricTemplates||(this.AltmetricTemplates={}),this.AltmetricTemplates.badge=Handlebars.template({compiler:[8,">= 4.3.0"],main:function(e,t,n,i,r){var o,a,l=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return'<a target="'+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"linkTarget")||(null!=t?l(t,"linkTarget"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"linkTarget",hash:{},data:r,loc:{start:{line:1,column:11},end:{line:1,column:25}}}):a)+'" href="'+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"link")||(null!=t?l(t,"link"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"link",hash:{},data:r,loc:{start:{line:1,column:33},end:{line:1,column:41}}}):a)+'" style="display:inline-block;">\n'+(null!=(o=e.invokePartial(l(i,"image_or_text"),t,{name:"image_or_text",data:r,indent:"    ",helpers:n,partials:i,decorators:e.decorators}))?o:"")+"</a>"},usePartial:!0,useData:!0}),this.AltmetricTemplates.badge}.call(this),function(){return this.AltmetricTemplates||(this.AltmetricTemplates={}),this.AltmetricTemplates.badge_with_details=Handlebars.template({compiler:[8,">= 4.3.0"],main:function(e,t,n,i,r){var o,a,l=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return'<div style="overflow:hidden;">\n    <div class=\''+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"legendStyle")||(null!=t?l(t,"legendStyle"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"legendStyle",hash:{},data:r,loc:{start:{line:2,column:16},end:{line:2,column:31}}}):a)+"'>\n        <a target=\""+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"linkTarget")||(null!=t?l(t,"linkTarget"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"linkTarget",hash:{},data:r,loc:{start:{line:3,column:19},end:{line:3,column:33}}}):a)+'" href="'+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"link")||(null!=t?l(t,"link"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"link",hash:{},data:r,loc:{start:{line:3,column:41},end:{line:3,column:49}}}):a)+'" style="display:inline-block;">\n'+(null!=(o=e.invokePartial(l(i,"image_or_text"),t,{name:"image_or_text",data:r,indent:"            ",helpers:n,partials:i,decorators:e.decorators}))?o:"")+'        </a>\n        <p class=\'altmetric-see-more-details\' style="padding-top: 10px; text-align: center;"><a target="'+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"linkTarget")||(null!=t?l(t,"linkTarget"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"linkTarget",hash:{},data:r,loc:{start:{line:6,column:104},end:{line:6,column:118}}}):a)+'" href="'+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"link")||(null!=t?l(t,"link"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"link",hash:{},data:r,loc:{start:{line:6,column:126},end:{line:6,column:134}}}):a)+'">See more details</a></p>\n    </div>\n    <div id="_altmetric_popover_el_'+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"uuid")||(null!=t?l(t,"uuid"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"uuid",hash:{},data:r,loc:{start:{line:8,column:35},end:{line:8,column:43}}}):a)+'" class="altmetric-embed right" style="margin:0; padding:0; display:inline-block; float:left; position:relative;">\n        <div class="altmetric_container">\n            <div class="altmetric-embed altmetric-popover-inner right">\n                <div style="padding:0; margin: 0;" class="altmetric-embed altmetric-popover-content">\n                    '+(null!=(o="function"==typeof(a=null!=(a=l(n,"contents")||(null!=t?l(t,"contents"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"contents",hash:{},data:r,loc:{start:{line:12,column:20},end:{line:12,column:34}}}):a)?o:"")+"\n                </div>\n            </div>\n        </div>\n    </div>\n</div>"},usePartial:!0,useData:!0}),this.AltmetricTemplates.badge_with_details}.call(this),function(){return this.AltmetricTemplates||(this.AltmetricTemplates={}),this.AltmetricTemplates.badge_with_popover=Handlebars.template({compiler:[8,">= 4.3.0"],main:function(e,t,n,i,r){var o,a,l=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return'<a target="'+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"linkTarget")||(null!=t?l(t,"linkTarget"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"linkTarget",hash:{},data:r,loc:{start:{line:1,column:11},end:{line:1,column:25}}}):a)+'" href="'+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"link")||(null!=t?l(t,"link"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"link",hash:{},data:r,loc:{start:{line:1,column:33},end:{line:1,column:41}}}):a)+'" rel="popover" data-content="<div>'+(null!=(o="function"==typeof(a=null!=(a=l(n,"contents")||(null!=t?l(t,"contents"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"contents",hash:{},data:r,loc:{start:{line:1,column:76},end:{line:1,column:90}}}):a)?o:"")+'</div>" style="display:inline-block;" data-badge-popover="'+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"position")||(null!=t?l(t,"position"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"position",hash:{},data:r,loc:{start:{line:1,column:148},end:{line:1,column:160}}}):a)+'">\n'+(null!=(o=e.invokePartial(l(i,"image_or_text"),t,{name:"image_or_text",data:r,indent:"    ",helpers:n,partials:i,decorators:e.decorators}))?o:"")+"</a>"},usePartial:!0,useData:!0}),this.AltmetricTemplates.badge_with_popover}.call(this),function(){return this.AltmetricTemplates||(this.AltmetricTemplates={}),this.AltmetricTemplates.legend=Handlebars.template({1:function(e,t,n,i,r){var o,a,l=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"    <div style='padding-left: 10px; line-height:18px; border-left: 16px solid "+e.escapeExpression(e.lambda((o=r&&l(r,"source"))&&l(o,"light_colour"),t))+";'>\n      <a class='link-to-altmetric-details-tab' target='"+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"linkTarget")||(null!=t?l(t,"linkTarget"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"linkTarget",hash:{},data:r,loc:{start:{line:3,column:55},end:{line:3,column:69}}}):a)+"' href='"+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"link")||(null!=t?l(t,"link"):t))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"link",hash:{},data:r,loc:{start:{line:3,column:77},end:{line:3,column:85}}}):a)+"&tab="+e.escapeExpression(e.lambda((o=r&&l(r,"source"))&&l(o,"request_id"),t))+"'>\n"+(null!=(o=l(n,"if").call(null!=t?t:e.nullContext||{},null!=t?l(t,"condensedLegend"):t,{name:"if",hash:{},fn:e.program(2,r,0),inverse:e.program(4,r,0),data:r,loc:{start:{line:4,column:8},end:{line:8,column:15}}}))?o:"")+"      </a>\n    </div>\n"},2:function(e,t,n,i,r){var o,a,l=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"          "+e.escapeExpression(e.lambda((o=r&&l(r,"source"))&&l(o,"name"),t))+" ("+e.escapeExpression("function"==typeof(a=null!=(a=l(n,"count")||r&&l(r,"count"))?a:e.hooks.helperMissing)?a.call(null!=t?t:e.nullContext||{},{name:"count",hash:{},data:r,loc:{start:{line:5,column:28},end:{line:5,column:38}}}):a)+")\n"},4:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"          "+(null!=(o=(a(n,"legendText")||t&&a(t,"legendText")||e.hooks.helperMissing).call(null!=t?t:e.nullContext||{},(o=r&&a(r,"source"))&&a(o,"legend_text"),r&&a(r,"count"),{name:"legendText",hash:{},data:r,loc:{start:{line:7,column:10},end:{line:7,column:53}}}))?o:"")+"\n"},6:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"    <div class='altmetric-embed readers' style='margin-top: 10px;'>\n"+(null!=(o=a(n,"if").call(null!=t?t:e.nullContext||{},null!=(o=null!=t?a(t,"readerVisibility"):t)?a(o,"mendeley"):o,{name:"if",hash:{},fn:e.program(7,r,0),inverse:e.noop,data:r,loc:{start:{line:15,column:6},end:{line:24,column:13}}}))?o:"")+(null!=(o=a(n,"if").call(null!=t?t:e.nullContext||{},null!=(o=null!=t?a(t,"readerVisibility"):t)?a(o,"citeulike"):o,{name:"if",hash:{},fn:e.program(12,r,0),inverse:e.noop,data:r,loc:{start:{line:25,column:6},end:{line:34,column:13}}}))?o:"")+"    </div>\n"},7:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"          <div class='altmetric-embed tip_mendeley'\n               style='padding-left: 10px; line-height:18px; border-left: 16px solid #A60000;'>\n"+(null!=(o=a(n,"if").call(null!=t?t:e.nullContext||{},null!=t?a(t,"condensedLegend"):t,{name:"if",hash:{},fn:e.program(8,r,0),inverse:e.program(10,r,0),data:r,loc:{start:{line:18,column:12},end:{line:22,column:19}}}))?o:"")+"          </div>\n"},8:function(e,t){var n,i=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"              Mendeley ("+e.escapeExpression(e.lambda(null!=(n=null!=t?i(t,"readers"):t)?i(n,"mendeley"):n,t))+")\n"},10:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"              <b>"+e.escapeExpression(e.lambda(null!=(o=null!=t?a(t,"readers"):t)?a(o,"mendeley"):o,t))+"</b> "+e.escapeExpression((a(n,"pluralize")||t&&a(t,"pluralize")||e.hooks.helperMissing).call(null!=t?t:e.nullContext||{},null!=(o=null!=t?a(t,"readers"):t)?a(o,"mendeley"):o,"reader",{name:"pluralize",hash:{},data:r,loc:{start:{line:21,column:42},end:{line:21,column:81}}}))+" on Mendeley\n"},12:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"          <div class='altmetric-embed tip_citeulike'\n               style='padding-left: 10px; line-height:18px; border-left: 16px solid #BCD2EF;'>\n"+(null!=(o=a(n,"if").call(null!=t?t:e.nullContext||{},null!=t?a(t,"condensedLegend"):t,{name:"if",hash:{},fn:e.program(13,r,0),inverse:e.program(15,r,0),data:r,loc:{start:{line:28,column:12},end:{line:32,column:19}}}))?o:"")+"          </div>\n"},13:function(e,t){var n,i=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"              CiteULike ("+e.escapeExpression(e.lambda(null!=(n=null!=t?i(t,"readers"):t)?i(n,"citeulike"):n,t))+")\n"},15:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"              <b>"+e.escapeExpression(e.lambda(null!=(o=null!=t?a(t,"readers"):t)?a(o,"citeulike"):o,t))+"</b> "+e.escapeExpression((a(n,"pluralize")||t&&a(t,"pluralize")||e.hooks.helperMissing).call(null!=t?t:e.nullContext||{},null!=(o=null!=t?a(t,"readers"):t)?a(o,"citeulike"):o,"reader",{name:"pluralize",hash:{},data:r,loc:{start:{line:31,column:43},end:{line:31,column:83}}}))+" on CiteULike\n"},17:function(e,t,n,i,r){var o,a=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined};return"    <div style='margin-top: 10px; text-align: center;'>\n        <a class='altmetric_details' target='"+e.escapeExpression("function"==typeof(o=null!=(o=a(n,"linkTarget")||(null!=t?a(t,"linkTarget"):t))?o:e.hooks.helperMissing)?o.call(null!=t?t:e.nullContext||{},{name:"linkTarget",hash:{},data:r,loc:{start:{line:39,column:45},end:{line:39,column:59}}}):o)+"' href='"+e.escapeExpression("function"==typeof(o=null!=(o=a(n,"link")||(null!=t?a(t,"link"):t))?o:e.hooks.helperMissing)?o.call(null!=t?t:e.nullContext||{},{name:"link",hash:{},data:r,loc:{start:{line:39,column:67},end:{line:39,column:75}}}):o)+"'>\n            See more details\n        </a>\n    </div>\n"},compiler:[8,">= 4.3.0"],main:function(e,t,n,i,r){var o,a,l,s=e.lookupProperty||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:undefined},u="";return a=null!=(a=s(n,"eachSourceWithCount")||(null!=t?s(t,"eachSourceWithCount"):t))?a:e.hooks.helperMissing,l={name:"eachSourceWithCount",hash:{},fn:e.program(1,r,0),inverse:e.noop,data:r,loc:{start:{line:1,column:0},end:{line:11,column:24}}},o="function"==typeof a?a.call(null!=t?t:e.nullContext||{},l):a,s(n,"eachSourceWithCount")||(o=e.hooks.blockHelperMissing.call(t,o,l)),null!=o&&(u+=o),u+"\n"+(null!=(o=s(n,"if").call(null!=t?t:e.nullContext||{},null!=(o=null!=t?s(t,"readerVisibility"):t)?s(o,"visible"):o,{name:"if",hash:{},fn:e.program(6,r,0),inverse:e.noop,data:r,loc:{start:{line:13,column:0},end:{line:36,column:7}}}))?o:"")+(null!=(o=s(n,"if").call(null!=t?t:e.nullContext||{},null!=t?s(t,"popover"):t,{name:"if",hash:{},fn:e.program(17,r,0),inverse:e.noop,data:r,loc:{start:{line:37,column:0},end:{line:43,column:7}}}))?o:"")},useData:!0}),this.AltmetricTemplates.legend}.call(this),function(){var s,i,r,t,n=[].slice;s=Handlebars.noConflict(),i=_altmetric.assert,t=_altmetric.log,r=_altmetric.encodeHTML,s.registerHelper("pluralize",function(e,t){return 1===e?t:t+"s"}),s.registerHelper("eachSourceWithCount",function(r){var o,a,l;return function(){var e,t,n,i;for(i=[],e=0,t=(n=_altmetric.sources).length;e<t;e++)l=n[e],(o="cited_by_"+l.basic_api_key+"_count")in this?((a=s.createFrame(r.data||{})).source=l,a.count=this[o],i.push(r.fn(this,{data:a}))):i.push(void 0);return i}.call(this).join("")}),s.registerHelper("legendText",function(e,t){var n;return i(void 0!==e,"LegendText should not be undefined"),i(void 0!==t,"Count should never be undefined"),n=0<t,r(e).replace(/\*\*(\w+)\*\*/,"<b>$1</b>").replace(/COUNT/,t).replace(/(\w+)\(s\)/,n?"$1s":"$1")}),s.registerHelper("donutImgUrl",function(e){var t;return t=e.hash.size,new s.SafeString("<img src='"+s.Utils.escapeExpression(this.donutImageUrl(2*t))+"' width='"+t+"' height='"+t+"'/>")}),s.registerPartial("image_or_text",AltmetricTemplates.image_or_text),s.registerHelper("log",function(){var e;return e=1<=arguments.length?n.call(arguments,0):[],t.apply(null,e)})}.call(this),function(){var e,t,l,n,o,a,i=function(e,t){return function(){return e.apply(t,arguments)}};o=_altmetric.log,a=_altmetric.qwery,t=_altmetric.bean,l=_altmetric.bonzo,n=_altmetric.array_first,e=function(){function e(e,t){this.badge_element=e,this.hidden=null==t||t,this.hide=i(this.hide,this),this.pauseHide=i(this.pauseHide,this),this.cancelHide=i(this.cancelHide,this),this.show=i(this.show,this),this.pauseShow=i(this.pauseShow,this),this.tapElement=i(this.tapElement,this),this.element=n(a("a[rel=popover]",this.badge_element)),this.element||(this.element=this.badge_element.firstChild),this.contents=this.element.getAttribute("data-content"),this.position=this.element.getAttribute("data-badge-popover"),this.uuid=this.badge_element.getAttribute("data-uuid")}return e.prototype.build=function(){return this.buildPopover(),this.attachEvents()},e.prototype.cssPath=function(){var e;return o("css_url",e="https://embed.altmetric.com/assets/embed-59614f5c46b49b21eeef3bb28c4fb38d1e7069e8d014752fcb66e84942556802.css"),e},e.prototype.insertCSS=function(){var e,t;if(0===a("#altmetric-embed-css").length)return e=this.cssPath(),o("injecting CSS: "+e),(t=document.createElement("link")).setAttribute("id","altmetric-embed-css"),t.setAttribute("rel","stylesheet"),t.setAttribute("type","text/css"),l(a("head")).append(t),t.setAttribute("href",e)},e.prototype.buildPopover=function(){var e,t,n,i,r;return i=a("div.altmetric-popover[data-uuid="+this.uuid+"]"),r=a("div.altmetric-popover-inner[data-uuid="+this.uuid+"]"),0<i.length&&0<r.length?(this.el=i[0],this.inner=r[0]):(o("creating popover for "+this.uuid),(t=document.createElement("div")).setAttribute("class","altmetric_container"),(e=document.createElement("div")).setAttribute("class","altmetric_arrow altmetric-"+this.position),t.appendChild(e),this.inner=document.createElement("div"),t.appendChild(this.inner),this.el=document.createElement("div"),this.el.appendChild(t),document.body.appendChild(this.el)),this.el.setAttribute("class","altmetric-embed altmetric-popover altmetric-"+this.position),this.el.setAttribute("data-uuid",this.uuid),this.el.setAttribute("id","_altmetric_popover_el_"+this.uuid),this.el.style.margin="0px",this.inner.setAttribute("class","altmetric-embed altmetric-popover-inner altmetric-floating altmetric-"+this.position),this.inner.setAttribute("data-uuid",this.uuid),(n=document.createElement("div")).setAttribute("class","altmetric-embed altmetric-popover-content altmetric-floating"),n.innerHTML=this.contents,l(this.inner).html(n),l(this.el).show("block").hide()},e.prototype.attachEvents=function(){return t.add(this.element,"mouseover",this.pauseShow),t.add(this.element,"touchstart",this.tapElement),t.add(this.el,"mouseover",this.cancelHide),t.add(this.el,"mouseleave.pause",this.pauseHide)},e.prototype.tapElement=function(e){if(this.hidden)return e.stop(),this.pauseShow()},e.prototype.pauseShow=function(){return this.hidden=!1,t.add(this.element,"mouseleave.pause",this.pauseHide),setTimeout((e=this,function(){return e.show()}),300);var e},e.prototype.show=function(){var e,t,n,i,r,o,a;if(!this.hidden)switch(n=l(this.element).offset(),l(this.el).show("block"),t=this.el.offsetWidth,e=this.el.offsetHeight,o=n.top,r=n.left,a=n.width,i=n.height,this.position){case"right":return this.el.style.top=o+i/2-e/2+"px",this.el.style.left=r+a+"px";case"left":return this.el.style.top=o+i/2-e/2+"px",this.el.style.left=r-t+"px";case"top":return this.el.style.top=o-e+"px",this.el.style.left=r+a/2-t/2+"px";case"bottom":return this.el.style.top=o+i+"px",this.el.style.left=r+a/2-t/2+"px"}},e.prototype.cancelHide=function(){return this.hide_popover=!1},e.prototype.pauseHide=function(){return this.hide_popover=!0,setTimeout((e=this,function(){return e.hide()}),300);var e},e.prototype.hide=function(){if(this.hide_popover)return l(this.el).hide(),this.hidden=!0},e}(),_altmetric.exports({Popover:e})}.call(this),function(){var e,t;_altmetric.log,t=function(e,t){var n;return n=e.images.small,t=Math.min(t,320),n.replace(/size=\d+/,"size="+t)},e=function(e,t){var n;return n=e.images.medium,t=Math.min(t,320),n=n.replace(/size=(\d+)/,"size="+t),/style=(\w+)/.test(n)?n.replace(/style=(\w+)/,"style=bar"):n+"&style=bar"},_altmetric.exports({donut_url:t,bar_url:e})}.call(this),function(){var e,u={}.hasOwnProperty;e=function(){function e(e){this.altmetric_info=e}return e.prototype.lowercaseHandles=function(){var e;return e=this.altmetric_info.handles||[],this.altmetric_info.handle&&(e=_altmetric.array_unique(e.concat(this.altmetric_info.handle))),e=e.map(function(e){return e.toLowerCase()})},e.prototype.getIdentifiers=function(){var e,t;return[(e=this.altmetric_info).altmetric_id,e.pmid,e.arxiv_id,e.uri,e.urn,null!=(t=e.doi)?t.toLowerCase():void 0,e.nct_id].concat(e.isbns).concat(this.lowercaseHandles())},e.prototype.getMatchingBadges=function(e){var t,n,i,r,o,a,l,s;for(a=[],n=0,o=(r=this.getIdentifiers()).length;n<o;n++)if((i=r[n])&&e.hasOwnProperty(i))for(s in l=e[i])u.call(l,s)&&(t=l[s],a.push(t));return 0===a.length&&_altmetric.log("No matching badges for "+r),a},e}(),_altmetric.exports({BadgesMatcher:e})}.call(this),function(){var a,i,r,t,n,o,l,e,s,u,c,d,p,h,f,m,g,_;h=_altmetric.log,u=_altmetric.errorLog,g=_altmetric.qwery,o=_altmetric.bowser,t=_altmetric.bean,n=_altmetric.bonzo,i=_altmetric.api_uri,r=_altmetric.api_version,l=_altmetric.details_uri,a=function(){function e(e){var t,n;this.element=e,this.setUUID(),this.classes=this.element.className.split(" "),this.altmetric_id=this.getAltmetricId(),this.doi=this.getDoi(),this.arxiv_id=this.getArxivId(),this.pubmed_id=this.getPubmedId(),this.handle_id=this.getHandle(),this.uri=this.getURI(),this.urn=this.getURN(),this.nct_id=this.getNCTID(),this.isbn=this.getISBN(),this.dataBadgeType=this.getProperty("badge-type"),this.detailsTemplate=this.getProperty("template"),this.popoverPosition=this.getProperty("badge-popover"),this.detailsPosition=this.getProperty("badge-details"),this.condensed="true"===(null!=(t=this.getProperty("condensed"))?t.toLowerCase():void 0),this.linkTarget=this.getProperty("link-target")||"_self",this.badge_cdn="https://d1uo4w7k31k5mn.cloudfront.net",this.hasScore=null==this.getProperty("no-score"),this.cache_breaker=this._getCacheBreaker(),this.badgeSizes=this._getBadgeSizes(),this.getProperty("hide-less-than")&&(this.hideLessThanScore=parseInt(this.getProperty("hide-less-than"),10)),"true"===(null!=(n=this.getProperty("hide-no-mentions"))?n.toLowerCase():void 0)&&!this.hideLessThanScore&&(this.hideLessThanScore=1)}return e.prototype.getClassProperty=function(e){var r,o,t,n;if(r="altmetric-"+e,n=function(){var e,t,n,i;for(i=[],e=0,t=(n=this.classes).length;e<t;e++)0===(o=n[e]).indexOf(r)&&i.push(o.replace(/\s+/g,""));return i}.call(this),t=_altmetric.array_first(n))return t.substring(r.length+1)},e.prototype.getProperty=function(e){return this.element.getAttribute("data-"+e)||this.getClassProperty(e)},e.prototype.defaultBadgeType=function(){return"v2"},e.prototype.badgeType=function(){return null==this.dataBadgeType?this.defaultBadgeType():this.dataBadgeType.match(/\d+/)?"v"+this.dataBadgeType:this.dataBadgeType.match(/(donut|bar|text)/)?this.dataBadgeType.replace("-","_"):this.defaultBadgeType()},e.prototype.badgePath=function(){return this.badgeType().match(/donut/)?"donut":this.badgeType().match(/bar/)?"bar":this.useHighQualityBadge()?this.badgeType()+"_hq":this.badgeType()},e.prototype.setUUID=function(){var e;return e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)},this.uuid=this.element.getAttribute("data-uuid")||e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e(),this.element.setAttribute("data-uuid",this.uuid)},e.prototype.getDoi=function(){var e,t;return _altmetric.extractDOI(null!=(e=this.getProperty("doi"))?e.toLowerCase():void 0)||(null!=(t=this.getProperty("doi"))?t.toLowerCase():void 0)},e.prototype.getArxivId=function(){var e,t;return null!=(e=this.getProperty("arxiv-id"))&&null!=(t=e.match(/([^\:]*)$/g))?t[0]:void 0},e.prototype.getAltmetricId=function(){return this.getProperty("altmetric-id")},e.prototype.getPubmedId=function(){return this.getProperty("pmid")},e.prototype.getHandle=function(){var e;return null!=(e=this.getProperty("handle"))?e.toLowerCase():void 0},e.prototype.getURI=function(){return this.getProperty("uri")},e.prototype.getURN=function(){return this.getProperty("urn")},e.prototype.getISBN=function(){var e;return(e=_altmetric.normalizeISBN(this.getProperty("isbn")))?e:this.getProperty("isbn")},e.prototype.getNCTID=function(){return this.getProperty("nct-id")},e.prototype.badgeId=function(){var e;return this.altmetric_id||this.doi||this.arxiv_id||this.pubmed_id||this.handle_id||this.uri||this.urn||this.isbn||this.nct_id||(this.doi=null!=(e=_altmetric.findDOI(void 0))?e.toLowerCase():void 0)},e.prototype.insertPlaceholder=function(){var e,t,n,i,r,o;return i=document.createElement("a"),o=this.getBadgeWidth(),n=this.getBadgeHeight(),e=this.badgePath(),this.shouldShowPlaceholder()&&(i.style.cursor="pointer",i.style.display="inline-block","text"===this.badgeType()?i.innerHTML="?":(i.style.backgroundImage=this.getBackgroundImage(o,e),i.style.backgroundRepeat="no-repeat",i.style.backgroundSize="contain",i.style.width=o+"px",i.style.height=n+"px")),t=l+"/details.php?domain="+document.domain+"&"+this.placeholderDetailsParam(),this.detailsTemplate&&(t+="&template="+this.detailsTemplate),i.setAttribute("href",t),i.setAttribute("target",this.linkTarget),i.appendChild(document.createTextNode(" ")),(r=document.createElement("div")).appendChild(i),this.element.innerHTML=r.innerHTML},e.prototype.prepareBadge=function(){return this.shouldShowPlaceholder()&&this.insertPlaceholder(),this.fetchData()},e.prototype.shouldShowPlaceholder=function(){return!this.hideLessThanScore},e.prototype.placeholderDetailsParam=function(){return this.altmetric_id?"citation_id="+encodeURIComponent(this.altmetric_id):this.doi?"doi="+encodeURIComponent(this.doi):this.arxiv_id?"arxiv_id="+encodeURIComponent(this.arxiv_id):this.pubmed_id?"pmid="+encodeURIComponent(this.pubmed_id):this.handle_id?"handle="+encodeURIComponent(this.handle_id):this.uri?"uri="+encodeURIComponent(this.uri):this.urn?"urn="+encodeURIComponent(this.urn):this.isbn?"isbn="+encodeURIComponent(this.isbn):this.nct_id?"nct_id="+encodeURIComponent(this.nct_id):void 0},e.prototype.apiCall=function(){return this.altmetric_id?"id/"+this.altmetric_id:this.doi?"doi/"+this.doi:this.arxiv_id?"arxiv/"+this.arxiv_id:this.pubmed_id?"pmid/"+this.pubmed_id:this.handle_id?"handle/"+this.handle_id:this.uri?"uri/"+encodeURIComponent(this.uri):this.urn?"urn/"+this.urn:this.isbn?"isbn/"+this.isbn:this.nct_id?"nct_id/"+this.nct_id:void 0},e.prototype.fetchData=function(){var e,t,n;return t=i+"/"+r+"/internal-556fdf0f/"+this.apiCall()+"?callback=_altmetric.embed_callback&domain="+document.domain+"&"+this.cache_breaker,(e=document.createElement("script")).setAttribute("type","text/javascript"),e.onerror=(n=this,function(e){return h("Error fetching JSONP details for "+n.badgeId()+":"),h(e.target.src),n.clear()}),document.body.appendChild(e),e.setAttribute("src",t)},e.prototype.clear=function(){return this.shouldShowPlaceholder()?this.insertPlaceholder():this.hide()},e.prototype.hide=function(){return n(this.element).addClass("altmetric-hidden"),t.fire(this.element,"altmetric:hide"),this.element.innerHTML=""},e.prototype.renderBadge=function(e){var t,n,i;return this.altmetric_info=e,this.score=Math.ceil(this.altmetric_info.score),this.altmetric_id=this.altmetric_info.altmetric_id,this.altmetric_link=l+"/details.php?domain="+document.domain+"&citation_id="+this.altmetric_id,this.hasScore||(this.altmetric_link+="&no_score=true"),this.detailsTemplate&&(this.altmetric_link+="&template="+this.detailsTemplate),i="badge",n=this.getBadgeInfo(),this.popoverPosition?(n.contents=this.prepareViewContent(!0),n.position=this.popoverPosition,i="badge_with_popover"):this.detailsPosition&&(n.uuid=this.uuid,n.contents=this.prepareViewContent(!1),n.position=this.detailsPosition,i="badge_with_details"),t=AltmetricTemplates[i](n),this.scoreGreaterThanThreshold()?(this.show(t),this.renderPopover()):this.clear()},e.prototype.scoreGreaterThanThreshold=function(){return!this.hideLessThanScore||this.score>=this.hideLessThanScore},e.prototype.show=function(e){return this.element.innerHTML=e,n(this.element).removeClass("altmetric-hidden"),t.fire(this.element,"altmetric:show")},e.prototype.getBadgeInfo=function(){return{link:this.altmetric_link,show_img:"text"!==this.badgeType(),img_width:""+this.getBadgeWidth(),img_height:""+this.getBadgeHeight(),img_src:this.imageURL(),score:this.score,alt_text:"Article has an altmetric score of "+this.score,linkTarget:this.linkTarget,legendStyle:this.getLegendStyle()}},e.prototype.getLegendStyle=function(){return this.condensed?"altmetric-condensed-legend":"altmetric-normal-legend"},e.prototype.getBadgeWidth=function(){return this.badgeSizes[this.badgeType()].width},e.prototype.getBadgeHeight=function(){return this.badgeSizes[this.badgeType()].height},e.prototype.getBackgroundImage=function(e,t){return this.hasCustomisableImage(t)?"url(https://badges.altmetric.com/?size="+e+"&score=?&types=????????&style="+t+")":"url("+this.badge_cdn+"/"+t+"/0.png)"},e.prototype.hasCustomisableImage=function(e){return"donut"===e||"bar"===e},e.prototype.renderPopover=function(){if(this.popover=new _altmetric.Popover(this.element),this.popover.insertCSS(),this.popoverPosition&&this.shouldRenderPopover())return this.popover.build()},e.prototype.shouldRenderPopover=function(){return!o.msie||o.msie&&7<o.version},e.prototype.prepareViewContent=function(e){return this.altmetric_info.link=this.altmetric_link,this.altmetric_info.popover=e,this.altmetric_info.linkTarget=this.linkTarget,this.altmetric_info.condensedLegend=this.condensed,this.altmetric_info.readerVisibility={visible:!0},this._setReadersCountersVisibility(this.altmetric_info),AltmetricTemplates.legend(this.altmetric_info)},e.prototype._setReadersCountersVisibility=function(e){var t,n,i,r;if(0===e.readers_count)return e.readerVisibility.visible=!1;for(
t in i=[],n=e.readers)r=n[t],i.push(e.readerVisibility[t]=0<r);return i},e.prototype._getCacheBreaker=function(){var e;return"cache_until="+(e=new Date).getHours()+"-"+e.getDate()},e.prototype._getBadgeSizes=function(){return{v1:{width:"110",height:"20"},v2:{width:"88",height:"18"},v3:{width:"99",height:"18"},v4:{width:"85",height:"15"},donut:{width:"64",height:"64"},medium_donut:{width:"120",height:"120"},large_donut:{width:"180",height:"180"},bar:{width:"100",height:"15"},medium_bar:{width:"120",height:"18"},large_bar:{width:"180",height:"27"},text:{width:"110",height:"20"}}},e.prototype.imageURL=function(){var e;return e="donut"===this.badgePath()?_altmetric.donut_url(this.altmetric_info,2*this.badgeSizes[this.badgeType()].width):"bar"===this.badgePath()?_altmetric.bar_url(this.altmetric_info,2*this.badgeSizes[this.badgeType()].width):this.useHighQualityBadge()?this.badge_cdn+"/"+this.badgePath()+"/"+this.formattedScore()+".png":this.badge_cdn+"/"+this.badgePath()+"/"+this.score+".png",this.hasScore||(e=e.replace(/score=[0-9\?]+/,"score=")),e},e.prototype.useHighQualityBadge=function(){return"v2"===this.badgeType()},e.prototype.formattedScore=function(){return this.score<1e5?this.score:this.score<1e6?Math.floor(this.score/1e3)+"k":"1m"},e}(),e=function(e){try{return _(e)}catch(t){return f(t)}},s=function(e){null==e&&(e=document);try{return m(e)}catch(t){return f(t)}},f=function(e){return u(e.message||e.description),_altmetric.bugsnag.notifyException(e)},_=function(e){var t,n,i,r,o;for(o=[],n=0,i=(r=new _altmetric.BadgesMatcher(e).getMatchingBadges(_altmetric.badges)).length;n<i;n++)t=r[n],o.push(t.renderBadge(e));return o},p=function(e){return null==e&&(e=document),g(".altmetric-embed:not(.altmetric-popover):not(.altmetric-popover-inner):not(.altmetric-popover-content)",e)},c=function(e){var t,n,i,r,o;for(null==e&&(e=document),o=[],n=0,i=(r=p(e)).length;n<i;n++)t=r[n],o.push(new a(t));return o},d=function(e){var t,n,i,r,o;for(null==e&&(e=document),o=[],n=0,i=(r=c(e)).length;n<i;n++)null!=(t=r[n]).badgeId()&&o.push(t);return o},m=function(e){var t,n,i,r,o,a,l,s,u;for(null==e&&(e=document),r=0,a=(n=d(e)).length;r<a;r++)t=n[r],(i=_altmetric.badges)[s=t.badgeId()]||(i[s]={}),_altmetric.badges[t.badgeId()][t.uuid]=t;for(u=[],o=0,l=n.length;o<l;o++)t=n[o],u.push(t.prepareBadge());return u},_altmetric.exports({Badge:a,embed_init:s,badges:{},embed_callback:e})}.call(this),function(e,t){"undefined"!=typeof module?module.exports=t():_altmetric[e]=t()}("domready",function(n){function e(e){for(p=1;e=i.shift();)e()}var t,i=[],r=!1,o=document,a=o.documentElement,l=a.doScroll,s="DOMContentLoaded",u="addEventListener",c="onreadystatechange",d="readyState",p=(l?/^loaded|^c/:/^loaded|c/).test(o[d]);return o[u]&&o[u](s,t=function(){o.removeEventListener(s,t,r),e()},r),l&&o.attachEvent(c,t=function(){/^c/.test(o[d])&&(o.detachEvent(c,t),e())}),n=l?function(t){self!=top?p?t():i.push(t):function(){try{a.doScroll("left")}catch(e){return setTimeout(function(){n(t)},50)}t()}()}:function(e){p?e():i.push(e)}}),function(e){var t=window.Bugsnag;window.Bugsnag=e(window,document,navigator,t)}(function(c,u,d,e){function a(e){var t=c.console;t!==undefined&&t.log!==undefined&&t.log("[Bugsnag] "+e)}function l(e,t){var n=[];for(var i in e)if(e.hasOwnProperty(i)&&null!=i&&null!=e[i]){var r=t?t+"["+i+"]":i,o=e[i];n.push("object"==typeof o?l(o,r):encodeURIComponent(r)+"="+encodeURIComponent(o))}return n.join("&")}function p(e,t){if(null==t)return e;for(var n in e=e||{},t)if(t.hasOwnProperty(n))try{t[n].constructor===Object?e[n]=p(e[n],t[n]):e[n]=t[n]}catch(i){e[n]=t[n]}return e}function h(e,t){b.testRequest?b.testRequest(e,t):(new Image).src=e+"?"+l(t)+"&ct=img&cb="+(new Date).getTime()}function i(e){for(var t={},n=/^data\-([\w\-]+)$/,i=e.attributes,r=0;r<i.length;r++){var o=i[r];if(n.test(o.nodeName))t[o.nodeName.match(n)[1]]=o.nodeValue}return t}function f(e,t){v=v||i(S);var n=b[e]!==undefined?b[e]:v[e.toLowerCase()];return"false"===n&&(n=!1),n!==undefined?n:t}function m(e){return!(null==e||!e.match(w))||(a("Invalid API key '"+e+"'"),!1)}function s(e,t){var n=f("apiKey");if(m(n)){var i=f("releaseStage"),r=f("notifyReleaseStages");if(r){for(var o=!1,a=0;a<r.length;a++)if(i===r[a]){o=!0;break}if(!o)return}var l=p(f("metaData"),t),s=b.beforeNotify;if("function"==typeof s)if(!1===s(e,l))return;var u=c.location;h(f("endpoint")||P,{notifierVersion:C,apiKey:n,projectRoot:f("projectRoot")||u.protocol+"//"+u.host,context:f("context")||u.pathname,userId:f("userId"),metaData:l,releaseStage:i,url:c.location.href,userAgent:d.userAgent,language:d.language||d.userLanguage,name:e.name,message:e.message,stacktrace:e.stacktrace,file:e.file,lineNumber:e.lineNumber,columnNumber:e.columnNumber})}}function r(){var e,t=10,n="[anonymous]";try{throw new Error("")}catch(a){e=g(a)}if(!e){for(var i=[],r=arguments.callee.caller.caller;r&&i.length<t;){var o=k.test(r.toString())&&RegExp.$1||n;i.push(o),r=r.caller}e=i.join("\n")}return e}function g(e){return e.stack||e.backtrace||e.stacktrace}function t(){var e=f("metrics"),t=f("apiKey");if((!0===e||"true"===e)&&m(t)){var n="bugsnag_"+t,i=y(n);null==i&&_(n,i=o(),1e3,!0),h(f("metricsEndpoint")||T,{userId:i,apiKey:t})}}function n(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}function o(){return n()+n()+"-"+n()+"-"+n()+"-"+n()+"-"+n()+n()+n()}function _(e,t,n,i){var r="",o="";if(n){var a=new Date;a.setTime(a.getTime()+24*n*60*60*1e3),o="; expires="+a.toGMTString()}if(i){var l=c.location.hostname.match(/[a-z0-9][a-z0-9\-]+\.[a-z\.]{2,6}$/i),s=l?l[0]:"";r=s?"; domain=."+s:""}u.cookie=e+"="+encodeURIComponent(t)+o+"; path=/"+r}function y(e){var t=u.cookie.match(e+"=([^$;]+)");return t?decodeURIComponent(t[1]):null}var b={noConflict:function(){return c.Bugsnag=e,b},notifyException:function(e,t,n){"string"!=typeof t&&(n=t),s({name:t||e.name,message:e.message||e.description,stacktrace:g(e)||r(),file:e.fileName||e.sourceURL,lineNumber:e.lineNumber||e.line},n)},notify:function(e,t,n){s({name:e,message:t,stacktrace:r()},n)}};b._onerror=c.onerror,c.onerror=function(e,t,n,i,r){var o=f("autoNotify",!0);o&&"Script error."===e&&""===t&&0===n&&(a("Error on cross-domain script, couldn't notify Bugsnag."),o=!1),o&&s({name:"window.onerror",message:e,file:t,lineNumber:n,columnNumber:i,stacktrace:r&&g(r)}),b._onerror&&b._onerror(e,t,n)};var v,w=/^[0-9a-f]{32}$/i,k=/function\s*([\w\-$]+)?\s*\(/i,x="https://notify.bugsnag.com/",P=x+"js",T=x+"metrics",C="1.0.10",E=u.getElementsByTagName("script"),S=E[E.length-1];return t(),b}),_altmetric.bugsnag=Bugsnag.noConflict(),_altmetric.bugsnag.autoNotify=!1,_altmetric.bugsnag.releaseStage="production",_altmetric.bugsnag.notifyReleaseStages=["production","staging"],_altmetric.bugsnag.apiKey="daca05de9b5d741310b8b3308bdda3df",_altmetric_embed_init=_altmetric.embed_init,_altmetric.domready(function(){_altmetric.embed_init()});