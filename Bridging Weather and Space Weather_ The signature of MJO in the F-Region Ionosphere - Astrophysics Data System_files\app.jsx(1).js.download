function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_toPropertyKey(n.key),n)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}function _callSuper(e,t,r){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],_getPrototypeOf(e).constructor):t.apply(e,r))}function _possibleConstructorReturn(e,t){if(t&&("object"==_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}define(["underscore","react","prop-types"],function(e,n,t){function o(e){return e=e.children,n.createElement("div",{className:"associated__header__title"},n.createElement("i",{className:"fa fa-folder-open",style:l.icon,"aria-hidden":"true"}),e)}function i(e){var t=e.items,r=e.onClick;return n.createElement("div",{style:l.list,id:"associated_works"},t.map(function(e){return n.createElement("div",{key:e.id,style:l.link,className:"resources__content__link associated_work"},e.circular?e.name:(t=e).external?n.createElement(n.Fragment,null,n.createElement("a",{href:t.url,target:"_blank",rel:"noopener",onClick:function(){return r(t)}},t.name," "),n.createElement("i",{className:"fa fa-external-link","aria-hidden":"true"})):n.createElement("a",{href:t.url,onClick:function(){return r(t)}},t.name));var t}))}var l={list:{listStyleType:"none",marginLeft:"7px"},link:{fontSize:"1em",borderLeft:"solid 3px grey",paddingLeft:"5px"},icon:{fontSize:"1.4em","padding-right":"5px"}},r=(()=>{function t(e){return _classCallCheck(this,t),(e=_callSuper(this,t,[e])).state={showAllBtn:!1,showAll:!1,items:[]},e.onToggleShowAll=e.onToggleShowAll.bind(e),e}return _inherits(t,n.Component),_createClass(t,[{key:"componentWillReceiveProps",value:function(e){4<e.items.length?this.setState({items:e.items.slice(0,4),showAllBtn:!0,showAll:!1}):this.setState({items:e.items})}},{key:"onToggleShowAll",value:function(e){var t=this;e.preventDefault(),this.setState(function(e){return{showAll:!e.showAll,items:e.showAll?t.props.items.slice(0,4):t.props.items}})}},{key:"render",value:function(){var e=this.props,t=(e.loading,this.state),r=t.items;return 0<r.length?n.createElement("div",{className:"resources__container"},n.createElement(o,null,"Related Materials (",this.props.items.length,")"),n.createElement(i,{items:r,onClick:e.handleLinkClick}),t.showAllBtn&&n.createElement("input",{type:"button",id:"associated_works_btn",className:"btn btn-default btn-xs",onClick:this.onToggleShowAll,value:t.showAll?"Show Less":"Show All"})):null}}])})();return r.propTypes={hasError:t.bool,items:t.array,loading:t.bool,handleLinkClick:t.func},r.defaultProps={hasError:!1,items:[],loading:!1,handleLinkClick:function(){}},r});
//# sourceMappingURL=app.jsx.js.map