(window.webpackJsonp=window.webpackJsonp||[]).push([[1],{204:function(e,t,n){var r=n(93);e.exports=function _defineProperty(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},298:function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return c}));var r=n(204),i=n.n(r),a=n(8),o=n.n(a),s=n(9),l=n.n(s);function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var c=function(){return l()((function AdobeDigitalData(){var e=this;o()(this,AdobeDigitalData),this.selectors={acdlLinks:"a[data-acdl-event], button[data-acdl-event], input[data-acdl-event]",loginInitiate:"[data-acdl-event=login-initiate],[data-acdl-event=registration-initiated]",individualUser:".loginBar .individualUser"},this.elements={pageObj:null,updatedACDL:null,acdlLinks:null,loginInitiate:null,individualUser:null},this.variables={isFieldsValid:!0},this.eTOCEvents={successCTA:function successCTA(){e.pushToACDL({event:"alert-signup-success",eTOC:{name:"journal alert cta"}})},submitForm:function submitForm(){e.pushToACDL({event:"alert-signup-submit",eTOC:{name:"alert simple sign-up form"}})},requestVerificationShown:function requestVerificationShown(){e.pushToACDL({event:"request-verification-shown",eTOC:{name:"alert simple sign-up form"}})},requestVerification:function requestVerification(){e.pushToACDL({event:"request-verification",eTOC:{name:"alert simple sign-up form"}})},contactSupport:function contactSupport(){e.pushToACDL({event:"contact-customer-service",eTOC:{name:"alert simple sign-up form"}})}},this.registrationEvents={error:function error(t){e.pushToACDL({event:"registration-form-error",registration:{step:"registration form error","error-message":t}})},submitted:function submitted(){e.pushToACDL({event:"registration-form-submitted",registration:{step:"registration form submitted"}})},completed:function completed(){e.pushToACDL({event:"registration",registration:{step:"registration completed"}})}},this.loginEvents={error:function error(t){e.pushToACDL({event:"login-error",login:{step:"login error","error-message":t}})},success:function success(){e.pushToACDL({event:"login-success",login:{step:"login success"}})}},this.eCommerce={AddressVerification:function AddressVerification(){e.pushToACDL({event:"cart-address-verify"})},PaymentErrors:function PaymentErrors(t){e.pushToACDL({event:"cart-payment-error",error:{message:t}})},AddToCart:function AddToCart(t){e.pushToACDL({event:"add-to-cart",ecommerce:{product:t}})}},this.handleElementClicked=function(t){var n=t.target.getAttribute("data-ajax");if(!n&&t.preventDefault(),"A"===t.currentTarget.nodeName){var r=t.currentTarget.getAttribute("href"),i=t.currentTarget.getAttribute("acdl-redirect");e.getAttributes(t),"true"===i&&(window.location.href=r)}else if(("BUTTON"===t.currentTarget.nodeName||"INPUT"===t.currentTarget.nodeName)&&"submit"===t.currentTarget.type){var a=t.currentTarget.closest("form"),o=null==a?void 0:a.querySelectorAll('input[required], input[type="checkbox"][required], select[required]');if(null==a||a.reportValidity(),o&&e.handleFieldsValidation(o),!e.variables.isFieldsValid)return;e.getAttributes(t),!n&&(null==a||a.submit())}},this.handleFieldsValidation=function(t){t.forEach((function(t){e.variables.isFieldsValid=!("select"===t.tagName.toLowerCase()&&0===t.selectedIndex||"input"===t.tagName.toLowerCase()&&"checkbox"===t.type.toLowerCase()&&!t.checked||"input"===t.tagName.toLowerCase()&&""===t.value.trim())}))},this.getAttributes=function(t){var n=t.currentTarget.attributes,r={};Array.from(n).filter((function(e){return e.name.startsWith("data-acdl-")})).forEach((function(e){var t=e.name.split("--");if("data-acdl-event"===t[0])r.event=e.value;else{var n=t[0].split("-")[2];if("etoc"===n&&(n="eTOC"),r[n]||(r[n]={}),t.length>2){var i=t.slice(1),a=i.pop();i.reduce((function(e,t){return e[t]||(e[t]={}),e[t]}),r[n])[a]=e.value}else{var o=t[1];r[n][o]=e.value}}})),e.pushToACDL(r)},window.adobeDataLayer&&(this.getOsanoConsent(),this.setElements(),this.eventListeners(),this.loginSuccess())}),[{key:"setElements",value:function setElements(){this.elements.acdlLinks=document.querySelectorAll(this.selectors.acdlLinks),this.elements.loginInitiate=UX.utils.convertToArray(document.querySelectorAll(this.selectors.loginInitiate)),this.elements.individualUser=document.querySelector(this.selectors.individualUser)}},{key:"eventListeners",value:function eventListeners(){var e,t,n=this;null===(e=this.elements.acdlLinks)||void 0===e||e.forEach((function(e){e.addEventListener("click",n.handleElementClicked)})),null===(t=this.elements.loginInitiate)||void 0===t||t.forEach((function(e){e.addEventListener("click",(function(){localStorage.setItem("logInSubmit",!0)}))}))}},{key:"ajaxPageLoad",value:function ajaxPageLoad(e){var t;this.elements.updatedACDL=this.getUpdatedACDL(e),window.adobeDataLayer.push({event:"page-load",search:_objectSpread({},null===(t=this.elements.updatedACDL)||void 0===t?void 0:t.search)})}},{key:"changeTabsLoad",value:function changeTabsLoad(e){this.elements.pageObj=window.adobeDataLayer.getState().page,e.closest(".citation-search__tab")&&(this.elements.pageObj["secondary-section"]="citation search"),e.closest(".adv-search__tab")&&(this.elements.pageObj["secondary-section"]="advanced"),window.adobeDataLayer.push({event:"page-load",page:_objectSpread({},this.elements.pageObj)})}},{key:"getUpdatedACDL",value:function getUpdatedACDL(e){try{var t=(new DOMParser).parseFromString(e,"text/html").getElementById("adobeDigitalData").innerHTML.match(/window\.adobeDataLayer\.push\((\{[^]*\})\);/)[1];return JSON.parse(t)}catch(e){return console.error("Error occurred while parsing adobeDigitalData: ".concat(e)),null}}},{key:"getOsanoConsent",value:function getOsanoConsent(){var e=!1,t=null,n={event:"page-load",user:{}},r=setInterval((function(){var i,a,o,s;(window.Osano&&window.Osano("onInitialized",(function(n){e=!0,t=n})),e)&&(clearInterval(r),n.user["consent-osano"]="analytics: ".concat(null===(i=t)||void 0===i?void 0:i.ANALYTICS," | marketing: ").concat(null===(a=t)||void 0===a?void 0:a.MARKETING," | personalization: ").concat(null===(o=t)||void 0===o?void 0:o.PERSONALIZATION," | essential: ").concat(null===(s=t)||void 0===s?void 0:s.ESSENTIAL),window.adobeDataLayer.push(n))}),50)}},{key:"pushToACDL",value:function pushToACDL(e){window.adobeDataLayer.push(e)}},{key:"loginSuccess",value:function loginSuccess(){localStorage.getItem("logInSubmit")&&this.elements.individualUser?(window.adobeDataLayer&&this.loginEvents.success(),localStorage.removeItem("logInSubmit")):localStorage.getItem("logInSubmit")&&localStorage.removeItem("logInSubmit")}}])}()}}]);
//# sourceMappingURL=adobeDigitalData-4835cf3c3adcf66a6724.js.map