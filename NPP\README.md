usra-nprp-latex
================

#### A LaTeX template for fellowship applications for the NASA Postdoctoral Research Program (NPRP).  

- [How to Apply](https://npp.usra.edu/apply/)  
- [Research Opportunities](https://npp.usra.edu/opportunities/)  
- [Applicant Portal](https://npp.usra.edu/applicant_connect/)  
- [Policies and Procedures](https://npp.usra.edu/policies-procedures/)  
- [FAQ's for Applicants](https://npp.usra.edu/about/faq/applicants/)  
- [Contacts for More Information](https://npp.usra.edu/about/contacts/)  

#### Download

The template homepage is:   
https://github.com/tsutterley/usra-nprp-latex    
A zip archive of the latest version is available directly at:    
https://github.com/tsutterley/usra-nprp-latex/archive/master.zip  

#### Usage  

The main file for creating your proposal is `NPRP.tex`.  Auxiliary files are included for overall style and to create bibliographies.  Compile `NPRP.tex` with `pdflatex` to create your PDF file (required for submission).   

#### Disclaimer  
This template is not sponsored or maintained by the Universities Space Research Association (USRA) or NASA.  It is provided here for your convenience but _with no guarantees whatsoever_.

#### Why use Git for manuscripts?
 -  Single files: using git simplifies things as there's only a single version of a manuscript.  
 -  Merging: if two people work on the same file at the same time, they can be merged without much issue.  
 -  Simplified "diff" documents: can also use latexdiff-vc (latexdiff with version control) to make pdf files similar to Word documents with "Track Changes".  
 -  Easier collaboration: No need to email files back and forth.  

