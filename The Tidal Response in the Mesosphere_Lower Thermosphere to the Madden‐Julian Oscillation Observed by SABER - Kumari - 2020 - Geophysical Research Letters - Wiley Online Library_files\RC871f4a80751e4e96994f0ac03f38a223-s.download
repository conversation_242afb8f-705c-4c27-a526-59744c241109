// For license information, see `https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RC871f4a80751e4e96994f0ac03f38a223-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/59640f9fa510/591d3f748997/47245d1f4e07/RC871f4a80751e4e96994f0ac03f38a223-source.min.js', "<script type=\"text/javascript\">\n  // Check if the current domain contains 'febs.onlinelibrary.com'\n  if (window.location.hostname.includes('febs.')) {\n    // LinkedIn Conversion Pixel Script\n    var linkedinPartnerId = \"6336738\";\n    window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];\n    window._linkedin_data_partner_ids.push(linkedinPartnerId);\n\n    (function(l) {\n      if (!l) {\n        window.lintrk = function(a, b) {\n          window.lintrk.q.push([a, b]);\n        };\n        window.lintrk.q = [];\n      }\n      var s = document.getElementsByTagName(\"script\")[0];\n      var b = document.createElement(\"script\");\n      b.type = \"text/javascript\";\n      b.async = true;\n      b.src = \"https://snap.licdn.com/li.lms-analytics/insight.min.js\";\n      s.parentNode.insertBefore(b, s);\n    })(window.lintrk);\n  }\n</script>\n");