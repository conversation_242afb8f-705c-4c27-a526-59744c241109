define(["underscore","jquery","js/components/generic_module","js/components/api_request","js/mixins/dependon","js/components/api_response","js/components/api_query","js/components/api_feedback","js/mixins/hardened","js/mixins/api_access","moment"],function(a,u,e,t,s,n,c,o,i,r,h){e=e.extend({url:"/api/1/",clientVersion:null,outstandingRequests:0,access_token:null,refresh_token:null,expires_at:null,defaultTimeoutInMs:6e4,activate:function(e){this.setBeeHive(e)},done:function(e,t,s){e=new n(e);e.setApiQuery(this.request.get("query")),this.api.trigger("api-response",e)},fail:function(e,t,s){console.error("API call failed:",JSON.stringify(this.request.url()),e.status,s);var n,i=this.api.hasBeeHive()?this.api.getPubSub():null;i?(n=new o({code:o.CODES.API_REQUEST_ERROR,msg:t,request:this.request,error:e,psk:this.key||this.api.getPubSub().getCurrentPubSubKey(),errorThrown:s,text:t,beVerbose:!0}),i.publish(i.FEEDBACK,n)):this.api&&this.api.trigger("api-error",this,e,t,s)},initialize:function(){this.always=a.bind(function(){this.outstandingRequests--},this)},getNumOutstandingRequests:function(){return this.outstandingRequests},setVals:function(e){a.each(e,function(e,t){this[t]=e},this)},modifyRequestOptions:function(e){},hardenedInterface:{request:"make a request to the API",setVals:"set a value on API (such as new access token)"}});return e.prototype._request=function(e,t){t=a.extend({},t,e.get("options"));var s=this,n=e.get("query");if(n&&!(n instanceof c))throw Error("Api.query must be instance of ApiQuery");n&&(o="application/json"===t.contentType?JSON.stringify(n.toJSON()):n.url());var i,o,n=e.get("target")||"",r=-1<n.indexOf("http")?n:this.url+(0<n.length&&0==n.indexOf("/")?n:n&&"/"+n);if(r=r.substring(0,this.url.length-2)+r.substring(this.url.length-2,r.length).replace("//","/"))return i={type:"GET",url:r,dataType:"json",data:o,contentType:"application/x-www-form-urlencoded",context:{request:e,api:s},timeout:this.defaultTimeoutInMs,headers:{},cache:!0},t.timeout&&(i.timeout=t.timeout),this.clientVersion&&(i.headers["X-BB-Api-Client-Version"]=this.clientVersion),this.access_token&&(i.headers.Authorization=this.access_token,/accounts\/bootstrap/i.test(n))&&(i.headers.Authorization=""),a.extend(i.headers,t.headers),a.extend(i,a.omit(t,"headers")),this.outstandingRequests++,this.modifyRequestOptions(i,e),t.useFetch&&t.fetchOptions?((r=a.assign({credentials:"include",mode:"cors",timeout:this.defaultTimeoutInMs},t.fetchOptions)).headers=a.assign({"Content-Type":i.contentType},i.headers,t.fetchOptions.headers),o=window.fetch(i.url,r).then(function(e){if(s.always.call(i.context,e),a.isFunction(i.always)&&i.always.call(i.context,e),!e.ok)throw(i.fail||s.fail).call(i.context,e),Error(e.statusText);(i.done||s.done).call(i.context,e)}).catch(function(e){throw(i.fail||s.fail).call(i.context,e),Error(e)}),a.extend(o,{done:o.then,fail:o.catch})):(n=u.ajax(i).always(i.always?[this.always,i.always]:this.always).done(i.done||this.done).fail(i.fail||this.fail)).promise(n);throw Error("Sorry, you can't use api without url")},e.prototype.getCurrentTimestamp=function(){return Math.floor(Date.now()/1e3)},e.prototype.request=function(s,n){function i(){var e=u.Deferred(),t=o.getApiAccess({tokenRefresh:!0,reconnect:!0});return t.done(function(){e.resolve(o._request(s,n))}),t.fail(function(){0<--r?a.delay(i,1e3):e.reject.apply(e,arguments)}),e.promise()}var o=this,r=3;return!this.expires_at||this.expires_at-this.getCurrentTimestamp()<120?i():this._request(s,n)},a.extend(e.prototype,s.BeeHive,i,r),e});
//# sourceMappingURL=api.js.map