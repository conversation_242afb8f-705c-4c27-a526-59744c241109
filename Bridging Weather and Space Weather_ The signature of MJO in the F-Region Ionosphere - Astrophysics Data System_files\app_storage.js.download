define(["backbone","underscore","js/components/api_query","js/mixins/hardened","js/mixins/dependon"],function(e,n,t,r,s){e=e.Model.extend({activate:function(e){this.setBeeHive(e),n.bindAll(this,"onPaperSelection","onBulkPaperSelection");e=this.getPubSub();e.subscribe(e.PAPER_SELECTION,this.onPaperSelection),e.subscribe(e.BULK_PAPER_SELECTION,this.onBulkPaperSelection)},initialize:function(){this.on("change:selectedPapers",function(e){var t;this._updateNumSelected(),(t=this.hasPubSub()?this.getPubSub():t).publish(t.STORAGE_PAPER_UPDATE,this.getNumSelectedPapers())})},setCurrentQuery:function(e){if(!((e=e||new t)instanceof t))throw new Error("You must save ApiQuery instance");this.set("currentQuery",e),this.getBeeHive().hasService("PersistentStorage")&&this.getBeeHive().getService("PersistentStorage").set("currentQuery",e.toJSON()),window.getSentry(function(t){var r=e.toJSON();Object.keys(r).forEach(function(e){e.startsWith("__")&&"fq"!==e||t.setTag("query.".concat(e),r[e].join(" | "))})})},setCurrentNumFound:function(e){this.set("numFound",e)},getCurrentQuery:function(){return this.get("currentQuery")},hasCurrentQuery:function(){return this.has("currentQuery")},hasSelectedPapers:function(){return!!n.keys(this.get("selectedPapers")).length},getSelectedPapers:function(){return n.keys(this.get("selectedPapers")||{})},clearSelectedPapers:function(){this.set("selectedPapers",{})},addSelectedPapers:function(e){var t=n.clone(this.get("selectedPapers")||{}),r=!1;n.isArray(e)?n.each(e,function(e){t[e]||(t[e]=!0,r=!0)}):t[e]||(t[e]=!0,r=!0),r&&this.set("selectedPapers",t)},isPaperSelected:function(e){if(n.isArray(e))throw new Error("Identifier must be a string or a number");return!!(this.get("selectedPapers")||{})[e]},removeSelectedPapers:function(e){var t=n.clone(this.get("selectedPapers")||{});n.isArray(e)?n.each(e,function(e){delete t[e]}):e?delete t[e]:t={},this.set("selectedPapers",t)},getNumSelectedPapers:function(){return this._numSelectedPapers||0},_updateNumSelected:function(){this._numSelectedPapers=n.keys(this.get("selectedPapers")||{}).length},onPaperSelection:function(e){this.isPaperSelected(e)?this.removeSelectedPapers(e):this.addSelectedPapers(e)},onBulkPaperSelection:function(e,t){"remove"==t?this.removeSelectedPapers(e):this.addSelectedPapers(e)},setConfig:function(e){this.set("dynamicConfig",e)},getConfigCopy:function(){return JSON.parse(JSON.stringify(this.get("dynamicConfig")))},setDocumentTitle:function(e){this.set("documentTitle",e)},getDocumentTitle:function(){return this.get("documentTitle")},hardenedInterface:{getNumSelectedPapers:"getNumSelectedPapers",isPaperSelected:"isPaperSelected",hasSelectedPapers:"hasSelectedPapers",getSelectedPapers:"getSelectedPapers",clearSelectedPapers:"clearSelectedPapers",getCurrentQuery:"getCurrentQuery",hasCurrentQuery:"hasCurrentQuery",setDocumentTitle:"setDocumentTitle",getDocumentTitle:"getDocumentTitle",getConfigCopy:"get read-only copy of dynamic config",set:"set a value into app storage",get:"get a val from app storage"}});return n.extend(e.prototype,r,s.BeeHive),e});
//# sourceMappingURL=app_storage.js.map