(window.webpackJsonp=window.webpackJsonp||[]).push([[52],{256:function(e,t,r){"use strict";r.r(t),r.d(t,"default",(function(){return m}));var n=r(14),a=r.n(n),s=r(8),u=r.n(s),c=r(9),l=r.n(c);function _regenerator(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function i(r,n,a,u){var c=n&&n.prototype instanceof Generator?n:Generator,l=Object.create(c.prototype);return _regeneratorDefine2(l,"_invoke",function(r,n,a){var u,c,l,m=0,h=a||[],p=!1,v={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function d(t,r){return u=t,c=0,l=e,v.n=r,s}};function d(r,n){for(c=r,l=n,t=0;!p&&m&&!a&&t<h.length;t++){var a,u=h[t],b=v.p,g=u[2];r>3?(a=g===n)&&(l=u[(c=u[4])?5:(c=3,3)],u[4]=u[5]=e):u[0]<=b&&((a=r<2&&b<u[1])?(c=0,v.v=n,v.n=u[1]):b<g&&(a=r<3||u[0]>n||n>g)&&(u[4]=r,u[5]=n,v.n=g,c=0))}if(a||r>1)return s;throw p=!0,n}return function(a,h,b){if(m>1)throw TypeError("Generator is already running");for(p&&1===h&&d(h,b),c=h,l=b;(t=c<2?e:l)||!p;){u||(c?c<3?(c>1&&(v.n=-1),d(c,l)):v.n=l:v.v=l);try{if(m=2,u){if(c||(a="next"),t=u[a]){if(!(t=t.call(u,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,c<2&&(c=0)}else 1===c&&(t=u.return)&&t.call(u),c<2&&(l=TypeError("The iterator does not provide a '"+a+"' method"),c=1);u=e}else if((t=(p=v.n<0)?l:r.call(n,v))!==s)break}catch(t){u=e,c=1,l=t}finally{m=1}}return{value:t,done:p}}}(r,a,u),!0),l}var s={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}t=Object.getPrototypeOf;var u=[][n]?t(t([][n]())):(_regeneratorDefine2(t={},n,(function(){return this})),t),c=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(u);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,_regeneratorDefine2(e,a,"GeneratorFunction")),e.prototype=Object.create(c),e}return GeneratorFunction.prototype=GeneratorFunctionPrototype,_regeneratorDefine2(c,"constructor",GeneratorFunctionPrototype),_regeneratorDefine2(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",_regeneratorDefine2(GeneratorFunctionPrototype,a,"GeneratorFunction"),_regeneratorDefine2(c),_regeneratorDefine2(c,a,"Generator"),_regeneratorDefine2(c,n,(function(){return this})),_regeneratorDefine2(c,"toString",(function(){return"[object Generator]"})),(_regenerator=function _regenerator(){return{w:i,m:f}})()}function _regeneratorDefine2(e,t,r,n){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}(_regeneratorDefine2=function _regeneratorDefine(e,t,r,n){function o(t,r){_regeneratorDefine2(e,t,(function(e){return this._invoke(t,r,e)}))}t?a?a(e,t,{value:r,enumerable:!n,configurable:!n,writable:!n}):e[t]=r:(o("next",0),o("throw",1),o("return",2))})(e,t,r,n)}var m=function(){return l()((function UTM(){u()(this,UTM),this.selectors={links:"a[href*='http']:not([href*='onlinelibrary']):not([href*='ciplit']):not([href*='literatumonline']):not([href*='facebook']):not([href*='instagram']):not([href*='linkedin']):not([href*='twitter'])",metaTag:"meta[name=submission-systems-domains]"},this.elements={links:null,metaTag:null},this.variables={utmParamsObj:{},utmParamsStr:"",fileContent:[]},window.utmParamsEnabled&&(this.setElements(),this.init())}),[{key:"setElements",value:function setElements(){this.elements.links=Array.from(document.querySelectorAll(this.selectors.links)),this.elements.metaTag=document.querySelector(this.selectors.metaTag)}},{key:"init",value:(t=a()(_regenerator().m((function _callee(){var e,t,r,n;return _regenerator().w((function(a){for(;;)switch(a.n){case 0:return e=new URL(window.location.href),a.n=1,this.fetchData();case 1:t=a.v,r=!1,n=UX.utils.getCookie("utm_params_persistence"),UX.utils.deleteCookie("s_utm_cmp","/"),e.href.includes("utm")?(this.utmParams(e),UX.utils.setCookie("utm_params_persistence",this.variables.utmParamsStr),r=!0):n&&(e.search=n,r=!0),r&&this.addParams(e,t);case 2:return a.a(2)}}),_callee,this)}))),function init(){return t.apply(this,arguments)})},{key:"addParams",value:function addParams(e,t){var r=this,n="",a="",s="";this.elements.links.forEach((function(u){n=u.getAttribute("href"),a=n.startsWith("/")?new URL(n,e):new URL(n),(s=a.hostname).startsWith("www")&&(s=s.replace("www.","")),null!=t&&t.includes(s)&&(a.searchParams.toString().length<3?(a.search=e.searchParams.toString(),u.setAttribute("href",a.href)):(r.setParams(a),u.setAttribute("href",a.href)))}))}},{key:"setParams",value:function setParams(e){for(var t in this.variables.utmParamsObj)e.searchParams.set(t,this.variables.utmParamsObj[t])}},{key:"utmParams",value:function utmParams(e){var t=this;this.variables.utmParamsStr="",this.variables.utmParamsObj={},e.searchParams.forEach((function(e,r){if(r.includes("utm")){var n=e.slice(0,50);t.variables.utmParamsObj[r]=n,t.variables.utmParamsStr+="".concat(r,"=").concat(n,"&")}})),this.variables.utmParamsStr=this.variables.utmParamsStr.slice(0,-1)}},{key:"fetchData",value:(e=a()(_regenerator().m((function _callee2(){var e,t,r;return _regenerator().w((function(n){for(;;)switch(n.n){case 0:if(!(t=null===(e=this.elements.metaTag)||void 0===e?void 0:e.getAttribute("content"))){n.n=2;break}return n.n=1,UX.utils.readFromFile(t);case 1:return r=n.v,n.a(2,r.split("\n"));case 2:return n.a(2)}}),_callee2,this)}))),function fetchData(){return e.apply(this,arguments)})}]);var e,t}()}}]);
//# sourceMappingURL=utm-2b5dc1e394175278a44f.js.map