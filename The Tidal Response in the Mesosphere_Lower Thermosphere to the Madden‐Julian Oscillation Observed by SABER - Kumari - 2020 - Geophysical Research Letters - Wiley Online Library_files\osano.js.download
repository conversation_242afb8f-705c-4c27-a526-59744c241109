/*! For license information please see osano.js.LICENSE.txt */
(()=>{var e={5289:()=>{!function(){var e=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/;function t(t){return null==t?String(t):(t=e.exec(Object.prototype.toString.call(Object(t))))?t[1].toLowerCase():"object"}function n(e,t){return Object.prototype.hasOwnProperty.call(Object(e),t)}function r(e){if(!e||"object"!=t(e)||e.nodeType||e==e.window)return!1;try{if(e.constructor&&!n(e,"constructor")&&!n(e.constructor.prototype,"isPrototypeOf"))return!1}catch(o){return!1}for(var r in e);return void 0===r||n(e,r)}function o(e,t,n){this.b=e,this.f=t||function(){},this.d=!1,this.a={},this.c=[],this.e=function(e){return{set:function(t,n){c(s(t,n),e.a)},get:function(t){return e.get(t)}}}(this),i(this,e,!n);var r=e.push,o=this;e.push=function(){var t=[].slice.call(arguments,0),n=r.apply(e,t);return i(o,t),n}}function i(e,n,o){for(e.c.push.apply(e.c,n);!1===e.d&&0<e.c.length;){if("array"==t(n=e.c.shift()))e:{var i=n,a=e.a;if("string"==t(i[0])){for(var l=i[0].split("."),u=l.pop(),p=(i=i.slice(1),0);p<l.length;p++){if(void 0===a[l[p]])break e;a=a[l[p]]}try{a[u].apply(a,i)}catch(f){}}}else if("function"==typeof n)try{n.call(e.e)}catch(g){}else{if(!r(n))continue;for(var d in n)c(s(d,n[d]),e.a)}o||(e.d=!0,e.f(e.a,n),e.d=!1)}}function s(e,t){for(var n={},r=n,o=e.split("."),i=0;i<o.length-1;i++)r=r[o[i]]={};return r[o[o.length-1]]=t,n}function c(e,o){for(var i in e)if(n(e,i)){var s=e[i];"array"==t(s)?("array"==t(o[i])||(o[i]=[]),c(s,o[i])):r(s)?(r(o[i])||(o[i]={}),c(s,o[i])):o[i]=s}}window.DataLayerHelper=o,o.prototype.get=function(e){var t=this.a;e=e.split(".");for(var n=0;n<e.length;n++){if(void 0===t[e[n]])return;t=t[e[n]]}return t},o.prototype.flatten=function(){this.b.splice(0,this.b.length),this.b[0]={},c(this.a,this.b[0])}}()},628:(e,t,n)=>{"use strict";t.$y=void 0;var r=n(9592),o=function(){var e=[],t=[],n=void 0;return{enhancer:function(e){return n=e,function(e){return function(n){return r.compose.apply(void 0,t)(e)(n)}}},addMiddleware:function(){for(var r,o,i=arguments.length,s=Array(i),c=0;c<i;c++)s[c]=arguments[c];(r=t).push.apply(r,s.map((function(e){return e(n)}))),(o=e).push.apply(o,s)},removeMiddleware:function(n){var r=e.findIndex((function(e){return e===n}));-1!==r?(e=e.filter((function(e,t){return t!==r})),t=t.filter((function(e,t){return t!==r}))):console.error("Middleware does not exist!",n)},resetMiddlewares:function(){t=[],e=[]}}},i=o();i.enhancer,i.addMiddleware,i.removeMiddleware,i.resetMiddlewares,t.$y=o},9592:(e,t,n)=>{"use strict";n.r(t),n.d(t,{__DO_NOT_USE__ActionTypes:()=>l,applyMiddleware:()=>b,bindActionCreators:()=>m,combineReducers:()=>f,compose:()=>h,createStore:()=>p,legacy_createStore:()=>d});var r=n(1286);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var c="function"==typeof Symbol&&Symbol.observable||"@@observable",a=function(){return Math.random().toString(36).substring(7).split("").join(".")},l={INIT:"@@redux/INIT"+a(),REPLACE:"@@redux/REPLACE"+a(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+a()}};function u(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function p(e,t,n){var r;if("function"==typeof t&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw new Error(s(0));if("function"==typeof t&&void 0===n&&(n=t,t=void 0),void 0!==n){if("function"!=typeof n)throw new Error(s(1));return n(p)(e,t)}if("function"!=typeof e)throw new Error(s(2));var o=e,i=t,a=[],d=a,f=!1;function g(){d===a&&(d=a.slice())}function m(){if(f)throw new Error(s(3));return i}function h(e){if("function"!=typeof e)throw new Error(s(4));if(f)throw new Error(s(5));var t=!0;return g(),d.push(e),function(){if(t){if(f)throw new Error(s(6));t=!1,g();var n=d.indexOf(e);d.splice(n,1),a=null}}}function b(e){if(!u(e))throw new Error(s(7));if(void 0===e.type)throw new Error(s(8));if(f)throw new Error(s(9));try{f=!0,i=o(i,e)}finally{f=!1}for(var t=a=d,n=0;n<t.length;n++){(0,t[n])()}return e}return b({type:l.INIT}),(r={dispatch:b,subscribe:h,getState:m,replaceReducer:function(e){if("function"!=typeof e)throw new Error(s(10));o=e,b({type:l.REPLACE})}})[c]=function(){var e,t=h;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(s(11));function n(){e.next&&e.next(m())}return n(),{unsubscribe:t(n)}}})[c]=function(){return this},e},r}var d=p;function f(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var o=t[r];0,"function"==typeof e[o]&&(n[o]=e[o])}var i,c=Object.keys(n);try{!function(e){Object.keys(e).forEach((function(t){var n=e[t];if(void 0===n(void 0,{type:l.INIT}))throw new Error(s(12));if(void 0===n(void 0,{type:l.PROBE_UNKNOWN_ACTION()}))throw new Error(s(13))}))}(n)}catch(a){i=a}return function(e,t){if(void 0===e&&(e={}),i)throw i;for(var r=!1,o={},a=0;a<c.length;a++){var l=c[a],u=n[l],p=e[l],d=u(p,t);if(void 0===d){t&&t.type;throw new Error(s(14))}o[l]=d,r=r||d!==p}return(r=r||c.length!==Object.keys(e).length)?o:e}}function g(e,t){return function(){return t(e.apply(this,arguments))}}function m(e,t){if("function"==typeof e)return g(e,t);if("object"!=typeof e||null===e)throw new Error(s(16));var n={};for(var r in e){var o=e[r];"function"==typeof o&&(n[r]=g(o,t))}return n}function h(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function b(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),r=function(){throw new Error(s(15))},o={getState:n.getState,dispatch:function(){return r.apply(void 0,arguments)}},c=t.map((function(e){return e(o)}));return r=h.apply(void 0,c)(n.dispatch),i(i({},n),{},{dispatch:r})}}}},5267:(e,t,n)=>{"use strict";n.d(t,{SK:()=>Z,Qm:()=>te,Gf:()=>X,LB:()=>ee,R$:()=>I,fQ:()=>ne,tu:()=>B,wm:()=>F,AZ:()=>K,Qj:()=>J});var r=n(1286);const o={};function i(e,t){let n;const r=function(...t){return delete o[n],e(...t)};return t?r():n=document.hidden?`${setTimeout(r,0)}_`:`_${window.requestAnimationFrame(r)}`,n&&(o[n]=e),n}function s(e){const[t,n]=e.split("_").map((function(e){return parseInt(e,10)}));e&&delete o[e],n&&window.cancelAnimationFrame(parseInt(n,10)),t&&clearTimeout(parseInt(t,10))}document.addEventListener("visibilitychange",(function(){Object.entries(o).forEach((function([e,t]){s(e),i(t)}))}),!1);var c=n(1184),a=n.n(c);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e){const t=new Blob(["self.onmessage=function(__e){(",e,")(__e,self)}"],{type:"text/javascript"}),n=URL.createObjectURL(t);return new Worker(n)}class d{constructor(e){this.doWork=e}get running(){return void 0!==this.requestId||void 0!==this.webWorker}wait(e,t){this.requestId=i((function(){e(Date.now())}),t)}createInlineWorker(e,t,n){let r;for(;void 0===r;){if(r=this.doWork(this.data),r){this.cleanup(),e(r);break}if(Date.now()-n>100){this.wait.call(this,this.createInlineWorker.bind(this,e,t));break}}}createWebWorker(e,t){var n=this;const r=p(this.doWork);this.webWorker=r,r.onmessage=function(t){n.cleanup(),r.terminate(),e(t.data)},r.onerror=function(e){n.cleanup(),r.terminate(),t(e.error)},r.postMessage(this.data)}cleanup(){s(this.requestId),this.webWorker&&this.webWorker.terminate(),delete this.webWorker,delete this.requestId}postMessage(e){this.data=u(u({},this.data),e),this.webWorker&&this.webWorker.postMessage(this.data)}terminate(){this.cleanup(),this.inlineReject&&this.inlineReject("abort")}classify(e){var t=this;return this._promise?this.postMessage(e):(this.data=e,this._promise=new(a())((function(e,n){t.inlineReject=n,"string"==typeof t.doWork?t.wait(t.createWebWorker.bind(t,e,n)):t.wait(t.createInlineWorker.bind(t,e,n))}))),this._promise}get promise(){return this._promise||a().reject()}}var f=n(8734),g=n(5036),m=n(4045);const h=/(^|@)\S+:\d+/,b=/^\s*at .*(\S+:\d+|\(native\))/m,y=/^(eval@)?(\[native code])?$/;function O(e){return e&&e.replace(/^\(/,"").replace(/\)$/,"")}const v={parse:function(e){if(void 0!==e.stacktrace||void 0!==e["opera#sourceloc"])return this.parseOpera(e);if(e.stack&&e.stack.match(b))return this.parseV8OrIE(e);if(e.stack)return this.parseFFOrSafari(e);throw new Error("Cannot parse given Error object")},extractLocation:function(e){if(-1===e.indexOf(":"))return[e];var t=O(e),n=/(?:\(.*)?:(\d+)?(?::(\d+))?\)?$/.exec(t),r=n&&n[1]||void 0,o=n&&n[2]||void 0,i=n&&n.index||void 0;return[t.slice(0,i),r&&parseInt(r,10),o&&parseInt(o,10)]},parseV8OrIE:function(e){return e.stack.split("\n").filter((function(e){return!!e.match(b)}),this).map((function(e){e.indexOf("(eval ")>-1&&(e=e.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(\),.*$)/g,""));var t=e.replace(/^\s+/,"").replace(/\(eval code/g,"("),n=t.match(/ (\((.+):(\d+):(\d+)\)$)/),r=(t=n?t.replace(n[0],""):t).split(/\s+/).slice(1),o=this.extractLocation(n?n[1]:r.pop());return{functionName:r.join(" ")||void 0,fileName:["eval","<anonymous>"].indexOf(o[0])>-1?void 0:o[0],lineNumber:o[1],columnNumber:o[2],source:e}}),this)},parseFFOrSafari:function(e){return e.stack.split("\n").filter((function(e){return!e.match(y)}),this).map((function(e){if(e.indexOf(" > eval")>-1&&(e=e.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===e.indexOf("@")&&-1===e.indexOf(":")||e.indexOf("data:text/")>=0)return{functionName:e};var t=/((.*".+"[^@]*)?[^@]*)(?:@)/,n=e.match(t),r=n&&n[1]?n[1]:void 0,o=this.extractLocation(e.replace(t,""));return{functionName:r,fileName:o[0],lineNumber:o[1],columnNumber:o[2],source:e}}),this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf("\n")>-1&&e.message.split("\n").length>e.stacktrace.split("\n").length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(e){for(var t=/Line (\d+).*script (?:in )?(\S+)/i,n=e.message.split("\n"),r=[],o=2,i=n.length;o<i;o+=2){var s=t.exec(n[o]);s&&r.push({fileName:O(s[2]),lineNumber:s[1]&&parseInt(s[1],10),source:n[o]})}return r},parseOpera10:function(e){for(var t=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,n=e.stacktrace.split("\n"),r=[],o=0,i=n.length;o<i;o+=2){var s=t.exec(n[o]);s&&r.push({functionName:s[3]||void 0,fileName:O(s[2]),lineNumber:s[1]&&parseInt(s[1],10),source:n[o]})}return r},parseOpera11:function(e){return e.stack.split("\n").filter((function(e){return!!e.match(h)&&!e.match(/^Error created at/)}),this).map((function(e){var t,n=e.split("@"),r=this.extractLocation(n.pop()),o=n.shift()||"",i=o.replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;return o.match(/\(([^)]*)\)/)&&(t=o.replace(/^[^(]+\(([^)]*)\)$/,"$1")),{functionName:i,args:void 0===t||"[arguments not available]"===t?void 0:t.split(","),fileName:r[0],lineNumber:r[1],columnNumber:r[2],source:e}}),this)}};var w=n(4274),$=n(8428),_=n(1344),j=n(924),A=n(5041),k=n(2078),P=n(2981);function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const E=new Set([]),D=new WeakMap,T={iframe:{},cookie:{},script:{}},C=Symbol("osano.cm.userscript"),N={},I=function(e){const t=e&&e[f.gY]||e;return t&&D.has(t)&&D.get(t)||void 0};function L(){let e=new Error;if(f.lw)try{throw e}catch(o){e=o}const t=v.parse(e);let n,r;for(;n=t.pop();){const{fileName:e,source:t}=n;r=r||!!e;const o=e&&0!==e.indexOf(w.OSANO_IFRAME_URI)&&("http"===e.slice(0,4)||"//"===e.slice(0,2))&&e||(t&&t.indexOf("<anonymous>")>=0?r&&C:void 0);if(o)return[o]}return[]}const M=function(e){const{node:t=e,src:n}=e||N,r=t&&n?String(n).substring(0,20).trim().substring(0,5):"";return!r&&(null==t?void 0:t.textContent)||"data:"===r},R=function(e){const{node:t=e}=e||N;return!(!t||!M(e))||!(!t||!t[f.wo]||t===t[f.wo])&&R(t[f.wo])},F=function(e){const{node:t=e}=e||N;return!(!t||!t[f.I_])||!(!t||!t[f.wo]||t===t[f.wo])&&F(t[f.wo])};function H(e,t){if(!R(e)){const n=e&&e.ownerDocument?e.ownerDocument.currentScript:f.Bl.currentScript;if(n)return[n,[]];const r=t||e&&e.ownerDocument===f.Bl&&L()||[],o=r[r.length-1];return[o&&(0,j.iF)(o)||void 0,r]}return[void 0,[]]}const B=function(e,t){const{node:n=e}=e||N;if(!R(e)){if(F(e))return!0;const[r,o]=H(n,t);return F(r)||function(e){try{return(e||L()).filter((function(e){return e===C||E.has(e)})).length>0||void 0}catch(t){return!1}}(o)}return!1},U=function(){return!0},K=function(e){if(e&&(0,m.Tv)(e)){const t=I(e),{src:n=k.oK.call(e)}=t||e;n&&!M(t||e)&&E.add(n)}try{Object.defineProperty(e,f.I_,{configurable:!1,enumerable:!1,get:U})}catch(t){}return e},W=["img","script","iframe"];function J(e){if(e&&e.tagName&&!e[f.b7]&&!e[f.gY]&&W.includes(e.tagName.toLowerCase())){try{const[t,n]=H(e);Object.defineProperty(e,f.wo,{configurable:!1,enumerable:!1,get:function(){return t}}),B(e,n)&&K(e)}catch(t){}try{Object.defineProperty(e,f.b7,{configurable:!1,enumerable:!1,get:U})}catch(t){}}}const G=function(e){const{classifications:t,value:n}=e,r=t.shift();if(void 0===r)return f.zT;const o=r[0],i=new RegExp(""+o,"gm");if(String(n).match(i)){const{classification:e=r[1]}=r[1]||{};return e}},Y=function(e,t){var n=e.data.classifications,r=n.length;function o(i){var s=e.data.category||"";if(i)for(var c=0;c<r;c++){if(t.loop!==o)return"";var a=n[c][0],l=new RegExp(""+a,"gm");if(String(i).match(l)){var u=n[c][1];s=u&&u.classification||u;break}}return t.postMessage(s||"UNCLASSIFIED"),String(s||"UNCLASSIFIED")}return t.loop=o,o(e.data.value)},V=Y.toString();let Q=!1;try{const e=p(V);e.onmessage=function(){Q=!0,e.terminate()},e.onerror=function(){Q=!1,e.terminate()},e.postMessage({value:"",classifications:[["",""]]})}catch(re){Q=!1}const q=function(e,t){if(t&&(t[f.I_]||t[f.wo]&&t[f.wo][f.I_]))return K(t),[e,!0];const n=(0,$.A)(e,(0,_.A)(t&&t.ownerDocument||f.Bl));if(t&&e&&(!/^http(s)?:/.test(n.href)||f.TW.test(n.href)||f.aD.test(n.href)))return[e,!0];try{f.Y3.href=n.href}catch(r){return[e,!0]}return[e?String(n.href):e,!1]},z=function(e,t,n){const{entityType:r}=e||{},{node:o}=e,i=o&&o[f.gY]||o,s=I(i)||x({},e);let c=!1,a=!1,l="",u="",p="",m=/||/g;const h=(0,g.aX)(n,i);switch(r){case"iframe":{p=A.LC.value.call(o,"src")||"",u=e.src||p||s.src||"",l=u;const[t,r]=q(u,o);c=s.src!==t,a=!!u&&o&&!h,m=f.aD,(0,g.XL)(n)&&!r||(e.ignore=!0),e.src=t,u=t;break}case"script":{const t=o.parentElement;p=A.LC.value.call(o,"src")||k.oK.call(o)||"",u=e.src||p||s.src||"",l=t&&s.originalValue||u;const[n,r]=q(u,o);c=!(t&&p||s.src===n),a=!f.lw&&!!u&&!!o&&P.L.value.call(o,"async")&&!h,m=f.TW,r&&(e.ignore=!0),e.src=n,u=t?l:n;break}case"cookie":u=e.name,p=e.name,l=e.value,c=s.name!==u,a=!1,m=f.Wm}if(u=String(u),Object.entries(e).forEach((function([e,t]){if("ignore"===e)!1===t&&delete s[`${e}`]||(s[`${e}`]=t);else void 0!==t&&(s[`${e}`]=t)})),(!s.classification||c&&u)&&(s.classification="",s.originalValue=l,s.async&&s.async.running&&(s.ignore||!a?(s.async.terminate(),delete s.async):s.async.postMessage({value:e.src,classifications:Object.entries(t)})),!s.async&&!s.ignore)){if(h)s.classification=h;else{const e=T[r][u];if(e)s.classification=s.classification||e;else{const[e,n]=t[`${p}`]?[t[`${p}`],p]:function(e,t){f.Y3.href=e;const n=f.Y3.pathname.split("/").filter((function(e){return!!e})),r=`${f.Y3.protocol}//${f.Y3.hostname}/`;return n.reduce((function([e,n],r){if(e)return[e,n];const o=`${n}${r}/`;return[t[`${o}`],o]}),[t[`${e}`]||t[`${r}`],r])}(u,t);if(e){const{classification:o=e}=e;o?(s.classification=o,T[r][u]=o):delete t[`${n}`]}}}if(!s.classification)if(a)s.async=new d(Q?V:G),s.async.classify({value:e.src,classifications:Object.entries(t)}).then((function(e){return delete s.async,s.classification=e,T[r][u]=e,s})).catch((function(e){if(delete s.async,"abort"===e)s.classification="",delete T[r][u];else s.classification=f.zT,T[r][u]=f.zT;return s}));else{const n=function(e,t,n){const{name:r,src:o=r}=e;let i=T[n][o];return void 0===i&&(i=Y({data:{value:o,classifications:Object.entries(t)}},{postMessage:function(){}}),T[n][o]=i),i}(e,t,r);s.classification=n,T[r][u]=n}}(!s.fromOsano||c&&u)&&(s.fromOsano=m.test(u)),"cookie"===r&&!s.fromOsano&&["",f.zT].includes(s.classification)&&(s.ignore=s.ignore||B());try{D.set(i,s)}catch(b){}return s},Z=function(e,t){const n="string"==typeof e?e.replace(/\s*;\s*/g,";").split(";").reduce((function(e,t,n){const[r,...o]=t.split("="),i=o.join("=");if(0===n)return-1!==t.indexOf("=")||i?x(x({},e),{},{name:r,value:i}):x(x({},e),{},{name:"",value:r});switch(r.toLowerCase()){case"domain":case"path":case"secure":case"httponly":case"samesite":case"expires":case"max-age":return x(x({},e),{},{[r.toLowerCase()]:i||""})}return e}),{}):e,{name:r,value:o,httponly:i,Secure:s=!1,secure:c=s,path:a,domain:l,expires:u,"max-age":p,Samesite:d="lax",SameSite:f=d,samesite:m=f,ignore:h}=n,b={};return b.secure=""===c||!!c,void 0!==a&&(b.path=a.replace(/['"]/g,"")),void 0!==l&&(b.domain=l),void 0!==p?b["max-age"]=p:void 0!==u&&(b.expires=u),b.samesite=m,void 0!==h&&(b.ignore=h),void 0!==i&&(b.httponly=i),z(x(x({name:r,value:o},b),{},{entityType:"cookie"}),(0,g.qv)(t),t)},X=function(e,t){let n=e;e instanceof Node&&(n={node:e});const{node:r}=n,o=A.LC.value.call(r,"src")||"",{src:i,classification:s,ignore:c}=n,a={node:r,classification:s,src:i||o,entityType:"iframe"};return void 0!==c&&(a.ignore=c),z(a,(0,g.Cw)(t),t)},ee=function(e,t){let n=e;if(e instanceof Node){if(!(0,m.Qn)(e))return;n={node:e}}const{node:r}=n,o=A.LC.value.call(r,"src")||"",{src:i,classification:s,ignore:c}=n,a={node:r,classification:s,src:i||o,entityType:"script"};return void 0!==c&&(a.ignore=c),z(a,(0,g.nj)(t),t)},te=function(e,t){let{node:n=e,tagName:r=n.tagName||"",entityType:o=r.toLowerCase()}=e||{};try{switch(o){case"iframe":return X(e,t);case"script":return ee(e,t)}}catch(i){}},ne=function(e,t){return I(e)&&te({node:e,ignore:!1},t)}},8251:(e,t,n)=>{"use strict";n.d(t,{UF:()=>f,e1:()=>u,KU:()=>p});var r=n(5126),o=n(1286);var i=n(8734);const s=["hideOptOut","eu","tcf","usp"],c=["iab","cookies","iframes","scripts"];function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const u=function(customerConfig,flavor,language){return l({type:flavor,lang:language||"en"},customerConfig||window.customerConfig||{})},p=void 0!==window.__CSP_NONCE?window.__CSP_NONCE:i.Bl.currentScript&&i.Bl.currentScript.nonce||void 0,d=function(e,t){if(!t)return e;const n=Object.keys(t);return l(l({},t),Object.entries(e).filter((function([e]){return!n.includes(e)})).reduce((function(e,[t,n]){return e[`${t}`]=n,e}),{}))},f=function(e,t){const{iab:{hideOptOut:n=!1,eu:o,tcf:i=o,usp:a}={},cookies:u,iframes:p,scripts:f}=e,g=(0,r.A)(e.iab,s),m=(0,r.A)(e,c),{notified:h}=a||{};return{config:l({cookies:d(u,null==t?void 0:t.cookies),iframes:d(p,null==t?void 0:t.iframes),scripts:d(f,null==t?void 0:t.scripts)},m),iab:l(l({},g),{},{tcf:i,usp:{hideOptOut:n,notified:h,signatory:void 0}})}}},8734:(e,t,n)=>{"use strict";n.d(t,{FA:()=>ie,rO:()=>q,iZ:()=>ee,KW:()=>$e,D4:()=>Se,Fr:()=>se,rG:()=>Z,qY:()=>xe,Tn:()=>Oe,jA:()=>le,SD:()=>ue,q6:()=>pe,yp:()=>ce,u4:()=>ae,ft:()=>fe,aI:()=>de,RG:()=>te,e5:()=>me,hT:()=>Ee,TG:()=>Q,gz:()=>he,PA:()=>Pe,H7:()=>re,UM:()=>U,Wm:()=>_e,wo:()=>R,EH:()=>ge,aD:()=>Ae,gY:()=>H,bW:()=>B,TW:()=>ke,b7:()=>M,I_:()=>F,$w:()=>z,Kq:()=>ve,_U:()=>V,O3:()=>X,AQ:()=>K,SP:()=>J,O8:()=>G,gy:()=>W,hB:()=>Y,Pb:()=>De,fb:()=>L,zT:()=>ne,_$:()=>we,AR:()=>be,ex:()=>ye,zM:()=>oe,Y3:()=>I,q9:()=>T,lw:()=>m,iQ:()=>Ce,Yk:()=>O,Bl:()=>C,gg:()=>N,Bh:()=>h});var r=n(4274),o=n(1286),i=n(4525);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}const c="en",a=["ar","arc","ckb","dv","fa","ha","he","khw","ks","ps","ur","uz-af","yi"],l=new WeakMap,u=new WeakMap,p=new WeakMap;var d=n(8428),f=n(1344);const g=new Map,m=!!window.document.documentMode,h=function(e){try{return e.EventTarget}catch(t){return}},b=document.createElement("iframe");document.head.appendChild(b);let y=b.contentWindow||window;const O={Document:y.Document,Element:y.Element,EventTarget:h(y),HTMLDocument:y.HTMLDocument,HTMLElement:y.HTMLElement,HTMLIFrameElement:y.HTMLIFrameElement,HTMLScriptElement:y.HTMLScriptElement,Navigator:y.Navigator,Node:y.Node,Storage:y.Storage,window:y};!m&&document.head.removeChild(b);const{Document:v,Element:w,EventTarget:$,HTMLDocument:_,HTMLElement:j,HTMLIFrameElement:A,HTMLImageElement:k,HTMLScriptElement:P,Navigator:S,Node:x,Storage:E}=O,D=Symbol("osano.cmp.domLoaded"),T=function(){return g.get(D)},C=document;g.set(D,!1),document.addEventListener("DOMContentLoaded",(function(){g.set(D,!0)}));const N=window,I=C.createElement("a"),L=parseInt("%%TATTLE_RATIO%%",10),M=Symbol("osano.cmp.stamp"),R=Symbol("osano.cmp.createdBy"),F=Symbol("osano.cmp.userscript"),H=Symbol("osano.cmp.originalNode"),B=Symbol("osano.cmp.replacementNode"),U=Symbol("osano.cmp.allowed"),K="osano_consentmanager",W=`${K}_tattles-ignore`,J=`${K}_tattles-cookie`,G=`${K}_tattles-iframe`,Y=`${K}_tattles-script`,V="osano/blocked",Q="MARKETING",q="ANALYTICS",z="PERSONALIZATION",Z="ESSENTIAL",X="STORAGE",ee="BLACKLISTED",te="HIDDEN",ne="UNCLASSIFIED",re="OPT_OUT",oe=[Q,q,z,Z],ie="ACCEPT",se="DENY",ce="config",ae="consent",le={[q]:["analytics_storage"],[Q]:["ad_storage","ad_user_data","ad_personalization"],[z]:["personalization_storage"],[Z]:["security_storage","functionality_storage"]},ue="default",pe="update",de="granted",fe={[ie]:de,[se]:"denied"},ge="dMzRlOT",me=r.IFRAME_TIMEOUT||10,he=10,be="VALIDATE_IFRAME",ye="VALIDATE_IFRAME_RESPONSE",Oe="GET_STORAGE",ve="RECEIVE_STORAGE",we="UPDATE_STORAGE",$e="CLEAR_STORAGE",_e=new RegExp(`^${K}`),je=(0,d.A)(r.OSANO_IFRAME_URI,(0,f.A)(document)),Ae=new RegExp(`^(?:https?:)?(?://)?(?:${je.host}${je.pathname}|${r.OSANO_DSAR_URI})(([#|?].*)|(?!.*))?$`),ke=new RegExp(`${r.OSANO_SCRIPT_URI}(?:/[^?#]*)?/osano(?:-[a-z]*(?:\\.legacy|\\.modern)?)?.js(?:[#|?].*)?$`),Pe=33177600,Se=Pe,xe=(new Date(864e13).getTime(),{BLOCKING:"osano-cm-blocking",INIT:"osano-cm-initialized",NEW_CONSENT:"osano-cm-consent-new",CONSENT_CHANGED:"osano-cm-consent-changed",CONSENT_SAVED:"osano-cm-consent-saved",LOCALE_UPDATED:"osano-cm-locale-updated",SCRIPT_BLOCKED:"osano-cm-script-blocked",COOKIE_BLOCKED:"osano-cm-cookie-blocked",IFRAME_BLOCKED:"osano-cm-iframe-blocked",CONSENT_ACCEPT_MARKETING:"osano-cm-marketing",CONSENT_ACCEPT_ANALYTICS:"osano-cm-analytics",CONSENT_ACCEPT_PERSONALIZATION:"osano-cm-personalization",CONSENT_ACCEPT_STORAGE:"osano-cm-storage",CCPA_OPT_OUT:"osano-cm-opt-out",UI_CHANGED_STATE:"osano-cm-ui-changed"}),Ee=new class{setup(e={},t=c){const n={};Object.entries(e).forEach((function([locale,e]){n[`${locale}`]=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e)})),l.set(this,n),u.set(this,{}),p.set(this,t)}clear(locale){return locale?delete l.get(this)[`${locale}`]:l.set(this,{}),this}extend(e,locale=c){return l.get(this)[`${locale}`]=(0,i.h1)(l.get(this)[`${locale}`]||{},e),this}override(e,locale=c){return u.get(this)[`${locale}`]=e,this}set locale(locale){p.set(this,locale)}get locale(){return p.get(this)}get fallbackLanguages(){const[e,t]=this.locale.split("-");return t?[this.locale,e,c]:[this.locale,c]}get isRTL(){return a.includes(this.locale)}replace(e,t){return t?l.get(this)[`${t}`]=e||{}:l.set(this,{[this.locale]:e||{}}),this}template(e){var t=this;return function(n,r){return t.translate(e,n,r)}}translate(e,t={},n){const locale="string"==typeof t?t:t.locale||this.locale;let r="string"==typeof n?n:`${locale}.${e}`;const o=l.get(this)[`${locale}`],s=u.get(this)[`${locale}`];let c=s&&(0,i.Jt)(s,e)||(0,i.Jt)(o,e)||(locale.length>2?this.translate(e,locale.slice(0,2),r):r);const{interpolations:a={}}=t;return Object.entries(a).forEach((function([e,t]){const n=new RegExp(`%{${e}}`,"g");c=c.replace(n,t)})),c}isLocaleAvailable(locale){return!!r.LANGUAGES.includes(locale)}isLocaleLoaded(locale){const e=l.get(this)[`${locale}`];return"object"==typeof e&&Object.keys(e).length>0}isEmpty(){return 0===Object.keys(l.get(this)).length}},De={None:0,Dialog:1,Drawer:2},Te="fr",Ce={re:Te,gp:Te,mq:Te,gy:Te,yt:Te,mf:Te}},3505:(e,t,n)=>{"use strict";n.d(t,{M:()=>r,V:()=>o});const r={PUSH:1,POP:-1,REPLACE:0},o={CONSENT:"consent",DO_NOT_SELL:"doNotSell",DISCLOSURE:"disclosure",VENDOR_CONSENT:"vendorConsent"}},8503:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>Ki,getStyleConstant:()=>Bi});var r,o=n(1286),i=n(8734);const s=window,c=s.trustedTypes,a=c?c.createPolicy("lit-html",{createHTML:function(e){return e}}):void 0,l="$lit$",u=`lit$${(Math.random()+"").slice(9)}$`,p="?"+u,d=`<${p}>`,f=document,g=function(){return f.createComment("")},m=function(e){return null===e||"object"!=typeof e&&"function"!=typeof e},h=Array.isArray,b=function(e){return h(e)||"function"==typeof(null==e?void 0:e[Symbol.iterator])},y="[ \t\n\f\r]",O=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,v=/-->/g,w=/>/g,$=RegExp(`>|${y}(?:([^\\s"'>=/]+)(${y}*=${y}*(?:[^ \t\n\f\r"'\`<>=]|("|')|))|$)`,"g"),_=/'/g,j=/"/g,A=/^(?:script|style|textarea|title)$/i,k=function(e){return function(t,...n){return{_$litType$:e,strings:t,values:n}}},P=k(1),S=(k(2),Symbol.for("lit-noChange")),x=Symbol.for("lit-nothing"),E=new WeakMap,D=f.createTreeWalker(f,129,null,!1);function T(e,t){if(!Array.isArray(e)||!e.hasOwnProperty("raw"))throw Error("invalid template strings array");return void 0!==a?a.createHTML(t):t}const C=function(e,t){const n=e.length-1,r=[];let o,i=2===t?"<svg>":"",s=O;for(let c=0;c<n;c++){const t=e[c];let n,a,p=-1,f=0;for(;f<t.length&&(s.lastIndex=f,a=s.exec(t),null!==a);)f=s.lastIndex,s===O?"!--"===a[1]?s=v:void 0!==a[1]?s=w:void 0!==a[2]?(A.test(a[2])&&(o=RegExp("</"+a[2],"g")),s=$):void 0!==a[3]&&(s=$):s===$?">"===a[0]?(s=null!=o?o:O,p=-1):void 0===a[1]?p=-2:(p=s.lastIndex-a[2].length,n=a[1],s=void 0===a[3]?$:'"'===a[3]?j:_):s===j||s===_?s=$:s===v||s===w?s=O:(s=$,o=void 0);const g=s===$&&e[c+1].startsWith("/>")?" ":"";i+=s===O?t+d:p>=0?(r.push(n),t.slice(0,p)+l+t.slice(p)+u+g):t+u+(-2===p?(r.push(void 0),c):g)}return[T(e,i+(e[n]||"<?>")+(2===t?"</svg>":"")),r]};class N{constructor({strings:e,_$litType$:t},n){let r;this.parts=[];let o=0,i=0;const s=e.length-1,a=this.parts,[d,f]=C(e,t);if(this.el=N.createElement(d,n),D.currentNode=this.el.content,2===t){const e=this.el.content,t=e.firstChild;t.remove(),e.append(...t.childNodes)}for(;null!==(r=D.nextNode())&&a.length<s;){if(1===r.nodeType){if(r.hasAttributes()){const e=[];for(const t of r.getAttributeNames())if(t.endsWith(l)||t.startsWith(u)){const n=f[i++];if(e.push(t),void 0!==n){const e=r.getAttribute(n.toLowerCase()+l).split(u),t=/([.?@])?(.*)/.exec(n);a.push({type:1,index:o,name:t[2],strings:e,ctor:"."===t[1]?F:"?"===t[1]?B:"@"===t[1]?U:R})}else a.push({type:6,index:o})}for(const t of e)r.removeAttribute(t)}if(A.test(r.tagName)){const e=r.textContent.split(u),t=e.length-1;if(t>0){r.textContent=c?c.emptyScript:"";for(let n=0;n<t;n++)r.append(e[n],g()),D.nextNode(),a.push({type:2,index:++o});r.append(e[t],g())}}}else if(8===r.nodeType)if(r.data===p)a.push({type:2,index:o});else{let e=-1;for(;-1!==(e=r.data.indexOf(u,e+1));)a.push({type:7,index:o}),e+=u.length-1}o++}}static createElement(e,t){const n=f.createElement("template");return n.innerHTML=e,n}}function I(e,t,n=e,r){var o,i,s,c;if(t===S)return t;let a=void 0!==r?null===(o=n._$Co)||void 0===o?void 0:o[r]:n._$Cl;const l=m(t)?void 0:t._$litDirective$;return(null==a?void 0:a.constructor)!==l&&(null===(i=null==a?void 0:a._$AO)||void 0===i||i.call(a,!1),void 0===l?a=void 0:(a=new l(e),a._$AT(e,n,r)),void 0!==r?(null!==(s=(c=n)._$Co)&&void 0!==s?s:c._$Co=[])[r]=a:n._$Cl=a),void 0!==a&&(t=I(e,a._$AS(e,t.values),a,r)),t}class L{constructor(e,t){this._$AV=[],this._$AN=void 0,this._$AD=e,this._$AM=t}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(e){var t;const{el:{content:n},parts:r}=this._$AD,o=(null!==(t=null==e?void 0:e.creationScope)&&void 0!==t?t:f).importNode(n,!0);D.currentNode=o;let i=D.nextNode(),s=0,c=0,a=r[0];for(;void 0!==a;){if(s===a.index){let t;2===a.type?t=new M(i,i.nextSibling,this,e):1===a.type?t=new a.ctor(i,a.name,a.strings,this,e):6===a.type&&(t=new K(i,this,e)),this._$AV.push(t),a=r[++c]}s!==(null==a?void 0:a.index)&&(i=D.nextNode(),s++)}return D.currentNode=f,o}v(e){let t=0;for(const n of this._$AV)void 0!==n&&(void 0!==n.strings?(n._$AI(e,n,t),t+=n.strings.length-2):n._$AI(e[t])),t++}}class M{constructor(e,t,n,r){var o;this.type=2,this._$AH=x,this._$AN=void 0,this._$AA=e,this._$AB=t,this._$AM=n,this.options=r,this._$Cp=null===(o=null==r?void 0:r.isConnected)||void 0===o||o}get _$AU(){var e,t;return null!==(t=null===(e=this._$AM)||void 0===e?void 0:e._$AU)&&void 0!==t?t:this._$Cp}get parentNode(){let e=this._$AA.parentNode;const t=this._$AM;return void 0!==t&&11===(null==e?void 0:e.nodeType)&&(e=t.parentNode),e}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(e,t=this){e=I(this,e,t),m(e)?e===x||null==e||""===e?(this._$AH!==x&&this._$AR(),this._$AH=x):e!==this._$AH&&e!==S&&this._(e):void 0!==e._$litType$?this.g(e):void 0!==e.nodeType?this.$(e):b(e)?this.T(e):this._(e)}k(e){return this._$AA.parentNode.insertBefore(e,this._$AB)}$(e){this._$AH!==e&&(this._$AR(),this._$AH=this.k(e))}_(e){this._$AH!==x&&m(this._$AH)?this._$AA.nextSibling.data=e:this.$(f.createTextNode(e)),this._$AH=e}g(e){var t;const{values:n,_$litType$:r}=e,o="number"==typeof r?this._$AC(e):(void 0===r.el&&(r.el=N.createElement(T(r.h,r.h[0]),this.options)),r);if((null===(t=this._$AH)||void 0===t?void 0:t._$AD)===o)this._$AH.v(n);else{const e=new L(o,this),t=e.u(this.options);e.v(n),this.$(t),this._$AH=e}}_$AC(e){let t=E.get(e.strings);return void 0===t&&E.set(e.strings,t=new N(e)),t}T(e){h(this._$AH)||(this._$AH=[],this._$AR());const t=this._$AH;let n,r=0;for(const o of e)r===t.length?t.push(n=new M(this.k(g()),this.k(g()),this,this.options)):n=t[r],n._$AI(o),r++;r<t.length&&(this._$AR(n&&n._$AB.nextSibling,r),t.length=r)}_$AR(e=this._$AA.nextSibling,t){var n;for(null===(n=this._$AP)||void 0===n||n.call(this,!1,!0,t);e&&e!==this._$AB;){const t=e.nextSibling;e.remove(),e=t}}setConnected(e){var t;void 0===this._$AM&&(this._$Cp=e,null===(t=this._$AP)||void 0===t||t.call(this,e))}}class R{constructor(e,t,n,r,o){this.type=1,this._$AH=x,this._$AN=void 0,this.element=e,this.name=t,this._$AM=r,this.options=o,n.length>2||""!==n[0]||""!==n[1]?(this._$AH=Array(n.length-1).fill(new String),this.strings=n):this._$AH=x}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(e,t=this,n,r){const o=this.strings;let i=!1;if(void 0===o)e=I(this,e,t,0),i=!m(e)||e!==this._$AH&&e!==S,i&&(this._$AH=e);else{const r=e;let s,c;for(e=o[0],s=0;s<o.length-1;s++)c=I(this,r[n+s],t,s),c===S&&(c=this._$AH[s]),i||(i=!m(c)||c!==this._$AH[s]),c===x?e=x:e!==x&&(e+=(null!=c?c:"")+o[s+1]),this._$AH[s]=c}i&&!r&&this.j(e)}j(e){e===x?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=e?e:"")}}class F extends R{constructor(){super(...arguments),this.type=3}j(e){this.element[this.name]=e===x?void 0:e}}const H=c?c.emptyScript:"";class B extends R{constructor(){super(...arguments),this.type=4}j(e){e&&e!==x?this.element.setAttribute(this.name,H):this.element.removeAttribute(this.name)}}class U extends R{constructor(e,t,n,r,o){super(e,t,n,r,o),this.type=5}_$AI(e,t=this){var n;if((e=null!==(n=I(this,e,t,0))&&void 0!==n?n:x)===S)return;const r=this._$AH,o=e===x&&r!==x||e.capture!==r.capture||e.once!==r.once||e.passive!==r.passive,i=e!==x&&(r===x||o);o&&this.element.removeEventListener(this.name,this,r),i&&this.element.addEventListener(this.name,this,e),this._$AH=e}handleEvent(e){var t,n;"function"==typeof this._$AH?this._$AH.call(null!==(n=null===(t=this.options)||void 0===t?void 0:t.host)&&void 0!==n?n:this.element,e):this._$AH.handleEvent(e)}}class K{constructor(e,t,n){this.element=e,this.type=6,this._$AN=void 0,this._$AM=t,this.options=n}get _$AU(){return this._$AM._$AU}_$AI(e){I(this,e)}}const W={O:l,P:u,A:p,C:1,M:C,L,D:b,R:I,I:M,V:R,H:B,N:U,U:F,F:K},J=s.litHtmlPolyfillSupport;null==J||J(N,M),(null!==(r=s.litHtmlVersions)&&void 0!==r?r:s.litHtmlVersions=[]).push("2.7.5");const G=function(e,t,n){var r,o;const i=null!==(r=null==n?void 0:n.renderBefore)&&void 0!==r?r:t;let s=i._$litPart$;if(void 0===s){const e=null!==(o=null==n?void 0:n.renderBefore)&&void 0!==o?o:null;i._$litPart$=s=new M(t.insertBefore(g(),e),e,void 0,null!=n?n:{})}return s._$AI(e),s};var Y=n(5036);const V=i.hT.template("aria.newWindow"),Q=i.hT.template("aria.external"),q=i.hT.template("aria.externalNewWindow");var z=n(5126);const Z={block:"__",modifier:"--",space:"-",value:"_"};function X(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?X(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):X(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const te=function({block:e,element:t,modifiers:n={},prefix:r="",syntax:o={},verbose:i=!1,className:s=[]}){if(!e&&!t)throw new Error("You must specify a block or an element when using BEM syntax");const c=s?Array.isArray(s)?s:"object"==typeof s?Object.keys(s):s.split(" "):[],a=ee(ee({},Z),o),l=r?r.replace(/^['"]/,"").replace(/['"]$/,""):"";if(e){c.push(t?`${l}${e}${a.block}${t}`:`${l}${e}`);(n.block?Object.keys(n.block):[]).forEach((function(r){const o=!(!n.block[`${r}`]||""===n.block[`${r}`])&&n.block[`${r}`];if("true"===o||o===!!o){if(o){const n=`${l}${e}${a.modifier}${r}`;c.push(n),i&&t&&c.push(`${n}${a.block}${t}`)}}else{const n=`${l}${e}${a.modifier}${r}${a.value}${o}`;c.push(n),i&&t&&c.push(`${n}${a.block}${t}`)}}))}if(t){c.push(`${l}${t}`);(n.element?Object.keys(n.element):[]).forEach((function(r){const o=!(!n.element[`${r}`]||""===n.element[`${r}`])&&n.element[`${r}`];"true"===o||o===!!o?o&&(c.push(`${l}${t}${a.modifier}${r}`),i&&e&&c.push(`${l}${e}${a.block}${t}${a.modifier}${r}`)):(c.push(`${l}${t}${a.modifier}${r}${a.value}${o}`),i&&e&&c.push(`${l}${e}${a.block}${t}${a.modifier}${r}${a.value}${o}`))}))}return c.map((function(e){return e.toString().replace(/\s\s+/g," ").replace(/[\s!"#$%&'()*+,./:<=>?@[\\\]^`{|}~]/g,a.space)}))},ne=function(...e){return te.apply(null,e).reduce((function(e,t){return e[`${t}`]=!0,e}),{})},re=te,oe=1,ie=2,se=function(e){return function(...t){return{_$litDirective$:e,values:t}}};class ce{constructor(e){}get _$AU(){return this._$AM._$AU}_$AT(e,t,n){this._$Ct=e,this._$AM=t,this._$Ci=n}_$AS(e,t){return this.update(e,t)}update(e,t){return this.render(...t)}}const ae=se(class extends ce{constructor(e){var t;if(super(e),e.type!==oe||"class"!==e.name||(null===(t=e.strings)||void 0===t?void 0:t.length)>2)throw Error("`classMap()` can only be used in the `class` attribute and must be the only part in the attribute.")}render(e){return" "+Object.keys(e).filter((function(t){return e[t]})).join(" ")+" "}update(e,[t]){var n,r,o=this;if(void 0===this.it){this.it=new Set,void 0!==e.strings&&(this.nt=new Set(e.strings.join(" ").split(/\s/).filter((function(e){return""!==e}))));for(const e in t)t[e]&&!(null===(n=this.nt)||void 0===n?void 0:n.has(e))&&this.it.add(e);return this.render(t)}const i=e.element.classList;this.it.forEach((function(e){e in t||(i.remove(e),o.it.delete(e))}));for(const s in t){const e=!!t[s];e===this.it.has(s)||(null===(r=this.nt)||void 0===r?void 0:r.has(s))||(e?(i.add(s),this.it.add(s)):(i.remove(s),this.it.delete(s)))}return S}}),le=function(e){return null!=e?e:x},ue=["block","label","disabled","onClick","className","modifiers","ariaLabel","tabIndex"];function pe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function de(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pe(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const fe="button";function ge(e){const{block:t,label:n,disabled:r,onClick:o,className:i,modifiers:s={},ariaLabel:c,tabIndex:a=0}=e,l=(0,z.A)(e,ue),{prefix:u="osano-cm-"}=l,p={root:ne({prefix:u,block:t,element:fe,modifiers:{block:s.block,element:de(de({},s.element),{},{disabled:r})},className:i})};return P`<button class="${ae(p.root)}" .disabled="${r}" @click="${le(o)}" aria-label="${le(c)}" tabindex="${a}"> ${n} </button>`}var me=n(5785),he=n(8437);function be(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ye(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?be(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):be(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Oe=function({dispatch:e}){return function(){e((0,me.Qi)(i.FA)),e((0,me.Zx)(i.FA)),e((0,me.ov)(i.FA)),e(he.Ay.acceptAllConsent()),e(he.Ay.saveConsent()),e(he.Ay.hideAll()),e(he.Ay.showWidget()),e(he.Ay.focusWidget())}},ve={element:{type:"accept"}},we=i.hT.template("buttons.acceptAll");function $e(e){const{prefix:t,store:n,onClick:r}=e;return P`${ge(ye(ye({},e),{},{className:`${t}accept-all`,label:we(),onClick:null!=r?r:Oe(n),modifiers:ve}))}`}function _e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function je(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_e(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_e(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Ae=function({dispatch:e}){return function(){e(he.Ay.acceptAllConsent()),e(he.Ay.saveConsent()),e(he.Ay.hideDialog()),e(he.Ay.showWidget()),e(he.Ay.focusWidget())}},ke={element:{type:"accept"}},Pe=i.hT.template("buttons.accept");function Se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function xe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Se(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Ee=function({dispatch:e}){return function(){e((0,me.Qi)(i.Fr)),e((0,me.Zx)(i.Fr)),e((0,me.ov)(i.Fr)),e((0,me.$V)(i.Fr)),e(he.Ay.denyAllConsent()),e(he.Ay.saveConsent()),e(he.Ay.hideAll()),e(he.Ay.showWidget()),e(he.Ay.focusWidget())}},De={element:{type:"denyAll"}},Te=i.hT.template("buttons.denyAll"),Ce=i.hT.template("buttons.rejectNonEssential");function Ne(e){const{prefix:t,store:n,onClick:r}=e;return P`${ge(xe(xe({},e),{},{label:(0,Y.Hb)(n.getState())?Ce():Te(),className:`${t}denyAll`,onClick:null!=r?r:Ee(n),modifiers:De}))}`}function Ie(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Le(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ie(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ie(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Me=function({dispatch:e}){return function(){e(he.Ay.denyAllConsent()),e(he.Ay.saveConsent()),e(he.Ay.hideDialog()),e(he.Ay.showWidget()),e(he.Ay.focusWidget())}},Re={element:{type:"deny"}},Fe=i.hT.template("buttons.deny"),He=i.hT.template("buttons.denyNonEssential");function Be(e){const{prefix:t,store:n,block:r}=e;return P`<div class="osano-cm-drawer-iab-button-container"> ${$e({block:r,prefix:t,store:n})} ${Ne({block:r,prefix:t,store:n})} </div>`}function Ue(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ke(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ue(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ue(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const We=function({dispatch:e}){return function(){e(he.Ay.saveConsent()),e(he.Ay.hideDialog()),e(he.Ay.showWidget())}},Je={element:{type:"save"}},Ge=i.hT.template("buttons.save");const Ye=["ariaDescribedBy","block","className","href","target","label","onClick","tabIndex","title","type"];function Ve(e){const{ariaDescribedBy:t,block:n,className:r="",href:o,target:i,label:s,onClick:c,tabIndex:a=0,title:l,type:u}=e,p=(0,z.A)(e,Ye),{prefix:d="osano-cm-"}=p,f={root:ne({prefix:d,block:n,element:"link",className:r,modifiers:{element:{type:u}}})};return P`<a tabindex="${a}" rel="noopener" href="${le(o)}" target="${le(i)}" class="${ae(f.root)}" @click="${c}" title="${le(l)}" aria-describedby="${le(t)}">${s}</a>`}i.hT.template("buttons.denyAll");function Qe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function qe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Qe(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Qe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ze(e){const t=e.store.getState(),n=e.linkText||(0,Y.IZ)(t),r=i.hT.template(`buttons.${n}`),o=i.hT.template(`messaging.${n}`),s=`${e.prefix}${e.className||"storage-policy"}`,c=r(void 0,o()),a=e.href||(0,Y.Pq)(t);return P`${Ve(qe(qe({},e),{},{ariaDescribedBy:`${e.prefix}aria.newWindow`,className:s,label:c,href:a,target:"_blank"}))}`}var Ze=n(924),Xe=n(2240),et=n(3505);const tt=function(e){return function(t){t(he.Ay.showDisclosure(et.M.PUSH,e))}},nt=["type","purposeId","onClick","retention"];function rt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}const ot=i.hT.template("iab.legal.title"),it=i.hT.template("iab.partnerCount"),st=i.hT.template("iab.partnerCountPlural"),ct=i.hT.template("iab.vendor.dataRetention");function at(e){const{type:t,purposeId:n,onClick:r,retention:s}=e,c=(0,z.A)(e,nt),{prefix:a,store:{dispatch:l,getState:u}}=c,p=n?`${a}tcf-v2-legal--${t}_${n}`:`${a}tcf-v2-legal`,d=n?i.hT.translate(`iab.${t}.${n}.description`):void 0,f=n?i.hT.translate(`iab.${t}.${n}.name`):ot(),g=Object.keys((0,Xe.GQ)(u(),{type:t,id:n})).length,m=1===g?it({interpolations:{partnerCount:g}}):st({interpolations:{partnerCount:g}});return P`${Ve(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?rt(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):rt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({label:`${f} ${"purposes"===t?`(${m})`:""}`,onClick:r||function(e){l(tt(n));const t=document.getElementById(p);return(0,Ze.mH)(t||e.target).scrollTop=t?t.offsetTop:0,!1},title:d,type:n?"purpose":void 0},c))}${s?` - ${ct({interpolations:{days:s}})}`:""}`}const lt=function(e){return function(t){t(he.Ay.showVendorConsent(et.M.PUSH,e))}},ut=["vendorId","onClick"];function pt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}const dt=i.hT.template("iab.vendor.title");function ft(e){const{vendorId:t,onClick:n}=e,r=(0,z.A)(e,ut),{store:{dispatch:i,getState:s}}=r,c=s(),a=t?(0,Xe.Tp)(c,t).name:dt();return P`${Ve(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pt(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({label:a,onClick:n||function(){return i(lt(t)),!1},type:"vendor"},r))}`}var gt=n(3803),mt=n(6269);const ht=["id","ariaLabel","ariaLevel","ariaDescription","descriptionId","block","label","checked","disabled","onChange","className","category","tabIndex","type","name","value"],bt="toggle";function yt(e){const{id:t=(0,mt.v4)("toggle"),ariaLabel:n,ariaLevel:r,ariaDescription:o,descriptionId:i,block:s,label:c,checked:a,disabled:l,onChange:u,className:p,category:d,tabIndex:f=0,type:g,name:m,value:h}=e,b=(0,z.A)(e,ht),{prefix:y="osano-cm-"}=b,O={root:ne({prefix:y,block:s,element:bt,modifiers:{element:{checked:a,disabled:l,type:g}},className:p}),input:ne({prefix:y,block:bt,element:"input",modifiers:{element:{checked:a,disabled:l}}}),switch:ne({prefix:y,block:bt,element:"switch"}),label:ne({prefix:y,block:bt,element:"label"})};return P`<label class="${ae(O.root)}" for="${t}"><input class="${ae(O.input)}" id="${t}" type="checkbox" @change="${u}" .checked="${a}" .disabled="${l}" data-category="${le(d)}" name="${le(m)}" value="${le(h)}" role="switch" tabindex="${f}" aria-label="${le(n)}" aria-description="${le(o)}" aria-describedby="${le(!o&&i?i:void 0)}"><span class="${ae(O.switch)}"></span><span role="${le(r?"heading":void 0)}" aria-level="${le(r)}" class="${ae(O.label)}">${c}</span></label>`}function Ot(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function vt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ot(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ot(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const wt=function({category:e,store:t}){return function(n){const r=n.target.checked;t.dispatch(he.Ay.setConsent(e,r?i.FA:i.Fr))}};function $t(e){const{category:t,prefix:n,store:{getState:r}}=e,o=r(),s=(0,Y.MJ)(o),c=(0,Y.aE)(o,t),a=(0,Y.pU)(o,t),l=s&&`${n}drawer-toggle--category_${t}--description`,u=i.hT.translate(`categories.${t}.label`)||"",p=i.hT.translate(`categories.${t}.label`)||"";return yt(vt(vt({},e),{},{descriptionId:l,label:u,title:p||u,checked:c,disabled:a,onChange:wt(e)}))}function _t(e){const{store:{getState:t},label:n}=e,r=t(),o=(0,mt.v4)("arialabelclose"),{linkColor:i,closeButtonColor:s=i}=(0,Y.Sf)(r);return P`<svg width="20px" height="20px" viewBox="0 0 20 20" role="img" aria-labelledby="${o}"> <title id="${o}">${n}</title> <line role="presentation" x1="2" y1="2" x2="18" y2="18" .stroke="${s}"/> <line role="presentation" x1="2" y1="18" x2="18" y2="2" .stroke="${s}"/> </svg>`}function jt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function At(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?jt(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):jt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const kt=function(e,{className:t,block:n,prefix:r="osano-cm-",modifiers:o={}}={},i){return{prefix:r,block:n,element:e,className:t,modifiers:At(At({},o),{},{element:At(At({},o.element),i)})}},Pt=function(...e){return ne(kt(...e))},St=function(...e){return re(kt(...e)).join(" ")};var xt=n(4525);const{I:Et}=W,Dt=function(e,t){return void 0===t?void 0!==(null==e?void 0:e._$litType$):(null==e?void 0:e._$litType$)===t},Tt=function(){return document.createComment("")},Ct=function(e,t,n){var r;const o=e._$AA.parentNode,i=void 0===t?e._$AB:t._$AA;if(void 0===n){const t=o.insertBefore(Tt(),i),r=o.insertBefore(Tt(),i);n=new Et(t,r,e,e.options)}else{const t=n._$AB.nextSibling,s=n._$AM,c=s!==e;if(c){let t;null===(r=n._$AQ)||void 0===r||r.call(n,e),n._$AM=e,void 0!==n._$AP&&(t=e._$AU)!==s._$AU&&n._$AP(t)}if(t!==i||c){let e=n._$AA;for(;e!==t;){const t=e.nextSibling;o.insertBefore(e,i),e=t}}}return n},Nt=function(e,t,n=e){return e._$AI(t,n),e},It={},Lt=function(e,t=It){return e._$AH=t},Mt=function(e){return e._$AH},Rt=function(e){var t;null===(t=e._$AP)||void 0===t||t.call(e,!1,!0);let n=e._$AA;const r=e._$AB.nextSibling;for(;n!==r;){const e=n.nextSibling;n.remove(),n=e}},Ft=function(e){e._$AR()},Ht=function(e,t,n){const r=new Map;for(let o=t;o<=n;o++)r.set(e[o],o);return r},Bt=se(class extends ce{constructor(e){if(super(e),e.type!==ie)throw Error("repeat() can only be used in text expressions")}dt(e,t,n){let r;void 0===n?n=t:void 0!==t&&(r=t);const o=[],i=[];let s=0;for(const c of e)o[s]=r?r(c,s):s,i[s]=n(c,s),s++;return{values:i,keys:o}}render(e,t,n){return this.dt(e,t,n).values}update(e,[t,n,r]){var o;const i=Mt(e),{values:s,keys:c}=this.dt(t,n,r);if(!Array.isArray(i))return this.ht=c,s;const a=null!==(o=this.ht)&&void 0!==o?o:this.ht=[],l=[];let u,p,d=0,f=i.length-1,g=0,m=s.length-1;for(;d<=f&&g<=m;)if(null===i[d])d++;else if(null===i[f])f--;else if(a[d]===c[g])l[g]=Nt(i[d],s[g]),d++,g++;else if(a[f]===c[m])l[m]=Nt(i[f],s[m]),f--,m--;else if(a[d]===c[m])l[m]=Nt(i[d],s[m]),Ct(e,l[m+1],i[d]),d++,m--;else if(a[f]===c[g])l[g]=Nt(i[f],s[g]),Ct(e,i[d],i[f]),f--,g++;else if(void 0===u&&(u=Ht(c,g,m),p=Ht(a,d,f)),u.has(a[d]))if(u.has(a[f])){const t=p.get(c[g]),n=void 0!==t?i[t]:null;if(null===n){const t=Ct(e,i[d]);Nt(t,s[g]),l[g]=t}else l[g]=Nt(n,s[g]),Ct(e,i[d],n),i[t]=null;g++}else Rt(i[f]),f--;else Rt(i[d]),d++;for(;g<=m;){const t=Ct(e,l[m+1]);Nt(t,s[g]),l[g++]=t}for(;d<=f;){const e=i[d++];null!==e&&Rt(e)}return this.ht=c,Lt(e,l),S}}),Ut=["children","term","id","render","termClass","descriptionClass"];function Kt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Wt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Kt(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Kt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Jt(e){const{children:t,term:n,id:r=(0,mt.v4)("dt"),render:o,termClass:i,descriptionClass:s}=e,c=(0,z.A)(e,Ut),a="list-item",l={term:Pt(a,Wt(Wt({},e),{},{className:i}),{type:"term"}),description:Pt(a,Wt(Wt({},e),{},{className:s}),{type:"description"})};return P` <dt class="${ae(l.term)}" id="${r}">${n}</dt> ${Bt(t,xt.D_,(function(e){return P` <dd role="definition" aria-labelledby="${r}" class="${ae(l.description)}"> ${o?o(Wt(Wt({},c),{},{block:a},e)):e} </dd> `}))} `}const Gt=se(class extends ce{constructor(...e){super(...e),(0,o.A)(this,"part",null),(0,o.A)(this,"children",null),(0,o.A)(this,"mapper",null),(0,o.A)(this,"index",0)}commitChild(e){0===this.index&&Ft(this.part);const t=Ct(this.part);Nt(t,e)}draw(){var e=this;const{children:t,mapper:n}=this;window.requestAnimationFrame((function(r){e&&e.children===t&&e.mapper===n&&e.loopChildren(r)}))}loopChildren(e){for(;this.index<this.children.length;){let t=this.children[this.index];if(void 0!==this.mapper&&(t=this.mapper(t,this.index)),this.commitChild(t),this.index+=1,Date.now()-e>10)return void this.draw()}}update(e,[t,n]){return t===this.children&&n===this.mapper||(this.part=e,this.children=t,this.mapper=n,this.index=0,this.loopChildren(Date.now())),S}render(){return S}});se(class extends ce{constructor(e){super(e),this.loaded=!1}update({element:e},[t,...n]){return this.loaded?S:(t.apply(void 0,[e,...n]),this.loaded=!0)}render(){return this.loaded}});const Yt=new WeakMap;class Vt extends ce{constructor(e){super(e)}static onScroll(e){e.stopPropagation();const t=Yt.get(e.target)||{};null!=t&&t.activeView&&(t.scrollPositions[`${t.activeView}`]=e.target.scrollTop)}static saveScrollState(e,t){const n=Yt.get(e)||{activeView:"",scrollPositions:{}},r=n.activeView!==t;if(r){const r=n.activeView||t;n.activeView=t,n.scrollPositions[`${r}`]=e.scrollTop||0}return Yt.set(e,n),r}update(e,[t]){const{element:n}=e;if(Vt.saveScrollState(n,t)){var r;const e=Yt.get(n),o=null!==(r=e.scrollPositions[`${t}`])&&void 0!==r?r:0;window.cancelAnimationFrame(e.nextScroll),e.nextScroll=window.requestAnimationFrame((function(){n.scrollTop=o}))}return n.addEventListener("scroll",Vt.onScroll),""}render(){return S}}const Qt=se(Vt),qt={},zt=se(class extends ce{constructor(){super(...arguments),this.st=qt}render(e,t){return t()}update(e,[t,n]){var r=this;if(Array.isArray(t)){if(Array.isArray(this.st)&&this.st.length===t.length&&t.every((function(e,t){return e===r.st[t]})))return S}else if(this.st===t)return S;return this.st=Array.isArray(t)?Array.from(t):t,this.render(t,n)}}),Zt=["block","children","itemId","itemRenderer"];function Xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function en(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function tn(e){const{block:t,children:n=[],itemId:r="id",itemRenderer:o}=e,i=(0,z.A)(e,Zt);return n.length>30?zt(n,(function(){return P`${Gt(n,(function(e){return o(en(en({},i),"object"==typeof e?e:{[r]:e}))}))}`})):Bt(n,(function(e){return o(en(en({},i),{},{block:t},"object"==typeof e?e:{[r]:e}))}))}const nn=["id","element","className","itemClassName","title","ariaLive","role"];function rn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function on(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?rn(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):rn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const sn=["render","elementModifiers"];function cn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function an(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cn(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const ln="list-item";function un(e){const{render:t,elementModifiers:n}=e,r=(0,z.A)(e,sn),o={root:Pt(ln,r,n)};return P`<li class="${ae(o.root)}"> ${t(an(an({},r),{},{block:ln}))} </li>`}const pn=["id","element","className","itemClassName","title","ariaLive","role"];function dn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function fn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?dn(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):dn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function gn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function mn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?gn(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const hn="list";function bn(e){const{type:t}=e,n=St(hn,e,{type:t});return"description"===t?function(e){const{id:t,element:n,className:r="",itemClassName:o="",title:i,ariaLive:s,role:c="list"}=e,a=(0,z.A)(e,nn);return P`<dl id="${le(t)}" class="${le(r)}" role="${c}" aria-label="${le(i)}" aria-live="${le(s)}"> ${tn(on(on({},a),{},{block:n,className:o,itemRenderer:Jt}))} </dl> `}(mn(mn({},e),{},{className:n,element:hn})):function(e){const{id:t,element:n,className:r="",itemClassName:o="",title:i,ariaLive:s,role:c="list"}=e,a=(0,z.A)(e,pn);return P`<ul id="${le(t)}" class="${r}" role="${c}" aria-label="${le(i)}" aria-live="${le(s)}"> ${tn(fn(fn({},a),{},{block:n,className:o,itemRenderer:un}))} </ul>`}(mn(mn({},e),{},{className:n,element:hn}))}const yn=function(e,t,n){const r=(0,Y.qi)(e).toLowerCase(),o=null==r?void 0:r.split("-")[0];return i.hT.translate(`${t}.${o}`,void 0,n)},On=["block"],vn=["block"],wn=["block","disabled","className"],$n=["block"],_n=["block"],jn=["id","block","disabled","className"],An=["id","block","className"];function kn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Pn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?kn(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):kn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Sn=i.hT.template("messaging.usageWhat"),xn=i.hT.template("messaging.usageHow"),En=i.hT.template("messaging.usageList"),Dn=i.hT.template("messaging.timer"),Tn=i.hT.template("messaging.categories"),Cn=i.hT.template("messaging.default"),Nn=i.hT.template("iab.layer1"),In=i.hT.template("messaging.closeButton"),Ln=i.hT.template("dialog.close"),Mn=i.hT.template("dialog.label"),Rn=i.hT.template("buttons.denyAll"),Fn=i.hT.template("buttons.managePreferences"),Hn=i.hT.template("drawer.header"),Bn=i.hT.template("iab.vendor.title"),Un=function({dispatch:e,getState:t}){return function(){const n=t();!(0,Y.Gs)(n)&&(0,Y.dP)(n)&&(e(he.Ay.acceptAllConsent()),e(he.Ay.saveConsent())),e(he.Ay.hideDialog()),e(he.Ay.showWidget()),e(he.Ay.focusWidget())}},Kn=function({dispatch:e}){return function(){e(he.Ay.hideDialog()),e(he.Ay.showDrawer())}},Wn=function({dispatch:e}){return function(){e(he.Ay.hideDialog()),e(he.Ay.showVendorConsent(et.M.REPLACE))}},Jn=function(e){const t=Object.assign({},(function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(e),e)),{prefix:n,store:r}=t,{getState:o}=r,i=o(),s=yn(i,"buttons.dialog.openDrawer",Fn());return P`${ge(Pn(Pn({},e),{},{className:`${n}manage`,label:s,onClick:Kn(r),modifiers:{element:{type:"manage"}}}))}`},Gn=function(e){const{block:t}=e,n=(0,z.A)(e,On),{store:r}=n,{getState:o}=r,i=o();return P`${(0,Y.Ow)(i)?Jn(e):function(e){const{prefix:t,store:n}=e;return P`${ge(Ke(Ke({},e),{},{className:`${t}save`,label:Ge(),onClick:We(n),modifiers:Je}))}`}(Pn(Pn({},n),{},{block:t}))}`},Yn=function(e){const{block:t}=e,n=(0,z.A)(e,vn),{store:r}=e,{getState:o}=r,i=o();if(!(0,Y.sD)(i))return"";const s=yn(i,"buttons.dialog.denyAll",Rn());return P`${Ne(Pn(Pn({},n),{},{block:t,label:s}))}`},Vn=function(e){const{block:t,disabled:n,className:r=""}=e,o=(0,z.A)(e,wn),{prefix:i,store:s}=o,{getState:c}=s,a=c(),l="buttons",u=n||!(0,Y.R_)(a),p={root:ne({prefix:i,block:t,element:l,modifiers:{element:{disabled:u}},className:r})};return(0,Y.dP)(a)?(0,Y.td)(a)?P`<div class="${ae(p.root)}"> ${Jn(Pn(Pn({},o),{},{block:l}))} </div>`:"":(0,Xe.lF)(a)||(0,Y.kA)(a)?P` <div class="${ae(p.root)}"> ${Gn(Pn(Pn({},o),{},{block:l}))} ${$e(Pn(Pn({},o),{},{block:l}))} ${Yn(Pn(Pn({},o),{},{block:l}))} </div> `:P` <div class="${ae(p.root)}"> ${function(e){const{prefix:t,store:n}=e;return P`${ge(je(je({},e),{},{className:`${t}accept`,label:Pe(),onClick:Ae(n),modifiers:ke}))}`}(Pn(Pn({},o),{},{block:l}))} ${function(e){const{prefix:t,store:n}=e;return P`${ge(Le(Le({},e),{},{className:`${t}deny`,label:(0,Y.Xg)(n.getState())?He():Fe(),onClick:Me(n),modifiers:Re}))}`}(Pn(Pn({},o),{},{block:l}))} ${Yn(Pn(Pn({},o),{},{block:l}))} ${(0,Y.td)(a)?Jn(Pn(Pn({},o),{},{block:l})):""} </div>`},Qn=function({line:e}){return e},qn="dialog";function zn(e){const{id:t=(0,mt.v4)(qn),block:n,className:r=""}=e,o=(0,z.A)(e,An),{prefix:s="osano-cm-",store:c}=o,{getState:a}=c,l=a(),u=(0,Y.rE)(l),p=(0,Xe.lF)(l),d=(0,Y.I1)(l),f=!(0,Y.R_)(l),g=p?(0,Y.PR)(l):(0,Y.U0)(l),m=(0,Y.LU)(l),h=(0,Y.Sf)(l),{dialogType:b,displayPosition:y}=h,O=`${t}__label`,v={root:ne({prefix:s,block:n,element:qn,modifiers:{element:{hidden:f,position:y,type:b,context:(0,Y.jB)(l)&&"amp",wide:"box"===b&&p}},className:r}),content:ne({prefix:s,block:qn,element:"content"})},w=u.filter((function([e]){return"subjectRightsRequest"!==e})).map((function([e,t]){return P`<span class="osano-cm-link-separator"></span>${ze(Pn(Pn({},o),{},{block:"content",className:"additional-link",href:t,linkText:e}))}`})),$=`${s}dialog-toggle-group`;return P` <div id="${t}" role="dialog" aria-label="${Mn()}" aria-describedby="${O}" class="${ae(v.root)}" @keyup="${function(e){if("keyup"!==e.type)return;const{getState:t}=c,n=t();27===(e.charCode||e.keyCode)&&(0,Y.Um)(n)&&(e.preventDefault(),e.stopPropagation(),(0,Y.sD)(n)?Ee(c)():Un(c)())}}"> ${function(e){const{block:t,className:n,prefix:r,store:o}=e,i=o.getState();if(!(0,Y.Um)(i))return"";const s={root:ne({prefix:r,block:t,element:"close",className:n})},c=(0,Y.sD)(i)?Ee(o):Un(o);return P` <button class="${ae(s.root)}" @click="${c}"> ${_t({store:o,label:Ln()})} </button> `}(Pn(Pn({},o),{},{block:"dialog"}))} <div class="${ae(v.content)}"> ${function(e){const{id:t,block:n,disabled:r,className:o=""}=e,s=(0,z.A)(e,jn),{prefix:c,store:{getState:a}}=s,l=a(),u=(0,Y.I1)(l),p=(0,Y.Kn)(l)||Object.keys((0,Xe.YQ)(l)).length,d={root:ne({prefix:c,block:n,className:o,element:"message",modifiers:{element:{disabled:r}}})},f={analytics:i.hT.translate("categories.ANALYTICS.label").toLowerCase(),personalization:i.hT.translate("categories.PERSONALIZATION.label").toLowerCase(),marketing:i.hT.translate("categories.MARKETING.label").toLowerCase()};let g=i.hT.template("iab.purposes.1.name")();gt.L_.forEach((function(e,t){const n=i.hT.template(`iab.stacks.${e}.name`),r=t===gt.L_.length-1?`, and ${n()}`:`, ${n()}`;g=g.concat(r)}));const m={};for(const y of gt.A5){const e=i.hT.template(`iab.specialFeatures.${y}.name`);m[`iab.specialFeatures.${y}.name`]=e().toLowerCase()}const h=Pn({vendorCount:p,stacks:g.toLowerCase()},m),b=Nn({interpolations:h}).split("\n\n");return P` <span id="${t}" class="${ae(d.root)}"> ${(0,Xe.lF)(l)?b.map((function(e,t){return P`${t>0?P`<br><br>`:null}${e}`})):u?Sn():Cn({interpolations:f})} ${(0,Y.dP)(l)?Dn():(0,Y.ze)(l)?Tn():""} ${(0,Y.Um)(l)&&(0,Y.sD)(l)?In():""} </span> `}(Pn(Pn({},o),{},{block:"content",id:O}))} ${d?function(e){const{block:t}=e,n=(0,z.A)(e,_n),{prefix:r}=n,o="usage-list",i={root:ne({prefix:r,block:t,element:o})};return P` <p class="${ae(i.root)}"> ${xn()} ${bn(Pn(Pn({},n),{},{block:o,children:En().split(/\r?\n/).map((function(e){return{line:e}})),render:Qn}))} </p> `}(Pn(Pn({},o),{},{block:"content"})):""} ${ze(Pn(Pn({},o),{},{block:"content"}))}${w} ${p||(0,Y.Gy)(l)?function(e){const{block:t}=e,n=(0,z.A)(e,$n),{prefix:r,store:o,drawerId:i}=n,s=o.getState(),c="drawer-links",a={root:ne({prefix:r,block:t,element:c})};return P` <p class="${ae(a.root)}"> ${(0,Y.Ow)(s)?"":Ve(Pn(Pn({},n),{},{block:c,type:"manage",label:Hn(),href:`#${i}`,onClick:Kn(o)}))} ${(0,Xe.lF)(s)?Ve(Pn(Pn({},n),{},{block:c,type:"vendor-list",label:Bn(),onClick:Wn(o)})):""} </p> `}(Pn(Pn({},o),{},{block:"content",id:t})):""} ${m?P`<div role="group" aria-labelledby="${$}"> ${bn(Pn(Pn({},o),{},{id:$,tabIndex:f?-1:0,block:"dialog",children:g.map((function(e){return{category:e,id:`${s}dialog-toggle--category_${e}`}})),itemId:"category",render:$t,title:Hn()}))} </div>`:""} </div> ${Vn(Pn(Pn({},o),{},{block:"dialog"}))} </div> `}const Zn=function(){return function(e,t){e(he.Ay.revertConsent()),e(he.Ay.hideDrawer());const n=t();setTimeout((function(){e(he.Ay.hideDoNotSell()),(0,Y.Gs)(n)?(e(he.Ay.showWidget()),e(he.Ay.focusWidget())):e(he.Ay.showDialog())}),400)}};const Xn=se(class extends ce{constructor(e){super(e),this.tt=new WeakMap}render(e){return[e]}update(e,[t]){if(Dt(this.et)&&(!Dt(t)||this.et.strings!==t.strings)){const t=Mt(e).pop();let n=this.tt.get(this.et.strings);if(void 0===n){const e=document.createDocumentFragment();n=G(x,e),n.setConnected(!1),this.tt.set(this.et.strings,n)}Lt(n,[t]),Ct(n,void 0,t)}if(Dt(t)){if(!Dt(this.et)||this.et.strings!==t.strings){const n=this.tt.get(t.strings);if(void 0!==n){const t=Mt(n).pop();Ft(e),Ct(e,void 0,t),Lt(e,[t])}}this.et=t}else this.et=void 0;return this.render(t)}}),er=function({category:e,open:t}){return function(n){n(he.Ay.toggleDisclosure(e,t))}},tr=["block","name","title","expiry","provider","purpose","type"],nr=["category","disclosures","onToggle","className","label","tabIndex"];function rr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function or(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?rr(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):rr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const ir=i.hT.template("disclosure.titles.name"),sr=i.hT.template("disclosure.titles.provider"),cr=i.hT.template("disclosure.noProvider"),ar=i.hT.template("disclosure.titles.expiry"),lr=i.hT.template("disclosure.titles.purpose"),ur=i.hT.template("disclosure.titles.type"),pr=i.hT.template("messaging.viewDisclosures"),dr=i.hT.template("disclosure.none"),fr={image:"trackingPixel"},gr=function(e){const{block:t,name:n,title:r,expiry:o,provider:s,purpose:c,type:a="cookie"}=e,l=(0,z.A)(e,tr),u=fr[a]||a;if("no-results"===u)return P`${dr()}`;const{prefix:p="osano-cm-"}=l,d="disclosure-item",f=St(d,or(or({},l),{},{prefix:p,block:t})),g={termClass:St("title",{prefix:p,block:d}),descriptionClass:St("description",{prefix:p,block:d})},m=i.hT.template(`disclosure.types.${(0,xt.xQ)(u)}`);return P`${Xn(bn(or(or({},l),{},{block:t,className:`${St(`${(0,xt.kW)(u)}-disclosure`,{prefix:p})} ${f}`,children:[or(or({},g),{},{modifiers:{element:{term:"name"}},term:ir(),children:[r||n||" "]}),or(or({},g),{},{modifiers:{element:{term:"provider"}},term:sr(),children:[s||cr()]}),or(or({},g),{},{modifiers:{element:{term:"type"}},term:ur(),children:["iframe"===u?"iFrame":(0,xt.kR)(m({},(0,xt.LW)(`${u||" "}`).toLowerCase().replace(/_/g," ")))]}),...null!=o&&o.replace(/\s+/g,"")?[or(or({},g),{},{modifiers:{element:{term:"expiry"}},term:ar(),children:[o||" "]})]:[],or(or({},g),{},{modifiers:{element:{term:"purpose"}},term:lr(),children:[c||" "]})],type:"description"})))}`},mr="disclosure";const hr=i.hT.template("iab.terms.consent"),br=i.hT.template("iab.terms.legInt");const yr=function(e,t){return function(n){return function(r){const o=n.target.checked;r(he.Ay.setConsent(e,o?i.FA:i.Fr,t))}}},Or=["purposeId"],vr=["category","block","hasDisclosures"];function wr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function $r(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wr(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _r(e){const{purposeId:t}=e,n=(0,z.A)(e,Or),r=n.store.getState(),o=!!(0,Xe.kb)(r)[`${t}`],s=!!(0,Xe.jP)(r)[`${t}`];return P`${at(e)}${t?function(e){const{consentChecked:t,liChecked:n,purposeId:r,prefix:o,store:{dispatch:s}}=e,c=function(e,t){return function(n){n.preventDefault();const o=t?i.Fr:i.FA;return s(e(r,o)),!1}},a=`${o}tcf-purpose${r}-consent`,l=hr(),u=`${o}tcf-purpose--label`,p=P`<label for="${a}" class="${u}"> <input id="${a}" name="${a}" type="checkbox" .checked="${t}" value="purpose${r}-consent" @change="${c(me.b6,t)}"> ${l} </label>`;if(!gt.C1.includes(r))return p;const d=`${o}tcf-purpose${r}-legint`,f=br(),g=P`<label for="${d}" class="${u}"> <input id="${d}" name="${d}" type="checkbox" .checked="${n}" value="purpose${r}-legint" @change="${c(me.sL,n)}"> ${f} </label>`;return P`${p}${g}`}($r({purposeId:t,consentChecked:o,liChecked:s},n)):""}`}function jr(e){const{category:t="",block:n,hasDisclosures:r=!0}=e,o=(0,z.A)(e,vr),{prefix:s,store:{dispatch:c,getState:a}}=o,l=a(),u=`${s}drawer-toggle--category_${t}--description`,p=(0,Xe.lF)(l)&&gt.pO[`${t}`],d=p?i.hT.translate(`iab.stacks.${p}.name`):i.hT.translate(`categories.${t}.label`)||"",f=p?`iab.stacks.${p}.description`:`categories.${t}.description`,g=(i.hT.translate(f)||"").split("\n\n").filter(Boolean),m=(gt.Ll[`${t}`]||[]).map((function(e){return{purposeId:e,type:"purposes"}})),h="description",b={description:ne({prefix:s,block:n,element:h})};return P`${yt($r($r({},o),{},{ariaLevel:"2",id:`${s}drawer-toggle--category_${t}`,descriptionId:u,category:t,block:n,label:d,checked:(0,Y.aE)(l,t),disabled:(0,Y.pU)(l,t),onChange:function(e){return c(yr(t,(0,Y.IP)(l))(e))},className:re({prefix:s,block:n,element:"drawer-toggle"}).join(" ")}))} <div class="${ae(b.description)}"> <p id="${u}"> ${g.map((function(e,t){return P`${t>0?P`<br><br>`:null}${e}`}))} </p> ${(0,Xe.lF)(l)?bn($r($r({},o),{},{block:h,children:m,itemId:"purposeId",render:_r})):""} </div> ${r?function(e){const{category:t,disclosures:n=[],onToggle:r=function(){},className:o="",label:i,tabIndex:s=0}=e,c=(0,z.A)(e,nr),{prefix:a,store:{dispatch:l,getState:u}}=c,p=u(),d=`${a}${t}_disclosures`,f=(0,Y.ro)(p)&&!(0,Y.DZ)(p,t),g=!(0,Y.G8)(p,t),m={root:Pt(mr,or(or({},c),{},{className:o}),{collapse:g,loading:f}),toggle:Pt("toggle",or(or({},c),{},{block:mr}))},h=function(e){if(e.preventDefault(),"keypress"===e.type){const t=e.charCode||e.keyCode;if(32!==t&&13!==t)return!1}return l(er({category:t})),r(t,!g),!1};return P`<div class="${ae(m.root)}"> <div class="${ae(m.toggle)}" @click="${h}" @keypress="${h}" tabindex="${s}" role="link" aria-expanded="${!g}" aria-controls="${d}" aria-label="${i} ${pr()}"> ${pr()} </div> ${bn(or(or({},c),{},{id:d,block:"disclosure",children:n,render:gr,ariaLive:g?"none":"polite"}))} </div>`}($r($r({},o),{},{block:n,category:t,disclosures:(0,Y.fc)(l,t),label:d})):""}`}function Ar(e){return Math.max(e||0,0)/60/60/24}const kr=["id","block"];function Pr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Sr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pr(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const xr=i.hT.template("drawer.description"),Er=i.hT.template("iab.drawer.description"),Dr=i.hT.template("doNotSell.description"),Tr=i.hT.template("doNotSell.link");function Cr(e){const{id:t,block:n}=e,r=(0,z.A)(e,kr),{prefix:o,store:{getState:s}}=r,c={description:ne({prefix:o,block:n,element:"description"})},a=s(),l=(0,Xe.lF)(a),u=(0,Y.Pq)(a),p=(0,Y.Zp)(a),d=(0,Y.IZ)(a),f=(0,Y.rE)(a),g=i.hT.template(`buttons.${d}`),m=i.hT.template(`messaging.${d}`),h=Sr(Sr({},r),{},{block:"content",className:"osano-cm-storage-policy",label:g(void 0,m()),href:u,target:"_blank"}),b=f.map((function([e,t]){return P`<span class="osano-cm-link-separator"></span>${Ve(Sr(Sr({},h),{},{className:"osano-cm-additional-link",label:i.hT.template(`messaging.${e}`)(),href:t}))}`})),y=f.filter((function([e]){return"subjectRightsRequest"===e})).map((function([e,t]){return P`${Ve(Sr(Sr({},h),{},{className:"osano-cm-additional-link",label:i.hT.template(`messaging.${e}`)(),href:t}))}`}));return P`<p id="${le(t)}" class="${ae(c.description)}" tabindex="-1"> ${function(e){if((0,Y.IP)(e))return Dr();if((0,Xe.lF)(e))return Er({interpolations:{consentMaxAgeMonths:Math.floor(Ar((0,Y.Ym)(e))/30)}}).split(/\r?\n/g).map((function(e){return P`<br>${e}`}));return xr()}(a)} </p> ${(0,Y.IP)(a)?P`<p class="${ae(c.description)}"> ${Tr()} ${Ve(h)}${b} </p>`:""} <p class="${ae(c.description)}"> ${(0,Y.IP)(a)?"":p?P`${Ve(h)}${b}`:y} ${l?ft(Sr({block:"description"},r)):""} ${l?at(Sr({block:"description"},r)):""} </p> ${l?Be(Sr(Sr({},r),{},{block:n})):""} `}const Nr=function(e){return function(t){return function(n){const r=t.target.checked;n((0,me.c)(e,r?i.FA:i.Fr))}}},Ir=["block","description","name","specialFeatureId"];function Lr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Mr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Lr(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Lr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Rr(e){const{block:t,description:n,name:r,specialFeatureId:o}=e,i=(0,z.A)(e,Ir),{store:{dispatch:s,getState:c}}=i,a=c(),{prefix:l="osano-cm-"}=i,u={description:ne({prefix:l,block:t,element:"description"})};return P` ${yt(Mr(Mr({},i),{},{id:`${l}drawer-toggle--specialFeature_${o}`,block:t,label:r,title:r,checked:(0,Xe.uY)(a,{specialFeatureId:o}),onChange:function(e){return s(Nr(o)(e))},className:re({prefix:l,block:t,element:"drawer-toggle"}).join(" ")}))} <div class="${ae(u.description)}">${n}</div> `}const Fr=["id","active","block","className"];function Hr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Br(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Hr(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Hr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Ur=i.hT.template("buttons.save"),Kr=i.hT.template("messaging.poweredBy");function Wr(e){const{id:t,active:n,block:r,className:o=""}=e,s=(0,z.A)(e,Fr),{prefix:c="osano-cm-",store:{dispatch:a,getState:l}}=s,u=l(),p=(0,Xe.lF)(u),d=[{category:i.H7,hasDisclosures:!1}],f="iab.specialFeatures",g=(0,Y.IP)(u)?d:[...p?(0,Y.PR)(u).map((function(e){return{category:e}})):(0,Y.bW)(u).map((function(e){return{category:e}})),...(0,Y.tu)(u)?d:[],...p?Object.keys(i.hT.translate(f)).map((function(e){const t=`${f}.${e}`;return{specialFeatureId:e,name:i.hT.translate(`${t}.name`),description:i.hT.translate(`${t}.description`),id:`osano-cm-tcf-v2-specialFeatures--specialFeature_${e}`,render:Rr}})):[]],m="view",h={root:ne({prefix:c,block:r,element:m,className:o,modifiers:{element:{active:n,type:"consent"}}}),poweredBy:ne({prefix:c,block:m,element:"powered-by"})};return P`<div class="${ae(h.root)}"> ${Cr(Br(Br({},s),{},{prefix:c,id:t,block:m}))} ${bn(Br(Br({},s),{},{prefix:c,block:m,children:g,render:jr,itemClassName:re({prefix:c,element:"drawer-item"}).join(" ")}))} ${ge(Br(Br({},s),{},{prefix:c,block:m,className:"osano-cm-save",label:Ur(),onClick:function(){return a((function(e){e(he.Ay.saveConsent()),e(he.Ay.hideDrawer()),setTimeout((function(){e(he.Ay.hideDoNotSell()),e(he.Ay.showWidget()),e(he.Ay.focusWidget())}),400)}))}}))} <div class="${ae(h.poweredBy)}"> ${Ve(Br(Br({},s),{},{prefix:c,block:"powered-by",label:Kr(),ariaDescribedBy:`${c}aria.external`,href:"https://www.osano.com/?utm_campaign=cmp&utm_source=cmp-dialog&utm_medium=drawer"}))} </div> </div>`}const Jr=["element","type","plural","itemId"],Gr=["id","active","className"];function Yr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Vr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Yr(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Yr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Qr=i.hT.template("iab.legal.preamble"),qr=i.hT.template("iab.partnerCount"),zr=i.hT.template("iab.partnerCountPlural"),Zr=i.hT.template("iab.terms.illustrations"),Xr=function(e){const{element:t,type:n,plural:r,itemId:o}=e,s=(0,z.A)(e,Jr),{store:{getState:c}}=s,a={description:Pt(t,e)},l=`iab.${r}.${o}`,u=i.hT.translate(`${l}.description`),p=Object.keys((0,Xe.GQ)(c(),{type:"legIntPurposes",id:o})).length,d="purpose"===n?1===p?qr({interpolations:{partnerCount:p}}):zr({interpolations:{partnerCount:p}}):"",f=`${l}.illustrations`,g=i.hT.translate(f),m="object"==typeof g?Object.keys(g).map((function(e){return{item:i.hT.translate(`${f}.${e}`)}})):[];return P`<p class="${ae(a.description)}">${d}</p> <p class="${ae(a.description)}">${u}</p> ${m.length?bn({type:"description",block:"illustrations",children:[{id:`osano-cm-tcf-v2_2--${n}-${o}-illustrations`,term:Zr(),children:m}],render:function({item:e}){return e}}):""}`},eo=function(e,t=`${e}s`){return Object.keys(i.hT.translate(`iab.${t}`)).map((function(n){return{element:e,term:i.hT.translate(`iab.${t}.${n}.name`),id:`osano-cm-tcf-v2-${t}--${e}_${n}`,itemId:n,children:[{type:e,plural:t,itemId:n}]}}))};function to(e){const{id:t,active:n,className:r=""}=e,o=(0,z.A)(e,Gr),i="view",s=eo("purpose"),c=eo("specialPurpose"),a=eo("feature"),l=eo("specialFeature"),u=eo("dataCategory","dataCategories"),p=Vr(Vr({},o),{},{type:"description",block:i,render:Xr}),d={root:Pt(i,Vr(Vr({},o),{},{className:r}),{active:n,type:"disclosure"}),description:Pt("description",p)};return P`<div class="${ae(d.root)}"> <p id="${le(t)}" class="${ae(d.description)}" tabindex="-1"> ${Qr()} </p> ${bn(Vr(Vr({},p),{},{children:s}))}${bn(Vr(Vr({},p),{},{children:c}))}${bn(Vr(Vr({},p),{},{children:a}))}${bn(Vr(Vr({},p),{},{children:l}))}${bn(Vr(Vr({},p),{},{children:u}))} </div> `}const no={preventScroll:!0};function ro(e){return null!==e.offsetParent}const oo=function(e){return(e||document).querySelectorAll('button, a[href]:not([rel="ignore"]), input:not([type="hidden"]):not([type="file"]), select, textarea, [tabindex]:not([tabindex="-1"]):not([data-focus="first"]):not([data-focus="last"])')},io=function(e,t){if(null===e)return;const n=document.activeElement,r=Array.from(oo(e)).filter(ro)[0];try{r.focus(no)}catch(a){t&&t.focus(no)}return n},so={first:"last",last:"first"},co=function(e=""){return function(){const t=Array.from(oo(this.parentNode)),n=so[`${e}`]||"first",r="last"===n?t.length-1:0,o="last"===n?-1:1;let i=t[parseInt(r,10)];if(i){if(i.getAttribute("data-focus")===n&&(i=t[r+o]),i.getAttribute("data-focus")===e)return;try{i.focus()}catch(a){}}}};function ao({dataFocus:e}){return P`${function({onFocus:e,dataFocus:t}){return P`<span tabindex="0" aria-hidden="true" data-focus="${t}" @focus="${e}"></span>`}({dataFocus:e,onFocus:co(e)})}`}const lo=function(e){return function(t){return function(n){const r=t.target.checked;n((0,me.Fk)(e,r?i.FA:i.Fr))}}},uo=function(e){return function(t){return function(n){const r=t.target.checked;n((0,me.gG)(e,r?i.FA:i.Fr))}}};const po=function(e,t){return function(n){n(he.Ay.toggleExpansionPanel(e,t))}},fo=["body","header","id","tabIndex"],go=function(e,{prefix:t,element:n}){const r=`${t}${n}--expanded`;e.nextElementSibling.parentNode.classList.toggle(r)},mo="expansion-panel";const ho=["purposes","vendorId"],bo=["legIntPurposes","vendorId"],yo=["category","block","vendorId"];function Oo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function vo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Oo(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Oo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const wo=i.hT.template("iab.basis.legit"),$o=i.hT.template("iab.basis.consent"),_o=i.hT.template("iab.basis.combined"),jo=i.hT.template("iab.terms.specialPurposes"),Ao=i.hT.template("iab.terms.features"),ko=i.hT.template("iab.terms.specialFeatures"),Po=i.hT.template("iab.terms.dataCategories"),So=i.hT.template("iab.legal.title"),xo=i.hT.template("iab.vendor.usesNonCookieAccess"),Eo=i.hT.template("iab.vendor.maxCookieStorage"),Do=i.hT.template("disclosure.day"),To=i.hT.template("disclosure.dayPlural"),Co=i.hT.template("messaging.moreDetails"),No=i.hT.template("iab.terms.consent"),Io=i.hT.template("iab.terms.legInt"),Lo={privacy:i.hT.template("messaging.privacyPolicy"),legIntClaim:i.hT.template("iab.terms.legIntClaim")},Mo=function(e,t,n){if(!e)return n;const r=e[t];return void 0===r?n:r},Ro=function(e){return function(t){return{retention:Mo(e.purposes,t,e.stdRetention),purposeId:t,type:"purposes"}}},Fo=function(e){return function(t){return{retention:Mo(e.specialPurposes,t,e.stdRetention),purposeId:t,type:"specialPurposes"}}},Ho=function(e){return{purposeId:e,type:"features"}},Bo=function(e){return{purposeId:e,type:"specialFeatures"}},Uo=function(e){return{purposeId:e,type:"dataCategories"}},Ko=function(e,t,n){return null!=t&&t.length?[{term:e(),children:t.map(n)}]:[]},Wo=function(e,t,n){if(null==e||!e.length)return P``;const r=e.reduce((function(e,t){return e.set(t.langId,t)}),new Map),o=i.hT.fallbackLanguages.find((function(e){return r.has(e)})),s=o?r.get(o):e[0],c=Object.entries(Lo).map((function([e,n]){const r=s[e];return r?{label:n(),href:r,ariaDescribedBy:`${t.prefix}aria.externalNewWindow`,target:"_blank"}:void 0})).filter(Boolean);return bn(vo(vo({},t),{},{block:n.block,className:St("vendor-link",n),children:c,render:function(e){return Ve(e)}}))},Jo=function(e){const{purposes:t,vendorId:n}=e,r=(0,z.A)(e,ho);if(0===t.length)return"";const{store:{getState:o}}=r,i=o(),s=No(),c=(0,Xe.Sh)(i)[`${n}`],a=c&&!(0,Xe.CT)(i,{vendorId:n});return yt(vo(vo({},r),{},{label:s,title:s,disabled:!c,checked:a}))},Go=function(e){const{legIntPurposes:t,vendorId:n}=e,r=(0,z.A)(e,bo),{store:{getState:o}}=r,i=o(),s=(0,Xe.g4)(i,n);if(0===t.length&&!s)return"";const c=Io(),a=s||(0,Xe.ZZ)(i)[`${n}`],l=s||a&&!(0,Xe.r6)(i,{vendorId:n});return yt(vo(vo({},r),{},{label:c,title:c,disabled:s||!a,checked:l}))};function Yo(e){const{category:t="",block:n,vendorId:r}=e,o=(0,z.A)(e,yo),{store:{dispatch:i,getState:s}}=o,c=s(),{name:a,purposes:l,legIntPurposes:u,flexiblePurposes:p,specialPurposes:d,features:f,specialFeatures:g,cookieMaxAgeSeconds:m,usesNonCookieAccess:h,deviceStorageDisclosureUrl:b,urls:y,dataRetention:O={},dataDeclaration:v}=(0,Xe.Tp)(c,{vendorId:r}),{prefix:w}=o,$=l.filter((function(e){return!p.includes(e)})),_=Ar(m),j=Ro(O),A=Fo(O);return P`${function(e){const{id:t,level:n,title:r}=e;return r?P`<div id="${le(t)}" role="heading" aria-level="${le(n)}" class="osano-visually-hidden"> ${r} </div>`:""}({level:2,title:a})} <p>${a}</p> ${Jo(vo(vo({},o),{},{purposes:l,vendorId:r,category:t,block:n,id:`${w}drawer-toggle--vendor_${r}`,onChange:function(e){return i(lo(r)(e))},className:St("drawer-toggle",e)}))} ${Go(vo(vo({},o),{},{legIntPurposes:u,vendorId:r,category:t,block:n,id:`${w}drawer-toggle--vendor_${r}_li`,onChange:function(e){return i(uo(r)(e))},className:St("drawer-toggle",e)}))}${Wo(y,o,e)}${function(e){const{body:t="",header:n,id:r=(0,mt.v4)("expansionPanel"),tabIndex:o=0}=e,i=(0,z.A)(e,fo),{prefix:s="osano-cm-",store:c}=i,{dispatch:a,getState:l}=c,u=l(),p=(0,Y.a8)(u,r),d={prefix:s,block:mo},f={root:Pt(mo,e),body:Pt("body",d),toggle:Pt("toggle",d)},g=function(e){if(e.preventDefault(),e.stopPropagation(),"keypress"===e.type){const t=e.charCode||e.keyCode;if(32!==t&&13!==t)return!1}return a(po(r)),go(e.target,{prefix:s,element:mo}),!1};return P` <div class="${ae(f.root)}"> <div class="${ae(f.toggle)}" @click="${g}" @keypress="${g}" tabindex="${o}" role="link" aria-expanded="${p}" aria-controls="${r}" aria-label="${n}" id="${r}-control"> ${n} </div> <div id="${r}" class="${ae(f.body)}" role="presentation" aria-labelledby="${r}-control" aria-live="${p?"polite":"none"}"> ${t} </div> </div> `}(vo(vo({},o),{},{block:n,id:`${w}vendor-disclosure--${r}`,header:So(),body:bn(vo(vo({},o),{},{block:"disclosures",className:St("disclosures",vo(vo({},e),{},{block:"expansion-panel"})),children:[{term:Eo(),children:[{type:"string",disclosure:`${_} ${1===_?Do():To()}`},...h?[{type:"string",disclosure:P`<em>${xo()}</em>`}]:[]]},...Ko(wo,u,j),...Ko($o,$,j),...Ko(_o,p,j),...Ko(jo,d,A),...Ko(Ao,f,Ho),...Ko(ko,g,Bo),...Ko(Po,v,Uo),...b?[{term:Co(),children:[{type:"link",href:b,label:b,ariaDescribedBy:`${w}aria.externalNewWindow`,title:`${a} ${So()}`,target:"_blank"}]}]:[]],type:"description",render:function(e){const{type:t,disclosure:n}=e;return"string"===t?n:"link"===t?Ve(e):at(e)}}))}))}`}const Vo=["id","active","className"];function Qo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function qo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Qo(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Qo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const zo=i.hT.template("iab.vendor.preamble");function Zo(e){const{id:t,active:n,className:r=""}=e,o=(0,z.A)(e,Vo),{prefix:i="osano-cm-",store:{getState:s}}=o,c="view",a=s(),l=(0,Xe.lT)(a),u={root:Pt(c,qo(qo({},e),{},{className:r}),{active:n,type:"vendor-consent"}),description:Pt("description",qo(qo({},o),{},{block:c}))};return P`<div class="${ae(u.root)}"> <p id="${le(t)}" class="${ae(u.description)}" tabindex="-1"> ${zo()} </p> ${Be(qo(qo({},o),{},{block:c}))} ${Xn(bn(qo(qo({},o),{},{block:c,children:l,render:Yo,itemId:"vendorId",itemClassName:St({prefix:i,element:"drawer-item"})})))} </div>`}const Xo=["activeView"],ei=["id"],ti=["className","id"];function ni(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ri(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ni(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ni(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const oi=document.createElement("span");oi.innerHTML="&slarr;";const ii=i.hT.template("drawer.header"),si=i.hT.template("doNotSell.header"),ci=i.hT.template("iab.legal.title"),ai=i.hT.template("iab.vendor.title"),li=i.hT.template("drawer.close"),ui=function(){const e=(0,mt.v4)("ccpaoptouticon");return P`<div class="ccpa-opt-out-icon"> <svg role="img" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 30 14" style="enable-background:new 0 0 30 14" xml:space="preserve" aria-labelledby="${e}"> <title id="${e}">California Consumer Privacy Act (CCPA) Opt-Out Icon</title> <g transform="translate(-1275.000000, -200.000000)"> <g transform="translate(1275.000000, 200.000000)"> <path style="fill-rule:evenodd;clip-rule:evenodd;fill:#fff" d="M7.4,12.8h6.8l3.1-11.6H7.4C4.2,1.2,1.6,3.8,1.6,7S4.2,12.8,7.4,12.8z"/> </g> </g> <g transform="translate(-1275.000000, -200.000000)"> <g transform="translate(1275.000000, 200.000000)"> <path style="fill-rule:evenodd;clip-rule:evenodd;fill:#06f" d="M22.6,0H7.4c-3.9,0-7,3.1-7,7s3.1,7,7,7h15.2c3.9,0,7-3.1,7-7S26.4,0,22.6,0z M1.6,7c0-3.2,2.6-5.8,5.8-5.8 h9.9l-3.1,11.6H7.4C4.2,12.8,1.6,10.2,1.6,7z"/> <path style="fill:#fff" d="M24.6,4c0.2,0.2,0.2,0.6,0,0.8l0,0L22.5,7l2.2,2.2c0.2,0.2,0.2,0.6,0,0.8c-0.2,0.2-0.6,0.2-0.8,0 l0,0l-2.2-2.2L19.5,10c-0.2,0.2-0.6,0.2-0.8,0c-0.2-0.2-0.2-0.6,0-0.8l0,0L20.8,7l-2.2-2.2c-0.2-0.2-0.2-0.6,0-0.8 c0.2-0.2,0.6-0.2,0.8,0l0,0l2.2,2.2L23.8,4C24,3.8,24.4,3.8,24.6,4z"/> <path style="fill:#06f" d="M12.7,4.1c0.2,0.2,0.3,0.6,0.1,0.8l0,0L8.6,9.8C8.5,9.9,8.4,10,8.3,10c-0.2,0.1-0.5,0.1-0.7-0.1l0,0 L5.4,7.7c-0.2-0.2-0.2-0.6,0-0.8c0.2-0.2,0.6-0.2,0.8,0l0,0L8,8.6l3.8-4.5C12,3.9,12.4,3.9,12.7,4.1z"/> </g> </g> </svg> </div>`}(),pi={[et.V.CONSENT]:ii,[et.V.DO_NOT_SELL]:si,[et.V.DISCLOSURE]:ci,[et.V.VENDOR_CONSENT]:ai},di=function(e){return e.stopPropagation()},fi=function(e){switch(e){case et.V.CONSENT:case et.V.DO_NOT_SELL:return Wr;case et.V.DISCLOSURE:return to;case et.V.VENDOR_CONSENT:return Zo}return function(){return P``}},gi=function({dispatch:e}){return function(){return e((function(e){e(he.Ay.goBack())}))}},mi=function(e){const{id:t}=e,n=(0,z.A)(e,ei),{store:r}=e,{getState:o}=r,i="info-dialog-header",s=o(),c=(0,Y.q$)(s),a=(0,Y.kR)(s),l={root:Pt(i,e),close:Pt("close",ri(ri({},e),{},{block:i})),header:Pt("header",ri(ri({},e),{},{block:i}))},u=pi[`${c[`${a}`]}`]||function(){return""},p=pi[`${c[""+(a-1)]}`]||function(){return""};return P`<div class="${ae(l.root)}" role="presentation"> <p role="heading" aria-level="1" id="${le(t)}" class="${ae(l.header)}"> ${u()} </p> ${(0,Y.IP)(s)?ui:""} <button class="${ae(l.close)}" @click="${function({dispatch:e}){return function(){return e(Zn())}}(r)}"> ${_t(ri(ri({},n),{},{block:i,label:li()}))} </button> ${a>0?ge(ri(ri({},n),{},{block:i,className:"osano-cm-back",label:oi,ariaLabel:`← ${p()}`,onClick:gi(r)})):""} </div>`},hi="info";function bi(e){const{className:t="",id:n}=e,r=(0,z.A)(e,ti),{prefix:o,store:{dispatch:i,getState:s}}=r,c=s(),a=!(0,Y.MJ)(c),l=(0,Y.Sf)(c),u=(0,Y.q$)(c).map((function(e,t){return{name:e,index:t,id:`${o}${e}--view`,view:fi(e)}})),p=(0,Y.kR)(c),{infoDialogPosition:d}=l,f=`${n}__label`,g={wrapper:Pt("info-dialog",ri(ri({},e),{},{className:t}),{hidden:a}),root:Pt(hi,ri(ri({},r),{},{block:"info-dialog"}),{position:d,do_not_sell:(0,Y.IP)(c),open:!a}),view:Pt("info-views",ri(ri({},r),{},{block:hi}),{hidden:a,position:`${p}`})},m=u[parseInt(p,10)],h=ri(ri({},r),{},{tabIndex:a?-1:0,activeView:m&&m.name,block:"info-views"});return P`<div id="${n}" role="dialog" aria-labelledby="${f}" aria-describedby="${le(m&&m.id)}" aria-modal="true" aria-hidden="${a&&!function(){const e=document.activeElement,t=document.getElementById(n);return t&&(t===e||t.contains(e))}()}" class="${ae(g.wrapper)}" @click="${function(){return i(Zn())}}"> ${ao({dataFocus:"first"})} <div role="presentation" class="${ae(g.root)}" @click="${di}" ${Qt(null==m?void 0:m.name)} @keyup="${function(e){if("keyup"!==e.type)return;27===(e.charCode||e.keyCode)&&i(Zn())}}"> ${mi(ri(ri({},r),{},{block:hi,id:f}))} <div class="${ae(g.view)}" role="presentation"> ${zt([u],(function(){return Bt(u,(function({name:e}){return e}),function(e){let{activeView:t}=e,n=(0,z.A)(e,Xo);return function({view:e,index:r,id:o,name:i}){return Xn(e(ri(ri({},n),{},{index:r,id:o,active:t===i})))}}(h))}))} </div> </div> ${ao({dataFocus:"last"})} </div>`}const yi=function(e="",t){if(0===e.indexOf("rgb"))try{return e.replace(/[^\d,.]/g,"").split(",").map((function(e){const t=parseFloat(e);if(isNaN(t))throw new Error("Invalid color value");return t}))}catch(i){return yi(t,"rgba(0,0,0,1)")}let n=`${e||""}`.toLowerCase().replace(/[^0-9a-f]/,"");if(3===n.length||4===n.length)n=n.split("").map((function(e){return`${e}${e}`})).join("");else if(6!==n.length&&8!==n.length)return yi(t,"rgba(0,0,0,1)");const r=[];let o=0;for(;o<n.length&&r.length<3;){const e=parseInt(n.slice(o,o+2),16);r.push(255&e),o+=2}if(o<n.length&&3===r.length){const e=parseInt(n.slice(o,o+2),16);r.push(e/255)}return r},Oi=function(e){return(~~(255*e)%256+256)%256},vi=function(e,t,n){return(Math.round(299*e)+Math.round(587*t)+Math.round(114*n))/1e3>=128?0:1},wi=function(e,t,n){return Oi(.21*e+.72*t+.07*n)},$i=function(e,t){const n=Math.abs(t)!==t?-1:1,r=Math.abs(t)<=1;let o=Math.abs(t);o=Math.floor(Math.min(255,Math.max(-255,r?Math.round(255*o):o)))*n;let[i,s,c]="string"==typeof e?yi(e):e;return i+=o,i>255?i=255:i<0&&(i=0),c+=o,c>255?c=255:c<0&&(c=0),s+=o,s>255?s=255:s<0&&(s=0),`#${function(...e){return e.slice(0,3).reduce((function(e,t){return`${e}${`00${t.toString(16)}`.slice(-2)}`}),"")}(i,s,c)}`},_i={},ji=function(e){var t,n;const r=_i[`${e}`]||[yi(e)];return r[1]=null!==(t=r[1])&&void 0!==t?t:vi.apply(null,r[0]),r[2]=null!==(n=r[2])&&void 0!==n?n:wi.apply(null,r[0]),_i[`${e}`]=r,r},Ai=function(e,t=.08){const[,n]=ji(e);return(t<0?!n:n)?(r=e,o=Math.abs(t),$i(r,o)):function(e,t){return $i(e,-t)}(e,Math.abs(t));var r,o},ki=["block","id"],Pi=i.hT.template("messaging.widgetAltText"),Si=function({store:e}){return function(){e.dispatch((function(e){e(he.Ay.hideWidget()),e(he.Ay.showDrawer())}))}},xi="widget";var Ei=n(6277),Di=n(5053),Ti=n(4274);let Ci,Ni;function Ii({dom:e,drawerId:t,dialogId:n,widgetId:r}){return function(o){return function(s){return function(c){const a=s(c),{type:l,payload:u}=c,p=o.getState(),d=(0,Y.YK)(p),f=(0,Y.QH)(p);switch(l){case he.gK.render:e.render();break;case he.gK.ready:switch(u){case"blocking":case"consent":case"dom":if((0,Y.Dp)(p)){const e=(0,Y.z6)(p)||!(0,Y.Gs)(p);requestAnimationFrame((function(){e?o.dispatch(he.Ay.showDialog()):o.dispatch(he.Ay.showWidget())}))}}break;case he.gK.saveConsent:(0,Y.Gs)(p)&&!u.shouldShowDialog&&requestAnimationFrame((function(){o.dispatch(he.Ay.hideDialog())}));break;case he.gK.toggleDisclosure:{const{category:e}=u;!(0,Y.ro)(p)&&!(0,Y.DZ)(p,e)&&(o.dispatch(he.Ay.fetchDisclosuresBegin(e)),function({customerId:e,configId:t,category:n,language="en"}){const r={language,category:n};return Di.A.get(`${Ti.DISCLOSURE_URI}/customer/${e}/config/${t}`,r)}({customerId:d,configId:f,category:e}).then((function(t){const n=t||[];return o.dispatch(he.Ay.fetchDisclosuresSuccess(n,e)),t})).catch((function(t){o.dispatch(he.Ay.fetchDisclosuresFailure(t,e))})));break}case he.gK.hideDialog:clearTimeout(Ni);break;case he.gK.showDialog:(0,Y.fJ)(p)&&(Ni&&clearTimeout(Ni),Ni=function(e){return e.dispatch(he.Ay.timeoutBegin()),setTimeout((function(){(0,Y.Gs)(e.getState())||(e.dispatch(he.Ay.acceptAllConsent()),e.dispatch(he.Ay.saveConsent())),(0,Y.z6)(e.getState())&&(e.dispatch(he.Ay.timeoutComplete()),e.dispatch(he.Ay.showWidget())),(0,Y.HC)(e.getState())||e.dispatch(he.Ay.hideDialog())}),1e3*(0,Y.jU)(e.getState()))}(o)),Ci&&Ci.focus(),Ci=io(i.Bl.getElementById(n));break;case he.gK.showWidget:io(i.Bl.getElementById(r),Ci),Ci=void 0;break;case he.gK.focusWidget:var g;null===(g=i.Bl.getElementById(r))||void 0===g||g.focus(),Ci=void 0;break;case he.gK.showVendorConsent:case he.gK.showDisclosure:case he.gK.showDrawer:Ci&&Ci.focus(),Ci=io(i.Bl.getElementById(t))}return a}}}}var Li=n(6421);var Mi=n(8251),Ri=n(5677);function Fi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Hi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Fi(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Fi(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Bi=function(e){return window.getComputedStyle(i.Bl.querySelector(".osano-cm-window")).getPropertyValue(`--${e}`)},Ui=new WeakMap;class Ki{get middleware(){const{middleware:e}=Ui.get(this);return e}get requiredNodes(){const{container:e,styleContainer:t}=Ui.get(this);return[e,t]}constructor({dynamicMiddleware:e,store:t,config:n}){const r=i.Bl.createElement("div");r.setAttribute("data-nosnippet","");const o=i.Bl.createElement("style");Mi.KU&&Ri.yu.value.call(o,"nonce",Mi.KU);const s=(0,mt.v4)("dialog"),c=(0,mt.v4)("drawer"),a=(0,mt.v4)("widget");Ui.set(this,{template:function(){const{getState:e}=t,{prefix:r="osano-cm-"}=e(),o="window";return P` ${function({prefix:e}){return P`<div hidden class="osano-visually-hidden"> <span id="${`${e}aria.newWindow`}">${V()}</span> <span id="${`${e}aria.external`}">${Q()}</span> <span id="${`${e}aria.externalNewWindow`}">${q()}</span> </div>`}({config:n,prefix:r,store:t})} ${zn({id:s,block:o,config:n,prefix:r,store:t,drawerId:c})} ${(0,Y.Wp)(e())?function(e){const{block:t,id:n}=e,r=(0,z.A)(e,ki),{prefix:o="osano-cm-",store:{getState:i}}=r,s=i(),{widgetPosition:c}=(0,Y.Sf)(s),a=!(0,Y.R_)(s),l=!(0,Y.MJ)(s),u=(0,Y.tC)(s),p={root:ne({prefix:o,block:t,element:xi,modifiers:{element:{hidden:u||!l||!a,position:c}}}),outline:ne({prefix:o,block:xi,element:"outline"}),dot:ne({prefix:o,block:xi,element:"dot"})};return P` <button id="${n}" class="${ae(p.root)}" title="${Pi()}" aria-label="${Pi()}" @click="${Si(e)}"> <svg role="img" width="40" height="40" viewBox="0 0 71.85 72.23" xmlns="http://www.w3.org/2000/svg" aria-labelledby="${n}"> <path d="m67.6 36.73a6.26 6.26 0 0 1 -3.2-2.8 5.86 5.86 0 0 0 -5.2-3.1h-.3a11 11 0 0 1 -11.4-9.5 6 6 0 0 1 -.1-1.4 9.2 9.2 0 0 1 .4-2.9 8.65 8.65 0 0 0 .2-1.6 5.38 5.38 0 0 0 -1.9-4.3 7.3 7.3 0 0 1 -2.5-5.5 3.91 3.91 0 0 0 -3.5-3.9 36.46 36.46 0 0 0 -15 1.5 33.14 33.14 0 0 0 -22.1 22.7 35.62 35.62 0 0 0 -1.5 10.2 34.07 34.07 0 0 0 4.8 17.6.75.75 0 0 0 .07.12c.11.17 1.22 1.39 2.68 3-.36.47 5.18 6.16 5.65 6.52a34.62 34.62 0 0 0 55.6-21.9 4.38 4.38 0 0 0 -2.7-4.74z" stroke-width="3" class="${ae(p.outline)}"></path> <path d="m68 41.13a32.37 32.37 0 0 1 -52 20.5l-2-1.56c-2.5-3.28-5.62-7.15-5.81-7.44a32 32 0 0 1 -4.5-16.5 34.3 34.3 0 0 1 1.4-9.6 30.56 30.56 0 0 1 20.61-21.13 33.51 33.51 0 0 1 14.1-1.4 1.83 1.83 0 0 1 1.6 1.8 9.38 9.38 0 0 0 3.3 7.1 3.36 3.36 0 0 1 1.2 2.6 3.37 3.37 0 0 1 -.1 1 12.66 12.66 0 0 0 -.5 3.4 9.65 9.65 0 0 0 .1 1.7 13 13 0 0 0 10.5 11.2 16.05 16.05 0 0 0 3.1.2 3.84 3.84 0 0 1 3.5 2 10 10 0 0 0 4.1 3.83 2 2 0 0 1 1.4 2z" stroke-width="3" class="${ae(p.outline)}"></path> <g class="${ae(p.dot)}"> <path d="m26.6 31.43a5.4 5.4 0 1 1 5.4-5.43 5.38 5.38 0 0 1 -5.33 5.43z"></path> <path d="m25.2 53.13a5.4 5.4 0 1 1 5.4-5.4 5.44 5.44 0 0 1 -5.4 5.4z"></path> <path d="m47.9 52.33a5.4 5.4 0 1 1 5.4-5.4 5.32 5.32 0 0 1 -5.24 5.4z"></path> </g> </svg> </button> `}({id:a,block:o,config:n,prefix:r,store:t}):null} ${bi({id:c,block:o,config:n,prefix:r,store:t})} `},container:r,dynamicMiddleware:e,middleware:Ii({dom:this,dialogId:s,widgetId:a,drawerId:c}),styleContainer:o,store:t})}setup(){const e=Ui.get(this)||{},{dynamicMiddleware:t,store:n}=e;if(t&&t.addMiddleware(this.middleware),!e.unsubscribe){const t=n.subscribe(this.render.bind(this));Ui.set(this,Hi(Hi({},e),{},{unsubscribe:t}))}return this}teardown(){const e=Ui.get(this)||{},{dynamicMiddleware:t}=e;return t.removeMiddleware(this.middleware),e.unsubscribe&&(e.unsubscribe(),delete e.unsubscribe,Ui.set(this,Hi({},e))),this}render(){if(null===i.Bl.body)return;const{template:e,container:t,styleContainer:n,store:r}=Ui.get(this);if(!(0,Y.Dp)(r.getState()))return;const{getState:o}=r,s=o(),{prefix:c="osano-cm-"}=s,a=re({prefix:c,block:"window",modifiers:{block:{context:(0,Y.jB)(s)&&"amp"}}}).join(" ");return t.className!==a&&(t.className=a),t.parentNode&&t.parentNode===i.Bl.body||(i.Bl.body.firstChild?Li.y.value.call(i.Bl.body,t,i.Bl.body.firstChild):Ei.y.value.call(i.Bl.body,t)),n.parentNode&&n.parentNode===i.Bl.head||(i.Bl.head.firstChild?Li.y.value.call(i.Bl.head,n,i.Bl.head.firstChild):Ei.y.value.call(i.Bl.head,n)),Ri.yu.value.call(t,"dir",i.hT.isRTL?"rtl":"ltr"),G(function(e){const t=e.getState(),{buttonBackgroundColor:n,buttonForegroundColor:r,buttonDenyBackgroundColor:o,buttonDenyForegroundColor:s,dialogBackgroundColor:c,dialogForegroundColor:a,infoDialogBackgroundColor:l,infoDialogForegroundColor:u,infoDialogOverlayColor:p,linkColor:d,toggleOffBackgroundColor:f,toggleButtonOffColor:g,toggleOnBackgroundColor:m,toggleButtonOnColor:h,widgetColor:b,widgetFillColor:y,widgetOutlineColor:O}=(0,Y.Sf)(t),v=`rgba(${yi(l).slice(0,3).join(",")},0)`;return P` ${'.osano-cm-window{font-family:Helvetica,Arial,Hiragino Sans GB,STXihei,Microsoft YaHei,WenQuanYi Micro Hei,Hind,MS Gothic,Apple SD Gothic Neo,NanumBarunGothic,sans-serif;font-size:16px;font-smooth:always;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothingz:auto;display:block;left:0;line-height:1;position:absolute;top:0;width:100%;z-index:2147483638;--fade-transition-time:700ms;--slide-transition-time:400ms}.osano-cm-window--context_amp{height:100%}.osano-visually-hidden{height:1px;left:-10000px;margin:-1px;opacity:0;overflow:hidden;position:absolute;width:1px}.osano-cm-button{border-radius:.25em;border-style:solid;border-width:thin;cursor:pointer;flex:1 1 auto;font-size:1em;font-weight:700;line-height:1;margin:.125em;min-width:6em;padding:.5em .75em;transition-duration:.2s;transition-property:background-color;transition-timing-function:ease-out}.osano-cm-button--type_icon{border-radius:50%;height:1em;line-height:0;min-width:1em;width:1em}.osano-cm-button:focus,.osano-cm-button:hover{outline:none}.osano-cm-close{border-radius:50%;border-style:solid;border-width:2px;box-sizing:content-box;cursor:pointer;height:20px;margin:.5em;min-height:20px;min-width:20px;order:0;outline:none;overflow:hidden;padding:0;width:20px;stroke-width:1px;justify-content:center;line-height:normal;text-decoration:none;transform:rotate(0deg);transition-duration:.2s;transition-property:transform,color,background-color,stroke,stroke-width;transition-timing-function:ease-out;z-index:2}.osano-cm-close:focus,.osano-cm-close:hover{transform:rotate(90deg);stroke-width:2px}.ccpa-opt-out-icon{display:flex;flex:1 1 auto}.ccpa-opt-out-icon svg{max-width:40px}.osano-cm-link{cursor:pointer;text-decoration:underline;transition-duration:.2s;transition-property:color;transition-timing-function:ease-out}.osano-cm-link:active,.osano-cm-link:hover{outline:none}.osano-cm-link:focus{font-weight:700;outline:none}.osano-cm-link--type_feature,.osano-cm-link--type_purpose,.osano-cm-link--type_specialFeature,.osano-cm-link--type_specialPurpose{cursor:help;display:block;-webkit-text-decoration:dashed;text-decoration:dashed}.osano-cm-link--type_denyAll{display:block;text-align:right}[dir=rtl] .osano-cm-link--type_denyAll{text-align:left}.osano-cm-link--type_vendor{display:block}.osano-cm-vendor-link{font-size:.75em}.osano-cm-list-item{margin:0}.osano-cm-list-item--type_term{border-top-style:solid;border-top-width:1px;font-size:.875rem;font-weight:400;margin-bottom:.25em;margin-top:.5em;padding:.5em .75rem 0;position:relative;top:-1px}.osano-cm-list-item--type_description{font-size:.75rem;font-weight:lighter;padding:0 .75rem}.osano-cm-list{list-style-position:outside;list-style-type:none;margin:0;padding:0}.osano-cm-list__list-item{text-indent:0}.osano-cm-list--type_description{margin:0 -1em}.osano-cm-list:first-of-type .osano-cm-list__list-item:first-of-type{border-top-width:0;margin-top:0;padding-top:0}.osano-cm-toggle{align-items:center;display:flex;flex-direction:row-reverse;justify-content:flex-start;margin:.25em 0;pointer-events:auto;position:relative}.osano-cm-toggle__label{margin:0 .5em 0 0}[dir=rtl] .osano-cm-toggle__label{margin:0 0 0 .5em}.osano-cm-toggle__switch{border-radius:14px;border-style:solid;border-width:2px;box-sizing:content-box;color:transparent;display:block;flex-shrink:0;height:18px;line-height:0;margin:0;position:relative;text-indent:-9999px;transition-duration:.2s;transition-property:background-color;transition-timing-function:ease-out;width:40px}.osano-cm-toggle__switch:hover{cursor:pointer}.osano-cm-toggle__switch:after{border-radius:9px;border-width:0;height:18px;left:0;top:0;width:18px}.osano-cm-toggle__switch:before{border-radius:16px;border-width:2px;bottom:-6px;box-sizing:border-box;left:-6px;right:-6px;top:-6px}.osano-cm-toggle__switch:after,.osano-cm-toggle__switch:before{border-style:solid;content:"";margin:0;position:absolute;transform:translateX(0);transition-duration:.3s;transition-property:transform,left,border-color;transition-timing-function:ease-out}.osano-cm-toggle__switch:after:active,.osano-cm-toggle__switch:before:active{transition-duration:.1s}.osano-cm-toggle__switch:after:active{width:26px}.osano-cm-toggle__switch:before:active{width:34px}[dir=rtl] .osano-cm-toggle__switch:after{left:100%;transform:translateX(-100%)}.osano-cm-toggle__input{height:1px;left:-10000px;margin:-1px;opacity:0;overflow:hidden;position:absolute;width:1px}[dir=rtl] .osano-cm-toggle__input{left:0;right:-10000px}.osano-cm-toggle__input:disabled{cursor:default}.osano-cm-toggle--type_checkbox .osano-cm-toggle__switch{border-radius:4px;border-style:solid;border-width:1px;height:22px;width:22px}.osano-cm-toggle--type_checkbox .osano-cm-toggle__switch:after{background-color:transparent!important;border-bottom-width:2px;border-left-width:2px;border-radius:0;content:none;height:6px;left:3px;top:3px;transform:rotate(-45deg);transition-property:color;transition-timing-function:ease-out;width:12px}.osano-cm-toggle--type_opt-out .osano-cm-toggle__switch{border-radius:4px;border-style:solid;border-width:1px;height:22px;width:22px}.osano-cm-toggle--type_opt-out .osano-cm-toggle__switch:after,.osano-cm-toggle--type_opt-out .osano-cm-toggle__switch:before{background-color:transparent!important;border-bottom-width:1px;border-radius:0;border-top-width:1px;content:none;height:0;left:-3px;top:7px;transition-property:color;transition-timing-function:ease-out;width:12px}.osano-cm-toggle--type_opt-out .osano-cm-toggle__switch:after{transform:translate(50%,50%) rotate(-45deg)}.osano-cm-toggle--type_opt-out .osano-cm-toggle__switch:before{transform:translate(50%,50%) rotate(45deg)}.osano-cm-toggle__input:checked+.osano-cm-toggle__switch:after{left:100%;transform:translateX(-100%)}[dir=rtl] .osano-cm-toggle__input:checked+.osano-cm-toggle__switch:after{left:0;transform:translateX(0)}.osano-cm-toggle__input:disabled+.osano-cm-toggle__switch{cursor:default}.osano-cm-toggle--type_checkbox .osano-cm-toggle__input:checked+.osano-cm-toggle__switch:after{content:"";left:3px;top:3px;transform:rotate(-45deg)}.osano-cm-toggle--type_opt-out .osano-cm-toggle__input:checked+.osano-cm-toggle__switch:after,.osano-cm-toggle--type_opt-out .osano-cm-toggle__input:checked+.osano-cm-toggle__switch:before{content:"";left:-1px;top:9px}.osano-cm-toggle--type_opt-out .osano-cm-toggle__input:checked+.osano-cm-toggle__switch:after{transform:translate(50%,50%) rotate(-45deg)}.osano-cm-toggle--type_opt-out .osano-cm-toggle__input:checked+.osano-cm-toggle__switch:before{transform:translate(50%,50%) rotate(45deg)}.osano-cm-toggle--type_checkbox .osano-cm-toggle__input:disabled+.osano-cm-toggle__switch,.osano-cm-toggle--type_opt-out .osano-cm-toggle__input:disabled+.osano-cm-toggle__switch{opacity:.3}.osano-cm-widget{background:none;border:none;bottom:12px;cursor:pointer;height:40px;opacity:.9;outline:none;padding:0;position:fixed;transition:transform .1s linear 0s,opacity .2s linear 0ms,visibility 0ms linear 0ms;visibility:visible;width:40px;z-index:2147483636}.osano-cm-widget--position_right{right:12px}.osano-cm-widget--position_left{left:12px}.osano-cm-widget:focus{outline:solid;outline-offset:.2rem}.osano-cm-widget:focus,.osano-cm-widget:hover{opacity:1;transform:scale(1.1)}.osano-cm-widget--hidden{opacity:0;visibility:hidden}.osano-cm-widget--hidden:focus,.osano-cm-widget--hidden:hover{opacity:0;transform:scale(1)}.osano-cm-dialog{align-items:center;box-sizing:border-box;font-size:1em;line-height:1.25;max-height:100vh;overflow:auto;padding:1.5em;position:fixed;transition-delay:0ms,0ms;transition-duration:.7s,0ms;transition-property:opacity,visibility;visibility:visible;z-index:2147483637}.osano-cm-dialog--hidden{opacity:0;transition-delay:0ms,.7s;visibility:hidden}.osano-cm-dialog--type_bar{box-sizing:border-box;display:flex;flex-direction:column;left:0;right:0}.osano-cm-dialog--type_bar .osano-cm-button{flex:none;margin:.125em auto;width:80%}@media screen and (min-width:768px){.osano-cm-dialog--type_bar{flex-direction:row}.osano-cm-dialog--type_bar .osano-cm-button{flex:1 1 100%;margin:.25em .5em;width:auto}}.osano-cm-dialog--type_box{flex-direction:column;max-height:calc(100vh - 2em);max-width:20em;width:calc(100vw - 2em)}.osano-cm-dialog__close{position:absolute;right:0;top:0}.osano-cm-dialog__list{margin:.5em 0 0;padding:0}.osano-cm-dialog__list .osano-cm-item{display:flex;margin-top:0}.osano-cm-dialog__list .osano-cm-item:last-child{margin-bottom:0}.osano-cm-dialog__list .osano-cm-toggle{flex-direction:row}[dir=rtl] .osano-cm-dialog__list .osano-cm-toggle{flex-direction:row-reverse}.osano-cm-dialog__list .osano-cm-label{white-space:nowrap}[dir=ltr] .osano-cm-dialog__list .osano-cm-label{margin-left:.375em}[dir=rtl] .osano-cm-dialog__list .osano-cm-label{margin-right:.375em}.osano-cm-dialog__buttons{display:flex;flex-wrap:wrap}.osano-cm-dialog--type_bar .osano-cm-dialog__content{flex:5;margin-bottom:.25em;width:100%}.osano-cm-dialog--type_box .osano-cm-dialog__content{display:flex;flex-direction:column;flex-grow:.0001;transition:flex-grow 1s linear}.osano-cm-dialog--type_box .osano-cm-link-separator:before{content:"";padding:0}.osano-cm-dialog--type_box .osano-cm-content__link{margin-bottom:.5em}.osano-cm-dialog--type_bar .osano-cm-dialog__list{display:flex;flex-direction:column;flex-wrap:wrap;justify-content:flex-start;margin:.75em auto}@media screen and (min-width:376px){.osano-cm-dialog--type_bar .osano-cm-dialog__list{flex-direction:row}}@media screen and (min-width:768px){.osano-cm-dialog--type_bar .osano-cm-dialog__list{margin:.5em 0 0 auto}[dir=rtl] .osano-cm-dialog--type_bar .osano-cm-dialog__list{margin:.5em auto 0 0}}[dir=ltr] .osano-cm-dialog--type_bar .osano-cm-dialog__list .osano-cm-item{margin-right:.5em}[dir=rtl] .osano-cm-dialog--type_bar .osano-cm-dialog__list .osano-cm-item{margin-left:.5em}.osano-cm-dialog--type_bar .osano-cm-dialog__list .osano-cm-label{padding-top:0}.osano-cm-dialog--type_bar .osano-cm-dialog__buttons{flex:1;justify-content:flex-end;margin:0;width:100%}@media screen and (min-width:768px){.osano-cm-dialog--type_bar .osano-cm-dialog__buttons{margin:0 0 0 .5em;max-width:30vw;min-width:16em;position:sticky;top:0;width:auto}[dir=rtl] .osano-cm-dialog--type_bar .osano-cm-dialog__buttons{margin:0 .5em 0 0}}.osano-cm-dialog--type_box .osano-cm-dialog__buttons{margin:.5em 0 0}.osano-cm-dialog--type_bar.osano-cm-dialog--position_top{top:0}.osano-cm-dialog--type_bar.osano-cm-dialog--position_bottom{bottom:0}.osano-cm-dialog--type_box.osano-cm-dialog--position_top-left{left:1em;top:1em}.osano-cm-dialog--type_box.osano-cm-dialog--position_top-right{right:1em;top:1em}.osano-cm-dialog--type_box.osano-cm-dialog--position_bottom-left{bottom:1em;left:1em}.osano-cm-dialog--type_box.osano-cm-dialog--position_bottom-right{bottom:1em;right:1em}.osano-cm-dialog--type_box.osano-cm-dialog--position_center{left:50%;top:50%;transform:translate(-50%,-50%)}.osano-cm-dialog--type_box.osano-cm-dialog--wide{max-width:50em}@media screen and (max-height:800px)and (max-width:1200px){.osano-cm-dialog--type_box.osano-cm-dialog--wide{max-width:calc(100vw - 4em)}}.osano-cm-dialog--type_box.osano-cm-dialog--wide .osano-cm-dialog__list{display:flex;flex-wrap:wrap}.osano-cm-dialog--context_amp{height:100%;position:relative}.osano-cm-content__message{margin-bottom:1em;white-space:pre-line;word-break:break-word}.osano-cm-drawer-links{margin:.5em 0 0}.osano-cm-drawer-links__link{display:block}.osano-cm-storage-policy{display:inline-block}.osano-cm-usage-list{margin:0 0 .5em}.osano-cm-usage-list__list{list-style-position:inside;list-style-type:disc}:export{fadeTransitionTime:.7s;slideTransitionTime:.4s}.osano-cm-info-dialog{height:100vh;left:0;position:fixed;top:0;transition-delay:0ms,0ms;transition-duration:.2s,0ms;transition-property:opacity,visibility;visibility:visible;width:100vw;z-index:2147483638}.osano-cm-info-dialog--hidden{opacity:0;transition-delay:0ms,.2s;visibility:hidden}.osano-cm-header{margin:0 0 -1em;padding:1em 0;position:sticky;top:0;z-index:1}.osano-cm-info{animation:delay-overflow .4s;bottom:0;box-shadow:0 0 2px 2px #ccc;box-sizing:border-box;max-width:20em;overflow:visible visible;position:fixed;top:0;transition-duration:.4s;transition-property:transform;width:100%}.osano-cm-info--position_left{left:0;transform:translate(-100%)}.osano-cm-info--position_right{right:0;transform:translate(100%)}.osano-cm-info--open{animation:none;overflow:hidden auto;transform:translate(0)}.osano-cm-info--do_not_sell{animation:none;height:-moz-fit-content;height:fit-content;left:50%;max-height:100vh;position:fixed;right:auto;top:50%;transform:translate(-50%,-50%);transition:none}.osano-cm-info--do_not_sell .osano-cm-close{order:-1}.osano-cm-info--do_not_sell .osano-cm-header{box-sizing:content-box;display:block;flex:none}.osano-cm-info-views{align-items:flex-start;display:flex;flex-direction:row;flex-wrap:nowrap;height:100%;transition-duration:.4s;transition-property:transform;width:100%}[dir=rtl] .osano-cm-info-views{flex-direction:row-reverse}.osano-cm-info-views__view{box-sizing:border-box;flex-shrink:0;width:100%}.osano-cm-info-views--position_0>:not(:first-of-type){max-height:100%;overflow:hidden}.osano-cm-info-views--position_1{transform:translateX(-100%)}.osano-cm-info-views--position_1>:not(:nth-of-type(2)){max-height:100%;overflow:hidden}.osano-cm-info-views--position_2{transform:translateX(-200%)}.osano-cm-info-views--position_2>:not(:nth-of-type(3)){max-height:100%;overflow:hidden}.osano-cm-info--do_not_sell .osano-cm-info-views{height:-moz-fit-content;height:fit-content}.osano-cm-view{height:0;padding:0 .75em 1em;transition-delay:.4s;transition-duration:0ms;transition-property:height,visibility;visibility:hidden;width:100%}.osano-cm-view__button{font-size:.875em;margin:1em 0 0;width:100%}.osano-cm-view--active{height:auto;transition-delay:0ms;visibility:visible}.osano-cm-description{font-size:.75em;font-weight:300;line-height:1.375;margin:1em 0 0;white-space:pre-line}.osano-cm-description:first-child{margin:0}.osano-cm-description:last-of-type{margin-bottom:1em}.osano-cm-drawer-toggle .osano-cm-label{font-size:.875em;line-height:1.375em;margin:0 auto 0 0}[dir=rtl] .osano-cm-drawer-toggle .osano-cm-label{margin:0 0 0 auto}.osano-cm-info-dialog-header{align-items:center;display:flex;flex-direction:row-reverse;left:auto;min-height:3.25em;position:sticky;top:0;width:100%;z-index:1}[dir=rtl] .osano-cm-info-dialog-header{flex-direction:row}.osano-cm-info-dialog-header__header{align-items:center;display:flex;flex:1 1 auto;font-size:1em;justify-content:flex-start;margin:0;order:1;padding:1em .75em}.osano-cm-info-dialog-header__description{font-size:.75em;line-height:1.375}.osano-cm-back,.osano-cm-info-dialog-header__close{position:relative}.osano-cm-back{flex:0 1 auto;margin:0 0 0 .5em;min-width:0;order:2;width:auto;z-index:2}[dir=rtl] .osano-cm-back{margin:0 .5em 0 0}.osano-cm-powered-by{align-items:center;display:flex;flex-direction:column;font-weight:700;justify-content:center;margin:1em 0}.osano-cm-powered-by__link{font-size:.625em;outline:none;text-decoration:none}.osano-cm-powered-by__link:focus,.osano-cm-powered-by__link:hover{text-decoration:underline}@keyframes delay-overflow{0%{overflow:hidden auto}}.osano-cm-drawer-iab-button-container{display:flex;gap:.5em;justify-content:center;margin-bottom:2em}.osano-cm-illustrations__list>.osano-cm-list-item--type_description{padding:.2rem 1rem}.osano-cm-drawer-item.osano-cm-description__list li{padding-top:.75em}.osano-cm-tcf-purpose--label{border-bottom:1px solid rgba(0,0,0,.1);display:block;margin-bottom:.5em;padding:.25em 0 .5em}.osano-cm-link.osano-cm-link--type_purpose{font-weight:400}.osano-cm-tcf-purpose--label input{float:right;margin-right:.5em}.osano-cm-expansion-panel{border-bottom:1px solid rgba(0,0,0,.1);display:block;font-size:.75em;margin:0 -1.5em 1em;padding:1.5em 1.5em 0}.osano-cm-expansion-panel--expanded{border-bottom:none}.osano-cm-expansion-panel--empty,.osano-cm-expansion-panel--empty:not([open]){border-bottom:1px solid rgba(0,0,0,.1);padding-bottom:0}.osano-cm-expansion-panel__body{background-color:rgba(0,0,0,.1);line-height:1.25;list-style:none;margin:0 -1.5em;max-height:0;overflow:hidden;padding:0 1.5em;transition-delay:0ms,0ms,0ms,.3s;transition-duration:.3s,.3s,.3s,0s;transition-property:max-height,padding-top,padding-bottom,visibility;transition-timing-function:ease-out;visibility:hidden}.osano-cm-expansion-panel__toggle{cursor:pointer;display:block;line-height:1.25;margin:0 auto 1em 0;outline:none;position:relative}.osano-cm-expansion-panel__toggle:active,.osano-cm-expansion-panel__toggle:focus,.osano-cm-expansion-panel__toggle:hover{outline:none}[dir=rtl] .osano-cm-expansion-panel__toggle{margin:0 0 1em auto}.osano-cm-expansion-panel--expanded .osano-cm-expansion-panel__body{max-height:none;padding:1.25em 1.5em 1em;transition-delay:0ms,0ms,0ms,0ms;visibility:visible}.osano-cm-disclosure-item__title{border:0;clear:both;display:block;flex:0 1 30%;font-size:1em;font-weight:700;line-height:1.375;margin:0 0 .5em;padding:0}.osano-cm-disclosure-item__description{flex:0 1 70%;font-size:1em;line-height:1.375;margin:0 0 .5em;overflow:hidden;padding:0;text-overflow:ellipsis;white-space:pre-line}.osano-cm-disclosure{border-bottom:none;display:block;font-size:.75em;margin:0 -1.5em 1em;padding:1.5em 1.5em 0}.osano-cm-disclosure--collapse{border-bottom:1px solid rgba(0,0,0,.1);padding-bottom:1em}.osano-cm-disclosure--empty,.osano-cm-disclosure--empty:not([open]){border-bottom:1px solid rgba(0,0,0,.1);padding-bottom:0}.osano-cm-disclosure__list{background-color:rgba(0,0,0,.1);line-height:1.25;list-style:none;margin:0 -1.5em;padding:1.25em 1.5em 1em}.osano-cm-disclosure__list:empty{border:none;padding:0 1.5em}.osano-cm-disclosure__list:first-of-type{margin-top:1em;padding:1.25em 1.5em 1em}.osano-cm-disclosure__list:first-of-type:empty{padding:1.75em 1.5em .75em}.osano-cm-disclosure__list:not(:first-of-type):not(:empty){border-top:1px solid rgba(0,0,0,.1)}.osano-cm-disclosure__list:empty+.osano-cm-disclosure__list:not(:empty){border:none;padding:0 1.5em}.osano-cm-disclosure__list:not(:empty)~.osano-cm-disclosure__list:empty+.osano-cm-disclosure__list:not(:empty){border-top:1px solid rgba(0,0,0,.1)}.osano-cm-disclosure__list>.osano-cm-list-item{line-height:1.25}.osano-cm-disclosure__list>.osano-cm-list-item:not(:first-of-type){border-top:1px solid rgba(0,0,0,.1);margin:1em -1.25em 0;padding:1em 1.25em 0}.osano-cm-disclosure__toggle{cursor:pointer;display:block;font-weight:700;line-height:1.25;margin:0 auto 0 0;outline:none;position:relative}.osano-cm-disclosure__toggle:focus,.osano-cm-disclosure__toggle:hover{text-decoration:underline}[dir=rtl] .osano-cm-disclosure__toggle{margin:0 0 0 auto}.osano-cm-disclosure--loading .osano-cm-disclosure__list{height:0;line-height:0;max-height:0}.osano-cm-disclosure--loading .osano-cm-disclosure__list>*{display:none}.osano-cm-disclosure--loading .osano-cm-disclosure__list:after{animation-duration:1s;animation-iteration-count:infinite;animation-name:osano-load-scale;animation-timing-function:ease-in-out;border-radius:100%;content:"";display:block;height:1em;position:relative;top:-.125em;transform:translateY(-50%);width:1em}.osano-cm-disclosure--collapse .osano-cm-disclosure__list{display:none}.osano-cm-disclosure--collapse .osano-cm-disclosure__list:after{content:none}.osano-cm-disclosure-item{display:flex;flex-wrap:wrap;margin:0}.osano-cm-disclosure-item__title:last-of-type{margin-bottom:0}.osano-cm-disclosure-item__description:hover{word-break:break-word}.osano-cm-disclosure-item__description:last-of-type{margin-bottom:0}@keyframes osano-load-scale{0%{transform:translateY(-50%) scale(0)}to{opacity:0;transform:translateY(-50%) scale(1)}}'} .osano-cm-window { direction: ${i.hT.isRTL?"rtl":"ltr"}; text-align: ${i.hT.isRTL?"right":"left"}; } .osano-cm-dialog { background: ${c}; color: ${a}; } .osano-cm-dialog__close { color: ${a}; stroke: ${a}; } .osano-cm-dialog__close:focus { background-color: ${a}; border-color: ${a}; stroke: ${c}; } .osano-cm-dialog__close:hover { stroke: ${Ai(a)}; } .osano-cm-dialog__close:focus:hover { stroke: ${Ai(c)}; } .osano-cm-info-dialog { background: ${p}; } .osano-cm-header, .osano-cm-info-dialog-header { background: ${l}; background: linear-gradient( 180deg, ${l} 2.5em, ${v} 100% ); } .osano-cm-info { background: ${l}; color: ${u}; } .osano-cm-link-separator::before { content: '|'; padding: 0 0.5em; } .osano-cm-close { display: flex; background-color: transparent; border-color: transparent; } .osano-cm-info-dialog-header__close { color: ${u}; stroke: ${u}; } .osano-cm-info-dialog-header__close:focus { background-color: ${u}; border-color: ${u}; stroke: ${l}; } .osano-cm-info-dialog-header__close:hover { stroke: ${Ai(u)}; } .osano-cm-info-dialog-header__close:focus:hover { stroke: ${Ai(l)}; } .osano-cm-disclosure__list:first-of-type::after { background-color: ${d}; } .osano-cm-disclosure__toggle, .osano-cm-expansion-panel__toggle { color: ${d}; } .osano-cm-disclosure__toggle:hover, .osano-cm-disclosure__toggle:active, .osano-cm-expansion-panel__toggle:hover, .osano-cm-expansion-panel__toggle:active { color: ${d}; } .osano-cm-disclosure__toggle:focus, .osano-cm-expansion-panel__toggle:focus { color: ${Ai(d)}; } .osano-cm-button { background-color: ${n}; border-color: ${r}; color: ${r}; } .osano-cm-button--type_deny { background-color: ${o}; border-color: ${s}; color: ${s}; } .osano-cm-button:focus, .osano-cm-button:hover { background-color: ${Ai(n,.08)}; } .osano-cm-button--type_deny:focus, .osano-cm-button--type_deny:hover { background-color: ${Ai(o,.08)}; } .osano-cm-link { color: ${d}; } .osano-cm-link:hover, .osano-cm-link:active { color: ${d}; } .osano-cm-link:focus { color: ${Ai(d)}; } .osano-cm-toggle__switch { background-color: ${f}; } .osano-cm-toggle__switch::after { background-color: ${g}; border-color: ${g}; } .osano-cm-toggle__switch::before { border-color: transparent; } .osano-cm-toggle__input:checked + .osano-cm-toggle__switch { background-color: ${m}; border-color: ${m}; } .osano-cm-toggle__input:checked + .osano-cm-toggle__switch::after { background-color: ${h}; border-color: ${h}; } .osano-cm-toggle__input:focus + .osano-cm-toggle__switch, .osano-cm-toggle__input:hover + .osano-cm-toggle__switch { background-color: ${Ai(f)}; border-color: ${Ai(f)}; } .osano-cm-toggle__input:focus + .osano-cm-toggle__switch::before { border-color: ${Ai(f)}; } .osano-cm-toggle__input:checked:focus + .osano-cm-toggle__switch, .osano-cm-toggle__input:checked:hover + .osano-cm-toggle__switch { background-color: ${Ai(m)}; border-color: ${Ai(m)}; } .osano-cm-toggle__input:checked:focus + .osano-cm-toggle__switch::before { border-color: ${Ai(m)}; } .osano-cm-toggle__input:disabled + .osano-cm-toggle__switch, .osano-cm-toggle__input:disabled:focus + .osano-cm-toggle__switch, .osano-cm-toggle__input:disabled:hover + .osano-cm-toggle__switch { background-color: ${Ai(f,.25)}; border-color: ${Ai(f,.25)}; } .osano-cm-toggle__input:disabled + .osano-cm-toggle__switch::after, .osano-cm-toggle__input:disabled:focus + .osano-cm-toggle__switch::after, .osano-cm-toggle__input:disabled:hover + .osano-cm-toggle__switch::after { background-color: ${Ai(g,.25)}; border-color: ${Ai(g,.25)}; } .osano-cm-toggle__input:disabled + .osano-cm-toggle__switch::before, .osano-cm-toggle__input:disabled:focus + .osano-cm-toggle__switch::before, .osano-cm-toggle__input:disabled:hover + .osano-cm-toggle__switch::before { border-color: transparent; } .osano-cm-toggle__input:disabled:checked + .osano-cm-toggle__switch, .osano-cm-toggle__input:disabled:checked:focus + .osano-cm-toggle__switch, .osano-cm-toggle__input:disabled:checked:hover + .osano-cm-toggle__switch { background-color: ${Ai(m,.25)}; border-color: ${Ai(m,.25)}; } .osano-cm-toggle__input:disabled:checked + .osano-cm-toggle__switch::after, .osano-cm-toggle__input:disabled:checked:focus + .osano-cm-toggle__switch::after, .osano-cm-toggle__input:disabled:checked:hover + .osano-cm-toggle__switch::after { background-color: ${Ai(h,.25)}; border-color: ${Ai(h,.25)}; } .osano-cm-toggle__input:disabled:checked + .osano-cm-toggle__switch::before, .osano-cm-toggle__input:disabled:checked:focus + .osano-cm-toggle__switch::before, .osano-cm-toggle__input:disabled:checked:hover + .osano-cm-toggle__switch::before { border-color: transparent; } .osano-cm-widget__outline { fill: ${y}; stroke: ${O}; } .osano-cm-widget__dot { fill: ${b}; } .osano-cm-tcf-purpose--label input { accent-color: ${m}; } `}(r),n),G(e(),t),this}}},8375:(e,t,n)=>{"use strict"},6796:(e,t,n)=>{"use strict";n.d(t,{oR:()=>o});n(5036),n(8375),n(8734);var r=n(4525);const o=function(e,t){return(0,r.Jt)(e,"config.jurisdiction","").substring(0,t.length)===t}},5785:(e,t,n)=>{"use strict";n.d(t,{$V:()=>f,Fk:()=>a,Qi:()=>l,Zx:()=>p,b6:()=>i,c:()=>d,gG:()=>u,ov:()=>c,sL:()=>s});var r=n(2775);const o="OsanoCMP_IAB-TCF",i=(0,r.lQ)(`${o}/SET_PURPOSE_CONSENT`,(0,r.YW)("purposeId","acceptOrDeny")),s=(0,r.lQ)(`${o}/SET_PURPOSE_LI_CONSENT`,(0,r.YW)("purposeId","acceptOrDeny")),c=(0,r.lQ)(`${o}/SET_ALL_PURPOSE_CHOICES`,(0,r.YW)("acceptOrDeny")),a=(0,r.lQ)(`${o}/SET_VENDOR_CONSENT`,(0,r.YW)("vendorId","acceptOrDeny")),l=(0,r.lQ)(`${o}/SET_ALL_VENDOR_CONSENT`,(0,r.YW)("acceptOrDeny")),u=(0,r.lQ)(`${o}/SET_VENDOR_LI_CONSENT`,(0,r.YW)("vendorId","acceptOrDeny")),p=(0,r.lQ)(`${o}/SET_ALL_VENDOR_LI_CONSENT`,(0,r.YW)("acceptOrDeny")),d=(0,r.lQ)(`${o}/SET_SPECIAL_FEATURE_CONSENT`,(0,r.YW)("specialFeatureId","acceptOrDeny")),f=(0,r.lQ)(`${o}/SET_ALL_SPECIAL_FEATURE_CONSENT`,(0,r.YW)("acceptOrDeny"));(0,r.lQ)(`${o}/SET_GDPR_APPLIES`,r.Mv),(0,r.lQ)(`${o}/UPDATE_PURPOSE_LIST`,(0,r.YW)("purposeList","language")),(0,r.lQ)(`${o}/UPDATE_VENDOR_LIST_BEGIN`,r.m7),(0,r.lQ)(`${o}/UPDATE_VENDOR_LIST_SUCCESS`,r.Mv),(0,r.lQ)(`${o}/UPDATE_VENDOR_LIST_FAILURE`,r.Mv)},3803:(e,t,n)=>{"use strict";n.d(t,{A5:()=>u,C1:()=>c,L_:()=>l,Ll:()=>a,pO:()=>p,ue:()=>d});var r=n(1286),o=n(8734);function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const c=[2,7,8,9,10,11],a={[o.rO]:[7,8,9,10],[o.O3]:[1],[o.TG]:[2,3,4],[o.$w]:[5,6,11]},l=[20,3,44],u=[1,2],p={[o.rO]:20,[o.TG]:3,[o.$w]:44},d={iab:{tcf:{v2:{purposes:{consents:[1,2,3,4,5,6,7,8,9,10,11].reduce((function(e,t){return s(s({},e),{},{[`${t}`]:!1})}),{}),legitimateInterests:c.reduce((function(e,t){return s(s({},e),{},{[`${t}`]:!0})}),{})},specialFeatureOptins:{},stacks:[],vendorListError:null,vendorListLoading:!1,vendorListLoaded:!1,vendorListVersion:0,vendorOptOuts:{consents:{},legitimateInterests:{}},vendors:{}}}}}},2240:(e,t,n)=>{"use strict";n.d(t,{CT:()=>A,GQ:()=>w,O$:()=>_,Sh:()=>S,Tp:()=>h,YQ:()=>m,ZZ:()=>x,g4:()=>$,jP:()=>d,kb:()=>p,lF:()=>P,lT:()=>b,r6:()=>k,uY:()=>j});var r=n(3803),o=n(4274),i=(n(8734),n(4525)),s=n(5036),c=n(6796);const a=function(e){return(0,i.Jt)(e,"iab.tcf",{})},l=function(e,t=2){return(0,i.Jt)(a(e),[`v${parseInt(t,10)}`],{})},u=function(e,{gppSection:t,tcString:n}){return(0,i.Jt)(e,["iab","consentStrings",t],n)},p=function(e,t){return(0,i.Jt)(l(e,t),"purposes.consents")||{}},d=function(e,t){return(0,i.Jt)(l(e,t),"purposes.legitimateInterests")||{}},f=function(e,t){return(0,i.Jt)(l(e,t),"savedConsent")},g=function(e,t){return(0,i.Jt)(f(e,t),"vendorOptOuts")},m=function(e,t){return(0,i.Jt)(l(e,t),"vendors")||{}},h=function(e,{apiVersion:t,vendorId:n}){return(0,i.Jt)(m(e,t),`${n}`)||{}},b=function(e,t){const n=m(e,t);return Object.values(n).filter((function(e){return!e.deletedDate})).map((function(e){return Number(e.id)}))},y=function(e,t){return(0,i.Jt)(l(e,t),"vendorOptOuts")||{}},O=function(e,t){return(0,i.Jt)(y(e,t),"consents")||{}},v=function(e,t){return(0,i.Jt)(y(e,t),"legitimateInterests")||{}},w=function(e,{type:t,id:n}){const r=m(e);return Object.values(r).filter((function(e){return(e[`${t}`]||[]).includes(Number(n))}))},$=function(e,t){const{purposes:n,legIntPurposes:r,specialPurposes:o}=h(e,{vendorId:t});return 0===(null==n?void 0:n.length)&&0===(null==r?void 0:r.length)&&(null==o?void 0:o.length)>0},_=function(e){return{iab:{v2:{p:{c:p(e,"2.0"),li:d(e,"2.0")},v:{c:O(e,"2.0"),li:v(e,"2.0")}},euconsent:u(e,{gppSection:"tcfeuv2"})}}},j=function(e,{apiVersion:t,specialFeatureId:n}){return!!function(e,t){return(0,i.Jt)(l(e,t),"specialFeatureOptins")||{}}(e,t)[`${n}`]},A=function(e,{apiVersion:t,vendorId:n}){return!!O(e,t)[`${n}`]},k=function(e,{apiVersion:t,vendorId:n}){return!!v(e,t)[`${n}`]},P=function(e){return(function(e){return!!(0,i.Jt)(a(e),"gdprApplies",o.GDPR_COUNTRIES.includes((0,s.gI)(e)))}(e)||(0,c.oR)(e,"ca"))&&!!(0,i.Jt)(a(e),"enabled",function(e){return!!(0,i.Jt)(e,"config.iabEnabled")}(e))},S=function(e,{apiVersion:t,useSaved:n}={}){if(n&&!f(e,t))return{};const r=m(e,t),o=n?function(e,t){return(0,i.Jt)(g(e,t),"consents")||{}}(e,t):{},s=n?function(e,t){return(0,i.Jt)(f(e,t),"purposes.consents")||{}}(e,t):p(e,t);return Object.entries(r).reduce((function(e,[t,{purposes:n=[],deletedDate:r}={}]){return r||o[`${t}`]||!n.some((function(e){return s[`${e}`]}))||(e[`${t}`]=!0),e}),{})},x=function(e,{apiVersion:t,useSaved:n}={}){const o=n?function(e,t){return(0,i.Jt)(g(e,t),"legitimateInterests")||{}}(e,t):{},s=n?function(e,t){return(0,i.Jt)(f(e,t),"purposes.legitimateInterests")}(e,t)||r.ue.iab.tcf.v2.purposes.legitimateInterests:d(e,t),c=m(e,t),a={};return Object.entries(c).forEach((function([e,{legIntPurposes:t=[],purposes:n=[],specialPurposes:r=[],deletedDate:i}={}]){if(!i&&!o[`${e}`]){(t&&t.some((function(e){return s[`${e}`]}))||0===n.length&&0===t.length&&r.length>0)&&(a[`${e}`]=!0)}})),a}},4814:(e,t,n)=>{"use strict";n.d(t,{LC:()=>u,ZS:()=>d,dG:()=>p});var r=n(8734),o=n(7479),i=n(2248),s=n(5023),c=n(8952),a=n(5267);const l=Object.getOwnPropertyDescriptor(Document.prototype,"createElement")||Object.getOwnPropertyDescriptor(HTMLDocument.prototype,"createElement"),u=Object.getOwnPropertyDescriptor(r.Yk.Document.prototype,"createElement")||Object.getOwnPropertyDescriptor(r.Yk.HTMLDocument.prototype,"createElement"),p=function(e){const t=e.tagName.toLowerCase(),n=(0,i.mf)(e),a=l.value.call(r.Bl,t),u=(0,s.KA)(n);Object.entries(u).forEach((function([t,n]){for(let[r,i]of n)for(let[n,s]of i)o.y.value.call(a,t,r,n,...s),c.y.value.call(e,t,r,n,...s);n.clear()}));try{Object.defineProperty(a,r.gY,{configurable:!1,enumerable:!1,get:()=>n})}catch(p){}try{Object.defineProperty(e,r.bW,{configurable:!1,enumerable:!1,get:()=>a})}catch(d){}return a},d={configurable:!0,enumerable:l.enumerable,writable:!0,value:function(...e){const t=l.value.apply(this,e);let[n]=e;switch(null===n&&(n="null"),n?n.toLowerCase():""){case"img":case"iframe":case"script":(0,a.Qj)(t)}return t}}},5041:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>p,LC:()=>l,yu:()=>a});var r=n(2248),o=n(4960),i=n(5267),s=n(8734),c=n(4045);const a=Object.getOwnPropertyDescriptor(Element.prototype,"getAttribute"),l=Object.getOwnPropertyDescriptor(s.Yk.Element.prototype,"getAttribute");function u(e){let t;switch(null==this?void 0:this.tagName){case"SCRIPT":t="src"===e||c.uH[`${e}`]?e:void 0;break;case"IFRAME":if(!(0,o.oR)())return a.value.call(this,e);t="src"===e||o.uH[`${e}`]?e:void 0}if(t)switch(this[`${e}`],e){case"src":{const e=(0,i.R$)(this);if(e)return String(e.originalValue);break}default:if(r.w9.has(this)&&void 0!==r.w9.get(this)[`${e}`])return String(r.w9.get(this)[`${e}`][0])}return a.value.call(this,e)}const p={configurable:a.configurable,enumerable:a.enumerable,writable:a.writable,value(e){return u.call((0,r.fb)(this),e)}}},2981:(e,t,n)=>{"use strict";n.d(t,{L:()=>o});var r=n(8734);Object.getOwnPropertyDescriptor(Element.prototype,"hasAttribute");const o=Object.getOwnPropertyDescriptor(r.Yk.Element.prototype,"hasAttribute")},2425:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>u,LC:()=>a});var r=n(4960),o=n(2248),i=n(8734),s=n(4045);const c=Object.getOwnPropertyDescriptor(Element.prototype,"removeAttribute"),a=Object.getOwnPropertyDescriptor(i.Yk.Element.prototype,"removeAttribute");function l(e){switch(null==this?void 0:this.tagName){case"SCRIPT":switch(e){case"async":case"defer":this[`${e}`]=!1;break;case"src":this[`${e}`]="";break;default:s.uH[`${e}`]&&(this[`${e}`]="")}break;case"IFRAME":if(!(0,r.oR)())break;if("src"===e)this[`${e}`]="";else r.uH[`${e}`]&&(this[`${e}`]="")}return c.value.call(this,e)}const u={configurable:c.configurable,enumerable:c.enumerable,writable:c.writable,value(e){const t=(0,o.fb)(this);return t!==this&&l.call(t,e),l.call(this,e)}}},5677:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>u,LC:()=>a,yu:()=>c});var r=n(4960),o=n(2248),i=n(8734),s=n(4045);const c=Object.getOwnPropertyDescriptor(Element.prototype,"setAttribute"),a=Object.getOwnPropertyDescriptor(i.Yk.Element.prototype,"setAttribute");function l(e,t){switch(null==this?void 0:this.tagName){case"SCRIPT":switch(e){case"async":case"defer":return this[`${e}`]=!0,c.value.call(this,e,t);case"src":return void(this[`${e}`]=t);case"data-osano":case"osano":throw new Error("All inline classifications must be provided in the markup");default:return s.uH[`${e}`]?void(this[`${e}`]=t):c.value.call(this,e,t)}case"IFRAME":if(!(0,r.oR)())break;switch(e){case"src":return void(this.src=t);case"data-osano":case"osano":throw new Error("All inline classifications must be provided in the markup");default:return r.uH[`${e}`]?void(this[`${e}`]=t):c.value.call(this,e,t)}}return c.value.call(this,e,t)}const u={configurable:c.configurable,enumerable:c.enumerable,writable:c.writable,value(e,t){const n=(0,o.fb)(this);return n!==this&&l.call(n,e,t),l.call(this,e,t)}}},7479:(e,t,n)=>{"use strict";n.d(t,{y:()=>r});const r=Object.hasOwnProperty.call(Node.prototype,"addEventListener")?Object.getOwnPropertyDescriptor(Node.prototype,"addEventListener"):Object.getOwnPropertyDescriptor(EventTarget.prototype,"addEventListener")},5023:(e,t,n)=>{"use strict";n.d(t,{KA:()=>f,F6:()=>y});var r=n(8734),o=n(5267),i=n(7479);const s=Object.hasOwnProperty.call(Node.prototype,"dispatchEvent")?Object.getOwnPropertyDescriptor(Node.prototype,"dispatchEvent"):Object.getOwnPropertyDescriptor(EventTarget.prototype,"dispatchEvent");var c=n(5036),a=n(4960),l=n(4045),u=n(8952),p=n(2248);const d=new WeakMap;function f(e){const t=d.has(e)?d.get(e):{};return d.set(e,t),t}function g(e,t){const n=f(e);return n[`${t}`]=n[`${t}`]||new Map}function m(e,...t){const[n]=t;if((0,l.Tv)(this)&&!this[r.gY]){const[,e,o=!1,...c]=t,a=g(this,n),{capture:l=o}=o||{},u=a.has(e)&&a.get(e)||new Map;if(u.set(l,c),a.set(e,u),this[r.bW]){try{i.y.value.call(this[r.bW],...t)}catch(s){}return}}if("load"===n&&(0,a.Tv)(this)){const t=g(this,n);if(!t.has("load")){const r=function(t){const n=e.getState(),r=(0,o.Gf)(this,n);if(!((0,c.OB)(n,r)||(0,o.tu)(r)))return(0,p.dG)(t)};i.y.value.call(this,n,r.bind(this),!0),t.set("load",r)}}return i.y.value.call(this,...t)}function h(e,...t){if((0,l.Tv)(this)&&!this[r.gY]){const[e,o,i=!1]=t,s=g(this,e);if(s.has(o)){const{capture:e=i}=i||{},t=s.get(o);t&&t.delete(e)}if(this[r.bW])try{u.y.value.call(this[r.bW],...t)}catch(n){}}return u.y.value.call(this,...t)}function b(e,...t){if((0,l.Tv)(this)&&this[r.bW])try{s.value.apply(this[r.bW],t)}catch(n){}return s.value.apply(this,t)}const y=function(e,t,n){const r={configurable:i.y.configurable,enumerable:i.y.enumerable,value:function(...e){return m.call(this,n,...e)},writable:i.y.writable},o={configurable:u.y.configurable,enumerable:u.y.enumerable,value:function(...e){return h.call(this,n,...e)},writable:u.y.writable},c={configurable:s.configurable,enumerable:s.enumerable,value:function(...e){return b.call(this,n,...e)},writable:s.writable};try{const n=Object.hasOwnProperty.call(t.prototype,"addEventListener")?t.prototype:e.prototype;Object.defineProperties(n,{addEventListener:r,dispatchEvent:c,removeEventListener:o})}catch(a){0}}},8952:(e,t,n)=>{"use strict";n.d(t,{y:()=>r});const r=Object.hasOwnProperty.call(Node.prototype,"removeEventListener")?Object.getOwnPropertyDescriptor(Node.prototype,"removeEventListener"):Object.getOwnPropertyDescriptor(EventTarget.prototype,"removeEventListener")},6591:(e,t,n)=>{"use strict";n.d(t,{m:()=>o,y:()=>r});const r=Object.getOwnPropertyDescriptor(HTMLElement.prototype,"onerror"),o={defaultValue:null,descriptor:r}},8865:(e,t,n)=>{"use strict";n.d(t,{m:()=>o,y:()=>r});const r=Object.getOwnPropertyDescriptor(HTMLElement.prototype,"onload"),o={defaultValue:null,descriptor:r}},4960:(e,t,n)=>{"use strict";n.d(t,{cw:()=>w,om:()=>$,uH:()=>v,RA:()=>j,oR:()=>k,Tv:()=>_,F6:()=>A});var r=n(1286),o=n(2248),i=n(5267),s=n(8437),c=n(1184),a=n.n(c),l=(n(5041),n(5036));const u=Object.getOwnPropertyDescriptor(HTMLIFrameElement.prototype,"src");function p(e){const t=u.get.call(this);return e!==t&&o.u1.call(this,e)!==t&&u.set.call(this,e),e}const d=function(e,t){return function(n,r){const{node:c,originalValue:a}=e;if(a!==t&&o.u1.call(c,a)!==o.u1.call(c,t))return e;return(0,l.OB)(r(),e)||(0,i.tu)(e)?(p.call(c,t),n(s.Ay.allowIFrame(e))):n(s.Ay.blockIFrame(e)),e}};function f(e){return{configurable:u.configurable,enumerable:u.enumerable,get(){const t=(0,i.Gf)(this,e.getState());return o.qi.call(this,t.src)},set(t){return function(e,t){return function(n,r){const o=(0,i.Gf)({node:e,src:t,ignore:!1},r()),{async:s}=o;return s?s.promise.then((function(){return d(o,t)(n,r)})):a().resolve(d(o,t)(n,r))}}(this,t)(e.dispatch,e.getState),t}}}var g=n(8734);const m={defaultValue:"0",descriptor:Object.getOwnPropertyDescriptor(HTMLIFrameElement.prototype,"height")};var h=n(6591),b=n(8865);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}let O=!1;const v={height:m,onerror:h.m,onload:b.m},w=function(e){if(!O)return;const{node:t,src:n}=e;t[g.UM]=!0,(0,o.fq)(t,v),n&&p.call(t,n)},$=function(e){if(!O)return;const{node:t,ignore:n}=e;if(n)return w(e);t&&(0,o.js)(t,v),p.call(t,"")},_=function(e){return e&&1===e.nodeType&&"IFRAME"===e.tagName},j=function(e){return(null==e?void 0:e.getElementsByTagName)&&Array.from(e.getElementsByTagName("iframe"))||[]},A=function(e,t){O=!0;try{Object.defineProperties(e.prototype,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({src:f(t)},(0,o.Jx)(t,v)))}catch(n){0}};function k(){return O}},6277:(e,t,n)=>{"use strict";n.d(t,{y:()=>r});const r=Object.getOwnPropertyDescriptor(Node.prototype,"appendChild")},6421:(e,t,n)=>{"use strict";n.d(t,{y:()=>r});const r=Object.getOwnPropertyDescriptor(Node.prototype,"insertBefore")},1667:(e,t,n)=>{"use strict";n.d(t,{y:()=>r});const r=Object.getOwnPropertyDescriptor(Node.prototype,"parentElement")||Object.getOwnPropertyDescriptor(HTMLElement.prototype,"parentElement")},3615:(e,t,n)=>{"use strict";n.d(t,{L:()=>i,y:()=>o});var r=n(8734);const o=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode"),i=Object.getOwnPropertyDescriptor(r.Yk.Node.prototype,"parentNode")},7861:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>i,yu:()=>o});var r=n(2248);const o=Object.getOwnPropertyDescriptor(Node.prototype,"removeChild"),i={configurable:o.configurable,enumerable:o.enumerable,value(e){return o.value.call(this,(0,r.fb)(e))},writable:o.writable}},8549:(e,t,n)=>{"use strict";n.d(t,{y:()=>r});const r=Object.getOwnPropertyDescriptor(Node.prototype,"replaceChild")},4045:(e,t,n)=>{"use strict";n.d(t,{cw:()=>S,om:()=>x,uH:()=>k,Tv:()=>E,F6:()=>T,Qn:()=>D});var r=n(1286),o=n(8734),i=n(2248),s=n(2078);const c=Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype,"type");function a(e){return!e||["text/javascript","application/javascript","module",o._U,"application/ecmascript","application/x-ecmascript","application/x-javascript","text/ecmascript","text/javascript1.0","text/javascript1.1","text/javascript1.2","text/javascript1.3","text/javascript1.4","text/javascript1.5","text/jscript","text/livescript","text/x-ecmascript","text/x-javascript"].some((function(t){return String(e).split(";")[0]===t}))}const l={defaultValue:o._U,descriptor:c,shouldPrevent:a,useSetAttribute:!0};var u=n(7479),p=n(6277),d=n(5267);const f=Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype,"async");function g(e){return{configurable:f.configurable,enumerable:f.enumerable,get:f.get,set(t){return f.set.call(this,t),(0,d.fQ)(this,e.getState()),t}}}const m=Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype,"defer");function h(e){return{configurable:m.configurable,enumerable:m.enumerable,get:m.get,set(t){return m.set.call(this,t),(0,d.fQ)(this,e.getState()),t}}}var b=n(4814),y=n(5041),O=n(1667),v=n(7861),w=n(8952),$=n(8549),_=n(5677);const j=Object.getOwnPropertyDescriptor(Node.prototype,"textContent");function A(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}const k={type:l},P=function(e){if(e.target&&e.target[o.bW]||y.yu.value.call(e.target,"type")===o._U)return e.preventDefault(),!1},S=function(e){const{node:t,src:n,originalValue:r=n}=e;if(!t)return;if(t[o.UM]=!0,t[o.bW])return void _.yu.value.call(t,"type",o._U);if(y.yu.value.call(t,"type")!==o._U)return(0,i.fq)(t,k),r&&s.qB.call(t,r),t;const c=t?t.ownerDocument:o.Bl,a=t&&(O.y?O.y.get.call(t):t.parentElement)||c.body||c.head,l=(0,b.dG)(t);j.set.call(l,t.textContent);const u=t.attributes;for(let o=u.length-1;o>=0;o--){let{name:e,value:n}=u[parseInt(o,10)];if("nonce"===e)n=t.nonce,_.yu.value.call(l,e,n);else k[`${e}`]||_.yu.value.call(l,e,n)}(0,i.fq)(l,k);for(const[o,i]of Object.entries(t))l[`${o}`]=i;t.onload&&(l.onload=t.onload,t.onload=null),t.onerror&&(l.onerror=t.onerror,t.onerror=null),r&&s.qB.call(l,r),w.y.value.call(l,"beforescriptexecute",P);try{$.y.value.call(a,l,t)}catch(f){try{t&&a&&v.yu.value.call(a,t)}catch(f){}p.y.value.call(a,l)}(0,i.fq)(t,k);const d=y.yu.value.call(t,"src");return d&&s.qB.call(l,d),l},x=function(e){const{node:t,ignore:n,shouldRemoveOnBlock:r}=e;if(n)return S(e);t&&((0,i.js)(t,k),u.y.value.call(t,"beforescriptexecute",P),r&&t.parentElement&&t.parentElement.removeChild(t))},E=function(e){return e&&Object.isPrototypeOf.call(HTMLScriptElement.prototype,e)},D=function(e){return E(e)&&a(y.yu.value.call(e,"type"))},T=function(e,t){try{Object.defineProperties(e.prototype,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?A(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):A(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({async:g(t),defer:h(t),src:(0,s.Ay)(t)},(0,i.Jx)(t,k)))}catch(n){0}}},2078:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>b,oK:()=>f,qB:()=>g});var r=n(2248),o=n(5267),i=n(8437),s=n(1184),c=n.n(s),a=n(5041),l=n(5036),u=n(8734),p=n(5677);const d=Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype,"src");Object.getOwnPropertyDescriptor(u.Yk.HTMLScriptElement.prototype,"src");function f(){if("string"==typeof a.LC.value.call(this,"src")){const e=d.get.call(this);if(e)return r.qi.call(this,e)}return""}function g(e){return d.set.call(this,e),p.LC.value.call(this,"src",e),e}const m=function(e,t){return function(n,s){const{node:c,originalValue:a}=e;if(a!==t&&r.u1.call(c,a)!==r.u1.call(c,t))return e;return n((0,l.OB)(s(),e)||(0,o.tu)(e)?i.Ay.allowScript(e):i.Ay.blockScript(e)),e}},h=function(e,t){return function(n,r){const i=(0,o.LB)({node:e,src:t,ignore:!1},r());if(void 0===i)return c().resolve(i);const{async:s}=i;return s?s.promise.then((function(){return m(i,t)(n,r)})):c().resolve(m(i,t)(n,r))}};function b(e){return{configurable:d.configurable,enumerable:d.enumerable,get(){const t=(0,o.LB)(this,e.getState());return void 0===t?d.get.call(this):r.qi.call(this,t.src)},set(t){return h(this,t)(e.dispatch,e.getState),t}}}},2248:(e,t,n)=>{"use strict";n.d(t,{Jx:()=>v,dG:()=>w,fb:()=>d,fq:()=>y,js:()=>O,mG:()=>m,mf:()=>f,qi:()=>_,u1:()=>$,w9:()=>h,wH:()=>g});var r=n(8734),o=n(5267),i=n(8428),s=n(1344),c=n(2981),a=n(5036),l=n(3615),u=n(2425),p=n(5677);function d(e){let t;try{t=l.L.get.call(e)}catch(n){}return e&&!t&&e[r.bW]||e}function f(e){return e&&e[r.gY]||e}function g(e){return{configurable:e.configurable,enumerable:e.enumerable,value:function(...t){t.length>1&&(t[1]=d(t[1]));const n=e.value.apply(this,t);return(0,o.Qj)(n),n},writable:e.writable}}function m(e,t){if(void 0===e)return;const n=!e.value,r=n&&!e.set,o={configurable:e.configurable,enumerable:e.enumerable};return n?(o.get=function(){return e.get.call(d(this))},r||(o.set=function(n){const r=d(this);return t&&r!==this&&e.set.call(this,n),e.set.call(r,n)})):(o.writable=e.writable,o.value=function(...n){const r=d(this);return t&&r!==this&&e.value.call(this,...n),e.value.call(r,...n)}),o}const h=new WeakMap;function b(e,t,n,o,i){const{defaultValue:s,descriptor:a,convert:l=function(e){return e},shouldPrevent:u=function(){return!0}}=n,p=h.has(e)?h.get(e):{},d=u(o);let f=!1;return void 0!==p[`${t}`]||i||(f=(o=l(a.get.call(e)))===r._U||!c.L.value.call(e,t)),d&&void 0!==o&&(p[`${t}`]=[o,a,f],h.set(e,p)),d?s:o}function y(e,t){const{node:n}=(0,o.R$)(e)||{node:e},r=h.get(n)||{};e&&Object.entries(t||{}).forEach((function([t,n]){const{descriptor:o}=n,[i,s,c]=r[`${t}`]||[];if(void 0!==i)if(c)u.LC.value.call(e,t);else try{o.set.call(e,i)}catch(a){p.LC.value.call(e,t,i)}})),e===n&&h.delete(n)}function O(e,t){Object.entries(t||{}).forEach((function([t,n]){const{descriptor:r}=n,o=b(e,t,n);if(e)try{r.set.call(e,o)}catch(i){p.LC.value.call(e,t,o)}}))}function v(e,t){const{getState:n}=e;return Object.entries(t||{}).reduce((function(e,[t,r]){const{descriptor:i,useSetAttribute:s}=r;return e[`${t}`]={configurable:i.configurable,enumerable:i.enumerable,get(){const e=n(),r=(0,o.R$)(this);if(!(0,a.OB)(e,r)){const e=h.get(this);if(e&&e[`${t}`])return e[`${t}`][0]}return i.get.call(this)},set(e){const c=n(),l=(0,o.R$)(this)||(0,o.Qm)(this,c),u=(0,a.OB)(c,l),d=b(this,t,r,e,!0),f=u?e:d;try{i.set.call(this,f),s&&p.LC.value.call(this,t,f)}catch(g){p.LC.value.call(this,t,f)}}},e}),{})}function w(e){try{e.preventDefault()}catch(t){}try{e.stopPropagation()}catch(t){}try{e.stopImmediatePropagation()}catch(t){}return!1}function $(e){return(0,i.A)(e,(0,s.A)(this.ownerDocument||r.Bl)).href}function _(e){return"http"===String(e).substring(0,4)||"//"===String(e).substring(0,2)?$.call(this,e):e}},3731:(e,t,n)=>{"use strict";n.d(t,{Lg:()=>u});var r=n(2033);const o=window,i=`_${(new Date).getTime()}`;const s=Object.getOwnPropertyDescriptor(window,"localStorage"),c=new r.Ay,a=function(){try{const e=window.localStorage;return e.setItem(i,"1"),e.removeItem(i),!0}catch(e){return!1}}(),l=a&&window.localStorage||c,u=function(){try{return a&&s.get.call(o)||c}catch(e){}return l}},2033:(e,t,n)=>{"use strict";n.d(t,{Ai:()=>c,Ay:()=>l,SO:()=>s});const r=new WeakMap,o=function(e){return r.has(e)||r.set(e,{}),r.get(e)};function i(e){const t=o(this);return Object.hasOwnProperty.call(t,e)?t[`${e}`]:null}function s(e,t){o(this)[`${e}`]=String(t)}function c(e){delete o(this)[`${e}`]}function a(){r.has(this)&&r.set(this,{})}class l{constructor(){r.set(this,{})}getItem(e){return i.call(this,e)}setItem(e,t){return s.call(this,e,t)}removeItem(e){return c.call(this,e)}clear(){return a.call(this)}toString(){return"[object Storage]"}}},8437:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>a,gK:()=>c});var r=n(1286),o=n(2775);function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}const s={init:o.Mv,ready:o.Mv,readyApi:(0,o.YW)("apiName","options"),updateLocale:(0,o.YW)("language","json"),updateConfig:o.Mv,fetchDisclosuresBegin:o.Mv,fetchDisclosuresFailure:(0,o.YW)("error","category"),fetchDisclosuresSuccess:(0,o.YW)("disclosures","category"),setExtUsrData:o.Mv,setConsent:(0,o.YW)("category","acceptOrDeny","isDoNotSellView"),setConsentId:o.Mv,allowCookie:o.Mv,blockCookie:o.Mv,allowIFrame:o.Mv,blockIFrame:o.Mv,allowScript:o.Mv,blockScript:o.Mv,acceptAllConsent:o.m7,denyAllConsent:o.m7,saveConsent:(0,o.YW)("consentTimestamp","skipRecord","shouldShowDialog"),revertConsent:o.m7,clearConsent:o.Mv,recordConsent:(0,o.YW)("uuid","consented"),timeoutBegin:o.m7,timeoutComplete:o.m7,render:o.m7,showDialog:o.m7,hideDialog:o.m7,showDrawer:o.m7,hideDrawer:o.m7,showWidget:o.m7,hideWidget:o.m7,focusWidget:o.m7,showDoNotSell:o.m7,hideDoNotSell:o.m7,hideAll:o.m7,goBack:o.m7,showDisclosure:o.Mv,hideDisclosure:o.Mv,showVendorConsent:o.Mv,hideVendorConsent:o.Mv,toggleDisclosure:(0,o.YW)("category","open"),toggleExpansionPanel:(0,o.YW)("id","open")},c=(0,o.cY)([...Object.keys(s)],"OsanoCMP"),a=(0,o.kr)(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},s),c)},2775:(e,t,n)=>{"use strict";n.d(t,{Mv:()=>a,YW:()=>l,cY:()=>u,kr:()=>d,lQ:()=>p,m7:()=>c,mz:()=>f});var r=n(1286),o=n(4525);function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const c=function(){},a=function(e){return{payload:e}},l=function(...e){let t;if("object"==typeof e[e.length-1]&&(t=e.pop()),e.some((function(e){return"string"!=typeof e})))throw new Error("Redux Utils :: 'namedParameters' arguments must be of type 'String'");return function(...n){return{payload:e.reduce((function(e,t,r){return s(s({},e),{},{[t]:n[parseInt(r,10)]})}),s({},t))}}},u=function(e=[],t){return function(e,t){return t?Object.entries(e).reduce((function(e,[n,r]){return e[`${n}`]=`${t}/${r}`,e}),{}):e}(e.reduce((function(e,t){return"string"==typeof t?s(s({},e),{},{[`${(0,o.xQ)(t)}`]:`${(0,o.LW)(t).toUpperCase()}`}):e}),{}),t)},p=function(e,t=c){if(!e)return;const n=function(...n){return s({type:e},t(...n))};return Object.defineProperty(n,"type",{get:function(){return e},configurable:!1,enumerable:!1}),n},d=function(e,t){return Object.keys(e).reduce((function(n,r){const o=p(t[`${r}`],e[`${r}`]);return o?s(s({},n),{},{[r]:o}):n}),{})},f=function(e,t){return function(n,{type:r,payload:o,meta:i}={}){const s=void 0===n?e:n,c=t[`${r}`];return"function"!=typeof c?s:c(s,o,i)}}},6136:(e,t,n)=>{"use strict";n.d(t,{vy:()=>v,Ay:()=>w,ue:()=>g});var r=n(1286),o=n(8734),i=n(3505),s=n(5036),c=n(8437),a=n(4274),l=n(2775),u=n(4525);const p=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var r="function"!=typeof t[0]&&t.shift(),o=t;if(void 0===r)throw new TypeError("The initial state may not be undefined. If you do not want to set a value for this reducer, you can use null instead of undefined.");return function(e,t){for(var n=arguments.length,i=Array(n>2?n-2:0),s=2;s<n;s++)i[s-2]=arguments[s];var c=void 0===e,a=void 0===t;return c&&a&&r?r:o.reduce((function(e,n,r){if(void 0===n)throw new TypeError("An undefined reducer was passed in at index "+r);return n.apply(void 0,[e,t].concat(i))}),c&&!a&&r?r:e)}};function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const g={amp:!1,api:{},cmpContentHash:"1jvAGTXSv.E1ku_funqCbqTSKDPerD0h",cmpVersion:a.CMP_VERSION,config:{configId:"",crossDomain:!1,remoteConsent:!1,customerId:"",timeoutSeconds:10,mode:"production",ccpaRelaxed:!1,googleConsent:!1,iabEnabled:!1,iframeBlocking:"",managePreferencesEnabled:!1,noTattle:!1,type:{timer:!1,analyticsAlways:!1,categories:!1,rejectAll:!1,firstLayerUsage:!1,managePreferences:!1,canDismissDialog:!1},ccpaApplies:!1,forceReconsent:0,gdprApplies:"false",jurisdiction:"br-sp",iframes:{},inline:{},scripts:{},cookies:{},domains:[],categories:[o.rG,o.TG,o.$w,o.rO],lang:"en",palette:{dialogType:"bar",displayPosition:"bottom",infoDialogPosition:"right",widgetPosition:"right",buttonBackgroundColor:"#fff",buttonForegroundColor:"#000",buttonDenyBackgroundColor:"#989",buttonDenyForegroundColor:"#fff",dialogBackgroundColor:"#000",dialogForegroundColor:"#fff",infoDialogOverlayColor:"rgba(0,0,0,0.45)",infoDialogBackgroundColor:"#fff",infoDialogForegroundColor:"#000",linkColor:"#29246a",toggleOffBackgroundColor:"#d2cfff",toggleButtonOffColor:"#ffffff",toggleOnBackgroundColor:"#37cd8f",toggleButtonOnColor:"#f4f4f4",widgetColor:"#37cd8f",widgetOutlineColor:"#29246a",widgetFillColor:"#fff"},allowTimeout:!0,forceManagePreferences:!1},consent:{[o.rG]:o.FA,[o.O3]:o.Fr,[o.TG]:o.Fr,[o.$w]:o.Fr,[o.rO]:o.Fr,[o.H7]:o.Fr},disclosures:{results:{},loading:!1,fetched:[],open:{}},consentTimestamp:0,extUsrData:"",maxConsentSeconds:parseInt("31536000",10),publishTimestamp:"1758114460824",ready:{blocking:!1,consent:!1,dom:!1},savedConsent:null,shouldTattle:Number.isNaN(o.fb)||1==~~((o.fb||NaN)*Math.random()+1),timeoutRunning:!1,ui:{currentScreen:o.Pb.None,dialog:{hidden:!0},drawer:{currentIndex:0,hidden:!0},expansionPanels:{},widget:{hidden:!0}}},m=function(e,t){const{classification:n}=t||{};if(!t||!n)return e;const r=e[`${n}`]||[];return e[`${n}`]=r,r.push(f({type:"cookie"},t)),e},h=function(e){return function(t){const n=(0,s.lf)(t);return f(f({},t),{},{consent:f(f({},t.consent),n.reduce((function(n,r){const i=function(e,n){switch(e){case o.rG:return o.FA;case o.H7:return(0,s.v)(t,n===o.FA?o.Fr:o.FA)}return n===o.FA?o.FA:o.Fr}(r,e);return i&&(n[`${r}`]=i),n}),{}))})}},b=function(e,{hidden:t,view:n,direction:r}){const c=(0,u.h1)(f({},e),{ui:{drawer:{hidden:!!t}}}),a=[...(0,s.q$)(e)];let l=(0,s.kR)(e);switch(r){case i.M.POP:a.splice(a.lastIndexOf(n)+1,a.length),l=Math.max(0,a.lastIndexOf(n)-1);break;case i.M.PUSH:a.splice(l+1,a.length,n),l=a.length-1;break;case i.M.REPLACE:a.splice(0,a.length,n),l=0}return c.ui.drawer.views=a,c.ui.drawer.currentIndex=l,a.length>=1?c.ui.currentScreen=o.Pb.Drawer:c.ui.currentScreen=o.Pb.None,c},y=function(e,t){const n=(0,u.h1)(f({},e),t);return(0,s.Vt)(n)&&(n.config.type={timer:!0,analyticsAlways:!0,categories:!1,rejectAll:!1,firstLayerUsage:!1,managePreferences:!1,canDismissDialog:!0}),(0,s.Gs)(n)||(n.consent[`${o.rO}`]=(0,s.Gd)(n)?o.FA:o.Fr),n},O=(0,l.mz)(g,{[c.gK.ready]:function(e,t){return f(f({},e),{},{ready:f(f({},e.ready),{},{[t]:!0})})},[c.gK.readyApi]:function(e,{apiName:t,options:n={}}){return"shopify"===t?f(f({},e),{},{api:f(f({},e.api),{},{[`${t}`]:n})}):e},[c.gK.timeoutBegin]:function(e){return f(f({},e),{},{timeoutRunning:!0})},[c.gK.timeoutComplete]:function(e){return f(f({},e),{},{timeoutRunning:!1})},[c.gK.init]:y,[c.gK.updateConfig]:function(e,t){return y(e,{config:t})},[c.gK.updateLocale]:function(e,{language}={}){return language?f(f({},e),{},{config:f(f({},e.config),{},{lang:language})}):e},[c.gK.setExtUsrData]:function(e,t){return f(f({},e),{},{extUsrData:`${t}`||e.extUsrData})},[c.gK.showDialog]:function(e){return(0,u.h1)(f({},e),{ui:{currentScreen:o.Pb.Dialog,dialog:{hidden:!1}}})},[c.gK.hideDialog]:function(e){return(0,u.h1)(f({},e),{ui:{currentScreen:o.Pb.None,dialog:{hidden:!0}},timeoutRunning:!1})},[c.gK.showWidget]:function(e){return(0,u.h1)(f({},e),{ui:{widget:{hidden:!1}}})},[c.gK.hideWidget]:function(e){return(0,u.h1)(f({},e),{ui:{widget:{hidden:!0}}})},[c.gK.showDrawer]:function(e){return b(e,{view:i.V.CONSENT,direction:i.M.REPLACE})},[c.gK.hideDrawer]:function(e){return(0,u.h1)(f({},e),{ui:{currentScreen:o.Pb.None,drawer:{hidden:!0}}})},[c.gK.showDoNotSell]:function(e){return b(e,{view:i.V.DO_NOT_SELL,direction:i.M.REPLACE})},[c.gK.hideDoNotSell]:function(e){return f(f({},e),{},{ui:f(f({},e.ui),{},{currentScreen:o.Pb.None,drawer:{currentIndex:0,hidden:!0}})})},[c.gK.goBack]:function(e){var t,n;const r=(null==e||null===(t=e.ui)||void 0===t||null===(t=t.drawer)||void 0===t?void 0:t.views)||[],o=(null==e||null===(n=e.ui)||void 0===n||null===(n=n.drawer)||void 0===n?void 0:n.currentIndex)||0;return b(e,{view:r[parseInt(o,10)]||i.V.CONSENT,direction:i.M.POP})},[c.gK.showDisclosure]:function(e,t){return b(e,{view:i.V.DISCLOSURE,direction:t})},[c.gK.hideDisclosure]:function(e){return b(e,{view:i.V.DISCLOSURE,direction:i.M.POP})},[c.gK.showVendorConsent]:function(e,t){return b(e,{view:i.V.VENDOR_CONSENT,direction:t})},[c.gK.hideVendorConsent]:function(e){return b(e,{view:i.V.VENDOR_CONSENT,direction:i.M.POP})},[c.gK.hideAll]:function(e){return(0,u.h1)(f({},e),{ui:{dialog:{hidden:!0},drawer:{hidden:!0},widget:{hidden:!0}},timeoutRunning:!1})},[c.gK.setConsent]:function(e,{category:t,acceptOrDeny:n,isDoNotSellView:r}){const i=(0,s.lf)(e),c=r||(0,s.tu)(e);function a(t,n,r){switch(t){case o.rG:return o.FA;case o.H7:return(0,s.v)(e,n);case o.TG:if(c){if(r&&r[`${o.H7}`]===o.FA)return o.Fr;if(!r&&(0,s.aE)(e,o.H7))return o.Fr}return n}return n===o.FA?o.FA:o.Fr}if("object"==typeof t){const{extUsrData:n,consentTimestamp:r}=t;return f(f({},e),{},{consent:f(f({},e.consent),Object.entries(t).reduce((function(e,[n,r]){return i.includes(n)&&(e[`${n}`]=a(n,r,t)),e}),{})),consentTimestamp:r>0?r:e.consentTimestamp,extUsrData:n?`${n}`:e.extUsrData})}if("string"==typeof t){if(!i.includes(t))return e;const r=f(f({},e.consent),{},{[t]:a(t,n)});return c&&t===o.H7&&n===o.FA&&(r[`${o.TG}`]=o.Fr),f(f({},e),{},{consent:r})}return e},[c.gK.acceptAllConsent]:function(e){const t=h(o.FA)(e),n=(0,s.v)(e,o.Fr);return n===o.FA&&(t.consent[`${o.H7}`]=n,t.consent[`${o.TG}`]=o.Fr),t},[c.gK.denyAllConsent]:h(o.Fr),[c.gK.saveConsent]:function(e,{consentTimestamp:t=Date.now()}){const n=(0,s.Vk)(e,t),r=(0,s.hr)(e);return(0,s.IP)(e)&&(0,s.aE)(e,o.H7)&&(r[`${o.TG}`]=o.Fr),f(f({},e),{},{consent:r,consentTimestamp:t,savedConsent:n?g.savedConsent:f({},r)})},[c.gK.clearConsent]:function(e){const t=(0,s.v)(e,g.consent[`${o.H7}`]);return f(f({},e),{},{consent:f(f({},g.consent),{},{[o.H7]:t,[o.rO]:(0,s.Gd)(e)?o.FA:o.Fr}),consentTimestamp:0,savedConsent:g.savedConsent})},[c.gK.revertConsent]:function(e){const t=f({},(0,s.Yh)(e)||g.consent);return(0,s.Yh)(e)||(t[`${o.H7}`]=(0,s.v)(e,t[`${o.H7}`]),t[`${o.rO}`]=(0,s.Gd)(e)?o.FA:o.Fr),f(f({},e),{},{consent:t})},[c.gK.toggleExpansionPanel]:function(e,{id:t,open:n}){const r=f({},e.ui.expansionPanels);let o=!r[`${t}`];return void 0!==n&&(o=n),o?r[`${t}`]=!0:delete r[`${t}`],f(f({},e),{},{ui:f(f({},e.ui),{},{expansionPanels:r})})},[c.gK.toggleDisclosure]:function(e,{category:t,open:n}){const r=f({},e.disclosures.open);let o=!r[`${t}`];return void 0!==n&&(o=n),o?r[`${t}`]=!0:delete r[`${t}`],f(f({},e),{},{disclosures:f(f({},e.disclosures),{},{open:r})})},[c.gK.fetchDisclosuresBegin]:function(e,t){return f(f({},e),{},{disclosures:f(f({},e.disclosures),{},{loading:!0,fetched:[...e.disclosures.fetched,t].filter((function(e,t,n){return n.indexOf(e)===t}))})})},[c.gK.fetchDisclosuresFailure]:function(e,{category:t}){return f(f({},e),{},{disclosures:f(f({},e.disclosures),{},{loading:!1,fetched:e.disclosures.fetched.filter((function(e){return e!==t}))})})},[c.gK.fetchDisclosuresSuccess]:function(e,{disclosures:t=[]}){const{disclosures:{results:n,fetched:r=[]}}=e,o=(0,s.bW)(e),i=[...r],c=Object.entries(t.reduce(m,f({},n))).filter((function([e]){return o.includes(e)&&i.push(e)})).reduce((function(e,[t,n]){const r={};return e[`${t}`]=n.filter((function(e){if(!e)return!1;const{name:t,type:n="cookie"}=e,o=`${n}:${t}`;return!r[`${o}`]&&(r[`${o}`]=!0,!0)})).sort((function(e,t){return`${e.type}:${e.name}`.localeCompare(`${t.type}:${t.name}`)})),e}),{});return f(f({},e),{},{disclosures:f(f({},e.disclosures),{},{results:c,fetched:i.filter((function(e,t,n){return n.indexOf(e)===t})),loading:!1})})}});function v(e={}){return p.apply(null,[u.h1.apply(null,[{},g,...Object.values(e).map((function(e){return e()}))]),O,...Object.values(e)])}const w=v()},5036:(e,t,n)=>{"use strict";n.d(t,{Cw:()=>U,DQ:()=>we,DZ:()=>j,Dp:()=>ge,G8:()=>pe,Gd:()=>ce,Gs:()=>Oe,Gy:()=>He,H:()=>Ie,HC:()=>ye,Hb:()=>Fe,I1:()=>De,IP:()=>de,IZ:()=>re,JH:()=>R,Kn:()=>Be,LU:()=>Le,MJ:()=>be,OB:()=>Ae,Ou:()=>B,Ow:()=>Me,PR:()=>S,Pq:()=>V,QH:()=>D,R_:()=>he,Sf:()=>G,U0:()=>x,UX:()=>F,Um:()=>Ne,Vk:()=>_e,Vt:()=>_,Wi:()=>J,Wp:()=>Ke,XL:()=>je,Xg:()=>$,YK:()=>I,Yh:()=>Z,Ym:()=>Y,Zp:()=>Se,_m:()=>v,_o:()=>me,a8:()=>fe,aE:()=>ie,aX:()=>K,ao:()=>ae,bW:()=>P,c4:()=>xe,cJ:()=>ue,dP:()=>q,fJ:()=>z,fc:()=>E,gI:()=>O,h:()=>N,hr:()=>T,i_:()=>W,jB:()=>se,jU:()=>Q,kA:()=>Re,kR:()=>te,lf:()=>C,nj:()=>X,pH:()=>ee,pU:()=>le,q$:()=>ne,qi:()=>L,qv:()=>M,rE:()=>oe,ro:()=>b,sD:()=>Ee,tC:()=>$e,td:()=>Ce,tu:()=>w,uF:()=>ke,uc:()=>We,v:()=>k,vk:()=>Ue,ye:()=>H,z6:()=>ve,ze:()=>Pe});var r=n(1286),o=n(8734),i=n(4274),s=n(8428),c=n(3505),a=n(4525),l=n(3731),u=n(6136);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}const d=(0,s.A)(i.OSANO_IFRAME_URI).hostname,f=["","debug","permissive","production"],g=[],m=[{elementModifiers:{type:"no-results"},type:"no-results"}],h=function(e){return!!(0,a.Jt)(e,"config.managePreferencesEnabled")},b=function(e){return!!(0,a.Jt)(e,"disclosures.loading")},y=function(e){return(0,a.Jt)(e,"config.jurisdiction")||""},O=function(e){return`${y(e)}`.substring(0,2)||null},v=function(e){return"us"===(O(e)||"").toLowerCase()},w=function(e){return!!(0,a.Jt)(e,"iab.usp.ccpaApplies",v(e))},$=function(e){return!1===Object.values((0,a.Jt)(e,"config.type",{})).reduce((function(e,t){return e||t}),!1)},_=function(e){return(0,a.Jt)(e,"config.ccpaRelaxed",!1)&&v(e)&&$(e)},j=function(e,t){return(t&&(0,a.Jt)(e,"disclosures.fetched")||[]).indexOf(t)>=0},A=function(e){const t=!!(0,a.Jt)(e,"config.gpcSupport"),n=!!(0,a.Jt)(e,"config.dntSupport");if(!t&&!n)return!1;const r=!!o.gg.navigator.globalPrivacyControl,i=["1",1,!0].includes(o.gg.navigator.doNotTrack);return t&&r||n&&i},k=function(e,t){if(A(e)||t===o.FA||!0===t)return o.FA;if(void 0===t){return(0,a.Jt)(e,`consent.${o.H7}`)||u.ue.consent[o.H7]}return o.Fr},P=function(e){return(0,a.Jt)(e,"config.categories")||u.ue.config.categories},S=function(e){return[o.O3,...P(e)].filter((function(e){return e!==o.rG}))},x=function(e){return P(e).filter((function(e){return e!==o.rG}))},E=function(e,t){const n=(0,a.Jt)(e,`disclosures.results.${t}`)||[];if(n.length)return n;return b(e)&&!j(e,t)?g:m},D=function(e){return(0,a.Jt)(e,"config.configId")||""},T=function(e){const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},(0,a.Jt)(e,"consent")||u.ue.consent);return t[o.H7]=k(e),t},C=function(e){return[...P(e),o.O3,o.H7]},N=function(e){return(0,a.Jt)(e,"consentTimestamp")||0},I=function(e){return(0,a.Jt)(e,"config.customerId")||""},L=function(e){return o.iQ[`${O(e)}`.toLowerCase()]||y(e)},M=function(e){return(0,a.Jt)(e,"config.cookies")||{}},R=function(e,t){const n=(0,a.Jt)(e,"config.mode")||"production",r=(0,a.Jt)(e,`config.${t}Blocking`,n),o=Math.min(f.indexOf(n),f.indexOf(r));return f[parseInt(o,10)]},F=function(e){return(0,a.Jt)(e,"config.domains")||[]},H=function(e){return(0,a.Jt)(e,"config.remoteConsent")||!1},B=function(e){return H(e)?(0,a.Jt)(e,"extUsrData",""):""},U=function(e){return(0,a.Jt)(e,"config.iframes")||{}},K=function(e,t){var n;const r=(null==t?void 0:t.osano)||(null==t||null===(n=t.dataset)||void 0===n?void 0:n.osano)||"",i=r&&((0,a.Jt)(e,`config.inline.${r}`)||P(e).find((function(e){return e===`${r}`.toUpperCase()}))||"");return o.zM.find((function(e){return e===i}))},W=function(e){const t=o.Bl.location.hostname,n=F(e);return n.filter((function(e){return t.match(e)})).reduce((function(e,t){return e?e.length<t.length?t:e:t}),null)||n[0]},J=function(e){return(0,a.Jt)(e,"config.mode","listener")},G=function(e){return Object.entries((0,a.Jt)(e,"config.palette",{})).reduce((function(e,[t,n]){return e[`${t}`]=n||((0,a.Jt)(u.ue,"config.palette")[`${t}`]||"inherit"),e}),{})},Y=function(e){const t=parseInt((0,a.Jt)(e,"maxConsentSeconds"),10);return Math.min(Number.isNaN(t)?o.D4:t,o.D4)},V=function(e){return(0,a.Jt)(e,"config.storagePolicyHref")},Q=function(e){return Math.max(0,parseInt((0,a.Jt)(e,"config.timeoutSeconds"),10))||10},q=function(e){return!!(0,a.Jt)(e,"config.type.timer")},z=function(e){return q(e)&&function(e){return(0,a.Jt)(e,"config.allowTimeout",!0)}(e)},Z=function(e){return(0,a.Jt)(e,"savedConsent",null)},X=function(e){return(0,a.Jt)(e,"config.scripts")||{}},ee=function(e){return(0,a.Jt)(e,"api")},te=function(e){return(0,a.Jt)(e,"ui.drawer.currentIndex",0)},ne=function(e){return(0,a.Jt)(e,"ui.drawer.views",[])},re=function(e){return(0,a.Jt)(e,"config.policyLinkText","storagePolicy")},oe=function(e){return(0,a.Jt)(e,"config.additionalLinks",[])},ie=function(e,t){return T(e)[`${t}`]===o.FA},se=function(e){return(0,a.Jt)(e,"amp",!1)},ce=function(e){return!!(0,a.Jt)(e,"config.type.analyticsAlways")},ae=function(e){return"boolean"==typeof(0,a.Jt)(e,"config.codeSplitting")},le=function(e,t){return t===o.rG||(t===o.H7?A(e):t===o.TG&&(A(e)||w(e)&&ie(e,o.H7)))},ue=function(e){return(0,a.Jt)(e,"ready.consent")},pe=function(e,t){return!!(0,a.Jt)(e,"disclosures.open",{})[`${t}`]},de=function(e){return ne(e)[te(e)]===c.V.DO_NOT_SELL},fe=function(e,t){return!!(0,a.Jt)(e,"ui.expansionPanels",{})[`${t}`]},ge=function(e){return function(e){return!!(0,a.Jt)(e,"ready.blocking")}(e)&&ue(e)&&function(e){return!!(0,a.Jt)(e,"ready.dom")}(e)},me=function(e,t){return"debug"===R(e,t)||!R(e,t)},he=function(e){return!(0,a.Jt)(e,"ui.dialog.hidden")},be=function(e){return!(0,a.Jt)(e,"ui.drawer.hidden")},ye=function(e){return!!(0,a.Jt)(e,"config.visualOnly")},Oe=function(e){return null!==(0,l.Lg)().getItem(se(e)?`${o.AQ}_${D(e)}`:o.AQ)||!!Z(e)},ve=function(e){return(0,a.Jt)(e,"timeoutRunning")||!1},we=function(e,t){const n=(0,s.A)(t).hostname;return n===d||F(e).filter((function(e){return n.match(e)})).length>0},$e=function(e){return!!(0,a.Jt)(e,"ui.widget.hidden")},_e=function(e,t){const n=Y(e),r=parseInt(t,10),o=isNaN(r)?0:r+1e3*n,i=Date.now(),s=function(e){const t=~~(Date.now()/1e3),n=parseInt((0,a.Jt)(e,"config.forceReconsent"),10);return isNaN(n)||n>t?0:n}(e);return o<=i||t<1e3*s},je=function(e){return!!(0,a.Jt)(e,"config.iframeBlocking")},Ae=function(e,t){if(void 0===t)return!0;const{async:n,classification:r,entityType:i,fromOsano:s,ignore:c,node:a,src:l}=t;if(c)return!0;if(n&&n.running)return!1;if(a&&a[o.I_])return!0;if(me(e,i))return!0;switch(i){case"cookie":{const{"max-age":e,expires:n}=t;if(void 0!==e){if(parseInt(e,10)<=0)return!0}else if(void 0!==n&&new Date(n).getTime()<=Date.now())return!0;if(s)return!1;break}case"iframe":if(!je(e)||!l)return!0;if(s)return!0;break;case"script":if(s||!l)return!0}if(""===r)return!1;if(r===o.rG)return!0;if(r===o.iZ)return!1;if(!Oe(e)&&r===o.rO)if(ce(e))return!0;return r===o.RG||r===o.zT?function(e,t){return"permissive"===R(e,t)}(e,i):(Z(e)||{})[`${r}`]===o.FA},ke=function(e){return(0,a.Jt)(e,"config.googleConsent")||!1},Pe=function(e){return!!(0,a.Jt)(e,"config.type.categories")},Se=function(e){return!!(0,a.Jt)(e,"config.policyLinkInDrawer",!1)},xe=function(e){return!!(0,a.Jt)(e,"shouldTattle")},Ee=function(e){return(0,a.Jt)(e,"config.type.rejectAll")},De=function(e){return(0,a.Jt)(e,"config.type.firstLayerUsage")},Te=function(e){return(0,a.Jt)(e,"config.type.managePreferences")},Ce=function(e){return(0,a.Jt)(e,"config.forceManagePreferences",!1)},Ne=function(e){return(0,a.Jt)(e,"config.type.canDismissDialog")},Ie=function(e){return!!(0,a.Jt)(e,"config.crossDomain",!1)},Le=function(e){return!h(e)&&Pe(e)},Me=function(e){return Pe(e)?h(e):Te(e)||Ce(e)},Re=function(e){return Te(e)||Pe(e)},Fe=function(e){return Ee(e)&&Pe(e)},He=function(e){return Te(e)&&Le(e)&&!Me(e)&&(q(e)||Re(e)||!Ce(e))},Be=function(e){return(0,a.Jt)(e,"config.disclosedVendorCount")},Ue=function(e){return(0,a.Jt)(e,"config.documentPatching",!0)},Ke=function(e){return(0,a.Jt)(e,"config.showWidget",!0)},We=function(e){return"true"===(0,a.Jt)(e,"config.gdprApplies",!1)}},5053:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(1184),o=n.n(r),i=n(8428);const s=function(e,t,n={}){let r=(0,i.A)(t).href;const s={method:e.toUpperCase(),headers:{Accept:"application/json","Content-Type":"application/json"},mode:"cors",redirect:"follow",referrer:"no-referrer",body:"GET"!==e?JSON.stringify(n):void 0};return"get"===e.toLowerCase()&&(r=Object.entries(n).reduce((function(e,[t,n],r){return`${e}${0===r?"?":"&"}${encodeURIComponent(t)}=${encodeURIComponent(n)}`}),r)),new(o())((function(e,t){const n=new XMLHttpRequest,i=[],c=[],a={},l=function(){return{ok:2==(n.status/100|0),statusText:n.statusText,status:n.status,url:n.responseURL,text:function(){return o().resolve(n.responseText)},json:function(){return o().resolve(n.responseText).then(JSON.parse)},blob:function(){return o().resolve(new Blob([n.response]))},clone:l,headers:{keys:function(){return i},entries:function(){return c},get:function(e){return a[e.toLowerCase()]},has:function(e){return e.toLowerCase()in a}}}};n.onreadystatechange=function(){4===n.readyState&&(n.status>=200&&n.status<300?(n.getAllResponseHeaders().replace(/^(.*?):[^\S\n]*([\s\S]*?)$/gm,(function(e,t,n){i.push(t=t.toLowerCase()),c.push([t,n]),a[`${t}`]=a[`${t}`]?`${a[`${t}`]},${n}`:n})),e(function(e){return 204===e.status||"opaque"===e.type?"":e.json().then((function(t){return e.ok?t:o().reject(e)}))}(l()))):t({status:n.status,statusText:n.statusText}))},n.open(s.method||"get",r,!0),n.withCredentials="include"===s.credentials;for(const r in s.headers)Object.hasOwnProperty.call(s.headers,r)&&n.setRequestHeader(`${r}`,s.headers[`${r}`]);n.send(s.body||null)}))},c={get:function(e,t){return s("GET",e,t)},post:function(e,t){return s("POST",e,t)}}},924:(e,t,n)=>{"use strict";n.d(t,{iF:()=>a,mH:()=>c,D_:()=>u});var r=n(8428),o=n(1344);const i=Object.getOwnPropertyDescriptor(Document.prototype,"getElementsByTagName")||Object.getOwnPropertyDescriptor(HTMLDocument.prototype,"getElementsByTagName");var s=n(8734);const c=function(e){const t=e instanceof HTMLElement&&window.getComputedStyle(e).overflowY;return e?"visible"!==t&&"hidden"!==t&&e.scrollHeight>=e.clientHeight?e:c(e.parentNode)||document.body:null},a=function(e){const t=i.value.call(s.Bl,"script"),n=(0,r.A)(e,(0,o.A)(s.Bl));for(let r=0;r<t.length;r++)if(t[r].src===n.href)return t[r]},l=["complete","interactive"],u=function(e){if("loading"===s.Bl.readyState){const t=function(n){if(l.includes(s.Bl.readyState))return s.Bl.removeEventListener(n.type,t,!0),e()};s.Bl.addEventListener("readystatechange",t,!0)}else e()}},8428:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const r=function(e,t){const n=String(e).replace(/^\s+|\s+$/g,"").match(/^([^:/?#]+:)?(?:\/\/(?:([^:@/?#]*)(?::([^:@/?#]*))?@)?(([^:/?#]*)(?::(\d*))?))?([^?#]*)(\?[^#]*)?(#[\s\S]*)?/);if(!n)throw new RangeError;let o=n[1]||"",i=n[2]||"",s=n[3]||"",c=n[4]||"",a=n[5]||"",l=n[6]||"",u=n[7]||"",p=n[8]||"",d=n[9]||"";if(void 0!==t){const e=r(t),n=""===o&&""===c&&""===i;n&&""===u&&""===p&&(p=e.search),n&&"/"!==u.charAt(0)&&(u=""!==u?`${""===e.host&&""===e.username||""!==e.pathname?"":"/"}${e.pathname.slice(0,e.pathname.lastIndexOf("/")+1)}${u}`:e.pathname);const d=[];u.replace(/^(\.\.?(\/|$))+/,"").replace(/\/(\.(\/|$))+/g,"/").replace(/\/\.\.$/,"/../").replace(/\/?[^/]*/g,(function(e){return"/.."===e?d.pop():d.push(e),""})),u=d.join("").replace(/^\//,"/"===u.charAt(0)?"/":""),n&&(l=e.port,a=e.hostname,c=e.host,s=e.password,i=e.username),""===o&&(o=e.protocol)}const f=""!==o||""!==c?"//":"";return{origin:`${o}${f}${c}`,href:`${o}${f}${""!==i?`${i}${""!==s?`:${s}`:""}@`:""}${c}${u}${p}${d}`,protocol:o,username:i,password:s,host:c,hostname:a,port:l,pathname:u,search:"?"===p?"":p,hash:"#"===d?"":d}},o=r},6269:(e,t,n)=>{"use strict";var r;n.d(t,{v4:()=>d});var o=new Uint8Array(16);function i(){if(!r&&!(r="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return r(o)}const s=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;const c=function(e){return"string"==typeof e&&s.test(e)};for(var a=[],l=0;l<256;++l)a.push((l+256).toString(16).substr(1));const u=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase();if(!c(n))throw TypeError("Stringified UUID is invalid");return n};const p=function(e,t,n){var r=(e=e||{}).random||(e.rng||i)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var o=0;o<16;++o)t[n+o]=r[o];return t}return u(r)};function d(e){let t;try{t=p(e)}catch(n){t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}return t}},1344:(e,t,n)=>{"use strict";function r(e){let t=e&&e.baseURI;if(e&&!t)try{const n=e.getElementsByTagName("base")[0];t=n?n.href:e.location}catch(n){t=e.location}return t}n.d(t,{A:()=>r})},4525:(e,t,n)=>{"use strict";n.d(t,{D_:()=>c,Jt:()=>l,LW:()=>u,h1:()=>s,kR:()=>f,kW:()=>g,xQ:()=>p});var r=n(1286);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}const i=function(e,[t,n]){const r=e[`${t}`],o=typeof r,s=typeof n;if(null===n)e[`${t}`]=null;else switch(o){case"object":Array.isArray(r)?Array.isArray(n)&&(e[`${t}`]=Object.entries(n).reduce(i,e[`${t}`])):"object"!==s||Array.isArray(n)||(e[`${t}`]=Object.entries(n).reduce(i,e[`${t}`]));break;case"function":break;default:switch(s){case"function":break;case"object":Array.isArray(n)?e[`${t}`]=[...n]:e[`${t}`]=Object.entries(n).reduce(i,{});break;default:e[`${t}`]=n}}return e};function s(e,t){return e===t||"object"!=typeof t?function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e||{}):Object.entries(t||{}).reduce(i,Object.entries(e||{}).reduce(i,{}))}const c=function(e){return e},a=function(e){return Array.isArray(e)?e.map((function(e){return String(e)})).join(".").split("."):e.split(".")};function l(e,t,n){const r=a(t).reduce((function(e,t){return void 0===e?e:"()"===t.slice(-2)?e[`${t}`]():e[`${t}`]}),e);return void 0===r?n:r}function u(e){return e&&e.match(/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g).map((function(e){return e.toLowerCase()})).join("_")}function p(e){return e&&e.match(/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g).map((function(e,t){return 0===t?e.toLowerCase():`${e[0].toUpperCase()}${e.slice(1).toLowerCase()}`})).join("")}function d(e){return`${e[0].toUpperCase()}${e.slice(1).toLowerCase()}`}function f(e){return e.split(" ").map(d).join(" ")}function g(e){return e.match(/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g).map((function(e){return e.toLowerCase()})).join("-")}},1184:e=>{"use strict";e.exports=window.Promise},4274:e=>{"use strict";e.exports={LANGUAGES:["af","ar","bg","ca","cs","da","de-at","de","el","en-au","en-ca","en-gb","en","es-mx","es","et","fa","fi","fr-ca","fr","gd","he","hi","hr","hu","hy","id","is","it","ja","kk","ko","lt","lv","mi","ms","nb","nl-be","nl","nn","no","pl","pt-br","pt","ro","ru","sk","sl","sq","sr","sv","th","tl","tr","uk","vi","zh-hk","zh-tw","zh","zu"],CONSENT_URI:"https://consent.api.osano.com",DISCLOSURE_URI:"https://disclosure.api.osano.com",LOCALE_URI:"https://locale.cmp.osano.com",TATTLE_URL:"https://tattle.api.osano.com",LOOKUPS_URI:"https://lookups.api.osano.com",OSANO_IFRAME_URI:"https://cmp.osano.com",OSANO_IFRAME_ORIGIN:"https://cmp.osano.com",OSANO_SCRIPT_URI:"https://cmp.osano.com",OSANO_DSAR_URI:"https://my.datasubject.com",OSANO_VENDORLIST_URI:"https://cmp.osano.com",OSANO_ORIGIN:"https://cmp.osano.com",CMP_VERSION:"2025.9.2",IAB_CMP_VERSION:2850,GDPR_COUNTRIES:[]}},1286:(e,t,n)=>{"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}function i(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,{A:()=>i})},5126:(e,t,n)=>{"use strict";function r(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}n.d(t,{A:()=>r})}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e=n(1184),t=n.n(e),r=n(1286),o=n(8734),i=n(9592),s=n(5036),c=n(8437);const a=new WeakMap;function l(e,...t){const n=a.get(this),r=[...n[`${e}`]||[]];n[`${e}`]=r.filter((function([,e]){return!e})),a.set(this,n),r.forEach((function([e]){return e.apply(void 0,t)}))}function u(e,t,n){if("function"==typeof t){const r=a.get(this),o=r[`${e}`]||[];if(o.some((function([e]){return e===t})))return;o.push([t,!!n]),r[`${e}`]=o,a.set(this,r)}}function p(e,t){const n=a.get(this),r=n[`${e}`]||[];n[`${e}`]=r.filter((function([e]){return e!==t})),a.set(this,n)}const d=new class{constructor(){a.set(this,{})}emit(...e){return l.apply(this,e)}addListener(...e){return u.call(this,e[0],e[1])}once(...e){return u.call(this,e[0],e[1],!0)}removeListener(...e){return p.apply(this,e)}};function f({dispatchEvent:e}){return function(t){return function(n){return function(r){const i=t.getState(),a=(0,s.hr)(i),l=null!==(0,s.Yh)(i),u=(0,s.Yh)(i)||{},p=n(r),d=t.getState(),f=(0,s.lf)(d),{type:g,payload:m}=r;switch(g){case c.gK.ready:switch("blocking"===m&&e(o.qY.BLOCKING),m){case"blocking":case"consent":case"dom":if((0,s.Dp)(d)){const t=(0,s.Yh)(d);e(o.qY.INIT,t||void 0)}}break;case c.gK.updateLocale:{const{json:t}=m;e(o.qY.LOCALE_UPDATED,t);break}case c.gK.setConsent:{const{category:t,acceptOrDeny:n}=m;if(t&&"object"==typeof t){const r=Object.entries(t).reduce((function(e,[t,n]){return f.includes(t)&&n!==a[`${t}`]&&(e[`${t}`]=n),e}),{});!n&&Object.keys(r).length&&e(o.qY.CONSENT_CHANGED,r)}else"string"==typeof t&&f.includes(t)&&e(o.qY.CONSENT_CHANGED,{[t]:n===o.FA?o.FA:o.Fr});break}case c.gK.saveConsent:{const t=(0,s.Yh)(d);t&&(e(o.qY.CONSENT_SAVED,t),l||e(o.qY.NEW_CONSENT,t),[o.rO,o.TG,o.$w,o.O3].forEach((function(n){t[`${n}`]===o.FA&&u[`${n}`]!==t[`${n}`]&&e(o.qY[`CONSENT_ACCEPT_${n}`])})),t[`${o.H7}`]===o.FA&&u[`${o.H7}`]!==t[`${o.H7}`]&&e(o.qY.CCPA_OPT_OUT));break}case c.gK.clearConsent:break;case c.gK.blockCookie:case c.gK.blockIFrame:case c.gK.blockScript:{const{payload:t}=r,{entityType:n}=t;switch(n){case"cookie":e(o.qY.COOKIE_BLOCKED,t.name);break;case"iframe":e(o.qY.IFRAME_BLOCKED,t.src);break;case"script":e(o.qY.SCRIPT_BLOCKED,t.src)}break}case c.gK.hideDialog:e(o.qY.UI_CHANGED_STATE,"dialog","hide");break;case c.gK.hideDrawer:e(o.qY.UI_CHANGED_STATE,"drawer","hide");break;case c.gK.hideWidget:e(o.qY.UI_CHANGED_STATE,"widget","hide");break;case c.gK.hideDoNotSell:e(o.qY.UI_CHANGED_STATE,"doNotSell","hide");break;case c.gK.showDialog:e(o.qY.UI_CHANGED_STATE,"dialog","show");break;case c.gK.showDrawer:e(o.qY.UI_CHANGED_STATE,"drawer","show");break;case c.gK.showWidget:e(o.qY.UI_CHANGED_STATE,"widget","show");break;case c.gK.showDoNotSell:e(o.qY.UI_CHANGED_STATE,"doNotSell","show")}return p}}}}var g=n(6136),m=n(3731),h=n(4525);function b(e){return function(t){var n=t.dispatch,r=t.getState;return function(t){return function(o){return"function"==typeof o?o(n,r,e):t(o)}}}}var y=b();y.withExtraArgument=b;const O=y;function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}const w="true"===(0,m.Lg)().getItem(`${o.AQ}_debug`),$=function(e={},t){const n=[t,f({dispatchEvent:function(...e){return d.emit.apply(d,e)}}),O].filter((function(e){return!!e})),c=w&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__||i.compose;let a;const l=(0,h.h1)(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},g.ue),e);(0,s.Vt)(l)&&(l.config.type={timer:!0,analyticsAlways:!0,categories:!1,rejectAll:!1,firstLayerUsage:!1,managePreferences:!1,canDismissDialog:!0}),(0,s.ye)(l)||delete l.extUsrData,(0,s.Gs)(l)||(l.consent[`${o.H7}`]=(0,s.v)(l),l.consent[`${o.rO}`]=(0,s.Gd)(l)?o.FA:o.Fr);try{a=(0,i.createStore)(g.Ay,l,c((0,i.applyMiddleware)(...n)))}catch(u){a=(0,i.createStore)(g.Ay,{},c((0,i.applyMiddleware)(...n)))}return a.dynamicReducers={},a};function _(e,t,n){e.dynamicReducers[`${t}`]=n,e.replaceReducer((0,g.vy)(e.dynamicReducers))}var j=n(2775);const A=(0,j.lQ)("OsanoCMP_IAB/SET_CONSENT_DATA",(0,j.YW)("gppSection","value"));function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const S=(0,j.mz)({iab:{}},{[A.type]:function(e,{gppSection:t,value:n}){var r,o,i;return P(P({},e),{},{iab:P(P({},null==e?void 0:e.iab),{},{consentStrings:P(P({},null==e||null===(r=e.iab)||void 0===r?void 0:r.consentStrings),{},{[t]:n}),hasActed:P(P({},null==e||null===(o=e.iab)||void 0===o?void 0:o.hasActed),{},{[t]:!!(0,s.cJ)(e)&&n!==(null==e||null===(i=e.iab)||void 0===i?void 0:i.consentStrings[`${t}`])})})})}}),x=document,E=window,D=function(e){const t=!!E.frames[`${e}`];if(!t)if(x.body){const t=x.createElement("iframe");t.style.cssText="display:none",t.name=e,x.body.appendChild(t)}else setTimeout(D,5,e);return!t},T=function(e){return function(t,n){const r=new Map;_(t,"iab",S),Object.entries(e).forEach((function([e,{reducer:o,versions:i}]){o&&_(t,`iab.${e}`,o),Object.entries(i).forEach((function([o,{api:i,apiName:s,createMiddleware:c,gppSection:a,iframeName:l,postMessageEventHandler:u,reducer:p}]){"gpp"!==a&&t.dispatch(A(a,"")),p&&_(t,`iab.${e}.${o}`,p),c&&n.addMiddleware(c({dispatchEvent:i.dispatch}));const d=i.bind(t,r);r.set(a,d),!s||E[`${s}`]&&"function"==typeof E[`${s}`]||!D(l)||(E[`${s}`]=d,E.addEventListener("message",u,!1))}))}))}};var C=n(4274),N=n(5053);const I=function(e,t,n,r){const i=o.hT.isEmpty();n&&o.hT.extend(n,t),r&&!i||(o.hT.locale=t,e.dispatch(c.Ay.updateLocale(t,n)))};function L(e,n,r){const i=void 0!==r;if(!o.hT.isLocaleAvailable(n)&&!r){if(!i)return t().reject("Language unavailable");n="en"}const c=o.hT.isLocaleLoaded(n);if(r||c)return I(e,n,r,i),t().resolve(n);const a=e.getState(),l=(0,s.ao)(a)?`${C.OSANO_ORIGIN}/${(0,s.YK)(a)}/${(0,s.QH)(a)}/${n}.json`:`${C.LOCALE_URI}/${n}.json`;return N.A.get(l).then((function(t){return I(e,n,t,i),n}))}function M(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?M(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):M(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var F,H,B;F=window,B="data",F[H="Osano"]=F[H]||function(){F[H][B].push(arguments)},F[H][B]=F[H][B]||[];const U=new WeakMap,K=function(e,t,{emitter:n,store:r},i){n.once.apply(n,i);const c=r.getState();if((0,s.cJ)(c)&&(0,s.Gs)(c)){(0,s.Yh)(c)[`${e}`]===o.FA&&n.emit.call(n,t)}},W=function(e){const t=new Error('"userData" must be a string or number that is no longer than 128 characters'),n=`${e}`;if(n.length>128)throw t;switch(typeof e){case"string":case"number":return n;default:throw t}};const J=function(){const e={},t=window.Osano.data;for(let r=t.length-1;r>=0;r--){const[o,...i]=Array.from(t[r]);if("userData"===o){try{e.extUsrData=W(i[0])}catch(n){console.error(n)}t.splice(r,1)}}return e}();class G{constructor({emitter:e,store:t}){U.set(this,{emitter:e,store:t,deprecation:{storage:{getConsent:function(){return R({},(0,s.hr)(t.getState()))}}}})}setup(e){e.Osano.cm=this;const t=function(t){const[n,...r]=Array.from(t);if("string"==typeof n)if(n.startsWith("on")){const t=`osano-cm-${n.substring(2).replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}`;e.Osano.cm.addEventListener(t,...r)}else 1===r.length?e.Osano.cm[n]=r[0]:console.error(new Error(`Osano: Expected one argument for property setter "${n}", got ${r.length}`));else console.error(new Error(`Osano: First argument must be a "string", got "${typeof n}"`))},n=e.Osano.data;n.push=t,n.forEach(t),n.splice(0,n.length)}on(...e){this.addEventListener.apply(this,e)}addEventListener(...e){const[t]=e,{emitter:n,store:r}=U.get(this),i=r.getState();switch(t){case o.qY.INIT:if(n.once.apply(n,e),(0,s.Dp)(i))if((0,s.Gs)(i)){const e=R({},(0,s.Yh)(i));n.emit.call(n,o.qY.INIT,e)}else n.emit.call(n,o.qY.INIT);break;case o.qY.CONSENT_SAVED:if(n.addListener.apply(n,e),(0,s.cJ)(i)&&(0,s.Gs)(i)){const t=e[1];if("function"==typeof t){const e=R({},(0,s.Yh)(i));requestAnimationFrame((function(){return t(e)}))}}break;case o.qY.CONSENT_ACCEPT_ANALYTICS:K(o.rO,t,U.get(this),e);break;case o.qY.CONSENT_ACCEPT_MARKETING:K(o.TG,t,U.get(this),e);break;case o.qY.CONSENT_ACCEPT_PERSONALIZATION:K(o.$w,t,U.get(this),e);break;case o.qY.CONSENT_ACCEPT_STORAGE:K(o.O3,t,U.get(this),e);break;case o.qY.CCPA_OPT_OUT:K(o.H7,t,U.get(this),e);break;default:n.addListener.apply(n,e)}}off(...e){this.removeEventListener.apply(this,e)}removeEventListener(...e){const{emitter:t}=U.get(this);t.removeListener.apply(t,e)}emit(e){"osano-cm-dom-info-dialog-open"!==e||this.showDrawer()}get drawerOpen(){const{store:e}=U.get(this);return(0,s.MJ)(e.getState())}get dialogOpen(){const{store:e}=U.get(this);return(0,s.R_)(e.getState())}set locale(e){const{store:t}=U.get(this);L(t,e)}get locale(){return o.hT.locale}get mode(){const{store:e}=U.get(this);return(0,s.Wi)(e.getState())}showWidget(){const{store:e}=U.get(this);e.dispatch(c.Ay.showWidget())}hideWidget(){const{store:e}=U.get(this);e.dispatch(c.Ay.hideWidget())}showDialog(){const{store:e}=U.get(this);e.dispatch(c.Ay.showDialog())}hideDialog(){const{store:e}=U.get(this);e.dispatch(c.Ay.hideDialog())}showDrawer(){const{store:e}=U.get(this);e.dispatch(c.Ay.showDrawer())}hideDrawer(){const{store:e}=U.get(this);e.dispatch(c.Ay.hideDrawer())}showDoNotSell(){const{store:e}=U.get(this);e.dispatch(c.Ay.showDoNotSell())}hideDoNotSell(){const{store:e}=U.get(this);e.dispatch(c.Ay.hideDrawer()),setTimeout((function(){e.dispatch(c.Ay.hideDoNotSell())}),400)}render(){const{store:e}=U.get(this);e.dispatch(c.Ay.render())}ready(e,t){const{store:n}=U.get(this);n.dispatch(c.Ay.readyApi(e,t))}get analytics(){const{store:e}=U.get(this);return(0,s.hr)(e.getState())[`${o.rO}`]===o.FA}get cmpContentHash(){const{store:e}=U.get(this);return e.getState().cmpContentHash}get cmpVersion(){const{store:e}=U.get(this);return e.getState().cmpVersion}get consentModel(){const{store:e}=U.get(this),t=e.getState();return(0,s.dP)(t)?"implicit":"explicit"}get countryCode(){const{store:e}=U.get(this),t=e.getState();return(0,s.qi)(t).substring(0,2)}get gdprApplies(){const{store:e}=U.get(this);return(0,s.uc)(e.getState())}get jurisdiction(){const{store:e}=U.get(this),t=e.getState();return(0,s.qi)(t)}get marketing(){const{store:e}=U.get(this);return(0,s.hr)(e.getState())[`${o.TG}`]===o.FA}get personalization(){const{store:e}=U.get(this);return(0,s.hr)(e.getState())[`${o.$w}`]===o.FA}get publishTimestamp(){const{store:e}=U.get(this);return e.getState().publishTimestamp}get optOut(){const{store:e}=U.get(this);return(0,s.hr)(e.getState())[`${o.H7}`]===o.FA}getConsent(){const{store:e}=U.get(this);return R({},(0,s.hr)(e.getState()))}get storage(){const{deprecation:{storage:e}}=U.get(this);return e}set userData(e){const{store:t}=U.get(this);if((0,s.ye)(t.getState())){const n=W(e);t.dispatch(c.Ay.setExtUsrData(n))}}get userData(){const{store:e}=U.get(this);return(0,s.Ou)(e.getState())}}var Y=n(8251),V=n(4960),Q=n(4045),q=n(5267),z=n(5677);const Z={childList:!0,subtree:!0,attributes:!0,attributeFilter:["osano","data-osano"],attributeOldValue:!0},X=function(e,t){return function(n){const{async:r,entityType:i,node:s,ignore:a,classification:l}=n;if(s&&s.osano&&delete s.dataset.osano,!a&&!l&&r&&r.running){if(!r.listening){switch(i){case"iframe":(0,V.om)(n);break;case"script":(0,Q.om)(n)}r.listening=!0,r.promise.then((function(){return X(e,t)(n)}))}return n}if(e.shouldBlockEntity(n)){switch(i){case"iframe":t.dispatch(c.Ay.blockIFrame(n));break;case"script":t.dispatch(c.Ay.blockScript(n))}return n}if(s[o.UM])return n;switch(i){case"iframe":t.dispatch(c.Ay.allowIFrame(n));break;case"script":t.dispatch(c.Ay.allowScript(n))}return n}};function ee(e,t,n,r,o,i,s){try{var c=e[i](s),a=c.value}catch(e){return void n(e)}c.done?t(a):Promise.resolve(a).then(r,o)}function te(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function s(e){ee(i,r,o,s,c,"next",e)}function c(e){ee(i,r,o,s,c,"throw",e)}s(void 0)}))}}var ne=n(5126),re=n(4814),oe=n(924);const ie=["format","consentTimestamp","expDate"];function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const ae=re.LC.value.call(o.Bl,"iframe");Object.entries({width:0,height:0,border:"none",position:"absolute",left:"-9999px",top:"-9999px;",overflow:"hidden"}).forEach((function([e,t]){return ae.style[`${e}`]=t})),ae.setAttribute("width",0),ae.setAttribute("height",0),ae.setAttribute("src",C.OSANO_IFRAME_URI),ae.setAttribute("title","Osano Consent Manager Iframe"),ae.setAttribute("aria-hidden","true"),ae.setAttribute("tabIndex","-1"),Y.KU&&z.yu.value.call(ae,"nonce",Y.KU);const le=function(e){return ae.parentNode?t().resolve(e):new(t())((function(t,n){let r,i;r=function(){ae.removeEventListener("load",i),ae.removeEventListener("error",r),n("Unable to request remote consent")},i=function(){ae.removeEventListener("load",i),ae.removeEventListener("error",r),t(e)},ae.addEventListener("load",i),ae.addEventListener("error",r),o.Bl.body.appendChild(ae)}))},ue=function(e){return ae.parentNode&&ae.parentNode.removeChild(ae),t().resolve(e)};function pe(e){const{format:t,consentTimestamp:n=Date.now(),expDate:r}=e,o=(0,ne.A)(e,ie);if("string"===t){const{consent:e,storeKey:t,configId:n,eventName:i}=o;return`${t}|${n}|${i}${e?`|_|${JSON.stringify(e)}`:r?`|expdate|${r}`:""}`}return JSON.stringify(ce(ce({},o),{},{consentTimestamp:n}))}const de=function(e,t,n,r){return function(i,c){const a=function(i){const{data:l,origin:u,source:p}=i;if((0,s.DQ)(c(),u)&&p===ae.contentWindow){const{storeKey:i,eventName:s,consent:u=null,expDate:p}=function(e){switch(typeof e){case"string":try{const{storeKey:t,eventName:n,consent:r,consentTimestamp:o,expDate:i}=JSON.parse(e);return{storeKey:t,eventName:n,consent:r,consentTimestamp:o,expDate:i,format:"json"}}catch(t){if(e&&""!==e.replace(/"|'/gm,"")){const[t,r,i,s]=e.split("|"),c=parseInt(s,10);let a=i;try{a=JSON.parse(i)}catch(n){}return r===o.Kq?{storeKey:t,eventName:r,consent:a,expDate:Number.isNaN(c)?0:c,format:"string"}:{storeKey:t,eventName:r,format:"string"}}}break;case"object":{const{storeKey:t,eventName:n,consent:r,consentTimestamp:o,expDate:i}=e;return{storeKey:t,eventName:n,consent:r,consentTimestamp:o,expDate:i,format:"object"}}}return{}}(l);if(i===o.AQ)switch(s){case o.Kq:clearTimeout(r),o.gg.removeEventListener("message",a,!1),null!==u?t(yt(c(),u,p)):n(`No value for ${o.AQ}_${e}`);break;case o.ex:t(!0)}}};return a}},fe=function(e,n,r,i){return function(s,c){let a;const l=i&&function(e){return o.gg.removeEventListener("message",a,!1),i(e)};return new(t())((function(t,i){const l=setTimeout((function(){return i(r)}),o.e5);a=de(e,t,i,l)(s,c),o.gg.addEventListener("message",a,!1),ae.contentWindow.postMessage(n,C.OSANO_IFRAME_ORIGIN)})).then((function(e){return o.gg.removeEventListener("message",a,!1),!l&&ue(e),e}),l).catch((function(e){try{o.gg.removeEventListener("message",a,!1),ue()}catch(t){}throw e}))}},ge=function(e=0){return function(n,r){if(e>=o.gz)return t().reject("Iframe did not respond to messages.");const i=(0,s.QH)(r()),c=pe({storeKey:o.AQ,configId:i,eventName:o.AR,format:"json"});return fe(i,c,e,(function(e){return ge(e+1)(n,r)}))(n,r)}},me="Cross-Domain consent is not supported",he=function(){return function(e,n){if((0,s.H)(n()))return new(t())((function(t,n){(0,oe.D_)((function(){return le().then((function(){return e(ge())})).then((function(){return e((function(e,t){const n=(0,s.QH)(t()),r=pe({storeKey:o.AQ,configId:n,eventName:o.Tn,format:"json"});return fe(n,r,"Request for consent from root domain failed.")(e,t)}))})).then(t).catch(n)}))}));throw me}},be=function(e,n){return function(r,i){return(0,s.H)(i())?le().then((function(){return r(function(e,t=Date.now()){return function(n,r){const i=r(),c=(0,s.QH)(i),a=pe({storeKey:o.AQ,configId:c,eventName:o._$,consent:e,consentTimestamp:t,format:"json"});return fe(c,a,"Could not store consent on root domain.")(n,r)}}(e,n))})):t().reject(me)}},ye=function(){return function(e,n){return(0,s.H)(n())?le().then((function(){return e((function(e,t){const n=(0,s.QH)(t()),r=pe({storeKey:o.AQ,configId:n,eventName:o.KW,consentTimestamp:0,expDate:0,format:"json"});return fe(n,r,"Could not clear consent from root domain.")(e,t)}))})):t().reject(me)}},Oe=["customerId","configId","consentedCategories","uuid"];function ve(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function we(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ve(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ve(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const $e=["name","fromOsano","value"],_e=function(e,[t,n]){let r="";switch(t.toLowerCase()){case"max-age":case"expires":case"domain":case"path":n&&(r=`; ${t}=${n}`);break;case"httponly":(n||""===n)&&(r=`; ${t}=${n}`);break;case"secure":n&&(r=";secure");break;case"samesite":switch(n.toLowerCase()){case"lax":case"strict":case"none":r=`; samesite=${n.toLowerCase()}`}}return`${e}${r}`},je=Object.getOwnPropertyDescriptor(Document.prototype,"cookie")||Object.getOwnPropertyDescriptor(HTMLDocument.prototype,"cookie"),Ae=function(e){let t=je.get.call(o.Bl)||"";if(!e)return t;t=`;${t.replace(/; +/g,";")}`;const n=t.split(`;${e}=`);return n.length>=2?n.pop().split(";").shift():""},ke=function(e){const t=(0,q.SK)(e,{}),{name:n,fromOsano:r,value:i}=t,s=(0,ne.A)(t,$e),c=Object.entries(s).reduce(_e,`${n}=${i}`);r&&je.set.call(o.Bl,`${c}; expires=1 Jan 1970 00:00:00 GMT;`),je.set.call(o.Bl,c)},Pe=function(){if(window.dataLayer=window.dataLayer||[],!window.dataLayer.some((function(e){return e[1]===`developer_id.${o.EH}`})))try{window.dataLayer.push(["set",`developer_id.${o.EH}`,!0])}catch(e){}},Se=function(e={},t=o.q6){if(t===o.SD){if((window.dataLayer||[]).find((function(e){return e[0]===o.u4&&e[1]===o.SD})))return}try{const n=Object.entries(o.jA).reduce((function(t,[n,r]){return r.forEach((function(r){e[`${n}`]&&(t[`${r}`]=n===o.rG?o.aI:o.ft[e[`${n}`]])})),t}),{});Object.keys(n).length>0&&window.gtag(o.u4,t,n)}catch(n){}},xe=/^((GT)|G|(AW)|(DC)|(GMC)|(UA))-/i,Ee=new Set;let De=!1;function Te({0:e,1:t}={}){switch(null==e?void 0:e.toLowerCase()){case o.u4:return void([o.q6,o.SD].includes(null==t?void 0:t.toLowerCase())&&(De=!0));case o.yp:return void(De||Ee.has(t)||!xe.test(t)||(Ee.add(t),console.warn(`Error: Google tag ${t} loaded before Consent Mode update. Please review and resolve Google Consent Mode sequencing.`)))}}let Ce=!1;function Ne(){if(!window.dataLayer||Ce)return;let e=window.DataLayerHelper;e||(n(5289),e=window.DataLayerHelper,delete window.DataLayerHelper),new e(window.dataLayer,Te,!0),Ce=!0}var Ie=n(8428),Le=n(1344),Me=n(2033);function Re(e){return 0===String(e).indexOf(o.AQ)}const Fe=Object.getOwnPropertyDescriptor(Storage.prototype,"removeItem");function He(e){return this.constructor===Storage?Fe.value.call(this,e):Me.Ai.call(this,e)}const Be=Object.getOwnPropertyDescriptor(Storage.prototype,"setItem");function Ue(e,t){return this.constructor===Storage?Be.value.call(this,e,t):Me.SO.call(this,e,t)}const Ke=["value","classification","name","entityType","fromOsano"],We=(0,m.Lg)(),Je=function(e){return~~(function(e){return~-encodeURI(JSON.stringify(e)).split(/%..|./).length}(e)/1024)},Ge=[],Ye=["script","cookie","iframe"],Ve=function(e){let t=e;switch(e){case"script":t=o.hB;break;case"cookie":t=o.SP;break;case"iframe":t=o.O8;break;case"ignore":t=o.gy}try{return JSON.parse(We.getItem(t))}catch(n){He.call(We,t)}},Qe=function(){return Object.entries(Ve(o.gy)||{}).reduce((function(e,[t,n]){return Ye.includes(t)&&(e[`${t}`]=n&&Array.isArray(n)?new Set(n):e[`${t}`]),e}),{script:new Set([]),cookie:new Set([]),iframe:new Set([])})},qe=function([e,t,n],r,i){const[s,c,a]=r||[];(e||i)&&He.call(We,o.hB),(t||i)&&He.call(We,o.SP),(n||i)&&He.call(We,o.O8),(r||i)&&He.call(We,o.gy);try{e&&!i&&Ue.call(We,o.hB,JSON.stringify(Array.from(e.values())))}catch(l){}try{t&&!i&&Ue.call(We,o.SP,JSON.stringify(Array.from(t.values())))}catch(l){}try{n&&!i&&Ue.call(We,o.O8,JSON.stringify(Array.from(n.values())))}catch(l){}try{r&&!i&&Ue.call(We,o.gy,JSON.stringify({script:s?Array.from(s.values()):[],cookie:c?Array.from(c.values()):[],iframe:a?Array.from(a.values()):[]}))}catch(u){}},ze=function(e){const[t,n,r]=function(){const[e=new Set([]),t=new Set([]),n=new Set([])]=Ye.map(Ve).map((function(e){return e&&Array.isArray(e)?new Set(e):void 0}));return[e,t,n]}(),{script:o,cookie:i,iframe:s}=Qe();let c=0,a=0;e.forEach((function(e){const{storeType:l,storeKey:u}=e;switch(l){case"script":t.has(u)&&++c&&t.delete(u),!o.has(u)&&++a&&o.add(u);break;case"cookie":n.has(u)&&++c&&n.delete(u),!i.has(u)&&++a&&i.add(u);break;case"iframe":r.has(u)&&++c&&r.delete(u),!s.has(u)&&++a&&s.add(u)}})),(c||a)&&qe(c?[t,n,r]:[],a?[o,i,s]:void 0)},Ze=function(e){const{script:t,cookie:n,iframe:r}=Qe();let o=0;e.forEach((function(e){const{storeType:i,storeKey:s}=e;switch(i){case"script":t.has(s)&&++o&&t.delete(s);break;case"cookie":n.has(s)&&++o&&n.delete(s);break;case"iframe":r.has(s)&&++o&&r.delete(s)}})),o&&qe([],[t,n,r])},Xe=function(e,t){if(Array.isArray(t))return e.concat(t.reduce(Xe,[]));if("object"==typeof t){const{storeKey:n,storeType:r}=t;if(n&&r)return e.concat(t)}return e};setInterval((function(e){if(Ge.length>0){const t=Ge.splice(0);for(;Je(t)>224;){const e=[0,0].concat(t.splice(~~(t.length/2)));Array.prototype.splice.apply(Ge,e)}const n=e?t:t.reduce(Xe,[]);return N.A.post(`${C.TATTLE_URL}/`,n).then((function(e){return ze(n),e})).catch((function(){const e=[Ge.length,0].concat(n);Array.prototype.splice.apply(Ge,e)}))}return t().resolve()}),3e4);const et=function(e){const{entityType:t="script",name:n,src:r,node:i}=e;let s;switch(t){case"script":case"iframe":if("string"==typeof r&&`${r}`.trim())try{s=(0,Ie.A)(r,(0,Le.A)((null==i?void 0:i.ownerDocument)||o.Bl)).href,s=/http(s)?:/.test(s)?r.replace(/\?.*/g,""):""}catch(c){s=r.replace(/\?.*/g,"")}s=`${s||""}`.trim();break;case"cookie":"string"==typeof n&&(s=`${n}`)}return s};function tt(e,t,n,r,o,i){return(e>>>5^t<<2)+(t>>>3^e<<4)^(n^t)+(i[3&r^o]^e)}class nt{static encrypt(e,t){if(e=String(e),t=String(t),0===e.length)return"";const n=nt.strToLongs(nt.utf8Encode(e)),r=nt.strToLongs(nt.utf8Encode(t).slice(0,16)),o=nt.encode(n,r),i=nt.longsToStr(o);return nt.base64Encode(i).replace(/\+/gi,"-").replace(/\//gi,"_")}static decrypt(e,t){if(e=String(e),t=String(t),0===e.length)return"";const n=e.replace(/-/gi,"+").replace(/_/gi,"/"),r=nt.strToLongs(nt.base64Decode(n)),o=nt.strToLongs(nt.utf8Encode(t).slice(0,16)),i=nt.decode(r,o),s=nt.longsToStr(i);return nt.utf8Decode(s.replace(/\0+$/,""))}static encode(e,t){const n=[...e];n.length<2&&(n[1]=0);const r=n.length;let o,i=Math.floor(6+52/r),s=n[r-1],c=n[0],a=0,l=0;for(;i>0;){i--,l+=2654435769,a=l>>>2&3;for(let e=0;e<r;e++)c=n[(e+1)%r],o=tt(s,c,l,e,a,t),s=n[parseInt(e,10)]+=o}return n}static decode(e,t){const n=[...e],r=n.length,o=2654435769,i=Math.floor(6+52/r);let s,c,a=n[r-1],l=n[0],u=i*o;for(;0!==u;){c=u>>>2&3;for(let e=r-1;e>=0;e--)a=n[e>0?e-1:r-1],s=tt(a,l,u,e,c,t),l=n[parseInt(e,10)]-=s;u-=o}return n}static strToLongs(e){const t=Math.ceil(e.length/4),n=new Array(t);for(let r=0;r<n.length;r++)n[parseInt(r,10)]=e.charCodeAt(4*r)+(e.charCodeAt(4*r+1)<<8)+(e.charCodeAt(4*r+2)<<16)+(e.charCodeAt(4*r+3)<<24);return n}static longsToStr(e){let t="";for(let n=0;n<e.length;n++)t+=String.fromCharCode(255&e[parseInt(n,10)],e[parseInt(n,10)]>>>8&255,e[parseInt(n,10)]>>>16&255,e[parseInt(n,10)]>>>24&255);return t}static utf8Encode(e){return unescape(encodeURIComponent(e))}static utf8Decode(e){try{return decodeURIComponent(escape(e))}catch(t){return e}}static base64Encode(e){if("undefined"!=typeof btoa)return btoa(e);if("undefined"!=typeof Buffer)return new Buffer(e,"binary").toString("base64");throw new Error("No Base64 Encode")}static base64Decode(e){if("undefined"==typeof atob&&"undefined"==typeof Buffer)throw new Error("No base64 decode");try{if("undefined"!=typeof atob)return atob(e);if("undefined"!=typeof Buffer)return new Buffer(e,"base64").toString("binary")}catch(t){throw new Error(`Invalid ciphertext ${t.toString()}`)}}}const rt=nt;var ot=n(6269);const it=window.TextEncoder,st=window.crypto&&window.crypto.subtle&&window.crypto.subtle.digest;function ct(e,n,r){if(r&&it&&st){const t=(new it).encode(`${e}/${n}/${r}`);return window.crypto.subtle.digest("SHA-512",t).then((function(e){const t=new Uint8Array(e),n=String.fromCharCode(...Array.from(t));return btoa(n).replace(/\+/gi,"-").replace(/\//gi,"_")}))}return t().reject()}function at(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function lt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?at(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):at(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const ut=(0,m.Lg)(),pt=new WeakMap;function dt(e){const{ignoreCookieTattles:t,ignoreIFrameTattles:n,ignoreScriptTattles:r,store:o}=pt.get(this),{entityType:i,src:c,name:a}=e;switch(i){case"cookie":return t.has(a);case"iframe":return!(0,s.XL)(o.getState())||n.has(c);case"script":return r.has(c)}return!1}function ft(e){const{classification:t,ignore:n,name:r,src:i,entityType:s}=e;if(n||dt.call(this,e)){const{ignoreCookieTattles:e,ignoreIFrameTattles:t,ignoreScriptTattles:n}=pt.get(this);switch(s){case"cookie":e.add(r);break;case"iframe":t.add(i);break;case"script":n.add(i)}return!1}if(t===o.zT)switch(s){case"script":case"iframe":return!!i;case"cookie":return!0}return!1}const gt=function(e,t,n,r){const o="https:"===document.location.protocol;Ue.call(ut,e,t),ke({name:e,value:t,"max-age":r,domain:n,secure:o,path:"/"})},mt=function(e,t){const n="https:"===document.location.protocol;He.call(ut,e),ke({name:e,value:"","max-age":-99999999,domain:t,secure:n,path:"/"}),t&&ke({name:e,value:"","max-age":-99999999,secure:n,path:"/"})},ht=function(e,t){let n;try{n=JSON.parse(e)}catch(r){try{n=JSON.parse(rt.decrypt(e,t))}catch(o){}}return n},bt=function(e,t,n){const r=JSON.stringify({consent:e,consentTimestamp:t});return n?rt.encrypt(r,n):r},yt=function(e,t,n){const r=(0,s.Ym)(e);let i=(0,s.i_)(e);const c=function(e){return function(t,n){return t||((t=ht(e,n))&&(i=n),t)}};let a;if("string"==typeof t)try{a=JSON.parse(t)}catch(f){a=(0,s.UX)(e).reduce(c(t),void 0)||void 0}else a=t,i=t&&t.domain||i;let{consentTimestamp:l,consent:u=a,expDate:p=n}=a||{};if("string"==typeof u)try{u=JSON.parse(u)}catch(f){u=(0,s.UX)(e).reduce(c(u),void 0)||void 0}u&&(l=u.consentTimestamp||l,u=u.consent||u);const d=void 0===l;return d&&(l=p-1e3*o.PA),l=parseInt(l,10),l=Number.isNaN(l)?0:l,u&&l+1e3*r>Date.now()?{consent:u,consentTimestamp:l,domain:i,wasExpiryBased:d}:void 0},Ot=function(e){return new(t())((function(t){if((0,s.pH)(e.getState()).shopify)return t();const n=e.subscribe((function(){(0,s.pH)(e.getState()).shopify&&(n(),t())}))}))},vt=function(e){const t=(0,s.Ou)(e);if(!t||!(0,s.ye)(e))return;const n=(0,s.YK)(e),r=(0,s.QH)(e);return ct(n,r,t).then((function(i){return function(e,t,n){return N.A.get(`${C.LOOKUPS_URI}/customer/${e}/config/${t}/user/${n}/consent`).catch((function(){return null}))}(n,r,i).then((function({consented:n,lastConsentTimestamp:r,userConsentId:i}){if(!n||!r)return null;const c=Date.parse(r),a=(0,s.Ym)(e);if(Number.isNaN(c)||c+1e3*a<Date.now())return null;return{consent:lt(lt({},[...(0,s.bW)(e),o.H7].reduce((function(e,t){return lt(lt({},e),{},{[`${t}`]:n.includes(t)?o.FA:o.Fr})}),{})),{},{extUsrData:t}),consentTimestamp:c,uuid:i}})).catch((function(){return null}))}))},wt=function(e,t){const n=e.getState();(0,s.uF)(n)&&(Pe(),Ne(),Se(null!=t?t:(0,s.hr)(n),o.SD))},$t=function(e){const t=e.getState(),n=(0,s.i_)(t),r=ut.getItem(`${o.AQ}_expdate`);return yt(t,ht(ut.getItem(o.AQ),n),r?parseInt(r,10):0)};class _t{get uuid(){return pt.get(this).uuid}set uuid(e){pt.get(this).uuid=e;const t=this.store.getState(),n=(0,s.Ym)(t),r=(0,s.i_)(t);gt(`${o.AQ}_uuid`,e,r,n)}get store(){const{store:e}=pt.get(this)||{};return e}constructor(e){const t={store:e,uuid:ut.getItem(`${o.AQ}_uuid`)||(0,ot.v4)(),ignoreScriptTattles:new Set([]),ignoreIFrameTattles:new Set([]),ignoreCookieTattles:new Set([])},n=(0,s.i_)(e.getState()),r=ut.getItem(o.hB)||ut.getItem(`${o.AQ}_tattles`)||"[]";try{t.scriptTattles=new Set(JSON.parse(r)||[])}catch(l){mt(o.hB,n),t.scriptTattles=new Set([])}mt(`${o.AQ}_tattles`,n);const i=ut.getItem(o.SP)||"[]";try{t.cookieTattles=new Set(JSON.parse(i)||[])}catch(l){mt(o.SP,n),t.cookieTattles=new Set([])}const c=ut.getItem(o.O8)||"[]";try{t.iframeTattles=new Set(JSON.parse(c)||[])}catch(l){mt(o.O8,n),t.iframeTattles=new Set([])}const a=ut.getItem(`${o.gy}`)||"{}";try{const e=JSON.parse(a);Object.entries(a).reduce((function(e,[t,n]){return["script","cookie","iframe"].includes(t)&&(e[`${t}`]=n&&Array.isArray(n)?n:e[`${t}`]),e}),{script:[],cookie:[],iframe:[]}),t.ignoreCookieTattles=new Set(e.cookie),t.ignoreIFrameTattles=new Set(e.iframe),t.ignoreScriptTattles=new Set(e.script)}catch(l){mt(`${o.AQ}_tattles-ignore`,n)}pt.set(this,t)}setup(){const e=pt.get(this)||{},{store:n}=e,r=n.getState(),i=(0,s.i_)(r),a=vt(r);return function(e,t){var n;const{store:r}=e,i=Ae().replace(/; +/g,";").split(";").reduce((function(e,t){const[n,...r]=t.split("=");return 0===n.indexOf(o.AQ)&&(e[`${n}`]=e[`${n}`]||[],e[`${n}`].unshift(r.join("="))),e}),{}),s=i[`${o.AQ}`],c=null===(n=i[`${o.AQ}_expdate`])||void 0===n?void 0:n.map((function(e){return parseInt(e||0,10)}));let{consentTimestamp:a}=$t(r)||{};if(!s)return;let l=0;const[u,p]=s.map((function(e,n){const r=ht(e,t);if(!r)return-1;const{consentTimestamp:i}=r;return void 0!==i?(l++,i):c[n-l]-1e3*o.PA})).reduce((function([e,t],n,r){return t>=n||n<a?[e,t]:[r,n]}),[-1,0]);u>=0&&(Object.entries(i).forEach((function([e,n]){if(e===`${o.AQ}_expdate`)return;let i=n[u];if(e===o.AQ){const e=yt(r.getState(),i,p+1e3*o.PA);if(e&&e.domain===t){const{consent:n,consentTimestamp:r}=e;i=bt(n,r,t)}else i=void 0}i&&Ue.call(ut,e.trim(),i)})),e.uuid=ut.getItem(`${o.AQ}_uuid`)||e.uuid)}(e,i),function(e,n){let r;try{if(!(0,s.Gs)(e.getState()))throw"Unsaved";let{consentTimestamp:i,consent:c,wasExpiryBased:a}=$t(e)||{};if(void 0===c)throw"Unsaved, Expired or Malformed";mt(`${o.AQ}_expdate`,n),r=t().resolve({consent:c,consentTimestamp:i,domain:n,setStorage:a&&i>Date.now()-1e3*o.PA})}catch(i){mt(o.AQ,n),mt(`${o.AQ}_expdate`,n);try{r=he()(e.dispatch,e.getState).then((function(t){const{consent:r,consentTimestamp:o}=yt(e.getState(),t)||{};if(void 0===r)throw ye()(e.dispatch,e.getState),"Unsaved, Expired or Malformed";return{consent:r,consentTimestamp:o,domain:n,setStorage:!0}})).catch((function(){return null}))}catch(c){r=t().resolve(null)}}return r}(n,i).then(function(e,t,n,r){return function(o){var i;const c=null==o||null===(i=o.consent)||void 0===i?void 0:i.extUsrData,a="No valid consent found";if(!(0,s.ye)(e)){if(!o)throw a;if(c)throw n(),a;return o}const l=(0,s.Ou)(e);if(!l){if(!o)throw a;if(c)throw n(),a;return o}return r.then((function(e){if(!e){if(!o)throw a;if(l!==c)throw n(),a;return o}return l!==c?lt(lt({},e),{},{skipRecord:!0,setStorage:!0}):e.consentTimestamp>((null==o?void 0:o.consentTimestamp)||-1)?lt(lt({},e),{},{domain:t,setStorage:!0,skipRecord:!0}):o}))}}(r,i,(function(){e.uuid=(0,ot.v4)(),n.dispatch(c.Ay.clearConsent(!0))}),a)).then(function(){var t=te((function*(t){wt(n,t.consent),function(e){return function({consent:t,consentTimestamp:n,domain:r,setStorage:i,skipRecord:s,uuid:a}){const{store:l}=e;a&&(e.uuid=a),i&&Ue.call(ut,o.AQ,bt(t,n,r)),l.dispatch(c.Ay.setConsent(t,!0)),l.dispatch(c.Ay.saveConsent(n,s))}}(e)(t),window.Shopify&&(yield Ot(n)),n.dispatch(c.Ay.ready("consent"))}));return function(e){return t.apply(this,arguments)}}()).catch(function(){var e=te((function*(e){wt(n),function(e){return function(t){(0,s.cJ)(e.getState())?e.dispatch(c.Ay.ready("consent")):(0,s.dP)(e.getState())&&(e.dispatch(c.Ay.timeoutBegin()),e.dispatch(c.Ay.acceptAllConsent()),window.Shopify||e.dispatch(c.Ay.saveConsent()))}}(n)(e),window.Shopify&&(yield Ot(n)),n.dispatch(c.Ay.ready("consent"))}));return function(t){return e.apply(this,arguments)}}())}teardown(){}shouldTattleOnEntity(e){const{cookieTattles:t,iframeTattles:n,scriptTattles:r,store:i}=pt.get(this),c=i.getState();if(!(0,s.c4)(c))return!1;const{classification:a,node:l,ignore:u,entityType:p,src:d,name:f}=e;return!(u||a&&a!==o.zT)&&((!l||!l[o.I_])&&(!("script"===p&&r.has(d)||"cookie"===p&&t.has(f)||"iframe"===p&&n.has(d))&&!!(0,s.JH)(c,p)))}tattle(e){const{classification:t,entityType:n}=e,{cookieTattles:r,iframeTattles:i,scriptTattles:c,store:a}=pt.get(this),l=a.getState(),u=(0,s.QH)(l),p=(0,s.YK)(l);if(ft.call(this,e)){if(this.shouldTattleOnEntity(e))switch(function(e,{customerId:t,configId:n}){const{entityType:r="script"}=e,i={configId:n,customerId:t,currentURI:o.Bl.location.href,language:o.hT.locale,storeType:r};if(i.storeKey=et(e),i.storeKey){switch(r){case"cookie":{const{value:t,classification:n,name:r,entityType:o,fromOsano:s}=e,c=(0,ne.A)(e,Ke);Object.entries(c).forEach((function([e,t]){return i[`${e}`]=t}));break}}Ge.push(i)}}(e,{customerId:p,configId:u}),n){case"script":{const{src:t}=e;!c.has(t)&&c.add(t);break}case"cookie":{const{name:t}=e;!r.has(t)&&r.add(t);break}case"iframe":{const{src:t}=e;!i.has(t)&&i.add(t);break}}}else switch(function(e){const{entityType:t}=e,n=et(e);n&&Ge.reduce((function(e,{storeType:r,storeKey:o},i){return r!==t||o!==n||e.unshift(i),e}),[]).forEach((function(e){return Ge.splice(e,1)}))}(e),n){case"script":{const{classification:t,src:n}=e;c.has(n)&&c.delete(n),t&&Ze([{storeKey:n,storeType:"script"}]);break}case"cookie":{const{name:n}=e;r.has(n)&&r.delete(n),t&&Ze([{storeKey:n,storeType:"cookie"}]);break}case"iframe":{const{src:n}=e;i.has(n)&&i.delete(n),t&&Ze([{storeKey:n,storeType:"iframe"}]);break}}qe([c,r,i])}shouldRecordConsent(e,t){const n=this.store.getState(),r=(0,s.h)(n),i=(0,s.Ou)(n),c=[...(0,s.bW)(n),o.H7].reduce((function(n,r){return n[`${r}`]=t[`${r}`]||e[`${r}`],n}),{});return{consentedCategories:Object.entries(c).filter((function([,e]){return e===o.FA})).map((function([e]){return e})),extUsrData:i,consentTimestamp:r,shouldRecord:Object.keys(c).some((function(n){return t[`${n}`]!==e[`${n}`]}))||(null==e?void 0:e.consentTimestamp)<r}}saveConsent(e,n,r){var i=this;const c=this.store.getState();if((0,s._o)(c))return;const a=(0,s.Ym)(c),l=isNaN(parseInt(n,10))?Date.now():parseInt(n,10),u=(0,s.i_)(c),p=(0,s.YK)(c),d=(0,s.QH)(c),f=(0,s.uF)(c);let{consentTimestamp:g,consent:m}=$t(this.store)||{};gt(`${o.AQ}_uuid`,this.uuid,u,a);let h,b=e;try{b="string"==typeof e?JSON.parse(e):e,h=bt(b,l,u)}catch(O){return!1}(0,s.Vk)(c,l)?(mt(o.AQ,u),mt(`${o.AQ}_expdate`,u),this.store.dispatch(ye()).catch((function(){})),b={}):(!(0,s.HC)(c)&&gt(o.AQ,h,u,a),mt(`${o.AQ}_expdate`,u),this.store.dispatch(be(h,l)).catch((function(){})));const y=[];if(!r){const{consentedCategories:e,extUsrData:t,shouldRecord:n}=this.shouldRecordConsent(m||{},b);(n||g<l)&&y.push(ct(p,d,t).catch((function(){return""})).then((function(t){return function(e){let{customerId:t,configId:n,consentedCategories:r,uuid:o}=e,i=(0,ne.A)(e,Oe);return N.A.post(`${C.CONSENT_URI}/record`,we(we({},i),{},{osnoCustomerId:t,osnoConfigId:n,userConsentId:o,consented:r.join(", ")})).catch((function(){}))}({configId:d,consentedCategories:e,customerId:p,extUsrData:t,uuid:i.uuid})})))}return f&&(Pe(),Ne(),Se(b,o.q6)),t().all(y)}}var jt=n(2240);function At(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function kt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?At(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):At(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}let Pt;Pt=document.contains;const St=Pt,xt=new WeakMap;class Et{set dom(e){const t=xt.get(this);t.dom=e,xt.set(this,t)}get dom(){const{dom:e}=xt.get(this);return e}get isActive(){const{active:e}=xt.get(this);return!!e}get storage(){const{storage:e}=xt.get(this);return e}get middleware(){const{middleware:e}=xt.get(this);return e}constructor({dynamicMiddleware:e,store:t}){const n=new MutationObserver(function({manager:e,store:t}){const n=X(e,t),r=function(e){if(!e||!(0,Q.Qn)(e)&&!(0,V.Tv)(e))return;!(0,o.q9)()||e[o.b7]||e[o.gY]||(0,V.Tv)(e)||(0,q.AZ)(e);const r=(0,q.R$)(e)||(0,q.Qm)(e,t.getState());r&&!(0,q.wm)(e)&&(!o.lw||(0,o.q9)()||"script"!==r.entityType||e[o.b7]||e[o.gY]||(r.shouldRemoveOnBlock=!0),n(r))};return function(n,i){i.disconnect(),n.forEach((function(n){const{addedNodes:o=[],removedNodes:i=[],type:s,attributeName:a,target:l,oldValue:u}=n;switch(s){case"attributes":((0,Q.Tv)(l)||(0,V.Tv)(l))&&["osano","data-osano"].includes(a)&&z.yu.value.call(l,a,u);break;case"childList":{var p;Array.from(o).forEach((function(e){[e,...(0,V.RA)(e)].forEach(r)}));const n=[...(null==e||null===(p=e.dom)||void 0===p?void 0:p.requiredNodes)||[]];Array.from(i).some((function(e){return n.includes(e)}))&&t.dispatch(c.Ay.render());break}}})),i.observe(o.Bl.documentElement,Z)}}({manager:this,store:t})),r=new _t(t),i=function({manager:e,storage:t}){return function(n){return function(r){return function(o){const i=n.getState(),a=r(o),{type:l,payload:u}=o,p=n.getState(),d=(0,s.hr)(p),f=l===c.gK.setExtUsrData?u:(0,s.Ou)(p);switch(l){case c.gK.revertConsent:e.unblock();break;case c.gK.setExtUsrData:if(!(0,s.ye)(p)||(0,s.Ou)(i)===f)break;if(f){t.setup();break}if(!(0,s.Gs)(p))break;case c.gK.saveConsent:{const{payload:{consentTimestamp:n=Date.now(),skipRecord:r}}=o,i=kt(kt(kt({},d),(0,jt.O$)(p)),{},{extUsrData:f});f&&(i.extUsrData=f),t.saveConsent(i,n,r),e.unblock();break}case c.gK.clearConsent:{const n=o.payload;t.saveConsent({},0,n),e.unblock();break}case c.gK.setConsentId:t.uuid=u;break;case c.gK.allowCookie:{const{payload:t}=o;e.permitCookie(t);break}case c.gK.allowIFrame:{const{payload:t}=o;e.permitIFrame(t);break}case c.gK.allowScript:{const{payload:t}=o;e.permitScript(t);break}case c.gK.blockCookie:{const{payload:t}=o;e.preventCookie(t);break}case c.gK.blockIFrame:{const{payload:t}=o;e.preventIFrame(t);break}case c.gK.blockScript:{const{payload:t}=o;e.preventScript(t);break}}return a}}}}({manager:this,storage:r}),a={active:!1,cookies:new Set([]),dynamicMiddleware:e,iframes:new Set([]),scripts:new Set([]),middleware:i,documentObserver:n,store:t,storage:r};xt.set(this,a)}preventCookie(e){const{cookies:t}=xt.get(this);t.add(e),this.storage.tattle(e)}preventIFrame(e){(0,V.om)(e);const{iframes:t}=xt.get(this);t.add(e),this.storage.tattle(e)}preventScript(e){(0,Q.om)(e);const{scripts:t}=xt.get(this);t.add(e),this.storage.tattle(e)}permitCookie(e){ke(e);const{cookies:t}=xt.get(this);t.delete(e),this.storage.tattle(e)}permitIFrame(e){(0,V.cw)(e);const{iframes:t}=xt.get(this);t.delete(e),this.storage.tattle(e)}permitScript(e){(0,Q.cw)(e);const{scripts:t}=xt.get(this);t.delete(e),this.storage.tattle(e)}shouldBlockEntity(e){const{store:t}=xt.get(this),n=t.getState();return!(0,s.OB)(n,e)}unblock(){var e=this;const{cookies:t,iframes:n,scripts:r,store:i}=xt.get(this),s=Array.from(r),a=Array.from(n),l=Array.from(t);r.clear(),n.clear(),t.clear(),s.filter((function(t){const{src:n,node:r}=t,s=r?r.ownerDocument:o.Bl;return!(!r||!(r.parentElement&&St.call(s,r)||!o.Bl.querySelectorAll(`script[src="${n}"]`).length))&&(!!e.shouldBlockEntity(t)||(i.dispatch(c.Ay.allowScript(t)),!1))})).forEach((function(t){return e.preventScript.call(e,t)})),a.filter((function(t){const{node:n}=t;return!!n&&(!!e.shouldBlockEntity(t)||(i.dispatch(c.Ay.allowIFrame(t)),!1))})).forEach((function(t){return e.preventIFrame.call(e,t)})),l.filter((function(t){return!!e.shouldBlockEntity(t)||(i.dispatch(c.Ay.allowCookie(t)),!1)})).forEach((function(t){return e.preventCookie.call(e,t)}))}setup(){const e=xt.get(this),{documentObserver:t,dynamicMiddleware:n}=e;e.active=!0,t.observe(o.Bl.documentElement,Z),n&&n.addMiddleware(this.middleware),this.storage.setup()}teardown(){const e=xt.get(this),{documentObserver:t,dynamicMiddleware:n}=e;e.active=!1,t.disconnect(),n&&n.removeMiddleware(this.middleware),this.storage.teardown()}}var Dt=n(2248);const Tt=Object.getOwnPropertyDescriptor(Document.prototype,"append"),Ct={configurable:Tt.configurable,enumerable:Tt.enumerable,value:function(...e){return e.filter((function(e){return e&&1===e.nodeType})).forEach((function(e){(0,q.Qj)(e)})),Tt.value.apply(this,e)},writable:Tt.writable},Nt=Object.getOwnPropertyDescriptor(Document.prototype,"createElementNS")||Object.getOwnPropertyDescriptor(HTMLDocument.prototype,"createElementNS"),It={configurable:!0,enumerable:Nt.enumerable,writable:!0,value:function(...e){const t=Nt.value.apply(this,e);let[,n]=e;switch(null===n&&(n="null"),n?n.toLowerCase():""){case"img":case"iframe":case"script":(0,q.Qj)(t)}return t}},Lt=Object.getOwnPropertyDescriptor(Document.prototype,"prepend"),Mt={configurable:Lt.configurable,enumerable:Lt.enumerable,value:function(...e){return e.filter((function(e){return e&&1===e.nodeType})).forEach((function(e){(0,q.Qj)(e)})),Lt.value.apply(this,e)},writable:Lt.writable},Rt=Object.getOwnPropertyDescriptor(Document.prototype,"write")||Object.getOwnPropertyDescriptor(HTMLDocument.prototype,"write"),Ft=function(e,n){const r={configurable:je.configurable,enumerable:je.enumerable,get:()=>Ae(),set:e=>(function(e){return function(n,r){const o=(0,q.SK)(String(e),r());return(0,s.OB)(r(),o)?(ke(o),n(c.Ay.allowCookie(o))):n(c.Ay.blockCookie(o)),t().resolve(o)}}(String(e))(n.dispatch,n.getState),e)},o={configurable:Rt.configurable,enumerable:Rt.enumerable,writable:Rt.writable,value:function(...e){return Rt.value.apply(this,e)}},i={append:Ct,cookie:r,createElementNS:It,prepend:Mt,write:o};(0,s.vk)(n.getState())&&(i.createElement=re.ZS);try{Object.defineProperties(e.prototype,i)}catch(a){0}},Ht=Object.getOwnPropertyDescriptor(Element.prototype,"innerHTML"),Bt=Ht?Element.prototype:HTMLElement.prototype,Ut=Ht||Object.getOwnPropertyDescriptor(HTMLElement.prototype,"innerHTML");function Kt(){return Ut.get.call(this)}function Wt(e){return Ut.set.call(this,e),e}const Jt=function(e,t){return function(){return Wt.call(e,t)}},Gt=Object.getOwnPropertyDescriptor(Element.prototype,"append"),Yt={configurable:Gt.configurable,enumerable:Gt.enumerable,value:function(...e){return e.filter((function(e){return e&&1===e.nodeType})).forEach((function(e){(0,q.Qj)(e)})),Gt.value.apply(this,e)},writable:Gt.writable},Vt=Object.getOwnPropertyDescriptor(Element.prototype,"toggleAttribute"),Qt=(0,Dt.mG)(Vt);var qt=n(5041);const zt=new WeakMap,Zt=function(e=""){let t=null===e?"":`${e}`;"[object RegExp]"===Object.prototype.toString.call(e)&&(t=t.split(/\/(.+?)\//)[1]);try{zt.set(this,t)}catch(n){}},Xt={configurable:!1,enumerable:!1,get:function(){return St.call(o.Bl,this)&&(this[o.b7]||void 0!==zt.get(this)||Zt.call(this,qt.yu.value.call(this,"data-osano"))),zt.get(this)||""}},en=Object.getOwnPropertyDescriptor(Element.prototype,"prepend"),tn={configurable:en.configurable,enumerable:en.enumerable,value:function(...e){return e.filter((function(e){return e&&1===e.nodeType})).forEach((function(e){(0,q.Qj)(e)})),en.value.apply(this,e)},writable:en.writable};var nn=n(2425);const rn=function(e,t){const n={configurable:Ut.configurable,enumerable:Ut.enumerable,get(){return Kt.call(this)},set(e){return Jt(this,e)(t.dispatch,t.getState),e}};try{Object.defineProperties(e.prototype,{append:Yt,osano:Xt,prepend:tn,removeAttribute:nn.Ay,getAttribute:qt.Ay,setAttribute:z.Ay}),Object.defineProperty(Bt,"innerHTML",n),e.prototype.toggleAttribute&&Object.defineProperty(e.prototype,"toggleAttribute",function(e){return{configurable:Vt.configurable,enumerable:Vt.enumerable,value(t,n){const r=Qt.value.call(this,t,n);if(this&&this.nodeType===Node.ELEMENT_NODE&&"SCRIPT"===this.tagName)switch(t){case"async":case"defer":(0,q.LB)(this,e.getState())}return r}}}(t))}catch(r){0}};var on=n(5023),sn=n(6591),cn=n(8865);const an=new WeakMap;function ln(e){const t=e&&e[o.gY]||e,n=an.has(t)?an.get(t):{};return an.set(t,n),n}function un(e,t,...n){const[r]=n,i=e.getState(),c=(0,q.Qm)(this,i);if(!((0,s.OB)(i,c)||(0,q.tu)(c)))return(0,Dt.dG)(r);if((0,Q.Tv)(this)){if(!!!(this&&this[o.gY]||!this[o.bW]))return(0,Dt.dG)(r)}return t&&t.call((0,Dt.mf)(this),...n)}const pn=function(e,t){const n={configurable:cn.y.configurable,enumerable:cn.y.enumerable,get:function(){return ln(this).onload||cn.y.get.call(this)},set:function(e){const n=ln(this);return e&&((0,V.Tv)(this)||(0,Q.Tv)(this))?(n.onload=e,this[o.bW]&&cn.y.set.call(this[o.bW],un.bind(this[o.bW],t,e)),cn.y.set.call(this,un.bind(this,t,e))):(delete n.onload,cn.y.set.call(this,e))}},r={configurable:sn.y.configurable,enumerable:sn.y.enumerable,get:function(){return ln(this).onerror||sn.y.get.call(this)},set:function(e){const n=ln(this);return e&&((0,V.Tv)(this)||(0,Q.Tv)(this))?(n.onerror=e,this[o.bW]&&sn.y.set.call(this[o.bW],un.bind(this[o.bW],t,e)),sn.y.set.call(this,un.bind(this,t,e))):(delete n.onerror,sn.y.set.call(this,e))}};try{Object.defineProperties(e.prototype,{onload:n,onerror:r})}catch(i){0}};var dn=n(6277);const fn=Object.getOwnPropertyDescriptor(Node.prototype,"cloneNode");var gn=n(6421);const mn=Object.getOwnPropertyDescriptor(Node.prototype,"nextSibling");var hn=n(1667),bn=n(3615);const yn=Object.getOwnPropertyDescriptor(Node.prototype,"previousSibling");var On=n(7861),vn=n(8549);const wn=function(e){try{Object.defineProperties(e.prototype,{appendChild:(0,Dt.wH)(dn.y),cloneNode:(0,Dt.wH)(fn),insertBefore:(0,Dt.wH)(gn.y),nextSibling:(0,Dt.mG)(mn),parentElement:(0,Dt.mG)(hn.y),parentNode:(0,Dt.mG)(bn.y),previousSibling:(0,Dt.mG)(yn),removeChild:On.Ay,replaceChild:(0,Dt.wH)(vn.y)})}catch(t){0}},$n=function(e,t){const n={setItem:{configurable:Be.configurable,enumerable:Be.enumerable,writable:Be.writable,value:function(e,t){if(this!==(0,m.Lg)()||!Re(e))return Ue.call(this,e,t)}}};try{Object.defineProperties(e.prototype,n)}catch(r){0}try{Object.defineProperties(t.prototype,n)}catch(r){0}},_n=function(){};var jn=n(628);function An(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function kn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?An(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):An(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Pn="consent_id",Sn="consent_timestamp";function xn(e,t,n,r){const i=t.getState(),a=!!(0,s.pH)(t.getState()).shopify,l=(0,s.cJ)(i);if(!a||!l)return;const u=(0,s.Yh)(i),p=window.Shopify.customerPrivacy.getTrackingConsentMetafield(Pn),d=new Date(window.Shopify.customerPrivacy.getTrackingConsentMetafield(Sn)),f=e.storage.uuid,g=new Date((0,s.h)(i)),m=!p&&!+g,h=(new Date-d)/1e3>=(0,s.Ym)(i);m||r!==c.gK.saveConsent&&h?r!==c.gK.saveConsent&&(0,s.dP)(i)&&t.dispatch(c.Ay.saveConsent(Date.now(),!1,!0)):!p||d<g?function(e,t,n,r){const i=[];t&&i.push({key:Pn,value:t},{key:Sn,value:n.toISOString()}),window.Shopify.customerPrivacy.setTrackingConsent(kn(kn({analytics:e[`${o.rO}`]===o.FA,marketing:e[`${o.TG}`]===o.FA,preferences:e[`${o.$w}`]===o.FA,sale_of_data:e[`${o.H7}`]!==o.FA},r),{},{metafields:i}),(function(){}))}(u,f,g,n):!h&&g<d&&function(e,t,n){e.dispatch(c.Ay.hideDialog()),e.dispatch(c.Ay.setConsentId(t));const r=window.Shopify.customerPrivacy;e.dispatch(c.Ay.setConsent({[o.rO]:r.analyticsProcessingAllowed()?o.FA:o.Fr,[o.TG]:r.marketingAllowed()?o.FA:o.Fr,[o.$w]:r.preferencesProcessingAllowed()?o.FA:o.Fr,[o.H7]:r.saleOfDataAllowed()?o.Fr:o.FA})),e.dispatch(c.Ay.saveConsent(+n,!0))}(t,p,d)}function En(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Dn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?En(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):En(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Tn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Cn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Tn(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Tn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Nn={iab:{usp:{ccpaApplies:!1,notified:void 0,signatory:void 0,optOut:!1}}},In=function(e,t){const{config:n}=t||{},{config:{jurisdiction:r}={}}=e,{jurisdiction:o=r}=n||{},{ccpaApplies:i="us"===`${o||""}`.toLowerCase().substring(0,2)}=n||{};return(0,h.h1)(Cn({},e),{iab:{usp:{ccpaApplies:i}}})},Ln=function(e,t){return[(0,s.v)(e,t),t].includes(o.FA)||!0===t},Mn=(0,j.mz)(Nn,{[c.gK.init]:In,[c.gK.updateConfig]:function(e,t){return In(e,{config:t})},[c.gK.setConsent]:function(e,{category:t,acceptOrDeny:n}){return"object"==typeof t&&t[`${o.H7}`]?(0,h.h1)(Cn({},e),{iab:{usp:{optOut:Ln(e,t[`${o.H7}`])}}}):"string"==typeof t&&t===o.H7?(0,h.h1)(Cn({},e),{iab:{usp:{optOut:Ln(e,n)}}}):e},[c.gK.revertConsent]:function(e){const{[o.H7]:t}=(0,s.Yh)(e)||{};return(0,h.h1)(Cn({},e),{iab:{usp:{optOut:Ln(e,t)}}})},[c.gK.acceptAllConsent]:function(e){return(0,h.h1)(Cn({},e),{iab:{usp:{optOut:Ln(e,!1)}}})},[c.gK.denyAllConsent]:function(e){return(0,h.h1)(Cn({},e),{iab:{usp:{optOut:Ln(e,!0)}}})}}),Rn="__uspapi",Fn="uspv1",Hn={gppSection:Fn,version:1},Bn=function(e){return void 0===e?"-":e?"Y":"N"},Un=function(e){return(0,h.Jt)(e,"iab.usp",{})},Kn=function(e){return!!(0,h.Jt)(e,"iab.usp.ccpaApplies",(0,s._m)(e))},Wn=function(e,{gppSection:t,version:n}={}){const r=(0,h.Jt)(e,["iab","consentStrings",t]);if(r)return r;const{notified:o,signatory:i,optOut:s}=Un(e),c=[o,s,i],a=isNaN(parseInt(n,10))?1:parseInt(n,10);return Kn(e)?`${parseInt(a.toString(),10)}${c.map(Bn).join("")}`:`${parseInt(a.toString(),10)}---`},Jn=function({version:e=1}={}){return function(t){const[n=e,r,o,i]=(t||"").split("").map((function(t,n){return 0===n?isNaN(parseInt(t,10))?e:parseInt(t,10):"-"===(r=t)||void 0===r?void 0:!("Y"!==(r||"N").toUpperCase());var r}));return{version:n,notified:r,signatory:i,optOut:o}}},Gn=function(e={}){return function(t,n){const{version:r,notified:o,optOut:i,signatory:s}=Jn(e)(Wn(n,e)),{version:c=r,notified:a=o,optOut:l=i,signatory:u=s}=(0,h.h1)(Un(n),t),p=[a,l,u];return Kn(n)?`${parseInt(c.toString(),10)}${p.map(Bn).join("")}`:`${parseInt(c.toString(),10)}---`}},Yn=function(e){return function(t,n){const{version:r,notified:o,optOut:i,signatory:s}=Jn(e)(Wn(n,e)),{version:c=r,notified:a=o,optOut:l=i,signatory:u=s}=(0,h.h1)(Un(n),t);return Kn(n)?{version:c,notified:a,signatory:u,optOut:l}:{version:c}}};function Vn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Qn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Vn(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Vn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const qn=window;const zn=Yn(Hn),Zn=Gn(Hn);var Xn;const er={reducer:Mn,versions:{v1_0:{api:(Xn=Hn,{bind:function(e){return function(t,n,r,o){const i=e.getState(),s=function(e={}){return function(t,n){const[,r,o,i]=Wn(t,e).split("");switch(n){case"Version":return 1;case"Notice":return r;case"OptOutSale":return o;case"LspaCovered":return i}}}(Xn),c=function(e,t=Xn.apiVersion){try{const n=function(e){return function(t){const{version:n}=e;if(1!==n)throw new Error("Unsupported version");return{version:n,uspString:Gn(e)(Yn(e)(void 0,t),t)}}}(Qn(Qn({},Xn),{},{version:t}))(e);return n}catch(n){return}};switch(t){case"getUSPData":if("function"==typeof r){const e=c(i,n);r(e,!!e)}break;case"getField":return s(i,o);case"getSection":return[c(i,n)]}}}}),apiName:Rn,createMiddleware:function(){return function(e){return function(t){return function(n){const r=t(n),{type:i}=n,a=e.getState();switch(i){case c.gK.saveConsent:{const{[o.H7]:t}=(0,s.hr)(a),n=Zn(zn({optOut:t===o.FA},a),a);e.dispatch(A(Fn,n));break}case c.gK.clearConsent:{const t=Zn(zn({optOut:o.Fr},a),a);e.dispatch(A(Fn,t));break}}return r}}}},gppSection:Fn,iframeName:"__uspapiLocator",postMessageEventHandler:function(e,t=!1){return function(n){const{data:r,source:o}=n,i="string"==typeof r;let s={};try{s=i?JSON.parse(r):r}catch(a){s=r}const c="object"==typeof s&&s[`${e}Call`];if(c){const{command:n,parameter:r,version:s,callId:a}=c,l=function(t,n=!0){let r={[`${e}Return`]:{returnValue:t,success:n,callId:a}};i&&(r=JSON.stringify(r)),o.postMessage(r,"*")},u=t?[n,s,l,r]:[n,l,r],p=qn[`${e}`].apply(this,u);p&&l(p)}}}(Rn,!0)}}};Promise=t(),function(customerConfig,flavor,language,locale,e,r,i){o.hT.setup({},language);const a=(0,Y.e1)(customerConfig,flavor,language),l=(0,Y.UF)(a,e),u=(0,jn.$y)(),p=$((0,h.h1)(i,Dn(Dn({},l),{},{extUsrData:J.extUsrData})),u.enhancer),f=new G({emitter:d,store:p});(0,s.HC)(p.getState())||function(e,t=window){const{Document:n,Element:r,HTMLElement:i,HTMLIFrameElement:c,HTMLScriptElement:a,Node:l,Storage:u}=t;try{return Ft(n,e),rn(r,e),(0,on.F6)((0,o.Bh)(t),l,e),pn(i,e),(0,s.XL)(e.getState())&&(0,V.F6)(c,e),wn(l,e),(0,Q.F6)(a,e),$n(u,Me.Ay,e),_n(t,e),!0}catch(p){}}(p);const g=new Et({dynamicMiddleware:u,store:p});u.addMiddleware(function(e){return function(t){return function(n){return function(r){const o=n(r),i=t.getState(),{type:a}=r;switch(a){case c.gK.saveConsent:case c.gK.readyApi:case c.gK.ready:Object.entries((0,s.pH)(i)||{}).forEach((function([n,r]){if("shopify"===n)try{xn(e,t,r,a)}catch(o){}}))}return o}}}}(g)),r&&r(p,u),g.setup(),p.dispatch(c.Ay.init(l)),p.dispatch(c.Ay.ready("blocking")),(0,s._o)(p.getState())||(0,oe.D_)((function(){const e=language,r=locale;return t().all([Promise.resolve().then(n.bind(n,8503)),L(p,e,r)]).then((function([{default:e}]){const t=new e({dynamicMiddleware:u,store:p});g.dom=t,t.setup(),requestAnimationFrame((function(){return requestAnimationFrame((function(){return p.dispatch(c.Ay.ready("dom"))}))}))})).catch((function(e){0}))})),f.setup(window)}({iab:{tcf:{v2:{vendors:{},vendorListVersion:0},vendors:[]},hideOptOut:false},cookies:{adobe_plt:{expiry:'',purpose:'',provider:'Adobe Inc',classification:'ANALYTICS'},adobe_ppn:{expiry:'',purpose:'',provider:'Adobe Inc',classification:'ANALYTICS'},'^AMCV_.*':{expiry:'',purpose:'Set by Adobe Marketing Cloud. It stores a unique visitor identifier and uses an organisation identifier to allow a company to track users across their domains and services.',provider:'Adobe Inc',classification:'ANALYTICS'},'^AMCVS.*':{expiry:'',purpose:'Set by Adobe Marketing Cloud. It stores a unique visitor identifier and uses an organisation identifier to allow a company to track users across their domains and services.',provider:'Adobe Inc',classification:'ANALYTICS'},__atrfs:{expiry:'',purpose:'',provider:'Oracle LLC',classification:'MARKETING'},__atssc:{expiry:'',purpose:'',provider:'Oracle LLC',classification:'MARKETING'},'^__atuvc$':{expiry:'',purpose:'Enables visitors to share content with a range of networking and sharing platforms. It stores an updated page share count.',provider:'Oracle LLC',classification:'MARKETING'},'^__atuvs$':{expiry:'',purpose:'Enables visitors to share content with a range of networking and sharing platforms. It stores an updated page share count.',provider:'Oracle LLC',classification:'MARKETING'},'^_cc_id$':{expiry:'269 Days',purpose:'Collects anonymous statistical data related to the user\'s website visits, such as the number of visits, average time spent on the website and what pages have been loaded. The purpose is to segment the website\'s users according to factors such as demographics and geographical location, in order to enable media and marketing agencies to structure and understand their target groups to enable customised online advertising.',provider:'Lotame Solutions Inc',classification:'BLACKLISTED'},_cm:{expiry:'',purpose:'',provider:'Doceree',classification:'MARKETING'},'com\\.adobe\\.alloy':{expiry:'',purpose:'',provider:'Adobe Inc',classification:'ANALYTICS'},cookietest:{expiry:'Session',purpose:'Used to determine whether the user\'s browser accepts cookies, essential for website functionality and user experience.',provider:'Atypon Systems',classification:'ESSENTIAL'},_curator_id:{expiry:'',purpose:'',provider:'Doceree',classification:'MARKETING'},'_curator_id ':{expiry:'',purpose:'',provider:'Doceree',classification:'MARKETING'},'^_dc_gtm_UA-.*':{expiry:'1 minute',purpose:'This is a Google Tag Manager cookie that is used to control the loading of a Google Analytics script tag, to track the performance of ad campaigns.',provider:'Google LLC',classification:'ANALYTICS'},_docereePlatformContext:{expiry:'',purpose:'',provider:'Doceree',classification:'MARKETING'},dtCookie:{expiry:'Session',purpose:'Dynatrace Real User Monitoring (RUM) also relies on browser cookies to correlate user interactions in the browser, such as user actions, with general page and backend performance metrics',provider:'DYNATRACE LLC',classification:'ANALYTICS'},'^dtPC$':{expiry:'Session',purpose:'Dynatrace Real User Monitoring (RUM) also relies on browser cookies to correlate user interactions in the browser, such as user actions, with general page and backend performance metrics',provider:'DYNATRACE LLC',classification:'ANALYTICS'},'^dtSa$':{expiry:'Session',purpose:'Dynatrace Real User Monitoring (RUM) also relies on browser cookies to correlate user interactions in the browser, such as user actions, with general page and backend performance metrics',provider:'DYNATRACE LLC',classification:'ANALYTICS'},dTValidationCookie:{expiry:'Milliseconds',purpose:'Dynatrace Real User Monitoring (RUM) also relies on browser cookies to correlate user interactions in the browser, such as user actions, with general page and backend performance metrics',provider:'DYNATRACE LLC',classification:'ANALYTICS'},'^_fbc$':{expiry:'',purpose:'Placed by Facebook to store last visit',provider:'Facebook, Inc.',classification:'MARKETING'},'^_fbp$':{expiry:'',purpose:'Used by Facebook to deliver a series of advertisement products such as real time bidding from third party advertisers',provider:'Facebook, Inc.',classification:'MARKETING'},'^_ga$':{expiry:'',purpose:'Associated with Google Universal Analytics to distinguish unique users by assigning a randomly generated number as a client identifier.',provider:'Google LLC',classification:'ANALYTICS'},'_ga_.*':{expiry:'2 years',purpose:'Used to persist session state for analytics',provider:'Google LLC',classification:'ANALYTICS'},'_gac_UA.*':{expiry:'90 days',purpose:'Google Analytics sets this cookie to store and count pageviews.',provider:'Google LLC',classification:'ANALYTICS'},'_gac_UA-68565440-15':{expiry:'',purpose:'',provider:'Google LLC',classification:'ANALYTICS'},'^__gads$':{expiry:'',purpose:'Used by DoubleClick for the showing of adverts on the site, from which the owner may earn revenue.',provider:'Google LLC',classification:'MARKETING'},'^_gat_.*':{expiry:'',purpose:'',provider:'Google LLC',classification:'ANALYTICS'},'_gcl_.*':{expiry:'',purpose:'Used to help advertisers determine the number of times users who click on their ads end up taking an action on their site.',provider:'Google LLC',classification:'ANALYTICS'},'^_gcl_au$':{expiry:'',purpose:'Used by Google AdSense for experimenting with advertisement efficiency across websites using their services',provider:'Google LLC',classification:'MARKETING'},'^_gcl_aw$':{expiry:'',purpose:'Stores ad click information so that conversions can be attributed outside the landing page',provider:'Google LLC',classification:'MARKETING'},_gcna:{expiry:'',purpose:'',provider:'OneCount',classification:'BLACKLISTED'},_gcnb:{expiry:'',purpose:'',provider:'OneCount',classification:'BLACKLISTED'},_gcnz:{expiry:'',purpose:'',provider:'OneCount',classification:'BLACKLISTED'},'^_gid$':{expiry:'',purpose:'Associated with Google Universal Analytics to distinguish unique users by assigning a randomly generated number as a client identifier.',provider:'Google LLC',classification:'ANALYTICS'},'^GoogleAdServingTest$':{expiry:'',purpose:'Used to determine what ads have been shown to the website visitor.',provider:'Google LLC',classification:'MARKETING'},googtrans:{expiry:'',purpose:'Google Translate sets this cookie to store language settings.',provider:'Google LLC',classification:'PERSONALIZATION'},__gpi:{expiry:'',purpose:'Google Ads Service uses this cookie to collect information about from multiple websites for retargeting ads.',provider:'Google LLC',classification:'MARKETING'},__gpi_opt_out:{expiry:'',purpose:'Google Ads Service uses this cookie to collect information about from multiple websites for retargeting ads.',provider:'Google LLC',classification:'MARKETING'},'Hm_.*':{expiry:'',purpose:'',provider:'Baidu, Inc.',classification:'ANALYTICS'},kndctr:{expiry:'',purpose:'',provider:'Adobe Inc',classification:'ANALYTICS'},lastRskxRun:{expiry:'',purpose:'',provider:'Riskified',classification:'ESSENTIAL'},li_adsId:{expiry:'6 months',purpose:'Generated by LinkedIn, pseudonymous identifier for LinkedIn Ads to further support conversion tracking. This ID is only applicable to website traffic from outside the EU, EEA, Quebec, and UK territories. It is stored in a user device’s browser storage for six months, then automatically expires. When a member revisits a website with an Insight Tag after expiration, a new LinkedIn Ads ID is generated.',provider:'LinkedIn Corporation',classification:'MARKETING'},li_gc:{expiry:'6 months',purpose:'Used by Linkedin to help with correct attribution of ads and helps build the advertising audiences.',provider:'LinkedIn Corporation',classification:'MARKETING'},li_mc:{expiry:'6 months',purpose:'Used by Linkedin to help with correct attribution of ads and helps build the advertising audiences.',provider:'LinkedIn Corporation',classification:'MARKETING'},__ocid:{expiry:'',purpose:'',provider:'OneCount',classification:'BLACKLISTED'},'oc-js-session':{expiry:'',purpose:'',provider:'OneCount',classification:'BLACKLISTED'},__oc_third_party_sync:{expiry:'',purpose:'',provider:'OneCount',classification:'BLACKLISTED'},__ocusername:{expiry:'',purpose:'',provider:'OneCount',classification:'BLACKLISTED'},osano_consentmanager:{expiry:'12 months',purpose:'Consent categorization (e.g., MARKETING: ACCEPT/DENY) as well as when the user\'s consent expires, and consent needs to be asked for again.',provider:'Osano Inc',classification:'ESSENTIAL'},osano_consentmanager_uuid:{expiry:'12 months',purpose:' The end-users unique user id (for consent lookups).',provider:'Osano Inc',classification:'ESSENTIAL'},_parsely_:{expiry:'Undefined',purpose:'Used to determine if the visitor has visited the website before, or if it is a new visitor on the website.',provider:'Parsely Inc',classification:'ANALYTICS'},'parsely_*':{expiry:'',purpose:'Used by Parse.ly analytics',provider:'Parsely Inc',classification:'ANALYTICS'},'professional-disclaimer':{expiry:'',purpose:'',provider:'Atypon Systems',classification:'PERSONALIZATION'},QSI_HistorySession:{expiry:'',purpose:'',provider:'Qualtrics International Inc',classification:'ANALYTICS'},'^QSI_SI_(.*)_intercept$':{expiry:'',purpose:'',provider:'Qualtrics International Inc',classification:'ANALYTICS'},randomizeUser:{expiry:'',purpose:'',provider:'Adobe Inc',classification:'MARKETING'},rCookie:{expiry:'',purpose:'',provider:'Riskified',classification:'ESSENTIAL'},realReferer:{expiry:'',purpose:'',provider:'OneCount',classification:'MARKETING'},rskxRunCookie:{expiry:'',purpose:'',provider:'Riskified',classification:'ESSENTIAL'},'^rxVisitor$':{expiry:'Undefined',purpose:'Used by Dynatrace to store an anonymous ID for the user to correlate across sessions on the world service.',provider:'DYNATRACE LLC',classification:'ANALYTICS'},'^rxvt$':{expiry:'Undefined',purpose:'Used by Dynatrace to determine session length and the end of a session.',provider:'DYNATRACE LLC',classification:'ANALYTICS'},'sb-installed':{expiry:'',purpose:'',provider:'Atypon Systems',classification:'ESSENTIAL'},'^s_cc$':{expiry:'',purpose:'Adobe Site Catalyst cookie, determines whether cookies are enabled in the browser',provider:'Adobe Inc',classification:'ANALYTICS'},'^s_fid$':{expiry:'',purpose:'Set by Adobe Analytics as a \'fallback\' visitor identifier. It contains a randomly generated, unique id.',provider:'Adobe Inc',classification:'ANALYTICS'},'^s_sq$':{expiry:'',purpose:'Set by Adobe Site Catalys to store information about the previous link clicked within the site',provider:'Adobe Inc',classification:'ANALYTICS'},__tempcookie:{expiry:'',purpose:'',provider:'OneCount',classification:'BLACKLISTED'},'^test$':{expiry:'',purpose:'No description available.',provider:'Adobe Inc',classification:'MARKETING'},TEST_AMCV_COOKIE:{expiry:'',purpose:'',provider:'Adobe Inc',classification:'ANALYTICS'},'^_uetdbg$':{expiry:'',purpose:'',provider:'Microsoft Corporation',classification:'MARKETING'},'^_uetvid$':{expiry:'16 Days',purpose:'This is a cookie utilised by Microsoft Bing Ads and is a tracking cookie. It allows us to engage with a user that has previously visited our website.',provider:'Microsoft Corporation',classification:'MARKETING'},userRandomGroup:{expiry:'',purpose:'',provider:'Atypon Systems',classification:'ESSENTIAL'},'^utag_main$':{expiry:'Undefined',purpose:'Used for web analytics to improve user experience.',provider:'Tealium Inc',classification:'ANALYTICS'},utm_params_persistence:{expiry:'',purpose:'',provider:'Atypon Systems',classification:'ANALYTICS'}},palette:{linkColor:'',borderless:false,dialogType:'bar',widgetColor:'#37cd8f',widgetPosition:'left',displayPosition:'bottom',widgetFillColor:'#fff',infoDialogPosition:'left',widgetOutlineColor:'#29246a',toggleButtonOnColor:'',toggleButtonOffColor:'',buttonBackgroundColor:'#4b81e8',buttonForegroundColor:'',dialogBackgroundColor:'#edeff5',dialogForegroundColor:'#414246',infoDialogOverlayColor:'',toggleOnBackgroundColor:'',toggleOffBackgroundColor:'',buttonDenyBackgroundColor:'',buttonDenyForegroundColor:'',infoDialogBackgroundColor:'',infoDialogForegroundColor:''},scripts:{'2gether.video':'BLACKLISTED','2mdn.net':'MARKETING','33across\\.com':'MARKETING','\\/addthismoatframe568911941483\\/moatframe.js':'MARKETING','addtoany\\.com':'MARKETING','adform.net/':'MARKETING','adservice\\.google\\.':'MARKETING','\\/adsid\\/integrator.js':'MARKETING','\\/ajax\\/libs\\/jQuery.dotdotdot\\/1.8.3\\/jquery.dotdotdot.min.js':'ESSENTIAL','\\/ajax\\/libs\\/mathjax':'ESSENTIAL','\\/ajax\\/libs\\/mathjax\\/2.7.5\\/.*':'ESSENTIAL','\\/ajax\\/libs\\/mathjax\\/2.7.5\\/config\\/TeX-AMS-MML_HTMLorMML.js':'ESSENTIAL','\\/ajax\\/libs\\/mathjax\\/2.7.5\\/MathJax.js':'ESSENTIAL','alloy\\.js':'ANALYTICS','altmetric.com':'ESSENTIAL','amazon-adsystem\\.com':'MARKETING','\\/analytics.js':'ANALYTICS','AppMeasurement\\.min\\.js':'ANALYTICS','AppMeasurement_Module_ActivityMap\\.min\\.js':'ANALYTICS','AppMeasurement_Module_AudienceManagement.min.js':'ANALYTICS','app.reprintsdesk.com':'BLACKLISTED','assets.adobedtm.com':'ESSENTIAL','\\/assets\\/altmetric_badges-(.*).js':'ESSENTIAL','\\/assets\\/embed.js':'ESSENTIAL','association-revenue-partners.scoop.it':'BLACKLISTED','baidu\\.com\\/':'ANALYTICS','bat.bing.com/bat.js':'MARKETING','\\/beacon.min.js':'ANALYTICS','beacon.riskified.com':'ESSENTIAL','bing\\.com':'MARKETING','\\/botdetectcaptcha':'ESSENTIAL','brightcove.com':'ESSENTIAL','brightcove.net':'ESSENTIAL','bsky.app':'MARKETING','\\/b\\/ss\\/wileyonlinelibrary-literatum':'ANALYTICS','buffer.com':'MARKETING','buzzsprout.com':'MARKETING','captcha.com':'ESSENTIAL','careercenter.awwa.org':'MARKETING','\\/cdn-cgi\\/bm\\/cv\\/669835187\\/api.js':'ESSENTIAL','\\/cdn-cgi\\/challenge-platform\\/.*\\/invisible.js':'ESSENTIAL','\\/cdn-cgi\\/scripts\\/.*\\/cloudflare-static\\/email-decode\\.min\\.js':'ESSENTIAL','cdn.scite.ai\\/badge\\/scite-badge-latest.min.js':'MARKETING','cloudflare.com':'ESSENTIAL','cloudflarestream.com':'ESSENTIAL','cloudfront.net':'ESSENTIAL','\\/cloud-reader\\/.*':'ESSENTIAL','confiant-integrations\\.net':'MARKETING','\\/controltag':'MARKETING','creativecdn\\.com':'BLACKLISTED','criteo\\.net':'BLACKLISTED','crossmark-cdn.crossref.org':'ESSENTIAL','crwdcntrl\\.net':'MARKETING','(disqus.com)|(disquscdn.com)':'ESSENTIAL','\\/doi\\/epdf\\/build\\/dev-sandbox\\/pdf.sandbox.js':'ESSENTIAL','doubleclick\\.net|\\/tag\\/js\\/gpt\\.js':'MARKETING','doubleverify.com/':'MARKETING','embed.videodelivery.net':'ESSENTIAL','en25.com':'MARKETING','\\/en_US\\/fbevents.js':'MARKETING','facebook\\.net\\/':'MARKETING','feedburner\\.com':'ESSENTIAL','googleadservices\\.com\\/':'MARKETING','google-analytics\\.com\\/':'ANALYTICS','googleapis\\.com\\/':'ESSENTIAL','google\\.com':'MARKETING','google.com\\/pagead':'MARKETING','(google.com\\/recaptcha)|(\\/recaptcha\\/api.js)':'ESSENTIAL','googlesyndication\\.com\\/':'MARKETING','googletagmanager\\.com\\/':'ESSENTIAL','googletagservices.com':'MARKETING','\\/gpt\\/pubads_impl_(.*).js':'MARKETING','gstatic\\.com\\/':'ESSENTIAL','\\/gtag\\/js':'ESSENTIAL','\\/hm.js':'ANALYTICS','https://servedbydoceree.doceree.com/':'MARKETING','https://www.diversityinresearch.careers/js/article-widget.js':'MARKETING','\\/i\\/adsct':'ANALYTICS','\\/iframe_api':'ESSENTIAL','\\/inject\\.js':'BLACKLISTED','\\/js\\/300\\/addthis_widget.js':'MARKETING','\\/js\\/all.min.js':'MARKETING','\\/js\\/custom\\/index.php':'MARKETING','\\/js\\/custom\\/wly-extra.js':'MARKETING','\\/js\\/custom\\/wly-oc-identify.js':'MARKETING','\\/js\\/custom\\/wly-onecount-sync.js':'MARKETING','\\/js\\/custom\\/wly-sync-contactID.js':'MARKETING','krxd\\.net\\/':'MARKETING','\\/live\\/boost\\/ra-51225be51b56b4c4\\/_ate.track.config_resp':'MARKETING','\\/live\\/red_lojson\\/300lo.json':'MARKETING','\\/main\\/channels.cgi':'MARKETING','microsofttranslator\\.com':'BLACKLISTED','moatads.com/':'MARKETING','moatads\\.com|\\/moatheader\\.js':'MARKETING','omtrdc.net':'ANALYTICS','\\/onecount\\/api\\/public\\/index.php':'MARKETING','\\/onecount\\/automation\\/a.php':'MARKETING','osano.com':'ESSENTIAL','pagead2.googlesyndication.com':'MARKETING','\\/pagead\\/conversion_async.js':'MARKETING','\\/pagead\\/viewthroughconversion':'MARKETING','parsely\\.com':'BLACKLISTED','\\/pb-assets\\/catchpoint(.*).js':'ANALYTICS','pingdom.net':'ANALYTICS','/player.captivate\\.fm\\/':'MARKETING','podcasts.apple.com':'MARKETING','\\/products\\/acropolis\\/pericles\\/releasedAssets':'ESSENTIAL','\\/products\\/pericles\\/releasedAssets\\/js':'ESSENTIAL','\\/prum.min.js':'ANALYTICS','(qualtrics.com)|(\\/dxjsmodule\\/.*.chunk.js)':'ANALYTICS','RC6e4e9fb26c37495b937e35b1cd336268-source.min.js':'MARKETING','RC784212f34e2b4645b6b062211c8ebcf2-source.min.js':'MARKETING','(remotexs.*)(\\/misc\\/)':'BLACKLISTED','\\/resources\\/page-builder':'ESSENTIAL','\\/resources\\/pb-ui\\/min\\/Main.js':'ESSENTIAL','rlcdn\\.com':'BLACKLISTED','scholar.google.com':'ESSENTIAL','\\/scholar_js\\/casa.js':'ESSENTIAL','secure.na.tnspayments.com':'ESSENTIAL','servedbyadbutler.com/':'MARKETING','\\/signals\\/config\\/263124174624252':'MARKETING','\\/signals\\/plugins\\/identity.js':'MARKETING','snap\\.licdn\\.com':'ANALYTICS','(snap\\.licdn\\.com)*(\\/li.lms-analytics\\/insight\\.min\\.js)':'ANALYTICS','snap.licdn.com\\li.lms-analytics\\insight.min.js':'MARKETING','\\/sodar\\/sodar2.js':'MARKETING','\\/s\\/player\\/(.*)\\/www-widgetapi.vflset\\/www-widgetapi.js':'ESSENTIAL','static.cloudflareinsights.com':'ANALYTICS','\\/static\\/js\\/api.js':'ESSENTIAL','\\/static\\/js\\/(.*)article-metrics-phase2.js':'ANALYTICS','\\/static\\/js\\/build.lazyload.bundle(.*).js':'ESSENTIAL','\\/static\\/js\\/catchpoint-1549680351760.js':'ANALYTICS','\\/static\\/js\\/channels.js':'MARKETING','\\/static\\/js\\/fbevents.js':'MARKETING','\\/static\\/js\\/main.bundle(.*).js':'ESSENTIAL','\\/static\\/js\\/(.*)product.js':'ESSENTIAL','\\/static\\/js\\/thiss-ds.js':'ESSENTIAL','\\/static\\/layers.fa6cd1947ce26e890d3d.js':'MARKETING','\\/templates\\/jsp\\/.*':'ESSENTIAL','\\/@theidentityselector\\/thiss-ds':'ESSENTIAL','tiqcdn\\.com':'BLACKLISTED','translate.google.com':'ESSENTIAL','twimg.com':'MARKETING','twitter\\.com\\/':'MARKETING','unpkg.com\\/@theidentityselector\\/thiss-ds':'ESSENTIAL','\\/uwt.js':'MARKETING','validate.onecount.net':'MARKETING','vjs.zencdn.net':'ESSENTIAL','walkme\\.com':'BLACKLISTED','\\/widgets.js':'MARKETING','\\/widget\\/v2.0\\/widget.js':'ESSENTIAL','wiley.grapeshot.co.uk':'MARKETING','wol-jobs-widget.madgexjb.com/':'ESSENTIAL','\\/wro\\/(.*)article-metrics-phase2.js':'ANALYTICS','\\/wro\\/.*.js':'ESSENTIAL','\\/wro\\/(.*)pb.js':'ESSENTIAL','\\/wro\\/(.*)product.js':'ESSENTIAL','\\/WRSiteInterceptEngine':'ANALYTICS','youtube\\.com\\/':'ESSENTIAL'},dntSupport:false,gpcSupport:false,iabEnabled:false,ccpaRelaxed:true,crossDomain:false,disclosures:[],allowTimeout:true,codeSplitting:false,googleConsent:true,iframeBlocking:'production',policyLinkText:'privacyPolicy',timeoutSeconds:10,additionalLinks:[],storagePolicyHref:'https://www.wiley.com/privacy',policyLinkInDrawer:true,forcedClassifyEnabled:false,forceManagePreferences:false,managePreferencesEnabled:true,customerId:'AzZdRbSORDOOzF9W',configId:'67a788d8-9344-4c9b-a3c2-89b5073ee231',mode:'production',domains:['onlinelibrary.wiley.com'],iframes:{'acast.com':'ESSENTIAL','adform.net/':'MARKETING','anchor.fm':'MARKETING','bcove.video':'ESSENTIAL','brightcove.net':'ESSENTIAL','buzzsprout.com':'MARKETING','castos.com':'ESSENTIAL','creators.spotify\\.com':'MARKETING','crossmark.crossref.org':'ESSENTIAL','demdex.net':'ANALYTICS','/dest5.html':'ANALYTICS','disqus.com':'ESSENTIAL','doi/pdfdirect':'ESSENTIAL','doubleclick.net':'MARKETING','doubleverify.com/':'MARKETING','drive.google.com':'ESSENTIAL','embed.podcasts.apple.com\\/':'MARKETING','facebook.com':'MARKETING','(google.com)?\\/recaptcha':'ESSENTIAL','googlesyndication.com':'MARKETING','googletagservices.com':'MARKETING','greenadblocker.com':'BLACKLISTED','https://docs.google.com/forms/d/150ywbWeqLCZKUq6eesopQK4IR5cFRHUFS_YhHTkvUqM/viewform':'ESSENTIAL','https://wiley.scienceconnect.io/teaser.html':'ESSENTIAL','iframe.videodelivery.net':'ESSENTIAL','jobs.sciencecareers.org':'MARKETING','libsyn.com':'MARKETING','linkedin.com':'MARKETING','mega.nz':'BLACKLISTED','mendeley.com':'BLACKLISTED','moatads.com/':'MARKETING','network.febs.org':'MARKETING','open.spotify.com':'MARKETING','/pb-assets':'ESSENTIAL','platform.twitter.com':'MARKETING','/player.captivate\\.fm\\/':'MARKETING','/player.vimeo\\.com\\/':'MARKETING','podbean.com':'ESSENTIAL','/safeframe/1-0-38/html/container.html':'MARKETING','secure.na.tnspayments.com':'ESSENTIAL','servedbyadbutler.com/':'MARKETING','service.seamlessaccess.org':'ESSENTIAL','/sodar/sodar2/225/runner.html':'MARKETING','soundcloud.com':'ESSENTIAL','/static/sh(.*)html':'BLACKLISTED','templates/jsp/_ux3/_acropolis/_pericles/pdf-viewer/web/viewer.html':'ESSENTIAL','twitter.com':'MARKETING','uploads.knightlab.com':'MARKETING','use.thiss.io':'ESSENTIAL','videomarketingplatform.co':'ESSENTIAL','/widget/bootstrap/job':'MARKETING','/widget/jobs':'MARKETING','wiley.com':'ESSENTIAL','wiley.sharepoint.com':'MARKETING','wol-jobs-widget.madgexjb.com/':'MARKETING','www.diversityinresearch.careers':'MARKETING','youtube.com':'ESSENTIAL'},disclosedVendorCount:16},{timer:false,analyticsAlways:false,categories:false,rejectAll:false,firstLayerUsage:false,managePreferences:false,canDismissDialog:false},"en",{"buttons":{"accept":"Accept","acceptAll":"Accept All","deny":"Deny","denyNonEssential":"Deny Non-Essential","rejectNonEssential":"Reject Non-Essential","dialog":{"denyAll":{"fr":"Continue Without Accepting"},"openDrawer":{"fr":"Customize Your Choices"}},"denyAll":"Reject All","managePreferences":"Manage Preferences","save":"Save","storagePolicy":"Data Storage Policy"},"categories":{"ESSENTIAL":{"label":"Essential","description":"Required to enable basic website functionality. You may not disable essential cookies."},"MARKETING":{"label":"Targeted Advertising","description":"Used to deliver advertising that is more relevant to you and your interests. May also be used to limit the number of times you see an advertisement and measure the effectiveness of advertising campaigns.  Advertising networks usually place them with the website operator’s permission."},"PERSONALIZATION":{"label":"Personalization","description":"Allow the website to remember choices you make (such as your username, language, or the region you are in) and provide enhanced, more personal features. For example, a website may provide you with local weather reports or traffic news by storing data about your general location."},"ANALYTICS":{"label":"Analytics","description":"Help the website operator understand how its website performs, how visitors interact with the site, and whether there may be technical issues."},"STORAGE":{"label":"Storage","description":"Allows the website to store data locally on your device. For example, this site may store data that will remember your previous selections, search filters, or other information that would provide a seamless user experience as you navigate throughout the website."},"OPT_OUT":{"label":"Do Not Sell or Share My Personal Information","description":"By switching the toggle to the right and clicking save, you are indicating that you do not want us to sell your personal information or share your personal information for online targeted advertising activities. Please note if you use different computers or browsers, you must indicate your choices again on each computer or browser used to access our services."}},"dialog":{"close":"Close this dialog","label":"Cookie Consent Banner"},"messaging":{"poweredBy":"Powered by Osano","default":"This website utilizes technologies such as cookies to enable essential site functionality, as well as for %{analytics}, %{personalization}, and %{marketing}.","categories":"You may change your settings at any time or accept the default settings.","cookieQuestion":"What is a cookie?","timer":"To learn more, view the following link:","privacyPolicy":"Privacy Policy","cookiePolicy":"Cookie Policy","privacyNotice":"Privacy Notice","cookieNotice":"Cookie Notice","moreDetails":"More Details","widgetAltText":"Cookie Preferences","usageWhat":"To improve your experience, we (and our partners) store and/or access information on your terminal (cookie or equivalent) with your consent for all our websites and applications, on your connected terminals.","usageHow":"Our website may use these cookies to:","usageList":"Measure the audience of the advertising on our website, without profiling\nDisplay personalized ads based on your navigation and your profile\nPersonalize our editorial content based on your navigation\nAllow you to share content on social networks or platforms present on our website\nSend you advertising based on your location","viewCookies":"View Cookies","viewDisclosures":"View Disclosures","closeButton":"You may close this banner to continue with only essential cookies.","subjectRightsRequest":"Subject Rights Request","doNotSellOrShare":"Do Not Sell or Share My Personal Information","securityPolicy":"Security Policy","imprint":"Imprint","googlePrivacyPolicy":"Google Privacy Policy","termsAndConditions":"Terms and Conditions","termsOfService":"Terms of Service"},"disclosure":{"day":"day","label":"Storage Items","none":"No Storage Items","noProvider":"No provider listed","cookie":{"purpose":"Purpose","expiry":"Expiration","name":"Name","classification":"Classification","provider":"Provider"},"dayPlural":"days","script":{"purpose":"Purpose","name":"Name","classification":"Classification","provider":"Provider"},"titles":{"classification":"Classification","expiry":"Expiration","name":"Name","provider":"Provider","purpose":"Purpose","type":"Type"},"types":{"localStorage":"Local Storage","trackingPixel":"Tracking Pixel"}},"drawer":{"header":"Storage Preferences","close":"Close Cookie Preferences","description":"When you visit websites, they may store or retrieve data about you using cookies and similar technologies (\"cookies\"). Cookies may be necessary for the basic functionality of the website as well as other purposes. You have the option of disabling certain types of cookies, though doing so may impact your experience on the website."},"doNotSell":{"header":"Your Privacy Choices","description":"We allow certain online advertising partners to collect information from our services through cookies and similar technologies to deliver ads which are more relevant to you and assist us with advertising-related analytics. This may be considered \"selling\" or \"sharing\" for targeted online advertising under certain laws. To opt out of this activity, use the toggle below.","link":"You may learn more about our privacy practices by reviewing our policies:"},"aria":{"newWindow":"Opens in a new window","external":"Opens an external website","externalNewWindow":"Opens an external website in a new window"}},null,T({usp:er}))})()})();
//# sourceMappingURL=osano.js.map